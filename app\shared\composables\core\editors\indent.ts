import { Extension } from "@tiptap/core";

export interface IndentOptions {
  types: string[];
  indentSize: number;
  maxIndent: number;
}

declare module "@tiptap/core" {
  interface Commands<ReturnType> {
    indent: {
      /**
       * Increase indentation
       */
      indent: () => ReturnType;
      /**
       * Decrease indentation
       */
      outdent: () => ReturnType;
    };
  }
}

export const Indent = Extension.create<IndentOptions>({
  name: "indent",
  
  addOptions() {
    return {
      types: ["heading", "paragraph", "listItem"],
      indentSize: 24, // Default indent size in pixels
      maxIndent: 5,   // Maximum number of indentation levels
    };
  },
  
  addGlobalAttributes() {
    return [
      {
        types: this.options.types,
        attributes: {
          indent: {
            default: 0,
            parseHTML: (element) => {
              // Try to parse from text-indent style
              const textIndent = element.style.textIndent;
              if (textIndent && textIndent.endsWith('px')) {
                const value = parseInt(textIndent, 10);
                return Math.round(value / this.options.indentSize) || 0;
              }

              // If no text-indent style, check for data attribute
              const indent = element.getAttribute('data-indent');
              return indent ? parseInt(indent, 10) : 0;
            },
            renderHTML: (attributes) => {
              if (!attributes.indent || attributes.indent === 0) {
                return {};
              }

              // Calculate text-indent based on indent level
              const indentValue = attributes.indent * this.options.indentSize;

              return {
                style: `text-indent: ${indentValue}px`,
                'data-indent': attributes.indent,
              };
            },
          },
        },
      },
    ];
  },
  
  addCommands() {
    return {
      indent: () => ({ commands, state }) => {
        const { selection } = state;
        const { $from, $to } = selection;
        
        // Get all nodes in the current selection
        const range = $from.blockRange($to);
        if (!range) return false;
        
        // Check if we're in a node that supports indentation
        const nodeType = $from.parent.type.name;
        if (!this.options.types.includes(nodeType)) {
          return false;
        }
        
        // Get current indent level
        const currentIndent = $from.parent.attrs.indent || 0;
        
        // Don't exceed max indent
        if (currentIndent >= this.options.maxIndent) {
          return false;
        }

        // Increase indent level
        return commands.updateAttributes(nodeType, {
          indent: currentIndent + 1,
        });
      },
      
      outdent: () => ({ commands, state }) => {
        const { selection } = state;
        const { $from, $to } = selection;
        
        // Get all nodes in the current selection
        const range = $from.blockRange($to);
        if (!range) return false;
        
        // Check if we're in a node that supports indentation
        const nodeType = $from.parent.type.name;
        if (!this.options.types.includes(nodeType)) {
          return false;
        }
        
        // Get current indent level
        const currentIndent = $from.parent.attrs.indent || 0;
        
        // Can't outdent if already at 0
        if (currentIndent <= 0) {
          return false;
        }
        
        // Decrease indent level
        return commands.updateAttributes(nodeType, {
          indent: currentIndent - 1,
        });
      },
    };
  },
});
