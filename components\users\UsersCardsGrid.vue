<template>
  <div>
    <!-- Loading State -->
    <div v-if="loading" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
      <div
        v-for="i in 8"
        :key="i"
        class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 animate-pulse"
      >
        <div class="flex items-center gap-3 mb-4">
          <div class="w-12 h-12 bg-gray-200 dark:bg-gray-700 rounded-full"></div>
          <div class="flex-1">
            <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded mb-2"></div>
            <div class="h-3 bg-gray-200 dark:bg-gray-700 rounded w-2/3"></div>
          </div>
        </div>
        <div class="space-y-3">
          <div class="h-3 bg-gray-200 dark:bg-gray-700 rounded"></div>
          <div class="h-3 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div v-else-if="!users || users.length === 0" class="text-center py-12">
      <Icon name="material-symbols:group" class="h-16 w-16 text-gray-400 mx-auto mb-4" />
      <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No users found</h3>
      <p class="text-gray-500 dark:text-gray-400 mb-6">
        Get started by inviting your first user to the platform.
      </p>
      <UiButton variant="primary">
        <Icon name="material-symbols:person-add" class="h-4 w-4 mr-2" />
        Invite User
      </UiButton>
    </div>

    <!-- Users Grid -->
    <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
      <div
        v-for="user in users"
        :key="user.id"
        class="group bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-lg hover:border-brandPrimary/20 dark:hover:border-brandPrimary/30 transition-all duration-200 overflow-hidden"
      >
        <!-- Card Header -->
        <div class="p-6 pb-4">
          <div class="flex items-start justify-between mb-4">
            <div class="flex items-center gap-3">
              <!-- Avatar -->
              <div class="relative">
                <img
                  v-if="user.avatarUrl"
                  :src="user.avatarUrl"
                  :alt="`${user.name} avatar`"
                  class="w-12 h-12 rounded-full object-cover ring-2 ring-gray-200 dark:ring-gray-700 group-hover:ring-brandPrimary/30 transition-all duration-200"
                />
                <div
                  v-else
                  class="w-12 h-12 rounded-full bg-gradient-to-br from-brandPrimary to-brandSecondary flex items-center justify-center ring-2 ring-gray-200 dark:ring-gray-700 group-hover:ring-brandPrimary/30 transition-all duration-200"
                >
                  <span class="text-lg font-bold text-white">
                    {{ user.name.charAt(0).toUpperCase() }}
                  </span>
                </div>
                
                <!-- Status Indicators -->
                <div class="absolute -bottom-1 -right-1 flex gap-1">
                  <!-- Online Status -->
                  <div
                    :class="[
                      'w-4 h-4 rounded-full border-2 border-white dark:border-gray-800',
                      isUserOnline(user.id) ? 'bg-green-500' : 'bg-gray-400'
                    ]"
                  ></div>
                  
                  <!-- 2FA Indicator -->
                  <div
                    v-if="user.otpEnabled"
                    class="w-4 h-4 rounded-full bg-blue-500 border-2 border-white dark:border-gray-800 flex items-center justify-center"
                  >
                    <Icon name="material-symbols:security" class="h-2 w-2 text-white" />
                  </div>
                </div>
              </div>

              <!-- User Info -->
              <div class="flex-1 min-w-0">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white truncate group-hover:text-brandPrimary transition-colors duration-200">
                  {{ user.name }}
                </h3>
                <p class="text-sm text-gray-500 dark:text-gray-400 truncate">
                  {{ user.email }}
                </p>
                <p v-if="user.title" class="text-xs text-gray-400 dark:text-gray-500 truncate">
                  {{ user.title }}
                </p>
              </div>
            </div>

            <!-- Quick Actions Menu -->
            <div class="relative">
              <UiButton
                @click="toggleActionsMenu(user.id)"
                size="sm"
                variant="ghost"
                class="opacity-0 group-hover:opacity-100 transition-opacity duration-200"
              >
                <Icon name="material-symbols:more-vert" class="h-4 w-4" />
              </UiButton>
              
              <!-- Actions Dropdown -->
              <Transition
                enter-active-class="transition ease-out duration-100"
                enter-from-class="transform opacity-0 scale-95"
                enter-to-class="transform opacity-100 scale-100"
                leave-active-class="transition ease-in duration-75"
                leave-from-class="transform opacity-100 scale-100"
                leave-to-class="transform opacity-0 scale-95"
              >
                <div
                  v-if="activeMenu === user.id"
                  class="absolute right-0 top-8 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 py-1 z-10"
                >
                  <button
                    @click="handleAction('view', user)"
                    class="w-full px-4 py-2 text-left text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center gap-2"
                  >
                    <Icon name="material-symbols:visibility" class="h-4 w-4" />
                    View Profile
                  </button>
                  <button
                    @click="handleAction('edit', user)"
                    class="w-full px-4 py-2 text-left text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center gap-2"
                  >
                    <Icon name="material-symbols:edit" class="h-4 w-4" />
                    Edit User
                  </button>
                  <button
                    @click="handleAction('impersonate', user)"
                    class="w-full px-4 py-2 text-left text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center gap-2"
                  >
                    <Icon name="material-symbols:person-play" class="h-4 w-4" />
                    Impersonate
                  </button>
                  <button
                    @click="handleAction('reset-password', user)"
                    class="w-full px-4 py-2 text-left text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center gap-2"
                  >
                    <Icon name="material-symbols:lock-reset" class="h-4 w-4" />
                    Reset Password
                  </button>
                  <hr class="my-1 border-gray-200 dark:border-gray-700" />
                  <button
                    @click="handleAction(user.isActive ? 'deactivate' : 'activate', user)"
                    :class="[
                      'w-full px-4 py-2 text-left text-sm hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center gap-2',
                      user.isActive 
                        ? 'text-red-600 dark:text-red-400' 
                        : 'text-green-600 dark:text-green-400'
                    ]"
                  >
                    <Icon 
                      :name="user.isActive ? 'material-symbols:person-off' : 'material-symbols:person-check'" 
                      class="h-4 w-4" 
                    />
                    {{ user.isActive ? 'Deactivate' : 'Activate' }}
                  </button>
                </div>
              </Transition>
            </div>
          </div>

          <!-- Roles -->
          <div class="mb-4">
            <div class="flex flex-wrap gap-1">
              <span
                v-for="role in user.roles.slice(0, 3)"
                :key="role"
                :class="[
                  'inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium',
                  getRoleBadgeClass(role)
                ]"
              >
                {{ role }}
              </span>
              <span
                v-if="user.roles.length > 3"
                class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-300"
              >
                +{{ user.roles.length - 3 }}
              </span>
            </div>
          </div>

          <!-- Stats -->
          <div class="grid grid-cols-2 gap-4 mb-4">
            <div class="text-center p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
              <div class="text-lg font-semibold text-gray-900 dark:text-white">
                {{ user.tenantId ? 'Tenant' : 'Platform' }}
              </div>
              <div class="text-xs text-gray-500 dark:text-gray-400">Scope</div>
            </div>
            <div class="text-center p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
              <div class="text-lg font-semibold text-gray-900 dark:text-white">
                {{ user.lastLoginAt ? formatDate(user.lastLoginAt) : 'Never' }}
              </div>
              <div class="text-xs text-gray-500 dark:text-gray-400">Last Login</div>
            </div>
          </div>
        </div>

        <!-- Card Footer -->
        <div class="px-6 py-4 bg-gray-50 dark:bg-gray-700/30 border-t border-gray-200 dark:border-gray-700">
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-2">
              <div
                :class="[
                  'w-2 h-2 rounded-full',
                  user.isActive ? 'bg-green-500' : 'bg-red-500'
                ]"
              ></div>
              <span
                :class="[
                  'text-sm font-medium',
                  user.isActive ? 'text-green-700 dark:text-green-400' : 'text-red-700 dark:text-red-400'
                ]"
              >
                {{ user.isActive ? 'Active' : 'Inactive' }}
              </span>
              
              <!-- Security Indicators -->
              <div class="flex items-center gap-1 ml-2">
                <Icon
                  v-if="user.otpEnabled"
                  name="material-symbols:security"
                  class="h-4 w-4 text-blue-500"
                  title="2FA Enabled"
                />
                <Icon
                  v-if="isUserOnline(user.id)"
                  name="material-symbols:circle"
                  class="h-3 w-3 text-green-500"
                  title="Online"
                />
              </div>
            </div>
            
            <div class="flex items-center gap-1">
              <UiButton
                @click="handleAction('view', user)"
                size="sm"
                variant="ghost"
                class="text-gray-600 hover:text-brandPrimary dark:text-gray-400 dark:hover:text-brandPrimary"
              >
                <Icon name="material-symbols:arrow-forward" class="h-4 w-4" />
              </UiButton>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import type { User } from '~/stores/user'

interface Props {
  users: User[]
  loading: boolean
  onlineUsers?: string[]
}

interface Emits {
  (e: 'user-action', payload: { action: string; user: User }): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// State
const activeMenu = ref<string | null>(null)

// Methods
const toggleActionsMenu = (userId: string) => {
  activeMenu.value = activeMenu.value === userId ? null : userId
}

const handleAction = (action: string, user: User) => {
  activeMenu.value = null
  emit('user-action', { action, user })
}

const isUserOnline = (userId: string) => {
  return props.onlineUsers?.includes(userId) || false
}

const getRoleBadgeClass = (role: string) => {
  const roleClasses = {
    'SUPER_ADMIN': 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
    'ADMIN': 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300',
    'LAWYER': 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
    'TENANT_OWNER': 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
    'MEMBER': 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300',
  }
  return roleClasses[role as keyof typeof roleClasses] || 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300'
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric'
  })
}

// Close menu when clicking outside
const handleClickOutside = (event: Event) => {
  const target = event.target as HTMLElement
  if (!target.closest('.relative')) {
    activeMenu.value = null
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>
