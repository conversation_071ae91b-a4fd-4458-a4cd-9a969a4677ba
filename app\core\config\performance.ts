/**
 * Performance Configuration
 * 
 * Centralized configuration for performance optimizations
 * including lazy loading, code splitting, and caching strategies
 */

export interface PerformanceConfig {
  // Code Splitting
  codeSplitting: {
    enabled: boolean
    chunkSizeLimit: number
    routeBasedSplitting: boolean
    featureBasedSplitting: boolean
    vendorSplitting: boolean
  }
  
  // Lazy Loading
  lazyLoading: {
    enabled: boolean
    components: boolean
    routes: boolean
    images: boolean
    intersectionThreshold: number
    rootMargin: string
  }
  
  // Caching
  caching: {
    enabled: boolean
    apiCache: {
      enabled: boolean
      ttl: number
      maxSize: number
      strategy: 'lru' | 'fifo' | 'ttl'
    }
    componentCache: {
      enabled: boolean
      maxComponents: number
    }
    routeCache: {
      enabled: boolean
      maxRoutes: number
    }
  }
  
  // Bundle Optimization
  bundleOptimization: {
    enabled: boolean
    treeShaking: boolean
    minification: boolean
    compression: boolean
    sourceMaps: boolean
  }
  
  // Runtime Performance
  runtime: {
    virtualScrolling: {
      enabled: boolean
      itemHeight: number
      bufferSize: number
    }
    debouncing: {
      search: number
      input: number
      resize: number
    }
    throttling: {
      scroll: number
      mousemove: number
    }
  }
  
  // Monitoring
  monitoring: {
    enabled: boolean
    performanceObserver: boolean
    memoryUsage: boolean
    bundleAnalysis: boolean
    vitals: {
      fcp: boolean // First Contentful Paint
      lcp: boolean // Largest Contentful Paint
      fid: boolean // First Input Delay
      cls: boolean // Cumulative Layout Shift
    }
  }
}

export const defaultPerformanceConfig: PerformanceConfig = {
  codeSplitting: {
    enabled: true,
    chunkSizeLimit: 500000, // 500KB
    routeBasedSplitting: true,
    featureBasedSplitting: true,
    vendorSplitting: true
  },
  
  lazyLoading: {
    enabled: true,
    components: true,
    routes: true,
    images: true,
    intersectionThreshold: 0.1,
    rootMargin: '50px'
  },
  
  caching: {
    enabled: true,
    apiCache: {
      enabled: true,
      ttl: 300000, // 5 minutes
      maxSize: 100,
      strategy: 'lru'
    },
    componentCache: {
      enabled: true,
      maxComponents: 50
    },
    routeCache: {
      enabled: true,
      maxRoutes: 20
    }
  },
  
  bundleOptimization: {
    enabled: true,
    treeShaking: true,
    minification: true,
    compression: true,
    sourceMaps: process.env.NODE_ENV === 'development'
  },
  
  runtime: {
    virtualScrolling: {
      enabled: true,
      itemHeight: 50,
      bufferSize: 10
    },
    debouncing: {
      search: 300,
      input: 150,
      resize: 100
    },
    throttling: {
      scroll: 16,
      mousemove: 16
    }
  },
  
  monitoring: {
    enabled: process.env.NODE_ENV === 'production',
    performanceObserver: true,
    memoryUsage: true,
    bundleAnalysis: process.env.NODE_ENV === 'development',
    vitals: {
      fcp: true,
      lcp: true,
      fid: true,
      cls: true
    }
  }
}

// Environment-specific overrides
export const getPerformanceConfig = (): PerformanceConfig => {
  const config = { ...defaultPerformanceConfig }
  
  if (process.env.NODE_ENV === 'development') {
    config.bundleOptimization.minification = false
    config.bundleOptimization.compression = false
    config.monitoring.enabled = false
  }
  
  if (process.env.NODE_ENV === 'test') {
    config.lazyLoading.enabled = false
    config.caching.enabled = false
    config.monitoring.enabled = false
  }
  
  return config
}

// Performance metrics thresholds
export const PERFORMANCE_THRESHOLDS = {
  // Core Web Vitals
  LCP: 2500, // Largest Contentful Paint (ms)
  FID: 100,  // First Input Delay (ms)
  CLS: 0.1,  // Cumulative Layout Shift
  
  // Custom metrics
  FCP: 1800, // First Contentful Paint (ms)
  TTI: 3800, // Time to Interactive (ms)
  TBT: 200,  // Total Blocking Time (ms)
  
  // Bundle sizes
  INITIAL_BUNDLE: 250000,  // 250KB
  ROUTE_CHUNK: 100000,     // 100KB
  COMPONENT_CHUNK: 50000,  // 50KB
  
  // Memory usage
  HEAP_SIZE: 50000000,     // 50MB
  COMPONENT_COUNT: 1000,   // Max components in memory
  
  // API performance
  API_RESPONSE_TIME: 1000, // 1 second
  API_ERROR_RATE: 0.01,    // 1%
}

// Performance optimization strategies
export const OPTIMIZATION_STRATEGIES = {
  // Code splitting patterns
  ROUTE_BASED: 'route-based',
  FEATURE_BASED: 'feature-based',
  COMPONENT_BASED: 'component-based',
  
  // Loading strategies
  EAGER: 'eager',
  LAZY: 'lazy',
  PREFETCH: 'prefetch',
  PRELOAD: 'preload',
  
  // Caching strategies
  CACHE_FIRST: 'cache-first',
  NETWORK_FIRST: 'network-first',
  STALE_WHILE_REVALIDATE: 'stale-while-revalidate',
  
  // Rendering strategies
  SSR: 'server-side-rendering',
  SSG: 'static-site-generation',
  CSR: 'client-side-rendering',
  ISR: 'incremental-static-regeneration'
}

// Feature-specific performance configurations
export const FEATURE_PERFORMANCE_CONFIG = {
  auth: {
    lazyLoad: false, // Critical for app initialization
    cache: true,
    prefetch: true
  },
  
  cases: {
    lazyLoad: true,
    cache: true,
    virtualScrolling: true,
    pagination: true
  },
  
  documents: {
    lazyLoad: true,
    cache: false, // Sensitive data
    compression: true,
    streaming: true
  },
  
  templates: {
    lazyLoad: true,
    cache: true,
    prefetch: false
  },
  
  settings: {
    lazyLoad: true,
    cache: true,
    prefetch: false
  }
}
