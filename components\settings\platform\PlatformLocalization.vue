<template>
  <div class="space-y-6">
    <div>
      <h2 class="text-md font-semibold text-gray-800 dark:text-gray-100 mb-4">Localization & Regional Settings</h2>
      <p class="text-sm text-gray-600 dark:text-gray-400">Manage platform languages, regional formats, and right-to-left (RTL) text direction.</p>
    </div>

    <!-- Language Management Section -->
    <UiCard title="Language Management">
      <div class="space-y-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Supported Languages</label>
          <div class="mt-2 space-y-2">
            <UiCheckbox v-for="lang in availableLanguages" :key="lang.value" v-model="languageSettings.supportedLanguages[lang.value]" :label="lang.label" :id="`lang-${lang.value}`" />
            <!-- TODO: Consider a more advanced multi-select/tag input component for better UX if many languages -->
          </div>
        </div>
        <div>
          <label for="defaultPlatformLanguage" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Default Platform Language</label>
          <UiSelect id="defaultPlatformLanguage" :name="'defaultPlatformLanguage'" v-model="languageSettings.defaultLanguage" :options="platformLanguageOptions" class="mt-1" />
        </div>
        <UiToggle v-model="languageSettings.allowUserSelection" label="Allow Users to Select Their Own Language" />
      </div>
    </UiCard>

    <!-- Regional Formatting Section -->
    <UiCard title="Regional Formatting">
      <div class="space-y-4">
        <div>
          <label for="defaultNumberFormat" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Default Number Format</label>
          <UiSelect id="defaultNumberFormat" :name="'defaultNumberFormat'" v-model="regionalFormatting.numberFormat" :options="numberFormatOptions" class="mt-1" />
        </div>
        <div>
          <label for="defaultCurrency" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Default Currency</label>
          <UiSelect id="defaultCurrency" :name="'defaultCurrency'" v-model="regionalFormatting.currency" :options="currencyOptions" class="mt-1" />
          <!-- TODO: Consider a searchable dropdown for currencies -->
        </div>
        <div>
          <label for="defaultDateTimeFormat" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Default Date & Time Format</label>
          <UiSelect id="defaultDateTimeFormat" :name="'defaultDateTimeFormat'" v-model="regionalFormatting.dateTimeFormat" :options="dateTimeFormatOptions" class="mt-1" />
        </div>
      </div>
    </UiCard>

    <!-- Right-to-Left (RTL) Support Section -->
    <UiCard title="Right-to-Left (RTL) Support">
      <div class="space-y-4">
        <UiToggle v-model="rtlSupport.enableRtlLayout" label="Enable RTL Layout for Supported Languages" description="Applies to languages like Hebrew, Arabic, etc." />
        <div>
          <label for="defaultTextDirection" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Default Text Direction for New Content</label>
          <UiSelect id="defaultTextDirection" :name="'defaultTextDirection'" v-model="rtlSupport.defaultTextDirection" :options="textDirectionOptions" class="mt-1" />
          <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Can be automatically inferred from the default platform language.</p>
        </div>
      </div>
    </UiCard>

  </div>
</template>

<script setup lang="ts">
import { reactive, computed } from 'vue';
import UiCard from '~/components/ui/UiCard.vue';
import UiToggle from '~/components/ui/UiToggle.vue';
import UiSelect from '~/components/ui/UiSelect.vue';
import UiCheckbox from '~/components/ui/UiCheckbox.vue'; // Assuming UiCheckbox is suitable for multi-select here

// TODO: Populate from i18n setup or a central configuration
const availableLanguages = [
  { label: 'English', value: 'en' },
  { label: 'Español (Spanish)', value: 'es' },
  { label: 'Français (French)', value: 'fr' },
  { label: 'עברית (Hebrew)', value: 'he' },
  { label: 'العربية (Arabic)', value: 'ar' },
];

// Language Management
const languageSettings = reactive({
  supportedLanguages: availableLanguages.reduce((acc, lang) => {
    acc[lang.value] = lang.value === 'en'; // Default English to true, others to false
    return acc;
  }, {} as Record<string, boolean>),
  defaultLanguage: 'en',
  allowUserSelection: true,
});

const platformLanguageOptions = computed(() => {
  return availableLanguages
    .filter(lang => languageSettings.supportedLanguages[lang.value])
    .map(lang => ({ label: lang.label, value: lang.value }));
});

// Regional Formatting
const regionalFormatting = reactive({
  numberFormat: 'comma_dot', // 1,234.56
  currency: 'USD',
  dateTimeFormat: 'MM/DD/YYYY_hh:mm_A', // MM/DD/YYYY hh:mm A
});

const numberFormatOptions = [
  { label: '1,234.56 (Comma thousands, Dot decimal)', value: 'comma_dot' },
  { label: '1.234,56 (Dot thousands, Comma decimal)', value: 'dot_comma' },
];

const currencyOptions = [ // Simplified list
  { label: 'USD - US Dollar', value: 'USD' },
  { label: 'EUR - Euro', value: 'EUR' },
  { label: 'GBP - British Pound', value: 'GBP' },
  { label: 'ILS - Israeli New Shekel', value: 'ILS' },
  { label: 'AED - UAE Dirham', value: 'AED' },
];

const dateTimeFormatOptions = [
  { label: 'MM/DD/YYYY hh:mm A (e.g., 06/18/2025 11:30 PM)', value: 'MM/DD/YYYY_hh:mm_A' },
  { label: 'DD/MM/YYYY HH:mm (e.g., 18/06/2025 23:30)', value: 'DD/MM/YYYY_HH:mm' },
  { label: 'YYYY-MM-DD HH:mm (e.g., 2025-06-18 23:30)', value: 'YYYY-MM-DD_HH:mm' },
];

// Right-to-Left (RTL) Support
const rtlSupport = reactive({
  enableRtlLayout: computed(() => // Auto-enable if Hebrew or Arabic is supported
    languageSettings.supportedLanguages.he || languageSettings.supportedLanguages.ar
  ),
  defaultTextDirection: computed(() => { // Auto-set based on default language
    const defaultLang = languageSettings.defaultLanguage;
    if (defaultLang === 'he' || defaultLang === 'ar') {
      return 'rtl';
    }
    return 'ltr';
  }),
});

const textDirectionOptions = [
  { label: 'Left-to-Right (LTR)', value: 'ltr' },
  { label: 'Right-to-Left (RTL)', value: 'rtl' },
];

// TODO: Load initial values from backend and implement save logic for these settings
// TODO: Ensure `UiCheckbox` v-model works as expected for the `supportedLanguages` object structure.
//       It might be better to have `supportedLanguages` as an array of strings if `UiCheckbox` is designed for boolean v-model.
//       Alternatively, a custom multi-select component or individual toggles per language might be needed.
</script>
