<!--
  Platform Settings Component
  
  General platform configuration and preferences
-->

<template>
  <div class="max-w-4xl mx-auto space-y-6">
    <!-- Header -->
    <div>
      <h1 class="text-2xl font-bold text-gray-900">Platform Settings</h1>
      <p class="mt-1 text-sm text-gray-500">
        Configure general platform preferences and defaults
      </p>
    </div>

    <!-- General Settings -->
    <div class="bg-white shadow rounded-lg p-6">
      <h2 class="text-lg font-medium text-gray-900 mb-6">General Settings</h2>
      
      <form @submit.prevent="saveSettings" class="space-y-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Organization Name -->
          <div>
            <label for="orgName" class="block text-sm font-medium text-gray-700 mb-2">
              Organization Name
            </label>
            <input
              id="orgName"
              v-model="settings.organizationName"
              type="text"
              class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
            />
          </div>

          <!-- Time Zone -->
          <div>
            <label for="timezone" class="block text-sm font-medium text-gray-700 mb-2">
              Default Time Zone
            </label>
            <select
              id="timezone"
              v-model="settings.timezone"
              class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
            >
              <option value="UTC">UTC</option>
              <option value="America/New_York">Eastern Time</option>
              <option value="America/Chicago">Central Time</option>
              <option value="America/Denver">Mountain Time</option>
              <option value="America/Los_Angeles">Pacific Time</option>
            </select>
          </div>

          <!-- Date Format -->
          <div>
            <label for="dateFormat" class="block text-sm font-medium text-gray-700 mb-2">
              Date Format
            </label>
            <select
              id="dateFormat"
              v-model="settings.dateFormat"
              class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
            >
              <option value="MM/DD/YYYY">MM/DD/YYYY</option>
              <option value="DD/MM/YYYY">DD/MM/YYYY</option>
              <option value="YYYY-MM-DD">YYYY-MM-DD</option>
            </select>
          </div>

          <!-- Currency -->
          <div>
            <label for="currency" class="block text-sm font-medium text-gray-700 mb-2">
              Default Currency
            </label>
            <select
              id="currency"
              v-model="settings.currency"
              class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
            >
              <option value="USD">USD - US Dollar</option>
              <option value="EUR">EUR - Euro</option>
              <option value="GBP">GBP - British Pound</option>
              <option value="CAD">CAD - Canadian Dollar</option>
            </select>
          </div>
        </div>

        <!-- Feature Toggles -->
        <div class="border-t border-gray-200 pt-6">
          <h3 class="text-sm font-medium text-gray-900 mb-4">Feature Settings</h3>
          
          <div class="space-y-4">
            <div class="flex items-center justify-between">
              <div>
                <h4 class="text-sm font-medium text-gray-900">Document Versioning</h4>
                <p class="text-sm text-gray-500">Enable automatic document version tracking</p>
              </div>
              <input
                v-model="settings.documentVersioning"
                type="checkbox"
                class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
              />
            </div>

            <div class="flex items-center justify-between">
              <div>
                <h4 class="text-sm font-medium text-gray-900">Email Notifications</h4>
                <p class="text-sm text-gray-500">Send email notifications for important events</p>
              </div>
              <input
                v-model="settings.emailNotifications"
                type="checkbox"
                class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
              />
            </div>

            <div class="flex items-center justify-between">
              <div>
                <h4 class="text-sm font-medium text-gray-900">Audit Logging</h4>
                <p class="text-sm text-gray-500">Track all user actions for compliance</p>
              </div>
              <input
                v-model="settings.auditLogging"
                type="checkbox"
                class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
              />
            </div>

            <div class="flex items-center justify-between">
              <div>
                <h4 class="text-sm font-medium text-gray-900">Two-Factor Authentication</h4>
                <p class="text-sm text-gray-500">Require 2FA for all users</p>
              </div>
              <input
                v-model="settings.requireTwoFactor"
                type="checkbox"
                class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
              />
            </div>
          </div>
        </div>

        <!-- Save Button -->
        <div class="flex justify-end border-t border-gray-200 pt-6">
          <button
            type="submit"
            :disabled="isSaving"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <UiSpinner v-if="isSaving" size="sm" color="white" class="mr-2" />
            {{ isSaving ? 'Saving...' : 'Save Settings' }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'

// State
const isSaving = ref(false)

const settings = reactive({
  organizationName: 'Legal SaaS Platform',
  timezone: 'America/New_York',
  dateFormat: 'MM/DD/YYYY',
  currency: 'USD',
  documentVersioning: true,
  emailNotifications: true,
  auditLogging: true,
  requireTwoFactor: false
})

// Methods
const saveSettings = async () => {
  try {
    isSaving.value = true
    
    // TODO: Replace with actual API call
    console.log('Saving platform settings:', settings)
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    alert('Settings saved successfully!')
    
  } catch (error) {
    console.error('Error saving settings:', error)
    alert('Failed to save settings. Please try again.')
  } finally {
    isSaving.value = false
  }
}

// Lifecycle
onMounted(() => {
  // Load current settings
})
</script>
