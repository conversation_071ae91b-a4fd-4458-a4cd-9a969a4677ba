<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900 py-12">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
      <!-- Header -->
      <div class="text-center mb-12">
        <h1 class="text-4xl font-bold text-gray-900 dark:text-white mb-4">
          Component Demos
        </h1>
        <p class="text-lg text-gray-600 dark:text-gray-400">
          Interactive demonstrations of UI components
        </p>
      </div>

      <!-- Demo Grid -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <!-- Color Picker Demo -->
        <NuxtLink 
          to="/demo/color-picker"
          class="group bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow"
        >
          <div class="flex items-center mb-4">
            <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
              <Icon name="heroicons:color-swatch" class="w-6 h-6 text-white" />
            </div>
            <h3 class="ml-4 text-lg font-semibold text-gray-900 dark:text-white group-hover:text-indigo-600 dark:group-hover:text-indigo-400">
              Color Picker
            </h3>
          </div>
          <p class="text-gray-600 dark:text-gray-400 text-sm">
            Interactive color picker with predefined colors, recent colors, and advanced HSV controls
          </p>
        </NuxtLink>

        <!-- Document Editor Demo -->
        <NuxtLink 
          to="/demo/document-editor"
          class="group bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow"
        >
          <div class="flex items-center mb-4">
            <div class="w-12 h-12 bg-gradient-to-r from-green-500 to-teal-600 rounded-lg flex items-center justify-center">
              <Icon name="heroicons:document-text" class="w-6 h-6 text-white" />
            </div>
            <h3 class="ml-4 text-lg font-semibold text-gray-900 dark:text-white group-hover:text-indigo-600 dark:group-hover:text-indigo-400">
              Document Editor
            </h3>
          </div>
          <p class="text-gray-600 dark:text-gray-400 text-sm">
            Rich text editor with formatting tools and document management features
          </p>
        </NuxtLink>

        <!-- Document Editor Toolbar Demo -->
        <NuxtLink 
          to="/demo/document-editor-toolbar"
          class="group bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow"
        >
          <div class="flex items-center mb-4">
            <div class="w-12 h-12 bg-gradient-to-r from-orange-500 to-red-600 rounded-lg flex items-center justify-center">
              <Icon name="heroicons:wrench-screwdriver" class="w-6 h-6 text-white" />
            </div>
            <h3 class="ml-4 text-lg font-semibold text-gray-900 dark:text-white group-hover:text-indigo-600 dark:group-hover:text-indigo-400">
              Editor Toolbar
            </h3>
          </div>
          <p class="text-gray-600 dark:text-gray-400 text-sm">
            Document editor toolbar with formatting options and tools
          </p>
        </NuxtLink>

        <!-- Placeholder for more demos -->
        <div class="bg-gray-100 dark:bg-gray-700 rounded-lg shadow-lg p-6 opacity-50">
          <div class="flex items-center mb-4">
            <div class="w-12 h-12 bg-gray-300 dark:bg-gray-600 rounded-lg flex items-center justify-center">
              <Icon name="heroicons:plus" class="w-6 h-6 text-gray-500" />
            </div>
            <h3 class="ml-4 text-lg font-semibold text-gray-500 dark:text-gray-400">
              More Demos
            </h3>
          </div>
          <p class="text-gray-500 dark:text-gray-400 text-sm">
            Additional component demos coming soon...
          </p>
        </div>
      </div>

      <!-- Navigation -->
      <div class="mt-12 text-center">
        <NuxtLink 
          to="/" 
          class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors"
        >
          ← Back to Home
        </NuxtLink>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// Page meta
definePageMeta({
  title: 'Component Demos',
  description: 'Interactive demonstrations of UI components'
})
</script>
