<template>
  <div class="space-y-8 py-8">
    <!-- Platform Overview Header -->
    <div class="bg-gradient-to-r from-brandPrimary-600 to-brandSecondary-600 rounded-xl p-8 text-white">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-3xl font-bold mb-2">Platform Dashboard</h1>
          <p class="text-brandPrimary-100 text-lg">
            Welcome back! Here's what's happening across your legal platform.
          </p>
        </div>
        <div class="hidden md:flex items-center gap-4">
          <div class="text-right">
            <p class="text-2xl font-bold">{{ formatTime(currentTime) }}</p>
            <p class="text-brandPrimary-100 text-sm">{{ formatDate(currentTime) }}</p>
          </div>
          <div class="w-16 h-16 bg-white/10 rounded-full flex items-center justify-center">
            <Icon name="material-symbols:dashboard" class="h-8 w-8" />
          </div>
        </div>
      </div>
    </div>

    <!-- Key Metrics Overview -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <!-- Total Tenants -->
      <UiCard
        icon="material-symbols:business"
        icon-color="blue"
        :loading="isLoading"
        hover-effect
      >
        <template #header>
          <div class="flex items-center justify-between w-full">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">{{ $t('dashboard.totalTenants') }}</h3>
            <UiBadge :variant="tenantGrowth > 0 ? 'success' : 'neutral'">
              {{ tenantGrowth > 0 ? '+' : '' }}{{ tenantGrowth }}%
            </UiBadge>
          </div>
        </template>
        <div class="space-y-2">
          <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ totalTenants }}</p>
          <p class="text-sm text-gray-500 dark:text-gray-400">
            {{ activeTenants }} active, {{ inactiveTenants }} inactive
          </p>
          <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div
              class="bg-blue-600 h-2 rounded-full transition-all duration-300"
              :style="{ width: `${tenantActivePercentage}%` }"
            ></div>
          </div>
        </div>
      </UiCard>

      <!-- Total Users -->
      <UiCard
        icon="material-symbols:group"
        icon-color="green"
        :loading="isLoading"
        hover-effect
      >
        <template #header>
          <div class="flex items-center justify-between w-full">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">{{ $t('dashboard.totalUsers') }}</h3>
            <UiBadge :variant="userGrowth > 0 ? 'success' : 'neutral'">
              {{ userGrowth > 0 ? '+' : '' }}{{ userGrowth }}%
            </UiBadge>
          </div>
        </template>
        <div class="space-y-2">
          <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ totalUsers }}</p>
          <p class="text-sm text-gray-500 dark:text-gray-400">
            {{ activeUsers }} active, {{ onlineUsers }} online now
          </p>
          <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div
              class="bg-green-600 h-2 rounded-full transition-all duration-300"
              :style="{ width: `${userActivePercentage}%` }"
            ></div>
          </div>
        </div>
      </UiCard>

      <!-- System Health -->
      <UiCard
        icon="material-symbols:health-and-safety"
        icon-color="purple"
        :loading="isLoading"
        hover-effect
      >
        <template #header>
          <div class="flex items-center justify-between w-full">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">System Health</h3>
            <UiBadge :variant="systemHealthStatus === 'healthy' ? 'success' : systemHealthStatus === 'warning' ? 'warning' : 'error'">
              {{ systemHealthStatus }}
            </UiBadge>
          </div>
        </template>
        <div class="space-y-2">
          <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ systemHealthScore }}%</p>
          <p class="text-sm text-gray-500 dark:text-gray-400">
            {{ healthyServices }}/{{ totalServices }} services healthy
          </p>
          <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div
              :class="[
                'h-2 rounded-full transition-all duration-300',
                systemHealthScore >= 90 ? 'bg-green-600' :
                systemHealthScore >= 70 ? 'bg-yellow-600' : 'bg-red-600'
              ]"
              :style="{ width: `${systemHealthScore}%` }"
            ></div>
          </div>
        </div>
      </UiCard>

      <!-- Monthly Revenue -->
      <UiCard
        icon="material-symbols:trending-up"
        icon-color="yellow"
        :loading="isLoading"
        hover-effect
      >
        <template #header>
          <div class="flex items-center justify-between w-full">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">{{ $t('dashboard.monthlyRevenue') }}</h3>
            <UiBadge :variant="revenueGrowth > 0 ? 'success' : 'neutral'">
              {{ revenueGrowth > 0 ? '+' : '' }}{{ revenueGrowth }}%
            </UiBadge>
          </div>
        </template>
        <div class="space-y-2">
          <p class="text-3xl font-bold text-gray-900 dark:text-white">${{ formatCurrency(monthlyRevenue) }}</p>
          <p class="text-sm text-gray-500 dark:text-gray-400">
            ${{ formatCurrency(dailyRevenue) }} today
          </p>
          <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div
              class="bg-yellow-600 h-2 rounded-full transition-all duration-300"
              :style="{ width: `${revenueProgress}%` }"
            ></div>
          </div>
        </div>
      </UiCard>
    </div>

    <!-- Quick Actions -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <UiCard
        hover-effect
        clickable
        @click="navigateTo('/dashboard/tenants/create')"
        class="group cursor-pointer"
      >
        <div class="text-center p-6">
          <div class="w-16 h-16 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-200">
            <Icon name="material-symbols:add-business" class="h-8 w-8 text-blue-600 dark:text-blue-400" />
          </div>
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Create Tenant</h3>
          <p class="text-sm text-gray-500 dark:text-gray-400">Add a new tenant to the platform</p>
        </div>
      </UiCard>

      <UiCard
        hover-effect
        clickable
        @click="navigateTo('/dashboard/users/invite')"
        class="group cursor-pointer"
      >
        <div class="text-center p-6">
          <div class="w-16 h-16 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-200">
            <Icon name="material-symbols:person-add" class="h-8 w-8 text-green-600 dark:text-green-400" />
          </div>
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Invite User</h3>
          <p class="text-sm text-gray-500 dark:text-gray-400">Invite new users to the platform</p>
        </div>
      </UiCard>

      <UiCard
        hover-effect
        clickable
        @click="navigateTo('/dashboard/platform/health')"
        class="group cursor-pointer"
      >
        <div class="text-center p-6">
          <div class="w-16 h-16 bg-purple-100 dark:bg-purple-900/20 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-200">
            <Icon name="material-symbols:monitor-heart" class="h-8 w-8 text-purple-600 dark:text-purple-400" />
          </div>
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">System Health</h3>
          <p class="text-sm text-gray-500 dark:text-gray-400">Monitor system performance</p>
        </div>
      </UiCard>

      <UiCard
        hover-effect
        clickable
        @click="navigateTo('/dashboard/platform/analytics/usage')"
        class="group cursor-pointer"
      >
        <div class="text-center p-6">
          <div class="w-16 h-16 bg-yellow-100 dark:bg-yellow-900/20 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-200">
            <Icon name="material-symbols:analytics" class="h-8 w-8 text-yellow-600 dark:text-yellow-400" />
          </div>
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">View Analytics</h3>
          <p class="text-sm text-gray-500 dark:text-gray-400">Analyze platform usage data</p>
        </div>
      </UiCard>
    </div>
    <!-- Recent Activity & System Status -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Recent Activity -->
      <UiCard>
        <template #header>
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Recent Activity</h3>
            <UiButton @click="navigateTo('/dashboard/platform/audit-logs')" variant="ghost" size="sm">
              View All
            </UiButton>
          </div>
        </template>
        <div class="space-y-3">
          <div
            v-for="activity in recentActivity"
            :key="activity.id"
            class="flex items-start gap-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors duration-200"
          >
            <div class="flex-shrink-0">
              <div :class="[
                'w-8 h-8 rounded-full flex items-center justify-center',
                activity.type === 'success' ? 'bg-green-100 dark:bg-green-900/20' :
                activity.type === 'warning' ? 'bg-yellow-100 dark:bg-yellow-900/20' :
                activity.type === 'error' ? 'bg-red-100 dark:bg-red-900/20' :
                'bg-blue-100 dark:bg-blue-900/20'
              ]">
                <Icon :name="activity.icon" :class="[
                  'h-4 w-4',
                  activity.type === 'success' ? 'text-green-600 dark:text-green-400' :
                  activity.type === 'warning' ? 'text-yellow-600 dark:text-yellow-400' :
                  activity.type === 'error' ? 'text-red-600 dark:text-red-400' :
                  'text-blue-600 dark:text-blue-400'
                ]" />
              </div>
            </div>
            <div class="flex-1 min-w-0">
              <p class="text-sm text-gray-900 dark:text-white">{{ activity.description }}</p>
              <p class="text-xs text-gray-500 dark:text-gray-400">{{ activity.timestamp }}</p>
            </div>
          </div>

          <div v-if="recentActivity.length === 0" class="text-center py-6">
            <Icon name="material-symbols:timeline" class="h-8 w-8 text-gray-400 mx-auto mb-2" />
            <p class="text-sm text-gray-500 dark:text-gray-400">No recent activity</p>
          </div>
        </div>
      </UiCard>

      <!-- System Status -->
      <UiCard>
        <template #header>
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">System Status</h3>
            <UiBadge :variant="overallSystemStatus === 'operational' ? 'success' : 'warning'">
              {{ overallSystemStatus }}
            </UiBadge>
          </div>
        </template>
        <div class="space-y-4">
          <div
            v-for="service in systemServices"
            :key="service.name"
            class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg"
          >
            <div class="flex items-center gap-3">
              <div :class="[
                'w-3 h-3 rounded-full',
                service.status === 'operational' ? 'bg-green-500' :
                service.status === 'degraded' ? 'bg-yellow-500' :
                'bg-red-500'
              ]"></div>
              <div>
                <p class="text-sm font-medium text-gray-900 dark:text-white">{{ service.name }}</p>
                <p class="text-xs text-gray-500 dark:text-gray-400">{{ service.description }}</p>
              </div>
            </div>
            <div class="text-right">
              <p class="text-sm text-gray-900 dark:text-white">{{ service.uptime }}%</p>
              <p class="text-xs text-gray-500 dark:text-gray-400">uptime</p>
            </div>
          </div>
        </div>
      </UiCard>
    </div>

    <!-- Platform Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
      <!-- Storage Usage -->
      <UiCard>
        <template #header>
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Storage Usage</h3>
            <Icon name="material-symbols:storage" class="h-5 w-5 text-gray-400" />
          </div>
        </template>
        <div class="space-y-4">
          <div class="text-center">
            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ storageUsed }}GB</p>
            <p class="text-sm text-gray-500 dark:text-gray-400">of {{ storageLimit }}GB used</p>
          </div>
          <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3">
            <div
              :class="[
                'h-3 rounded-full transition-all duration-300',
                storagePercentage >= 90 ? 'bg-red-600' :
                storagePercentage >= 70 ? 'bg-yellow-600' : 'bg-blue-600'
              ]"
              :style="{ width: `${storagePercentage}%` }"
            ></div>
          </div>
          <p class="text-xs text-center text-gray-500 dark:text-gray-400">
            {{ storagePercentage }}% used
          </p>
        </div>
      </UiCard>

      <!-- API Usage -->
      <UiCard>
        <template #header>
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">API Usage</h3>
            <Icon name="material-symbols:api" class="h-5 w-5 text-gray-400" />
          </div>
        </template>
        <div class="space-y-4">
          <div class="text-center">
            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ apiCallsToday }}</p>
            <p class="text-sm text-gray-500 dark:text-gray-400">API calls today</p>
          </div>
          <div class="space-y-2">
            <div class="flex justify-between text-sm">
              <span class="text-gray-500 dark:text-gray-400">Success Rate</span>
              <span class="text-gray-900 dark:text-white">{{ apiSuccessRate }}%</span>
            </div>
            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div
                class="bg-green-600 h-2 rounded-full transition-all duration-300"
                :style="{ width: `${apiSuccessRate}%` }"
              ></div>
            </div>
          </div>
        </div>
      </UiCard>

      <!-- Active Sessions -->
      <UiCard>
        <template #header>
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Active Sessions</h3>
            <Icon name="material-symbols:person-play" class="h-5 w-5 text-gray-400" />
          </div>
        </template>
        <div class="space-y-4">
          <div class="text-center">
            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ activeSessions }}</p>
            <p class="text-sm text-gray-500 dark:text-gray-400">users online</p>
          </div>
          <div class="space-y-2">
            <div class="flex justify-between text-sm">
              <span class="text-gray-500 dark:text-gray-400">Peak Today</span>
              <span class="text-gray-900 dark:text-white">{{ peakSessions }}</span>
            </div>
            <div class="flex justify-between text-sm">
              <span class="text-gray-500 dark:text-gray-400">Avg Session</span>
              <span class="text-gray-900 dark:text-white">{{ avgSessionDuration }}m</span>
            </div>
          </div>
        </div>
      </UiCard>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useTenantStore } from '~/stores/tenant'
import { useUserStore } from '~/stores/user'
import { PlatformRoles } from '~/app/features/auth/constants/roles'

// Page meta configuration
definePageMeta({
  layout: 'dashboard',
  title: 'Platform Dashboard',
  description: 'Overview of platform metrics, system health, and recent activity',
  pageHeaderIcon: 'material-symbols:dashboard',
  showRealTimeStatus: true,
  autoRefreshEnabled: true,
  refreshInterval: 30000, // 30 seconds
  middleware: ['rbac'],
  roles: [PlatformRoles.SUPER_ADMIN],
  breadcrumb: [
    { label: 'Dashboard', href: '/dashboard' },
    { label: 'Platform Overview' },
  ],
})

// Stores
const tenantStore = useTenantStore()
const userStore = useUserStore()

// Reactive state
const isLoading = ref(true)
const currentTime = ref(new Date())
const refreshInterval = ref<NodeJS.Timeout | null>(null)

// Mock data for demonstration (in real app, these would come from APIs)
const mockMetrics = ref({
  tenantGrowth: 12.5,
  userGrowth: 8.3,
  revenueGrowth: 15.2,
  monthlyRevenue: 45750,
  dailyRevenue: 1525,
  systemHealthScore: 98,
  healthyServices: 12,
  totalServices: 12,
  storageUsed: 245,
  storageLimit: 1000,
  apiCallsToday: 15420,
  apiSuccessRate: 99.2,
  activeSessions: 127,
  peakSessions: 189,
  avgSessionDuration: 24
})

// Computed properties for metrics
const totalTenants = computed(() => tenantStore.tenants.length)
const activeTenants = computed(() => tenantStore.tenants.filter(t => t.isActive).length)
const inactiveTenants = computed(() => totalTenants.value - activeTenants.value)
const tenantActivePercentage = computed(() =>
  totalTenants.value > 0 ? Math.round((activeTenants.value / totalTenants.value) * 100) : 0
)

const totalUsers = computed(() => userStore.users.length)
const activeUsers = computed(() => userStore.users.filter(u => u.isActive).length)
const onlineUsers = computed(() => Math.floor(activeUsers.value * 0.15)) // Mock 15% online
const userActivePercentage = computed(() =>
  totalUsers.value > 0 ? Math.round((activeUsers.value / totalUsers.value) * 100) : 0
)

// System health computed properties
const systemHealthScore = computed(() => mockMetrics.value.systemHealthScore)
const healthyServices = computed(() => mockMetrics.value.healthyServices)
const totalServices = computed(() => mockMetrics.value.totalServices)
const systemHealthStatus = computed(() => {
  if (systemHealthScore.value >= 95) return 'healthy'
  if (systemHealthScore.value >= 80) return 'warning'
  return 'critical'
})

// Revenue computed properties
const monthlyRevenue = computed(() => mockMetrics.value.monthlyRevenue)
const dailyRevenue = computed(() => mockMetrics.value.dailyRevenue)
const revenueProgress = computed(() => {
  const target = 50000 // Monthly target
  return Math.min(Math.round((monthlyRevenue.value / target) * 100), 100)
})

// Growth metrics
const tenantGrowth = computed(() => mockMetrics.value.tenantGrowth)
const userGrowth = computed(() => mockMetrics.value.userGrowth)
const revenueGrowth = computed(() => mockMetrics.value.revenueGrowth)

// Storage metrics
const storageUsed = computed(() => mockMetrics.value.storageUsed)
const storageLimit = computed(() => mockMetrics.value.storageLimit)
const storagePercentage = computed(() =>
  Math.round((storageUsed.value / storageLimit.value) * 100)
)

// API metrics
const apiCallsToday = computed(() => mockMetrics.value.apiCallsToday)
const apiSuccessRate = computed(() => mockMetrics.value.apiSuccessRate)

// Session metrics
const activeSessions = computed(() => mockMetrics.value.activeSessions)
const peakSessions = computed(() => mockMetrics.value.peakSessions)
const avgSessionDuration = computed(() => mockMetrics.value.avgSessionDuration)

// System status
const overallSystemStatus = computed(() => {
  const healthScore = systemHealthScore.value
  return healthScore >= 95 ? 'operational' : 'degraded'
})

const systemServices = computed(() => [
  {
    name: 'API Gateway',
    description: 'Core API services',
    status: 'operational',
    uptime: 99.9
  },
  {
    name: 'Database',
    description: 'Primary database cluster',
    status: 'operational',
    uptime: 99.8
  },
  {
    name: 'File Storage',
    description: 'Document storage service',
    status: 'operational',
    uptime: 99.7
  },
  {
    name: 'Email Service',
    description: 'Notification delivery',
    status: 'operational',
    uptime: 99.5
  },
  {
    name: 'Search Engine',
    description: 'Document search indexing',
    status: 'operational',
    uptime: 98.9
  }
])

// Recent activity data
const recentActivity = computed(() => [
  {
    id: 1,
    icon: 'material-symbols:business',
    description: 'New tenant "Legal Partners LLC" was created',
    timestamp: '2 minutes ago',
    type: 'success'
  },
  {
    id: 2,
    icon: 'material-symbols:person-add',
    description: 'User "<EMAIL>" was invited to platform',
    timestamp: '5 minutes ago',
    type: 'info'
  },
  {
    id: 3,
    icon: 'material-symbols:warning',
    description: 'High API usage detected for tenant "Acme Corp"',
    timestamp: '12 minutes ago',
    type: 'warning'
  },
  {
    id: 4,
    icon: 'material-symbols:security',
    description: 'Security scan completed successfully',
    timestamp: '18 minutes ago',
    type: 'success'
  },
  {
    id: 5,
    icon: 'material-symbols:backup',
    description: 'Daily backup completed',
    timestamp: '1 hour ago',
    type: 'success'
  }
])

// Utility functions
const formatTime = (date: Date) => {
  return date.toLocaleTimeString('en-US', {
    hour: '2-digit',
    minute: '2-digit',
    hour12: true
  })
}

const formatDate = (date: Date) => {
  return date.toLocaleDateString('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-US').format(amount)
}

// Methods
const updateCurrentTime = () => {
  currentTime.value = new Date()
}

const refreshData = async () => {
  try {
    isLoading.value = true

    // Fetch fresh data from stores
    await Promise.all([
      tenantStore.fetchAllTenants(),
      userStore.fetchAllUsers()
    ])

    // In a real app, you would also fetch system metrics here
    // await fetchSystemMetrics()

  } catch (error) {
    console.error('Error refreshing dashboard data:', error)
  } finally {
    isLoading.value = false
  }
}

// Lifecycle hooks
onMounted(async () => {
  // Initial data load
  await refreshData()

  // Set up time update interval
  const timeInterval = setInterval(updateCurrentTime, 1000)

  // Set up data refresh interval (every 30 seconds)
  refreshInterval.value = setInterval(refreshData, 30000)

  // Cleanup function
  onUnmounted(() => {
    clearInterval(timeInterval)
    if (refreshInterval.value) {
      clearInterval(refreshInterval.value)
    }
  })
})
</script>

<style scoped>
/* Enhanced animations and transitions */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* Gradient background animation */
@keyframes gradient-shift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

.bg-gradient-to-r {
  background-size: 200% 200%;
  animation: gradient-shift 10s ease infinite;
}

/* Card hover effects */
.group:hover .group-hover\:scale-110 {
  transform: scale(1.1);
}

/* Progress bar animations */
@keyframes progress-fill {
  from {
    width: 0%;
  }
  to {
    width: var(--progress-width);
  }
}

.transition-all {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Status indicator pulse */
@keyframes pulse-dot {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.bg-green-500 {
  animation: pulse-dot 2s infinite;
}

/* Loading shimmer effect */
@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.animate-pulse {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
  .animate-pulse {
    background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
    background-size: 200px 100%;
  }
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .grid-cols-1 {
    gap: 1rem;
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Focus states */
.focus-ring:focus {
  outline: 2px solid var(--color-brandPrimary-500);
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .border-gray-200 {
    border-color: #000;
  }

  .text-gray-500 {
    color: #000;
  }
}
</style>