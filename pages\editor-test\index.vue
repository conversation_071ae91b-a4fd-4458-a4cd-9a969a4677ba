<template>
  <div class="editor-canvas">
    <EditorContent :editor="editor" />
  </div>
</template>
<script setup lang="ts">
import '~/assets/css/document-editor.css'
import { useEditor, EditorContent } from "@tiptap/vue-3";
// import StarterKit from '@tiptap/starter-kit'

// import { getCurrentlyFocusedNode } from '~/app/shared/composables/core/editors/utils/utils'

// import { Pages } from '~/app/shared/composables/core/editors/pages'
import { Doc } from "~/app/shared/composables/core/editors/page";
definePageMeta({
  layout: "canvas",
});

const content = {
  type: "doc",
  content: [
    {
      type: "page",
      content: [
        {
          type: "paragraph",
          content: [{ type: "text", text: " " }],
        },
      ],
    },
  ],
};
const editor = useEditor({
  extensions: [ Doc ],
  content
});
</script>
<style scope>
p {
  border: 1px dashed blue;
  padding: 1rem;
}
</style>
