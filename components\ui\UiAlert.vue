<template>
  <div :class="['p-4 rounded-md', alertClasses]">
    <div class="flex">
      <div class="flex-shrink-0">
        <IconInfo v-if="type === 'info'" class="h-5 w-5" :class="iconClasses" aria-hidden="true" />
        <IconCheckCircle v-if="type === 'success'" class="h-5 w-5" :class="iconClasses" aria-hidden="true" />
        <IconWarning v-if="type === 'warning'" class="h-5 w-5" :class="iconClasses" aria-hidden="true" />
        <IconXCircle v-if="type === 'error'" class="h-5 w-5" :class="iconClasses" aria-hidden="true" />
      </div>
      <div class="ml-3">
        <h3 class="text-sm font-medium" :class="textClasses">{{ title }}</h3>
        <div class="mt-2 text-sm" :class="textClasses">
          <p><slot></slot></p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, computed } from 'vue';
import IconInfo from '../icons/IconInfo.vue';
import IconCheckCircle from '../icons/IconCheckCircle.vue';
import IconWarning from '../icons/IconWarning.vue';
import IconXCircle from '../icons/IconXCircle.vue';

const props = defineProps<{
  type?: 'info' | 'success' | 'warning' | 'error';
  title: string;
}>();

const alertClasses = computed(() => {
  switch (props.type) {
    case 'success': return 'bg-brandSuccess-lightBg border border-brandSuccess-lightBorder';
    case 'error': return 'bg-brandDanger-lightBg border border-brandDanger-lightBorder';
    case 'warning': return 'bg-brandWarning-lightBg border border-brandWarning-lightBorder';
    case 'info':
    default: return 'bg-brandInfo-lightBg border border-brandInfo-lightBorder';
  }
});

const iconClasses = computed(() => {
  switch (props.type) {
    // Using the main brand color for icons, as it's usually more vibrant for an icon.
    case 'success': return 'text-brandSuccess';
    case 'error': return 'text-brandDanger';
    case 'warning': return 'text-brandWarning';
    case 'info':
    default: return 'text-brandInfo';
  }
});

const textClasses = computed(() => {
  switch (props.type) {
    case 'success': return 'text-brandSuccess-darkText';
    case 'error': return 'text-brandDanger-darkText';
    case 'warning': return 'text-brandWarning-darkText';
    case 'info':
    default: return 'text-brandInfo-darkText';
  }
});
</script>

<style scoped>
/* Tailwind classes */
</style>