import { defineStore } from 'pinia';
import { useApi } from '~/composables/useApi';
import type { GetRequestQuery } from '~/types/api';

// Define interfaces for better type safety and clarity
export interface AuditLog {
  id: string;
  createdAt: string;
  updatedAt: string;
  createdById: string | null;
  updatedById: string | null;
  isDeleted: boolean;
  deletedAt: string | null;
  action: string; // e.g., "AuditLogsController.findAll"
  actorEmail: string | null;
  actorId: string | null; // ID of the user who performed the action
  actorRole: string | null;
  after: Record<string, any> | null; // JSON object representing the state after the action
  before: Record<string, any> | null; // JSON object representing the state before the action
  description: string | null; // JSON string with details like path, method, duration
  entityId: string | null; // ID of the entity affected by the action
  entityName: string | null; // e.g., 'AuditLogsController', 'Tenant'
  ipAddress: string | null;
  tenantId: string | null;
  userAgent: string | null;
}

// Extended query interface for audit logs
interface AuditLogQuery extends GetRequestQuery {
  relations?: string; // Comma-separated relation names
  actorId?: string; // Filter by actor ID
  action?: string; // Filter by action type
  entityName?: string; // Filter by entity name
  entityId?: string; // Filter by entity ID
  startDate?: string; // Filter by start date (ISO string)
  endDate?: string; // Filter by end date (ISO string)
}

interface AuditLogsResponse {
  data: AuditLog[];
  meta: {
    total: number;
    page: number;
    limit: number;
  };
}

export const useAuditLogStore = defineStore('auditLog', {
  state: () => ({
    auditLogs: [] as AuditLog[],
    auditLogsMeta: null as AuditLogsResponse['meta'] | null,
    isLoading: false,
    error: null as string | null,
  }),

  getters: {
    hasAuditLogs: (state) => state.auditLogs.length > 0,
  },

  actions: {
    /**
     * Fetches all audit logs.
     * API: GET /audit-logs
     * @param query Optional query parameters for pagination, sorting, and filtering.
     */
    async fetchAllAuditLogs(query?: AuditLogQuery): Promise<void> {
      this.isLoading = true;
      this.error = null;
      try {
        const api = useApi();
        // Ensure query is an object, even if empty
        const params = query || {};
        const response = await api.get<AuditLogsResponse>('/audit-logs', { params });
        this.auditLogs = response.data;
        this.auditLogsMeta = response.meta;
        console.log('All audit logs fetched with query:', query, this.auditLogs, this.auditLogsMeta);
      } catch (err: any) {
        this.error = this.handleApiError(err, 'Failed to fetch audit logs.');
        this.auditLogs = []; // Clear audit logs on error
        this.auditLogsMeta = null; // Clear meta on error
        console.error('Error fetching audit logs:', err);
      } finally {
        this.isLoading = false;
      }
    },

    /**
     * Helper to handle API errors consistently.
     */
    handleApiError(err: any, defaultMessage: string): string {
      if (err.response && err.response.data && err.response.data.message) {
        if (Array.isArray(err.response.data.message)) {
          return err.response.data.message.join(', ');
        }
        return err.response.data.message;
      }
      return err.message || defaultMessage;
    },

    /**
     * Placeholder for a global notification service.
     */
    showSuccessNotification(message: string) {
      // TODO: Implement actual notification display using a toast service
      console.log('Success:', message);
      // Example: useToast().add({ severity: 'success', summary: 'Success', detail: message, life: 3000 });
    },
  },
});