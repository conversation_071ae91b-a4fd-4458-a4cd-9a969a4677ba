<!--
  Security Settings Component
  
  Security policies and access control configuration
-->

<template>
  <div class="max-w-4xl mx-auto space-y-6">
    <!-- Header -->
    <div>
      <h1 class="text-2xl font-bold text-gray-900">Security Settings</h1>
      <p class="mt-1 text-sm text-gray-500">
        Manage security policies and access controls
      </p>
    </div>

    <!-- Password Policy -->
    <div class="bg-white shadow rounded-lg p-6">
      <h2 class="text-lg font-medium text-gray-900 mb-6">Password Policy</h2>
      
      <form @submit.prevent="savePasswordPolicy" class="space-y-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Minimum Length -->
          <div>
            <label for="minLength" class="block text-sm font-medium text-gray-700 mb-2">
              Minimum Password Length
            </label>
            <input
              id="minLength"
              v-model="passwordPolicy.minLength"
              type="number"
              min="6"
              max="32"
              class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
            />
          </div>

          <!-- Password Expiry -->
          <div>
            <label for="expiryDays" class="block text-sm font-medium text-gray-700 mb-2">
              Password Expiry (days)
            </label>
            <input
              id="expiryDays"
              v-model="passwordPolicy.expiryDays"
              type="number"
              min="0"
              class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
            />
            <p class="mt-1 text-xs text-gray-500">Set to 0 for no expiry</p>
          </div>
        </div>

        <!-- Password Requirements -->
        <div>
          <h3 class="text-sm font-medium text-gray-900 mb-4">Password Requirements</h3>
          
          <div class="space-y-3">
            <div class="flex items-center">
              <input
                v-model="passwordPolicy.requireUppercase"
                type="checkbox"
                class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
              />
              <label class="ml-2 block text-sm text-gray-900">
                Require uppercase letters
              </label>
            </div>

            <div class="flex items-center">
              <input
                v-model="passwordPolicy.requireLowercase"
                type="checkbox"
                class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
              />
              <label class="ml-2 block text-sm text-gray-900">
                Require lowercase letters
              </label>
            </div>

            <div class="flex items-center">
              <input
                v-model="passwordPolicy.requireNumbers"
                type="checkbox"
                class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
              />
              <label class="ml-2 block text-sm text-gray-900">
                Require numbers
              </label>
            </div>

            <div class="flex items-center">
              <input
                v-model="passwordPolicy.requireSpecialChars"
                type="checkbox"
                class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
              />
              <label class="ml-2 block text-sm text-gray-900">
                Require special characters
              </label>
            </div>
          </div>
        </div>

        <div class="flex justify-end">
          <button
            type="submit"
            :disabled="isSavingPassword"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <UiSpinner v-if="isSavingPassword" size="sm" color="white" class="mr-2" />
            {{ isSavingPassword ? 'Saving...' : 'Save Password Policy' }}
          </button>
        </div>
      </form>
    </div>

    <!-- Session Management -->
    <div class="bg-white shadow rounded-lg p-6">
      <h2 class="text-lg font-medium text-gray-900 mb-6">Session Management</h2>
      
      <form @submit.prevent="saveSessionSettings" class="space-y-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Session Timeout -->
          <div>
            <label for="sessionTimeout" class="block text-sm font-medium text-gray-700 mb-2">
              Session Timeout (minutes)
            </label>
            <input
              id="sessionTimeout"
              v-model="sessionSettings.timeout"
              type="number"
              min="5"
              class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
            />
          </div>

          <!-- Max Concurrent Sessions -->
          <div>
            <label for="maxSessions" class="block text-sm font-medium text-gray-700 mb-2">
              Max Concurrent Sessions
            </label>
            <input
              id="maxSessions"
              v-model="sessionSettings.maxConcurrent"
              type="number"
              min="1"
              class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
            />
          </div>
        </div>

        <div class="flex justify-end">
          <button
            type="submit"
            :disabled="isSavingSession"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <UiSpinner v-if="isSavingSession" size="sm" color="white" class="mr-2" />
            {{ isSavingSession ? 'Saving...' : 'Save Session Settings' }}
          </button>
        </div>
      </form>
    </div>

    <!-- Access Control -->
    <div class="bg-white shadow rounded-lg p-6">
      <h2 class="text-lg font-medium text-gray-900 mb-6">Access Control</h2>
      
      <div class="space-y-4">
        <div class="flex items-center justify-between">
          <div>
            <h3 class="text-sm font-medium text-gray-900">IP Restrictions</h3>
            <p class="text-sm text-gray-500">Restrict access to specific IP addresses</p>
          </div>
          <input
            v-model="accessControl.ipRestrictions"
            type="checkbox"
            class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
          />
        </div>

        <div class="flex items-center justify-between">
          <div>
            <h3 class="text-sm font-medium text-gray-900">Device Registration</h3>
            <p class="text-sm text-gray-500">Require device registration for new logins</p>
          </div>
          <input
            v-model="accessControl.deviceRegistration"
            type="checkbox"
            class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
          />
        </div>

        <div class="flex items-center justify-between">
          <div>
            <h3 class="text-sm font-medium text-gray-900">Login Notifications</h3>
            <p class="text-sm text-gray-500">Send email notifications for new logins</p>
          </div>
          <input
            v-model="accessControl.loginNotifications"
            type="checkbox"
            class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
          />
        </div>
      </div>

      <div class="mt-6 flex justify-end">
        <button
          @click="saveAccessControl"
          :disabled="isSavingAccess"
          class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <UiSpinner v-if="isSavingAccess" size="sm" color="white" class="mr-2" />
          {{ isSavingAccess ? 'Saving...' : 'Save Access Control' }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'

// State
const isSavingPassword = ref(false)
const isSavingSession = ref(false)
const isSavingAccess = ref(false)

const passwordPolicy = reactive({
  minLength: 8,
  expiryDays: 90,
  requireUppercase: true,
  requireLowercase: true,
  requireNumbers: true,
  requireSpecialChars: false
})

const sessionSettings = reactive({
  timeout: 30,
  maxConcurrent: 3
})

const accessControl = reactive({
  ipRestrictions: false,
  deviceRegistration: true,
  loginNotifications: true
})

// Methods
const savePasswordPolicy = async () => {
  try {
    isSavingPassword.value = true
    
    // TODO: Replace with actual API call
    console.log('Saving password policy:', passwordPolicy)
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    alert('Password policy saved successfully!')
    
  } catch (error) {
    console.error('Error saving password policy:', error)
    alert('Failed to save password policy. Please try again.')
  } finally {
    isSavingPassword.value = false
  }
}

const saveSessionSettings = async () => {
  try {
    isSavingSession.value = true
    
    // TODO: Replace with actual API call
    console.log('Saving session settings:', sessionSettings)
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    alert('Session settings saved successfully!')
    
  } catch (error) {
    console.error('Error saving session settings:', error)
    alert('Failed to save session settings. Please try again.')
  } finally {
    isSavingSession.value = false
  }
}

const saveAccessControl = async () => {
  try {
    isSavingAccess.value = true
    
    // TODO: Replace with actual API call
    console.log('Saving access control:', accessControl)
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    alert('Access control settings saved successfully!')
    
  } catch (error) {
    console.error('Error saving access control:', error)
    alert('Failed to save access control. Please try again.')
  } finally {
    isSavingAccess.value = false
  }
}

// Lifecycle
onMounted(() => {
  // Load current security settings
})
</script>
