<template>
  <div>
    <!-- Label -->
    <label 
      v-if="label" 
      :for="`${id}-0`"
      :class="[
        'block text-sm font-semibold transition-colors duration-200 mb-3',
        hasErrors ? 'text-brandDanger' : 'text-gray-700 dark:text-gray-300',
        required ? 'after:content-[\'*\'] after:text-brandDanger after:ml-1' : ''
      ]"
    >
      {{ label }}
    </label>

    <!-- Email Inputs -->
    <div class="space-y-3">
      <div
        v-for="(email, index) in emails"
        :key="index"
        class="flex items-start gap-3"
      >
        <div class="flex-1">
          <UiInput
            :id="`${id}-${index}`"
            :name="`${name}-${index}`"
            v-model="emails[index]"
            type="email"
            :placeholder="placeholder"
            :error-message="errors[index]"
            :leading-icon="leadingIcon"
            :required="required && index === 0"
            :disabled="disabled"
            :readonly="readonly"
            @blur="validateEmail(index)"
            @input="handleEmailInput(index)"
          />
        </div>
        <div class="flex items-center gap-2 pt-2">
          <UiButton
            v-if="emails.length > minEmails"
            type="button"
            variant="ghost"
            size="sm"
            shape="circle"
            :disabled="disabled"
            @click="removeEmail(index)"
            :aria-label="`Remove email ${index + 1}`"
          >
            <Icon name="material-symbols:close" class="h-4 w-4" />
          </UiButton>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="flex items-center gap-3">
        <UiButton
          v-if="emails.length < maxEmails"
          type="button"
          variant="ghost"
          size="sm"
          :leading-icon="addIcon"
          :disabled="disabled"
          @click="addEmail"
          class="flex-1"
        >
          {{ addButtonText }}
        </UiButton>
        <UiButton
          v-if="showBulkImport"
          type="button"
          variant="outline"
          size="sm"
          :leading-icon="bulkImportIcon"
          :disabled="disabled"
          @click="$emit('bulk-import')"
        >
          {{ bulkImportText }}
        </UiButton>
      </div>
    </div>

    <!-- Help Text -->
    <p 
      v-if="helpText && !hasErrors" 
      class="text-sm text-gray-500 dark:text-gray-400 mt-2"
    >
      {{ helpText }}
    </p>

    <!-- Global Error Message -->
    <Transition name="error-slide">
      <div v-if="errorMessage" class="mt-2 flex items-start space-x-2">
        <Icon name="material-symbols:error" class="h-4 w-4 text-brandDanger flex-shrink-0 mt-0.5" />
        <p class="text-sm text-brandDanger font-medium">{{ errorMessage }}</p>
      </div>
    </Transition>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch, nextTick } from 'vue'

interface Props {
  id: string
  name: string
  modelValue?: string[]
  label?: string
  placeholder?: string
  helpText?: string
  errorMessage?: string
  required?: boolean
  disabled?: boolean
  readonly?: boolean
  leadingIcon?: string
  minEmails?: number
  maxEmails?: number
  addButtonText?: string
  addIcon?: string
  showBulkImport?: boolean
  bulkImportText?: string
  bulkImportIcon?: string
  validateOnInput?: boolean
  allowDuplicates?: boolean
}

interface Emits {
  (e: 'update:modelValue', value: string[]): void
  (e: 'bulk-import'): void
  (e: 'validate', isValid: boolean): void
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: 'Enter email address',
  leadingIcon: 'material-symbols:mail',
  minEmails: 1,
  maxEmails: 50,
  addButtonText: 'Add Another Email',
  addIcon: 'material-symbols:add',
  showBulkImport: false,
  bulkImportText: 'Import CSV',
  bulkImportIcon: 'material-symbols:upload-file',
  validateOnInput: true,
  allowDuplicates: false
})

const emit = defineEmits<Emits>()

// Internal state
const emails = ref<string[]>([])
const errors = ref<string[]>([])

// Initialize emails array
const initializeEmails = () => {
  if (props.modelValue && props.modelValue.length > 0) {
    emails.value = [...props.modelValue]
  } else {
    emails.value = [''] // Start with one empty email
  }
  errors.value = new Array(emails.value.length).fill('')
}

// Watch for external changes
watch(() => props.modelValue, (newValue) => {
  if (newValue && JSON.stringify(newValue) !== JSON.stringify(emails.value)) {
    initializeEmails()
  }
}, { immediate: true })

// Computed properties
const hasErrors = computed(() => {
  return errors.value.some(error => error !== '') || !!props.errorMessage
})

// Email validation
const validateEmail = (index: number) => {
  const email = emails.value[index]?.trim()
  
  if (!email) {
    if (props.required && index === 0) {
      errors.value[index] = 'Email is required'
    } else {
      errors.value[index] = ''
    }
    return
  }

  // Basic email validation
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!emailRegex.test(email)) {
    errors.value[index] = 'Please enter a valid email address'
    return
  }

  // Check for duplicates if not allowed
  if (!props.allowDuplicates) {
    const duplicateIndex = emails.value.findIndex((e, i) => 
      i !== index && e.trim().toLowerCase() === email.toLowerCase()
    )
    if (duplicateIndex !== -1) {
      errors.value[index] = 'This email address is already added'
      return
    }
  }

  errors.value[index] = ''
}

// Handle email input
const handleEmailInput = (index: number) => {
  if (props.validateOnInput) {
    validateEmail(index)
  }
  emitUpdate()
}

// Add new email
const addEmail = () => {
  if (emails.value.length < props.maxEmails) {
    emails.value.push('')
    errors.value.push('')
  }
}

// Remove email
const removeEmail = (index: number) => {
  if (emails.value.length > props.minEmails) {
    emails.value.splice(index, 1)
    errors.value.splice(index, 1)
    emitUpdate()
  }
}

// Emit updated values
const emitUpdate = () => {
  const validEmails = emails.value.filter(email => email.trim() !== '')
  emit('update:modelValue', validEmails)
  
  // Emit validation status
  const isValid = !hasErrors.value && validEmails.length >= props.minEmails
  emit('validate', isValid)
}

// Validate all emails
const validateAll = () => {
  emails.value.forEach((_, index) => {
    validateEmail(index)
  })
}

// Expose methods for parent components
defineExpose({
  validateAll,
  addEmail,
  removeEmail: (index: number) => removeEmail(index),
  clearAll: () => {
    emails.value = ['']
    errors.value = ['']
    emitUpdate()
  }
})

// Initialize on mount
initializeEmails()
</script>

<style scoped>
.error-slide-enter-active,
.error-slide-leave-active {
  transition: all 0.3s ease;
}

.error-slide-enter-from,
.error-slide-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}
</style>
