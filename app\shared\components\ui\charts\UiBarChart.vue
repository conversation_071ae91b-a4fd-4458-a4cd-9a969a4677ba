<template>
  <div class="ui-bar-chart">
    <BarChart
      :key="colorModeKey"
      :data="data"
      :height="height"
      :categories="categories"
      :y-axis="yAxis"
      :x-num-ticks="xNumTicks"
      :radius="radius"
      :y-grid-line="yGridLine"
      :x-formatter="xFormatter"
      :y-formatter="yFormatter"
      :legend-position="legendPosition"
      :hide-legend="hideLegend"
    />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

// Types
interface BulletLegendItemInterface {
  name: string
  color: string
}

interface Props {
  data: any[]
  height?: number
  categories: Record<string, BulletLegendItemInterface>
  yAxis?: string[]
  xNumTicks?: number
  radius?: number
  yGridLine?: boolean
  xFormatter?: (i: number) => string
  yFormatter?: (i: number) => number
  legendPosition?: string
  hideLegend?: boolean
  colorModeKey?: string
}

// Props with defaults
const props = withDefaults(defineProps<Props>(), {
  height: 300,
  xNumTicks: 6,
  radius: 4,
  yGridLine: true,
  legendPosition: 'top',
  hideLegend: false
})

// Composables
const colorMode = useColorMode()

// Computed
const colorModeKey = computed(() => colorMode.value)
</script>

<style scoped>
.ui-bar-chart {
  width: 100%;
}
</style>
