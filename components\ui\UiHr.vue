<template>
  <hr :class="[
    'border-0 transition-all duration-200',
    sizeClasses,
    colorClasses,
    spacingClasses,
    variantClasses,
    {
      'opacity-50': faded,
      'animate-pulse': animated,
    }
  ]" />
</template>

<script setup lang="ts">
import { computed } from 'vue';

interface Props {
  /**
   * Visual variant of the horizontal rule
   */
  variant?: 'solid' | 'dashed' | 'dotted' | 'double' | 'gradient';
  
  /**
   * Color theme for the hr
   */
  color?: 'gray' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' | 'info';
  
  /**
   * Size/thickness of the hr
   */
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  
  /**
   * Vertical spacing around the hr
   */
  spacing?: 'none' | 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  
  /**
   * Whether the hr should appear faded
   */
  faded?: boolean;
  
  /**
   * Whether to show a subtle animation
   */
  animated?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'solid',
  color: 'gray',
  size: 'sm',
  spacing: 'md',
  faded: false,
  animated: false,
});

// Size classes for thickness
const sizeClasses = computed(() => {
  const sizeMap = {
    xs: 'h-px',
    sm: 'h-0.5',
    md: 'h-1',
    lg: 'h-1.5',
    xl: 'h-2',
  };
  return sizeMap[props.size];
});

// Color classes
const colorClasses = computed(() => {
  const colorMap = {
    gray: 'bg-gray-200 dark:bg-gray-700',
    primary: 'bg-brandPrimary-200 dark:bg-brandPrimary-700',
    secondary: 'bg-brandSecondary-200 dark:bg-brandSecondary-700',
    success: 'bg-green-200 dark:bg-green-700',
    warning: 'bg-yellow-200 dark:bg-yellow-700',
    danger: 'bg-red-200 dark:bg-red-700',
    info: 'bg-blue-200 dark:bg-blue-700',
  };
  return colorMap[props.color];
});

// Spacing classes for margin
const spacingClasses = computed(() => {
  const spacingMap = {
    none: 'my-0',
    xs: 'my-1',
    sm: 'my-2',
    md: 'my-4',
    lg: 'my-6',
    xl: 'my-8',
  };
  return spacingMap[props.spacing];
});

// Variant-specific classes
const variantClasses = computed(() => {
  switch (props.variant) {
    case 'dashed':
      return 'border-t-2 border-dashed bg-transparent';
    case 'dotted':
      return 'border-t-2 border-dotted bg-transparent';
    case 'double':
      return 'border-t-4 border-double bg-transparent';
    case 'gradient':
      return 'bg-gradient-to-r from-transparent via-current to-transparent h-px';
    case 'solid':
    default:
      return '';
  }
});
</script>

<style scoped>
/* Custom gradient variant styling */
.bg-gradient-to-r.from-transparent.via-current.to-transparent {
  background: linear-gradient(
    to right,
    transparent 0%,
    currentColor 20%,
    currentColor 80%,
    transparent 100%
  );
}
</style>
