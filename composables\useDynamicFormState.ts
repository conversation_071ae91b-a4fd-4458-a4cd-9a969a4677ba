import { ref, reactive, computed, watch, nextTick } from 'vue'
import type { 
  DynamicFormConfig, 
  DynamicFormField, 
  DynamicFormState,
  DynamicFormSubmission 
} from '~/app/shared/types'

export interface FormStateOptions {
  initialValues?: Record<string, any>
  persistence?: {
    enabled: boolean
    key: string
    storage?: 'localStorage' | 'sessionStorage'
    debounceMs?: number
  }
  autoSave?: {
    enabled: boolean
    debounceMs?: number
    onSave?: (data: Record<string, any>) => void
  }
}

export const useDynamicFormState = (
  config: DynamicFormConfig,
  options: FormStateOptions = {}
) => {
  // Form state
  const formState = reactive<DynamicFormState>({
    isValid: false,
    isDirty: false,
    isSubmitting: false,
    isLoading: false,
    errors: {},
    touched: {},
    values: {},
    meta: {
      submitCount: 0,
      validationCount: 0
    }
  })

  // Form data ref
  const formData = ref<Record<string, any>>({})

  // Submission state
  const submissionState = reactive({
    isSubmitting: false,
    submitCount: 0,
    lastSubmission: null as Date | null,
    lastSubmissionData: null as Record<string, any> | null,
    submissionError: null as Error | null
  })

  // Auto-save state
  const autoSaveState = reactive({
    isAutoSaving: false,
    lastAutoSave: null as Date | null,
    autoSaveError: null as Error | null
  })

  // Initialize form values
  const initializeValues = () => {
    const initialValues: Record<string, any> = {}

    // Set default values from field definitions
    config.fields.forEach(field => {
      if (field.defaultValue !== undefined) {
        initialValues[field.name] = field.defaultValue
      }
    })

    // Override with provided initial values
    if (options.initialValues) {
      Object.assign(initialValues, options.initialValues)
    }

    // Load from persistence if enabled
    if (options.persistence?.enabled) {
      const persistedData = loadPersistedData()
      if (persistedData) {
        Object.assign(initialValues, persistedData)
      }
    }

    formData.value = initialValues
    formState.values = initialValues
  }

  // Persistence functions
  const getStorageKey = (): string => {
    return options.persistence?.key || `dynamic-form-${config.id || 'default'}`
  }

  const getStorage = (): Storage => {
    return options.persistence?.storage === 'sessionStorage' 
      ? sessionStorage 
      : localStorage
  }

  const savePersistedData = (data: Record<string, any>) => {
    if (!options.persistence?.enabled) return

    try {
      const storage = getStorage()
      const key = getStorageKey()
      
      // Filter out excluded fields
      const dataToSave = { ...data }
      if (config.persistence?.exclude) {
        config.persistence.exclude.forEach(field => {
          delete dataToSave[field]
        })
      }

      const serializedData = JSON.stringify({
        data: dataToSave,
        timestamp: Date.now(),
        version: '1.0'
      })

      storage.setItem(key, serializedData)
    } catch (error) {
      console.warn('Failed to persist form data:', error)
    }
  }

  const loadPersistedData = (): Record<string, any> | null => {
    if (!options.persistence?.enabled) return null

    try {
      const storage = getStorage()
      const key = getStorageKey()
      const serializedData = storage.getItem(key)

      if (!serializedData) return null

      const { data, timestamp } = JSON.parse(serializedData)
      
      // Check if data is not too old (optional)
      const maxAge = 7 * 24 * 60 * 60 * 1000 // 7 days
      if (Date.now() - timestamp > maxAge) {
        storage.removeItem(key)
        return null
      }

      return data
    } catch (error) {
      console.warn('Failed to load persisted form data:', error)
      return null
    }
  }

  const clearPersistedData = () => {
    if (!options.persistence?.enabled) return

    try {
      const storage = getStorage()
      const key = getStorageKey()
      storage.removeItem(key)
    } catch (error) {
      console.warn('Failed to clear persisted form data:', error)
    }
  }

  // Auto-save functionality
  let autoSaveTimeout: NodeJS.Timeout | null = null

  const triggerAutoSave = () => {
    if (!options.autoSave?.enabled) return

    if (autoSaveTimeout) {
      clearTimeout(autoSaveTimeout)
    }

    autoSaveTimeout = setTimeout(async () => {
      try {
        autoSaveState.isAutoSaving = true
        autoSaveState.autoSaveError = null

        if (options.autoSave?.onSave) {
          await options.autoSave.onSave(formData.value)
        }

        autoSaveState.lastAutoSave = new Date()
      } catch (error) {
        autoSaveState.autoSaveError = error as Error
        console.error('Auto-save failed:', error)
      } finally {
        autoSaveState.isAutoSaving = false
      }
    }, options.autoSave?.debounceMs || 2000)
  }

  // Form value management
  const setFieldValue = (fieldName: string, value: any) => {
    formData.value[fieldName] = value
    formState.values[fieldName] = value
    formState.isDirty = true

    // Clear field error when value changes
    if (formState.errors[fieldName]) {
      delete formState.errors[fieldName]
    }

    // Trigger auto-save
    triggerAutoSave()
  }

  const getFieldValue = (fieldName: string): any => {
    return formData.value[fieldName]
  }

  const setValues = (values: Record<string, any>) => {
    Object.assign(formData.value, values)
    Object.assign(formState.values, values)
    formState.isDirty = true
  }

  const resetForm = () => {
    // Reset to initial values
    const initialValues: Record<string, any> = {}
    
    config.fields.forEach(field => {
      if (field.defaultValue !== undefined) {
        initialValues[field.name] = field.defaultValue
      }
    })

    if (options.initialValues) {
      Object.assign(initialValues, options.initialValues)
    }

    formData.value = initialValues
    formState.values = initialValues
    formState.isDirty = false
    formState.touched = {}
    formState.errors = {}
    formState.isValid = false

    // Clear persisted data if configured
    if (config.persistence?.clearOnSubmit) {
      clearPersistedData()
    }
  }

  // Touch management
  const touchField = (fieldName: string) => {
    formState.touched[fieldName] = true
  }

  const touchAll = () => {
    config.fields.forEach(field => {
      formState.touched[field.name] = true
    })
  }

  const isFieldTouched = (fieldName: string): boolean => {
    return !!formState.touched[fieldName]
  }

  // Error management
  const setFieldError = (fieldName: string, error: string | string[]) => {
    const errors = Array.isArray(error) ? error : [error]
    formState.errors[fieldName] = errors
    formState.isValid = false
  }

  const clearFieldError = (fieldName: string) => {
    delete formState.errors[fieldName]
    formState.isValid = Object.keys(formState.errors).length === 0
  }

  const clearAllErrors = () => {
    formState.errors = {}
    formState.isValid = true
  }

  // Submission handling
  const handleSubmit = async (
    submitHandler?: (data: Record<string, any>) => Promise<any>
  ): Promise<any> => {
    submissionState.isSubmitting = true
    formState.isSubmitting = true
    submissionState.submitCount++
    formState.meta.submitCount++
    submissionState.submissionError = null

    try {
      // Touch all fields
      touchAll()

      // Prepare submission data
      let submitData = { ...formData.value }

      // Transform data if configured
      if (config.submission?.transform) {
        submitData = config.submission.transform(submitData)
      }

      // Before submit hook
      if (config.submission?.beforeSubmit) {
        const shouldContinue = await config.submission.beforeSubmit(submitData)
        if (!shouldContinue) {
          return false
        }
      }

      // Call submit handler
      let result
      if (submitHandler) {
        result = await submitHandler(submitData)
      } else if (config.submission?.url) {
        // Handle API submission
        const response = await $fetch(config.submission.url, {
          method: config.submission.method || 'POST',
          headers: config.submission.headers,
          body: submitData
        })
        result = response
      }

      // Success handling
      submissionState.lastSubmission = new Date()
      submissionState.lastSubmissionData = submitData
      formState.meta.lastSubmission = new Date()

      if (config.submission?.onSuccess) {
        config.submission.onSuccess(result, submitData)
      }

      // Clear persisted data if configured
      if (config.persistence?.clearOnSubmit) {
        clearPersistedData()
      }

      return result

    } catch (error) {
      submissionState.submissionError = error as Error

      if (config.submission?.onError) {
        config.submission.onError(error, formData.value)
      }

      throw error
    } finally {
      submissionState.isSubmitting = false
      formState.isSubmitting = false
    }
  }

  // Computed properties
  const hasErrors = computed(() => {
    return Object.keys(formState.errors).length > 0
  })

  const errorCount = computed(() => {
    return Object.values(formState.errors).reduce((count, errors) => count + errors.length, 0)
  })

  const touchedFieldCount = computed(() => {
    return Object.keys(formState.touched).filter(field => formState.touched[field]).length
  })

  const isDirty = computed(() => {
    return formState.isDirty
  })

  const isSubmitting = computed(() => {
    return formState.isSubmitting || submissionState.isSubmitting
  })

  // Watchers
  let persistenceTimeout: NodeJS.Timeout | null = null

  watch(
    formData,
    (newData) => {
      // Update form state values
      formState.values = { ...newData }

      // Debounced persistence
      if (options.persistence?.enabled) {
        if (persistenceTimeout) {
          clearTimeout(persistenceTimeout)
        }

        persistenceTimeout = setTimeout(() => {
          savePersistedData(newData)
        }, options.persistence.debounceMs || 1000)
      }
    },
    { deep: true }
  )

  // Initialize
  initializeValues()

  return {
    // State
    formState,
    formData,
    submissionState,
    autoSaveState,

    // Computed
    hasErrors,
    errorCount,
    touchedFieldCount,
    isDirty,
    isSubmitting,

    // Methods
    setFieldValue,
    getFieldValue,
    setValues,
    resetForm,
    touchField,
    touchAll,
    isFieldTouched,
    setFieldError,
    clearFieldError,
    clearAllErrors,
    handleSubmit,

    // Persistence
    savePersistedData,
    loadPersistedData,
    clearPersistedData,

    // Utilities
    initializeValues
  }
}
