# Email Template Editor

A robust and well-designed email template editor built with GrapesJS, specifically tailored for the Legal SaaS platform. This editor provides a drag-and-drop interface for creating professional email templates with legal-specific components and features.

## Features

### 🎨 Visual Editor
- **Drag & Drop Interface**: Intuitive visual editor powered by GrapesJS
- **Live Preview**: Real-time preview of your email templates
- **Responsive Design**: Templates automatically adapt to different screen sizes
- **Device Preview**: Preview templates on desktop, tablet, and mobile devices

### 📧 Email-Specific Features
- **Newsletter Preset**: Pre-configured for email template creation
- **Email-Safe Components**: Components optimized for email clients
- **Responsive Email Layouts**: Mobile-first responsive design
- **Email Client Compatibility**: Tested across major email clients

### ⚖️ Legal SaaS Integration
- **Legal Blocks**: Custom blocks designed for legal professionals
- **Template Variables**: Dynamic content insertion with legal-specific variables
- **Case Information**: Dedicated blocks for case details and updates
- **Client Communication**: Professional client communication templates
- **Invoice Integration**: Invoice summary and payment reminder blocks

### 🛠️ Advanced Features
- **Auto-save**: Automatic saving of template changes
- **Version Control**: Template versioning and history
- **Export Options**: Export as HTML, JSON, or integrate with email services
- **Custom CSS**: Advanced styling with custom CSS support
- **Asset Management**: Upload and manage images and other assets

## Components

### EmailTemplateEditor.vue
Main editor component that provides the complete email template editing experience.

**Props:**
- `template`: EmailTemplate object to edit
- `height`: Editor height (default: '100%')
- `readonly`: Whether the editor is read-only

**Events:**
- `@close`: Emitted when editor is closed
- `@save`: Emitted when template is saved
- `@export`: Emitted when template is exported

### EmailTemplateSettingsForm.vue
Settings form for configuring email template properties and metadata.

**Props:**
- `template`: EmailTemplate object to configure

**Events:**
- `@save`: Emitted when settings are saved
- `@cancel`: Emitted when form is cancelled

## Legal Blocks

The editor includes custom blocks specifically designed for legal professionals:

### Legal Header
Professional header with firm branding and contact information.

### Case Information
Structured display of case details including case number, title, status, and hearing dates.

### Legal Notice
Important legal notices with proper formatting and visual emphasis.

### Invoice Summary
Professional invoice summary with itemized billing information.

### Client Information
Client contact details and case-specific information.

### Action Required
Urgent action items with deadlines and call-to-action buttons.

### Legal Footer
Professional footer with confidentiality notices and firm information.

### Document List
List of related documents with download links.

## Usage

### Basic Usage

```vue
<template>
  <EmailTemplateEditor
    :template="currentTemplate"
    @save="handleSave"
    @close="handleClose"
  />
</template>

<script setup>
import { ref } from 'vue'

const currentTemplate = ref(null)

const handleSave = (template) => {
  console.log('Template saved:', template)
}

const handleClose = () => {
  console.log('Editor closed')
}
</script>
```

### With Settings

```vue
<template>
  <div>
    <EmailTemplateEditor
      :template="currentTemplate"
      @save="handleSave"
    />
    
    <UiModal v-model="showSettings">
      <EmailTemplateSettingsForm
        :template="currentTemplate"
        @save="handleSettingsSave"
      />
    </UiModal>
  </div>
</template>
```

## Store Integration

The editor integrates with the Pinia email template store for state management:

```typescript
import { useEmailTemplateStore } from '~/stores/emailTemplate'

const emailTemplateStore = useEmailTemplateStore()

// Load templates
await emailTemplateStore.fetchTemplates()

// Create new template
const newTemplate = await emailTemplateStore.createTemplate(templateData)

// Update existing template
await emailTemplateStore.updateTemplate(templateId, updates)
```

## Pages

### Editor Page
`/dashboard/templates/email/editor` - Main editor page for creating and editing email templates.

### Demo Page
`/dashboard/templates/email/demo` - Comprehensive demo showcasing editor features and capabilities.

## Configuration

### GrapesJS Configuration
The editor is configured with:
- Newsletter preset for email-specific features
- Basic blocks for common components
- Forms plugin for interactive elements
- Custom legal blocks for legal professionals
- Responsive device manager
- Asset manager for file uploads

### Platform Integration
- Matches platform theme and design system
- Integrates with dashboard layout
- Uses platform UI components
- Follows platform navigation patterns

## Customization

### Adding Custom Blocks
Create new blocks by extending the legal blocks configuration:

```typescript
import { registerLegalBlocks } from '~/components/email-templates/blocks/LegalBlocks'

// Add custom block
const customBlocks = [
  {
    id: 'custom-block',
    label: 'Custom Block',
    category: 'Custom',
    content: '<div>Custom content</div>'
  }
]

// Register with editor
registerLegalBlocks(editor)
```

### Styling
The editor includes comprehensive CSS for:
- Platform theme integration
- Dark mode support
- Responsive design
- Custom block styling
- Email client compatibility

## Browser Support

- Chrome 70+
- Firefox 65+
- Safari 12+
- Edge 79+

## Dependencies

- GrapesJS 0.22.8+
- GrapesJS Newsletter Preset
- GrapesJS Basic Blocks
- GrapesJS Forms Plugin
- Vue 3.5+
- Nuxt 3.17+

## Performance

The editor is optimized for performance with:
- Lazy loading of GrapesJS modules
- Efficient state management
- Optimized rendering
- Memory leak prevention
- Auto-save throttling

## Accessibility

- Keyboard navigation support
- Screen reader compatibility
- ARIA labels and roles
- Focus management
- High contrast support

## Testing

Run tests with:
```bash
npm run test
```

## Contributing

1. Follow the existing code style
2. Add tests for new features
3. Update documentation
4. Test across different browsers
5. Ensure accessibility compliance

## License

This component is part of the Legal SaaS Frontend application.
