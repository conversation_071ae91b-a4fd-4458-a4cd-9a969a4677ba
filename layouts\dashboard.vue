<!--
  Enhanced Platform Layout
  
  Advanced Platform layout with performance optimizations,
  responsive design, and feature-based navigation
-->

<template>
  <div class="Platform-layout" :class="layoutClasses">
    <!-- Skip to main content for accessibility -->
    <a
      href="#main-content"
      class="sr-only focus:not-sr-only focus:absolute focus:top-4 ltr:focus:left-4 rtl:focus:right-4 bg-blue-600 text-white px-4 py-2 rounded z-50"
    >
      Skip to main content
    </a>

    <!-- Mobile overlay -->
    <Transition name="overlay">
      <div
        v-if="isMobile && isSidebarOpen"
        class="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
        @click="closeSidebar"
      />
    </Transition>

    <!-- Sidebar -->
    <Transition name="sidebar" appear>
      <aside
        :class="sidebarClasses"
        class="Platform-sidebar"
        :aria-hidden="isMobile && !isSidebarOpen"
      >
        <!-- Platform Navigation Component -->
        <PlatformNavigation
          :collapsed="isSidebarCollapsed"
          :user-roles="currentUserRoles"
          :tenant-roles="currentTenantRoles"
          :active-tenant-id="activeTenantId"
          @toggle-collapse="toggleSidebarCollapse"
        />
      </aside>
    </Transition>

    <!-- Main content area -->
    <div class="Platform-main" :class="mainContentClasses">
      <!-- Top navigation bar -->
      <header class="Platform-header">
        <PlatformTopBar
          :title="pageHeaderTitle"
          :sidebar-collapsed="isSidebarCollapsed"
          :mobile="isMobile"
          @toggle-sidebar="toggleSidebar"
          @toggle-collapse="toggleSidebarCollapse"
        />
      </header>

      <!-- Global Navigation (replaces breadcrumbs) -->
      <GlobalNavigation
        v-if="!isDashboardHome"
        :breadcrumb-items="currentBreadcrumbs"
        :has-active-filters="showActiveFilters"
        :selected-count="selectedCount"
        :show-view-toggle="showViewToggle"
        :show-quick-filters="showQuickFilters"
        :show-actions-menu="showActionsMenu"
        :show-keyboard-shortcuts="showKeyboardShortcuts"
        :current-view="currentView"
        :actions-menu="actionsMenuItems"
        @view-changed="handleViewChanged"
        @quick-filter="handleQuickFilter"
        @bulk-action="handleBulkAction"
        @action="handleAction"
        @clear-selection="handleClearSelection"
      />

      <!-- Global Page Header -->
      <GlobalPageHeader
        v-if="!isDashboardHome && showPageHeader"
        :title="pageHeaderTitle"
        :description="pageHeaderDescription"
        :logo-image="pageHeaderLogoImage"
        :icon="pageHeaderIcon"
        :item-stats="pageHeaderItemStats"
        :stats="pageHeaderStats"
        :labels="pageHeaderLabels"
        :tags="pageHeaderTags"
        :actions="pageHeaderActions"
        :loading="pageHeaderLoading"
        :show-default-actions="showPageHeaderActions"
        :show-export="showPageHeaderExport"
        :show-create="showPageHeaderCreate"
        :create-label="pageHeaderCreateLabel"
        :create-icon="pageHeaderCreateIcon"
        :show-title="pageHeaderShowTitle"
        :show-real-time-status="showRealTimeStatus"
        :auto-refresh-enabled="autoRefreshEnabled"
        :refresh-interval="refreshInterval"
        :is-loading="realTimeLoading"
        :show-filters="showFilters"
        :filter-configs="filterConfigs"
        :advanced-filter-configs="advancedFilterConfigs"
        :initial-filters="initialFilters"
        :show-advanced-filters="showAdvancedFilters"
        :compact-filters="compactFilters"
        @export="handlePageHeaderExport"
        @create="handlePageHeaderCreate"
        @refresh="handlePageHeaderRefresh"
        @auto-refresh-changed="handleAutoRefreshChanged"
        @filter-change="handleFilterChange"
        @filters-change="handleFiltersChange"
        @clear-filters="handleClearFilters"
        @advanced-filters-toggle="handleAdvancedFiltersToggle"
      >
        <template #actions>
          <slot name="page-header-actions" />
        </template>
      </GlobalPageHeader>

      <!-- Page content -->
      <main
        id="main-content"
        class="Platform-content"
        :class="contentClasses"
        role="main"
      >
        <slot />
      </main>
      <PlatformFooter />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch, onMounted, onUnmounted, inject } from "vue";
import { useRoute } from "vue-router";
import { useAuth } from "~/composables/useAuth";
import { useEventEmitters } from "~/composables/useEventEmitters.ts";

const { emit: globalEmit } = useEventEmitters();
const auth = useAuth();
const { currentUser, hasAnyPlatformRole, hasAnyActiveTenantRole } = auth;

// Platform components
import PlatformNavigation from "~/components/platform/PlatformNavigation.vue";
import PlatformTopBar from "~/components/platform/PlatformTopBar.vue";
import GlobalNavigation from "~/components/global/GlobalNavigation.vue";
import GlobalPageHeader from "~/components/global/GlobalPageHeader.vue";
import PlatformFooter from "~/components/platform/PlatformFooter.vue";

// State management
const route = useRoute();

// Reactive state
const isSidebarCollapsed = ref(false);
const isSidebarOpen = ref(false);
const isMobile = ref(false);
const isTablet = ref(false);

// Mock user data (replace with actual auth when implemented)
const currentUserRoles = ref([]);
const currentTenantRoles = ref([]);
const activeTenantId = ref(currentUser.value?.tenantId || null);

if (hasAnyPlatformRole.value) {
  currentUserRoles.value = currentUser.value?.roles || [];
} else {
  currentUserRoles.value = [];
}

if (hasAnyActiveTenantRole.value) {
  currentTenantRoles.value = currentUser.value?.roles || [];
} else {
  currentTenantRoles.value = [];
}

// Check if mobile/tablet
const checkScreenSize = () => {
  if (typeof window !== "undefined") {
    isMobile.value = window.innerWidth < 1024;
    isTablet.value = window.innerWidth >= 768 && window.innerWidth < 1024;
  }
};
const currentView = ref("table");

// Layout configuration
const showBreadcrumbs = computed(() => route.meta?.breadcrumbs !== false);
const showFooter = computed(() => route.meta?.footer !== false);

// Global Navigation configuration
const isDashboardHome = computed(() => route.path === "/dashboard");
const showViewToggle = computed(() => !!route.meta?.showViewToggle);
const showQuickFilters = computed(() => !!route.meta?.showQuickFilters);
const showActionsMenu = computed(() => !!route.meta?.showActionsMenu);
const actionsMenuItems = computed(() => route.meta?.actionsMenu || [])
const showKeyboardShortcuts = computed(() => !!route.meta?.showKeyboardShortcuts);
const showActiveFilters = computed(() => !!route.meta?.showActiveFilters);
const selectedCount = computed(() => route.meta?.selectedCount || 0);

// Global Navigation actions
const viewToggleAction =
  typeof route.meta?.viewToggleAction === "function"
    ? route.meta?.viewToggleAction
    : function () {
        return;
      };

// Auto-generate breadcrumbs from route
const currentBreadcrumbs = computed(() => {
  return route.meta?.breadcrumbs ? route.meta.breadcrumbs : [];
});
// console.log(route.meta);

// Page Header configuration
const showPageHeader = computed(() => !!route.meta?.showPageHeader);
const pageHeaderTitle = computed(() => {
  const title = route.meta?.pageHeaderTitle || route.meta?.title;
  if (typeof title === "function") {
    return title();
  } else {
    return title || "";
  }
});
const pageHeaderDescription = computed(() => {
  const description = route.meta?.description;
  if (typeof description === "function") {
    return description();
  } else {
    return description || "";
  }
});
const pageHeaderLogoImage = computed(() => {
  const logo = route.meta?.pageHeaderLogoImage;
  if (typeof logo === "function") {
    return logo();
  } else {
    return logo || "";
  }
});
const pageHeaderIcon = computed(() => route.meta?.pageHeaderIcon || "");
const pageHeaderItemStats = computed(() => {
  const stats = route.meta?.pageHeaderItemStats;
  if (typeof stats === "function") {
    return stats();
  } else {
    return stats;
  }
});
const pageHeaderStats = computed(() => route.meta?.pageHeaderStats || []);
const pageHeaderLabels = computed(() => route.meta?.pageHeaderLabels || []);
const pageHeaderTags = computed(() => route.meta?.pageHeaderTags || []);
const pageHeaderLoading = computed(() => route.meta?.pageHeaderLoading || false);
const showPageHeaderActions = computed(() => !!route.meta?.showPageHeaderActions);
const showPageHeaderExport = computed(() => !!route.meta?.showPageHeaderExport);
const showPageHeaderCreate = computed(() => !!route.meta?.showPageHeaderCreate);
const pageHeaderCreateLabel = computed(
  () => route.meta?.pageHeaderCreateLabel || "Create"
);
const pageHeaderCreateIcon = computed(
  () => route.meta?.pageHeaderCreateIcon || "material-symbols:add"
);
const pageHeaderActions = computed(() => route.meta?.pageHeaderActions || []);
const pageHeaderShowTitle = computed(() => !!route.meta?.showPageHeaderTitle);

// Page Header Navigation configuration

// Real-time status configuration
const showRealTimeStatus = computed(() => route.meta?.showRealTimeStatus || false);
const autoRefreshEnabled = computed(() => route.meta?.autoRefreshEnabled || false);
const refreshInterval = computed(() => route.meta?.refreshInterval || 30000);
const realTimeLoading = computed(() => route.meta?.isLoading || false);

// Filter configuration
const showFilters = computed(() => route.meta?.showFilters || false);
const filterConfigs = computed(() => route.meta?.filterConfigs || []);
const advancedFilterConfigs = computed(() => route.meta?.advancedFilterConfigs || []);
const initialFilters = computed(() => route.meta?.initialFilters || {});
const showAdvancedFilters = computed(() => route.meta?.showAdvancedFilters !== false);
const compactFilters = computed(() => route.meta?.compactFilters || false);

// CSS classes
const layoutClasses = computed(() => ({
  "sidebar-collapsed": isSidebarCollapsed.value,
  "sidebar-open": isSidebarOpen.value,
  "is-mobile": isMobile.value,
  "is-tablet": isTablet.value,
}));

const sidebarClasses = computed(() => ({
  "sidebar-collapsed": isSidebarCollapsed.value,
  "sidebar-open": isSidebarOpen.value,
  "sidebar-mobile": isMobile.value,
}));

const mainContentClasses = computed(() => ({
  "sidebar-collapsed": isSidebarCollapsed.value,
  "sidebar-expanded": !isSidebarCollapsed.value,
}));

const contentClasses = computed(() => ({
  "with-breadcrumbs": showBreadcrumbs.value,
  "with-footer": showFooter.value,
}));

// Methods
const toggleSidebar = () => {
  isSidebarOpen.value = !isSidebarOpen.value;
};

const toggleSidebarCollapse = () => {
  isSidebarCollapsed.value = !isSidebarCollapsed.value;
};

const closeSidebar = () => {
  isSidebarOpen.value = false;
};

// Global Navigation event handlers
const handleViewChanged = (view: string) => {
  console.log("View changed to:", view);
  if (typeof viewToggleAction === "function") {
    viewToggleAction(view);
    currentView.value = view;
  }
  globalEmit("view-changed", {view});
  // Emit event or update store as needed
};

const handleQuickFilter = (filter: string) => {
  console.log("Quick filter applied:", filter);
  // Handle quick filter logic
};

const handleBulkAction = (action: string) => {
  console.log("Bulk action:", action);
  globalEmit("sc:bulk-action:" + action);
  // Handle bulk actions
};

const handleAction = (action: string) => {
  console.log("Action:", action);
  globalEmit("sc:action:" + action);
  // Handle general actions
};

const handleClearSelection = () => {
  console.log("Clear selection");
  // Handle clearing selection
};

// Page Header event handlers
const handlePageHeaderExport = () => {
  console.log("Page header export");
  // Handle export action
};

const handlePageHeaderCreate = () => {
  console.log("Page header create");
  // Handle create action
};

// Inject page-level handlers
const pageRefreshHandler = inject("pageHeaderRefresh", () => {
  console.log("Page header refresh - no handler provided");
});

const autoRefreshChangedHandler = inject("autoRefreshChanged", (enabled: boolean) => {
  console.log("Auto-refresh changed:", enabled, "- no handler provided");
});

const handlePageHeaderRefresh = () => {
  console.log("Page header refresh");
  if (typeof pageRefreshHandler === "function") {
    pageRefreshHandler();
  }
};

const handleAutoRefreshChanged = (enabled: boolean) => {
  console.log("Auto-refresh changed:", enabled);
  if (typeof autoRefreshChangedHandler === "function") {
    autoRefreshChangedHandler(enabled);
  }
};

// Filter event handlers
const handleFilterChange = (data: { key: string; value: any }) => {
  console.log("Filter changed:", data.key, data.value);
  globalEmit("filter-change", data);
  // Emit event for pages to handle
};

const handleFiltersChange = (filters: Record<string, any>) => {
  console.log("Filters changed:", filters);
  globalEmit("filters-change", filters);
  // Emit event for pages to handle
};

const handleClearFilters = () => {
  console.log("Clear filters");
  globalEmit("clear-filters");
  // Emit event for pages to handle
};

const handleAdvancedFiltersToggle = (show: boolean) => {
  console.log("Advanced filters toggle:", show);
  globalEmit("advanced-filters-toggle", {show});
  // Emit event for pages to handle
};

// Initialize screen size check
onMounted(() => {
  checkScreenSize();
  window.addEventListener("resize", checkScreenSize);
});

onUnmounted(() => {
  window.removeEventListener("resize", checkScreenSize);
});

 

// Keyboard shortcuts
onMounted(() => {
  const handleKeydown = (event: KeyboardEvent) => {
    // Toggle sidebar with Ctrl/Cmd + B
    if ((event.ctrlKey || event.metaKey) && event.key === "b") {
      event.preventDefault();
      toggleSidebarCollapse();
    }

    // Close sidebar with Escape (mobile)
    if (event.key === "Escape" && isMobile.value && isSidebarOpen.value) {
      closeSidebar();
    }
  };

  document.addEventListener("keydown", handleKeydown);

  onUnmounted(() => {
    document.removeEventListener("keydown", handleKeydown);
  });
});

// Watch for route changes to close mobile sidebar
watch(route, () => {
  if (isMobile.value && isSidebarOpen.value) {
    closeSidebar();
  }
});
</script>

<style scoped>
/* Layout structure */
.Platform-layout {
  position: relative;
  height: 100vh;
  background-color: #f9fafb;
  overflow: hidden;
}

.Platform-sidebar {
  position: fixed;
  top: 0;
  bottom: 0;
  z-index: 50;
  width: 16rem;
  background-color: white;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  transform: translateX(0);
  transition: transform 0.3s ease-in-out, width 0.3s ease-in-out;
}

/* LTR positioning */
[dir="ltr"] .Platform-sidebar {
  left: 0;
}

/* RTL positioning */
[dir="rtl"] .Platform-sidebar {
  right: 0;
}

@media (min-width: 1024px) {
  .Platform-sidebar {
    position: fixed;
    /* Keep fixed positioning for proper layout */
    transform: translateX(0) !important;
  }
}

.Platform-sidebar.sidebar-collapsed {
  width: 4rem;
}

/* Mobile sidebar positioning */
[dir="ltr"] .Platform-sidebar.sidebar-mobile:not(.sidebar-open) {
  transform: translateX(-100%);
}

[dir="rtl"] .Platform-sidebar.sidebar-mobile:not(.sidebar-open) {
  transform: translateX(100%);
}

.Platform-main {
  position: relative;
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  margin-left: 0;
  transition: margin-left 0.3s ease-in-out;
}

@media (min-width: 1024px) {
  /* LTR main content margins */
  [dir="ltr"] .Platform-main {
    margin-left: 16rem;
    margin-right: 0;
  }

  [dir="ltr"] .Platform-main.sidebar-expanded {
    margin-left: 16rem;
  }

  [dir="ltr"] .Platform-main.sidebar-collapsed {
    margin-left: 4rem;
  }

  /* RTL main content margins */
  [dir="rtl"] .Platform-main {
    margin-right: 16rem;
    margin-left: 0;
  }

  [dir="rtl"] .Platform-main.sidebar-expanded {
    margin-right: 16rem;
  }

  [dir="rtl"] .Platform-main.sidebar-collapsed {
    margin-right: 4rem;
  }
}

/* Mobile: no margin since sidebar is overlay */
@media (max-width: 1023px) {
  .Platform-main {
    margin-left: 0 !important;
  }
}

.Platform-header {
  background-color: white;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  border-bottom: 1px solid #e5e7eb;
  /* padding: 0.75rem 1rem; */
}

.Platform-breadcrumbs {
  background-color: #f9fafb;
  padding: 0.5rem 1rem;
  border-bottom: 1px solid #e5e7eb;
}

.Platform-content {
  flex: 1;
  overflow: auto;
  position: relative;
  padding: 1.5rem;
}

.Platform-content.with-breadcrumbs {
  padding-top: 0;
}

.Platform-footer {
  background-color: white;
  border-top: 1px solid #e5e7eb;
  padding: 0.75rem 1rem;
}

/* Sidebar content */
.sidebar-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.sidebar-header {
  border-bottom: 1px solid #e5e7eb;
}

.sidebar-nav {
  flex: 1;
  overflow-y: auto;
  padding: 1rem 0;
}

.sidebar-footer {
  border-top: 1px solid #e5e7eb;
  padding: 1rem;
}

/* Transitions */
.overlay-enter-active,
.overlay-leave-active {
  transition: opacity 0.3s ease;
}

.overlay-enter-from,
.overlay-leave-to {
  opacity: 0;
}

.sidebar-enter-active,
.sidebar-leave-active {
  transition: transform 0.3s ease;
}

/* LTR sidebar transitions */
[dir="ltr"] .sidebar-enter-from,
[dir="ltr"] .sidebar-leave-to {
  transform: translateX(-100%);
}

/* RTL sidebar transitions */
[dir="rtl"] .sidebar-enter-from,
[dir="rtl"] .sidebar-leave-to {
  transform: translateX(100%);
}

.loading-enter-active,
.loading-leave-active {
  transition: opacity 0.2s ease;
}

.loading-enter-from,
.loading-leave-to {
  opacity: 0;
}

/* Responsive adjustments - removed duplicate rule, handled above */

/* Performance optimizations */
.Platform-layout {
  contain: layout style paint;
}

.Platform-sidebar {
  will-change: transform;
}

.Platform-content {
  contain: layout style paint;
}
</style>
