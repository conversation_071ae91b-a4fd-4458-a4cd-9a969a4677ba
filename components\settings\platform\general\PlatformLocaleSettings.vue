<template>
  <div class="space-y-6">
    <UiCard title="Core Locale Defaults">
      <div class="space-y-4">
        <div>
          <UiSelect label="Default Language" id="defaultLanguage" :name="'defaultLanguage'" v-model="form.defaultLanguage"
            @update:modelValue="updateSetting('defaultLanguage', $event)" :options="languageOptions" class="mt-1"></UiSelect>
          <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">Sets the default language for the platform for new users.</p>
        </div>
        <div>
          <UiSelect label="Default Timezone" id="defaultTimezone" :name="'defaultTimezone'" v-model="form.defaultTimezone" :options="timezoneOptions"
            @update:modelValue="updateSetting('defaultTimezone', $event)" class="mt-1"></UiSelect>
          <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">Configures the default timezone for system-generated timestamps and new users.</p>
        </div>
      </div>
    </UiCard>

    <UiCard title="Numerical & Currency Formatting">
      <div class="space-y-4">
        <div>
          <UiSelect label="Thousands Separator for Numbers" id="thousandsSeparator" :name="'thousandsSeparator'" v-model="form.thousandsSeparator" :options="thousandsSeparatorOptions" @update:modelValue="updateSetting('thousandsSeparator', $event)" class="mt-1"></UiSelect>
        </div>
        <div>
          <UiSelect label="Decimal Separator for Numbers" id="decimalSeparator" :name="'decimalSeparator'" v-model="form.decimalSeparator" :options="decimalSeparatorOptions" @update:modelValue="updateSetting('decimalSeparator', $event)" class="mt-1"></UiSelect>
        </div>
        <div>
          <UiSelect label="Default Currency Symbol Display Style" id="currencySymbolStyle" :name="'currencySymbolStyle'" v-model="form.currencySymbolStyle" :options="currencySymbolStyleOptions" @update:modelValue="updateSetting('currencySymbolStyle', $event)" class="mt-1"></UiSelect>
        </div>
        <div>
          <UiSelect label="Currency Symbol Position" id="currencySymbolPosition" :name="'currencySymbolPosition'" v-model="form.currencySymbolPosition" :options="currencySymbolPositionOptions" @update:modelValue="updateSetting('currencySymbolPosition', $event)" class="mt-1"></UiSelect>
        </div>
        <div>
          <label for="currencyDecimalPlaces" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Number of Decimal Places for Currency</label>
          <UiInput id="currencyDecimalPlaces" :name="'currencyDecimalPlaces'" v-model.number="form.currencyDecimalPlaces" type="number" placeholder="e.g., 2" @update:modelValue="updateSetting('currencyDecimalPlaces', $event)" class="mt-1" />
        </div>
      </div>
    </UiCard>

    <UiCard title="Language Fallback & Display">
      <div class="space-y-4">
        <div>
          <UiSelect label="Content Fallback Language" id="contentFallbackLanguage" :name="'contentFallbackLanguage'" v-model="form.contentFallbackLanguage" :options="languageOptions" @update:modelValue="updateSetting('contentFallbackLanguage', $event)" class="mt-1"></UiSelect>
          <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">If content isn't available in a user's selected language, display it in this language instead.</p>
        </div>
        <div>
          <UiSelect label="Display Language Names As" id="displayLanguageNamesAs" :name="'displayLanguageNamesAs'" v-model="form.displayLanguageNamesAs" :options="displayLanguageNamesAsOptions" @update:modelValue="updateSetting('displayLanguageNamesAs', $event)" class="mt-1"></UiSelect>
        </div>
      </div>
    </UiCard>

    <UiCard title="Measurement System (Optional)">
      <div class="space-y-4">
        <div>
          <UiSelect label="Default Unit System" id="defaultUnitSystem" :name="'defaultUnitSystem'" v-model="form.defaultUnitSystem" :options="unitSystemOptions" @update:modelValue="updateSetting('defaultUnitSystem', $event)" class="mt-1"></UiSelect>
          <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">Relevant if your platform deals with physical measurements.</p>
        </div>
      </div>
    </UiCard>

  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useApi } from '~/composables/useApi'; // Custom composable for API calls
import { useToast } from '~/composables/useToast'; // Custom composable for toast notifications
import UiCard from '~/components/ui/UiCard.vue';
import UiInput from '~/components/ui/UiInput.vue';
import UiSelect from '~/components/ui/UiSelect.vue';

const { get, patch } = useApi();
const { showToast } = useToast();

const form = ref<{
  defaultLanguage: string;
  defaultTimezone: string;
  // Numerical & Currency Formatting
  thousandsSeparator: string;
  decimalSeparator: string;
  currencySymbolStyle: string;
  currencySymbolPosition: string;
  currencyDecimalPlaces: number;
  // Language Fallback & Display
  contentFallbackLanguage: string;
  displayLanguageNamesAs: string;
  // Measurement System
  defaultUnitSystem: string;
}>({
  defaultLanguage: 'en',
  defaultTimezone: 'UTC',
  // Defaults for Numerical & Currency
  thousandsSeparator: ',',
  decimalSeparator: '.',
  currencySymbolStyle: 'symbol',
  currencySymbolPosition: 'before',
  currencyDecimalPlaces: 2,
  // Defaults for Language Fallback & Display
  contentFallbackLanguage: 'en',
  displayLanguageNamesAs: 'native',
  // Defaults for Measurement System
  defaultUnitSystem: 'metric',
});

const languageOptions = ref([
  { label: 'English', value: 'en' },
  { label: 'עברית (Hebrew)', value: 'he' },
  { label: 'العربية (Arabic)', value: 'ar' },
  // TODO: Consider syncing with global language list from PlatformLocalization.vue
]);

const timezoneOptions = ref([
  // ... (existing timezoneOptions - kept for brevity in diff)
  { "label": "UTC (Coordinated Universal Time)", "value": "UTC" },
  { "label": "America/New_York (Eastern Time - EDT/EST)", "value": "America/New_York" },
  { "label": "America/Los_Angeles (Pacific Time - PDT/PST)", "value": "America/Los_Angeles" },
  { "label": "Europe/London (Greenwich Mean Time - BST/GMT)", "value": "Europe/London" },
  { "label": "Europe/Paris (Central European Time - CEST/CET)", "value": "Europe/Paris" },
  { "label": "Australia/Sydney (Australian Eastern Standard Time - AEDT/AEST)", "value": "Australia/Sydney" },
  { "label": "Africa/Johannesburg (South African Standard Time)", "value": "Africa/Johannesburg" },
  { "label": "Asia/Jerusalem (Israel Standard Time - IDT/IST)", "value": "Asia/Jerusalem" },
  { "label": "Asia/Amman (Jordan Standard Time)", "value": "Asia/Amman" },
  { "label": "Asia/Beirut (Lebanon Standard Time)", "value": "Asia/Beirut" },
  { "label": "Asia/Damascus (Syria Standard Time)", "value": "Asia/Damascus" },
  { "label": "Asia/Gaza (Palestine Standard Time)", "value": "Asia/Gaza" },
  { "label": "Asia/Baghdad (Arabia Standard Time)", "value": "Asia/Baghdad" },
  { "label": "Asia/Riyadh (Arabia Standard Time)", "value": "Asia/Riyadh" },
  { "label": "Asia/Dubai (Gulf Standard Time)", "value": "Asia/Dubai" },
  { "label": "Asia/Tehran (Iran Standard Time - special offset)", "value": "Asia/Tehran" },
  { "label": "Asia/Kuwait (Arabia Standard Time)", "value": "Asia/Kuwait" },
  { "label": "Asia/Qatar (Arabia Standard Time)", "value": "Asia/Qatar" },
  { "label": "Asia/Muscat (Gulf Standard Time)", "value": "Asia/Muscat" },
  { "label": "Asia/Bahrain (Arabia Standard Time)", "value": "Asia/Bahrain" },
  { "label": "Asia/Aden (Arabia Standard Time)", "value": "Asia/Aden" },
  { "label": "Asia/Nicosia (Eastern European Time - Cyprus)", "value": "Asia/Nicosia" }
]);

const thousandsSeparatorOptions = ref([
  { label: 'Comma (,)', value: ',' },
  { label: 'Period (.)', value: '.' },
  { label: 'Space ( )', value: ' ' },
  { label: 'None', value: '' },
]);

const decimalSeparatorOptions = ref([
  { label: 'Period (.)', value: '.' },
  { label: 'Comma (,)', value: ',' },
]);

const currencySymbolStyleOptions = ref([
  { label: 'Symbol (e.g., $)', value: 'symbol' },
  { label: 'ISO Code (e.g., USD)', value: 'iso_code' },
]);

const currencySymbolPositionOptions = ref([
  { label: 'Before Amount (e.g., $123.45)', value: 'before' },
  { label: 'After Amount (e.g., 123.45 $)', value: 'after' },
]);

const displayLanguageNamesAsOptions = ref([
  { label: 'Native Name (e.g., Español)', value: 'native' },
  { label: 'Current UI Language Name (e.g., Spanish)', value: 'ui_language' },
]);

const unitSystemOptions = ref([
  { label: 'Metric (kilograms, meters, liters)', value: 'metric' },
  { label: 'Imperial (pounds, feet, gallons)', value: 'imperial' },
]);


onMounted(async () => {
  await fetchSettings();
});

const fetchSettings = async () => {
  const settingsToFetch = [
    'defaultLanguage', 'defaultTimezone',
    'thousandsSeparator', 'decimalSeparator', 'currencySymbolStyle', 'currencySymbolPosition', 'currencyDecimalPlaces',
    'contentFallbackLanguage', 'displayLanguageNamesAs',
    'defaultUnitSystem'
  ];
   for (const key of settingsToFetch) {
    try {
      const settingValue = await get(`/settings/${key}`);
      if (settingValue !== undefined && settingValue !== null) {
        (form.value as any)[key] = settingValue;
      }
    } catch (error) {
      console.error(`Failed to fetch platform locale setting for ${key}:`, error);
    }
  }
};

const updateSetting = async (key: string, value: any) => {
  try {
    const payloadValue = typeof value === 'boolean' ? value : (value === undefined ? null : value) ;
    await patch(`/settings/${key}`, { value: payloadValue });
    showToast({
      title: 'Success',
      message: `Successfully updated ${key}.`,
      type: 'success'
    });
  } catch (error: any) {
    console.error(`Failed to update setting ${key}:`, error);
     showToast({
      title: 'Error',
      message: `Failed to update ${key}. Please try again.`,
      type: 'error'
    });
  }
};
</script>