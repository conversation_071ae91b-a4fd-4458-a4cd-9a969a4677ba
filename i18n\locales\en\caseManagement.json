{"caseOverview": "Case Overview", "caseOverviewDescription": "Overview and quick access to case management features", "manageCases": "Manage Cases", "manageCasesDescription": "View, edit, and manage all legal cases", "viewAllCases": "View All Cases", "createCase": "Create Case", "createCaseDescription": "Add a new legal case to the system", "createNewCase": "Create New Case", "caseAnalytics": "Case Analytics", "caseAnalyticsDescription": "View case progress and performance reports", "viewAnalytics": "View Analytics", "caseTemplates": "Case Templates", "caseTemplatesDescription": "Manage case templates and workflows", "manageTemplates": "Manage Templates", "new": "New", "analytics": "Analytics", "templates": "Templates", "caseNumber": "Case Number", "caseTitle": "Case Title", "caseType": "Case Type", "caseStatus": "Case Status", "casePriority": "Priority", "client": "Client", "assignedLawyer": "Assigned Lawyer", "courtName": "Court Name", "judgeAssigned": "Judge Assigned", "filingDate": "Filing Date", "hearingDate": "Hearing Date", "closingDate": "Closing Date", "caseValue": "Case Value", "billableHours": "Billable Hours", "expenses": "Expenses", "notes": "Notes", "timeline": "Timeline", "tasks": "Tasks", "deadlines": "Deadlines", "contacts": "Contacts", "evidence": "Evidence", "correspondence": "Correspondence", "billing": "Billing", "reports": "Reports", "archive": "Archive", "unableToLoadCaseData": "Unable to load case data", "retryLoadingData": "Retry Loading Data", "searchPlaceholder": "Search cases by title, client, or case type...", "casesCount": "Cases ({count})", "newCase": "New Case", "case": "Case", "type": "Type", "priority": "Priority", "actions": "Actions", "created": "Created", "moreActions": "More actions"}