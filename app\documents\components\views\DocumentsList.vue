<!--
  Documents List View Component
  
  Displays a list of legal documents with filtering, sorting,
  and file management capabilities
-->

<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">Documents</h1>
        <p class="mt-1 text-sm text-gray-500">
          Manage and organize your legal documents
        </p>
      </div>
      
      <div class="mt-4 sm:mt-0">
        <NuxtLink
          to="/dashboard/documents/upload"
          class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200"
        >
          <Icon name="heroicons:arrow-up-tray" class="h-4 w-4 mr-2" />
          Upload Document
        </NuxtLink>
      </div>
    </div>

    <!-- Filters and Search -->
    <div class="bg-white shadow rounded-lg p-6">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <!-- Search -->
        <div class="md:col-span-2">
          <label for="search" class="sr-only">Search documents</label>
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Icon name="heroicons:magnifying-glass" class="h-5 w-5 text-gray-400" />
            </div>
            <input
              id="search"
              v-model="searchQuery"
              type="text"
              placeholder="Search documents..."
              class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
            />
          </div>
        </div>

        <!-- Type Filter -->
        <div>
          <label for="type" class="sr-only">Filter by type</label>
          <select
            id="type"
            v-model="selectedType"
            class="block w-full px-3 py-2 border border-gray-300 rounded-md leading-5 bg-white focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
          >
            <option value="">All Types</option>
            <option value="contract">Contracts</option>
            <option value="legal-brief">Legal Briefs</option>
            <option value="correspondence">Correspondence</option>
            <option value="evidence">Evidence</option>
            <option value="template">Templates</option>
            <option value="other">Other</option>
          </select>
        </div>

        <!-- Date Filter -->
        <div>
          <label for="dateRange" class="sr-only">Filter by date</label>
          <select
            id="dateRange"
            v-model="selectedDateRange"
            class="block w-full px-3 py-2 border border-gray-300 rounded-md leading-5 bg-white focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
          >
            <option value="">All Dates</option>
            <option value="today">Today</option>
            <option value="week">This Week</option>
            <option value="month">This Month</option>
            <option value="year">This Year</option>
          </select>
        </div>
      </div>
    </div>

    <!-- Documents Grid -->
    <div class="bg-white shadow rounded-lg overflow-hidden">
      <!-- Loading State -->
      <div v-if="isLoading" class="p-8 text-center">
        <UiSpinner size="lg" />
        <p class="mt-2 text-sm text-gray-500">Loading documents...</p>
      </div>

      <!-- Empty State -->
      <div v-else-if="filteredDocuments.length === 0" class="p-8 text-center">
        <Icon name="heroicons:document-text" class="mx-auto h-12 w-12 text-gray-400" />
        <h3 class="mt-2 text-sm font-medium text-gray-900">No documents found</h3>
        <p class="mt-1 text-sm text-gray-500">
          {{ searchQuery ? 'Try adjusting your search criteria.' : 'Get started by uploading your first document.' }}
        </p>
        <div class="mt-6" v-if="!searchQuery">
          <NuxtLink
            to="/dashboard/documents/upload"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-indigo-600 bg-indigo-100 hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200"
          >
            <Icon name="heroicons:arrow-up-tray" class="h-4 w-4 mr-2" />
            Upload your first document
          </NuxtLink>
        </div>
      </div>

      <!-- Documents Grid -->
      <div v-else class="p-6">
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          <div
            v-for="document in paginatedDocuments"
            :key="document.id"
            class="group relative bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow duration-200 cursor-pointer"
            @click="viewDocument(document)"
          >
            <!-- Document Icon -->
            <div class="flex items-center justify-center w-12 h-12 mx-auto mb-3 rounded-lg" :class="getDocumentIconClass(document.type)">
              <Icon :name="getDocumentIcon(document.type)" class="h-6 w-6 text-white" />
            </div>

            <!-- Document Info -->
            <div class="text-center">
              <h3 class="text-sm font-medium text-gray-900 truncate" :title="document.name">
                {{ document.name }}
              </h3>
              <p class="text-xs text-gray-500 mt-1">
                {{ formatFileSize(document.size) }}
              </p>
              <p class="text-xs text-gray-500">
                {{ formatDate(document.uploadedAt) }}
              </p>
            </div>

            <!-- Document Actions -->
            <div class="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
              <div class="flex space-x-1">
                <button
                  @click.stop="downloadDocument(document)"
                  class="p-1 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100"
                  title="Download"
                >
                  <Icon name="heroicons:arrow-down-tray" class="h-4 w-4" />
                </button>
                <button
                  @click.stop="shareDocument(document)"
                  class="p-1 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100"
                  title="Share"
                >
                  <Icon name="heroicons:share" class="h-4 w-4" />
                </button>
                <button
                  @click.stop="deleteDocument(document)"
                  class="p-1 rounded-md text-gray-400 hover:text-red-600 hover:bg-red-50"
                  title="Delete"
                >
                  <Icon name="heroicons:trash" class="h-4 w-4" />
                </button>
              </div>
            </div>

            <!-- Document Type Badge -->
            <div class="absolute top-2 left-2">
              <span :class="getTypeBadgeClass(document.type)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                {{ formatDocumentType(document.type) }}
              </span>
            </div>
          </div>
        </div>

        <!-- Pagination -->
        <div v-if="totalPages > 1" class="mt-6 flex items-center justify-between border-t border-gray-200 pt-6">
          <div class="flex-1 flex justify-between sm:hidden">
            <button
              :disabled="currentPage === 1"
              @click="currentPage--"
              class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Previous
            </button>
            <button
              :disabled="currentPage === totalPages"
              @click="currentPage++"
              class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Next
            </button>
          </div>
          <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p class="text-sm text-gray-700">
                Showing {{ ((currentPage - 1) * itemsPerPage) + 1 }} to {{ Math.min(currentPage * itemsPerPage, filteredDocuments.length) }} of {{ filteredDocuments.length }} results
              </p>
            </div>
            <div>
              <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                <button
                  :disabled="currentPage === 1"
                  @click="currentPage--"
                  class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <Icon name="heroicons:chevron-left" class="h-5 w-5" />
                </button>
                <button
                  v-for="page in visiblePages"
                  :key="page"
                  @click="currentPage = page"
                  :class="[
                    page === currentPage
                      ? 'z-10 bg-indigo-50 border-indigo-500 text-indigo-600'
                      : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50',
                    'relative inline-flex items-center px-4 py-2 border text-sm font-medium'
                  ]"
                >
                  {{ page }}
                </button>
                <button
                  :disabled="currentPage === totalPages"
                  @click="currentPage++"
                  class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <Icon name="heroicons:chevron-right" class="h-5 w-5" />
                </button>
              </nav>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'

// Composables
const router = useRouter()

// Mock data
const mockDocuments = [
  {
    id: '1',
    name: 'Contract Agreement.pdf',
    type: 'contract',
    size: 2048576, // 2MB
    uploadedAt: '2024-01-15T10:30:00Z',
    url: '/documents/contract-agreement.pdf'
  },
  {
    id: '2',
    name: 'Legal Brief.docx',
    type: 'legal-brief',
    size: 1024000, // 1MB
    uploadedAt: '2024-01-14T14:20:00Z',
    url: '/documents/legal-brief.docx'
  }
]

// State
const isLoading = ref(false)
const documents = ref(mockDocuments)
const searchQuery = ref('')
const selectedType = ref('')
const selectedDateRange = ref('')
const currentPage = ref(1)
const itemsPerPage = ref(12)

// Computed
const filteredDocuments = computed(() => {
  return documents.value.filter(doc => {
    const matchesSearch = !searchQuery.value || 
      doc.name.toLowerCase().includes(searchQuery.value.toLowerCase())
    
    const matchesType = !selectedType.value || doc.type === selectedType.value
    
    // Date filtering logic would go here
    const matchesDate = true // Simplified for now
    
    return matchesSearch && matchesType && matchesDate
  })
})

const totalPages = computed(() => Math.ceil(filteredDocuments.value.length / itemsPerPage.value))

const paginatedDocuments = computed(() => {
  const start = (currentPage.value - 1) * itemsPerPage.value
  const end = start + itemsPerPage.value
  return filteredDocuments.value.slice(start, end)
})

const visiblePages = computed(() => {
  const pages = []
  const start = Math.max(1, currentPage.value - 2)
  const end = Math.min(totalPages.value, start + 4)
  
  for (let i = start; i <= end; i++) {
    pages.push(i)
  }
  
  return pages
})

// Methods
const getDocumentIcon = (type: string) => {
  const icons = {
    contract: 'heroicons:document-text',
    'legal-brief': 'heroicons:document',
    correspondence: 'heroicons:envelope',
    evidence: 'heroicons:camera',
    template: 'heroicons:clipboard-document-list',
    other: 'heroicons:document'
  }
  return icons[type as keyof typeof icons] || 'heroicons:document'
}

const getDocumentIconClass = (type: string) => {
  const classes = {
    contract: 'bg-blue-500',
    'legal-brief': 'bg-green-500',
    correspondence: 'bg-yellow-500',
    evidence: 'bg-red-500',
    template: 'bg-purple-500',
    other: 'bg-gray-500'
  }
  return classes[type as keyof typeof classes] || 'bg-gray-500'
}

const getTypeBadgeClass = (type: string) => {
  const classes = {
    contract: 'bg-blue-100 text-blue-800',
    'legal-brief': 'bg-green-100 text-green-800',
    correspondence: 'bg-yellow-100 text-yellow-800',
    evidence: 'bg-red-100 text-red-800',
    template: 'bg-purple-100 text-purple-800',
    other: 'bg-gray-100 text-gray-800'
  }
  return classes[type as keyof typeof classes] || 'bg-gray-100 text-gray-800'
}

const formatDocumentType = (type: string) => {
  const types = {
    contract: 'Contract',
    'legal-brief': 'Brief',
    correspondence: 'Letter',
    evidence: 'Evidence',
    template: 'Template',
    other: 'Other'
  }
  return types[type as keyof typeof types] || 'Document'
}

const formatFileSize = (bytes: number) => {
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  if (bytes === 0) return '0 Bytes'
  const i = Math.floor(Math.log(bytes) / Math.log(1024))
  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

const viewDocument = (document: any) => {
  router.push(`/dashboard/documents/viewer?id=${document.id}`)
}

const downloadDocument = (document: any) => {
  console.log('Downloading document:', document.name)
  // Implement download logic
}

const shareDocument = (document: any) => {
  console.log('Sharing document:', document.name)
  // Implement share logic
}

const deleteDocument = (document: any) => {
  if (confirm(`Are you sure you want to delete "${document.name}"?`)) {
    console.log('Deleting document:', document.name)
    // Implement delete logic
  }
}

// Lifecycle
onMounted(() => {
  // Load documents
})
</script>
