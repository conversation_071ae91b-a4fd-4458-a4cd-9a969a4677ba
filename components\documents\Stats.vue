<template>
  <div class="space-y-4">
    <div class="space-y-3">
      <div class="flex items-center justify-between text-sm">
        <span class="text-gray-600 dark:text-gray-400">Word Count</span>
        <span class="font-medium text-gray-900 dark:text-white">{{ wordCount }}</span>
      </div>
      <div class="flex items-center justify-between text-sm">
        <span class="text-gray-600 dark:text-gray-400">Variables</span>
        <span class="font-medium text-gray-900 dark:text-white">{{
          formData.variables.length
        }}</span>
      </div>
      <div class="flex items-center justify-between text-sm">
        <span class="text-gray-600 dark:text-gray-400">Completion</span>
        <span class="font-medium text-gray-900 dark:text-white"
          >{{ completionPercentage }}%</span
        >
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useDebounceFn } from "@vueuse/core";

const props = defineProps({
  formData: {
    type: Object,
    required: true,
  },
});

const wordCount = computed(() => {
  return props.formData.content.trim().split(/\s+/).length;
});

const completionPercentage = computed(() => {
  if (!props.formData.content.trim()) return 0;
  const totalWords = wordCount.value;
  const completedWords = props.formData.content.trim().split(/\s+/).length;
  return Math.round((completedWords / totalWords) * 100);
});
</script>
