<template>
  <div class="fixed bottom-0 left-0 right-0 z-50 p-4 bg-white border-t border-gray-200 shadow-lg">
    <div class="container mx-auto max-w-6xl">
      <div class="flex flex-col md:flex-row items-start md:items-center justify-between gap-4">
        <!-- <PERSON><PERSON> message -->
        <div class="flex-1">
          <div class="flex items-start gap-3">
            <Icon name="material-symbols:cookie" class="w-6 h-6 text-brandPrimary-600 flex-shrink-0 mt-0.5" />
            <div>
              <h3 class="font-semibold text-gray-900 mb-1"><PERSON><PERSON> Consent</h3>
              <p class="text-sm text-gray-600 leading-relaxed">
                We use cookies to enhance your experience, analyze site traffic, and personalize content. 
                By clicking "Accept All", you consent to our use of cookies.
                <NuxtLink to="/privacy-policy" class="text-brandPrimary-600 hover:text-brandPrimary-700 underline ml-1">
                  Learn more
                </NuxtLink>
              </p>
            </div>
          </div>
        </div>

        <!-- Action buttons -->
        <div class="flex flex-col sm:flex-row gap-2 w-full md:w-auto">
          <UiButton
            variant="outline"
            size="sm"
            @click="$emit('decline')"
            class="w-full sm:w-auto"
          >
            Decline
          </UiButton>
          <UiButton
            variant="primary"
            size="sm"
            @click="$emit('accept')"
            class="w-full sm:w-auto"
          >
            Accept All
          </UiButton>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// Define emits
defineEmits<{
  accept: []
  decline: []
}>()
</script>

<style scoped>
/* Ensure the cookie banner appears above other content */
.fixed {
  backdrop-filter: blur(8px);
  background-color: rgba(255, 255, 255, 0.95);
}

@supports not (backdrop-filter: blur(8px)) {
  .fixed {
    background-color: white;
  }
}
</style>
