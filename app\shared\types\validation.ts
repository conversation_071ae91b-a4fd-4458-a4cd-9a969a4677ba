/**
 * Comprehensive Validation System
 * 
 * Type-safe validation schemas using Zod for runtime validation
 * and TypeScript type inference
 */

import { z } from 'zod'

// ============================================================================
// BASE VALIDATION SCHEMAS
// ============================================================================

/**
 * UUID validation schema
 */
export const UUIDSchema = z.string().uuid().brand<'UUID'>()

/**
 * User ID validation schema
 */
export const UserIdSchema = z.string().uuid().brand<'UserId'>()

/**
 * Tenant ID validation schema
 */
export const TenantIdSchema = z.string().uuid().brand<'TenantId'>()

/**
 * ISO Date string validation schema
 */
export const ISODateStringSchema = z.string().datetime().brand<'ISODateString'>()

/**
 * Email address validation schema
 */
export const EmailAddressSchema = z.string().email().brand<'EmailAddress'>()

/**
 * Phone number validation schema
 */
export const PhoneNumberSchema = z.object({
  countryCode: z.string().min(1).max(4),
  number: z.string().min(7).max(15),
  formatted: z.string(),
  verified: z.boolean(),
  verifiedAt: ISODateStringSchema.optional()
})

/**
 * Password validation schema with strength requirements
 */
export const PasswordSchema = z.string()
  .min(8, 'Password must be at least 8 characters')
  .max(128, 'Password must not exceed 128 characters')
  .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
  .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
  .regex(/[0-9]/, 'Password must contain at least one number')
  .regex(/[^a-zA-Z0-9]/, 'Password must contain at least one special character')

/**
 * Strong password schema for high-security contexts
 */
export const StrongPasswordSchema = PasswordSchema
  .min(12, 'Strong password must be at least 12 characters')
  .regex(/(?=.*[a-z].*[a-z])/, 'Password must contain at least two lowercase letters')
  .regex(/(?=.*[A-Z].*[A-Z])/, 'Password must contain at least two uppercase letters')
  .regex(/(?=.*[0-9].*[0-9])/, 'Password must contain at least two numbers')
  .regex(/(?=.*[^a-zA-Z0-9].*[^a-zA-Z0-9])/, 'Password must contain at least two special characters')

// ============================================================================
// COMMON FIELD VALIDATION SCHEMAS
// ============================================================================

/**
 * Name validation schemas
 */
export const FirstNameSchema = z.string().min(1).max(50).trim()
export const LastNameSchema = z.string().min(1).max(50).trim()
export const FullNameSchema = z.string().min(2).max(100).trim()

/**
 * Address validation schema
 */
export const AddressSchema = z.object({
  street1: z.string().min(1).max(100),
  street2: z.string().max(100).optional(),
  city: z.string().min(1).max(50),
  state: z.string().min(2).max(50),
  postalCode: z.string().min(3).max(20),
  country: z.string().length(2), // ISO 3166-1 alpha-2
  formatted: z.string(),
  coordinates: z.object({
    latitude: z.number().min(-90).max(90),
    longitude: z.number().min(-180).max(180)
  }).optional()
})

/**
 * URL validation schema
 */
export const URLSchema = z.string().url()

/**
 * Slug validation schema (URL-friendly strings)
 */
export const SlugSchema = z.string()
  .min(1)
  .max(100)
  .regex(/^[a-z0-9]+(?:-[a-z0-9]+)*$/, 'Slug must contain only lowercase letters, numbers, and hyphens')

/**
 * Color validation schema (hex colors)
 */
export const ColorSchema = z.string().regex(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/, 'Invalid hex color')

/**
 * File size validation (in bytes)
 */
export const FileSizeSchema = z.number().min(0).max(100 * 1024 * 1024) // 100MB max

/**
 * MIME type validation
 */
export const MimeTypeSchema = z.string().regex(/^[a-z]+\/[a-z0-9\-\+\.]+$/i, 'Invalid MIME type')

// ============================================================================
// PAGINATION AND FILTERING SCHEMAS
// ============================================================================

/**
 * Pagination parameters schema
 */
export const PaginationParamsSchema = z.object({
  page: z.number().int().min(1).default(1),
  limit: z.number().int().min(1).max(100).default(20),
  offset: z.number().int().min(0).optional()
})

/**
 * Sort parameters schema
 */
export const SortParamsSchema = z.object({
  field: z.string().min(1),
  direction: z.enum(['asc', 'desc']).default('asc')
})

/**
 * Filter condition schema
 */
export const FilterConditionSchema = z.object({
  field: z.string().min(1),
  operator: z.enum([
    'eq', 'ne', 'gt', 'gte', 'lt', 'lte',
    'in', 'nin', 'contains', 'starts_with', 'ends_with',
    'is_null', 'is_not_null'
  ]),
  value: z.any()
})

/**
 * Search parameters schema
 */
export const SearchParamsSchema = z.object({
  query: z.string().min(1).max(500),
  filters: z.array(FilterConditionSchema).optional(),
  sort: z.array(SortParamsSchema).optional(),
  pagination: PaginationParamsSchema.optional(),
  facets: z.array(z.string()).optional(),
  highlight: z.boolean().default(false)
})

// ============================================================================
// FILE UPLOAD VALIDATION SCHEMAS
// ============================================================================

/**
 * File upload schema
 */
export const FileUploadSchema = z.object({
  name: z.string().min(1).max(255),
  size: FileSizeSchema,
  type: MimeTypeSchema,
  lastModified: z.number().optional()
})

/**
 * Image upload schema with additional constraints
 */
export const ImageUploadSchema = FileUploadSchema.extend({
  type: z.string().regex(/^image\/(jpeg|jpg|png|gif|webp|svg\+xml)$/i, 'Invalid image type'),
  size: z.number().max(10 * 1024 * 1024) // 10MB max for images
})

/**
 * Document upload schema
 */
export const DocumentUploadSchema = FileUploadSchema.extend({
  type: z.string().regex(
    /^(application\/(pdf|msword|vnd\.openxmlformats-officedocument\.wordprocessingml\.document)|text\/(plain|rtf))$/i,
    'Invalid document type'
  ),
  size: z.number().max(50 * 1024 * 1024) // 50MB max for documents
})

// ============================================================================
// FORM VALIDATION SCHEMAS
// ============================================================================

/**
 * Contact form schema
 */
export const ContactFormSchema = z.object({
  name: FullNameSchema,
  email: EmailAddressSchema,
  phone: PhoneNumberSchema.optional(),
  subject: z.string().min(1).max(200),
  message: z.string().min(10).max(2000),
  consent: z.boolean().refine(val => val === true, 'Consent is required')
})

/**
 * Newsletter subscription schema
 */
export const NewsletterSubscriptionSchema = z.object({
  email: EmailAddressSchema,
  preferences: z.object({
    frequency: z.enum(['daily', 'weekly', 'monthly']).default('weekly'),
    topics: z.array(z.string()).min(1, 'Select at least one topic')
  }),
  consent: z.boolean().refine(val => val === true, 'Consent is required')
})

// ============================================================================
// BUSINESS LOGIC VALIDATION SCHEMAS
// ============================================================================

/**
 * Case number validation schema
 */
export const CaseNumberSchema = z.string()
  .min(1)
  .max(50)
  .regex(/^[A-Z0-9\-]+$/, 'Case number must contain only uppercase letters, numbers, and hyphens')

/**
 * Legal document reference schema
 */
export const LegalDocumentReferenceSchema = z.object({
  type: z.enum(['statute', 'regulation', 'case_law', 'contract', 'other']),
  citation: z.string().min(1).max(500),
  jurisdiction: z.string().min(1).max(100),
  date: ISODateStringSchema.optional(),
  url: URLSchema.optional()
})

/**
 * Billing rate validation schema
 */
export const BillingRateSchema = z.object({
  amount: z.number().min(0).max(10000),
  currency: z.string().length(3), // ISO 4217 currency code
  period: z.enum(['hour', 'day', 'week', 'month', 'year', 'fixed']),
  effective_date: ISODateStringSchema
})

/**
 * Time entry validation schema
 */
export const TimeEntrySchema = z.object({
  date: ISODateStringSchema,
  hours: z.number().min(0.1).max(24),
  description: z.string().min(1).max(1000),
  billable: z.boolean().default(true),
  rate: BillingRateSchema.optional(),
  tags: z.array(z.string()).optional()
})

// ============================================================================
// SECURITY VALIDATION SCHEMAS
// ============================================================================

/**
 * API key validation schema
 */
export const ApiKeySchema = z.string()
  .min(32)
  .max(128)
  .regex(/^[A-Za-z0-9\-_]+$/, 'Invalid API key format')

/**
 * JWT token validation schema
 */
export const JWTTokenSchema = z.string()
  .regex(/^[A-Za-z0-9\-_]+\.[A-Za-z0-9\-_]+\.[A-Za-z0-9\-_]+$/, 'Invalid JWT format')

/**
 * IP address validation schema
 */
export const IPAddressSchema = z.string().ip()

/**
 * CIDR notation validation schema
 */
export const CIDRSchema = z.string()
  .regex(/^(\d{1,3}\.){3}\d{1,3}\/\d{1,2}$/, 'Invalid CIDR notation')

/**
 * Security classification schema
 */
export const SecurityClassificationSchema = z.enum([
  'public',
  'internal',
  'confidential',
  'restricted'
])

// ============================================================================
// VALIDATION UTILITIES
// ============================================================================

/**
 * Create a validation function from a Zod schema
 */
export function createValidator<T>(schema: z.ZodSchema<T>) {
  return (data: unknown): { success: true; data: T } | { success: false; errors: z.ZodError } => {
    const result = schema.safeParse(data)
    if (result.success) {
      return { success: true, data: result.data }
    } else {
      return { success: false, errors: result.error }
    }
  }
}

/**
 * Async validation function
 */
export function createAsyncValidator<T>(schema: z.ZodSchema<T>) {
  return async (data: unknown): Promise<{ success: true; data: T } | { success: false; errors: z.ZodError }> => {
    try {
      const result = await schema.parseAsync(data)
      return { success: true, data: result }
    } catch (error) {
      if (error instanceof z.ZodError) {
        return { success: false, errors: error }
      }
      throw error
    }
  }
}

/**
 * Transform Zod errors to user-friendly messages
 */
export function formatValidationErrors(error: z.ZodError): Record<string, string[]> {
  const errors: Record<string, string[]> = {}
  
  for (const issue of error.issues) {
    const path = issue.path.join('.')
    if (!errors[path]) {
      errors[path] = []
    }
    errors[path].push(issue.message)
  }
  
  return errors
}

/**
 * Validate and transform data with custom error handling
 */
export function validateAndTransform<T, U>(
  schema: z.ZodSchema<T>,
  data: unknown,
  transform?: (data: T) => U
): { success: true; data: U extends undefined ? T : U } | { success: false; errors: Record<string, string[]> } {
  const result = schema.safeParse(data)
  
  if (result.success) {
    const transformedData = transform ? transform(result.data) : result.data
    return { success: true, data: transformedData as any }
  } else {
    return { success: false, errors: formatValidationErrors(result.error) }
  }
}
