<template>
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
    <!-- User Details -->
    <div class="space-y-3">
      <h4 class="text-sm font-semibold text-gray-900 dark:text-white">User Details</h4>
      <div class="space-y-2 text-sm">
        <div class="flex justify-between">
          <span class="text-gray-500 dark:text-gray-400">ID:</span>
          <span class="text-gray-900 dark:text-white font-mono text-xs">{{ user.id }}</span>
        </div>
        <div class="flex justify-between">
          <span class="text-gray-500 dark:text-gray-400">Phone:</span>
          <span class="text-gray-900 dark:text-white">{{ user.phone || 'Not provided' }}</span>
        </div>
        <div class="flex justify-between">
          <span class="text-gray-500 dark:text-gray-400">Created:</span>
          <span class="text-gray-900 dark:text-white">{{ formatDate(user.createdAt) }}</span>
        </div>
        <div class="flex justify-between">
          <span class="text-gray-500 dark:text-gray-400">Last Updated:</span>
          <span class="text-gray-900 dark:text-white">{{ formatDate(user.updatedAt) }}</span>
        </div>
        <div class="flex justify-between">
          <span class="text-gray-500 dark:text-gray-400">2FA Status:</span>
          <div class="flex items-center gap-1">
            <Icon 
              :name="user.otpEnabled ? 'material-symbols:security' : 'material-symbols:security-off'" 
              :class="user.otpEnabled ? 'text-green-500' : 'text-gray-400'"
              class="h-4 w-4" 
            />
            <span :class="user.otpEnabled ? 'text-green-700 dark:text-green-400' : 'text-gray-500'">
              {{ user.otpEnabled ? 'Enabled' : 'Disabled' }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- Roles and Permissions -->
    <div class="space-y-3">
      <h4 class="text-sm font-semibold text-gray-900 dark:text-white">Roles & Permissions</h4>
      
      <!-- Roles -->
      <div>
        <span class="text-xs text-gray-500 dark:text-gray-400 mb-2 block">Roles</span>
        <div class="flex flex-wrap gap-1">
          <span
            v-for="role in user.roles"
            :key="role"
            :class="[
              'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium',
              getRoleBadgeClass(role)
            ]"
          >
            {{ role }}
          </span>
        </div>
      </div>

      <!-- Activity Status -->
      <div class="space-y-2">
        <span class="text-xs text-gray-500 dark:text-gray-400 block">Activity</span>
        <div class="space-y-1">
          <div class="flex items-center gap-2">
            <div :class="[
              'w-2 h-2 rounded-full',
              isOnline ? 'bg-green-500' : 'bg-gray-400'
            ]"></div>
            <span class="text-xs text-gray-600 dark:text-gray-300">
              {{ isOnline ? 'Online' : 'Offline' }}
            </span>
          </div>
          <div class="text-xs text-gray-500 dark:text-gray-400">
            Last seen: {{ user.lastLoginAt ? formatRelativeTime(user.lastLoginAt) : 'Never' }}
          </div>
        </div>
      </div>
    </div>

    <!-- Quick Actions -->
    <div class="space-y-3">
      <h4 class="text-sm font-semibold text-gray-900 dark:text-white">Quick Actions</h4>
      <div class="grid grid-cols-2 gap-2">
        <UiButton
          @click="$emit('action', { action: 'view', user })"
          size="sm"
          variant="outline"
          class="w-full"
        >
          <Icon name="material-symbols:visibility" class="h-4 w-4 mr-1" />
          View Profile
        </UiButton>
        <UiButton
          @click="$emit('action', { action: 'edit', user })"
          size="sm"
          variant="outline"
          class="w-full"
        >
          <Icon name="material-symbols:edit" class="h-4 w-4 mr-1" />
          Edit User
        </UiButton>
        <UiButton
          @click="$emit('action', { action: 'reset-password', user })"
          size="sm"
          variant="outline"
          class="w-full"
        >
          <Icon name="material-symbols:lock-reset" class="h-4 w-4 mr-1" />
          Reset Password
        </UiButton>
        <UiButton
          @click="$emit('action', { action: 'impersonate', user })"
          size="sm"
          variant="outline"
          class="w-full"
        >
          <Icon name="material-symbols:person-play" class="h-4 w-4 mr-1" />
          Impersonate
        </UiButton>
        <UiButton
          @click="$emit('action', { action: 'manage-2fa', user })"
          size="sm"
          variant="outline"
          class="w-full"
        >
          <Icon name="material-symbols:security" class="h-4 w-4 mr-1" />
          Manage 2FA
        </UiButton>
        <UiButton
          @click="$emit('action', { action: 'audit-log', user })"
          size="sm"
          variant="outline"
          class="w-full"
        >
          <Icon name="material-symbols:history" class="h-4 w-4 mr-1" />
          Audit Log
        </UiButton>
      </div>
      
      <!-- Danger Zone -->
      <div class="pt-3 border-t border-gray-200 dark:border-gray-600">
        <div class="grid grid-cols-2 gap-2">
          <UiButton
            @click="$emit('action', { action: user.isActive ? 'deactivate' : 'activate', user })"
            size="sm"
            :variant="user.isActive ? 'outline' : 'primary'"
            :class="user.isActive ? 'text-red-600 border-red-300 hover:bg-red-50' : 'text-green-600 border-green-300 hover:bg-green-50'"
            class="w-full"
          >
            <Icon 
              :name="user.isActive ? 'material-symbols:person-off' : 'material-symbols:person-check'" 
              class="h-4 w-4 mr-1" 
            />
            {{ user.isActive ? 'Deactivate' : 'Activate' }}
          </UiButton>
          <UiButton
            @click="$emit('action', { action: 'delete', user })"
            size="sm"
            variant="outline"
            class="w-full text-red-600 border-red-300 hover:bg-red-50"
          >
            <Icon name="material-symbols:delete" class="h-4 w-4 mr-1" />
            Delete
          </UiButton>
        </div>
      </div>
    </div>
  </div>

  <!-- Recent Activity Timeline -->
  <div class="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
    <h4 class="text-sm font-semibold text-gray-900 dark:text-white mb-4">Recent Activity</h4>
    <div class="space-y-3 max-h-32 overflow-y-auto">
      <div
        v-for="activity in recentActivities"
        :key="activity.id"
        class="flex items-start gap-3 text-sm"
      >
        <div 
          :class="[
            'flex-shrink-0 w-6 h-6 rounded-full flex items-center justify-center',
            getActivityIconClass(activity.type)
          ]"
        >
          <Icon :name="getActivityIcon(activity.type)" class="h-3 w-3 text-white" />
        </div>
        <div class="flex-1 min-w-0">
          <p class="text-gray-900 dark:text-white">{{ activity.description }}</p>
          <p class="text-xs text-gray-500 dark:text-gray-400">{{ formatRelativeTime(activity.timestamp) }}</p>
        </div>
      </div>
      
      <div v-if="recentActivities.length === 0" class="text-center py-4">
        <Icon name="material-symbols:timeline" class="h-8 w-8 text-gray-400 mx-auto mb-2" />
        <p class="text-sm text-gray-500 dark:text-gray-400">No recent activity</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { User } from '~/stores/user'

interface Activity {
  id: string
  type: 'login' | 'logout' | 'password_change' | 'profile_update' | 'role_change' | 'security'
  description: string
  timestamp: string
}

interface Props {
  user: User
  isOnline?: boolean
}

interface Emits {
  (e: 'action', payload: { action: string; user: User }): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Mock recent activities - in real app, this would come from API
const recentActivities = computed<Activity[]>(() => [
  {
    id: '1',
    type: 'login',
    description: 'Logged in from Chrome on Windows',
    timestamp: new Date(Date.now() - 1000 * 60 * 30).toISOString() // 30 minutes ago
  },
  {
    id: '2',
    type: 'profile_update',
    description: 'Updated profile information',
    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString() // 2 hours ago
  },
  {
    id: '3',
    type: 'security',
    description: 'Enabled two-factor authentication',
    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24).toISOString() // 1 day ago
  }
])

// Methods
const getRoleBadgeClass = (role: string) => {
  const roleClasses = {
    'SUPER_ADMIN': 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
    'ADMIN': 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300',
    'LAWYER': 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
    'TENANT_OWNER': 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
    'MEMBER': 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300',
  }
  return roleClasses[role as keyof typeof roleClasses] || 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300'
}

const getActivityIcon = (type: Activity['type']) => {
  const icons = {
    login: 'material-symbols:login',
    logout: 'material-symbols:logout',
    password_change: 'material-symbols:lock',
    profile_update: 'material-symbols:edit',
    role_change: 'material-symbols:admin-panel-settings',
    security: 'material-symbols:security'
  }
  return icons[type] || 'material-symbols:info'
}

const getActivityIconClass = (type: Activity['type']) => {
  const classes = {
    login: 'bg-green-500',
    logout: 'bg-gray-500',
    password_change: 'bg-orange-500',
    profile_update: 'bg-blue-500',
    role_change: 'bg-purple-500',
    security: 'bg-red-500'
  }
  return classes[type] || 'bg-gray-500'
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const formatRelativeTime = (dateString: string) => {
  const date = new Date(dateString)
  const now = new Date()
  const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))
  
  if (diffInMinutes < 1) return 'Just now'
  if (diffInMinutes < 60) return `${diffInMinutes}m ago`
  
  const diffInHours = Math.floor(diffInMinutes / 60)
  if (diffInHours < 24) return `${diffInHours}h ago`
  
  const diffInDays = Math.floor(diffInHours / 24)
  if (diffInDays < 7) return `${diffInDays}d ago`
  
  return date.toLocaleDateString()
}
</script>
