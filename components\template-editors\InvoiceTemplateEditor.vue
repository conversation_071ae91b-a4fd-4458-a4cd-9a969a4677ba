<template>
  <div class="space-y-6">
    <!-- Invoice Header Settings -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <div class="space-y-4">
        <h3 class="text-sm font-medium text-gray-900 dark:text-white">Company Information</h3>
        
        <div class="space-y-3">
          <UiInput
            v-model="invoiceSettings.companyName"
            label="Company Name"
            placeholder="Your Company Name"
          />
          
          <UiTextarea
            v-model="invoiceSettings.companyAddress"
            label="Company Address"
            placeholder="123 Main St, City, State 12345"
            :rows="3"
          />
          
          <div class="grid grid-cols-2 gap-3">
            <UiInput
              v-model="invoiceSettings.companyPhone"
              label="Phone"
              placeholder="(*************"
            />
            <UiInput
              v-model="invoiceSettings.companyEmail"
              label="Email"
              placeholder="<EMAIL>"
              type="email"
            />
          </div>
        </div>
      </div>

      <div class="space-y-4">
        <h3 class="text-sm font-medium text-gray-900 dark:text-white">Invoice Settings</h3>
        
        <div class="space-y-3">
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Currency
            </label>
            <select
              v-model="invoiceSettings.currency"
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm"
            >
              <option value="USD">USD - US Dollar</option>
              <option value="EUR">EUR - Euro</option>
              <option value="GBP">GBP - British Pound</option>
              <option value="CAD">CAD - Canadian Dollar</option>
            </select>
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Payment Terms
            </label>
            <select
              v-model="invoiceSettings.paymentTerms"
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm"
            >
              <option value="immediate">Due Immediately</option>
              <option value="15">Net 15 Days</option>
              <option value="30">Net 30 Days</option>
              <option value="60">Net 60 Days</option>
              <option value="90">Net 90 Days</option>
            </select>
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Tax Rate (%)
            </label>
            <UiInput
              v-model.number="invoiceSettings.taxRate"
              type="number"
              min="0"
              max="100"
              step="0.01"
              placeholder="0.00"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- Line Items Configuration -->
    <div class="space-y-4">
      <div class="flex items-center justify-between">
        <h3 class="text-sm font-medium text-gray-900 dark:text-white">Line Items</h3>
        <UiButton @click="addLineItem" variant="outline" size="sm">
          <Icon name="heroicons:plus" class="w-4 h-4 mr-1" />
          Add Item
        </UiButton>
      </div>

      <div v-if="invoiceSettings.lineItems.length === 0" class="text-sm text-gray-500 dark:text-gray-400 italic text-center py-8">
        No line items added. Click "Add Item" to create invoice line items.
      </div>

      <div v-else class="space-y-3">
        <div
          v-for="(item, index) in invoiceSettings.lineItems"
          :key="index"
          class="flex items-center space-x-3 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700"
        >
          <div class="flex-1 grid grid-cols-1 md:grid-cols-5 gap-3">
            <UiInput
              v-model="item.description"
              placeholder="Item description"
              size="sm"
            />
            <UiInput
              v-model.number="item.quantity"
              type="number"
              min="0"
              step="0.01"
              placeholder="Qty"
              size="sm"
            />
            <UiInput
              v-model.number="item.rate"
              type="number"
              min="0"
              step="0.01"
              placeholder="Rate"
              size="sm"
            />
            <div class="flex items-center px-3 py-2 bg-gray-100 dark:bg-gray-700 rounded text-sm font-medium">
              {{ formatCurrency(item.quantity * item.rate) }}
            </div>
            <UiButton @click="removeLineItem(index)" variant="ghost" size="sm">
              <Icon name="heroicons:trash" class="w-4 h-4" />
            </UiButton>
          </div>
        </div>
      </div>
    </div>

    <!-- Invoice Template Editor -->
    <div class="space-y-4">
      <div class="flex items-center justify-between">
        <h3 class="text-sm font-medium text-gray-900 dark:text-white">Invoice Template</h3>
        <div class="flex items-center space-x-2">
          <UiButton @click="toggleEditorMode" variant="outline" size="sm">
            <Icon :name="editorMode === 'visual' ? 'heroicons:code-bracket' : 'heroicons:eye'" class="w-4 h-4 mr-1" />
            {{ editorMode === 'visual' ? 'HTML' : 'Visual' }}
          </UiButton>
          <UiButton @click="insertVariable" variant="outline" size="sm">
            <Icon name="heroicons:plus" class="w-4 h-4 mr-1" />
            Variable
          </UiButton>
          <UiButton @click="useDefaultTemplate" variant="outline" size="sm">
            <Icon name="heroicons:document-text" class="w-4 h-4 mr-1" />
            Default Template
          </UiButton>
        </div>
      </div>

      <!-- Visual Editor -->
      <div v-if="editorMode === 'visual'" class="border border-gray-300 dark:border-gray-600 rounded-lg overflow-hidden">
        <div
          ref="visualEditor"
          contenteditable="true"
          @input="handleVisualEditorInput"
          @paste="handlePaste"
          class="min-h-[400px] p-6 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-inset bg-white dark:bg-gray-900"
          :class="[
            'prose prose-lg max-w-none',
            'dark:prose-invert dark:text-gray-100',
            'placeholder:text-gray-400 dark:placeholder:text-gray-500'
          ]"
          :data-placeholder="'Design your invoice template here...'"
          v-html="localContent"
        ></div>
      </div>

      <!-- HTML Editor -->
      <div v-else class="space-y-2">
        <UiTextarea
          id="invoiceHtmlContent"
          v-model="localContent"
          label=""
          placeholder="Enter HTML content..."
          :rows="15"
          class="font-mono text-sm"
        />
        <div class="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
          <span>HTML mode - you can use HTML markup and CSS styling</span>
          <span>{{ contentLength }} characters</span>
        </div>
      </div>
    </div>

    <!-- Invoice Preview -->
    <div class="border-t border-gray-200 dark:border-gray-700 pt-6">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-sm font-medium text-gray-900 dark:text-white">Invoice Preview</h3>
        <div class="flex items-center space-x-2">
          <UiButton @click="printInvoice" variant="outline" size="sm">
            <Icon name="heroicons:printer" class="w-4 h-4 mr-1" />
            Print
          </UiButton>
          <UiButton @click="exportPdf" variant="outline" size="sm">
            <Icon name="heroicons:arrow-down-tray" class="w-4 h-4 mr-1" />
            Export PDF
          </UiButton>
        </div>
      </div>
      
      <!-- Invoice Preview Container -->
      <div class="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg">
        <div class="bg-white dark:bg-gray-900 shadow-lg mx-auto rounded border border-gray-200 dark:border-gray-700 overflow-hidden max-w-4xl">
          <div class="p-8">
            <div v-html="renderedInvoice" class="prose prose-lg max-w-none dark:prose-invert"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- Variable Insert Modal -->
    <VariableInsertModal
      v-if="showVariableModal"
      @close="showVariableModal = false"
      @insert="handleVariableInsert"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive, watch, onMounted, defineAsyncComponent } from 'vue'

// Lazy load modals
const VariableInsertModal = defineAsyncComponent(() => import('../template-modals/VariableInsertModal.vue'))

// Props
interface Props {
  content: string
  variables: string[]
  settings: any
  templateData: any
  category?: any
}

const props = withDefaults(defineProps<Props>(), {
  content: '',
  variables: () => [],
  settings: () => ({}),
})

// Emits
const emit = defineEmits<{
  'update:content': [content: string]
  'update:variables': [variables: string[]]
  'update:settings': [settings: any]
  'content-change': [content: string, variables: string[], settings: any]
}>()

// State
const editorMode = ref<'visual' | 'html'>('visual')
const showVariableModal = ref(false)
const visualEditor = ref<HTMLElement>()

const localContent = ref(props.content)
const localVariables = ref([...props.variables])

const invoiceSettings = reactive({
  companyName: '',
  companyAddress: '',
  companyPhone: '',
  companyEmail: '',
  currency: 'USD',
  paymentTerms: '30',
  taxRate: 0,
  lineItems: [],
  ...props.settings
})

// Computed
const contentLength = computed(() => localContent.value?.length || 0)

const subtotal = computed(() => {
  return invoiceSettings.lineItems.reduce((sum: number, item: any) => {
    return sum + (item.quantity * item.rate)
  }, 0)
})

const taxAmount = computed(() => {
  return subtotal.value * (invoiceSettings.taxRate / 100)
})

const total = computed(() => {
  return subtotal.value + taxAmount.value
})

const renderedInvoice = computed(() => {
  let content = localContent.value || getDefaultInvoiceTemplate()
  
  // Replace variables with sample values
  const sampleData = {
    companyName: invoiceSettings.companyName || 'Your Company Name',
    companyAddress: invoiceSettings.companyAddress || '123 Main St, City, State 12345',
    companyPhone: invoiceSettings.companyPhone || '(*************',
    companyEmail: invoiceSettings.companyEmail || '<EMAIL>',
    invoiceNumber: 'INV-001',
    invoiceDate: new Date().toLocaleDateString(),
    dueDate: new Date(Date.now() + (parseInt(invoiceSettings.paymentTerms) * 24 * 60 * 60 * 1000)).toLocaleDateString(),
    clientName: 'Client Name',
    clientAddress: 'Client Address',
    subtotal: formatCurrency(subtotal.value),
    taxRate: invoiceSettings.taxRate,
    taxAmount: formatCurrency(taxAmount.value),
    total: formatCurrency(total.value),
    currency: invoiceSettings.currency,
  }
  
  // Replace template variables
  Object.entries(sampleData).forEach(([key, value]) => {
    content = content.replace(new RegExp(`{${key}}`, 'g'), value.toString())
  })
  
  // Replace custom variables with placeholders
  localVariables.value.forEach(variable => {
    if (!sampleData[variable as keyof typeof sampleData]) {
      const placeholder = `<span class="bg-green-100 text-green-800 px-1 rounded text-sm font-mono">[${variable.toUpperCase()}]</span>`
      content = content.replace(new RegExp(`{${variable}}`, 'g'), placeholder)
    }
  })
  
  // Insert line items table
  if (content.includes('{lineItemsTable}')) {
    const lineItemsHtml = generateLineItemsTable()
    content = content.replace('{lineItemsTable}', lineItemsHtml)
  }
  
  return content
})

// Watchers
watch(localContent, (newContent) => {
  emit('update:content', newContent)
  emit('content-change', newContent, localVariables.value, invoiceSettings)
}, { deep: true })

watch(localVariables, (newVariables) => {
  emit('update:variables', newVariables)
  emit('content-change', localContent.value, newVariables, invoiceSettings)
}, { deep: true })

watch(invoiceSettings, (newSettings) => {
  emit('update:settings', newSettings)
  emit('content-change', localContent.value, localVariables.value, newSettings)
}, { deep: true })

// Methods
const toggleEditorMode = () => {
  editorMode.value = editorMode.value === 'visual' ? 'html' : 'visual'
}

const handleVisualEditorInput = () => {
  if (visualEditor.value) {
    localContent.value = visualEditor.value.innerHTML
  }
}

const handlePaste = (event: ClipboardEvent) => {
  event.preventDefault()
  const text = event.clipboardData?.getData('text/plain') || ''
  document.execCommand('insertText', false, text)
  handleVisualEditorInput()
}

const insertVariable = () => {
  showVariableModal.value = true
}

const handleVariableInsert = (variable: string) => {
  if (!localVariables.value.includes(variable)) {
    localVariables.value.push(variable)
  }
  
  const variableTag = `{${variable}}`
  
  if (editorMode.value === 'visual' && visualEditor.value) {
    document.execCommand('insertText', false, variableTag)
    handleVisualEditorInput()
  } else {
    localContent.value += variableTag
  }
  
  showVariableModal.value = false
}

const addLineItem = () => {
  invoiceSettings.lineItems.push({
    description: '',
    quantity: 1,
    rate: 0
  })
}

const removeLineItem = (index: number) => {
  invoiceSettings.lineItems.splice(index, 1)
}

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: invoiceSettings.currency
  }).format(amount)
}

const generateLineItemsTable = () => {
  if (invoiceSettings.lineItems.length === 0) {
    return '<p>No line items added.</p>'
  }
  
  let html = `
    <table style="width: 100%; border-collapse: collapse; margin: 20px 0;">
      <thead>
        <tr style="background-color: #f3f4f6;">
          <th style="padding: 12px; text-align: left; border: 1px solid #d1d5db;">Description</th>
          <th style="padding: 12px; text-align: center; border: 1px solid #d1d5db;">Quantity</th>
          <th style="padding: 12px; text-align: right; border: 1px solid #d1d5db;">Rate</th>
          <th style="padding: 12px; text-align: right; border: 1px solid #d1d5db;">Amount</th>
        </tr>
      </thead>
      <tbody>
  `
  
  invoiceSettings.lineItems.forEach((item: any) => {
    html += `
      <tr>
        <td style="padding: 12px; border: 1px solid #d1d5db;">${item.description || 'Item description'}</td>
        <td style="padding: 12px; text-align: center; border: 1px solid #d1d5db;">${item.quantity}</td>
        <td style="padding: 12px; text-align: right; border: 1px solid #d1d5db;">${formatCurrency(item.rate)}</td>
        <td style="padding: 12px; text-align: right; border: 1px solid #d1d5db;">${formatCurrency(item.quantity * item.rate)}</td>
      </tr>
    `
  })
  
  html += `
      </tbody>
    </table>
    <div style="margin-top: 20px; text-align: right;">
      <p><strong>Subtotal: ${formatCurrency(subtotal.value)}</strong></p>
      <p>Tax (${invoiceSettings.taxRate}%): ${formatCurrency(taxAmount.value)}</p>
      <p style="font-size: 1.2em;"><strong>Total: ${formatCurrency(total.value)}</strong></p>
    </div>
  `
  
  return html
}

const getDefaultInvoiceTemplate = () => {
  return `
    <div style="max-width: 800px; margin: 0 auto; font-family: Arial, sans-serif;">
      <header style="text-align: center; margin-bottom: 40px;">
        <h1 style="color: #1f2937; margin-bottom: 10px;">INVOICE</h1>
        <div style="color: #6b7280;">
          <p><strong>{companyName}</strong></p>
          <p>{companyAddress}</p>
          <p>{companyPhone} | {companyEmail}</p>
        </div>
      </header>
      
      <div style="display: flex; justify-content: space-between; margin-bottom: 30px;">
        <div>
          <h3>Bill To:</h3>
          <p>{clientName}</p>
          <p>{clientAddress}</p>
        </div>
        <div style="text-align: right;">
          <p><strong>Invoice #:</strong> {invoiceNumber}</p>
          <p><strong>Date:</strong> {invoiceDate}</p>
          <p><strong>Due Date:</strong> {dueDate}</p>
        </div>
      </div>
      
      {lineItemsTable}
      
      <footer style="margin-top: 40px; padding-top: 20px; border-top: 1px solid #e5e7eb;">
        <p><strong>Payment Terms:</strong> Net ${invoiceSettings.paymentTerms} Days</p>
        <p>Thank you for your business!</p>
      </footer>
    </div>
  `
}

const useDefaultTemplate = () => {
  localContent.value = getDefaultInvoiceTemplate()
}

const printInvoice = () => {
  const printWindow = window.open('', '_blank')
  if (printWindow) {
    printWindow.document.write(`
      <html>
        <head>
          <title>Invoice Preview</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }
            @media print { body { margin: 0; } }
          </style>
        </head>
        <body>
          ${renderedInvoice.value}
        </body>
      </html>
    `)
    printWindow.document.close()
    printWindow.print()
  }
}

const exportPdf = () => {
  // PDF export functionality would be implemented here
  console.log('Exporting invoice to PDF...')
}

// Lifecycle
onMounted(() => {
  // Initialize content if empty
  if (!localContent.value) {
    localContent.value = getDefaultInvoiceTemplate()
  }
})
</script>

<style scoped>
[contenteditable]:empty:before {
  content: attr(data-placeholder);
  color: var(--color-green-400);
  font-style: italic;
}

.dark [contenteditable]:empty:before {
  color: var(--color-green-500);
}
</style>
