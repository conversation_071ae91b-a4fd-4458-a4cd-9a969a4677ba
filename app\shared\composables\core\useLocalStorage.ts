/**
 * Local Storage Composable
 * 
 * Reactive local storage with type safety, SSR compatibility,
 * and automatic serialization/deserialization
 */

import { ref, watch } from 'vue'
import type { Ref } from 'vue'

export interface UseLocalStorageOptions<T> {
  defaultValue?: T
  serializer?: {
    read: (value: string) => T
    write: (value: T) => string
  }
  syncAcrossTabs?: boolean
  onError?: (error: Error) => void
}

export interface UseLocalStorageReturn<T> {
  value: Ref<T>
  isSupported: Ref<boolean>
  remove: () => void
  clear: () => void
}

/**
 * Reactive localStorage composable
 */
export function useLocalStorage<T>(
  key: string,
  defaultValue?: T,
  options?: UseLocalStorageOptions<T>
): UseLocalStorageReturn<T> {
  const {
    serializer = {
      read: JSON.parse,
      write: JSON.stringify
    },
    syncAcrossTabs = true,
    onError = (error: any) => console.error('useLocalStorage error:', error) // Typed error
  } = options || {}

  // Check if localStorage is supported
  const isSupported = ref(false)
  
  // Initialize support check
  if (typeof window !== 'undefined') {
    try {
      const testKey = '__localStorage_test__'
      localStorage.setItem(testKey, 'test')
      localStorage.removeItem(testKey)
      isSupported.value = true
    } catch (error) {
      isSupported.value = false
      onError(error as Error)
    }
  }

  // Read initial value
  const read = (): T => {
    if (!isSupported.value) {
      return defaultValue as T
    }

    try {
      const item = localStorage.getItem(key)
      if (item === null) {
        return defaultValue as T
      }
      return serializer.read(item)
    } catch (error) {
      onError(error as Error)
      return defaultValue as T
    }
  }

  // Write value to localStorage
  const write = (value: T): void => {
    if (!isSupported.value) {
      return
    }

    try {
      if (value === null || value === undefined) {
        localStorage.removeItem(key)
      } else {
        localStorage.setItem(key, serializer.write(value))
      }
    } catch (error) {
      onError(error as Error)
    }
  }

  // Create reactive value
  const storedValue = read()
  const value = ref<T>(storedValue) as Ref<T>

  // Watch for changes and update localStorage
  watch(
    value,
    (newValue) => {
      write(newValue)
    },
    { deep: true }
  )

  // Listen for storage events (changes from other tabs)
  if (typeof window !== 'undefined' && syncAcrossTabs) {
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === key && e.newValue !== serializer.write(value.value)) {
        try {
          if (e.newValue === null) {
            value.value = defaultValue as T
          } else {
            value.value = serializer.read(e.newValue)
          }
        } catch (error) {
          onError(error as Error)
        }
      }
    }

    window.addEventListener('storage', handleStorageChange)

    // Cleanup on unmount
    if (typeof window !== 'undefined') {
      const cleanup = () => {
        window.removeEventListener('storage', handleStorageChange)
      }
      
      // Store cleanup function for potential use
      ;(window as any).__useLocalStorageCleanup = cleanup
    }
  }

  // Remove item from localStorage
  const remove = () => {
    if (isSupported.value) {
      try {
        localStorage.removeItem(key)
        value.value = defaultValue as T
      } catch (error) {
        onError(error as Error)
      }
    }
  }

  // Clear all localStorage
  const clear = () => {
    if (isSupported.value) {
      try {
        localStorage.clear()
        value.value = defaultValue as T
      } catch (error) {
        onError(error as Error)
      }
    }
  }

  return {
    value,
    isSupported,
    remove,
    clear
  }
}

/**
 * Specialized composables for common data types
 */

export function useLocalStorageString(
  key: string,
  defaultValue = ''
): UseLocalStorageReturn<string> {
  return useLocalStorage(key, defaultValue, {
    serializer: {
      read: (value: string) => value,
      write: (value: string) => value
    }
  })
}

export function useLocalStorageNumber(
  key: string,
  defaultValue = 0
): UseLocalStorageReturn<number> {
  return useLocalStorage(key, defaultValue, {
    serializer: {
      read: (value: string) => Number(value),
      write: (value: number) => String(value)
    }
  })
}

export function useLocalStorageBoolean(
  key: string,
  defaultValue = false
): UseLocalStorageReturn<boolean> {
  return useLocalStorage(key, defaultValue, {
    serializer: {
      read: (value: string) => value === 'true',
      write: (value: boolean) => String(value)
    }
  })
}

export function useLocalStorageArray<T>(
  key: string,
  defaultValue: T[] = []
): UseLocalStorageReturn<T[]> {
  return useLocalStorage(key, defaultValue)
}

export function useLocalStorageObject<T extends Record<string, any>>(
  key: string,
  defaultValue: T
): UseLocalStorageReturn<T> {
  return useLocalStorage(key, defaultValue)
}

/**
 * Hook for managing multiple localStorage keys
 */
export function useLocalStorageState<T extends Record<string, any>>(
  keys: T,
  prefix = ''
): { [K in keyof T]: Ref<T[K]> } {
  const result = {} as { [K in keyof T]: Ref<T[K]> }

  for (const [key, defaultValue] of Object.entries(keys)) {
    const storageKey = prefix ? `${prefix}_${key}` : key
    const { value } = useLocalStorage(storageKey, defaultValue)
    result[key as keyof T] = value
  }

  return result
}

/**
 * Utility functions
 */
export const localStorageUtils = {
  /**
   * Get all keys with a specific prefix
   */
  getKeysWithPrefix: (prefix: string): string[] => {
    if (typeof window === 'undefined') return []
    
    const keys: string[] = []
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key && key.startsWith(prefix)) {
        keys.push(key)
      }
    }
    return keys
  },

  /**
   * Remove all keys with a specific prefix
   */
  removeKeysWithPrefix: (prefix: string): void => {
    if (typeof window === 'undefined') return
    
    const keys = localStorageUtils.getKeysWithPrefix(prefix)
    keys.forEach(key => localStorage.removeItem(key))
  },

  /**
   * Get storage size in bytes
   */
  getStorageSize: (): number => {
    if (typeof window === 'undefined') return 0
    
    let total = 0
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key) {
        const value = localStorage.getItem(key) || ''
        total += key.length + value.length
      }
    }
    return total
  },

  /**
   * Check if storage is near capacity
   */
  isStorageNearCapacity: (): boolean => { // Removed unused threshold
    if (typeof window === 'undefined') return false
    
    try {
      const testKey = '__storage_test__'
      const testValue = 'x'.repeat(1024) // 1KB test
      localStorage.setItem(testKey, testValue)
      localStorage.removeItem(testKey)
      return false
    } catch {
      return true
    }
  }
}
