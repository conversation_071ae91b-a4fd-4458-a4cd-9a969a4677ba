<template>
  <div class="space-y-6 mb-6">
    <!-- Overview Stats -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <!-- Total Tenants -->
      <div
        class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6"
      >
        <div class="flex items-center justify-between">
          <div class="p-3 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
            <Icon
              name="i-heroicons:building-office-2"
              size="calc(var(--spacing) * 6)"
              class="text-blue-600 dark:text-blue-400"
            />
          </div>
          <div class="text-right">
            <p class="text-2xl font-bold text-gray-900 dark:text-white">
              {{ totalTenants }}
            </p>
            <p class="text-sm text-gray-500 dark:text-gray-400">
              {{ $t("dashboard.totalTenants") }}
            </p>
          </div>
        </div>
        <div class="mt-4">
          <div class="flex items-center text-sm">
            <Icon
              :name="
                tenantGrowthPercentage >= 0
                  ? 'material-symbols:trending-up'
                  : 'material-symbols:trending-down'
              "
              :class="
                tenantGrowthPercentage >= 0
                  ? ' text-green-500 mr-1'
                  : ' text-red-500 mr-1'
              "
              size="calc(var(--spacing) * 5)"
            />
            <span
              :class="
                tenantGrowthPercentage >= 0
                  ? 'text-green-600 dark:text-green-400'
                  : 'text-red-600 dark:text-red-400'
              "
            >
              {{ tenantGrowthPercentage >= 0 ? "+" : ""
              }}{{ tenantGrowthPercentage.toFixed(1) }}%
            </span>
            <span class="text-gray-500 dark:text-gray-400 ml-1"
              >{{ $t("dashboard.fromLast") }} {{ tenantGrowthPeriod }}</span
            >
          </div>
        </div>
      </div>

      <!-- Active Tenants -->
      <div
        class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6"
      >
        <div class="flex items-center justify-between">
          <div class="p-3 bg-green-100 dark:bg-green-900/20 rounded-lg">
            <Icon
              name="material-symbols:check-circle"
              size="calc(var(--spacing) * 6)"
              class="text-green-600 dark:text-green-400"
            />
          </div>
          <div class="text-right">
            <p class="text-2xl font-bold text-gray-900 dark:text-white">
              {{ activeTenants }}
            </p>
            <p class="text-sm text-gray-500 dark:text-gray-400">
              {{ $t("dashboard.activeTenants") }}
            </p>
          </div>
        </div>
        <div class="mt-4">
          <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div
              class="bg-green-500 h-2 rounded-full transition-all duration-300"
              :style="{ width: `${activePercentage}%` }"
            ></div>
          </div>
          <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
            {{ activePercentage }}% {{ $t("dashboard.ofTotal") }}
          </p>
        </div>
      </div>

      <!-- Revenue -->
      <div
        class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6"
      >
        <div class="flex items-center justify-between">
          <div class="p-3 bg-purple-100 dark:bg-purple-900/20 rounded-lg">
            <Icon
              name="material-symbols:payments"
              size="calc(var(--spacing) * 6)"
              class="text-purple-600 dark:text-purple-400"
            />
          </div>
          <div class="text-right">
            <p class="text-2xl font-bold text-gray-900 dark:text-white">
              ${{ monthlyRevenue.toLocaleString() }}
            </p>
            <p class="text-sm text-gray-500 dark:text-gray-400">
              {{ $t("dashboard.monthlyRevenue") }}
            </p>
          </div>
        </div>
        <div class="mt-4">
          <div class="flex items-center text-sm">
            <Icon
              :name="
                revenueGrowthPercentage >= 0
                  ? 'material-symbols:trending-up'
                  : 'material-symbols:trending-down'
              "
              :class="
                revenueGrowthPercentage >= 0
                  ? ' text-green-500 mr-1'
                  : ' text-red-500 mr-1'
              "
              size="calc(var(--spacing) * 5)"
            />
            <span
              :class="
                revenueGrowthPercentage >= 0
                  ? 'text-green-600 dark:text-green-400'
                  : 'text-red-600 dark:text-red-400'
              "
            >
              {{ revenueGrowthPercentage >= 0 ? "+" : ""
              }}{{ revenueGrowthPercentage.toFixed(1) }}%
            </span>
            <span class="text-gray-500 dark:text-gray-400 ml-1">{{
              $t("dashboard.fromLastMonth")
            }}</span>
          </div>
        </div>
      </div>

      <!-- Storage Usage -->
      <div
        class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6"
      >
        <div class="flex items-center justify-between">
          <div class="p-3 bg-yellow-100 dark:bg-yellow-900/20 rounded-lg">
            <Icon
              name="material-symbols:storage"
              size="calc(var(--spacing) * 6)"
              class="text-yellow-600 dark:text-yellow-400"
            />
          </div>
          <div class="text-right">
            <p class="text-2xl font-bold text-gray-900 dark:text-white">
              {{ storageUsed }}GB
            </p>
            <p class="text-sm text-gray-500 dark:text-gray-400">
              {{ $t("dashboard.storageUsed") }}
            </p>
          </div>
        </div>
        <div class="mt-4">
          <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div
              class="bg-yellow-500 h-2 rounded-full transition-all duration-300"
              :style="{ width: `${storagePercentage}%` }"
            ></div>
          </div>
          <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
            {{ storagePercentage }}% {{ $t("dashboard.ofLimit") }}
          </p>
        </div>
      </div>
    </div>

    <!-- Alert Summary -->
    <div v-if="hasAlerts" class="mt-6">
      <UiCard
        :title="$t('dashboard.growthAlerts')"
        icon="material-symbols:notifications-active"
      >
        <div class="space-y-3">
          <!-- Critical Alerts -->
          <div v-if="alertStatus.critical.length > 0" class="space-y-2">
            <h4
              class="text-sm font-medium text-red-600 dark:text-red-400 flex items-center gap-1"
            >
              <Icon name="material-symbols:error" size="calc(var(--spacing) * 4 ) " />
              {{ $t("dashboard.critical") }}
            </h4>
            <div
              v-for="alert in alertStatus.critical"
              :key="alert"
              class="p-2 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-200 dark:border-red-800"
            >
              <p class="text-sm text-red-800 dark:text-red-200">{{ alert }}</p>
            </div>
          </div>

          <!-- Warning Alerts -->
          <div v-if="alertStatus.warning.length > 0" class="space-y-2">
            <h4
              class="text-sm font-medium text-yellow-600 dark:text-yellow-400 flex items-center gap-1"
            >
              <Icon name="material-symbols:warning" size="calc(var(--spacing) * 4 ) " />
              {{ $t("dashboard.warning") }}
            </h4>
            <div
              v-for="alert in alertStatus.warning"
              :key="alert"
              class="p-2 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-800"
            >
              <p class="text-sm text-yellow-800 dark:text-yellow-200">{{ alert }}</p>
            </div>
          </div>

          <!-- Opportunity Alerts -->
          <div v-if="alertStatus.opportunity.length > 0" class="space-y-2">
            <h4
              class="text-sm font-medium text-green-600 dark:text-green-400 flex items-center gap-1"
            >
              <Icon
                name="material-symbols:trending-up"
                size="calc(var(--spacing) * 4 ) "
              />
              {{ $t("dashboard.opportunity") }}
            </h4>
            <div
              v-for="alert in alertStatus.opportunity"
              :key="alert"
              class="p-2 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800"
            >
              <p class="text-sm text-green-800 dark:text-green-200">{{ alert }}</p>
            </div>
          </div>

          <!-- Infrastructure Alerts -->
          <div v-if="alertStatus.infrastructure.length > 0" class="space-y-2">
            <h4
              class="text-sm font-medium text-blue-600 dark:text-blue-400 flex items-center gap-1"
            >
              <Icon name="material-symbols:storage" size="calc(var(--spacing) * 4 ) " />
              {{ $t("dashboard.infrastructure") }}
            </h4>
            <div
              v-for="alert in alertStatus.infrastructure"
              :key="alert"
              class="p-2 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800"
            >
              <p class="text-sm text-blue-800 dark:text-blue-200">{{ alert }}</p>
            </div>
          </div>
        </div>
      </UiCard>
    </div>
    <!-- Plan Distribution -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Plan Distribution Chart -->
      <UiCard :title="$t('dashboard.planDistribution')">
        <div class="mt-6">
          <div class="relative h-64 flex items-center justify-center">
            <!-- Center content -->
            
              <UiDonutChart
                :data="planDistribution"
                :height="260"
                :hide-legend="false"
                :show-legend="true"
                :radius="0"
              
                :center-value="totalTenants"
                :center-label="$t('dashboard.totalTenants')"
              />
           
          </div>

          <!-- Legend -->
          <!-- <div class="mt-4 space-y-2">
            <div
              v-for="plan in planDistribution"
              :key="plan.name"
              class="flex items-center justify-between"
            >
              <div class="flex items-center gap-3">
                <div
                  class="w-3 h-3 rounded-full"
                  :style="{ backgroundColor: plan.color }"
                ></div>
                <span class="text-sm font-medium text-gray-900 dark:text-white">{{
                  plan.name
                }}</span>
              </div>
              <div class="flex items-center gap-2">
                <span class="text-sm text-gray-500 dark:text-gray-400">{{
                  plan.count
                }}</span>
                <span class="text-xs text-gray-400 dark:text-gray-500"
                  >({{ plan.percentage }}%)</span
                >
              </div>
            </div>
          </div> -->
        </div>
      </UiCard>

      <!-- Growth Trends -->
      <UiCard :title="$t('dashboard.growthTrends')">
        <div class="space-y-4">
          <!-- New Tenants Growth -->
          <div
            class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg"
          >
            <div>
              <p class="text-sm font-medium text-gray-900 dark:text-white">
                {{ $t("dashboard.newTenants") }} ({{ newTenantsGrowthPeriod }})
              </p>
              <p class="text-xs text-gray-500 dark:text-gray-400">
                {{ $t("dashboard.growthRate") }}
              </p>
            </div>
            <div class="text-right">
              <p class="text-lg font-bold text-gray-900 dark:text-white">
                {{ newTenantsCount }}
              </p>
              <div class="flex items-center text-sm">
                <Icon
                  :name="
                    newTenantsGrowthPercentage >= 0
                      ? 'material-symbols:trending-up'
                      : 'material-symbols:trending-down'
                  "
                  :class="
                    newTenantsGrowthPercentage >= 0
                      ? ' text-green-500 mr-1'
                      : ' text-red-500 mr-1'
                  "
                  size="calc(var(--spacing) * 5)"
                />
                <span
                  :class="[
                    newTenantsGrowthPercentage >= 0
                      ? 'text-green-600 dark:text-green-400'
                      : 'text-red-600 dark:text-red-400',
                    tenantStore.isLowNewTenantGrowth ? 'font-bold' : '',
                  ]"
                >
                  {{ newTenantsGrowthPercentage >= 0 ? "+" : ""
                  }}{{ newTenantsGrowthPercentage.toFixed(1) }}%
                </span>
                <Icon
                  v-if="tenantStore.isLowNewTenantGrowth"
                  name="material-symbols:warning"
                  size="calc(var(--spacing) * 3)"
                  class="text-yellow-500 ml-1"
                />
              </div>
            </div>
          </div>

          <!-- Churn Rate -->
          <div
            class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg"
          >
            <div>
              <p class="text-sm font-medium text-gray-900 dark:text-white">
                {{ $t("dashboard.churnRate") }}
              </p>
              <p class="text-xs text-gray-500 dark:text-gray-400">
                {{ churnRatePeriod }} {{ $t("dashboard.churn") }}
              </p>
            </div>
            <div class="text-right">
              <p
                :class="[
                  'text-lg font-bold',
                  tenantStore.isCriticalChurnRate
                    ? 'text-red-600 dark:text-red-400'
                    : 'text-gray-900 dark:text-white',
                ]"
              >
                {{ churnRateValue.toFixed(1) }}%
              </p>
              <div class="flex items-center text-sm">
                <Icon
                  :name="
                    churnRateChange <= 0
                      ? 'material-symbols:trending-down'
                      : 'material-symbols:trending-up'
                  "
                  :class="
                    churnRateChange <= 0 ? ' text-green-500 mr-1' : ' text-red-500 mr-1'
                  "
                  size="calc(var(--spacing) * 5)"
                />
                <span
                  :class="
                    churnRateChange <= 0
                      ? 'text-green-600 dark:text-green-400'
                      : 'text-red-600 dark:text-red-400'
                  "
                >
                  {{ churnRateChange <= 0 ? "" : "+" }}{{ churnRateChange.toFixed(1) }}%
                </span>
                <Icon
                  v-if="tenantStore.isCriticalChurnRate"
                  name="material-symbols:error"
                  size="calc(var(--spacing) * 3)"
                  class="text-red-500 ml-1"
                />
              </div>
            </div>
          </div>

          <!-- Average Revenue per Tenant -->
          <div
            class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg"
          >
            <div>
              <p class="text-sm font-medium text-gray-900 dark:text-white">
                {{ $t("dashboard.avgRevenuePerTenant") }}
              </p>
              <p class="text-xs text-gray-500 dark:text-gray-400">
                {{ arptPeriod }} {{ $t("dashboard.arpt") }}
              </p>
            </div>
            <div class="text-right">
              <p class="text-lg font-bold text-gray-900 dark:text-white">
                ${{ arptValue.toFixed(2) }}
              </p>
              <div class="flex items-center text-sm">
                <Icon
                  :name="
                    arptChangePercentage >= 0
                      ? 'material-symbols:trending-up'
                      : 'material-symbols:trending-down'
                  "
                  :class="
                    arptChangePercentage >= 0
                      ? ' text-green-500 mr-1'
                      : ' text-red-500 mr-1'
                  "
                  size="calc(var(--spacing) * 5)"
                />
                <span
                  :class="[
                    arptChangePercentage >= 0
                      ? 'text-green-600 dark:text-green-400'
                      : 'text-red-600 dark:text-red-400',
                    tenantStore.isHighARPTGrowth ? 'font-bold' : '',
                  ]"
                >
                  {{ arptChangePercentage >= 0 ? "+" : ""
                  }}{{ arptChangePercentage.toFixed(1) }}%
                </span>
                <Icon
                  v-if="tenantStore.isHighARPTGrowth"
                  name="material-symbols:trending-up"
                  size="calc(var(--spacing) * 3)"
                  class="text-green-500 ml-1"
                />
              </div>
            </div>
          </div>

          <!-- MRR Growth -->
          <div
            class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg"
          >
            <div>
              <p class="text-sm font-medium text-gray-900 dark:text-white">
                {{ $t("dashboard.mrrGrowth") }}
              </p>
              <p class="text-xs text-gray-500 dark:text-gray-400">
                {{ mrrGrowthPeriod }} {{ $t("dashboard.growth") }}
              </p>
            </div>
            <div class="text-right">
              <p class="text-lg font-bold text-gray-900 dark:text-white">
                ${{ mrrTotal.toLocaleString() }}
              </p>
              <div class="flex items-center text-sm">
                <Icon
                  :name="
                    mrrGrowthPercentage >= 0
                      ? 'material-symbols:trending-up'
                      : 'material-symbols:trending-down'
                  "
                  :class="
                    mrrGrowthPercentage >= 0
                      ? ' text-green-500 mr-1'
                      : ' text-red-500 mr-1'
                  "
                  size="calc(var(--spacing) * 5)"
                />
                <span
                  :class="
                    mrrGrowthPercentage >= 0
                      ? 'text-green-600 dark:text-green-400'
                      : 'text-red-600 dark:text-red-400'
                  "
                >
                  {{ mrrGrowthPercentage >= 0 ? "+" : ""
                  }}{{ mrrGrowthPercentage.toFixed(1) }}%
                </span>
              </div>
            </div>
          </div>

          <!-- Storage Growth -->
          <div
            class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg"
          >
            <div>
              <p class="text-sm font-medium text-gray-900 dark:text-white">
                {{ $t("dashboard.storageGrowth") }}
              </p>
              <p class="text-xs text-gray-500 dark:text-gray-400">
                {{ storageGrowthPeriod }} {{ $t("dashboard.usage") }}
              </p>
            </div>
            <div class="text-right">
              <p class="text-lg font-bold text-gray-900 dark:text-white">
                {{ (storageGrowthTotal / 1024).toFixed(1) }}GB
              </p>
              <div class="flex items-center text-sm">
                <Icon
                  :name="
                    storageGrowthPercentage >= 0
                      ? 'material-symbols:trending-up'
                      : 'material-symbols:trending-down'
                  "
                  :class="
                    storageGrowthPercentage >= 0
                      ? ' text-blue-500 mr-1'
                      : ' text-red-500 mr-1'
                  "
                  size="calc(var(--spacing) * 5)"
                />
                <span
                  :class="[
                    storageGrowthPercentage >= 0
                      ? 'text-blue-600 dark:text-blue-400'
                      : 'text-red-600 dark:text-red-400',
                    tenantStore.isHighStorageGrowth ? 'font-bold' : '',
                  ]"
                >
                  {{ storageGrowthPercentage >= 0 ? "+" : ""
                  }}{{ storageGrowthPercentage.toFixed(1) }}%
                </span>
                <Icon
                  v-if="tenantStore.isHighStorageGrowth"
                  name="material-symbols:storage"
                  size="calc(var(--spacing) * 3)"
                  class="text-orange-500 ml-1"
                />
              </div>
            </div>
          </div>
        </div>
      </UiCard>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted } from "vue";
import { useTenantStore } from "~/stores/tenant";

// interface Emits {
//   (e: 'create-tenant'): void
//   (e: 'view-all'): void
// }

// const emit = defineEmits<Emits>()
const tenantStore = useTenantStore();

// Fetch overview data on component mount
onMounted(async () => {
  if (!tenantStore.hasOverviewData) {
    await tenantStore.fetchAllOverviewData();
  }
});

// Computed properties using store data
const totalTenants = computed(() => tenantStore.totalTenantsCount);

const activeTenants = computed(() => tenantStore.activeTenantsCount);

const activePercentage = computed(() => {
  const total = tenantStore.tenantOverview?.totalTenants.total || 0;
  const active = tenantStore.tenantOverview?.statusCounts.active || 0;
  return total > 0 ? Math.round((active / total) * 100) : 0;
});

const monthlyRevenue = computed(() => tenantStore.monthlyRevenueAmount);

// const revenueGrowth = computed(() => tenantStore.tenantOverview?.monthlyRevenue.revenueGrowth || 0)

const revenueGrowthPercentage = computed(
  () => tenantStore.tenantOverview?.monthlyRevenue.revenueGrowthPercentage || 0
);

const storageUsed = computed(() => {
  const usedMB = tenantStore.tenantOverview?.storageUsed.totalUsedMB || 0;
  return Math.round(usedMB / 1024); // Convert MB to GB
});

const storagePercentage = computed(() => tenantStore.storageUsagePercentage);

const planDistribution = computed(() => {
  const distribution = tenantStore.tenantOverview?.planDistribution || [];

  const colors = {
    basic: "#3B82F6",
    pro: "#10B981",
    enterprise: "#8B5CF6",
    starter: "#F59E0B",
    professional: "#EF4444",
  };

  return distribution.map((item) => ({
    name: item.plan.charAt(0).toUpperCase() + item.plan.slice(1),
    count: item.count,
    percentage: item.percentage,
    value: item.percentage,
    color: colors[item.plan as keyof typeof colors] || "#6B7280",
  }));
});

// Function to calculate segment start position for pie chart
const getSegmentStart = (index: number) => {
  let start = 0;
  for (let i = 0; i < index; i++) {
    start += planDistribution.value[i]?.percentage || 0;
  }
  return start * 3.6; // Convert percentage to degrees
};

const avgRevenuePerTenant = computed(() => {
  const revenue = tenantStore.tenantOverview?.monthlyRevenue.monthlyRevenue || 0;
  const active = tenantStore.tenantOverview?.statusCounts.active || 0;
  return active > 0 ? Math.round(revenue / active) : 0;
});

// Growth data
const tenantGrowthPercentage = computed(
  () => tenantStore.tenantOverview?.totalTenants.growthPercentage || 0
);
const tenantGrowthPeriod = computed(
  () => tenantStore.tenantOverview?.totalTenants.period || "month"
);

// Enhanced growth trends data
const newTenantsCount = computed(() => tenantStore.newTenantsGrowth?.total || 0);
const newTenantsGrowthPercentage = computed(
  () => tenantStore.newTenantsGrowth?.growthPercentage || 0
);
const newTenantsGrowthPeriod = computed(
  () => tenantStore.newTenantsGrowth?.period || "30 days"
);

const churnRateValue = computed(() => tenantStore.churnRateData?.churnRate || 0);
const churnRateChange = computed(() => tenantStore.churnRateData?.churnRateChange || 0);
const churnRatePeriod = computed(() => tenantStore.churnRateData?.period || "30 days");

const arptValue = computed(() => tenantStore.avgRevenuePerTenantData?.avgRevenue || 0);
const arptChangePercentage = computed(
  () => tenantStore.avgRevenuePerTenantData?.avgRevenueChangePercentage || 0
);
const arptPeriod = computed(
  () => tenantStore.avgRevenuePerTenantData?.period || "30 days"
);

const mrrTotal = computed(() => tenantStore.mrrGrowthData?.total || 0);
const mrrGrowthPercentage = computed(
  () => tenantStore.mrrGrowthData?.growthPercentage || 0
);
const mrrGrowthPeriod = computed(() => tenantStore.mrrGrowthData?.period || "30 days");

const storageGrowthTotal = computed(() => tenantStore.storageGrowthData?.total || 0);
const storageGrowthPercentage = computed(
  () => tenantStore.storageGrowthData?.growthPercentage || 0
);
const storageGrowthPeriod = computed(
  () => tenantStore.storageGrowthData?.period || "30 days"
);

// Alert status
const alertStatus = computed(() => tenantStore.getAlertStatus());
const hasAlerts = computed(() => {
  const alerts = alertStatus.value;
  return (
    alerts.critical.length > 0 ||
    alerts.warning.length > 0 ||
    alerts.opportunity.length > 0 ||
    alerts.infrastructure.length > 0
  );
});

// Loading state
const loading = computed(() => tenantStore.isLoading);
</script>
