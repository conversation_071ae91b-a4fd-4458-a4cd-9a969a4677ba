/**
 * Auto-detects the DPI of the current environment.
 * @returns The detected DPI, or a default of 96.
 */
function getDPI() {
  if (typeof window === 'undefined') {
    return 96
  }

  const dpi = window.devicePixelRatio || 1
  return dpi * 96
}

/**
 * Converts centimeters to pixels.
 * @param cm - The value in centimeters.
 * @param dpi - The DPI to use for conversion. Auto-detected if not provided.
 * @returns The equivalent value in pixels.
 */
export function cmToPixels(cm: number, dpi?: number) {
  const d = dpi || getDPI()
  return Math.round((cm / 2.54) * d)
}

/**
 * Converts inches to pixels.
 * @param inches - The value in inches.
 * @param dpi - The DPI to use for conversion. Auto-detected if not provided.
 * @returns The equivalent value in pixels.
 */
export function inchToPixels(inches: number, dpi?: number) {
  const d = dpi || getDPI()
  return Math.round(inches * d)
}


/**
 * calc content height.
 * @param el - The element to calc.
 * @returns The equivalent value in pixels.
 */
export function calcContentHeight(el: HTMLElement) {
  return el.scrollHeight
}


export function getCurrentlyFocusedNode(tr: any) {
  if (
    !tr ||
    !tr.selection 
  ) {
    return {
      node: null,
      pos: null,
      dom: null,
    }
  }

 
  const { $anchor } = tr.selection

  if (!$anchor || typeof $anchor.before !== 'function' || typeof $anchor.node !== 'function') {
    return {
      node: null,
      pos: null,
      dom: null,
    }
  }

  // Get the position of the parent node that has focus
  const pos = $anchor.before($anchor.depth)
  const node = $anchor.node($anchor.depth)
  // const dom = editor.view.nodeDOM ? editor.view.nodeDOM(pos) : null

  return {
    node,
    pos,
    // dom: dom instanceof HTMLElement ? dom : null,
  }
}
 
 