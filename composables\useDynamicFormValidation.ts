import { ref, computed, reactive, watch } from 'vue'
import { z } from 'zod'
import type {
  Dynamic<PERSON>orm<PERSON>ield,
  DynamicFormFieldValidation,
  DynamicFormConfig,
  DynamicFormCondition
} from '~/app/shared/types'

export interface DynamicFormValidationError {
  field: string
  message: string
  type: string
}

export interface ValidationState {
  isValid: boolean
  isValidating: boolean
  errors: Record<string, string[]>
  touched: Record<string, boolean>
  validationCount: number
}

export const useDynamicFormValidation = (config: DynamicFormConfig, formData: Record<string, any>) => {
  // Reactive validation state
  const validationState = reactive<ValidationState>({
    isValid: false,
    isValidating: false,
    errors: {},
    touched: {},
    validationCount: 0
  })

  // Validation rules cache
  const validationRulesCache = new Map<string, z.ZodSchema>()

  // Computed properties
  const hasErrors = computed(() => {
    return Object.keys(validationState.errors).length > 0
  })

  const errorCount = computed(() => {
    return Object.values(validationState.errors).reduce((count, errors) => count + errors.length, 0)
  })

  const touchedFields = computed(() => {
    return Object.keys(validationState.touched).filter(field => validationState.touched[field])
  })

  // Create Zod schema for a field
  const createFieldSchema = (field: DynamicFormField): z.ZodSchema => {
    const cacheKey = `${field.name}-${JSON.stringify(field.validation)}`
    
    if (validationRulesCache.has(cacheKey)) {
      return validationRulesCache.get(cacheKey)!
    }

    let schema: z.ZodSchema = z.any()
    const validation = field.validation

    if (!validation) {
      validationRulesCache.set(cacheKey, schema)
      return schema
    }

    // Base schema based on field type
    switch (field.type) {
      case 'text':
      case 'email':
      case 'password':
      case 'textarea':
        schema = z.string()
        break
      case 'number':
        schema = validation.integer ? z.number().int() : z.number()
        break
      case 'checkbox':
        schema = z.boolean()
        break
      case 'date':
      case 'time':
      case 'datetime':
        schema = z.string().or(z.date())
        break
      case 'file':
      case 'image':
        schema = z.instanceof(File).or(z.array(z.instanceof(File)))
        break
      case 'select':
      case 'radio':
        schema = z.any()
        break
      case 'multiselect':
        schema = z.array(z.any())
        break
      default:
        schema = z.any()
    }

    // Apply validation rules
    if (field.type === 'text' || field.type === 'email' || field.type === 'password' || field.type === 'textarea') {
      let stringSchema = schema as z.ZodString

      if (validation.minLength) {
        stringSchema = stringSchema.min(validation.minLength, validation.messages?.minLength || `Minimum ${validation.minLength} characters required`)
      }

      if (validation.maxLength) {
        stringSchema = stringSchema.max(validation.maxLength, validation.messages?.maxLength || `Maximum ${validation.maxLength} characters allowed`)
      }

      if (validation.pattern) {
        const regex = typeof validation.pattern === 'string' ? new RegExp(validation.pattern) : validation.pattern
        stringSchema = stringSchema.regex(regex, validation.messages?.pattern || 'Invalid format')
      }

      if (validation.email || field.type === 'email') {
        stringSchema = stringSchema.email(validation.messages?.email || 'Invalid email address')
      }

      if (validation.url) {
        stringSchema = stringSchema.url(validation.messages?.url || 'Invalid URL')
      }

      schema = stringSchema
    }

    if (field.type === 'number') {
      let numberSchema = schema as z.ZodNumber

      if (validation.min !== undefined) {
        numberSchema = numberSchema.min(validation.min, validation.messages?.min || `Minimum value is ${validation.min}`)
      }

      if (validation.max !== undefined) {
        numberSchema = numberSchema.max(validation.max, validation.messages?.max || `Maximum value is ${validation.max}`)
      }

      if (validation.positive) {
        numberSchema = numberSchema.positive(validation.messages?.positive || 'Must be a positive number')
      }

      schema = numberSchema
    }

    // Required validation
    if (validation.required) {
      if (field.type === 'checkbox') {
        schema = z.boolean().refine(val => val === true, validation.messages?.required || 'This field is required')
      } else if (field.type === 'multiselect') {
        schema = z.array(z.any()).min(1, validation.messages?.required || 'At least one option must be selected')
      } else {
        schema = schema.refine(
          val => val !== undefined && val !== null && val !== '' && (!Array.isArray(val) || val.length > 0),
          validation.messages?.required || 'This field is required'
        )
      }
    } else {
      // Make optional if not required
      schema = schema.optional()
    }

    // Custom validation
    if (validation.custom) {
      schema = schema.refine(
        (val) => {
          const result = validation.custom!(val, formData)
          return result === null
        },
        (val) => ({
          message: validation.custom!(val, formData) || 'Invalid value'
        })
      )
    }

    validationRulesCache.set(cacheKey, schema)
    return schema
  }

  // Validate a single field
  const validateField = async (field: DynamicFormField, value: any): Promise<string[]> => {
    const errors: string[] = []

    try {
      // Check if field should be validated based on conditional logic
      if (!shouldValidateField(field)) {
        return errors
      }

      const schema = createFieldSchema(field)
      await schema.parseAsync(value)

      // Custom async validation
      if (field.validation?.customAsync) {
        const asyncResult = await field.validation.customAsync(value, formData)
        if (asyncResult) {
          errors.push(asyncResult)
        }
      }

    } catch (error) {
      if (error instanceof z.ZodError) {
        errors.push(...error.errors.map(err => err.message))
      } else {
        errors.push('Validation error')
      }
    }

    return errors
  }

  // Check if field should be validated based on conditional logic
  const shouldValidateField = (field: DynamicFormField): boolean => {
    // If field is not visible, don't validate
    if (!shouldShowField(field)) {
      return false
    }

    // If field is disabled, don't validate
    if (field.disabled || shouldDisableField(field)) {
      return false
    }

    // Check conditional require
    if (field.conditional?.require) {
      return evaluateConditions(field.conditional.require)
    }

    return true
  }

  // Check if field should be shown
  const shouldShowField = (field: DynamicFormField): boolean => {
    if (!field.conditional?.show && !field.conditional?.hide) {
      return true
    }

    if (field.conditional.show) {
      const showResult = evaluateConditions(field.conditional.show)
      if (!showResult) return false
    }

    if (field.conditional.hide) {
      const hideResult = evaluateConditions(field.conditional.hide)
      if (hideResult) return false
    }

    return true
  }

  // Check if field should be disabled
  const shouldDisableField = (field: DynamicFormField): boolean => {
    if (field.conditional?.disable) {
      return evaluateConditions(field.conditional.disable)
    }
    return false
  }

  // Evaluate conditional logic
  const evaluateConditions = (conditions: DynamicFormCondition[]): boolean => {
    if (!conditions.length) return true

    return conditions.every(condition => {
      const fieldValue = formData[condition.field]

      switch (condition.operator) {
        case 'equals':
          return fieldValue === condition.value
        case 'not_equals':
          return fieldValue !== condition.value
        case 'contains':
          return String(fieldValue).includes(String(condition.value))
        case 'not_contains':
          return !String(fieldValue).includes(String(condition.value))
        case 'in':
          return condition.values?.includes(fieldValue)
        case 'not_in':
          return !condition.values?.includes(fieldValue)
        case 'greater_than':
          return Number(fieldValue) > Number(condition.value)
        case 'less_than':
          return Number(fieldValue) < Number(condition.value)
        case 'greater_equal':
          return Number(fieldValue) >= Number(condition.value)
        case 'less_equal':
          return Number(fieldValue) <= Number(condition.value)
        case 'empty':
          return !fieldValue || fieldValue === '' || (Array.isArray(fieldValue) && fieldValue.length === 0)
        case 'not_empty':
          return fieldValue && fieldValue !== '' && (!Array.isArray(fieldValue) || fieldValue.length > 0)
        case 'regex':
          return new RegExp(condition.pattern || '').test(String(fieldValue))
        default:
          return true
      }
    })
  }

  // Validate all fields
  const validateAll = async (): Promise<boolean> => {
    validationState.isValidating = true
    validationState.validationCount++
    validationState.errors = {}

    const validationPromises = config.fields.map(async (field) => {
      const fieldErrors = await validateField(field, formData[field.name])
      if (fieldErrors.length > 0) {
        validationState.errors[field.name] = fieldErrors
      }
    })

    await Promise.all(validationPromises)

    validationState.isValid = Object.keys(validationState.errors).length === 0
    validationState.isValidating = false

    return validationState.isValid
  }

  // Validate single field by name
  const validateFieldByName = async (fieldName: string): Promise<boolean> => {
    const field = config.fields.find(f => f.name === fieldName)
    if (!field) return true

    const fieldErrors = await validateField(field, formData[fieldName])
    
    if (fieldErrors.length > 0) {
      validationState.errors[fieldName] = fieldErrors
    } else {
      delete validationState.errors[fieldName]
    }

    validationState.isValid = Object.keys(validationState.errors).length === 0
    return fieldErrors.length === 0
  }

  // Touch field
  const touchField = (fieldName: string) => {
    validationState.touched[fieldName] = true
  }

  // Touch all fields
  const touchAll = () => {
    config.fields.forEach(field => {
      validationState.touched[field.name] = true
    })
  }

  // Clear field error
  const clearFieldError = (fieldName: string) => {
    delete validationState.errors[fieldName]
    validationState.isValid = Object.keys(validationState.errors).length === 0
  }

  // Clear all errors
  const clearAllErrors = () => {
    validationState.errors = {}
    validationState.isValid = true
  }

  // Reset validation state
  const resetValidation = () => {
    validationState.errors = {}
    validationState.touched = {}
    validationState.isValid = false
    validationState.validationCount = 0
    validationRulesCache.clear()
  }

  // Get field error
  const getFieldError = (fieldName: string): string | null => {
    const errors = validationState.errors[fieldName]
    return errors && errors.length > 0 ? errors[0] : null
  }

  // Check if field has error
  const hasFieldError = (fieldName: string): boolean => {
    return !!validationState.errors[fieldName]?.length
  }

  // Check if field is touched
  const isFieldTouched = (fieldName: string): boolean => {
    return !!validationState.touched[fieldName]
  }

  return {
    // State
    validationState,
    hasErrors,
    errorCount,
    touchedFields,

    // Methods
    validateAll,
    validateField,
    validateFieldByName,
    touchField,
    touchAll,
    clearFieldError,
    clearAllErrors,
    resetValidation,
    getFieldError,
    hasFieldError,
    isFieldTouched,
    shouldValidateField,
    shouldShowField,
    shouldDisableField,
    evaluateConditions
  }
}

// Validation rule presets
export const validationPresets = {
  required: { required: true },
  email: { email: true, required: true },
  phone: { 
    pattern: /^[\+]?[1-9][\d]{0,15}$/,
    messages: { pattern: 'Please enter a valid phone number' }
  },
  url: { url: true },
  password: {
    required: true,
    minLength: 8,
    pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
    messages: {
      pattern: 'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'
    }
  },
  positiveNumber: {
    numeric: true,
    positive: true,
    messages: { positive: 'Must be a positive number' }
  },
  integer: {
    numeric: true,
    integer: true,
    messages: { integer: 'Must be a whole number' }
  }
}
