import { reactive, computed, watch, readonly } from 'vue';
import { useApi } from './useApi.js';
import { useToast } from './useToast.js';

export type SaveStatus = 'idle' | 'saving' | 'saved' | 'error';

export interface SettingsState<T = any> {
  data: T;
  originalData: T;
  loading: boolean;
  saveStatus: SaveStatus;
  hasChanges: boolean;
  errors: Record<string, string>;
}

export const useSettingsState = <T extends object>( // Changed constraint to 'object'
  initialData: T,
  options: {
    saveEndpoint?: string;
    loadEndpoint?: string;
    autoSave?: boolean;
    autoSaveDelay?: number;
  } = {}
) => {
  const { patch, get } = useApi();
  const { showToast } = useToast();

  // State
  const state = reactive<SettingsState<T>>({
    data: { ...initialData },
    originalData: { ...initialData },
    loading: false,
    saveStatus: 'idle',
    hasChanges: false,
    errors: {}
  });

  // Auto-save timer
  let autoSaveTimer: NodeJS.Timeout | null = null;

  // Computed
  const hasChanges = computed(() => {
    return JSON.stringify(state.data) !== JSON.stringify(state.originalData);
  });

  const canSave = computed(() => {
    return hasChanges.value && state.saveStatus !== 'saving' && Object.keys(state.errors).length === 0;
  });

  const isLoading = computed(() => state.loading);
  const isSaving = computed(() => state.saveStatus === 'saving');

  // Watch for changes
  watch(
    () => state.data,
    () => {
      state.hasChanges = hasChanges.value;
      
      // Auto-save functionality
      if (options.autoSave && hasChanges.value && options.saveEndpoint) {
        if (autoSaveTimer) {
          clearTimeout(autoSaveTimer);
        }
        
        autoSaveTimer = setTimeout(() => {
          save();
        }, options.autoSaveDelay || 2000);
      }
    },
    { deep: true }
  );

  // Methods
  const load = async (): Promise<void> => {
    if (!options.loadEndpoint) return;

    state.loading = true;
    try {
      const responseData: unknown = await get(options.loadEndpoint);
      if (typeof responseData === 'object' && responseData !== null && !Array.isArray(responseData)) {
        state.data = Object.assign({}, state.data, responseData as Partial<T>);
      } else if (responseData !== undefined && responseData !== null) {
        console.warn('Load settings: Response was not a suitable object.', responseData);
      }
      state.originalData = Object.assign({}, state.data); // Always update originalData based on current state.data
      state.hasChanges = false;
      state.errors = {};
    } catch (error) {
      console.error('Failed to load settings:', error);
      showToast({
        title: 'Error',
        message: 'Failed to load settings',
        type: 'error'
      });
      // On error, originalData should reflect the last known good state or current state if that's preferred
      state.originalData = Object.assign({}, state.data);
    } finally {
      state.loading = false;
    }
  };

  const save = async (): Promise<boolean> => {
    if (!options.saveEndpoint || !canSave.value) return false;

    // Clear auto-save timer
    if (autoSaveTimer) {
      clearTimeout(autoSaveTimer);
      autoSaveTimer = null;
    }

    state.saveStatus = 'saving';
    
    try {
      await patch(options.saveEndpoint, state.data); // Removed unused 'response'
      
      // Update original data to reflect saved state
      state.originalData = Object.assign({}, state.data);
      state.hasChanges = false;
      state.saveStatus = 'saved';
      state.errors = {};

      if (!options.autoSave) {
        showToast({
          title: 'Success',
          message: 'Settings saved successfully',
          type: 'success'
        });
      }

      // Reset save status after delay
      setTimeout(() => {
        if (state.saveStatus === 'saved') {
          state.saveStatus = 'idle';
        }
      }, 3000);

      return true;
    } catch (error: any) {
      state.saveStatus = 'error';
      
      // Handle validation errors
      if (error.response?.data?.errors) {
        state.errors = error.response.data.errors;
      }

      showToast({
        title: 'Error',
        message: error.response?.data?.message || 'Failed to save settings',
        type: 'error'
      });

      // Reset error status after delay
      setTimeout(() => {
        if (state.saveStatus === 'error') {
          state.saveStatus = 'idle';
        }
      }, 5000);

      return false;
    }
  };

  const reset = (): void => {
    // Ensure originalData is an object before spreading
    if (typeof state.originalData === 'object' && state.originalData !== null) {
      state.data = Object.assign({}, state.originalData);
    } else {
      console.warn('Reset settings: originalData was not an object.', state.originalData);
      Object.assign(state.data, { ...initialData }); // Try Object.assign
    }
    state.hasChanges = false;
    state.errors = {};
    state.saveStatus = 'idle';

    // Clear auto-save timer
    if (autoSaveTimer) {
      clearTimeout(autoSaveTimer);
      autoSaveTimer = null;
    }

    showToast({
      title: 'Reset',
      message: 'Settings have been reset',
      type: 'info'
    });
  };

  const updateField = <K extends keyof T>(field: K, value: T[K]): void => {
    (state.data as Record<keyof T, any>)[field] = value; // More specific cast
    
    // Clear field error if it exists
    if (state.errors[field as string]) {
      delete state.errors[field as string];
    }
  };

  const setError = (field: string, message: string): void => {
    state.errors[field] = message;
  };

  const clearError = (field: string): void => {
    delete state.errors[field];
  };

  const clearAllErrors = (): void => {
    state.errors = {};
  };

  const getFieldError = (field: string): string | undefined => {
    return state.errors[field];
  };

  const hasFieldError = (field: string): boolean => {
    return !!state.errors[field];
  };

  // Cleanup
  const cleanup = (): void => {
    if (autoSaveTimer) {
      clearTimeout(autoSaveTimer);
      autoSaveTimer = null;
    }
  };

  return {
    // State
    state: readonly(state),
    data: state.data, // Consumers can use state.data directly if they need the reactive version
    originalData: readonly(state.originalData as T), // Explicit cast to T
    loading: isLoading,
    saving: isSaving,
    saveStatus: computed(() => state.saveStatus),
    hasChanges,
    canSave,
    errors: computed(() => state.errors),

    // Methods
    load,
    save,
    reset,
    updateField,
    setError,
    clearError,
    clearAllErrors,
    getFieldError,
    hasFieldError,
    cleanup
  };
};

// Utility function for creating settings with common patterns
export const createSettingsState = <T extends Record<string, any>>(
  settingsKey: string,
  initialData: T,
  options: {
    autoSave?: boolean;
    autoSaveDelay?: number;
  } = {}
) => {
  return useSettingsState(initialData, {
    loadEndpoint: `/settings/${settingsKey}`,
    saveEndpoint: `/settings/${settingsKey}`,
    ...options
  });
};
