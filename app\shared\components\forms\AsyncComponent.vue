<template>
  <component :is="props.component" v-bind="componentProps" v-on="props.events">
    <template v-for="(slot, slotName) in props.slots" :key="slotName" v-slot:[slotName]>
      {{ slot }}
    </template>
  </component>
</template>
<script setup lang="ts">
import { useFormsProps } from "~/app/shared/composables/useFormsProps";

defineOptions({
  inheritAttrs: false,
});

interface Props {
  component: any;
  props?: any;
  slots?: any;
  events?: any;
}

const props = withDefaults(defineProps<Props>(), {
  component: () => null,
  props: () => ({}),
});

const { props: componentProps } = useFormsProps(props);
</script>
