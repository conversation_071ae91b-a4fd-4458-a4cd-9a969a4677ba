<template>
  <span>{{ displayValue }}</span>
</template>

<script setup lang="ts">
interface Props {
  target: number
  duration?: number
  format?: 'number' | 'currency' | 'percentage'
  decimals?: number
  separator?: string
  prefix?: string
  suffix?: string
}

const props = withDefaults(defineProps<Props>(), {
  duration: 2000,
  format: 'number',
  decimals: 0,
  separator: ',',
  prefix: '',
  suffix: ''
})

// Reactive state
const currentValue = ref(0)
const isVisible = ref(false)

// Computed display value
const displayValue = computed(() => {
  let value = currentValue.value

  // Apply formatting
  switch (props.format) {
    case 'currency':
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
        minimumFractionDigits: props.decimals,
        maximumFractionDigits: props.decimals
      }).format(value)
    
    case 'percentage':
      return `${value.toFixed(props.decimals)}%`
    
    case 'number':
    default:
      // Format number with separators
      const formatted = value.toFixed(props.decimals)
      const parts = formatted.split('.')
      parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, props.separator)
      return `${props.prefix}${parts.join('.')}${props.suffix}`
  }
})

// Animation function
const animateValue = () => {
  const startTime = Date.now()
  const startValue = 0
  const endValue = props.target
  const duration = props.duration

  const animate = () => {
    const now = Date.now()
    const elapsed = now - startTime
    const progress = Math.min(elapsed / duration, 1)

    // Easing function (ease-out)
    const easeOut = 1 - Math.pow(1 - progress, 3)
    
    currentValue.value = startValue + (endValue - startValue) * easeOut

    if (progress < 1) {
      requestAnimationFrame(animate)
    } else {
      currentValue.value = endValue
    }
  }

  animate()
}

// Intersection Observer for triggering animation when visible
const setupIntersectionObserver = () => {
  if (typeof window === 'undefined') return

  const observer = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting && !isVisible.value) {
          isVisible.value = true
          animateValue()
          observer.unobserve(entry.target)
        }
      })
    },
    {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    }
  )

  return observer
}

// Template ref
const counterRef = ref<HTMLElement>()

// Lifecycle
onMounted(() => {
  const observer = setupIntersectionObserver()
  if (observer && counterRef.value) {
    observer.observe(counterRef.value)
  }
})

// Watch for target changes
watch(() => props.target, () => {
  if (isVisible.value) {
    animateValue()
  }
})
</script>
