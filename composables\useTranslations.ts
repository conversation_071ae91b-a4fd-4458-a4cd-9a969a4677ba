/**
 * Composable for managing translations and language preferences
 * Must be called within a Vue setup function
 */
export const useTranslations = () => {
  // Get i18n instance - this must be called within setup()
  const i18n = useI18n()
  const { locale, setLocale, t } = i18n

  const supportedLanguages = [
    {
      code: 'en',
      name: 'English',
      nativeName: 'English',
      flag: '🇺🇸',
      icon: 'i-circle-flags:us',
      rtl: false
    },
    {
      code: 'he',
      name: 'Hebrew',
      nativeName: 'עברית',
      flag: '🇮🇱',
      icon: 'i-circle-flags:il',
      rtl: true
    },
    {
      code: 'ar',
      name: 'Arabic',
      nativeName: 'العربية',
      flag: '🇸🇦',
      icon: 'i-circle-flags:sa',
      rtl: true
    }
  ]

  const currentLanguage = computed(() => 
    supportedLanguages.find(lang => lang.code === locale.value) || supportedLanguages[0]
  )

  const isRTL = computed(() => currentLanguage.value.rtl)

  /**
   * Change the application language
   */
  const changeLanguage = (languageCode: string) => {
    if (!supportedLanguages.some(lang => lang.code === languageCode)) {
      console.warn(`Language ${languageCode} is not supported`)
      return
    }

    setLocale(languageCode)
    
    if (process.client) {
      const language = supportedLanguages.find(lang => lang.code === languageCode)
      
      // Update document attributes
      document.documentElement.dir = language?.rtl ? 'rtl' : 'ltr'
      document.documentElement.lang = languageCode
      
      // Store preference
      localStorage.setItem('preferred-language', languageCode)
      
      // Emit event for other components
      window.dispatchEvent(new CustomEvent('language-changed', { 
        detail: { language: languageCode, isRTL: language?.rtl } 
      }))
    }
  }

  /**
   * Get translation with fallback
   */
  const translate = (key: string, params?: Record<string, any>, fallback?: string) => {
    try {
      const translation = t(key, params)
      return translation !== key ? translation : (fallback || key)
    } catch (error) {
      console.warn(`Translation error for key "${key}":`, error)
      return fallback || key
    }
  }

  /**
   * Get translation for a specific category
   */
  const translateCategory = (category: string, key: string, params?: Record<string, any>) => {
    return translate(`${category}.${key}`, params)
  }

  /**
   * Format time relative to now
   */
  const formatTimeAgo = (date: Date | string | number) => {
    const now = new Date()
    const targetDate = new Date(date)
    const diffInSeconds = Math.floor((now.getTime() - targetDate.getTime()) / 1000)

    if (diffInSeconds < 60) {
      return translate('time.justNow')
    } else if (diffInSeconds < 3600) {
      const minutes = Math.floor(diffInSeconds / 60)
      return translate('time.minutesAgo', { minutes })
    } else if (diffInSeconds < 86400) {
      const hours = Math.floor(diffInSeconds / 3600)
      return translate('time.hoursAgo', { hours })
    } else if (diffInSeconds < 2592000) {
      const days = Math.floor(diffInSeconds / 86400)
      return translate('time.daysAgo', { days })
    } else if (diffInSeconds < 31536000) {
      const months = Math.floor(diffInSeconds / 2592000)
      return translate('time.monthsAgo', { months })
    } else {
      const years = Math.floor(diffInSeconds / 31536000)
      return translate('time.yearsAgo', { years })
    }
  }

  /**
   * Get localized number format
   */
  const formatNumber = (number: number, options?: Intl.NumberFormatOptions) => {
    if (process.client) {
      return new Intl.NumberFormat(locale.value, options).format(number)
    }
    return number.toString()
  }

  /**
   * Get localized currency format
   */
  const formatCurrency = (amount: number, currency = 'USD') => {
    return formatNumber(amount, {
      style: 'currency',
      currency
    })
  }

  /**
   * Get localized date format
   */
  const formatDate = (date: Date | string | number, options?: Intl.DateTimeFormatOptions) => {
    if (process.client) {
      return new Intl.DateTimeFormat(locale.value, options).format(new Date(date))
    }
    return new Date(date).toLocaleDateString()
  }

  /**
   * Initialize language from stored preference
   */
  const initializeLanguage = () => {
    if (process.client) {
      const savedLanguage = localStorage.getItem('preferred-language')
      if (savedLanguage && supportedLanguages.some(lang => lang.code === savedLanguage)) {
        changeLanguage(savedLanguage)
      }
    }
  }

  /**
   * Get direction-aware CSS classes
   */
  const getDirectionClasses = () => {
    return {
      'text-left': !isRTL.value,
      'text-right': isRTL.value,
      'rtl': isRTL.value,
      'ltr': !isRTL.value
    }
  }

  return {
    // State
    locale: readonly(locale),
    currentLanguage: readonly(currentLanguage),
    supportedLanguages: readonly(ref(supportedLanguages)),
    isRTL: readonly(isRTL),

    // Methods
    changeLanguage,
    translate,
    translateCategory,
    formatTimeAgo,
    formatNumber,
    formatCurrency,
    formatDate,
    initializeLanguage,
    getDirectionClasses,

    // Aliases for convenience
    t: translate,
    $t: translate
  }
}
