<template>
  <div class="tiptap-editor" :class="editorClasses">
    <!-- Advanced Toolbar -->
    <div class="editor-toolbar" :class="toolbarClasses">
      <!-- Row 1: Main formatting tools -->
      <div class="toolbar-row">
        <!-- Undo/Redo -->
        <div class="toolbar-group">
          <button
            @click="editor?.chain().focus().undo().run()"
            :disabled="!editor?.can().undo()"
            class="toolbar-button"
            :title="t('common.editor.toolbar.undo')"
          >
            <Icon name="material-symbols:undo" />
          </button>

          <button
            @click="editor?.chain().focus().redo().run()"
            :disabled="!editor?.can().redo()"
            class="toolbar-button"
            :title="t('common.editor.toolbar.redo')"
          >
            <Icon name="material-symbols:redo" />
          </button>
        </div>

        <!-- Font Family Dropdown -->
        <div class="toolbar-group">
          <select
            @change="setFontFamily($event.target.value)"
            class="toolbar-select"
            :title="t('common.editor.toolbar.fontFamily')"
          >
            <option value="">{{ t('common.editor.toolbar.defaultFont') }}</option>
            <option value="Arial">Arial</option>
            <option value="Helvetica">Helvetica</option>
            <option value="Times New Roman">Times New Roman</option>
            <option value="Georgia">Georgia</option>
            <option value="Verdana">Verdana</option>
            <option value="Courier New">Courier New</option>
            <option value="Tahoma">Tahoma</option>
            <option value="Trebuchet MS">Trebuchet MS</option>
          </select>
        </div>

        <!-- Font Size -->
        <div class="toolbar-group">
          <select
            @change="setFontSize($event.target.value)"
            class="toolbar-select font-size-select"
            :title="t('common.editor.toolbar.fontSize')"
          >
            <option value="8px">8</option>
            <option value="9px">9</option>
            <option value="10px">10</option>
            <option value="11px">11</option>
            <option value="12px">12</option>
            <option value="14px" selected>14</option>
            <option value="16px">16</option>
            <option value="18px">18</option>
            <option value="20px">20</option>
            <option value="24px">24</option>
            <option value="28px">28</option>
            <option value="32px">32</option>
            <option value="36px">36</option>
            <option value="48px">48</option>
            <option value="72px">72</option>
          </select>
        </div>

        <!-- Text Formatting -->
        <div class="toolbar-group">
          <button
            @click="editor?.chain().focus().toggleBold().run()"
            :class="{ 'is-active': editor?.isActive('bold') }"
            class="toolbar-button"
            :title="t('common.editor.toolbar.bold')"
          >
            <Icon name="material-symbols:format-bold" />
          </button>

          <button
            @click="editor?.chain().focus().toggleItalic().run()"
            :class="{ 'is-active': editor?.isActive('italic') }"
            class="toolbar-button"
            :title="t('common.editor.toolbar.italic')"
          >
            <Icon name="material-symbols:format-italic" />
          </button>

          <button
            @click="editor?.chain().focus().toggleUnderline().run()"
            :class="{ 'is-active': editor?.isActive('underline') }"
            class="toolbar-button"
            :title="t('common.editor.toolbar.underline')"
          >
            <Icon name="material-symbols:format-underlined" />
          </button>

          <button
            @click="editor?.chain().focus().toggleStrike().run()"
            :class="{ 'is-active': editor?.isActive('strike') }"
            class="toolbar-button"
            :title="t('common.editor.toolbar.strikethrough')"
          >
            <Icon name="material-symbols:strikethrough-s" />
          </button>
        </div>

        <!-- Text Color -->
        <div class="toolbar-group">
          <div class="color-picker-wrapper">
            <button
              @click="toggleColorPicker"
              class="toolbar-button color-button"
              :title="t('common.editor.toolbar.textColor')"
            >
              <Icon name="material-symbols:format-color-text" />
              <div class="color-indicator" :style="{ backgroundColor: currentTextColor }"></div>
            </button>
            <div v-if="showColorPicker" class="color-picker-dropdown">
              <div class="color-grid">
                <button
                  v-for="color in textColors"
                  :key="color"
                  @click="setTextColor(color)"
                  class="color-swatch"
                  :style="{ backgroundColor: color }"
                  :title="color"
                ></button>
              </div>
            </div>
          </div>

          <div class="color-picker-wrapper">
            <button
              @click="toggleHighlightPicker"
              class="toolbar-button color-button"
              :title="t('common.editor.toolbar.highlight')"
            >
              <Icon name="material-symbols:format-color-fill" />
              <div class="color-indicator" :style="{ backgroundColor: currentHighlightColor }"></div>
            </button>
            <div v-if="showHighlightPicker" class="color-picker-dropdown">
              <div class="color-grid">
                <button
                  @click="setHighlight('')"
                  class="color-swatch no-color"
                  :title="t('common.editor.toolbar.noHighlight')"
                >
                  <Icon name="material-symbols:close" class="w-3 h-3" />
                </button>
                <button
                  v-for="color in highlightColors"
                  :key="color"
                  @click="setHighlight(color)"
                  class="color-swatch"
                  :style="{ backgroundColor: color }"
                  :title="color"
                ></button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Row 2: Alignment, Lists, and Styles -->
      <div class="toolbar-row">
        <!-- Text Alignment -->
        <div class="toolbar-group">
          <button
            @click="editor?.chain().focus().setTextAlign('left').run()"
            :class="{ 'is-active': editor?.isActive({ textAlign: 'left' }) }"
            class="toolbar-button"
            :title="t('common.editor.toolbar.alignLeft')"
          >
            <Icon name="material-symbols:format-align-left" />
          </button>

          <button
            @click="editor?.chain().focus().setTextAlign('center').run()"
            :class="{ 'is-active': editor?.isActive({ textAlign: 'center' }) }"
            class="toolbar-button"
            :title="t('common.editor.toolbar.alignCenter')"
          >
            <Icon name="material-symbols:format-align-center" />
          </button>

          <button
            @click="editor?.chain().focus().setTextAlign('right').run()"
            :class="{ 'is-active': editor?.isActive({ textAlign: 'right' }) }"
            class="toolbar-button"
            :title="t('common.editor.toolbar.alignRight')"
          >
            <Icon name="material-symbols:format-align-right" />
          </button>

          <button
            @click="editor?.chain().focus().setTextAlign('justify').run()"
            :class="{ 'is-active': editor?.isActive({ textAlign: 'justify' }) }"
            class="toolbar-button"
            :title="t('common.editor.toolbar.alignJustify')"
          >
            <Icon name="material-symbols:format-align-justify" />
          </button>
        </div>

        <!-- Lists -->
        <div class="toolbar-group">
          <button
            @click="editor?.chain().focus().toggleBulletList().run()"
            :class="{ 'is-active': editor?.isActive('bulletList') }"
            class="toolbar-button"
            :title="t('common.editor.toolbar.bulletList')"
          >
            <Icon name="material-symbols:format-list-bulleted" />
          </button>

          <button
            @click="editor?.chain().focus().toggleOrderedList().run()"
            :class="{ 'is-active': editor?.isActive('orderedList') }"
            class="toolbar-button"
            :title="t('common.editor.toolbar.orderedList')"
          >
            <Icon name="material-symbols:format-list-numbered" />
          </button>
        </div>

        <!-- Indentation -->
        <div class="toolbar-group">
          <button
            @click="editor?.chain().focus().outdent().run()"
            class="toolbar-button"
            :title="t('common.editor.toolbar.outdent')"
          >
            <Icon name="material-symbols:format-indent-decrease" />
          </button>

          <button
            @click="editor?.chain().focus().indent().run()"
            class="toolbar-button"
            :title="t('common.editor.toolbar.indent')"
          >
            <Icon name="material-symbols:format-indent-increase" />
          </button>
        </div>

        <!-- Styles Dropdown -->
        <div class="toolbar-group">
          <select
            @change="applyStyle($event.target.value)"
            class="toolbar-select style-select"
            :title="t('common.editor.toolbar.styles')"
          >
            <option value="">{{ t('common.editor.toolbar.normal') }}</option>
            <option value="title1">{{ t('common.editor.toolbar.title1') }}</option>
            <option value="title2">{{ t('common.editor.toolbar.title2') }}</option>
            <option value="title3">{{ t('common.editor.toolbar.title3') }}</option>
            <option value="heading1">{{ t('common.editor.toolbar.heading1') }}</option>
            <option value="heading2">{{ t('common.editor.toolbar.heading2') }}</option>
            <option value="heading3">{{ t('common.editor.toolbar.heading3') }}</option>
          </select>
        </div>

        <!-- Additional Tools -->
        <div class="toolbar-group">
          <button
            @click="editor?.chain().focus().toggleSubscript().run()"
            :class="{ 'is-active': editor?.isActive('subscript') }"
            class="toolbar-button"
            :title="t('common.editor.toolbar.subscript')"
          >
            <Icon name="material-symbols:subscript" />
          </button>

          <button
            @click="editor?.chain().focus().toggleSuperscript().run()"
            :class="{ 'is-active': editor?.isActive('superscript') }"
            class="toolbar-button"
            :title="t('common.editor.toolbar.superscript')"
          >
            <Icon name="material-symbols:superscript" />
          </button>
        </div>

        <!-- Document Actions -->
        <div class="toolbar-group toolbar-actions">
          <button
            @click="importWord"
            class="toolbar-button action-button"
            :title="t('common.editor.toolbar.importWord')"
          >
            <Icon name="material-symbols:upload-file" />
            <span class="button-text">{{ t('common.editor.toolbar.importWord') }}</span>
          </button>

          <button
            @click="exportMarkdown"
            class="toolbar-button action-button"
            :title="t('common.editor.toolbar.markdown')"
          >
            <Icon name="material-symbols:code" />
            <span class="button-text">{{ t('common.editor.toolbar.markdown') }}</span>
          </button>

          <button
            @click="searchDocument"
            class="toolbar-button action-button"
            :title="t('common.editor.toolbar.search')"
          >
            <Icon name="material-symbols:search" />
            <span class="button-text">{{ t('common.editor.toolbar.search') }}</span>
          </button>

          <button
            @click="printDocument"
            class="toolbar-button action-button"
            :title="t('common.editor.toolbar.print')"
          >
            <Icon name="material-symbols:print" />
            <span class="button-text">{{ t('common.editor.toolbar.print') }}</span>
          </button>

          <button
            @click="showSizeDemo"
            class="toolbar-button action-button"
            :title="t('common.editor.toolbar.sizeDemo')"
          >
            <Icon name="material-symbols:fullscreen" />
            <span class="button-text">{{ t('common.editor.toolbar.sizeDemo') }}</span>
          </button>
        </div>
      </div>
    </div>

    <!-- Editor Content -->
    <div class="editor-content-wrapper">
      <EditorContent :editor="editor" class="editor-content" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { Editor, EditorContent } from '@tiptap/vue-3'
import StarterKit from '@tiptap/starter-kit'
import TextAlign from '@tiptap/extension-text-align'
import {TextStyle} from '@tiptap/extension-text-style'
import Color from '@tiptap/extension-color'
import FontFamily from '@tiptap/extension-font-family'
import Highlight from '@tiptap/extension-highlight'
import Underline from '@tiptap/extension-underline'
import Subscript from '@tiptap/extension-subscript'
import Superscript from '@tiptap/extension-superscript'

interface Props {
  modelValue?: string
  placeholder?: string
  editable?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  placeholder: '',
  editable: true
})

const emit = defineEmits<{
  'update:modelValue': [value: string]
}>()

const { locale, t } = useI18n()

// RTL languages
const rtlLanguages = ['he', 'ar']
const isRTL = computed(() => rtlLanguages.includes(locale.value))

// Editor classes
const editorClasses = computed(() => ({
  'rtl': isRTL.value,
  'ltr': !isRTL.value
}))

const toolbarClasses = computed(() => ({
  'rtl': isRTL.value,
  'ltr': !isRTL.value
}))

// Initialize editor
const editor = ref<Editor | null>(null)

// Color picker states
const showColorPicker = ref(false)
const showHighlightPicker = ref(false)
const currentTextColor = ref('#000000')
const currentHighlightColor = ref('transparent')

// Color palettes
const textColors = [
  '#000000', '#333333', '#666666', '#999999', '#CCCCCC', '#FFFFFF',
  '#FF0000', '#FF6600', '#FFCC00', '#00FF00', '#0066FF', '#6600FF',
  '#FF0066', '#FF3366', '#FF6699', '#66FF00', '#0099FF', '#9900FF'
]

const highlightColors = [
  '#FFFF00', '#00FF00', '#00FFFF', '#FF00FF', '#FFB6C1', '#FFA500',
  '#98FB98', '#87CEEB', '#DDA0DD', '#F0E68C', '#FF6347', '#40E0D0'
]

// Toolbar methods
const setFontFamily = (fontFamily: string) => {
  if (editor.value) {
    if (fontFamily) {
      editor.value.chain().focus().setFontFamily(fontFamily).run()
    } else {
      editor.value.chain().focus().unsetFontFamily().run()
    }
  }
}

const setFontSize = (fontSize: string) => {
  if (editor.value) {
    // Apply font size using CSS style
    editor.value.chain().focus().setMark('textStyle', { fontSize }).run()
  }
}

const toggleColorPicker = () => {
  showColorPicker.value = !showColorPicker.value
  showHighlightPicker.value = false
}

const toggleHighlightPicker = () => {
  showHighlightPicker.value = !showHighlightPicker.value
  showColorPicker.value = false
}

const setTextColor = (color: string) => {
  if (editor.value) {
    editor.value.chain().focus().setColor(color).run()
    currentTextColor.value = color
    showColorPicker.value = false
  }
}

const setHighlight = (color: string) => {
  if (editor.value) {
    if (color) {
      editor.value.chain().focus().setHighlight({ color }).run()
      currentHighlightColor.value = color
    } else {
      editor.value.chain().focus().unsetHighlight().run()
      currentHighlightColor.value = 'transparent'
    }
    showHighlightPicker.value = false
  }
}

const applyStyle = (style: string) => {
  if (!editor.value) return

  switch (style) {
    case 'title1':
      editor.value.chain().focus().toggleHeading({ level: 1 }).run()
      break
    case 'title2':
      editor.value.chain().focus().toggleHeading({ level: 2 }).run()
      break
    case 'title3':
      editor.value.chain().focus().toggleHeading({ level: 3 }).run()
      break
    case 'heading1':
      editor.value.chain().focus().toggleHeading({ level: 1 }).run()
      break
    case 'heading2':
      editor.value.chain().focus().toggleHeading({ level: 2 }).run()
      break
    case 'heading3':
      editor.value.chain().focus().toggleHeading({ level: 3 }).run()
      break
    default:
      editor.value.chain().focus().setParagraph().run()
  }
}

const importWord = () => {
  // TODO: Implement Word import functionality
  console.log('Import Word document')
}

const exportMarkdown = () => {
  // TODO: Implement Markdown export
  console.log('Export as Markdown')
}

const searchDocument = () => {
  // TODO: Implement document search
  console.log('Search document')
}

const printDocument = () => {
  window.print()
}

const showSizeDemo = () => {
  // TODO: Implement size demo/fullscreen
  console.log('Show size demo')
}

onMounted(() => {
  editor.value = new Editor({
    content: props.modelValue,
    editable: props.editable,
    extensions: [
      StarterKit,
      TextAlign.configure({
        types: ['heading', 'paragraph'],
      }),
      TextStyle,
      Color,
      FontFamily,
      Highlight.configure({
        multicolor: true,
      }),
      Underline,
      Subscript,
      Superscript,
    ],
    onUpdate: ({ editor }) => {
      emit('update:modelValue', editor.getHTML())
    },
    editorProps: {
      attributes: {
        class: 'prose prose-sm sm:prose lg:prose-lg xl:prose-2xl mx-auto focus:outline-none',
        dir: isRTL.value ? 'rtl' : 'ltr',
      },
    },
  })
})

// Watch for locale changes to update direction
watch(locale, () => {
  if (editor.value) {
    const editorElement = editor.value.view.dom
    editorElement.setAttribute('dir', isRTL.value ? 'rtl' : 'ltr')
  }
})

// Watch for content changes
watch(() => props.modelValue, (newValue) => {
  if (editor.value && editor.value.getHTML() !== newValue) {
    editor.value.commands.setContent(newValue)
  }
})

// Watch for editable changes
watch(() => props.editable, (newValue) => {
  if (editor.value) {
    editor.value.setEditable(newValue)
  }
})

// Close color pickers when clicking outside
const handleClickOutside = (event: Event) => {
  const target = event.target as HTMLElement
  if (!target.closest('.color-picker-wrapper')) {
    showColorPicker.value = false
    showHighlightPicker.value = false
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onBeforeUnmount(() => {
  if (editor.value) {
    editor.value.destroy()
  }
  document.removeEventListener('click', handleClickOutside)
})
</script>

<style scoped>
@reference '~/assets/css/tailwind.css';

.tiptap-editor {
  @apply border border-gray-300 dark:border-gray-600 rounded-lg overflow-hidden bg-white dark:bg-gray-800;
}

.editor-toolbar {
  @apply flex flex-col gap-2 p-3 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900;
}

.toolbar-row {
  @apply flex flex-wrap items-center gap-2;
}

.toolbar-group {
  @apply flex items-center gap-1 ltr:mr-4 rtl:ml-4 border-r border-gray-300 dark:border-gray-600 ltr:pr-4 rtl:pl-4;
}

.toolbar-group:last-child {
  @apply border-r-0 ltr:pr-0 rtl:pl-0;
}

.toolbar-button {
  @apply flex items-center justify-center w-8 h-8 rounded hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors duration-200 text-gray-600 dark:text-gray-400 relative;
}

.toolbar-button:disabled {
  @apply opacity-50 cursor-not-allowed;
}

.toolbar-button.is-active {
  @apply bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400;
}

.toolbar-select {
  @apply px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500;
}

.font-size-select {
  @apply w-16;
}

.style-select {
  @apply w-24;
}

.action-button {
  @apply w-auto px-2 gap-1;
}

.button-text {
  @apply text-xs hidden sm:inline;
}

.color-button {
  @apply relative;
}

.color-indicator {
  @apply absolute bottom-0 left-0 right-0 h-1 rounded-b;
}

.color-picker-wrapper {
  @apply relative;
}

.color-picker-dropdown {
  @apply absolute top-full left-0 mt-1 p-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg z-50;
}

.color-grid {
  @apply grid grid-cols-6 gap-1;
}

.color-swatch {
  @apply w-6 h-6 rounded border border-gray-300 dark:border-gray-600 hover:scale-110 transition-transform cursor-pointer;
}

.no-color {
  @apply bg-white dark:bg-gray-800 flex items-center justify-center;
}

.toolbar-actions {
  @apply ltr:ml-auto rtl:mr-auto;
}

.editor-content-wrapper {
  @apply p-4;
}

.editor-content {
  @apply min-h-[300px];
}

/* RTL Support */
.rtl .toolbar-group {
  @apply ltr:mr-0 rtl:ml-0 ltr:ml-4 rtl:mr-4;
}

.rtl .editor-content :deep(.ProseMirror) {
  direction: rtl;
  text-align: right;
}

.ltr .editor-content :deep(.ProseMirror) {
  direction: ltr;
  text-align: left;
}

/* Editor Content Styling */
.editor-content :deep(.ProseMirror) {
  @apply outline-none;
}

.editor-content :deep(.ProseMirror p.is-editor-empty:first-child::before) {
  content: attr(data-placeholder);
  @apply text-gray-400 pointer-events-none float-left h-0;
}

.editor-content :deep(.ProseMirror h1) {
  @apply text-2xl font-bold mb-4;
}

.editor-content :deep(.ProseMirror h2) {
  @apply text-xl font-bold mb-3;
}

.editor-content :deep(.ProseMirror h3) {
  @apply text-lg font-bold mb-2;
}

.editor-content :deep(.ProseMirror ul) {
  @apply list-disc ltr:ml-6 rtl:mr-6 mb-4;
}

.editor-content :deep(.ProseMirror ol) {
  @apply list-decimal ltr:ml-6 rtl:mr-6 mb-4;
}

.editor-content :deep(.ProseMirror li) {
  @apply mb-1;
}
</style>
