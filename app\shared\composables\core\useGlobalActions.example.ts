/**
 * Global Actions Composable - Usage Examples
 * 
 * This file demonstrates how to use the useGlobalActions composable
 * for various scenarios in the Legal SaaS application
 */

import { useGlobalActions } from './useGlobalActions'
import type { GlobalActionItem } from './useGlobalActions'

// ============================================================================
// EXAMPLE 1: BASIC USAGE WITH SIMPLE ITEMS
// ============================================================================

interface SimpleItem extends GlobalActionItem {
  id: string
  name: string
  description?: string
}

export function useSimpleItemsExample() {
  const { items, count, isEmpty, actions } = useGlobalActions<SimpleItem>({
    key: 'simple-items',
    initialItems: [
      { id: '1', name: 'Item 1', description: 'First item' },
      { id: '2', name: 'Item 2', description: 'Second item' }
    ],
    maxItems: 100,
    allowDuplicates: false,
    validateItem: (item) => {
      if (!item.name || item.name.trim().length === 0) {
        return 'Name is required'
      }
      return true
    },
    onAdd: async (item, allItems) => {
      console.log('Item added:', item)
      console.log('Total items:', allItems.length)
    },
    onRemove: async (item, allItems) => {
      console.log('Item removed:', item)
      console.log('Remaining items:', allItems.length)
    }
  })

  return {
    items,
    count,
    isEmpty,
    actions
  }
}

// ============================================================================
// EXAMPLE 2: TENANT MANAGEMENT
// ============================================================================

interface TenantItem extends GlobalActionItem {
  id: string
  name: string
  slug: string
  plan: 'basic' | 'pro' | 'enterprise'
  status: 'active' | 'inactive' | 'suspended'
  createdAt: Date
}

export function useTenantActionsExample() {
  const { items, count, actions, isLoading, error } = useGlobalActions<TenantItem>({
    key: 'tenant-actions',
    maxItems: 1000,
    allowDuplicates: false,
    validateItem: (tenant) => {
      if (!tenant.name || tenant.name.trim().length === 0) {
        return 'Tenant name is required'
      }
      if (!tenant.slug || tenant.slug.trim().length === 0) {
        return 'Tenant slug is required'
      }
      if (!['basic', 'pro', 'enterprise'].includes(tenant.plan)) {
        return 'Invalid plan type'
      }
      return true
    },
    onAdd: async (tenant) => {
      // Could trigger API call to create tenant
      console.log('Creating tenant:', tenant.name)
    },
    onRemove: async (tenant) => {
      // Could trigger API call to delete tenant
      console.log('Removing tenant:', tenant.name)
    },
    onUpdate: async (tenant) => {
      // Could trigger API call to update tenant
      console.log('Updating tenant:', tenant.name)
    }
  })

  // Helper methods specific to tenants
  const addTenant = async (tenantData: Omit<TenantItem, 'id' | 'createdAt'>) => {
    const newTenant: TenantItem = {
      ...tenantData,
      id: `tenant-${Date.now()}`,
      createdAt: new Date()
    }
    return await actions.add(newTenant)
  }

  const activateTenant = async (tenantId: string) => {
    return await actions.update(tenantId, { status: 'active' })
  }

  const suspendTenant = async (tenantId: string) => {
    return await actions.update(tenantId, { status: 'suspended' })
  }

  const getActiveTenants = () => {
    return items.value.filter(tenant => tenant.status === 'active')
  }

  const getTenantsByPlan = (plan: TenantItem['plan']) => {
    return items.value.filter(tenant => tenant.plan === plan)
  }

  return {
    // Core state and actions
    tenants: items,
    tenantCount: count,
    isLoading,
    error,
    actions,
    
    // Tenant-specific methods
    addTenant,
    activateTenant,
    suspendTenant,
    getActiveTenants,
    getTenantsByPlan
  }
}

// ============================================================================
// EXAMPLE 3: DOCUMENT MANAGEMENT
// ============================================================================

interface DocumentItem extends GlobalActionItem {
  id: string
  name: string
  type: 'contract' | 'template' | 'form' | 'other'
  size: number
  mimeType: string
  uploadedAt: Date
  tags: string[]
}

export function useDocumentActionsExample() {
  const { items, actions } = useGlobalActions<DocumentItem>({
    key: 'document-actions',
    maxItems: 5000,
    allowDuplicates: false,
    validateItem: (doc) => {
      if (!doc.name || doc.name.trim().length === 0) {
        return 'Document name is required'
      }
      if (doc.size <= 0) {
        return 'Document size must be greater than 0'
      }
      if (doc.size > 50 * 1024 * 1024) { // 50MB limit
        return 'Document size cannot exceed 50MB'
      }
      return true
    }
  })

  // Document-specific helper methods
  const addDocument = async (file: File, type: DocumentItem['type'], tags: string[] = []) => {
    const document: DocumentItem = {
      id: `doc-${Date.now()}`,
      name: file.name,
      type,
      size: file.size,
      mimeType: file.type,
      uploadedAt: new Date(),
      tags
    }
    return await actions.add(document)
  }

  const addTagToDocument = async (docId: string, tag: string) => {
    const doc = actions.find(docId)
    if (!doc) return false
    
    const updatedTags = [...new Set([...doc.tags, tag])]
    return await actions.update(docId, { tags: updatedTags })
  }

  const removeTagFromDocument = async (docId: string, tag: string) => {
    const doc = actions.find(docId)
    if (!doc) return false
    
    const updatedTags = doc.tags.filter(t => t !== tag)
    return await actions.update(docId, { tags: updatedTags })
  }

  const getDocumentsByType = (type: DocumentItem['type']) => {
    return items.value.filter(doc => doc.type === type)
  }

  const getDocumentsByTag = (tag: string) => {
    return items.value.filter(doc => doc.tags.includes(tag))
  }

  const getTotalSize = () => {
    return items.value.reduce((total, doc) => total + doc.size, 0)
  }

  return {
    documents: items,
    actions,
    addDocument,
    addTagToDocument,
    removeTagFromDocument,
    getDocumentsByType,
    getDocumentsByTag,
    getTotalSize
  }
}

// ============================================================================
// EXAMPLE 4: NOTIFICATION MANAGEMENT
// ============================================================================

interface NotificationItem extends GlobalActionItem {
  id: string
  title: string
  message: string
  type: 'info' | 'success' | 'warning' | 'error'
  read: boolean
  createdAt: Date
  expiresAt?: Date
}

export function useNotificationActionsExample() {
  const { items, count, actions } = useGlobalActions<NotificationItem>({
    key: 'notifications',
    maxItems: 200,
    allowDuplicates: true, // Allow duplicate notifications
    onAdd: async (notification) => {
      // Auto-remove expired notifications
      setTimeout(() => {
        if (notification.expiresAt && new Date() > notification.expiresAt) {
          actions.remove(notification.id)
        }
      }, 1000)
    }
  })

  const addNotification = async (
    title: string,
    message: string,
    type: NotificationItem['type'] = 'info',
    expiresIn?: number // milliseconds
  ) => {
    const notification: NotificationItem = {
      id: `notif-${Date.now()}-${Math.random()}`,
      title,
      message,
      type,
      read: false,
      createdAt: new Date(),
      ...(expiresIn && { expiresAt: new Date(Date.now() + expiresIn) })
    }
    return await actions.add(notification)
  }

  const markAsRead = async (notificationId: string) => {
    return await actions.update(notificationId, { read: true })
  }

  const markAllAsRead = async () => {
    const unreadNotifications = items.value
      .filter(n => !n.read)
      .map(n => ({ id: n.id, data: { read: true } }))
    
    return await actions.updateMany(unreadNotifications)
  }

  const clearExpired = async () => {
    const now = new Date()
    const expiredIds = items.value
      .filter(n => n.expiresAt && now > n.expiresAt)
      .map(n => n.id)
    
    return await actions.removeMany(expiredIds)
  }

  const getUnreadCount = () => {
    return items.value.filter(n => !n.read).length
  }

  const getNotificationsByType = (type: NotificationItem['type']) => {
    return items.value.filter(n => n.type === type)
  }

  return {
    notifications: items,
    notificationCount: count,
    actions,
    addNotification,
    markAsRead,
    markAllAsRead,
    clearExpired,
    getUnreadCount,
    getNotificationsByType
  }
}
