<template>
  <div class="space-y-6">
    <div>
      <h2 class="text-md font-semibold text-gray-800 dark:text-gray-100 mb-4">Security & Authentication Settings</h2>
    </div>

    <!-- Password Policy Section -->
    <UiCard title="Password Policy">
      <div class="space-y-4">
        <div>
          <label for="minPasswordLength" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Minimum Password Length</label>
          <UiInput id="minPasswordLength" :name="'minPasswordLength'" v-model="passwordPolicy.minLength" type="number" placeholder="e.g., 12" class="mt-1" />
        </div>
        <UiToggle v-model="passwordPolicy.requireUppercase" label="Require Uppercase Letter" />
        <UiToggle v-model="passwordPolicy.requireLowercase" label="Require Lowercase Letter" />
        <UiToggle v-model="passwordPolicy.requireNumber" label="Require Number" />
        <UiToggle v-model="passwordPolicy.requireSpecialChar" label="Require Special Character" />
        <div>
          <label for="passwordExpiration" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Password Expiration (days)</label>
          <UiInput id="passwordExpiration" :name="'passwordExpiration'" v-model="passwordPolicy.expirationDays" type="number" placeholder="e.g., 90 (0 for never)" class="mt-1" />
        </div>
      </div>
    </UiCard>

    <!-- Two-Factor Authentication (2FA) Section -->
    <UiCard title="Two-Factor Authentication (2FA)">
      <div class="space-y-4">
        <UiToggle v-model="twoFactorAuth.enforceForAll" label="Enforce 2FA for All Users" />
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Allowed 2FA Methods</label>
          <div class="mt-2 space-y-2">
            <UiCheckbox v-model="twoFactorAuth.allowedMethods.authenticatorApp" label="Authenticator App" id="2fa-authenticator" />
            <UiCheckbox v-model="twoFactorAuth.allowedMethods.sms" label="SMS" id="2fa-sms" />
            <UiCheckbox v-model="twoFactorAuth.allowedMethods.email" label="Email" id="2fa-email" />
          </div>
        </div>
      </div>
    </UiCard>

    <!-- Session Management Section -->
    <UiCard title="Session Management">
      <div class="space-y-4">
        <div>
          <label for="sessionTimeout" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Session Timeout (minutes)</label>
          <UiInput id="sessionTimeout" :name="'sessionTimeout'" v-model="sessionManagement.timeoutMinutes" type="number" placeholder="e.g., 30" class="mt-1" />
        </div>
        <UiToggle v-model="sessionManagement.allowRememberMe" label="Allow 'Remember Me' on Login" />
      </div>
    </UiCard>

  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue';
import UiInput from '~/components/ui/UiInput.vue';
import UiToggle from '~/components/ui/UiToggle.vue';
import UiCard from '~/components/ui/UiCard.vue';
import UiCheckbox from '~/components/ui/UiCheckbox.vue';

// Password Policy
const passwordPolicy = reactive({
  minLength: 12,
  requireUppercase: true,
  requireLowercase: true,
  requireNumber: true,
  requireSpecialChar: true,
  expirationDays: 90,
});

// Two-Factor Authentication
const twoFactorAuth = reactive({
  enforceForAll: false,
  allowedMethods: {
    authenticatorApp: true,
    sms: false,
    email: false,
  },
});

// Session Management
const sessionManagement = reactive({
  timeoutMinutes: 30,
  allowRememberMe: true,
});

// TODO: Load initial values from backend and implement save logic for all settings
</script>
