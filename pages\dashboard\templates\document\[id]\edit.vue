<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <!-- Loading State -->
    <div v-if="isLoading" class="flex items-center justify-center min-h-screen">
      <div class="text-center">
        <UiSpinner size="lg" />
        <p class="mt-4 text-gray-600 dark:text-gray-400">Loading template...</p>
      </div>
    </div>

    <!-- Template Not Found -->
    <div v-else-if="!template" class="flex items-center justify-center min-h-screen">
      <div class="text-center">
        <Icon name="heroicons:document-x-mark" class="w-16 h-16 text-gray-400 mx-auto mb-4" />
        <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">Template Not Found</h2>
        <p class="text-gray-600 dark:text-gray-400 mb-6">The template you're trying to edit doesn't exist or has been deleted.</p>
        <UiButton @click="$router.push('/dashboard/templates/document')">
          <Icon name="heroicons:arrow-left" class="w-4 h-4 mr-2" />
          Back to Templates
        </UiButton>
      </div>
    </div>

    <!-- Edit Form -->
    <div v-else>
      <!-- Header -->
      <div class="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="py-6">
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-4">
                <div class="flex items-center justify-center w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-xl">
                  <Icon name="heroicons:pencil" class="w-6 h-6 text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                  <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Edit Document Template</h1>
                  <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                    Modify your document template content and settings
                  </p>
                </div>
              </div>
              
              <div class="flex items-center space-x-3">
                <UiButton @click="previewTemplate" variant="outline" :disabled="!canPreview">
                  <Icon name="heroicons:eye" class="w-4 h-4 mr-2" />
                  Preview
                </UiButton>
                <UiButton @click="viewTemplate" variant="outline">
                  <Icon name="heroicons:arrow-top-right-on-square" class="w-4 h-4 mr-2" />
                  View
                </UiButton>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Main Content -->
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
          <!-- Main Form -->
          <div class="lg:col-span-3 space-y-6">
            <form @submit.prevent="handleSubmit">
              <!-- Template Information Card -->
              <UiCard class="p-6">
                <div class="flex items-center justify-between mb-6">
                  <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                      <Icon name="heroicons:information-circle" class="w-5 h-5 text-blue-600 dark:text-blue-400" />
                    </div>
                    <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Template Information</h2>
                  </div>
                  
                  <div class="flex items-center space-x-2">
                    <span
                      :class="[
                        'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                        formData.isDraft 
                          ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                          : 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                      ]"
                    >
                      <Icon 
                        :name="formData.isDraft ? 'heroicons:pencil' : 'heroicons:check-circle'" 
                        class="w-3 h-3 mr-1" 
                      />
                      {{ formData.isDraft ? 'Draft' : 'Published' }}
                    </span>
                  </div>
                </div>

                <div class="space-y-6">
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="space-y-2">
                      <UiInput
                        id="templateName"
                        v-model="formData.name"
                        label="Template Name"
                        placeholder="e.g., Service Agreement Template"
                        required
                        :error="errors.name"
                      />
                      <p class="text-xs text-gray-500 dark:text-gray-400">
                        Choose a descriptive name for easy identification
                      </p>
                    </div>

                    <div class="space-y-2">
                      <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Document Category
                      </label>
                      <div class="relative">
                        <select
                          v-model="formData.category"
                          class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                          required
                        >
                          <option value="">Select a category</option>
                          <option
                            v-for="category in documentCategories"
                            :key="category.id"
                            :value="category.id"
                          >
                            {{ category.name }}
                          </option>
                        </select>
                        <Icon name="heroicons:chevron-down" class="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" />
                      </div>
                    </div>
                  </div>

                  <div class="space-y-2">
                    <UiTextarea
                      id="templateDescription"
                      v-model="formData.description"
                      label="Description"
                      placeholder="Describe the purpose and usage of this template..."
                      :rows="3"
                    />
                  </div>

                  <!-- Category Details -->
                  <div v-if="selectedCategory" class="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                    <div class="flex items-start space-x-3">
                      <Icon :name="selectedCategory.icon" class="w-5 h-5 text-blue-600 dark:text-blue-400 mt-0.5" />
                      <div>
                        <h3 class="text-sm font-medium text-blue-900 dark:text-blue-100">{{ selectedCategory.name }}</h3>
                        <p class="text-sm text-blue-700 dark:text-blue-300 mt-1">{{ selectedCategory.description }}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </UiCard>

              <!-- Document Content Editor -->
              <UiCard class="p-6">
                <div class="flex items-center justify-between mb-6">
                  <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center">
                      <Icon name="heroicons:document-text" class="w-5 h-5 text-green-600 dark:text-green-400" />
                    </div>
                    <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Document Content</h2>
                  </div>
                  
                  <div class="flex items-center space-x-2">
                    <UiButton @click="insertVariable" variant="outline" size="sm">
                      <Icon name="heroicons:plus" class="w-4 h-4 mr-1" />
                      Variable
                    </UiButton>
                    <UiButton @click="showVersionHistory = true" variant="outline" size="sm">
                      <Icon name="heroicons:clock" class="w-4 h-4 mr-1" />
                      History
                    </UiButton>
                  </div>
                </div>

                <DocumentTemplateEditor
                  v-model:content="formData.content"
                  v-model:variables="formData.variables"
                  :template-data="formData"
                  :category="selectedCategory"
                  @content-change="handleContentChange"
                />
              </UiCard>

              <!-- Form Actions -->
              <div class="flex items-center justify-between">
                <UiButton @click="cancelEdit" variant="outline" type="button">
                  <Icon name="heroicons:x-mark" class="w-4 h-4 mr-2" />
                  Cancel
                </UiButton>
                
                <div class="flex items-center space-x-3">
                  <UiButton @click="saveDraft" variant="outline" type="button" :disabled="isSaving">
                    <Icon name="heroicons:document-duplicate" class="w-4 h-4 mr-2" />
                    Save as Draft
                  </UiButton>
                  <UiButton @click="publishTemplate" type="button" :disabled="isSaving || !isFormValid" class="bg-green-600 hover:bg-green-700">
                    <Icon name="heroicons:check" class="w-4 h-4 mr-2" />
                    {{ formData.isDraft ? 'Publish' : 'Update' }} Template
                  </UiButton>
                </div>
              </div>
            </form>
          </div>

          <!-- Sidebar -->
          <div class="lg:col-span-1 space-y-6">
            <!-- Template Stats -->
            <UiCard class="p-4">
              <h3 class="text-sm font-medium text-gray-900 dark:text-white mb-3">Template Stats</h3>
              <div class="space-y-3">
                <div class="flex items-center justify-between text-sm">
                  <span class="text-gray-600 dark:text-gray-400">Word Count</span>
                  <span class="font-medium text-gray-900 dark:text-white">{{ wordCount }}</span>
                </div>
                <div class="flex items-center justify-between text-sm">
                  <span class="text-gray-600 dark:text-gray-400">Variables</span>
                  <span class="font-medium text-gray-900 dark:text-white">{{ formData.variables.length }}</span>
                </div>
                <div class="flex items-center justify-between text-sm">
                  <span class="text-gray-600 dark:text-gray-400">Usage Count</span>
                  <span class="font-medium text-gray-900 dark:text-white">{{ template.usageCount || 0 }}</span>
                </div>
                <div class="flex items-center justify-between text-sm">
                  <span class="text-gray-600 dark:text-gray-400">Last Updated</span>
                  <span class="font-medium text-gray-900 dark:text-white">{{ formatDate(template.updatedAt) }}</span>
                </div>
              </div>
            </UiCard>

            <!-- Variables Panel -->
            <UiCard class="p-4" v-if="formData.variables.length > 0">
              <h3 class="text-sm font-medium text-gray-900 dark:text-white mb-3">Template Variables</h3>
              <div class="space-y-2">
                <div
                  v-for="variable in formData.variables"
                  :key="variable"
                  class="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-700 rounded-lg"
                >
                  <span class="text-sm font-mono text-gray-700 dark:text-gray-300">{{ variable }}</span>
                  <UiButton @click="removeVariable(variable)" variant="ghost" size="sm">
                    <Icon name="heroicons:x-mark" class="w-3 h-3" />
                  </UiButton>
                </div>
              </div>
            </UiCard>

            <!-- Unsaved Changes Warning -->
            <UiCard class="p-4" v-if="hasUnsavedChanges">
              <div class="flex items-start space-x-3">
                <Icon name="heroicons:exclamation-triangle" class="w-5 h-5 text-amber-500 mt-0.5" />
                <div>
                  <h3 class="text-sm font-medium text-gray-900 dark:text-white">Unsaved Changes</h3>
                  <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                    You have unsaved changes. Don't forget to save your work.
                  </p>
                </div>
              </div>
            </UiCard>
          </div>
        </div>
      </div>
    </div>

    <!-- Version History Modal -->
    <VersionHistoryModal
      v-if="showVersionHistory"
      :template-id="templateId"
      @close="showVersionHistory = false"
      @restore="restoreVersion"
    />

    <!-- Variable Insert Modal -->
    <VariableInsertModal
      v-if="showVariableModal"
      @close="showVariableModal = false"
      @insert="handleVariableInsert"
    />

    <!-- Unsaved Changes Modal -->
    <UiModal v-if="showUnsavedModal" @close="showUnsavedModal = false">
      <div class="p-6">
        <div class="flex items-center space-x-3 mb-4">
          <div class="w-10 h-10 bg-amber-100 dark:bg-amber-900 rounded-full flex items-center justify-center">
            <Icon name="heroicons:exclamation-triangle" class="w-5 h-5 text-amber-600 dark:text-amber-400" />
          </div>
          <div>
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Unsaved Changes</h3>
            <p class="text-sm text-gray-600 dark:text-gray-400">You have unsaved changes that will be lost.</p>
          </div>
        </div>
        
        <p class="text-gray-700 dark:text-gray-300 mb-6">
          Do you want to save your changes before leaving?
        </p>
        
        <div class="flex items-center justify-end space-x-3">
          <UiButton @click="discardChanges" variant="outline">Discard Changes</UiButton>
          <UiButton @click="saveAndLeave" class="bg-blue-600 hover:bg-blue-700">Save & Leave</UiButton>
        </div>
      </div>
    </UiModal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive, watch, onMounted, onBeforeUnmount, defineAsyncComponent } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useTemplateStore } from '~/stores/template'
import { getTemplateCategoriesByType, getTemplateCategoryById } from '~/utils/templateCategories'
import { PlatformRoles, TenantRoles } from '~/app/features/auth/constants/roles'

// Lazy load components
const DocumentTemplateEditor = defineAsyncComponent(() => import('~/components/template-editors/DocumentTemplateEditor.vue'))
const VersionHistoryModal = defineAsyncComponent(() => import('~/components/template-modals/VersionHistoryModal.vue'))
const VariableInsertModal = defineAsyncComponent(() => import('~/components/template-modals/VariableInsertModal.vue'))

// Page meta
definePageMeta({
  layout: 'dashboard',
  title: 'Edit Document Template',
  middleware: ['rbac'],
  roles: [PlatformRoles.SUPER_ADMIN, TenantRoles.TENANT_OWNER, TenantRoles.ADMIN, TenantRoles.LAWYER],
  breadcrumb: [
    { label: 'Dashboard', href: '/dashboard' },
    { label: 'Templates', href: '/dashboard/templates' },
    { label: 'Document Templates', href: '/dashboard/templates/document' },
    { label: 'Edit' },
  ],
})

// Composables
const route = useRoute()
const router = useRouter()
const templateStore = useTemplateStore()

// State
const isLoading = ref(true)
const isSaving = ref(false)
const showVersionHistory = ref(false)
const showVariableModal = ref(false)
const showUnsavedModal = ref(false)
const hasUnsavedChanges = ref(false)
const pendingNavigation = ref<string | null>(null)
const template = ref(null)
const originalData = ref(null)
const errors = ref<Record<string, string>>({})

const formData = reactive({
  name: '',
  description: '',
  category: '',
  content: '',
  variables: [] as string[],
  type: 'document',
  isDraft: false,
  metadata: {},
})

// Computed
const templateId = computed(() => route.params.id as string)

const documentCategories = computed(() => getTemplateCategoriesByType('document'))

const selectedCategory = computed(() =>
  formData.category ? getTemplateCategoryById(formData.category) : null
)

const wordCount = computed(() => {
  const text = formData.content.replace(/<[^>]*>/g, '').trim()
  return text ? text.split(/\s+/).length : 0
})

const isFormValid = computed(() =>
  formData.name.trim() &&
  formData.category &&
  formData.content.trim() &&
  !Object.keys(errors.value).length
)

const canPreview = computed(() =>
  formData.content.trim() && formData.name.trim()
)

// Watchers
watch(formData, () => {
  if (originalData.value) {
    hasUnsavedChanges.value = JSON.stringify(formData) !== JSON.stringify(originalData.value)
  }
}, { deep: true })

watch(() => formData.name, (newName) => {
  if (newName.trim() && errors.value.name) {
    delete errors.value.name
  }
})

// Methods
const loadTemplate = async () => {
  try {
    isLoading.value = true
    template.value = await templateStore.getTemplate(templateId.value)

    if (template.value) {
      // Populate form data
      Object.assign(formData, {
        name: template.value.name,
        description: template.value.description || '',
        category: template.value.category,
        content: template.value.content,
        variables: template.value.variables || [],
        type: template.value.type,
        isDraft: template.value.isDraft,
        metadata: template.value.metadata || {},
      })

      // Store original data for comparison
      originalData.value = JSON.parse(JSON.stringify(formData))
    }
  } catch (error) {
    console.error('Error loading template:', error)
    template.value = null
  } finally {
    isLoading.value = false
  }
}

const validateForm = () => {
  errors.value = {}

  if (!formData.name.trim()) {
    errors.value.name = 'Template name is required'
  } else if (formData.name.length < 3) {
    errors.value.name = 'Template name must be at least 3 characters'
  }

  if (!formData.category) {
    errors.value.category = 'Please select a category'
  }

  if (!formData.content.trim()) {
    errors.value.content = 'Template content is required'
  }

  return Object.keys(errors.value).length === 0
}

const handleSubmit = async () => {
  if (!validateForm()) return

  isSaving.value = true
  try {
    await templateStore.updateTemplate(templateId.value, {
      ...formData,
      isDraft: false,
    })

    hasUnsavedChanges.value = false
    router.push(`/dashboard/templates/document/${templateId.value}`)
  } catch (error) {
    console.error('Error updating template:', error)
  } finally {
    isSaving.value = false
  }
}

const saveDraft = async () => {
  if (!formData.name.trim()) {
    errors.value.name = 'Template name is required to save draft'
    return
  }

  isSaving.value = true
  try {
    await templateStore.updateTemplate(templateId.value, {
      ...formData,
      isDraft: true,
    })

    hasUnsavedChanges.value = false
    // Show success notification
  } catch (error) {
    console.error('Error saving draft:', error)
  } finally {
    isSaving.value = false
  }
}

const publishTemplate = async () => {
  if (!validateForm()) return

  isSaving.value = true
  try {
    await templateStore.updateTemplate(templateId.value, {
      ...formData,
      isDraft: false,
    })

    hasUnsavedChanges.value = false
    router.push(`/dashboard/templates/document/${templateId.value}`)
  } catch (error) {
    console.error('Error publishing template:', error)
  } finally {
    isSaving.value = false
  }
}

const cancelEdit = () => {
  if (hasUnsavedChanges.value) {
    pendingNavigation.value = `/dashboard/templates/document/${templateId.value}`
    showUnsavedModal.value = true
  } else {
    router.push(`/dashboard/templates/document/${templateId.value}`)
  }
}

const viewTemplate = () => {
  if (hasUnsavedChanges.value) {
    pendingNavigation.value = `/dashboard/templates/document/${templateId.value}`
    showUnsavedModal.value = true
  } else {
    router.push(`/dashboard/templates/document/${templateId.value}`)
  }
}

const previewTemplate = () => {
  if (!canPreview.value) return

  const previewData = {
    name: formData.name,
    content: formData.content,
    variables: formData.variables,
    category: selectedCategory.value
  }

  sessionStorage.setItem('templatePreview', JSON.stringify(previewData))
  window.open('/dashboard/templates/preview', '_blank')
}

const insertVariable = () => {
  showVariableModal.value = true
}

const handleVariableInsert = (variable: string) => {
  if (!formData.variables.includes(variable)) {
    formData.variables.push(variable)
  }
  showVariableModal.value = false
}

const removeVariable = (variable: string) => {
  const index = formData.variables.indexOf(variable)
  if (index > -1) {
    formData.variables.splice(index, 1)
  }
}

const handleContentChange = (content: string, variables: string[]) => {
  formData.content = content
  formData.variables = variables
}

const restoreVersion = (version: any) => {
  Object.assign(formData, version)
  showVersionHistory.value = false
}

const saveAndLeave = async () => {
  await saveDraft()
  if (pendingNavigation.value) {
    router.push(pendingNavigation.value)
  }
  showUnsavedModal.value = false
}

const discardChanges = () => {
  hasUnsavedChanges.value = false
  if (pendingNavigation.value) {
    router.push(pendingNavigation.value)
  }
  showUnsavedModal.value = false
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  })
}

// Navigation guard
const handleBeforeUnload = (event: BeforeUnloadEvent) => {
  if (hasUnsavedChanges.value) {
    event.preventDefault()
    event.returnValue = ''
  }
}

// Lifecycle
onMounted(() => {
  loadTemplate()
  window.addEventListener('beforeunload', handleBeforeUnload)
})

onBeforeUnmount(() => {
  window.removeEventListener('beforeunload', handleBeforeUnload)
})
</script>
