<template>
  <div class="space-y-6">
    <!-- Header Section -->
    <div class="text-center">
      <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 dark:bg-blue-900/20">
        <Icon name="material-symbols:mail-outline" class="h-6 w-6 text-blue-600 dark:text-blue-400" />
      </div>
      <h2 class="mt-6 text-3xl font-bold tracking-tight text-gray-900 dark:text-white">
        {{ $t("Check Your Invitation") }}
      </h2>
      <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">
        {{ $t("Enter your invitation details to get started") }}
      </p>
    </div>

    <!-- Invitation Form -->
    <div class="space-y-4">
      <div>
        <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          {{ $t("Email Address") }}
        </label>
        <UiInput
          id="email"
          v-model="email"
          type="email"
          :placeholder="$t('Enter your email address')"
          leading-icon="material-symbols:mail"
          :disabled="isLoading"
          required
        />
      </div>

      <div>
        <label for="inviteCode" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          {{ $t("Invitation Code") }} <span class="text-gray-400">({{ $t("Optional") }})</span>
        </label>
        <UiInput
          id="inviteCode"
          v-model="inviteCode"
          type="text"
          :placeholder="$t('Enter invitation code if provided')"
          leading-icon="material-symbols:key"
          :disabled="isLoading"
        />
      </div>

      <!-- Submit Button -->
      <div class="pt-4">
        <UiButton
          @click="checkInvitation"
          :loading="isLoading"
          :disabled="!email"
          variant="contained"
          color="primary"
          size="lg"
          class="w-full"
        >
          <Icon name="material-symbols:search" class="h-5 w-5 mr-2" />
          {{ $t("Check Invitation") }}
        </UiButton>
      </div>
    </div>

    <!-- Alternative Actions -->
    <div class="text-center space-y-4">
      <div class="relative">
        <div class="absolute inset-0 flex items-center">
          <div class="w-full border-t border-gray-300 dark:border-gray-600" />
        </div>
        <div class="relative flex justify-center text-sm">
          <span class="px-2 bg-gray-50 dark:bg-gray-900 text-gray-500 dark:text-gray-400">
            {{ $t("Or") }}
          </span>
        </div>
      </div>

      <div class="space-y-2">
        <UiButton
          @click="$router.push('/auth/login')"
          variant="outline"
          size="md"
          class="w-full"
        >
          <Icon name="material-symbols:login" class="h-4 w-4 mr-2" />
          {{ $t("Sign In to Existing Account") }}
        </UiButton>

        <UiButton
          @click="requestInvitation"
          variant="ghost"
          size="md"
          class="w-full"
        >
          <Icon name="material-symbols:contact-support" class="h-4 w-4 mr-2" />
          {{ $t("Request an Invitation") }}
        </UiButton>
      </div>
    </div>

    <!-- Information Section -->
    <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-800">
      <div class="flex items-start space-x-3">
        <Icon name="material-symbols:info" class="h-5 w-5 text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0" />
        <div class="text-sm">
          <h4 class="font-medium text-blue-900 dark:text-blue-100 mb-1">
            {{ $t("About Invitations") }}
          </h4>
          <p class="text-blue-700 dark:text-blue-300">
            {{ $t("Invitations are sent by administrators to grant access to the platform. Check your email for invitation links or contact your administrator for assistance.") }}
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, inject } from 'vue';
import { useRouter } from 'vue-router';

definePageMeta({
  layout: 'invite',
  title: 'Invitation Portal',
  subtitle: 'Access your invitation',
  meta: [
    {
      name: 'description',
      content: 'Check your invitation status and access the Legal SaaS Platform.',
    },
  ],
});

// Composables
const router = useRouter();

// Inject invite notifications from layout
const inviteNotifications = inject('inviteNotifications') as any;

// Reactive state
const email = ref('');
const inviteCode = ref('');
const isLoading = ref(false);

// Methods
const checkInvitation = async () => {
  if (!email.value) {
    inviteNotifications?.setError('Please enter your email address');
    return;
  }

  isLoading.value = true;
  inviteNotifications?.setLoading(true);

  try {
    // Simulate API call to check invitation
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Mock response - in real implementation, call actual API
    const hasInvitation = Math.random() > 0.5; // Random for demo
    
    if (hasInvitation) {
      inviteNotifications?.setSuccess('Invitation found! Redirecting to setup...');
      setTimeout(() => {
        router.push('/auth/accept-invite/demo-token');
      }, 1500);
    } else {
      inviteNotifications?.setWarning('No invitation found for this email address. Please check your email or contact your administrator.');
    }
  } catch (error: any) {
    inviteNotifications?.setError('Failed to check invitation. Please try again.');
    console.error('Invitation check error:', error);
  } finally {
    isLoading.value = false;
    inviteNotifications?.setLoading(false);
  }
};

const requestInvitation = () => {
  inviteNotifications?.setSuccess('Invitation request feature coming soon! Please contact your administrator directly.');
};
</script>
