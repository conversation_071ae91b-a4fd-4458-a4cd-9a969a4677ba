<template>
  <div class="notification-channel-editor">
    <!-- Channel-specific Editor -->
    <component
      :is="currentEditorComponent"
      v-model:content="localContent"
      v-model:variables="localVariables"
      :template-data="templateData"
      :category="category"
      @content-change="handleContentChange"
      @variables-change="handleVariablesChange"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, defineAsyncComponent } from 'vue'

// Props
interface Props {
  channel: string
  content: any
  variables: string[]
  templateData: any
  category?: any
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update:content': [content: any]
  'update:variables': [variables: string[]]
  'content-change': [content: any, variables: string[]]
  'variables-change': [variables: string[]]
}>()

// Local state
const localContent = ref(props.content || {})
const localVariables = ref([...props.variables])

// Dynamic editor components
const editorComponents = {
  email: defineAsyncComponent(() => import('./channels/EmailChannelEditor.vue')),
  sms: defineAsyncComponent(() => import('./channels/SmsChannelEditor.vue')),
  push: defineAsyncComponent(() => import('./channels/PushChannelEditor.vue')),
  in_app: defineAsyncComponent(() => import('./channels/InAppChannelEditor.vue')),
  log: defineAsyncComponent(() => import('./channels/LogChannelEditor.vue')),
}

// Computed
const currentEditorComponent = computed(() => {
  return editorComponents[props.channel as keyof typeof editorComponents] || null
})

// Watchers
watch(() => props.content, (newContent) => {
  localContent.value = newContent || {}
}, { deep: true, immediate: true })

watch(() => props.variables, (newVariables) => {
  localVariables.value = [...newVariables]
}, { deep: true, immediate: true })

watch(localContent, (newContent) => {
  emit('update:content', newContent)
  emit('content-change', newContent, localVariables.value)
}, { deep: true })

watch(localVariables, (newVariables) => {
  emit('update:variables', newVariables)
  emit('variables-change', newVariables)
}, { deep: true })

// Methods
const handleContentChange = (content: any, variables?: string[]) => {
  localContent.value = content
  if (variables) {
    localVariables.value = variables
  }
  emit('content-change', content, localVariables.value)
}

const handleVariablesChange = (variables: string[]) => {
  localVariables.value = variables
  emit('variables-change', variables)
}
</script>

<style scoped>
.notification-channel-editor {
  @apply w-full;
}
</style>
