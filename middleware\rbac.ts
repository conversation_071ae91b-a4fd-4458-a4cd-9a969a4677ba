// middleware/rbac.ts
import { useAuth } from '../composables/useAuth.js';
import { PlatformRoles, TenantRoles } from '../app/features/auth/constants/roles.js';
import type { RouteLocationNormalized } from 'vue-router';

import { defineNuxtRouteMiddleware, navigateTo } from '#app';




export default defineNuxtRouteMiddleware(async (to: RouteLocationNormalized, _from: RouteLocationNormalized) => {
  // The 'return;' at the top of the original middleware effectively disabled it.
  // Removing it to enable the logic.
  // return;

  const {
    isAuthenticated,
    isLoading,
    currentUser,
    hasPlatformRole,
    hasActiveTenantRole,
    platformUserRoles,
    activeTenantUserRoles
  } = useAuth();

  // If user data is still being fetched
  if (isLoading.value) {
    // Consider a more robust loading state handling, e.g., by returning a promise
    // or using a global loading indicator that pauses navigation.
    // For now, log and proceed with caution, or block if critical.
    console.warn('RBAC Middleware: User auth state is loading. Access check might be premature.');
    // return; // Or some form of blocking/redirecting to a loading page
  }

  const requiredPlatformRoles = to.meta.platformRoles as PlatformRoles[] | undefined;
  const requiredTenantRoles = to.meta.tenantRoles as TenantRoles[] | undefined;
  const requiresAuth = to.meta.requiresAuth ?? (!!requiredPlatformRoles || !!requiredTenantRoles); // Default to true if any roles are specified

  // If route doesn't require authentication and no specific roles, allow.
  if (!requiresAuth && !requiredPlatformRoles && !requiredTenantRoles) {
    return;
  }

  // If authentication is required and user is not authenticated, redirect to login.
  if (requiresAuth && !isAuthenticated.value) {
    console.warn(`RBAC Middleware: User not authenticated. Redirecting to /auth/login for route: ${to.path}`);
    // Include redirect parameter to return to the original page after login
    const redirectUrl = `/auth/login?redirect=${encodeURIComponent(to.fullPath)}`;
    return navigateTo(redirectUrl, { replace: true, external: false });
  }
  
  let hasPermission = false;

  // Check platform roles if specified
  if (requiredPlatformRoles && requiredPlatformRoles.length > 0) {
    if (requiredPlatformRoles.some(role => hasPlatformRole(role))) {
      hasPermission = true;
    }
  }

  // Check tenant roles if specified (and if not already permitted by platform role)
  if (!hasPermission && requiredTenantRoles && requiredTenantRoles.length > 0) {
    if (requiredTenantRoles.some(role => hasActiveTenantRole(role))) {
      hasPermission = true;
    }
  }
  
  // If no roles were required by the route (but requiresAuth might be true, e.g. for any logged-in user)
  // and we've passed the isAuthenticated check, then permission is granted.
  if (!requiredPlatformRoles && !requiredTenantRoles && isAuthenticated.value) {
      hasPermission = true;
  }


  if (!hasPermission) {
    const userPlatformRoles = platformUserRoles.value || [];
    const userActiveTenantRoles = activeTenantUserRoles.value || [];
    const userRolesForLog = [...userPlatformRoles, ...userActiveTenantRoles].join(', ');
    const requiredRolesForLog = [
      ...(requiredPlatformRoles || []).map(r => `Platform: ${r}`),
      ...(requiredTenantRoles || []).map(r => `Tenant: ${r}`)
    ].join(', ');

    console.warn(`RBAC Middleware: User "${currentUser.value?.email}" (Roles: ${userRolesForLog}) does not have required roles: [${requiredRolesForLog}] for route: ${to.path}`);
    return navigateTo('/access-denied', { replace: true, external: false });
  }

  const userPlatformRoles = platformUserRoles.value || [];
  const userActiveTenantRoles = activeTenantUserRoles.value || [];
  const userRolesForLog = [...userPlatformRoles, ...userActiveTenantRoles].join(', ');
  console.info(`RBAC Middleware: Access granted for route ${to.path} to user "${currentUser.value?.email}" with roles: ${userRolesForLog}`);
});

/**
 * Extend the Vue Router Meta interface to include scoped roles.
 */
declare module 'vue-router' {
  interface RouteMeta {
    requiresAuth?: boolean; // General flag if route needs authentication
    platformRoles?: PlatformRoles[]; // Array of platform roles allowed
    tenantRoles?: TenantRoles[];   // Array of tenant roles allowed (for active tenant)
    // Old 'roles' can be deprecated or kept for backward compatibility during transition
    roles?: string[]; // Deprecated: use platformRoles or tenantRoles
  }
}