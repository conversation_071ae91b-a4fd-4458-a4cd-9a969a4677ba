# Global Modal System

The Legal SaaS Frontend includes a comprehensive global modal system that allows you to display modals from anywhere in your application without prop drilling or complex state management.

## Overview

The global modal system consists of:

- **GlobalModals.vue**: The main component that renders all active modals
- **useModal**: Core modal composable with advanced features
- **useGlobalModal**: Simplified composable for common modal use cases
- **Modal Manager**: Handles modal stacking and lifecycle

## Setup

The `GlobalModals` component is automatically included in `app.vue` and is available across the entire application.

## Basic Usage

### Using useGlobalModal

```typescript
import { useGlobalModal } from '~/composables/useGlobalModal'

const { openModal, openConfirmModal, openAlertModal } = useGlobalModal()

// Open a basic modal
openModal({
  title: 'My Modal',
  size: ComponentSize.MD,
  closable: true,
  onClose: () => console.log('Modal closed')
})

// Open a confirmation modal
const confirmed = await openConfirmModal(
  'Confirm Delete',
  'Are you sure you want to delete this item?',
  {
    confirmText: 'Delete',
    cancelText: 'Cancel',
    type: 'error',
    onConfirm: () => deleteItem()
  }
)

// Open an alert modal
await openAlertModal(
  'Success',
  'Item was successfully created!',
  'success'
)
```

### Using the Core useModal

```typescript
import { useModal } from '~/app/shared/composables/ui/useModal'

const modal = useModal({
  title: 'Advanced Modal',
  size: ComponentSize.LG,
  animation: 'slide',
  persistent: true,
  onOpen: () => console.log('Modal opened'),
  onClose: () => console.log('Modal closed')
})

// Open the modal
modal.open()

// Close the modal
modal.close()
```

## Modal Configuration

### ModalConfig Options

```typescript
interface ModalConfig {
  id?: string                    // Unique identifier
  title?: string                 // Modal title
  size?: ComponentSize | 'full'  // Modal size
  closable?: boolean            // Show close button
  maskClosable?: boolean        // Close on backdrop click
  keyboard?: boolean            // Close on ESC key
  centered?: boolean            // Center the modal
  destroyOnClose?: boolean      // Destroy content on close
  zIndex?: number              // Custom z-index
  animation?: 'fade' | 'slide' | 'zoom' | 'none'
  persistent?: boolean          // Prevent closing
  maxWidth?: string            // Custom max width
  maxHeight?: string           // Custom max height
  className?: string           // Additional CSS classes
  onOpen?: () => void          // Open callback
  onClose?: () => void         // Close callback
  onCancel?: () => void        // Cancel callback
  onOk?: () => void           // OK callback
}
```

### Modal Sizes

- `ComponentSize.XS`: 20rem (320px)
- `ComponentSize.SM`: 24rem (384px)
- `ComponentSize.MD`: 32rem (512px)
- `ComponentSize.LG`: 48rem (768px)
- `ComponentSize.XL`: 64rem (1024px)
- `'full'`: 95vw x 95vh

## Advanced Features

### Modal Stacking

The system supports multiple modals open simultaneously with proper z-index management:

```typescript
// Open multiple modals
const modal1 = openModal({ title: 'First Modal' })
const modal2 = openModal({ title: 'Second Modal' }) // Will appear on top
```

### Custom Components

You can render custom Vue components inside modals:

```typescript
import MyComponent from '~/components/MyComponent.vue'

openModal({
  title: 'Custom Component',
  component: MyComponent,
  props: { userId: 123 },
  size: ComponentSize.LG
})
```

### Animations

Choose from different animation types:

- `fade`: Opacity transition (default)
- `slide`: Slide from top
- `zoom`: Scale transition
- `none`: No animation

### Accessibility

The modal system includes:

- Proper ARIA attributes
- Focus management
- Keyboard navigation (ESC to close)
- Screen reader support

### Responsive Design

Modals automatically adapt to different screen sizes:

- Mobile: Full width with minimal margins
- Tablet/Desktop: Centered with appropriate sizing
- Full-screen modals: 100% viewport on mobile

## Examples

See `components/examples/ModalExamples.vue` for comprehensive usage examples.

## Best Practices

1. **Use appropriate sizes**: Choose the right modal size for your content
2. **Provide clear titles**: Always include descriptive titles
3. **Handle loading states**: Show loading indicators for async operations
4. **Implement proper cleanup**: Clean up resources in onClose callbacks
5. **Consider mobile users**: Test modal behavior on mobile devices
6. **Use confirmation modals**: For destructive actions, always confirm
7. **Limit modal stacking**: Avoid opening too many modals simultaneously

## Troubleshooting

### Modal not appearing
- Check that GlobalModals is included in app.vue
- Verify the modal is being opened correctly
- Check for JavaScript errors in console

### Styling issues
- Ensure Tailwind CSS is properly configured
- Check for conflicting CSS rules
- Verify dark mode compatibility

### Performance issues
- Use `destroyOnClose` for heavy components
- Implement proper cleanup in onClose callbacks
- Avoid opening too many modals simultaneously
