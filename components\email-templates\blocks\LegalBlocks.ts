/**
 * Legal SaaS Email Template Blocks
 *
 * Custom blocks and components specifically designed for legal professionals
 */

interface EmailEditorBlock {
  id: string
  label: string
  category: string
  content: string | (() => string)
  media?: string
  attributes?: Record<string, any>
}

export const legalBlocks: EmailEditorBlock[] = [
  // Legal Header Block
  {
    id: 'legal-header',
    label: 'Legal Header',
    category: 'Legal',
    media: `<svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M12 2L2 7v10c0 5.55 3.84 9.74 9 11 5.16-1.26 9-5.45 9-11V7l-10-5z"/>
    </svg>`,
    content: `
      <div class="legal-header" style="background-color: #1e40af; color: white; padding: 24px; text-align: center;">
        <div style="max-width: 600px; margin: 0 auto;">
          <h1 style="margin: 0; font-size: 24px; font-weight: bold; margin-bottom: 8px;">
            {{firm_name}}
          </h1>
          <p style="margin: 0; font-size: 14px; opacity: 0.9;">
            {{firm_address}} | {{firm_phone}} | {{firm_email}}
          </p>
        </div>
      </div>
    `,
    attributes: {
      'data-gjs-type': 'legal-header',
      'data-gjs-editable': true,
      'data-gjs-droppable': false
    }
  },

  // Case Information Block
  {
    id: 'case-info',
    label: 'Case Information',
    category: 'Legal',
    media: `<svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8l-6-6z"/>
      <polyline points="14,2 14,8 20,8"/>
      <line x1="16" y1="13" x2="8" y2="13"/>
      <line x1="16" y1="17" x2="8" y2="17"/>
      <polyline points="10,9 9,9 8,9"/>
    </svg>`,
    content: `
      <div class="case-info" style="background-color: #f8fafc; border: 1px solid #e2e8f0; border-radius: 8px; padding: 20px; margin: 16px 0;">
        <h3 style="margin: 0 0 16px 0; color: #1e40af; font-size: 18px; font-weight: 600;">
          Case Information
        </h3>
        <table style="width: 100%; border-collapse: collapse;">
          <tr>
            <td style="padding: 8px 0; font-weight: 600; color: #374151; width: 30%;">Case Number:</td>
            <td style="padding: 8px 0; color: #6b7280;">{{case_number}}</td>
          </tr>
          <tr>
            <td style="padding: 8px 0; font-weight: 600; color: #374151;">Case Title:</td>
            <td style="padding: 8px 0; color: #6b7280;">{{case_title}}</td>
          </tr>
          <tr>
            <td style="padding: 8px 0; font-weight: 600; color: #374151;">Status:</td>
            <td style="padding: 8px 0; color: #6b7280;">{{case_status}}</td>
          </tr>
          <tr>
            <td style="padding: 8px 0; font-weight: 600; color: #374151;">Next Hearing:</td>
            <td style="padding: 8px 0; color: #6b7280;">{{next_hearing_date}}</td>
          </tr>
        </table>
      </div>
    `,
    attributes: {
      'data-gjs-type': 'case-info',
      'data-gjs-editable': true,
      'data-gjs-droppable': false
    }
  },

  // Legal Notice Block
  {
    id: 'legal-notice',
    label: 'Legal Notice',
    category: 'Legal',
    media: `<svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
    </svg>`,
    content: `
      <div class="legal-notice" style="background-color: #fef3c7; border-left: 4px solid #f59e0b; padding: 16px; margin: 16px 0;">
        <div style="display: flex; align-items: flex-start;">
          <div style="flex-shrink: 0; margin-right: 12px;">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="#f59e0b">
              <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
            </svg>
          </div>
          <div>
            <h4 style="margin: 0 0 8px 0; color: #92400e; font-size: 16px; font-weight: 600;">
              Important Legal Notice
            </h4>
            <p style="margin: 0; color: #92400e; font-size: 14px; line-height: 1.5;">
              {{notice_content}}
            </p>
          </div>
        </div>
      </div>
    `,
    attributes: {
      'data-gjs-type': 'legal-notice',
      'data-gjs-editable': true,
      'data-gjs-droppable': false
    }
  },

  // Invoice Summary Block
  {
    id: 'invoice-summary',
    label: 'Invoice Summary',
    category: 'Legal',
    media: `<svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8l-6-6z"/>
      <polyline points="14,2 14,8 20,8"/>
      <line x1="16" y1="13" x2="8" y2="13"/>
      <line x1="16" y1="17" x2="8" y2="17"/>
    </svg>`,
    content: `
      <div class="invoice-summary" style="border: 1px solid #e2e8f0; border-radius: 8px; overflow: hidden; margin: 16px 0;">
        <div style="background-color: #1e40af; color: white; padding: 16px;">
          <h3 style="margin: 0; font-size: 18px; font-weight: 600;">
            Invoice Summary
          </h3>
        </div>
        <div style="padding: 20px;">
          <table style="width: 100%; border-collapse: collapse;">
            <tr style="border-bottom: 1px solid #e2e8f0;">
              <td style="padding: 12px 0; font-weight: 600; color: #374151;">Invoice Number:</td>
              <td style="padding: 12px 0; color: #6b7280; text-align: right;">{{invoice_number}}</td>
            </tr>
            <tr style="border-bottom: 1px solid #e2e8f0;">
              <td style="padding: 12px 0; font-weight: 600; color: #374151;">Issue Date:</td>
              <td style="padding: 12px 0; color: #6b7280; text-align: right;">{{issue_date}}</td>
            </tr>
            <tr style="border-bottom: 1px solid #e2e8f0;">
              <td style="padding: 12px 0; font-weight: 600; color: #374151;">Due Date:</td>
              <td style="padding: 12px 0; color: #6b7280; text-align: right;">{{due_date}}</td>
            </tr>
            <tr style="border-bottom: 2px solid #1e40af;">
              <td style="padding: 16px 0; font-weight: 700; color: #1e40af; font-size: 18px;">Total Amount:</td>
              <td style="padding: 16px 0; font-weight: 700; color: #1e40af; font-size: 18px; text-align: right;">{{total_amount}}</td>
            </tr>
          </table>
        </div>
      </div>
    `,
    attributes: {
      'data-gjs-type': 'invoice-summary',
      'data-gjs-editable': true,
      'data-gjs-droppable': false
    }
  },

  // Client Information Block
  {
    id: 'client-info',
    label: 'Client Information',
    category: 'Legal',
    media: `<svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
    </svg>`,
    content: `
      <div class="client-info" style="background-color: #f0f9ff; border: 1px solid #0ea5e9; border-radius: 8px; padding: 20px; margin: 16px 0;">
        <h3 style="margin: 0 0 16px 0; color: #0c4a6e; font-size: 18px; font-weight: 600;">
          Client Information
        </h3>
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px;">
          <div>
            <p style="margin: 0 0 4px 0; font-weight: 600; color: #0c4a6e; font-size: 14px;">Name:</p>
            <p style="margin: 0 0 12px 0; color: #075985;">{{client_name}}</p>
            
            <p style="margin: 0 0 4px 0; font-weight: 600; color: #0c4a6e; font-size: 14px;">Email:</p>
            <p style="margin: 0 0 12px 0; color: #075985;">{{client_email}}</p>
          </div>
          <div>
            <p style="margin: 0 0 4px 0; font-weight: 600; color: #0c4a6e; font-size: 14px;">Phone:</p>
            <p style="margin: 0 0 12px 0; color: #075985;">{{client_phone}}</p>
            
            <p style="margin: 0 0 4px 0; font-weight: 600; color: #0c4a6e; font-size: 14px;">Address:</p>
            <p style="margin: 0; color: #075985;">{{client_address}}</p>
          </div>
        </div>
      </div>
    `,
    attributes: {
      'data-gjs-type': 'client-info',
      'data-gjs-editable': true,
      'data-gjs-droppable': false
    }
  },

  // Action Required Block
  {
    id: 'action-required',
    label: 'Action Required',
    category: 'Legal',
    media: `<svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
    </svg>`,
    content: `
      <div class="action-required" style="background-color: #fef2f2; border: 2px solid #ef4444; border-radius: 8px; padding: 20px; margin: 16px 0; text-align: center;">
        <div style="margin-bottom: 16px;">
          <svg width="48" height="48" viewBox="0 0 24 24" fill="#ef4444" style="margin: 0 auto;">
            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
          </svg>
        </div>
        <h3 style="margin: 0 0 12px 0; color: #dc2626; font-size: 20px; font-weight: 700;">
          Action Required
        </h3>
        <p style="margin: 0 0 16px 0; color: #991b1b; font-size: 16px; line-height: 1.5;">
          {{action_description}}
        </p>
        <div style="margin-bottom: 16px;">
          <p style="margin: 0; color: #991b1b; font-weight: 600;">Deadline: {{deadline_date}}</p>
        </div>
        <a href="{{action_link}}" style="display: inline-block; background-color: #ef4444; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 600;">
          Take Action Now
        </a>
      </div>
    `,
    attributes: {
      'data-gjs-type': 'action-required',
      'data-gjs-editable': true,
      'data-gjs-droppable': false
    }
  },

  // Legal Footer Block
  {
    id: 'legal-footer',
    label: 'Legal Footer',
    category: 'Legal',
    media: `<svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M12 2L2 7v10c0 5.55 3.84 9.74 9 11 5.16-1.26 9-5.45 9-11V7l-10-5z"/>
    </svg>`,
    content: `
      <div class="legal-footer" style="background-color: #374151; color: #d1d5db; padding: 24px; text-align: center; margin-top: 32px;">
        <div style="max-width: 600px; margin: 0 auto;">
          <p style="margin: 0 0 12px 0; font-size: 14px; line-height: 1.5;">
            This email contains confidential and legally privileged information. If you are not the intended recipient, please notify the sender immediately and delete this email.
          </p>
          <div style="border-top: 1px solid #4b5563; padding-top: 16px; margin-top: 16px;">
            <p style="margin: 0 0 8px 0; font-weight: 600; color: #f3f4f6;">{{firm_name}}</p>
            <p style="margin: 0; font-size: 12px;">
              {{firm_address}} | {{firm_phone}} | {{firm_email}}
            </p>
          </div>
        </div>
      </div>
    `,
    attributes: {
      'data-gjs-type': 'legal-footer',
      'data-gjs-editable': true,
      'data-gjs-droppable': false
    }
  },

  // Document List Block
  {
    id: 'document-list',
    label: 'Document List',
    category: 'Legal',
    media: `<svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8l-6-6z"/>
      <polyline points="14,2 14,8 20,8"/>
    </svg>`,
    content: `
      <div class="document-list" style="border: 1px solid #e2e8f0; border-radius: 8px; margin: 16px 0; overflow: hidden;">
        <div style="background-color: #f8fafc; padding: 16px; border-bottom: 1px solid #e2e8f0;">
          <h3 style="margin: 0; color: #374151; font-size: 18px; font-weight: 600;">
            Related Documents
          </h3>
        </div>
        <div style="padding: 16px;">
          <div style="display: flex; align-items: center; padding: 12px 0; border-bottom: 1px solid #f1f5f9;">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="#6b7280" style="margin-right: 12px;">
              <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8l-6-6z"/>
              <polyline points="14,2 14,8 20,8"/>
            </svg>
            <div style="flex: 1;">
              <p style="margin: 0; font-weight: 600; color: #374151;">{{document_1_name}}</p>
              <p style="margin: 0; font-size: 12px; color: #6b7280;">{{document_1_date}}</p>
            </div>
            <a href="{{document_1_link}}" style="color: #1e40af; text-decoration: none; font-size: 14px;">View</a>
          </div>
          <div style="display: flex; align-items: center; padding: 12px 0; border-bottom: 1px solid #f1f5f9;">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="#6b7280" style="margin-right: 12px;">
              <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8l-6-6z"/>
              <polyline points="14,2 14,8 20,8"/>
            </svg>
            <div style="flex: 1;">
              <p style="margin: 0; font-weight: 600; color: #374151;">{{document_2_name}}</p>
              <p style="margin: 0; font-size: 12px; color: #6b7280;">{{document_2_date}}</p>
            </div>
            <a href="{{document_2_link}}" style="color: #1e40af; text-decoration: none; font-size: 14px;">View</a>
          </div>
        </div>
      </div>
    `,
    attributes: {
      'data-gjs-type': 'document-list',
      'data-gjs-editable': true,
      'data-gjs-droppable': false
    }
  }
]

/**
 * Register legal blocks with GrapesJS editor
 */
export function registerLegalBlocks(editor: any) {
  const blockManager = editor.BlockManager

  // Add legal blocks with enhanced configuration
  legalBlocks.forEach(block => {
    blockManager.add(block.id, {
      label: block.label,
      category: {
        id: 'legal',
        label: 'Legal',
        open: true
      },
      media: block.media,
      content: block.content,
      attributes: {
        ...block.attributes,
        class: 'gjs-block-legal'
      }
    })
  })
  
  // Add custom CSS for legal blocks
  const css = `
    .legal-header, .case-info, .legal-notice, .invoice-summary, 
    .client-info, .action-required, .legal-footer, .document-list {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    }
    
    @media only screen and (max-width: 600px) {
      .case-info table, .invoice-summary table {
        font-size: 14px;
      }
      
      .client-info > div {
        grid-template-columns: 1fr !important;
      }
      
      .legal-header h1 {
        font-size: 20px !important;
      }
      
      .legal-header p {
        font-size: 12px !important;
      }
    }
  `
  
  editor.addStyle(css)
}
