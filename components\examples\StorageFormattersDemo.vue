<template>
  <div class="space-y-8 p-6 bg-white dark:bg-gray-900 rounded-lg shadow-lg">
    <div class="text-center">
      <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-2">
        Storage Formatters Demo
      </h2>
      <p class="text-gray-600 dark:text-gray-400">
        Demonstrating global storage formatting functions in Vue templates
      </p>
    </div>

    <!-- Basic Formatting Examples -->
    <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
        Basic Formatting Examples
      </h3>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div class="space-y-2">
          <h4 class="font-medium text-gray-700 dark:text-gray-300">File Sizes</h4>
          <div class="space-y-1 text-sm">
            <div>1024 bytes = {{ $formatBytes(1024) }}</div>
            <div>1048576 bytes = {{ $formatBytes(1048576) }}</div>
            <div>1073741824 bytes = {{ $formatBytes(1073741824) }}</div>
            <div>1099511627776 bytes = {{ $formatBytes(1099511627776) }}</div>
          </div>
        </div>
        
        <div class="space-y-2">
          <h4 class="font-medium text-gray-700 dark:text-gray-300">Binary Format</h4>
          <div class="space-y-1 text-sm">
            <div>1024 bytes = {{ $formatBytes(1024, { binary: true }) }}</div>
            <div>1048576 bytes = {{ $formatBytes(1048576, { binary: true }) }}</div>
            <div>1073741824 bytes = {{ $formatBytes(1073741824, { binary: true }) }}</div>
            <div>1099511627776 bytes = {{ $formatBytes(1099511627776, { binary: true }) }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Storage Usage Examples -->
    <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
        Storage Usage Examples
      </h3>
      
      <div class="space-y-4">
        <div class="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
          <h4 class="font-medium text-gray-700 dark:text-gray-300 mb-2">Tenant Storage Usage</h4>
          <div class="space-y-2 text-sm">
            <div>Used: {{ $formatBytes(2147483648) }} (2GB)</div>
            <div>Total: {{ $formatBytes(5368709120) }} (5GB)</div>
            <div>Usage: {{ $formatStorageUsage(2147483648, 5368709120) }}</div>
            <div>Remaining: {{ $formatRemainingStorage(2147483648, 5368709120) }}</div>
            <div>Status: {{ $getStorageStatus(2147483648, 5368709120) }}</div>
            <div>Near Capacity: {{ $isStorageNearCapacity(2147483648, 5368709120) ? 'Yes' : 'No' }}</div>
          </div>
        </div>
        
        <div class="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
          <h4 class="font-medium text-gray-700 dark:text-gray-300 mb-2">Document Storage</h4>
          <div class="space-y-2 text-sm">
            <div>Used: {{ $formatBytes(4831838208) }} (4.5GB)</div>
            <div>Total: {{ $formatBytes(5368709120) }} (5GB)</div>
            <div>Usage: {{ $formatStorageUsage(4831838208, 5368709120) }}</div>
            <div>Remaining: {{ $formatRemainingStorage(4831838208, 5368709120) }}</div>
            <div>Status: {{ $getStorageStatus(4831838208, 5368709120) }}</div>
            <div>Near Capacity: {{ $isStorageNearCapacity(4831838208, 5368709120) ? 'Yes' : 'No' }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Interactive Example -->
    <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
        Interactive Example
      </h3>
      
      <div class="space-y-4">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Bytes to Format
            </label>
            <input
              v-model.number="inputBytes"
              type="number"
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
              placeholder="Enter bytes..."
            />
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Decimal Places
            </label>
            <input
              v-model.number="decimals"
              type="number"
              min="0"
              max="5"
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
            />
          </div>
        </div>
        
        <div class="flex items-center space-x-4">
          <label class="flex items-center">
            <input
              v-model="useBinary"
              type="checkbox"
              class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
            />
            <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Use Binary (1024-based)</span>
          </label>
          
          <label class="flex items-center">
            <input
              v-model="useLongForm"
              type="checkbox"
              class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
            />
            <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Long Form</span>
          </label>
        </div>
        
        <div class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
          <h4 class="font-medium text-blue-900 dark:text-blue-100 mb-2">Formatted Result:</h4>
          <div class="text-lg font-mono text-blue-800 dark:text-blue-200">
            {{ $formatBytes(inputBytes, { decimals, binary: useBinary, longForm: useLongForm }) }}
          </div>
        </div>
      </div>
    </div>

    <!-- Legacy Compatibility -->
    <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
        Legacy Compatibility
      </h3>
      
      <div class="space-y-2 text-sm">
        <div>Using $formatFileSize (legacy): {{ $formatFileSize(1048576) }}</div>
        <div>Using $formatBytes (new): {{ $formatBytes(1048576) }}</div>
        <div class="text-gray-500 dark:text-gray-400 text-xs">
          Both functions work identically for backward compatibility
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

// Reactive data for interactive example
const inputBytes = ref(1073741824) // 1GB
const decimals = ref(2)
const useBinary = ref(false)
const useLongForm = ref(false)
</script>
