<template>
  <div class="space-y-8">
    <!-- Settings Navigation -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
      <div class="border-b border-gray-200 dark:border-gray-700">
        <nav class="flex space-x-8 px-6" aria-label="Settings">
          <button
            v-for="tab in settingsTabs"
            :key="tab.id"
            @click="activeTab = tab.id"
            :class="[
              'py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200',
              activeTab === tab.id
                ? 'border-brandPrimary text-brandPrimary'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
            ]"
          >
            <Icon :name="tab.icon" class="h-5 w-5 mr-2 inline" />
            {{ tab.label }}
          </button>
        </nav>
      </div>

      <!-- Settings Content -->
      <div class="p-6">
        <!-- General Settings -->
        <div v-if="activeTab === 'general'" class="space-y-6">
          <div>
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">General Settings</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <UiInput
                v-model="settings.general.platformName"
                label="Platform Name"
                placeholder="Enter platform name"
                :disabled="saving"
              />
              <UiInput
                v-model="settings.general.platformUrl"
                label="Platform URL"
                placeholder="https://example.com"
                :disabled="saving"
              />
              <UiSelect
                v-model="settings.general.defaultTimezone"
                label="Default Timezone"
                :options="timezoneOptions"
                :disabled="saving"
              />
              <UiSelect
                v-model="settings.general.defaultLanguage"
                label="Default Language"
                :options="languageOptions"
                :disabled="saving"
              />
              <UiInput
                v-model="settings.general.supportEmail"
                label="Support Email"
                type="email"
                placeholder="<EMAIL>"
                :disabled="saving"
              />
              <UiInput
                v-model="settings.general.maxFileSize"
                label="Max File Size (MB)"
                type="number"
                :disabled="saving"
              />
            </div>
          </div>

          <div>
            <h4 class="text-md font-medium text-gray-900 dark:text-white mb-3">Platform Features</h4>
            <div class="space-y-3">
              <UiCheckbox
                v-model="settings.general.enableRegistration"
                label="Enable User Registration"
                description="Allow new users to register for accounts"
                :disabled="saving"
              />
              <UiCheckbox
                v-model="settings.general.enableEmailVerification"
                label="Require Email Verification"
                description="Users must verify their email before accessing the platform"
                :disabled="saving"
              />
              <UiCheckbox
                v-model="settings.general.enableTwoFactor"
                label="Enable Two-Factor Authentication"
                description="Allow users to enable 2FA for enhanced security"
                :disabled="saving"
              />
              <UiCheckbox
                v-model="settings.general.enableMaintenanceMode"
                label="Maintenance Mode"
                description="Put the platform in maintenance mode"
                :disabled="saving"
              />
            </div>
          </div>
        </div>

        <!-- Security Settings -->
        <div v-if="activeTab === 'security'" class="space-y-6">
          <div>
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Security Settings</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <UiInput
                v-model="settings.security.sessionTimeout"
                label="Session Timeout (minutes)"
                type="number"
                :disabled="saving"
              />
              <UiInput
                v-model="settings.security.passwordMinLength"
                label="Minimum Password Length"
                type="number"
                :disabled="saving"
              />
              <UiInput
                v-model="settings.security.maxLoginAttempts"
                label="Max Login Attempts"
                type="number"
                :disabled="saving"
              />
              <UiInput
                v-model="settings.security.lockoutDuration"
                label="Lockout Duration (minutes)"
                type="number"
                :disabled="saving"
              />
            </div>
          </div>

          <div>
            <h4 class="text-md font-medium text-gray-900 dark:text-white mb-3">Password Requirements</h4>
            <div class="space-y-3">
              <UiCheckbox
                v-model="settings.security.requireUppercase"
                label="Require Uppercase Letters"
                :disabled="saving"
              />
              <UiCheckbox
                v-model="settings.security.requireLowercase"
                label="Require Lowercase Letters"
                :disabled="saving"
              />
              <UiCheckbox
                v-model="settings.security.requireNumbers"
                label="Require Numbers"
                :disabled="saving"
              />
              <UiCheckbox
                v-model="settings.security.requireSpecialChars"
                label="Require Special Characters"
                :disabled="saving"
              />
            </div>
          </div>

          <div>
            <h4 class="text-md font-medium text-gray-900 dark:text-white mb-3">Security Features</h4>
            <div class="space-y-3">
              <UiCheckbox
                v-model="settings.security.enableAuditLogging"
                label="Enable Audit Logging"
                description="Log all user actions for security auditing"
                :disabled="saving"
              />
              <UiCheckbox
                v-model="settings.security.enableIpWhitelist"
                label="Enable IP Whitelist"
                description="Restrict access to specific IP addresses"
                :disabled="saving"
              />
              <UiCheckbox
                v-model="settings.security.enableRateLimiting"
                label="Enable Rate Limiting"
                description="Limit API requests per user/IP"
                :disabled="saving"
              />
            </div>
          </div>
        </div>

        <!-- Email Settings -->
        <div v-if="activeTab === 'email'" class="space-y-6">
          <div>
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Email Configuration</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <UiInput
                v-model="settings.email.smtpHost"
                label="SMTP Host"
                placeholder="smtp.example.com"
                :disabled="saving"
              />
              <UiInput
                v-model="settings.email.smtpPort"
                label="SMTP Port"
                type="number"
                placeholder="587"
                :disabled="saving"
              />
              <UiInput
                v-model="settings.email.smtpUsername"
                label="SMTP Username"
                :disabled="saving"
              />
              <UiInput
                v-model="settings.email.smtpPassword"
                label="SMTP Password"
                type="password"
                :disabled="saving"
              />
              <UiInput
                v-model="settings.email.fromEmail"
                label="From Email"
                type="email"
                placeholder="<EMAIL>"
                :disabled="saving"
              />
              <UiInput
                v-model="settings.email.fromName"
                label="From Name"
                placeholder="Platform Name"
                :disabled="saving"
              />
            </div>
          </div>

          <div>
            <h4 class="text-md font-medium text-gray-900 dark:text-white mb-3">Email Features</h4>
            <div class="space-y-3">
              <UiCheckbox
                v-model="settings.email.enableSsl"
                label="Enable SSL/TLS"
                :disabled="saving"
              />
              <UiCheckbox
                v-model="settings.email.enableEmailQueue"
                label="Enable Email Queue"
                description="Queue emails for batch processing"
                :disabled="saving"
              />
              <UiCheckbox
                v-model="settings.email.enableEmailTracking"
                label="Enable Email Tracking"
                description="Track email opens and clicks"
                :disabled="saving"
              />
            </div>
          </div>

          <div class="flex items-center gap-4">
            <UiButton @click="testEmailConnection" variant="outline" :loading="testingEmail">
              <Icon name="material-symbols:send" class="h-4 w-4 mr-2" />
              Test Connection
            </UiButton>
            <UiButton @click="sendTestEmail" variant="outline" :loading="sendingTestEmail">
              <Icon name="material-symbols:email" class="h-4 w-4 mr-2" />
              Send Test Email
            </UiButton>
          </div>
        </div>

        <!-- Storage Settings -->
        <div v-if="activeTab === 'storage'" class="space-y-6">
          <div>
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Storage Configuration</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <UiSelect
                v-model="settings.storage.provider"
                label="Storage Provider"
                :options="storageProviders"
                :disabled="saving"
              />
              <UiInput
                v-model="settings.storage.maxStoragePerTenant"
                label="Max Storage per Tenant (GB)"
                type="number"
                :disabled="saving"
              />
              <UiInput
                v-model="settings.storage.retentionPeriod"
                label="File Retention Period (days)"
                type="number"
                :disabled="saving"
              />
              <UiSelect
                v-model="settings.storage.compressionLevel"
                label="Compression Level"
                :options="compressionOptions"
                :disabled="saving"
              />
            </div>
          </div>

          <div>
            <h4 class="text-md font-medium text-gray-900 dark:text-white mb-3">Storage Features</h4>
            <div class="space-y-3">
              <UiCheckbox
                v-model="settings.storage.enableEncryption"
                label="Enable File Encryption"
                description="Encrypt files at rest"
                :disabled="saving"
              />
              <UiCheckbox
                v-model="settings.storage.enableVersioning"
                label="Enable File Versioning"
                description="Keep multiple versions of files"
                :disabled="saving"
              />
              <UiCheckbox
                v-model="settings.storage.enableAutoBackup"
                label="Enable Auto Backup"
                description="Automatically backup files"
                :disabled="saving"
              />
            </div>
          </div>
        </div>

        <!-- API Settings -->
        <div v-if="activeTab === 'api'" class="space-y-6">
          <div>
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">API Configuration</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <UiInput
                v-model="settings.api.rateLimitPerMinute"
                label="Rate Limit (requests/minute)"
                type="number"
                :disabled="saving"
              />
              <UiInput
                v-model="settings.api.rateLimitPerHour"
                label="Rate Limit (requests/hour)"
                type="number"
                :disabled="saving"
              />
              <UiInput
                v-model="settings.api.maxRequestSize"
                label="Max Request Size (MB)"
                type="number"
                :disabled="saving"
              />
              <UiInput
                v-model="settings.api.apiTimeout"
                label="API Timeout (seconds)"
                type="number"
                :disabled="saving"
              />
            </div>
          </div>

          <div>
            <h4 class="text-md font-medium text-gray-900 dark:text-white mb-3">API Features</h4>
            <div class="space-y-3">
              <UiCheckbox
                v-model="settings.api.enableApiDocs"
                label="Enable API Documentation"
                description="Provide Swagger/OpenAPI documentation"
                :disabled="saving"
              />
              <UiCheckbox
                v-model="settings.api.enableCors"
                label="Enable CORS"
                description="Allow cross-origin requests"
                :disabled="saving"
              />
              <UiCheckbox
                v-model="settings.api.enableApiLogging"
                label="Enable API Logging"
                description="Log all API requests and responses"
                :disabled="saving"
              />
            </div>
          </div>
        </div>

        <!-- Save Actions -->
        <div class="flex items-center justify-between pt-6 border-t border-gray-200 dark:border-gray-700">
          <div class="flex items-center space-x-4">
            <UiButton @click="resetSettings" variant="outline" :disabled="saving">
              <Icon name="material-symbols:refresh" class="h-4 w-4 mr-2" />
              Reset to Defaults
            </UiButton>
            <UiButton @click="exportSettings" variant="outline" :disabled="saving">
              <Icon name="material-symbols:download" class="h-4 w-4 mr-2" />
              Export Settings
            </UiButton>
          </div>
          <div class="flex items-center space-x-4">
            <span v-if="lastSaved" class="text-sm text-gray-500 dark:text-gray-400">
              Last saved: {{ formatTime(lastSaved) }}
            </span>
            <UiButton @click="saveSettings" :loading="saving">
              <Icon name="material-symbols:save" class="h-4 w-4 mr-2" />
              Save Settings
            </UiButton>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { PlatformRoles } from '~/app/features/auth/constants/roles'

// Page meta configuration
definePageMeta({
  layout: 'dashboard',
  title: 'Platform Settings',
  description: 'Configure platform-wide settings and preferences',
  pageHeaderIcon: 'material-symbols:settings',
  pageHeaderStats: [
    { key: 'settings', label: 'Settings Groups', value: '5', color: 'blue' },
    { key: 'modified', label: 'Modified Today', value: '3', color: 'green' },
    { key: 'pending', label: 'Pending Changes', value: '0', color: 'yellow' },
    { key: 'backups', label: 'Config Backups', value: '12', color: 'purple' }
  ],
  showRealTimeStatus: false,
  autoRefreshEnabled: false,
  middleware: ['rbac'],
  roles: [PlatformRoles.SUPER_ADMIN],
  breadcrumb: [
    { label: 'Dashboard', href: '/dashboard' },
    { label: 'Platform', href: '/dashboard/platform' },
    { label: 'Settings' },
  ],
})

// Reactive state
const activeTab = ref('general')
const saving = ref(false)
const testingEmail = ref(false)
const sendingTestEmail = ref(false)
const lastSaved = ref<Date | null>(null)

// Settings tabs
const settingsTabs = [
  { id: 'general', label: 'General', icon: 'material-symbols:settings' },
  { id: 'security', label: 'Security', icon: 'material-symbols:security' },
  { id: 'email', label: 'Email', icon: 'material-symbols:email' },
  { id: 'storage', label: 'Storage', icon: 'material-symbols:storage' },
  { id: 'api', label: 'API', icon: 'material-symbols:api' }
]

// Settings data
const settings = reactive({
  general: {
    platformName: 'Legal SaaS Platform',
    platformUrl: 'https://legal-saas.example.com',
    defaultTimezone: 'UTC',
    defaultLanguage: 'en',
    supportEmail: '<EMAIL>',
    maxFileSize: 100,
    enableRegistration: true,
    enableEmailVerification: true,
    enableTwoFactor: true,
    enableMaintenanceMode: false
  },
  security: {
    sessionTimeout: 60,
    passwordMinLength: 8,
    maxLoginAttempts: 5,
    lockoutDuration: 15,
    requireUppercase: true,
    requireLowercase: true,
    requireNumbers: true,
    requireSpecialChars: true,
    enableAuditLogging: true,
    enableIpWhitelist: false,
    enableRateLimiting: true
  },
  email: {
    smtpHost: 'smtp.example.com',
    smtpPort: 587,
    smtpUsername: '',
    smtpPassword: '',
    fromEmail: '<EMAIL>',
    fromName: 'Legal SaaS Platform',
    enableSsl: true,
    enableEmailQueue: true,
    enableEmailTracking: false
  },
  storage: {
    provider: 's3',
    maxStoragePerTenant: 100,
    retentionPeriod: 365,
    compressionLevel: 'medium',
    enableEncryption: true,
    enableVersioning: true,
    enableAutoBackup: true
  },
  api: {
    rateLimitPerMinute: 100,
    rateLimitPerHour: 1000,
    maxRequestSize: 10,
    apiTimeout: 30,
    enableApiDocs: true,
    enableCors: true,
    enableApiLogging: true
  }
})

// Options for select fields
const timezoneOptions = [
  { value: 'UTC', label: 'UTC' },
  { value: 'America/New_York', label: 'Eastern Time' },
  { value: 'America/Chicago', label: 'Central Time' },
  { value: 'America/Denver', label: 'Mountain Time' },
  { value: 'America/Los_Angeles', label: 'Pacific Time' },
  { value: 'Europe/London', label: 'London' },
  { value: 'Europe/Paris', label: 'Paris' },
  { value: 'Asia/Tokyo', label: 'Tokyo' },
  { value: 'Australia/Sydney', label: 'Sydney' }
]

const languageOptions = [
  { value: 'en', label: 'English' },
  { value: 'es', label: 'Spanish' },
  { value: 'fr', label: 'French' },
  { value: 'de', label: 'German' },
  { value: 'it', label: 'Italian' },
  { value: 'pt', label: 'Portuguese' },
  { value: 'ja', label: 'Japanese' },
  { value: 'ko', label: 'Korean' },
  { value: 'zh', label: 'Chinese' }
]

const storageProviders = [
  { value: 's3', label: 'Amazon S3' },
  { value: 'gcs', label: 'Google Cloud Storage' },
  { value: 'azure', label: 'Azure Blob Storage' },
  { value: 'local', label: 'Local Storage' },
  { value: 'minio', label: 'MinIO' }
]

const compressionOptions = [
  { value: 'none', label: 'No Compression' },
  { value: 'low', label: 'Low Compression' },
  { value: 'medium', label: 'Medium Compression' },
  { value: 'high', label: 'High Compression' }
]

// Utility functions
const formatTime = (date: Date) => {
  return date.toLocaleString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// Methods
const saveSettings = async () => {
  try {
    saving.value = true

    // Simulate API call to save settings
    await new Promise(resolve => setTimeout(resolve, 2000))

    // In a real app, you would send the settings to the API
    // await $api.put('/platform/settings', settings)

    lastSaved.value = new Date()
    console.log('Settings saved successfully')

    // Show success notification
    // useToast().success('Settings saved successfully')

  } catch (error) {
    console.error('Error saving settings:', error)
    // useToast().error('Failed to save settings')
  } finally {
    saving.value = false
  }
}

const resetSettings = async () => {
  const confirmed = confirm('Are you sure you want to reset all settings to defaults? This action cannot be undone.')
  if (!confirmed) return

  try {
    // Reset to default values
    await loadDefaultSettings()
    console.log('Settings reset to defaults')

  } catch (error) {
    console.error('Error resetting settings:', error)
  }
}

const exportSettings = () => {
  try {
    const dataStr = JSON.stringify(settings, null, 2)
    const dataBlob = new Blob([dataStr], { type: 'application/json' })
    const url = URL.createObjectURL(dataBlob)

    const link = document.createElement('a')
    link.href = url
    link.download = `platform-settings-${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    URL.revokeObjectURL(url)
    console.log('Settings exported successfully')

  } catch (error) {
    console.error('Error exporting settings:', error)
  }
}

const testEmailConnection = async () => {
  try {
    testingEmail.value = true

    // Simulate testing email connection
    await new Promise(resolve => setTimeout(resolve, 2000))

    // In a real app, you would test the SMTP connection
    // await $api.post('/platform/settings/email/test-connection', settings.email)

    console.log('Email connection test successful')
    // useToast().success('Email connection test successful')

  } catch (error) {
    console.error('Email connection test failed:', error)
    // useToast().error('Email connection test failed')
  } finally {
    testingEmail.value = false
  }
}

const sendTestEmail = async () => {
  try {
    sendingTestEmail.value = true

    // Simulate sending test email
    await new Promise(resolve => setTimeout(resolve, 2000))

    // In a real app, you would send a test email
    // await $api.post('/platform/settings/email/send-test', settings.email)

    console.log('Test email sent successfully')
    // useToast().success('Test email sent successfully')

  } catch (error) {
    console.error('Failed to send test email:', error)
    // useToast().error('Failed to send test email')
  } finally {
    sendingTestEmail.value = false
  }
}

const loadDefaultSettings = async () => {
  // In a real app, you would fetch default settings from the API
  // const defaults = await $api.get('/platform/settings/defaults')

  // For now, we'll just reset to the initial values
  Object.assign(settings, {
    general: {
      platformName: 'Legal SaaS Platform',
      platformUrl: 'https://legal-saas.example.com',
      defaultTimezone: 'UTC',
      defaultLanguage: 'en',
      supportEmail: '<EMAIL>',
      maxFileSize: 100,
      enableRegistration: true,
      enableEmailVerification: true,
      enableTwoFactor: true,
      enableMaintenanceMode: false
    },
    // ... other default settings
  })
}

// Lifecycle hooks
onMounted(async () => {
  // Load current settings from API
  // const currentSettings = await $api.get('/platform/settings')
  // Object.assign(settings, currentSettings)

  console.log('Platform settings loaded')
})
</script>

<style scoped>
/* Tab navigation styles */
.border-brandPrimary {
  border-color: var(--color-brandPrimary-600);
}

.text-brandPrimary {
  color: var(--color-brandPrimary-600);
}

/* Form field spacing */
.space-y-6 > * + * {
  margin-top: 1.5rem;
}

.space-y-3 > * + * {
  margin-top: 0.75rem;
}

/* Grid responsive adjustments */
@media (max-width: 768px) {
  .grid-cols-1.md\:grid-cols-2 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
}

/* Transition effects */
.transition-colors {
  transition-property: color, background-color, border-color;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 200ms;
}

/* Focus states for accessibility */
button:focus,
input:focus,
select:focus {
  outline: 2px solid var(--color-brandPrimary-500);
  outline-offset: 2px;
}

/* Loading state styles */
.opacity-50 {
  opacity: 0.5;
}

/* Hover effects */
button:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
  .border-gray-200 {
    border-color: rgb(55 65 81);
  }

  .text-gray-500 {
    color: rgb(156 163 175);
  }

  .bg-gray-50 {
    background-color: rgb(55 65 81);
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .border-gray-200 {
    border-color: #000;
  }

  .text-gray-500 {
    color: #000;
  }
}
</style>
