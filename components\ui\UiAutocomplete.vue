<template>
  <div class="relative">
    <input
      type="text"
      v-model="searchQuery"
      @input="handleInput"
      @focus="handleFocus"
      @blur="handleBlur"
      class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
      :placeholder="placeholder"
    />
    <ul
      v-if="showSuggestions && (suggestions.length > 0 || loading || (loadingMore && suggestions.length > 0))"
      class="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto"
    >
      <li
        v-for="(suggestion, index) in suggestions"
        :key="getSuggestionValue(suggestion) || index"
        @mousedown="selectSuggestion(suggestion)"
        class="cursor-pointer hover:bg-gray-100"
      >
        <!-- Use a scoped slot for custom rendering, fallback to default -->
        <slot name="suggestion" :suggestion="suggestion" :index="index">
          <div class="px-3 py-2">
            {{ getSuggestionDisplay(suggestion) }}
          </div>
        </slot>
      </li>
      <!-- Load More Button/Indicator -->
      <li v-if="loadingMore" class="px-3 py-2 text-center text-sm text-gray-500">
        Loading more...
      </li>
      <li v-else-if="!loading && currentPage < totalPages" class="px-3 py-2 text-center">
        <button
          @click.stop="loadMoreItems"
          class="w-full text-sm text-indigo-600 hover:text-indigo-800 focus:outline-none"
        >
          Load More
        </button>
      </li>
    </ul>
    <div
      v-if="showSuggestions && suggestions.length === 0 && searchQuery && !loading && !loadingMore"
      class="absolute z-10 w-full mt-1 px-3 py-2 bg-white border border-gray-300 rounded-md shadow-lg text-sm text-gray-500"
    >
      No results found.
    </div>
    <div
      v-if="loading"
      class="absolute z-10 w-full mt-1 px-3 py-2 bg-white border border-gray-300 rounded-md shadow-lg text-sm text-gray-500"
    >
      Loading...
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { debounce } from 'lodash-es';

// Generic suggestion item type, actual structure depends on API response
interface SuggestionItem {
  [key: string]: any; // Allow any properties
}

const props = defineProps({
  modelValue: {
    type: [String, Number, Object], // Can be an ID or the full object
    default: null,
  },
  apiUrl: {
    type: String,
    required: true,
  },
  queryParam: { // Name of the query parameter for search text
    type: String,
    default: 'q',
  },
  pageQueryParam: { // Name of the query parameter for page number
    type: String,
    default: 'page',
  },
  limitQueryParam: { // Name of the query parameter for items per page
    type: String,
    default: 'limit',
  },
  itemsPerPage: {
    type: Number,
    default: 10,
  },
  displayKey: { // Key in the suggestion item for display text
    type: String,
    default: 'name',
  },
  valueKey: { // Key in the suggestion item for the actual value (e.g., 'id')
    type: String,
    default: 'id',
  },
  responseItemsPath: { // Path to items array in API response
    type: String,
    default: 'data',
  },
  responseMetaPath: { // Path to meta object in API response
    type: String,
    default: 'meta',
  },
  placeholder: {
    type: String,
    default: 'Search...',
  },
  debounceMs: {
    type: Number,
    default: 300,
  },
});

const emit = defineEmits(['update:modelValue']);

const searchQuery = ref(''); // Text in the input field
const suggestions = ref<SuggestionItem[]>([]); // List of suggestions shown in dropdown
const selectedItem = ref<SuggestionItem | null>(null); // The full selected item object

const showSuggestions = ref(false);
const loading = ref(false);
const loadingMore = ref(false); // For loading subsequent pages

const currentPage = ref(1);
const totalItems = ref(0);
const totalPages = ref(0);

const { $api } = useNuxtApp();

// Helper to get nested property
const getNestedValue = (obj: any, path: string) => {
  return path.split('.').reduce((acc, part) => acc && acc[part], obj);
};

const getSuggestionDisplay = (suggestion: SuggestionItem): string => {
  if (suggestion && typeof suggestion === 'object' && props.displayKey) {
    const displayValue = getNestedValue(suggestion, props.displayKey);
    return displayValue !== undefined && displayValue !== null ? String(displayValue) : '';
  }
  return String(suggestion); // Fallback for simple string arrays (though not expected with new structure)
};

const getSuggestionValue = (suggestion: SuggestionItem): any => {
  if (suggestion && typeof suggestion === 'object' && props.valueKey) {
    return getNestedValue(suggestion, props.valueKey);
  }
  return suggestion; // Fallback
};


const fetchSuggestionsDebounced = debounce(async (isLoadMore = false) => {
  if (!isLoadMore) {
    currentPage.value = 1; // Reset to page 1 for new searches
    loading.value = true;
  } else {
    loadingMore.value = true;
  }

  const params: Record<string, any> = {
    [props.pageQueryParam]: currentPage.value,
    [props.limitQueryParam]: props.itemsPerPage,
  };
  if (searchQuery.value) {
    params[props.queryParam] = searchQuery.value;
  }

  try {
    const response = await $api.get<{data: any}>(props.apiUrl, { params });

    const items = getNestedValue(response.data, props.responseItemsPath) || [];
    const meta = getNestedValue(response.data, props.responseMetaPath) || {};
    
    
    if (!Array.isArray(items)) {
        console.error('Fetched items are not an array:', items);
        suggestions.value = isLoadMore ? suggestions.value : []; // Keep existing on loadMore error
        return;
    }

    if (isLoadMore) {
      suggestions.value = [...suggestions.value, ...items];
    } else {
      suggestions.value = items;
      // Try to set initial display text if modelValue is present and item is in the first page
      if (props.modelValue && suggestions.value.length > 0) {
        const currentModelValue = typeof props.modelValue === 'object' ? getNestedValue(props.modelValue, props.valueKey) : props.modelValue;
        const matched = suggestions.value.find(s => getSuggestionValue(s) === currentModelValue);
        if (matched) {
          selectedItem.value = matched;
          // searchQuery.value = getSuggestionDisplay(matched); // This might overwrite user typing
        }
      }
    }

    totalItems.value = meta.total || 0;
    totalPages.value = meta.totalPages || Math.ceil(totalItems.value / props.itemsPerPage) || 0;

  } catch (error) {
    console.error('Error fetching suggestions:', error);
    if (!isLoadMore) suggestions.value = [];
  } finally {
    loading.value = false;
    loadingMore.value = false;
  }
}, props.debounceMs);


const triggerFetch = (isLoadMore = false) => {
  // For new search, allow empty query on focus, otherwise require some input
  if (!isLoadMore && !searchQuery.value && suggestions.value.length === 0 && !showSuggestions.value) {
     // This condition means it's likely an initial focus on an empty input, fetch initial page
     fetchSuggestionsDebounced(false);
  } else if (!isLoadMore && searchQuery.value) {
    fetchSuggestionsDebounced(false);
  } else if (isLoadMore) {
     fetchSuggestionsDebounced(true);
  } else if (!isLoadMore && !searchQuery.value && showSuggestions.value) {
    // Focused, empty query, show current suggestions or fetch if empty
    if (suggestions.value.length === 0) fetchSuggestionsDebounced(false);
  }
};


const handleFocus = () => {
  showSuggestions.value = true;
  if (suggestions.value.length === 0 && !loading.value) { // Fetch initial if list is empty
    triggerFetch(false);
  }
};

const handleInput = () => {
  selectedItem.value = null; // Clear full selection on new input
  emit('update:modelValue', null); // Clear model value as user is typing new search
  triggerFetch(false);
};

const loadMoreItems = () => {
  if (currentPage.value < totalPages.value && !loadingMore.value) {
    currentPage.value++;
    triggerFetch(true); // true indicates loadMore
  }
};

const selectSuggestion = (suggestion: SuggestionItem) => {
  selectedItem.value = suggestion;
  searchQuery.value = getSuggestionDisplay(suggestion);
  emit('update:modelValue', getSuggestionValue(suggestion));
  showSuggestions.value = false;
  // suggestions.value = []; // Don't clear suggestions, user might want to re-open and see them
};

const handleBlur = () => {
  setTimeout(() => {
    // If an item was selected, ensure searchQuery reflects it.
    // If not, and searchQuery doesn't match any suggestion, consider clearing modelValue.
    if (selectedItem.value) {
        if(searchQuery.value !== getSuggestionDisplay(selectedItem.value)) {
            // This case should ideally not happen if selection logic is correct
            // searchQuery.value = getSuggestionDisplay(selectedItem.value);
        }
    } else if (searchQuery.value && props.modelValue) {
        // User typed something but didn't select, and there was a modelValue.
        // Check if current searchQuery matches any existing suggestion's display.
        // If not, it implies the input is "invalid" relative to selection.
        // For now, we won't auto-clear, but this is a place for more advanced logic.
    }
    showSuggestions.value = false;
  }, 200); // Increased delay to allow "Load More" click
};

watch(() => props.modelValue, (newValue) => {
  if (newValue === null || newValue === undefined) {
    if (!selectedItem.value) searchQuery.value = ''; // Clear search query if model is cleared externally & no item selected
    selectedItem.value = null;
  } else {
    // If modelValue is set externally, try to find and display the item.
    // This is tricky if the item isn't in the currently loaded suggestions.
    // A robust solution might require a separate fetch for the specific item by its ID (newValue).
    const currentModelValue = typeof newValue === 'object' ? getNestedValue(newValue, props.valueKey) : newValue;
    const existingSuggestion = suggestions.value.find(s => getSuggestionValue(s) === currentModelValue);
    if (existingSuggestion) {
      if (!selectedItem.value || getSuggestionValue(selectedItem.value) !== currentModelValue) {
        selectedItem.value = existingSuggestion;
      }
      if (searchQuery.value !== getSuggestionDisplay(existingSuggestion)) {
        searchQuery.value = getSuggestionDisplay(existingSuggestion);
      }
    } else if (typeof newValue === 'object') {
        // If modelValue is an object, use it directly
        selectedItem.value = newValue as SuggestionItem;
        searchQuery.value = getSuggestionDisplay(newValue as SuggestionItem);
    } else {
      // modelValue is an ID, but item not found in current suggestions.
      // Set searchQuery to the ID for now, or fetch the item.
      // For simplicity, if it's just an ID, we might not have a display value without fetching.
      // searchQuery.value = String(currentModelValue); // Or clear: searchQuery.value = '';
      // To make this robust, you'd fetch the item by ID here if not found.
      // For now, if an ID is passed and not found, input might show ID or be blank.
      // Let's try to fetch if suggestions are empty, assuming it might be an initial load.
      if (suggestions.value.length === 0 && !loading.value && props.apiUrl) {
          // This is a simplified attempt to load initial data if modelValue is set.
          // A more targeted fetch for the specific ID would be better.
          // For now, we'll set searchQuery to the ID and let user search if needed.
          searchQuery.value = String(currentModelValue);
          // fetchSuggestionsDebounced(false); // Could trigger a general fetch
      } else if (!selectedItem.value) { // if no item is selected, reflect the modelValue
          searchQuery.value = String(currentModelValue);
      }
    }
  }
}, { immediate: true, deep: true });

</script>

<style scoped>
/* Add any component-specific styles here */
</style>
