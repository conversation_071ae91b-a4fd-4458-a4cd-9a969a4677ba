<!--
  Templates List View Component
  
  Displays a list of legal document templates with filtering
  and template management capabilities
-->

<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">{{ t('templates.templates') }}</h1>
        <p class="mt-1 text-sm text-gray-500">
          {{ t('templates.templateManagement') }}
        </p>
      </div>

      <div class="mt-4 sm:mt-0">
        <NuxtLink
          to="/dashboard/templates/create"
          class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200"
        >
          <Icon name="heroicons:plus" class="ltr:h-4 ltr:w-4 ltr:mr-2 rtl:h-4 rtl:w-4 rtl:ml-2" />
          {{ t('templates.createTemplate') }}
        </NuxtLink>
      </div>
    </div>

    <!-- Filters and Search -->
    <div class="bg-white shadow rounded-lg p-6">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <!-- Search -->
        <div class="md:col-span-2">
          <label for="search" class="sr-only">Search templates</label>
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Icon name="heroicons:magnifying-glass" class="h-5 w-5 text-gray-400" />
            </div>
            <input
              id="search"
              v-model="searchQuery"
              type="text"
              placeholder="Search templates..."
              class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
            />
          </div>
        </div>

        <!-- Category Filter -->
        <div>
          <label for="category" class="sr-only">Filter by category</label>
          <select
            id="category"
            v-model="selectedCategory"
            class="block w-full px-3 py-2 border border-gray-300 rounded-md leading-5 bg-white focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
          >
            <option value="">All Categories</option>
            <option value="contract">Contracts</option>
            <option value="legal-brief">Legal Briefs</option>
            <option value="correspondence">Correspondence</option>
            <option value="form">Forms</option>
            <option value="agreement">Agreements</option>
            <option value="other">Other</option>
          </select>
        </div>

        <!-- Status Filter -->
        <div>
          <label for="status" class="sr-only">Filter by status</label>
          <select
            id="status"
            v-model="selectedStatus"
            class="block w-full px-3 py-2 border border-gray-300 rounded-md leading-5 bg-white focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
          >
            <option value="">All Statuses</option>
            <option value="active">Active</option>
            <option value="draft">Draft</option>
            <option value="archived">Archived</option>
          </select>
        </div>
      </div>
    </div>

    <!-- Templates Grid -->
    <div class="bg-white shadow rounded-lg overflow-hidden">
      <!-- Loading State -->
      <div v-if="isLoading" class="p-8 text-center">
        <UiSpinner size="lg" />
        <p class="mt-2 text-sm text-gray-500">Loading templates...</p>
      </div>

      <!-- Empty State -->
      <div v-else-if="filteredTemplates.length === 0" class="p-8 text-center">
        <Icon name="heroicons:clipboard-document-list" class="mx-auto h-12 w-12 text-gray-400" />
        <h3 class="mt-2 text-sm font-medium text-gray-900">{{ t('common.noItemsFound') }}</h3>
        <p class="mt-1 text-sm text-gray-500">
          {{ searchQuery ? t('common.adjustSearchCriteria') : t('templates.getStartedFirstTemplate') }}
        </p>
        <div class="mt-6" v-if="!searchQuery">
          <NuxtLink
            to="/dashboard/templates/create"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-indigo-600 bg-indigo-100 hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200"
          >
            <Icon name="heroicons:plus" class="ltr:h-4 ltr:w-4 ltr:mr-2 rtl:h-4 rtl:w-4 rtl:ml-2" />
            {{ t('templates.createTemplate') }}
          </NuxtLink>
        </div>
      </div>

      <!-- Templates Grid -->
      <div v-else class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div
            v-for="template in paginatedTemplates"
            :key="template.id"
            class="group relative bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow duration-200"
          >
            <!-- Template Header -->
            <div class="flex items-start justify-between mb-4">
              <div class="flex-1">
                <h3 class="text-lg font-medium text-gray-900 group-hover:text-indigo-600 transition-colors duration-200">
                  {{ template.name }}
                </h3>
                <p class="text-sm text-gray-500 mt-1">{{ template.description }}</p>
              </div>
              
              <!-- Status Badge -->
              <span :class="getStatusBadgeClass(template.status)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                {{ template.status }}
              </span>
            </div>

            <!-- Template Info -->
            <div class="space-y-2 mb-4">
              <div class="flex items-center text-sm text-gray-500">
                <Icon name="heroicons:tag" class="h-4 w-4 mr-2" />
                {{ formatCategory(template.category) }}
              </div>
              <div class="flex items-center text-sm text-gray-500">
                <Icon name="heroicons:calendar" class="h-4 w-4 mr-2" />
                Updated {{ formatDate(template.updatedAt) }}
              </div>
              <div class="flex items-center text-sm text-gray-500">
                <Icon name="heroicons:document-duplicate" class="h-4 w-4 mr-2" />
                Used {{ template.usageCount }} times
              </div>
            </div>

            <!-- Template Actions -->
            <div class="flex items-center justify-between pt-4 border-t border-gray-200">
              <div class="flex space-x-2">
                <button
                  @click="useTemplate(template)"
                  class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200"
                >
                  <Icon name="heroicons:document-plus" class="h-3 w-3 mr-1" />
                  Use Template
                </button>
                
                <NuxtLink
                  :to="`/dashboard/templates/editor?id=${template.id}`"
                  class="inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200"
                >
                  <Icon name="heroicons:pencil" class="h-3 w-3 mr-1" />
                  Edit
                </NuxtLink>
              </div>

              <!-- More Actions -->
              <div class="relative">
                <button
                  @click="toggleActionsMenu(template.id)"
                  class="p-1 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                >
                  <Icon name="heroicons:ellipsis-vertical" class="h-4 w-4" />
                </button>

                <!-- Actions Dropdown -->
                <div
                  v-if="activeActionsMenu === template.id"
                  class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 z-10"
                >
                  <div class="py-1">
                    <button
                      @click="duplicateTemplate(template)"
                      class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      <Icon name="heroicons:document-duplicate" class="h-4 w-4 mr-2 inline" />
                      Duplicate
                    </button>
                    <button
                      @click="exportTemplate(template)"
                      class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      <Icon name="heroicons:arrow-down-tray" class="h-4 w-4 mr-2 inline" />
                      Export
                    </button>
                    <button
                      @click="archiveTemplate(template)"
                      class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      <Icon name="heroicons:archive-box" class="h-4 w-4 mr-2 inline" />
                      {{ template.status === 'archived' ? 'Unarchive' : 'Archive' }}
                    </button>
                    <button
                      @click="deleteTemplate(template)"
                      class="block w-full text-left px-4 py-2 text-sm text-red-700 hover:bg-red-50"
                    >
                      <Icon name="heroicons:trash" class="h-4 w-4 mr-2 inline" />
                      Delete
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Pagination -->
        <div v-if="totalPages > 1" class="mt-6 flex items-center justify-between border-t border-gray-200 pt-6">
          <div class="flex-1 flex justify-between sm:hidden">
            <button
              :disabled="currentPage === 1"
              @click="currentPage--"
              class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Previous
            </button>
            <button
              :disabled="currentPage === totalPages"
              @click="currentPage++"
              class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Next
            </button>
          </div>
          <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p class="text-sm text-gray-700">
                Showing {{ ((currentPage - 1) * itemsPerPage) + 1 }} to {{ Math.min(currentPage * itemsPerPage, filteredTemplates.length) }} of {{ filteredTemplates.length }} results
              </p>
            </div>
            <div>
              <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                <button
                  :disabled="currentPage === 1"
                  @click="currentPage--"
                  class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <Icon name="heroicons:chevron-left" class="h-5 w-5" />
                </button>
                <button
                  v-for="page in visiblePages"
                  :key="page"
                  @click="currentPage = page"
                  :class="[
                    page === currentPage
                      ? 'z-10 bg-indigo-50 border-indigo-500 text-indigo-600'
                      : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50',
                    'relative inline-flex items-center px-4 py-2 border text-sm font-medium'
                  ]"
                >
                  {{ page }}
                </button>
                <button
                  :disabled="currentPage === totalPages"
                  @click="currentPage++"
                  class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <Icon name="heroicons:chevron-right" class="h-5 w-5" />
                </button>
              </nav>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'

// Composables
const router = useRouter()
const { t } = useI18n()

// Mock data
const mockTemplates = [
  {
    id: '1',
    name: 'Service Agreement Template',
    description: 'Standard service agreement for client engagements',
    category: 'contract',
    status: 'active',
    usageCount: 15,
    updatedAt: '2024-01-15T10:30:00Z'
  },
  {
    id: '2',
    name: 'NDA Template',
    description: 'Non-disclosure agreement template',
    category: 'agreement',
    status: 'active',
    usageCount: 8,
    updatedAt: '2024-01-14T14:20:00Z'
  }
]

// State
const isLoading = ref(false)
const templates = ref(mockTemplates)
const searchQuery = ref('')
const selectedCategory = ref('')
const selectedStatus = ref('')
const currentPage = ref(1)
const itemsPerPage = ref(9)
const activeActionsMenu = ref<string | null>(null)

// Computed
const filteredTemplates = computed(() => {
  return templates.value.filter(template => {
    const matchesSearch = !searchQuery.value || 
      template.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      template.description.toLowerCase().includes(searchQuery.value.toLowerCase())
    
    const matchesCategory = !selectedCategory.value || template.category === selectedCategory.value
    const matchesStatus = !selectedStatus.value || template.status === selectedStatus.value
    
    return matchesSearch && matchesCategory && matchesStatus
  })
})

const totalPages = computed(() => Math.ceil(filteredTemplates.value.length / itemsPerPage.value))

const paginatedTemplates = computed(() => {
  const start = (currentPage.value - 1) * itemsPerPage.value
  const end = start + itemsPerPage.value
  return filteredTemplates.value.slice(start, end)
})

const visiblePages = computed(() => {
  const pages = []
  const start = Math.max(1, currentPage.value - 2)
  const end = Math.min(totalPages.value, start + 4)
  
  for (let i = start; i <= end; i++) {
    pages.push(i)
  }
  
  return pages
})

// Methods
const getStatusBadgeClass = (status: string) => {
  const classes = {
    active: 'bg-green-100 text-green-800',
    draft: 'bg-yellow-100 text-yellow-800',
    archived: 'bg-gray-100 text-gray-800'
  }
  return classes[status as keyof typeof classes] || 'bg-gray-100 text-gray-800'
}

const formatCategory = (category: string) => {
  const categories = {
    contract: 'Contract',
    'legal-brief': 'Legal Brief',
    correspondence: 'Correspondence',
    form: 'Form',
    agreement: 'Agreement',
    other: 'Other'
  }
  return categories[category as keyof typeof categories] || category
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

const toggleActionsMenu = (templateId: string) => {
  activeActionsMenu.value = activeActionsMenu.value === templateId ? null : templateId
}

const useTemplate = (template: any) => {
  console.log('Using template:', template.name)
  // Navigate to document creation with template
  router.push(`/dashboard/documents/create?template=${template.id}`)
}

const duplicateTemplate = (template: any) => {
  console.log('Duplicating template:', template.name)
  activeActionsMenu.value = null
  // Implement duplication logic
}

const exportTemplate = (template: any) => {
  console.log('Exporting template:', template.name)
  activeActionsMenu.value = null
  // Implement export logic
}

const archiveTemplate = (template: any) => {
  console.log('Archiving template:', template.name)
  activeActionsMenu.value = null
  // Implement archive logic
}

const deleteTemplate = (template: any) => {
  if (confirm(`Are you sure you want to delete "${template.name}"?`)) {
    console.log('Deleting template:', template.name)
    activeActionsMenu.value = null
    // Implement delete logic
  }
}

// Lifecycle
onMounted(() => {
  // Load templates
})
</script>
