<template>
  <div class="email-channel-editor space-y-6">
    <!-- Email Settings -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
      <UiInput
        v-model="localContent.fromName"
        label="From Name"
        placeholder="Your Organization"
        @input="handleContentChange"
      />
      <UiInput
        v-model="localContent.fromEmail"
        label="From Email"
        placeholder="<EMAIL>"
        type="email"
        @input="handleContentChange"
      />
    </div>

    <!-- Subject Line -->
    <div class="space-y-2">
      <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
        Subject Line
      </label>
      <div class="relative">
        <UiInput
          v-model="localContent.subject"
          placeholder="Enter email subject..."
          @input="handleContentChange"
        />
        <UiButton
          @click="showVariableModal = true"
          variant="ghost"
          size="sm"
          class="absolute right-2 top-1/2 transform -translate-y-1/2"
        >
          <Icon name="heroicons:variable" class="w-4 h-4" />
        </UiButton>
      </div>
      <div class="text-xs text-gray-500 dark:text-gray-400">
        Character count: {{ subjectLength }} (recommended: 30-50 characters)
      </div>
    </div>

    <!-- Editor Mode Toggle -->
    <div class="flex items-center justify-between">
      <h3 class="text-lg font-medium text-gray-900 dark:text-white">Email Content</h3>
      <div class="flex items-center space-x-2">
        <UiButton
          @click="showTemplateLibrary = true"
          variant="outline"
          size="sm"
        >
          <Icon name="heroicons:book-open" class="w-4 h-4 mr-2" />
          Templates
        </UiButton>
        <div class="flex rounded-lg border border-gray-300 dark:border-gray-600">
          <button
            @click="editorMode = 'visual'"
            :class="[
              'px-3 py-1 text-sm font-medium rounded-l-lg transition-colors',
              editorMode === 'visual'
                ? 'bg-blue-600 text-white'
                : 'bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700'
            ]"
          >
            Visual
          </button>
          <button
            @click="editorMode = 'html'"
            :class="[
              'px-3 py-1 text-sm font-medium rounded-r-lg transition-colors',
              editorMode === 'html'
                ? 'bg-blue-600 text-white'
                : 'bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700'
            ]"
          >
            HTML
          </button>
        </div>
      </div>
    </div>

    <!-- Visual Editor -->
    <div v-if="editorMode === 'visual'" class="border border-gray-300 dark:border-gray-600 rounded-lg overflow-hidden">
      <div class="bg-gray-50 dark:bg-gray-800 px-4 py-2 border-b border-gray-300 dark:border-gray-600">
        <div class="flex items-center space-x-2">
          <button @click="formatText('bold')" class="p-1 rounded hover:bg-gray-200 dark:hover:bg-gray-700">
            <Icon name="heroicons:bold" class="w-4 h-4" />
          </button>
          <button @click="formatText('italic')" class="p-1 rounded hover:bg-gray-200 dark:hover:bg-gray-700">
            <Icon name="heroicons:italic" class="w-4 h-4" />
          </button>
          <button @click="formatText('underline')" class="p-1 rounded hover:bg-gray-200 dark:hover:bg-gray-700">
            <Icon name="heroicons:underline" class="w-4 h-4" />
          </button>
          <div class="w-px h-4 bg-gray-300 dark:bg-gray-600"></div>
          <button @click="insertLink" class="p-1 rounded hover:bg-gray-200 dark:hover:bg-gray-700">
            <Icon name="heroicons:link" class="w-4 h-4" />
          </button>
          <button @click="showVariableModal = true" class="p-1 rounded hover:bg-gray-200 dark:hover:bg-gray-700">
            <Icon name="heroicons:variable" class="w-4 h-4" />
          </button>
        </div>
      </div>
      <div
        ref="visualEditor"
        contenteditable="true"
        @input="handleVisualEditorInput"
        @paste="handlePaste"
        class="min-h-[300px] p-4 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-inset bg-white dark:bg-gray-900"
        :class="[
          'prose prose-sm max-w-none',
          'dark:prose-invert dark:text-gray-100'
        ]"
        v-html="localContent.content"
      ></div>
    </div>

    <!-- HTML Editor -->
    <div v-else class="space-y-2">
      <UiTextarea
        v-model="localContent.content"
        placeholder="Enter HTML content..."
        :rows="12"
        class="font-mono text-sm"
        @input="handleContentChange"
      />
    </div>

    <!-- Email Settings -->
    <UiCard class="p-4">
      <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-4">Email Settings</h4>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Priority
          </label>
          <select
            v-model="localContent.settings.priority"
            @change="handleContentChange"
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm"
          >
            <option value="low">Low</option>
            <option value="normal">Normal</option>
            <option value="high">High</option>
          </select>
        </div>
        <div class="space-y-3">
          <label class="flex items-center">
            <input
              type="checkbox"
              v-model="localContent.settings.trackOpens"
              @change="handleContentChange"
              class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Track email opens</span>
          </label>
          <label class="flex items-center">
            <input
              type="checkbox"
              v-model="localContent.settings.trackClicks"
              @change="handleContentChange"
              class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Track link clicks</span>
          </label>
          <label class="flex items-center">
            <input
              type="checkbox"
              v-model="localContent.settings.enableUnsubscribe"
              @change="handleContentChange"
              class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Include unsubscribe link</span>
          </label>
        </div>
      </div>
    </UiCard>

    <!-- Template Library Modal -->
    <EmailTemplateLibraryModal
      v-if="showTemplateLibrary"
      @close="showTemplateLibrary = false"
      @select="insertTemplate"
    />

    <!-- Variable Insert Modal -->
    <VariableInsertModal
      v-if="showVariableModal"
      @close="showVariableModal = false"
      @insert="insertVariable"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, defineAsyncComponent } from 'vue'

// Lazy load modals
const EmailTemplateLibraryModal = defineAsyncComponent(() => import('../../template-modals/EmailTemplateLibraryModal.vue'))
const VariableInsertModal = defineAsyncComponent(() => import('../../template-modals/VariableInsertModal.vue'))

// Props
interface Props {
  content: any
  variables: string[]
  templateData: any
  category?: any
}

const props = withDefaults(defineProps<Props>(), {
  content: () => ({}),
  variables: () => [],
})

// Emits
const emit = defineEmits<{
  'update:content': [content: any]
  'update:variables': [variables: string[]]
  'content-change': [content: any, variables: string[]]
}>()

// State
const editorMode = ref<'visual' | 'html'>('visual')
const showTemplateLibrary = ref(false)
const showVariableModal = ref(false)
const visualEditor = ref<HTMLElement>()

// Local content with defaults
const localContent = ref({
  fromName: '',
  fromEmail: '',
  subject: '',
  content: '',
  settings: {
    priority: 'normal',
    trackOpens: true,
    trackClicks: true,
    enableUnsubscribe: true,
  },
  ...props.content
})

const localVariables = ref([...props.variables])

// Computed
const subjectLength = computed(() => localContent.value.subject?.length || 0)

// Watchers
watch(() => props.content, (newContent) => {
  localContent.value = {
    fromName: '',
    fromEmail: '',
    subject: '',
    content: '',
    settings: {
      priority: 'normal',
      trackOpens: true,
      trackClicks: true,
      enableUnsubscribe: true,
    },
    ...newContent
  }
}, { deep: true, immediate: true })

watch(() => props.variables, (newVariables) => {
  localVariables.value = [...newVariables]
}, { immediate: true })

// Methods
const handleContentChange = () => {
  emit('update:content', localContent.value)
  emit('content-change', localContent.value, localVariables.value)
}

const handleVisualEditorInput = () => {
  if (visualEditor.value) {
    localContent.value.content = visualEditor.value.innerHTML
    handleContentChange()
  }
}

const handlePaste = (event: ClipboardEvent) => {
  event.preventDefault()
  const text = event.clipboardData?.getData('text/plain') || ''
  document.execCommand('insertText', false, text)
}

const formatText = (command: string) => {
  document.execCommand(command, false)
  handleVisualEditorInput()
}

const insertLink = () => {
  const url = prompt('Enter URL:')
  if (url) {
    document.execCommand('createLink', false, url)
    handleVisualEditorInput()
  }
}

const insertTemplate = (template: any) => {
  localContent.value.subject = template.subject || localContent.value.subject
  localContent.value.content = template.content || localContent.value.content
  
  // Merge variables
  if (template.variables) {
    const newVariables = [...new Set([...localVariables.value, ...template.variables])]
    localVariables.value = newVariables
    emit('update:variables', newVariables)
  }
  
  showTemplateLibrary.value = false
  handleContentChange()
}

const insertVariable = (variable: string) => {
  const variableText = `{${variable}}`
  
  if (editorMode.value === 'visual' && visualEditor.value) {
    document.execCommand('insertText', false, variableText)
    handleVisualEditorInput()
  } else {
    localContent.value.content += variableText
    handleContentChange()
  }
  
  // Add variable to list if not already present
  if (!localVariables.value.includes(variable)) {
    localVariables.value.push(variable)
    emit('update:variables', localVariables.value)
  }
  
  showVariableModal.value = false
}

// Initialize
onMounted(() => {
  if (visualEditor.value && localContent.value.content) {
    visualEditor.value.innerHTML = localContent.value.content
  }
})
</script>
