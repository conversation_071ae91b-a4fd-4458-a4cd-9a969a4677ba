<template>
  <TransitionRoot :show="show" as="template">
    <div class="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
      <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <TransitionChild
          as="template"
          enter="ease-out duration-300"
          enter-from="opacity-0"
          enter-to="opacity-100"
          leave="ease-in duration-200"
          leave-from="opacity-100"
          leave-to="opacity-0"
        >
          <div class="fixed inset-0 bg-gray-900/75 transition-opacity" @click="handleOverlayClick"></div>
        </TransitionChild>

        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

        <TransitionChild
          as="template"
          enter="ease-out duration-300"
          enter-from="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
          enter-to="opacity-100 translate-y-0 sm:scale-100"
          leave="ease-in duration-200"
          leave-from="opacity-100 translate-y-0 sm:scale-100"
          leave-to="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
        >
          <div :class="['relative inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle z-9 min-w-md  ', widthClass]">
            <button
              v-if="showCloseButton"
              type="button"
              class="absolute top-0 right-0 mt-4 mr-4 text-gray-400 hover:text-gray-500 focus:outline-none"
              @click="closeModal"
            >
              <span class="sr-only">Close</span>
              <IconXCircle class="h-6 w-6" aria-hidden="true" />
            </button>
            <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
              <div class="sm:flex sm:items-start">
                <div v-if="icon" :class="['mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full sm:mx-0 sm:h-10 sm:w-10', iconBgClass]">
                  <component :is="icon" class="h-6 w-6" :class="iconColorClass" aria-hidden="true" />
                </div>
                
                <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                  <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">
                    {{ title }}
                    
                  </h3>
                  <p class="text-gray-700 mb-4 PY-2" v-if="subtitle"> {{ subtitle }} </p>
                   
                  <div class="mt-2">
                     
                    <slot></slot>
                  </div>
                </div>
              </div>
            </div>
           
            <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                
              <UiButton v-if="confirmButtonText" @click="handleConfirm" :variant="confirmButtonVariant" :loading="loadingConfirm" class="ml-3">{{ confirmButtonText }}</UiButton>
              <UiButton v-if="cancelButtonText" @click="handleCancel" variant="secondary">{{ cancelButtonText }}</UiButton>
            </div>
          </div>
        </TransitionChild>
      </div>
    </div>
  </TransitionRoot>
</template>

<script setup lang="ts">
import { defineProps, defineEmits, computed, withDefaults } from 'vue';
import { TransitionRoot, TransitionChild } from '@headlessui/vue';
// UiButton is auto-imported by Nuxt
import IconInfo from '../icons/IconInfo.vue';
import IconCheckCircle from '../icons/IconCheckCircle.vue';
import IconXCircle from '../icons/IconXCircle.vue';

const props = withDefaults(defineProps<{
  show: boolean;
  title: string;
  subtitle?: string;
  confirmButtonText?: string;
  cancelButtonText?: string;
  confirmButtonVariant?: 'primary' | 'danger';
  loadingConfirm?: boolean;
  persistent?: boolean;
  icon?: any; // A Vue component for the icon
  iconType?: 'info' | 'success' | 'error';
  width?: 'sm' | 'md' | 'lg' | 'xl';
  showCloseButton?: boolean;
}>(), {
  showCloseButton: true,
});

const emit = defineEmits(['update:show', 'confirm', 'cancel', 'close']);

const handleOverlayClick = () => {
  if (!props.persistent) {
    emit('update:show', false);
  }
};

const handleConfirm = () => {
  emit('confirm');
};

const handleCancel = () => {
  emit('cancel');
  emit('update:show', false);
};

const closeModal = () => {
  emit('update:show', false);
  emit('close');
};
 

const widthClass = computed(() => {
  switch (props.width) {
    case 'sm': return 'sm:max-w-sm';
    case 'md': return 'sm:max-w-md';
    case 'lg': return 'sm:max-w-lg';
    case 'xl': return 'sm:max-w-xl';
    default: return 'sm:max-w-lg';
  }
});

const iconBgClass = computed(() => {
  switch (props.iconType) {
    case 'success': return 'bg-green-100';
    case 'error': return 'bg-red-100';
    case 'info':
    default: return 'bg-blue-100';
  }
});

const iconColorClass = computed(() => {
  switch (props.iconType) {
    case 'success': return 'text-green-600';
    case 'error': return 'text-red-600';
    case 'info':
    default: return 'text-blue-600';
  }
});
</script>
