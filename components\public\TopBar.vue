<template>
  <header class="bg-white/95 backdrop-blur-md shadow-sm sticky top-0 z-40 border-b border-gray-100">
    <nav class="container mx-auto px-4 py-4" role="navigation" aria-label="Main navigation">
      <div class="flex justify-between items-center">
        <!-- Logo and brand -->
        <NuxtLink
          to="/"
          class="flex items-center space-x-3 text-2xl font-bold text-brandPrimary-700 hover:text-brandPrimary-800 transition-all duration-300 group"
          :aria-label="$t('navigation.legalFlow') + ' Home'"
        >
          <div class="relative">
            <Icon
              name="material-symbols:gavel"
              class="h-8 w-8 group-hover:scale-110 transition-transform duration-300"
            />
            <div class="absolute inset-0 bg-brandPrimary-600 rounded-full opacity-0 group-hover:opacity-20 transition-opacity duration-300 blur-sm"></div>
          </div>
          <span class="bg-gradient-to-r from-brandPrimary-700 to-brandSecondary-600 bg-clip-text text-transparent">
            LegalFlow
          </span>
        </NuxtLink>

        <!-- Desktop Navigation -->
        <div class="hidden lg:flex items-center space-x-8">
          <!-- Navigation Links -->
          <div class="flex items-center space-x-6">
            <NuxtLink
              v-for="link in navigationLinks"
              :key="link.path"
              :to="link.path"
              :class="[
                'relative font-medium transition-all duration-300 group',
                isActiveLink(link.path)
                  ? 'text-brandPrimary-700'
                  : 'text-gray-600 hover:text-brandPrimary-700'
              ]"
            >
              {{ link.name }}
              <span
                :class="[
                  'absolute -bottom-1 left-0 h-0.5 bg-brandPrimary-600 transition-all duration-300',
                  isActiveLink(link.path) ? 'w-full' : 'w-0 group-hover:w-full'
                ]"
              ></span>
            </NuxtLink>
          </div>

          <!-- Action Buttons -->
          <div class="flex items-center space-x-3">
            <UiButton
              variant="ghost"
              size="sm"
              to="/auth/login"
              class="text-gray-700 hover:text-brandPrimary-700 hover:bg-brandPrimary-50"
            >
              Sign In
            </UiButton>
            <UiButton
              variant="primary"
              size="sm"
              to="/get-started"
              class="bg-gradient-to-r from-brandPrimary-600 to-brandPrimary-700 hover:from-brandPrimary-700 hover:to-brandPrimary-800 shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300"
            >
              <Icon name="material-symbols:rocket-launch" class="w-4 h-4 mr-2" />
              Get Started
            </UiButton>
          </div>
        </div>

        <!-- Mobile Menu Button -->
        <button
          @click="toggleMobileMenu"
          class="lg:hidden p-2 rounded-md text-gray-600 hover:text-brandPrimary-700 hover:bg-gray-100 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-brandPrimary-500"
          aria-label="Toggle mobile menu"
          :aria-expanded="isMobileMenuOpen"
        >
          <Icon
            :name="isMobileMenuOpen ? 'material-symbols:close' : 'material-symbols:menu'"
            class="w-6 h-6"
          />
        </button>
      </div>

      <!-- Mobile Navigation Menu -->
      <Transition
        enter-active-class="transition-all duration-300 ease-out"
        enter-from-class="opacity-0 transform -translate-y-2"
        enter-to-class="opacity-100 transform translate-y-0"
        leave-active-class="transition-all duration-200 ease-in"
        leave-from-class="opacity-100 transform translate-y-0"
        leave-to-class="opacity-0 transform -translate-y-2"
      >
        <div v-if="isMobileMenuOpen" class="lg:hidden mt-4 pb-4 border-t border-gray-100">
          <div class="flex flex-col space-y-3 pt-4">
            <!-- Mobile Navigation Links -->
            <NuxtLink
              v-for="link in navigationLinks"
              :key="link.path"
              :to="link.path"
              @click="closeMobileMenu"
              :class="[
                'px-3 py-2 rounded-md font-medium transition-colors duration-200',
                isActiveLink(link.path)
                  ? 'text-brandPrimary-700 bg-brandPrimary-50'
                  : 'text-gray-600 hover:text-brandPrimary-700 hover:bg-gray-50'
              ]"
            >
              {{ link.name }}
            </NuxtLink>

            <!-- Mobile Action Buttons -->
            <div class="flex flex-col space-y-2 pt-4 border-t border-gray-100">
              <UiButton
                variant="outline"
                size="md"
                to="/auth/login"
                @click="closeMobileMenu"
                class="w-full justify-center"
              >
                Sign In
              </UiButton>
              <UiButton
                variant="primary"
                size="md"
                to="/get-started"
                @click="closeMobileMenu"
                class="w-full justify-center bg-gradient-to-r from-brandPrimary-600 to-brandPrimary-700"
              >
                <Icon name="material-symbols:rocket-launch" class="w-4 h-4 mr-2" />
                Get Started
              </UiButton>
            </div>
          </div>
        </div>
      </Transition>
    </nav>
  </header>
</template>

<script setup lang="ts">
// Import required composables
const route = useRoute()
const { t: $t } = useI18n()

// Navigation links configuration
const navigationLinks = computed(() => [
  { name: $t('navigation.features'), path: '/features' },
  { name: $t('navigation.pricing'), path: '/pricing' },
  { name: $t('navigation.aboutUs'), path: '/about' },
  { name: $t('navigation.contact'), path: '/contact' }
])

// Mobile menu state
const isMobileMenuOpen = ref(false)

// Methods
const toggleMobileMenu = () => {
  isMobileMenuOpen.value = !isMobileMenuOpen.value
}

const closeMobileMenu = () => {
  isMobileMenuOpen.value = false
}

const isActiveLink = (path: string) => {
  return route.path === path
}

// Close mobile menu when route changes
watch(() => route.path, () => {
  closeMobileMenu()
})

// Close mobile menu when clicking outside
onMounted(() => {
  const handleClickOutside = (event: Event) => {
    const target = event.target as Element
    if (isMobileMenuOpen.value && !target.closest('nav')) {
      closeMobileMenu()
    }
  }

  document.addEventListener('click', handleClickOutside)

  onUnmounted(() => {
    document.removeEventListener('click', handleClickOutside)
  })
})
</script>

<style scoped>
/* Enhanced backdrop blur support */
@supports (backdrop-filter: blur(12px)) {
  header {
    backdrop-filter: blur(12px);
    background-color: rgba(255, 255, 255, 0.95);
  }
}

@supports not (backdrop-filter: blur(12px)) {
  header {
    background-color: white;
  }
}

/* Smooth mobile menu animations */
.mobile-menu-enter-active,
.mobile-menu-leave-active {
  transition: all 0.3s ease;
}

.mobile-menu-enter-from,
.mobile-menu-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}
</style>