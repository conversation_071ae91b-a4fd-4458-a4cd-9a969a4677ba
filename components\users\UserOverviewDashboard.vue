<template>
  <div class="space-y-6">
    <!-- Error State -->
    <div v-if="userStore.overviewError" class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
      <div class="flex items-center">
        <Icon name="material-symbols:error" class="h-5 w-5 text-red-500 mr-2" />
        <h3 class="text-sm font-medium text-red-800 dark:text-red-200">Failed to load user overview</h3>
      </div>
      <p class="mt-1 text-sm text-red-700 dark:text-red-300">{{ userStore.overviewError }}</p>
      <div class="mt-3">
        <UiButton @click="retryFetch" size="sm" variant="outline" class="border-red-300 text-red-700 hover:bg-red-50">
          <Icon name="material-symbols:refresh" class="h-4 w-4 mr-1" />
          Retry
        </UiButton>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="userStore.isLoadingOverview && !userStore.hasOverviewData" class="space-y-6">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div v-for="i in 4" :key="i" class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div class="animate-pulse">
            <div class="flex items-center justify-between">
              <div class="w-12 h-12 bg-gray-200 dark:bg-gray-700 rounded-lg"></div>
              <div class="text-right">
                <div class="w-16 h-8 bg-gray-200 dark:bg-gray-700 rounded mb-2"></div>
                <div class="w-20 h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
              </div>
            </div>
            <div class="mt-4">
              <div class="w-24 h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Overview Stats -->
    <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <!-- Total Users -->
      <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex items-center justify-between">
          <div class="p-3 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
            <Icon name="material-symbols:group" size="calc(var(--spacing) * 6)" class=" text-blue-600 dark:text-blue-400" />
          </div>
          <div class="text-right">
            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ totalUsers }}</p>
            <p class="text-sm text-gray-500 dark:text-gray-400">Total Users</p>
          </div>
        </div>
        <div class="mt-4">
          <div class="flex items-center text-sm">
            <Icon
              :name="totalUsersGrowth >= 0 ? 'material-symbols:trending-up' : 'material-symbols:trending-down'"
              :class="totalUsersGrowth >= 0 ? ' text-green-500 mr-1' : ' text-red-500 mr-1'"
              size="calc(var(--spacing) * 5)"
            />
            <span :class="totalUsersGrowth >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'">
              {{ totalUsersGrowth >= 0 ? '+' : '' }}{{ totalUsersGrowth.toFixed(1) }}%
            </span>
            <span class="text-gray-500 dark:text-gray-400 ml-1">from last month</span>
          </div>
        </div>
      </div>

      <!-- Active Users -->
      <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex items-center justify-between">
          <div class="p-3 bg-green-100 dark:bg-green-900/20 rounded-lg">
            <Icon name="material-symbols:check-circle" size="calc(var(--spacing) * 6)" class=" text-green-600 dark:text-green-400" />
          </div>
          <div class="text-right">
            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ activeUsers }}</p>
            <p class="text-sm text-gray-500 dark:text-gray-400">Active Users</p>
          </div>
        </div>
        <div class="mt-4">
          <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div 
              class="bg-green-500 h-2 rounded-full transition-all duration-300"
              :style="{ width: `${activePercentage}%` }"
            ></div>
          </div>
          <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">{{ activePercentage }}% of total</p>
        </div>
      </div>

      <!-- Online Users -->
      <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex items-center justify-between">
          <div class="p-3 bg-purple-100 dark:bg-purple-900/20 rounded-lg">
            <Icon name="material-symbols:wifi" size="calc(var(--spacing) * 6)" class=" text-purple-600 dark:text-purple-400" />
          </div>
          <div class="text-right">
            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ onlineUsers }}</p>
            <p class="text-sm text-gray-500 dark:text-gray-400">Online Now</p>
          </div>
        </div>
        <div class="mt-4">
          <div class="flex items-center text-sm">
            <div class="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse"></div>
            <span class="text-gray-500 dark:text-gray-400">{{ onlinePercentage }}% online</span>
            <Icon
              :name="onlineUsersChange >= 0 ? 'material-symbols:trending-up' : 'material-symbols:trending-down'"
              :class="onlineUsersChange >= 0 ? ' text-green-500 ml-2' : ' text-red-500 ml-2'"
              size="calc(var(--spacing) * 5)"
            />
            <span :class="onlineUsersChange >= 0 ? 'text-green-600 dark:text-green-400 text-xs ml-1' : 'text-red-600 dark:text-red-400 text-xs ml-1'">
              {{ onlineUsersChange >= 0 ? '+' : '' }}{{ onlineUsersChange }}%
            </span>
          </div>
        </div>
      </div>

      <!-- New Users This Month -->
      <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex items-center justify-between">
          <div class="p-3 bg-orange-100 dark:bg-orange-900/20 rounded-lg">
            <Icon name="material-symbols:person-add" size="calc(var(--spacing) * 6)" class=" text-orange-600 dark:text-orange-400" />
          </div>
          <div class="text-right">
            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ newUsersThisMonth }}</p>
            <p class="text-sm text-gray-500 dark:text-gray-400">New This Month</p>
          </div>
        </div>
        <div class="mt-4">
          <div class="flex items-center text-sm">
            <Icon
              :name="newUsersChange >= 0 ? 'material-symbols:trending-up' : 'material-symbols:trending-down'"
              :class="newUsersChange >= 0 ? ' text-green-500 mr-1' : ' text-red-500 mr-1'"
              size="calc(var(--spacing) * 5)"
            />
            <span :class="newUsersChange >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'">
              {{ newUsersChange >= 0 ? '+' : '' }}{{ newUsersChange.toFixed(1) }}%
            </span>
            <span class="text-gray-500 dark:text-gray-400 ml-1">vs last month</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Role Distribution & User Engagement -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Role Distribution -->
      <UiCard :title="'Role Distribution'" icon="material-symbols:admin-panel-settings" icon-color="purple" :loading="userStore.isLoadingOverview" hover-effect>

        <!-- Loading State -->
        <div v-if="userStore.isLoadingOverview && roleDistribution.length === 0" class="space-y-4">
          <div v-for="i in 3" :key="i" class="animate-pulse">
            <div class="flex items-center justify-between">
              <div class="flex items-center gap-3">
                <div class="w-4 h-4 bg-gray-200 dark:bg-gray-700 rounded-full"></div>
                <div class="w-20 h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
              </div>
              <div class="flex items-center gap-2">
                <div class="w-8 h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
                <div class="w-12 h-3 bg-gray-200 dark:bg-gray-700 rounded"></div>
              </div>
            </div>
          </div>
        </div>

        <!-- Data State -->
        <div v-else class="space-y-4">
          <div v-for="role in roleDistribution" :key="role.name" class="flex items-center justify-between">
            <div class="flex items-center gap-3">
              <div class="w-4 h-4 rounded-full" :style="{ backgroundColor: role.color }"></div>
              <span class="text-sm font-medium text-gray-900 dark:text-white">{{ role.name }}</span>
            </div>
            <div class="flex items-center gap-2">
              <span class="text-sm text-gray-500 dark:text-gray-400">{{ role.count }}</span>
              <span class="text-xs text-gray-400 dark:text-gray-500">({{ role.percentage }}%)</span>
            </div>
          </div>
        </div>

        <div class="mt-6">
          <div class="flex h-2 rounded-full overflow-hidden">
            <div
              v-for="role in roleDistribution"
              :key="role.name"
              class="transition-all duration-300"
              :style="{ 
                width: `${role.percentage}%`, 
                backgroundColor: role.color 
              }"
            ></div>
          </div>
        </div>
      </UiCard>

      <!-- User Engagement Metrics -->
      <UiCard :title="'User Engagement'" icon="material-symbols:analytics" icon-color="blue" :loading="userStore.isLoadingEngagement" hover-effect>

        <!-- Loading State -->
        <div v-if="userStore.isLoadingEngagement && userEngagementMetrics.length === 0" class="space-y-4">
          <div v-for="i in 4" :key="i" class="animate-pulse">
            <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <div>
                <div class="w-32 h-4 bg-gray-200 dark:bg-gray-600 rounded mb-2"></div>
                <div class="w-20 h-3 bg-gray-200 dark:bg-gray-600 rounded"></div>
              </div>
              <div class="text-right">
                <div class="w-12 h-6 bg-gray-200 dark:bg-gray-600 rounded mb-2"></div>
                <div class="w-16 h-4 bg-gray-200 dark:bg-gray-600 rounded"></div>
              </div>
            </div>
          </div>
        </div>

        <!-- Data State -->
        <div v-else class="space-y-4">
          <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <div>
              <p class="text-sm font-medium text-gray-900 dark:text-white">Daily Active Users</p>
              <p class="text-xs text-gray-500 dark:text-gray-400">Last 24 hours</p>
            </div>
            <div class="text-right">
              <p class="text-lg font-bold text-gray-900 dark:text-white">{{ dailyActiveUsers }}</p>
              <div class="flex items-center text-sm">
                <Icon
                  :name="dailyActiveUsersChange >= 0 ? 'material-symbols:trending-up' : 'material-symbols:trending-down'"
                  :class="dailyActiveUsersChange >= 0 ? ' text-green-500 mr-1' : ' text-red-500 mr-1'"
                  size="calc(var(--spacing) * 5)"
                />
                <span :class="dailyActiveUsersChange >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'">
                  {{ dailyActiveUsersChange >= 0 ? '+' : '' }}{{ dailyActiveUsersChange }}%
                </span>
              </div>
            </div>
          </div>

          <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <div>
              <p class="text-sm font-medium text-gray-900 dark:text-white">Weekly Active Users</p>
              <p class="text-xs text-gray-500 dark:text-gray-400">Last 7 days</p>
            </div>
            <div class="text-right">
              <p class="text-lg font-bold text-gray-900 dark:text-white">{{ weeklyActiveUsers }}</p>
              <div class="flex items-center text-sm">
                <Icon
                  :name="weeklyActiveUsersChange >= 0 ? 'material-symbols:trending-up' : 'material-symbols:trending-down'"
                  :class="weeklyActiveUsersChange >= 0 ? ' text-green-500 mr-1' : ' text-red-500 mr-1'"
                  size="calc(var(--spacing) * 5)"
                />
                <span :class="weeklyActiveUsersChange >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'">
                  {{ weeklyActiveUsersChange >= 0 ? '+' : '' }}{{ weeklyActiveUsersChange }}%
                </span>
              </div>
            </div>
          </div>

          <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <div>
              <p class="text-sm font-medium text-gray-900 dark:text-white">Avg. Session Duration</p>
              <p class="text-xs text-gray-500 dark:text-gray-400">Per user session</p>
            </div>
            <div class="text-right">
              <p class="text-lg font-bold text-gray-900 dark:text-white">{{ avgSessionDuration }}</p>
              <div class="flex items-center text-sm">
                <Icon
                  :name="avgSessionDurationChange >= 0 ? 'material-symbols:trending-up' : 'material-symbols:trending-down'"
                  :class="avgSessionDurationChange >= 0 ? ' text-green-500 mr-1' : ' text-red-500 mr-1'"
                  size="calc(var(--spacing) * 5)"
                />
                <span :class="avgSessionDurationChange >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'">
                  {{ avgSessionDurationChange >= 0 ? '+' : '' }}{{ avgSessionDurationChange }}%
                </span>
              </div>
            </div>
          </div>

          <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <div>
              <p class="text-sm font-medium text-gray-900 dark:text-white">User Retention Rate</p>
              <p class="text-xs text-gray-500 dark:text-gray-400">30-day retention</p>
            </div>
            <div class="text-right">
              <p class="text-lg font-bold text-gray-900 dark:text-white">{{ retentionRate }}%</p>
              <div class="flex items-center text-sm">
                <Icon
                  :name="retentionRateChange >= 0 ? 'material-symbols:trending-up' : 'material-symbols:trending-down'"
                  :class="retentionRateChange >= 0 ? ' text-green-500 mr-1' : ' text-red-500 mr-1'"
                  size="calc(var(--spacing) * 5)"
                />
                <span :class="retentionRateChange >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'">
                  {{ retentionRateChange >= 0 ? '+' : '' }}{{ retentionRateChange }}%
                </span>
              </div>
            </div>
          </div>
        </div>
      </UiCard>
    </div>


  </div>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue'
import { useUserStore } from '~/stores/user'

interface Props {
  loading?: boolean
}

interface Emits {
  (e: 'create-user'): void
  (e: 'view-all'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Store
const userStore = useUserStore()

// Computed properties from store data
const totalUsers = computed(() => userStore.totalUsersCount)
const totalUsersGrowth = computed(() => userStore.userOverview?.totalUsers?.monthlyGrowthPercentage || 0)

const activeUsers = computed(() => userStore.activeUsersCount)
const inactiveUsers = computed(() => userStore.userOverview?.statusCounts?.inactive || 0)
const pendingUsers = computed(() => userStore.userOverview?.statusCounts?.pending || 0)
const suspendedUsers = computed(() => userStore.userOverview?.statusCounts?.suspended || 0)

const activePercentage = computed(() => {
  const total = totalUsers.value
  const active = activeUsers.value
  return total > 0 ? Math.round((active / total) * 100) : 0
})

const onlineUsers = computed(() => userStore.onlineUsersCount)
const onlineUsersChange = computed(() => userStore.userOverview?.onlineNow?.changePercentage || 0)

const onlinePercentage = computed(() => {
  const active = activeUsers.value
  const online = onlineUsers.value
  return active > 0 ? Math.round((online / active) * 100) : 0
})

const newUsersThisMonth = computed(() => userStore.newUsersThisMonth)
const newUsersChange = computed(() => userStore.userOverview?.newThisMonth?.changePercentage || 0)

const roleDistribution = computed(() => {
  const roles = userStore.roleDistribution || []
  const colors: Record<string, string> = {
    'SUPER_ADMIN': '#EF4444',
    'ADMIN': '#F59E0B',
    'MANAGER': '#8B5CF6',
    'USER': '#10B981',
    'VIEWER': '#6B7280',
    'lawyer': '#3B82F6',
    'client': '#10B981',
    'paralegal': '#8B5CF6',
    'admin': '#F59E0B'
  }

  return roles.map((role: any) => ({
    name: role.role.replace('_', ' ').replace(/\b\w/g, (l: string) => l.toUpperCase()),
    count: role.count,
    percentage: role.percentage,
    color: colors[role.role] || '#6B7280'
  }))
})
 

// User engagement metrics from API
const userEngagementMetrics = computed(() => userStore?.userOverview?.userEngagement || [])

const dailyActiveUsers = computed(() => {
  const dau = userEngagementMetrics.value.find((m: any) => m.name === 'Daily Active Users')
  return dau?.value || 0
})

const dailyActiveUsersChange = computed(() => {
  const dau = userEngagementMetrics.value.find((m: any) => m.name === 'Daily Active Users')
  return dau?.changePercentage || 0
})

const weeklyActiveUsers = computed(() => {
  const wau = userEngagementMetrics.value.find((m: any) => m.name === 'Weekly Active Users')
  return wau?.value || 0
})

const weeklyActiveUsersChange = computed(() => {
  const wau = userEngagementMetrics.value.find((m: any) => m.name === 'Weekly Active Users')
  return wau?.changePercentage || 0
})

const avgSessionDuration = computed(() => {
  const session = userEngagementMetrics.value.find((m: any) => m.name === 'Avg. Session Duration')
  return session?.displayValue || session?.value + 'm' || '0m'
})

const avgSessionDurationChange = computed(() => {
  const session = userEngagementMetrics.value.find((m: any) => m.name === 'Avg. Session Duration')
  return session?.changePercentage || 0
})

const retentionRate = computed(() => {
  const retention = userEngagementMetrics.value.find((m: any) => m.name === 'User Retention Rate')
  return retention?.value || 0
})

const retentionRateChange = computed(() => {
  const retention = userEngagementMetrics.value.find((m: any) => m.name === 'User Retention Rate')
  return retention?.changePercentage || 0
})

// Methods
const retryFetch = async () => {
  await userStore.fetchUserOverview()
}

// Lifecycle
onMounted(async () => {
  // Fetch overview data if not already loaded
  if (!userStore.hasOverviewData) {
    await userStore.fetchUserOverview()
  }
})
</script>
