<!--
  Forbidden Error Component
  
  Displays a 403 Forbidden error when users try to access
  resources they don't have permission for
-->

<template>
  <div class="min-h-[60vh] flex items-center justify-center px-4 py-12">
    <div class="max-w-md w-full text-center">
      <!-- Error illustration -->
      <div class="mb-8">
        <div class="mx-auto flex items-center justify-center h-24 w-24 rounded-full bg-red-100 mb-6">
          <Icon name="heroicons:shield-exclamation" class="h-12 w-12 text-red-500" />
        </div>
        
        <!-- Error code -->
        <div class="text-6xl font-bold text-red-300 mb-2">403</div>
      </div>

      <!-- Error message -->
      <h1 class="text-2xl font-bold text-gray-900 mb-4">
        {{ displayTitle }}
      </h1>

      <p class="text-gray-600 mb-8 leading-relaxed">
        {{ displayMessage }}
      </p>

      <!-- Action buttons -->
      <div class="flex flex-col sm:flex-row gap-3 justify-center">
        <button
          type="button"
          class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200"
          @click="goBack"
        >
          <Icon name="heroicons:arrow-left" class="h-4 w-4 mr-2" />
          Go Back
        </button>

        <NuxtLink
          to="/dashboard"
          class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200"
        >
          <Icon name="heroicons:home" class="h-4 w-4 mr-2" />
          Dashboard
        </NuxtLink>
      </div>

      <!-- Permission info -->
      <div class="mt-12 p-4 bg-amber-50 border border-amber-200 rounded-lg">
        <div class="flex items-start">
          <Icon name="heroicons:information-circle" class="h-5 w-5 text-amber-600 mt-0.5 mr-3 flex-shrink-0" />
          <div class="text-left">
            <h3 class="text-sm font-medium text-amber-800 mb-2">
              Need Access?
            </h3>
            <p class="text-xs text-amber-700 mb-3">
              If you believe you should have access to this resource, please contact your administrator or request the necessary permissions.
            </p>
            
            <div class="flex gap-2">
              <button
                type="button"
                class="inline-flex items-center px-3 py-1.5 border border-amber-300 text-xs font-medium rounded text-amber-700 bg-white hover:bg-amber-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-amber-500 transition-colors duration-200"
                @click="requestAccess"
              >
                <Icon name="heroicons:key" class="h-3 w-3 mr-1" />
                Request Access
              </button>
              
              <button
                type="button"
                class="inline-flex items-center px-3 py-1.5 border border-amber-300 text-xs font-medium rounded text-amber-700 bg-white hover:bg-amber-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-amber-500 transition-colors duration-200"
                @click="contactAdmin"
              >
                <Icon name="heroicons:user-group" class="h-3 w-3 mr-1" />
                Contact Admin
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Available resources -->
      <div class="mt-8 text-left" v-if="showAvailableResources">
        <h3 class="text-sm font-medium text-gray-900 mb-4">
          Resources you can access:
        </h3>
        
        <ul class="space-y-2">
          <li v-for="resource in availableResources" :key="resource.path">
            <NuxtLink
              :to="resource.path"
              class="flex items-center text-sm text-indigo-600 hover:text-indigo-500 transition-colors duration-200"
            >
              <Icon :name="resource.icon" class="h-4 w-4 mr-2" />
              {{ resource.name }}
            </NuxtLink>
          </li>
        </ul>
      </div>

      <!-- Help section -->
      <div class="mt-8 text-center">
        <p class="text-xs text-gray-500 mb-2">
          Need help understanding permissions?
        </p>
        <button
          type="button"
          class="text-xs text-indigo-600 hover:text-indigo-500 underline transition-colors duration-200"
          @click="showHelp"
        >
          Learn about access controls
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useI18n } from 'vue-i18n'

// Props
interface Props {
  title?: string
  message?: string
  showAvailableResources?: boolean
  requiredPermission?: string
}

const props = withDefaults(defineProps<Props>(), {
  title: '',
  message: '',
  showAvailableResources: true,
  requiredPermission: ''
})

// Composables
const router = useRouter()
const route = useRoute()
const { t } = useI18n()

// Computed properties for translations
const displayTitle = computed(() => props.title || t('errors.forbidden'))
const displayMessage = computed(() => props.message || t('errors.forbiddenMessage'))

// Available resources based on user permissions
const availableResources = computed(() => {
  // This would typically come from a user permissions store
  // For now, return some common resources
  return [
    {
      name: 'Dashboard Overview',
      path: '/dashboard',
      icon: 'heroicons:home'
    },
    {
      name: 'My Profile',
      path: '/dashboard/profile',
      icon: 'heroicons:user'
    },
    {
      name: 'Settings',
      path: '/dashboard/settings',
      icon: 'heroicons:cog-6-tooth'
    },
    {
      name: 'Help & Support',
      path: '/dashboard/help',
      icon: 'heroicons:question-mark-circle'
    }
  ]
})

// Methods
const goBack = () => {
  // Check if there's history to go back to
  if (window.history.length > 1) {
    router.back()
  } else {
    // Fallback to dashboard if no history
    router.push('/dashboard')
  }
}

const requestAccess = () => {
  // Open access request functionality
  // This could open a modal, navigate to request page, or send an API request
  console.log('Request access for:', route.fullPath)
  
  // Example implementation:
  // - Open a modal with access request form
  // - Send request to admin/system
  // - Show confirmation message
  
  alert('Access request functionality would be implemented here')
}

const contactAdmin = () => {
  // Contact administrator functionality
  // This could open email client, chat, or support ticket system
  console.log('Contact admin about access to:', route.fullPath)
  
  // Example implementation:
  // - Open email client with pre-filled subject/body
  // - Open support chat
  // - Navigate to contact form
  
  alert('Contact admin functionality would be implemented here')
}

const showHelp = () => {
  // Show help about access controls
  // This could open a help modal, navigate to documentation, etc.
  console.log('Show help about access controls')
  
  // Example: router.push('/dashboard/help/permissions')
  alert('Help documentation would be shown here')
}

// SEO and meta
useHead({
  title: 'Access Forbidden - Legal SaaS',
  meta: [
    {
      name: 'description',
      content: 'You do not have permission to access this resource. Contact your administrator for assistance.'
    },
    {
      name: 'robots',
      content: 'noindex, nofollow'
    }
  ]
})

// Analytics - track permission errors
onMounted(() => {
  // Track 403 error for analytics and security monitoring
  console.log('403 Forbidden:', {
    path: route.fullPath,
    requiredPermission: props.requiredPermission,
    referrer: document.referrer,
    timestamp: new Date().toISOString()
  })
})
</script>

<style scoped>
/* Custom animations for error state */
.error-illustration {
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .error-code {
    font-size: 4rem;
  }
}

/* Focus styles for accessibility */
button:focus,
a:focus {
  outline: none;
  box-shadow: 0 0 0 2px #6366f1, 0 0 0 4px rgba(99, 102, 241, 0.2);
}

/* Warning styles */
.warning-section {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .bg-amber-50 {
    background-color: #fffbeb;
    border: 2px solid #f59e0b;
  }
  
  .text-amber-700 {
    color: #92400e;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .error-illustration {
    animation: none;
  }
  
  * {
    transition: none !important;
  }
}
</style>
