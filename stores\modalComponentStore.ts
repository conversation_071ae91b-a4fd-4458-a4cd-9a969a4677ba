import { ref, type Ref } from 'vue'

/**
 * Global store for modal component references
 * This allows us to access component instances from anywhere
 */
class ModalComponentStore {
  private componentRefs = new Map<string, any>()
  private componentReadyCallbacks = new Map<string, Array<(component: any) => void>>()

  /**
   * Set a component reference for a modal
   */
  setComponentRef(modalId: string, component: any) {
    this.componentRefs.set(modalId, component)
    
    // Execute any pending callbacks
    const callbacks = this.componentReadyCallbacks.get(modalId)
    if (callbacks) {
      callbacks.forEach(callback => {
        try {
          callback(component)
        } catch (error) {
          console.error('Error in modal component ready callback:', error)
        }
      })
      this.componentReadyCallbacks.delete(modalId)
    }
  }

  /**
   * Get a component reference for a modal
   */
  getComponentRef(modalId: string) {
    return this.componentRefs.get(modalId) || null
  }

  /**
   * Remove a component reference when modal closes
   */
  removeComponentRef(modalId: string) {
    this.componentRefs.delete(modalId)
    this.componentReadyCallbacks.delete(modalId)
  }

  /**
   * Execute callback when component is ready
   */
  whenComponentReady(modalId: string, callback: (component: any) => void) {
    const component = this.componentRefs.get(modalId)
    if (component) {
      // Component is already ready
      callback(component)
    } else {
      // Store callback for when component becomes ready
      if (!this.componentReadyCallbacks.has(modalId)) {
        this.componentReadyCallbacks.set(modalId, [])
      }
      this.componentReadyCallbacks.get(modalId)!.push(callback)
    }
  }

  /**
   * Get component data
   */
  getComponentData(modalId: string) {
    const component = this.getComponentRef(modalId)
    if (!component) return null
    
    return component.$data || 
           component.data || 
           component.setupState || 
           component.exposed || 
           null
  }

  /**
   * Call a component method
   */
  callComponentMethod(modalId: string, methodName: string, ...args: any[]) {
    const component = this.getComponentRef(modalId)
    if (!component) {
      console.warn(`Cannot call method '${methodName}': modal component not found`)
      return null
    }

    const method = component[methodName] || 
                  component.exposed?.[methodName] ||
                  component.setupState?.[methodName]

    if (typeof method === 'function') {
      return method.apply(component, args)
    } else {
      console.warn(`Method '${methodName}' not found on modal component`)
      return null
    }
  }

  /**
   * Get a component property
   */
  getComponentProperty(modalId: string, propertyName: string) {
    const component = this.getComponentRef(modalId)
    if (!component) return null

    return component[propertyName] || 
           component.exposed?.[propertyName] ||
           component.setupState?.[propertyName] ||
           component.$data?.[propertyName] ||
           null
  }

  /**
   * Set a component property
   */
  setComponentProperty(modalId: string, propertyName: string, value: any) {
    const component = this.getComponentRef(modalId)
    if (!component) {
      console.warn(`Cannot set property '${propertyName}': modal component not found`)
      return false
    }

    if (component.exposed && propertyName in component.exposed) {
      component.exposed[propertyName] = value
      return true
    } else if (component.setupState && propertyName in component.setupState) {
      component.setupState[propertyName] = value
      return true
    } else if (component.$data && propertyName in component.$data) {
      component.$data[propertyName] = value
      return true
    } else if (propertyName in component) {
      component[propertyName] = value
      return true
    }

    console.warn(`Property '${propertyName}' not found on modal component`)
    return false
  }
}

// Create singleton instance
const modalComponentStore = new ModalComponentStore()

// Export store instance and composable
export { modalComponentStore }

export function useModalComponentStore() {
  return modalComponentStore
}
