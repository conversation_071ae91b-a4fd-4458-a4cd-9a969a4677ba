<template>
  <form class="space-y-6">
    <ui-alert v-if="hasErrors && props.showErrors" type="error" :title="$t('Please review the form and correct the errors.')" />

    <forms-layout :layout="layout" :sticky-sidebar="stickySidebar">
      <forms-schema-render :schema="defaultContent" />
      <template #sidebar v-if="layout === '2-col'">
        <forms-schema-render :schema="schema.sidebar" />
      </template>
    </forms-layout>
  </form>
</template>

<script setup lang="ts">
import { useForm } from "vee-validate";
import { computed } from "vue";
import { useZodSchema } from "~/app/shared/composables/useZodSchema";
import { toTypedSchema } from "@vee-validate/zod";


interface Props {
  schema: any;
  stickySidebar?: boolean;
  name?: string;
  validateOnMount?: boolean;
  initialTouched?: Record<string, boolean>;
  initialErrors?: Record<string, string>;
  initialValues?: Record<string, any>;
  validationSchema?: any;
  showErrors?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  validateOnMount: false,
  initialTouched: () => ({}),
  initialErrors: () => ({}),
  initialValues: () => ({}),
  showErrors: true,
});



const schemaValidator = useZodSchema(props.schema);

const form = useForm({
  name: props.name,
  validationSchema: toTypedSchema(props.validationSchema || schemaValidator),
  initialValues: props.initialValues,
  initialErrors: props.initialErrors,
  initialTouched: props.initialTouched,
  validateOnMount: props.validateOnMount,
});
const { values, errors, resetForm, submitForm: formSubmit, meta  } = form;

const hasErrors = computed(() => {
  return Object.keys(errors.value).length > 0
});


const emit = defineEmits(["submit", "values", "errors"]);


 

const submitForm = async() => {
    await formSubmit()
    if (meta.value.valid) {
 
      emit("submit", values);
    } else {
      emit("errors", errors.value);
    }
};


watch(errors, (newValues) => {
  emit("errors", newValues);
});

watch(values, (newValues) => {
  emit("values", newValues);
});
 
const layout = computed(() => {
  if (Array.isArray(props.schema)) {
    return "1-col";
  }
  if (props.schema.content && props.schema.sidebar) {
    return "2-col";
  }
  return "1-col"; // Default layout
});

const defaultContent = computed(() => {
  if (Array.isArray(props.schema)) {
    return props.schema;
  }
  return props.schema.content;
});


 

defineExpose({
  values,
  submitForm,
  errors,
  resetForm,
  hasErrors
});
</script>
