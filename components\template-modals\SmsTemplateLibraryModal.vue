<template>
  <UiModal @close="$emit('close')" size="lg">
    <div class="p-6">
      <!-- Header -->
      <div class="flex items-center space-x-3 mb-6">
        <div class="w-10 h-10 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center">
          <Icon name="heroicons:device-phone-mobile" class="w-5 h-5 text-green-600 dark:text-green-400" />
        </div>
        <div>
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">SMS Template Library</h3>
          <p class="text-sm text-gray-600 dark:text-gray-400">Choose from pre-built SMS templates</p>
        </div>
      </div>

      <!-- Search and Filter -->
      <div class="flex items-center space-x-4 mb-6">
        <div class="flex-1">
          <UiInput
            v-model="searchQuery"
            placeholder="Search SMS templates..."
            class="w-full"
          >
            <template #prefix>
              <Icon name="heroicons:magnifying-glass" class="w-4 h-4 text-gray-400" />
            </template>
          </UiInput>
        </div>
        <div>
          <select
            v-model="selectedCategory"
            class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm"
          >
            <option value="">All Categories</option>
            <option value="reminders">Reminders</option>
            <option value="confirmations">Confirmations</option>
            <option value="updates">Updates</option>
            <option value="alerts">Alerts</option>
          </select>
        </div>
      </div>

      <!-- Template List -->
      <div class="space-y-3 max-h-96 overflow-y-auto">
        <div
          v-for="template in filteredTemplates"
          :key="template.id"
          class="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:border-green-300 dark:hover:border-green-600 cursor-pointer transition-colors"
          @click="selectTemplate(template)"
        >
          <div class="flex items-start justify-between mb-3">
            <div>
              <h4 class="text-sm font-medium text-gray-900 dark:text-white">{{ template.name }}</h4>
              <p class="text-xs text-gray-500 dark:text-gray-400">{{ template.category }}</p>
            </div>
            <div class="flex items-center space-x-2">
              <span
                :class="[
                  'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium',
                  getCategoryColor(template.category)
                ]"
              >
                {{ template.category }}
              </span>
              <span
                :class="[
                  'text-xs px-2 py-1 rounded',
                  template.content.length <= 160 
                    ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                    : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                ]"
              >
                {{ template.content.length }}/160
              </span>
            </div>
          </div>
          
          <p class="text-sm text-gray-600 dark:text-gray-300 mb-3">{{ template.description }}</p>
          
          <!-- SMS Preview -->
          <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-3 mb-3">
            <div class="text-xs text-gray-500 dark:text-gray-400 mb-2">Message Preview:</div>
            <div class="text-sm text-gray-900 dark:text-white font-mono">
              {{ template.content }}
            </div>
          </div>
          
          <div v-if="template.variables.length > 0" class="pt-3 border-t border-gray-200 dark:border-gray-700">
            <div class="text-xs text-gray-500 dark:text-gray-400 mb-1">Variables:</div>
            <div class="flex flex-wrap gap-1">
              <span
                v-for="variable in template.variables"
                :key="variable"
                class="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-mono bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
              >
                {{ variable }}
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- Actions -->
      <div class="flex items-center justify-end space-x-3 mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
        <UiButton @click="$emit('close')" variant="outline">
          Cancel
        </UiButton>
      </div>
    </div>
  </UiModal>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

// Emits
const emit = defineEmits<{
  close: []
  select: [template: any]
}>()

// State
const searchQuery = ref('')
const selectedCategory = ref('')

// SMS templates library
const smsTemplates = [
  {
    id: 'appointment-reminder',
    name: 'Appointment Reminder',
    category: 'reminders',
    description: 'Remind clients about upcoming appointments',
    content: 'Reminder: You have an appointment with {attorneyName} on {appointmentDate} at {appointmentTime}. Please call {companyPhone} if you need to reschedule.',
    variables: ['attorneyName', 'appointmentDate', 'appointmentTime', 'companyPhone']
  },
  {
    id: 'court-date-reminder',
    name: 'Court Date Reminder',
    category: 'reminders',
    description: 'Remind clients about court appearances',
    content: 'IMPORTANT: Court appearance for {caseTitle} scheduled for {courtDate} at {courtTime}. Location: {courtName}. Contact us at {companyPhone} with questions.',
    variables: ['caseTitle', 'courtDate', 'courtTime', 'courtName', 'companyPhone']
  },
  {
    id: 'document-deadline',
    name: 'Document Deadline Reminder',
    category: 'reminders',
    description: 'Remind clients about document submission deadlines',
    content: 'Reminder: {documentTitle} is due by {deadlineDate}. Please submit ASAP to avoid delays. Questions? Call {companyPhone}.',
    variables: ['documentTitle', 'deadlineDate', 'companyPhone']
  },
  {
    id: 'appointment-confirmed',
    name: 'Appointment Confirmation',
    category: 'confirmations',
    description: 'Confirm scheduled appointments',
    content: 'Confirmed: Appointment with {attorneyName} on {appointmentDate} at {appointmentTime}. Address: {companyAddress}. See you then!',
    variables: ['attorneyName', 'appointmentDate', 'appointmentTime', 'companyAddress']
  },
  {
    id: 'payment-received',
    name: 'Payment Confirmation',
    category: 'confirmations',
    description: 'Confirm receipt of payments',
    content: 'Payment received: ${amount} for Invoice #{invoiceNumber}. Thank you! Receipt will be emailed shortly. - {companyName}',
    variables: ['amount', 'invoiceNumber', 'companyName']
  },
  {
    id: 'case-status-update',
    name: 'Case Status Update',
    category: 'updates',
    description: 'Brief case status updates',
    content: 'Case Update: {caseTitle} - {statusUpdate}. Full details emailed. Questions? Call {companyPhone}. - {attorneyName}',
    variables: ['caseTitle', 'statusUpdate', 'companyPhone', 'attorneyName']
  },
  {
    id: 'document-ready',
    name: 'Document Ready',
    category: 'updates',
    description: 'Notify when documents are ready',
    content: '{documentTitle} is ready for pickup/review. Please contact us at {companyPhone} to arrange. - {companyName}',
    variables: ['documentTitle', 'companyPhone', 'companyName']
  },
  {
    id: 'urgent-contact',
    name: 'Urgent Contact Request',
    category: 'alerts',
    description: 'Request urgent client contact',
    content: 'URGENT: Please call {companyPhone} regarding {caseTitle}. Important matter requires immediate attention. - {attorneyName}',
    variables: ['companyPhone', 'caseTitle', 'attorneyName']
  },
  {
    id: 'court-schedule-change',
    name: 'Court Schedule Change',
    category: 'alerts',
    description: 'Alert about court schedule changes',
    content: 'ALERT: Court date for {caseTitle} changed to {newCourtDate} at {newCourtTime}. Please confirm receipt. Call {companyPhone}.',
    variables: ['caseTitle', 'newCourtDate', 'newCourtTime', 'companyPhone']
  },
  {
    id: 'payment-overdue',
    name: 'Payment Overdue Alert',
    category: 'alerts',
    description: 'Alert about overdue payments',
    content: 'Payment overdue: Invoice #{invoiceNumber} for ${amount} was due {dueDate}. Please remit payment or call {companyPhone} to discuss.',
    variables: ['invoiceNumber', 'amount', 'dueDate', 'companyPhone']
  },
  {
    id: 'welcome-client',
    name: 'Welcome New Client',
    category: 'confirmations',
    description: 'Welcome message for new clients',
    content: 'Welcome to {companyName}! We\'re honored to represent you. Your attorney {attorneyName} will contact you soon. Questions? {companyPhone}',
    variables: ['companyName', 'attorneyName', 'companyPhone']
  },
  {
    id: 'consultation-followup',
    name: 'Consultation Follow-up',
    category: 'updates',
    description: 'Follow up after consultations',
    content: 'Thank you for consulting with {attorneyName}. Next steps and retainer agreement emailed. Questions? Call {companyPhone}. - {companyName}',
    variables: ['attorneyName', 'companyPhone', 'companyName']
  }
]

// Computed
const filteredTemplates = computed(() => {
  let templates = smsTemplates
  
  // Filter by category
  if (selectedCategory.value) {
    templates = templates.filter(template => template.category === selectedCategory.value)
  }
  
  // Filter by search query
  if (searchQuery.value.trim()) {
    const query = searchQuery.value.toLowerCase()
    templates = templates.filter(template =>
      template.name.toLowerCase().includes(query) ||
      template.description.toLowerCase().includes(query) ||
      template.content.toLowerCase().includes(query)
    )
  }
  
  return templates
})

// Methods
const selectTemplate = (template: any) => {
  emit('select', template)
}

const getCategoryColor = (category: string) => {
  const colors = {
    'reminders': 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
    'confirmations': 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
    'updates': 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200',
    'alerts': 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
  }
  return colors[category as keyof typeof colors] || 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
}
</script>
