<template>
  <div class="space-y-6">
    <!-- Results Header -->
    <div class="flex items-center justify-between">
      <div class="flex items-center gap-4">
        <h2 class="text-lg font-semibold text-gray-900 dark:text-white">
          Search Results
          <span v-if="total > 0" class="text-sm font-normal text-gray-500 dark:text-gray-400">
            ({{ total.toLocaleString() }} {{ total === 1 ? 'result' : 'results' }})
          </span>
        </h2>
        
        <!-- Processing Time -->
        <span v-if="processingTime" class="text-xs text-gray-400 dark:text-gray-500">
          {{ processingTime }}ms
        </span>
      </div>

      <!-- View Mode Toggle -->
      <div v-if="showViewToggle" class="flex items-center gap-2">
        <UiButton
          v-for="mode in viewModes"
          :key="mode.value"
          :variant="viewMode === mode.value ? 'primary' : 'ghost'"
          size="sm"
          @click="$emit('view-change', mode.value)"
        >
          <Icon :name="mode.icon" class="h-4 w-4" />
          <span class="hidden sm:inline ml-1">{{ mode.label }}</span>
        </UiButton>
      </div>
    </div>

    <!-- Type Filter Tabs -->
    <div v-if="showTypeFilter && totalByType" class="border-b border-gray-200 dark:border-gray-700">
      <nav class="-mb-px flex space-x-8 overflow-x-auto">
        <button
          v-for="type in availableTypes"
          :key="type.key"
          :class="[
            'whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors',
            selectedTypes.includes(type.key) || (selectedTypes.includes('all') && type.key === 'all')
              ? 'border-brandPrimary text-brandPrimary'
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
          ]"
          @click="$emit('type-change', type.key)"
        >
          <div class="flex items-center gap-2">
            <Icon :name="type.icon" class="h-4 w-4" />
            <span>{{ type.label }}</span>
            <span
              v-if="totalByType[type.key] > 0"
              :class="[
                'inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium',
                selectedTypes.includes(type.key) || (selectedTypes.includes('all') && type.key === 'all')
                  ? 'bg-brandPrimary/10 text-brandPrimary'
                  : 'bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-400'
              ]"
            >
              {{ totalByType[type.key].toLocaleString() }}
            </span>
          </div>
        </button>
      </nav>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="space-y-4">
      <SkeletonLoader
        :type="viewMode === 'grid' ? 'cards' : 'list'"
        :rows="6"
      />
    </div>

    <!-- Results Content -->
    <div v-else-if="results.length > 0">
      <!-- Grid View -->
      <div
        v-if="viewMode === 'grid'"
        :class="[
          'grid gap-6',
          compactMode
            ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'
            : 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3'
        ]"
      >
        <SearchResultItem
          v-for="result in results"
          :key="result.id"
          :result="result"
          :view-mode="viewMode"
          :show-thumbnail="showThumbnails"
          :show-metadata="showMetadata"
          :show-tags="showTags"
          :show-actions="showActions"
          :highlight-query="highlightQuery"
          :compact-mode="compactMode"
          @click="handleResultClick"
          @action="handleResultAction"
        />
      </div>

      <!-- List View -->
      <div v-else-if="viewMode === 'list'" class="space-y-4">
        <SearchResultItem
          v-for="result in results"
          :key="result.id"
          :result="result"
          :view-mode="viewMode"
          :show-thumbnail="showThumbnails"
          :show-metadata="showMetadata"
          :show-tags="showTags"
          :show-actions="showActions"
          :highlight-query="highlightQuery"
          :compact-mode="compactMode"
          @click="handleResultClick"
          @action="handleResultAction"
        />
      </div>

      <!-- Table View -->
      <div v-else class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
        <div class="divide-y divide-gray-200 dark:divide-gray-700">
          <SearchResultItem
            v-for="result in results"
            :key="result.id"
            :result="result"
            :view-mode="viewMode"
            :show-thumbnail="showThumbnails"
            :show-metadata="showMetadata"
            :show-tags="showTags"
            :show-actions="showActions"
            :highlight-query="highlightQuery"
            :compact-mode="compactMode"
            @click="handleResultClick"
            @action="handleResultAction"
          />
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div v-else-if="hasSearched" class="text-center py-12">
      <div class="mx-auto w-24 h-24 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mb-4">
        <Icon name="heroicons:magnifying-glass" class="h-12 w-12 text-gray-400" />
      </div>
      <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
        No results found
      </h3>
      <p class="text-gray-500 dark:text-gray-400 mb-6 max-w-md mx-auto">
        We couldn't find any results matching your search criteria. Try adjusting your search terms or filters.
      </p>
      <div class="flex items-center justify-center gap-3">
        <UiButton variant="outline" @click="$emit('clear-search')">
          Clear Search
        </UiButton>
        <UiButton variant="primary" @click="$emit('modify-search')">
          Modify Search
        </UiButton>
      </div>
    </div>

    <!-- Initial State -->
    <div v-else class="text-center py-12">
      <div class="mx-auto w-24 h-24 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mb-4">
        <Icon name="heroicons:magnifying-glass-plus" class="h-12 w-12 text-gray-400" />
      </div>
      <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
        Start your search
      </h3>
      <p class="text-gray-500 dark:text-gray-400 max-w-md mx-auto">
        Enter a search term above to find cases, documents, users, clients, and templates across your legal practice.
      </p>
    </div>

    <!-- Load More Button -->
    <div v-if="hasMore && results.length > 0" class="text-center pt-6">
      <UiButton
        variant="outline"
        :loading="loadingMore"
        @click="$emit('load-more')"
      >
        Load More Results
      </UiButton>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { 
  GlobalSearchResultItem, 
  SearchViewMode, 
  SearchEntityType 
} from '~/app/shared/types/search'

interface Props {
  results: GlobalSearchResultItem[]
  loading: boolean
  loadingMore?: boolean
  total: number
  totalByType?: Record<SearchEntityType, number>
  selectedTypes: SearchEntityType[]
  viewMode: SearchViewMode
  processingTime?: number
  highlightQuery?: string
  hasSearched: boolean
  hasMore?: boolean
  showViewToggle?: boolean
  showTypeFilter?: boolean
  showThumbnails?: boolean
  showMetadata?: boolean
  showTags?: boolean
  showActions?: boolean
  compactMode?: boolean
}

interface Emits {
  (e: 'result-click', result: GlobalSearchResultItem): void
  (e: 'result-action', action: string, result: GlobalSearchResultItem): void
  (e: 'view-change', viewMode: SearchViewMode): void
  (e: 'type-change', type: SearchEntityType): void
  (e: 'clear-search'): void
  (e: 'modify-search'): void
  (e: 'load-more'): void
}

const props = withDefaults(defineProps<Props>(), {
  loadingMore: false,
  showViewToggle: true,
  showTypeFilter: true,
  showThumbnails: true,
  showMetadata: true,
  showTags: true,
  showActions: true,
  compactMode: false,
  hasMore: false
})

const emit = defineEmits<Emits>()

// View mode options
const viewModes = [
  { value: 'list' as SearchViewMode, label: 'List', icon: 'heroicons:list-bullet' },
  { value: 'grid' as SearchViewMode, label: 'Grid', icon: 'heroicons:squares-2x2' },
  { value: 'table' as SearchViewMode, label: 'Table', icon: 'heroicons:table-cells' }
]

// Available entity types
const availableTypes = computed(() => [
  { key: 'all' as SearchEntityType, label: 'All', icon: 'heroicons:magnifying-glass' },
  { key: 'cases' as SearchEntityType, label: 'Cases', icon: 'heroicons:briefcase' },
  { key: 'documents' as SearchEntityType, label: 'Documents', icon: 'heroicons:document-text' },
  { key: 'users' as SearchEntityType, label: 'Users', icon: 'heroicons:users' },
  { key: 'clients' as SearchEntityType, label: 'Clients', icon: 'heroicons:user-group' },
  { key: 'templates' as SearchEntityType, label: 'Templates', icon: 'heroicons:document-duplicate' }
])

// Event handlers
function handleResultClick(result: GlobalSearchResultItem) {
  emit('result-click', result)
}

function handleResultAction(action: string, result: GlobalSearchResultItem) {
  emit('result-action', action, result)
}
</script>
