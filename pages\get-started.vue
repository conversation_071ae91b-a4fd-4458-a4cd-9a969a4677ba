<template>
  <div class="get-started-page">
    <!-- Hero Section -->
    <HeroSection
      title="Get Started with LegalFlow"
      subtitle="Transform your legal practice in just a few simple steps. Join thousands of legal professionals who trust LegalFlow."
      :badge="{
        text: '⚡ Setup in 5 Minutes',
        variant: 'success'
      }"
      :actions="[
        {
          text: 'Start Free Trial',
          variant: 'primary',
          to: '/auth/signup',
          icon: 'material-symbols:rocket-launch'
        },
        {
          text: 'Sign In',
          variant: 'outline',
          to: '/auth/login',
          icon: 'material-symbols:login'
        }
      ]"
      variant="gradient"
      size="lg"
    />

    <!-- Onboarding Steps -->
    <section class="py-16 bg-white">
      <div class="container mx-auto px-6">
        <h2 class="text-3xl md:text-4xl font-bold text-gray-800 text-center mb-12">
          Your Journey to Legal Excellence
        </h2>

        <div class="max-w-4xl mx-auto">
          <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            <!-- Step 1 -->
            <div class="text-center group">
              <div class="relative">
                <div class="w-16 h-16 bg-brandPrimary-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-brandPrimary-200 transition-colors duration-300">
                  <span class="text-2xl font-bold text-brandPrimary-600">1</span>
                </div>
                <div class="hidden md:block absolute top-8 left-16 w-full h-0.5 bg-gray-300"></div>
              </div>
              <h3 class="text-xl font-semibold text-gray-900 mb-3">Sign Up</h3>
              <p class="text-gray-600 mb-4">Create your account in under 2 minutes. No credit card required for the free trial.</p>
              <UiButton variant="outline" size="sm" to="/auth/signup">
                Get Started
              </UiButton>
            </div>

            <!-- Step 2 -->
            <div class="text-center group">
              <div class="relative">
                <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-green-200 transition-colors duration-300">
                  <span class="text-2xl font-bold text-green-600">2</span>
                </div>
                <div class="hidden md:block absolute top-8 left-16 w-full h-0.5 bg-gray-300"></div>
              </div>
              <h3 class="text-xl font-semibold text-gray-900 mb-3">Setup</h3>
              <p class="text-gray-600 mb-4">Configure your firm details, invite team members, and customize your workspace.</p>
              <UiButton variant="outline" size="sm" disabled>
                Coming Soon
              </UiButton>
            </div>

            <!-- Step 3 -->
            <div class="text-center group">
              <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-purple-200 transition-colors duration-300">
                <span class="text-2xl font-bold text-purple-600">3</span>
              </div>
              <h3 class="text-xl font-semibold text-gray-900 mb-3">Launch</h3>
              <p class="text-gray-600 mb-4">Start managing cases, uploading documents, and collaborating with your team.</p>
              <UiButton variant="outline" size="sm" disabled>
                Ready to Go
              </UiButton>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Quick Access Cards -->
    <section class="py-16 bg-gray-50">
      <div class="container mx-auto px-6">
        <h2 class="text-3xl font-bold text-gray-800 text-center mb-12">
          Choose Your Path
        </h2>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
          <!-- Existing Users -->
          <div class="bg-white rounded-xl shadow-lg p-8 text-center hover:shadow-xl transition-shadow duration-300">
            <div class="w-16 h-16 bg-brandPrimary-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <Icon name="material-symbols:login" class="w-8 h-8 text-brandPrimary-600" />
            </div>
            <h3 class="text-2xl font-semibold text-brandPrimary-700 mb-4">Already a Member?</h3>
            <p class="text-gray-600 mb-6">Access your existing cases, documents, and team workspace.</p>
            <UiButton variant="primary" size="lg" to="/auth/login" class="w-full">
              <Icon name="material-symbols:login" class="w-5 h-5 mr-2" />
              Sign In
            </UiButton>
          </div>

          <!-- New Users -->
          <div class="bg-white rounded-xl shadow-lg p-8 text-center hover:shadow-xl transition-shadow duration-300 border-2 border-green-200">
            <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <Icon name="material-symbols:person-add" class="w-8 h-8 text-green-600" />
            </div>
            <h3 class="text-2xl font-semibold text-green-700 mb-4">New to LegalFlow?</h3>
            <p class="text-gray-600 mb-6">Start your free trial and experience the difference. No credit card required.</p>
            <UiButton variant="primary" size="lg" to="/auth/signup" class="w-full bg-green-600 hover:bg-green-700">
              <Icon name="material-symbols:rocket-launch" class="w-5 h-5 mr-2" />
              Start Free Trial
            </UiButton>
            <p class="text-sm text-green-600 mt-3 font-medium">14 days free • Full access</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Support Section -->
    <section class="py-16 bg-white">
      <div class="container mx-auto px-6 text-center">
        <h2 class="text-2xl font-bold text-gray-900 mb-8">Need Help Getting Started?</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
          <div class="p-6">
            <Icon name="material-symbols:support-agent" class="w-12 h-12 text-brandPrimary-600 mx-auto mb-4" />
            <h3 class="text-lg font-semibold text-gray-900 mb-2">Live Support</h3>
            <p class="text-gray-600 mb-4">Chat with our experts for immediate assistance</p>
            <UiButton variant="outline" size="sm">
              Start Chat
            </UiButton>
          </div>

          <div class="p-6">
            <Icon name="material-symbols:calendar-month" class="w-12 h-12 text-green-600 mx-auto mb-4" />
            <h3 class="text-lg font-semibold text-gray-900 mb-2">Schedule Demo</h3>
            <p class="text-gray-600 mb-4">Book a personalized walkthrough</p>
            <UiButton variant="outline" size="sm" to="/contact">
              Book Demo
            </UiButton>
          </div>

          <div class="p-6">
            <Icon name="material-symbols:help" class="w-12 h-12 text-purple-600 mx-auto mb-4" />
            <h3 class="text-lg font-semibold text-gray-900 mb-2">Help Center</h3>
            <p class="text-gray-600 mb-4">Browse our comprehensive guides</p>
            <UiButton variant="outline" size="sm" href="#" external>
              View Docs
            </UiButton>
          </div>
        </div>
      </div>
    </section>

    <!-- Final CTA -->
    <CTASection
      title="Ready to Transform Your Practice?"
      subtitle="Join thousands of legal professionals who have already made the switch to LegalFlow."
      :actions="[
        {
          text: 'Start Your Free Trial',
          variant: 'primary',
          to: '/auth/signup',
          icon: 'material-symbols:rocket-launch',
          size: 'xl'
        }
      ]"
      :features="[
        '14-day free trial',
        'No credit card required',
        'Full feature access',
        'Cancel anytime'
      ]"
      :trust-indicators="[
        { text: 'SOC 2 Certified', icon: 'material-symbols:verified' },
        { text: '2,500+ Law Firms', icon: 'material-symbols:business' },
        { text: '99.9% Uptime', icon: 'material-symbols:cloud-done' }
      ]"
      variant="primary"
      size="lg"
    />
  </div>
</template>

<script setup lang="ts">
// Import components
import HeroSection from '~/components/public/HeroSection.vue'
import CTASection from '~/components/public/CTASection.vue'

// Define page metadata
definePageMeta({
  layout: 'default',
  middleware: ['guest']
})

// useHead for SEO/page title
useHead({
  title: 'Get Started - Legal SaaS Platform',
  meta: [
    {
      name: 'description',
      content: 'Start your journey with LegalFlow. Sign in or create a new account to streamline your legal operations and transform your practice.'
    }
  ]
})
</script>

<style scoped>
/* Styles are now handled by the individual components */
</style>