<template>
  <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden transition-all duration-200 hover:shadow-md">
    <!-- Card Header -->
    <div v-if="title || $slots.header" class="px-6 py-4 border-b border-gray-100 bg-gray-50/50">
      <slot name="header">
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-3">
            <div v-if="icon" class="flex-shrink-0">
              <div class="w-8 h-8 bg-brandPrimary/10 rounded-lg flex items-center justify-center">
                <Icon :name="icon" class="h-4 w-4 text-brandPrimary" />
              </div>
            </div>
            <div>
              <h3 class="text-lg font-semibold text-gray-900">{{ title }}</h3>
              <p v-if="subtitle" class="text-sm text-gray-500 mt-0.5">{{ subtitle }}</p>
            </div>
          </div>
          
          <!-- Header Actions -->
          <div v-if="$slots.actions" class="flex items-center gap-2">
            <slot name="actions" />
          </div>
        </div>
      </slot>
    </div>

    <!-- Card Content -->
    <div class="px-6 py-6">
      <slot />
    </div>

    <!-- Card Footer -->
    <div v-if="$slots.footer" class="px-6 py-4 bg-gray-50/50 border-t border-gray-100">
      <slot name="footer" />
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  title?: string;
  subtitle?: string;
  icon?: string;
}

defineProps<Props>();
</script>
