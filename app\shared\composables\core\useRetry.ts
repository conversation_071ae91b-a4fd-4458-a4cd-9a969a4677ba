/**
 * Retry Composable
 * 
 * Provides retry functionality for failed operations
 */

import { ref } from 'vue'

export interface RetryOptions {
  maxAttempts: number
  delay: number
  backoff?: 'linear' | 'exponential'
  backoffMultiplier?: number
  maxDelay?: number
  retryCondition?: (error: any) => boolean
}

export interface RetryState {
  attempts: number
  isRetrying: boolean
  lastError: any
}

/**
 * Retry a function with configurable options
 */
export function useRetry<T>(
  fn: () => Promise<T>,
  options: RetryOptions = {
    maxAttempts: 3,
    delay: 1000,
    backoff: 'exponential',
    backoffMultiplier: 2,
    maxDelay: 30000
  }
) {
  const state = ref<RetryState>({
    attempts: 0,
    isRetrying: false,
    lastError: null
  })
  
  /**
   * Calculate delay for next retry
   */
  function calculateDelay(attempt: number): number {
    let delay = options.delay
    
    if (options.backoff === 'exponential') {
      delay = options.delay * Math.pow(options.backoffMultiplier || 2, attempt - 1)
    } else if (options.backoff === 'linear') {
      delay = options.delay * attempt
    }
    
    return Math.min(delay, options.maxDelay || 30000)
  }
  
  /**
   * Sleep for specified duration
   */
  function sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
  
  /**
   * Execute function with retry logic
   */
  async function execute(): Promise<T> {
    state.value.attempts = 0
    state.value.isRetrying = false
    state.value.lastError = null
    
    while (state.value.attempts < options.maxAttempts) {
      try {
        state.value.attempts++
        
        if (state.value.attempts > 1) {
          state.value.isRetrying = true
          const delay = calculateDelay(state.value.attempts - 1)
          await sleep(delay)
        }
        
        const result = await fn()
        state.value.isRetrying = false
        return result
        
      } catch (error) {
        state.value.lastError = error
        
        // Check if we should retry this error
        if (options.retryCondition && !options.retryCondition(error)) {
          state.value.isRetrying = false
          throw error
        }
        
        // If this was the last attempt, throw the error
        if (state.value.attempts >= options.maxAttempts) {
          state.value.isRetrying = false
          throw error
        }
      }
    }
    
    // This should never be reached, but TypeScript requires it
    state.value.isRetrying = false
    throw state.value.lastError
  }
  
  /**
   * Reset retry state
   */
  function reset(): void {
    state.value.attempts = 0
    state.value.isRetrying = false
    state.value.lastError = null
  }
  
  return {
    execute,
    reset,
    state: readonly(state)
  }
}

/**
 * Retry with exponential backoff (common use case)
 */
export function useExponentialRetry<T>(
  fn: () => Promise<T>,
  maxAttempts: number = 3,
  initialDelay: number = 1000
) {
  return useRetry(fn, {
    maxAttempts,
    delay: initialDelay,
    backoff: 'exponential',
    backoffMultiplier: 2,
    maxDelay: 30000
  })
}

/**
 * Retry with linear backoff
 */
export function useLinearRetry<T>(
  fn: () => Promise<T>,
  maxAttempts: number = 3,
  delay: number = 1000
) {
  return useRetry(fn, {
    maxAttempts,
    delay,
    backoff: 'linear',
    maxDelay: 30000
  })
}
