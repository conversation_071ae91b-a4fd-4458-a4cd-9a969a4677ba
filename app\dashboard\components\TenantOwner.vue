<!--
  Dashboard Home Page

  Main dashboard overview with statistics and quick actions
-->

<template>
  <div class="space-y-6">
    <!-- Welcome Header -->
    <div class="bg-white shadow rounded-lg p-6">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">Welcome to the Dashboard</h1>
          <p class="mt-1 text-sm text-gray-500">
            Here's what's happening with your legal practice today.
          </p>
        </div>
        <div class="flex space-x-3">
          <NuxtLink
            to="/dashboard/cases/create"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
          >
            <Icon name="heroicons:plus" class="h-4 w-4 mr-2" />
            New Case
          </NuxtLink>
          <NuxtLink
            to="/dashboard/documents/upload"
            class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
          >
            <Icon name="heroicons:arrow-up-tray" class="h-4 w-4 mr-2" />
            Upload Document
          </NuxtLink>
        </div>
      </div>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <!-- Active Cases -->
      <div class="bg-white shadow rounded-lg p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="flex items-center justify-center h-12 w-12 rounded-lg bg-indigo-100">
              <Icon name="heroicons:briefcase" class="h-6 w-6 text-indigo-600" />
            </div>
          </div>
          <div class="ml-4">
            <h3 class="text-lg font-medium text-gray-900">Active Cases</h3>
            <p class="text-2xl font-bold text-indigo-600">24</p>
            <p class="text-sm text-gray-500">+3 this week</p>
          </div>
        </div>
      </div>

      <!-- Documents -->
      <div class="bg-white shadow rounded-lg p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="flex items-center justify-center h-12 w-12 rounded-lg bg-green-100">
              <Icon name="heroicons:document-text" class="h-6 w-6 text-green-600" />
            </div>
          </div>
          <div class="ml-4">
            <h3 class="text-lg font-medium text-gray-900">Documents</h3>
            <p class="text-2xl font-bold text-green-600">156</p>
            <p class="text-sm text-gray-500">+12 this week</p>
          </div>
        </div>
      </div>

      <!-- Templates -->
      <div class="bg-white shadow rounded-lg p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="flex items-center justify-center h-12 w-12 rounded-lg bg-yellow-100">
              <Icon name="heroicons:clipboard-document-list" class="h-6 w-6 text-yellow-600" />
            </div>
          </div>
          <div class="ml-4">
            <h3 class="text-lg font-medium text-gray-900">Templates</h3>
            <p class="text-2xl font-bold text-yellow-600">8</p>
            <p class="text-sm text-gray-500">Ready to use</p>
          </div>
        </div>
      </div>

      <!-- Users -->
      <div class="bg-white shadow rounded-lg p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="flex items-center justify-center h-12 w-12 rounded-lg bg-purple-100">
              <Icon name="heroicons:users" class="h-6 w-6 text-purple-600" />
            </div>
          </div>
          <div class="ml-4">
            <h3 class="text-lg font-medium text-gray-900">Team Members</h3>
            <p class="text-2xl font-bold text-purple-600">12</p>
            <p class="text-sm text-gray-500">All active</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Quick Actions -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Recent Cases -->
      <div class="bg-white shadow rounded-lg p-6">
        <div class="flex items-center justify-between mb-4">
          <h2 class="text-lg font-medium text-gray-900">Recent Cases</h2>
          <NuxtLink
            to="/dashboard/cases"
            class="text-sm text-indigo-600 hover:text-indigo-500"
          >
            View all
          </NuxtLink>
        </div>

        <div class="space-y-3">
          <div v-for="case_ in recentCases" :key="case_.id" class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <div>
              <h3 class="text-sm font-medium text-gray-900">{{ case_.title }}</h3>
              <p class="text-xs text-gray-500">{{ case_.client }}</p>
            </div>
            <span :class="getStatusBadgeClass(case_.status)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
              {{ case_.status }}
            </span>
          </div>
        </div>
      </div>

      <!-- Quick Links -->
      <div class="bg-white shadow rounded-lg p-6">
        <h2 class="text-lg font-medium text-gray-900 mb-4">Quick Actions</h2>

        <div class="grid grid-cols-2 gap-3">
          <NuxtLink
            to="/dashboard/cases"
            class="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-200"
          >
            <Icon name="heroicons:briefcase" class="h-8 w-8 text-indigo-600 mb-2" />
            <span class="text-sm font-medium text-gray-900">Manage Cases</span>
          </NuxtLink>

          <NuxtLink
            to="/dashboard/documents"
            class="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-200"
          >
            <Icon name="heroicons:document-text" class="h-8 w-8 text-green-600 mb-2" />
            <span class="text-sm font-medium text-gray-900">Documents</span>
          </NuxtLink>

          <NuxtLink
            to="/dashboard/templates"
            class="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-200"
          >
            <Icon name="heroicons:clipboard-document-list" class="h-8 w-8 text-yellow-600 mb-2" />
            <span class="text-sm font-medium text-gray-900">Templates</span>
          </NuxtLink>

          <NuxtLink
            to="/dashboard/users"
            class="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-200"
          >
            <Icon name="heroicons:users" class="h-8 w-8 text-purple-600 mb-2" />
            <span class="text-sm font-medium text-gray-900">Team</span>
          </NuxtLink>
        </div>
      </div>
    </div>

    <!-- Recent Activity -->
    <div class="bg-white shadow rounded-lg p-6">
      <h2 class="text-lg font-medium text-gray-900 mb-4">Recent Activity</h2>

      <div class="flow-root">
        <ul class="-mb-8">
          <li v-for="(activity, index) in recentActivity" :key="activity.id">
            <div class="relative pb-8">
              <span v-if="index !== recentActivity.length - 1" class="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200"></span>
              <div class="relative flex space-x-3">
                <div>
                  <span :class="[activity.iconBackground, 'h-8 w-8 rounded-full flex items-center justify-center ring-8 ring-white']">
                    <Icon :name="activity.icon" class="h-4 w-4 text-white" />
                  </span>
                </div>
                <div class="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                  <div>
                    <p class="text-sm text-gray-500">{{ activity.content }}</p>
                  </div>
                  <div class="text-right text-sm whitespace-nowrap text-gray-500">
                    {{ formatDate(activity.datetime) }}
                  </div>
                </div>
              </div>
            </div>
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

 

// Mock data
const recentCases = ref([
  { id: 1, title: 'Contract Dispute Resolution', client: 'Acme Corporation', status: 'active' },
  { id: 2, title: 'Employment Law Consultation', client: 'Tech Startup Inc', status: 'pending' },
  { id: 3, title: 'Intellectual Property Review', client: 'Innovation Labs', status: 'active' }
])

const recentActivity = ref([
  {
    id: 1,
    content: 'New case created: Contract Dispute Resolution',
    datetime: '2024-01-15T10:30:00Z',
    icon: 'heroicons:plus',
    iconBackground: 'bg-green-500'
  },
  {
    id: 2,
    content: 'Document uploaded: Service Agreement.pdf',
    datetime: '2024-01-15T09:15:00Z',
    icon: 'heroicons:arrow-up-tray',
    iconBackground: 'bg-blue-500'
  },
  {
    id: 3,
    content: 'Template updated: NDA Template',
    datetime: '2024-01-14T16:45:00Z',
    icon: 'heroicons:pencil',
    iconBackground: 'bg-yellow-500'
  }
])

// Methods
const getStatusBadgeClass = (status: string) => {
  const classes = {
    active: 'bg-green-100 text-green-800',
    pending: 'bg-yellow-100 text-yellow-800',
    closed: 'bg-gray-100 text-gray-800'
  }
  return classes[status as keyof typeof classes] || 'bg-gray-100 text-gray-800'
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}
</script>