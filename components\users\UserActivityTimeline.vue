<template>
  <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
    <div class="flex items-center justify-between mb-6">
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Recent User Activity</h3>
      <div class="flex items-center gap-2">
        <UiButton 
          size="sm" 
          :variant="timeFilter === 'today' ? 'primary' : 'ghost'"
          @click="timeFilter = 'today'"
        >
          Today
        </UiButton>
        <UiButton 
          size="sm" 
          :variant="timeFilter === 'week' ? 'primary' : 'ghost'"
          @click="timeFilter = 'week'"
        >
          Week
        </UiButton>
        <UiButton 
          size="sm" 
          :variant="timeFilter === 'month' ? 'primary' : 'ghost'"
          @click="timeFilter = 'month'"
        >
          Month
        </UiButton>
      </div>
    </div>

    <!-- Timeline -->
    <div class="relative">
      <!-- Timeline line -->
      <div class="absolute left-6 top-0 bottom-0 w-0.5 bg-gray-200 dark:bg-gray-700"></div>
      
      <!-- Activity items -->
      <div class="space-y-6">
        <div
          v-for="(activity, index) in filteredActivities"
          :key="activity.id"
          class="relative flex items-start gap-4 group"
        >
          <!-- Timeline dot -->
          <div 
            :class="[
              'relative z-10 flex items-center justify-center w-12 h-12 rounded-full border-4 border-white dark:border-gray-800 shadow-sm',
              getActivityColor(activity.type)
            ]"
          >
            <Icon :name="getActivityIcon(activity.type)" class="h-5 w-5 text-white" />
          </div>
          
          <!-- Activity content -->
          <div class="flex-1 min-w-0 pb-6">
            <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4 group-hover:bg-gray-100 dark:group-hover:bg-gray-700 transition-colors duration-200">
              <!-- Activity header -->
              <div class="flex items-center justify-between mb-2">
                <div class="flex items-center gap-2">
                  <span class="text-sm font-medium text-gray-900 dark:text-white">
                    {{ activity.title }}
                  </span>
                  <span 
                    :class="[
                      'inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium',
                      getActivityBadgeClass(activity.type)
                    ]"
                  >
                    {{ activity.type }}
                  </span>
                </div>
                <time class="text-xs text-gray-500 dark:text-gray-400">
                  {{ $formatRelativeTime(activity.timestamp) }}
                </time>
              </div>
              
              <!-- Activity description -->
              <p class="text-sm text-gray-600 dark:text-gray-300 mb-3">
                {{ activity.description }}
              </p>
              
              <!-- Activity metadata -->
              <div class="flex items-center justify-between">
                <div class="flex items-center gap-4">
                  <!-- User info -->
                  <div v-if="activity.user" class="flex items-center gap-2">
                    <img
                      v-if="activity.user.avatarUrl"
                      :src="activity.user.avatarUrl"
                      :alt="`${activity.user.name} avatar`"
                      class="w-6 h-6 rounded-full object-cover"
                    />
                    <div
                      v-else
                      class="w-6 h-6 rounded-full bg-gradient-to-br from-brandPrimary to-brandSecondary flex items-center justify-center"
                    >
                      <span class="text-xs font-semibold text-white">
                        {{ activity.user.name.charAt(0).toUpperCase() }}
                      </span>
                    </div>
                    <span class="text-xs text-gray-500 dark:text-gray-400">
                      {{ activity.user.name }}
                    </span>
                  </div>
                  
                  <!-- IP Address -->
                  <div v-if="activity.ipAddress" class="flex items-center gap-2">
                    <Icon name="material-symbols:location-on" class="h-4 w-4 text-gray-400" />
                    <span class="text-xs text-gray-500 dark:text-gray-400">
                      {{ activity.ipAddress }}
                    </span>
                  </div>

                  <!-- Device Info -->
                  <div v-if="activity.device" class="flex items-center gap-2">
                    <Icon name="material-symbols:devices" class="h-4 w-4 text-gray-400" />
                    <span class="text-xs text-gray-500 dark:text-gray-400">
                      {{ activity.device }}
                    </span>
                  </div>
                </div>
                
                <!-- Action button -->
                <UiButton
                  v-if="activity.actionUrl"
                  @click="handleActivityAction(activity)"
                  size="sm"
                  variant="ghost"
                  class="opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                >
                  <Icon name="material-symbols:arrow-forward" class="h-4 w-4" />
                </UiButton>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Load more button -->
      <div v-if="hasMoreActivities" class="text-center mt-6">
        <UiButton 
          @click="loadMoreActivities"
          variant="outline"
          :loading="loadingMore"
        >
          <Icon name="material-symbols:expand-more" class="h-4 w-4 mr-2" />
          Load More Activities
        </UiButton>
      </div>
      
      <!-- Empty state -->
      <div v-if="filteredActivities.length === 0" class="text-center py-8">
        <Icon name="material-symbols:timeline" class="h-12 w-12 text-gray-400 mx-auto mb-3" />
        <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-1">No recent activity</h4>
        <p class="text-sm text-gray-500 dark:text-gray-400">
          User activity will appear here when users perform actions.
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import type { User } from '~/stores/user'

interface ActivityItem {
  id: string
  type: 'login' | 'logout' | 'password_change' | 'profile_update' | 'role_change' | 'security' | 'created' | 'activated' | 'deactivated'
  title: string
  description: string
  timestamp: string
  user?: {
    id: string
    name: string
    avatarUrl?: string
  }
  ipAddress?: string
  device?: string
  actionUrl?: string
}

interface Props {
  users: User[]
}

const props = defineProps<Props>()

// State
const timeFilter = ref<'today' | 'week' | 'month'>('week')
const loadingMore = ref(false)
const hasMoreActivities = ref(true)

// Mock activity data - in real app, this would come from API
const activities = ref<ActivityItem[]>([
  {
    id: '1',
    type: 'login',
    title: 'User logged in',
    description: 'John Doe successfully logged in from Chrome browser',
    timestamp: new Date(Date.now() - 1000 * 60 * 15).toISOString(), // 15 minutes ago
    user: {
      id: '1',
      name: 'John Doe',
      avatarUrl: undefined
    },
    ipAddress: '*************',
    device: 'Chrome on Windows',
    actionUrl: '/dashboard/platform/users/1'
  },
  {
    id: '2',
    type: 'created',
    title: 'New user created',
    description: 'Sarah Johnson was invited and created an account',
    timestamp: new Date(Date.now() - 1000 * 60 * 60).toISOString(), // 1 hour ago
    user: {
      id: '2',
      name: 'Sarah Johnson',
      avatarUrl: undefined
    }
  },
  {
    id: '3',
    type: 'role_change',
    title: 'Role updated',
    description: 'Mike Wilson was promoted to Admin role',
    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(), // 2 hours ago
    user: {
      id: '3',
      name: 'Mike Wilson',
      avatarUrl: undefined
    }
  },
  {
    id: '4',
    type: 'security',
    title: '2FA enabled',
    description: 'Emily Davis enabled two-factor authentication',
    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 4).toISOString(), // 4 hours ago
    user: {
      id: '4',
      name: 'Emily Davis',
      avatarUrl: undefined
    }
  },
  {
    id: '5',
    type: 'password_change',
    title: 'Password reset',
    description: 'Alex Brown reset their password',
    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 6).toISOString(), // 6 hours ago
    user: {
      id: '5',
      name: 'Alex Brown',
      avatarUrl: undefined
    },
    ipAddress: '*********',
    device: 'Safari on macOS'
  }
])

// Computed properties
const filteredActivities = computed(() => {
  const now = new Date()
  let cutoffDate: Date
  
  switch (timeFilter.value) {
    case 'today':
      cutoffDate = new Date(now.getFullYear(), now.getMonth(), now.getDate())
      break
    case 'week':
      cutoffDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
      break
    case 'month':
      cutoffDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
      break
    default:
      cutoffDate = new Date(0)
  }
  
  return activities.value.filter(activity => 
    new Date(activity.timestamp) >= cutoffDate
  )
})

// Methods
const getActivityIcon = (type: ActivityItem['type']) => {
  const icons = {
    login: 'material-symbols:login',
    logout: 'material-symbols:logout',
    password_change: 'material-symbols:lock-reset',
    profile_update: 'material-symbols:edit',
    role_change: 'material-symbols:admin-panel-settings',
    security: 'material-symbols:security',
    created: 'material-symbols:person-add',
    activated: 'material-symbols:person-check',
    deactivated: 'material-symbols:person-off'
  }
  return icons[type] || 'material-symbols:info'
}

const getActivityColor = (type: ActivityItem['type']) => {
  const colors = {
    login: 'bg-green-500',
    logout: 'bg-gray-500',
    password_change: 'bg-orange-500',
    profile_update: 'bg-blue-500',
    role_change: 'bg-purple-500',
    security: 'bg-red-500',
    created: 'bg-green-500',
    activated: 'bg-green-500',
    deactivated: 'bg-red-500'
  }
  return colors[type] || 'bg-gray-500'
}

const getActivityBadgeClass = (type: ActivityItem['type']) => {
  const classes = {
    login: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
    logout: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300',
    password_change: 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300',
    profile_update: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
    role_change: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300',
    security: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
    created: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
    activated: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
    deactivated: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
  }
  return classes[type] || 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
}

// Note: Using global $formatRelativeTime formatter in template for consistent date formatting

const handleActivityAction = (activity: ActivityItem) => {
  if (activity.actionUrl) {
    // Navigate to the activity URL
    console.log('Navigate to:', activity.actionUrl)
  }
}

const loadMoreActivities = async () => {
  loadingMore.value = true
  
  // Simulate API call
  await new Promise(resolve => setTimeout(resolve, 1000))
  
  // In real app, load more activities from API
  hasMoreActivities.value = false
  loadingMore.value = false
}
</script>
