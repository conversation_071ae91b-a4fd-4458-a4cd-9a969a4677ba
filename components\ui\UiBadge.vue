<template>
  <span
    :class="[
      'inline-flex items-center font-medium transition-all duration-200 ease-in-out',
      shapeClasses,
      variantClasses,
      sizeClasses,
      animationClasses,
      interactiveClasses,
      accessibilityClasses
    ]"
    :role="role || undefined"
    :aria-label="ariaLabel || undefined"
    :tabindex="clickable ? 0 : -1"
    @click="handleClick"
    @keydown="handleKeydown"
  >
    <!-- Leading Icon -->
    <Icon
      v-if="leadingIcon"
      :name="leadingIcon"
      :class="[
        'flex-shrink-0',
        iconSizeClasses
      ]"
    />

    <!-- Status Indicator Dot -->
    <span
      v-if="showStatusDot"
      :class="[
        'flex-shrink-0 rounded-full',
        statusDotClasses,
        pulse && 'animate-pulse'
      ]"
    />

    <!-- Badge Content -->
    <span class="truncate">
      <slot>{{ label }}</slot>
    </span>

    <!-- Trailing Icon -->
    <Icon
      v-if="trailingIcon"
      :name="trailingIcon"
      :class="[
        'flex-shrink-0',
        iconSizeClasses
      ]"
    />

    <!-- Close Button -->
    <button
      v-if="closable"
      type="button"
      @click.stop="handleClose"
      :class="[
        'flex-shrink-0 ml-1 rounded-full hover:bg-black/10 dark:hover:bg-white/10 transition-colors duration-150',
        closeButtonSizeClasses
      ]"
      :aria-label="closeAriaLabel || 'Remove'"
    >
      <Icon :name="closeIcon" :class="closeIconSizeClasses" />
    </button>
  </span>
</template>

<script setup lang="ts">
import { computed } from 'vue';

// Enhanced type definitions with semantic variants
type BadgeColor = 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info' | 'gray' | 'red' | 'orange' | 'green' | 'blue' | 'yellow' | 'indigo' | 'purple' | 'pink';
type BadgeVariant = 'solid' | 'subtle' | 'outline' | 'soft' | 'ghost' | 'gradient' | 'info' | 'success' | 'warning' | 'error' | 'neutral';
type BadgeSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl';
type BadgeShape = 'rounded' | 'pill' | 'square';

interface Props {
  /**
   * The text content of the badge. Can be overridden by default slot.
   */
  label?: string;
  /**
   * Defines the color scheme of the badge.
   */
  color?: BadgeColor;
  /**
   * Controls the visual style of the badge.
   */
  variant?: BadgeVariant;
  /**
   * Adjusts the size of the badge.
   */
  size?: BadgeSize;
  /**
   * Controls the shape of the badge.
   */
  shape?: BadgeShape;
  /**
   * Icon to display before the text.
   */
  leadingIcon?: string;
  /**
   * Icon to display after the text.
   */
  trailingIcon?: string;
  /**
   * Shows a status indicator dot.
   */
  showStatusDot?: boolean;
  /**
   * Makes the status dot pulse.
   */
  pulse?: boolean;
  /**
   * Makes the badge clickable.
   */
  clickable?: boolean;
  /**
   * Shows a close button.
   */
  closable?: boolean;
  /**
   * Icon for the close button.
   */
  closeIcon?: string;
  /**
   * Aria label for accessibility.
   */
  ariaLabel?: string;
  /**
   * Aria label for close button.
   */
  closeAriaLabel?: string;
  /**
   * ARIA role for the badge.
   */
  role?: string;
  /**
   * Adds hover and focus effects.
   */
  interactive?: boolean;
  /**
   * Adds a subtle animation on mount.
   */
  animate?: boolean;
}

interface Emits {
  (e: 'click', event: MouseEvent): void;
  (e: 'close'): void;
}

const props = withDefaults(defineProps<Props>(), {
  color: 'primary',
  variant: 'solid',
  size: 'md',
  shape: 'pill',
  showStatusDot: false,
  pulse: false,
  clickable: false,
  closable: false,
  closeIcon: 'material-symbols:close',
  closeAriaLabel: 'Remove',
  role: 'status',
  interactive: false,
  animate: false,
});

const emit = defineEmits<Emits>();

// Enhanced computed properties for modern badge styling
const shapeClasses = computed(() => {
  switch (props.shape) {
    case 'rounded':
      return 'rounded-md';
    case 'pill':
      return 'rounded-full';
    case 'square':
      return 'rounded-none';
    default:
      return 'rounded-full';
  }
});

const variantClasses = computed(() => {
  const colorMap = {
    // Semantic colors using brand tokens
    primary: {
      solid: 'bg-brandPrimary text-white shadow-sm',
      subtle: 'bg-brandPrimary-50 text-brandPrimary-700 ring-1 ring-inset ring-brandPrimary-200',
      outline: 'bg-transparent text-brandPrimary ring-1 ring-inset ring-brandPrimary',
      soft: 'bg-brandPrimary-100 text-brandPrimary-800',
      ghost: 'bg-transparent text-brandPrimary hover:bg-brandPrimary-50',
      gradient: 'bg-gradient-to-r from-brandPrimary to-brandPrimary-600 text-white shadow-sm'
    },
    secondary: {
      solid: 'bg-brandSecondary text-white shadow-sm',
      subtle: 'bg-brandSecondary-50 text-brandSecondary-700 ring-1 ring-inset ring-brandSecondary-200',
      outline: 'bg-transparent text-brandSecondary ring-1 ring-inset ring-brandSecondary',
      soft: 'bg-brandSecondary-100 text-brandSecondary-800',
      ghost: 'bg-transparent text-brandSecondary hover:bg-brandSecondary-50',
      gradient: 'bg-gradient-to-r from-brandSecondary to-brandSecondary-600 text-white shadow-sm'
    },
    success: {
      solid: 'bg-brandSuccess text-white shadow-sm',
      subtle: 'bg-brandSuccess-50 text-brandSuccess-700 ring-1 ring-inset ring-brandSuccess-200',
      outline: 'bg-transparent text-brandSuccess ring-1 ring-inset ring-brandSuccess',
      soft: 'bg-brandSuccess-100 text-brandSuccess-800',
      ghost: 'bg-transparent text-brandSuccess hover:bg-brandSuccess-50',
      gradient: 'bg-gradient-to-r from-brandSuccess to-green-600 text-white shadow-sm'
    },
    warning: {
      solid: 'bg-brandWarning-500 text-white shadow-sm',
      subtle: 'bg-brandWarning-50 text-brandWarning-700 ring-1 ring-inset ring-brandWarning-200',
      outline: 'bg-transparent text-brandWarning-600 ring-1 ring-inset ring-brandWarning-500',
      soft: 'bg-brandWarning-100 text-brandWarning-800',
      ghost: 'bg-transparent text-brandWarning-600 hover:bg-brandWarning-50',
      gradient: 'bg-gradient-to-r from-brandWarning-400 to-brandWarning-600 text-white shadow-sm'
    },
    error: {
      solid: 'bg-brandDanger text-white shadow-sm',
      subtle: 'bg-brandDanger-50 text-brandDanger-700 ring-1 ring-inset ring-brandDanger-200',
      outline: 'bg-transparent text-brandDanger ring-1 ring-inset ring-brandDanger',
      soft: 'bg-brandDanger-100 text-brandDanger-800',
      ghost: 'bg-transparent text-brandDanger hover:bg-brandDanger-50',
      gradient: 'bg-gradient-to-r from-brandDanger to-red-600 text-white shadow-sm'
    },
    info: {
      solid: 'bg-brandInfo text-white shadow-sm',
      subtle: 'bg-brandInfo-50 text-brandInfo-700 ring-1 ring-inset ring-brandInfo-200',
      outline: 'bg-transparent text-brandInfo ring-1 ring-inset ring-brandInfo',
      soft: 'bg-brandInfo-100 text-brandInfo-800',
      ghost: 'bg-transparent text-brandInfo hover:bg-brandInfo-50',
      gradient: 'bg-gradient-to-r from-brandInfo to-blue-600 text-white shadow-sm'
    },
    // Standard colors
    gray: {
      solid: 'bg-gray-500 text-white shadow-sm',
      subtle: 'bg-gray-50 text-gray-700 ring-1 ring-inset ring-gray-200',
      outline: 'bg-transparent text-gray-600 ring-1 ring-inset ring-gray-500',
      soft: 'bg-gray-100 text-gray-800',
      ghost: 'bg-transparent text-gray-600 hover:bg-gray-50',
      gradient: 'bg-gradient-to-r from-gray-500 to-gray-600 text-white shadow-sm'
    }
  };

  // Add dark mode variants
  const darkModeMap = {
    primary: {
      solid: 'dark:bg-brandPrimary-600 dark:text-white',
      subtle: 'dark:bg-brandPrimary-900/50 dark:text-brandPrimary-300 dark:ring-brandPrimary-700',
      outline: 'dark:text-brandPrimary-400 dark:ring-brandPrimary-600',
      soft: 'dark:bg-brandPrimary-900/50 dark:text-brandPrimary-200',
      ghost: 'dark:text-brandPrimary-400 dark:hover:bg-brandPrimary-900/50',
      gradient: 'dark:from-brandPrimary-600 dark:to-brandPrimary-700'
    },
    // Add other colors as needed...
  };

  const baseClasses = colorMap[props.color as keyof typeof colorMap]?.[props.variant] || colorMap.gray[props.variant];
  const darkClasses = darkModeMap[props.color as keyof typeof darkModeMap]?.[props.variant] || '';

  return `${baseClasses} ${darkClasses}`;
});

const sizeClasses = computed(() => {
  const sizeMap = {
    xs: 'px-2 py-0.5 text-xs gap-1',
    sm: 'px-2.5 py-0.5 text-sm gap-1',
    md: 'px-3 py-1 text-sm gap-1.5',
    lg: 'px-3.5 py-1 text-base gap-1.5',
    xl: 'px-4 py-1.5 text-base gap-2'
  };
  return sizeMap[props.size] || sizeMap.md;
});

const iconSizeClasses = computed(() => {
  const iconSizeMap = {
    xs: 'h-3 w-3',
    sm: 'h-3.5 w-3.5',
    md: 'h-4 w-4',
    lg: 'h-4 w-4',
    xl: 'h-5 w-5'
  };
  return iconSizeMap[props.size] || iconSizeMap.md;
});

const statusDotClasses = computed(() => {
  const dotSizeMap = {
    xs: 'h-1.5 w-1.5',
    sm: 'h-2 w-2',
    md: 'h-2 w-2',
    lg: 'h-2.5 w-2.5',
    xl: 'h-3 w-3'
  };

  const dotColorMap = {
    success: 'bg-green-400',
    warning: 'bg-yellow-400',
    error: 'bg-red-400',
    info: 'bg-blue-400',
    primary: 'bg-brandPrimary',
    secondary: 'bg-brandSecondary',
    gray: 'bg-gray-400'
  };

  const sizeClass = dotSizeMap[props.size] || dotSizeMap.md;
  const colorClass = dotColorMap[props.color as keyof typeof dotColorMap] || dotColorMap.gray;

  return `${sizeClass} ${colorClass}`;
});

const closeButtonSizeClasses = computed(() => {
  const buttonSizeMap = {
    xs: 'h-3 w-3 p-0.5',
    sm: 'h-4 w-4 p-0.5',
    md: 'h-4 w-4 p-0.5',
    lg: 'h-5 w-5 p-1',
    xl: 'h-6 w-6 p-1'
  };
  return buttonSizeMap[props.size] || buttonSizeMap.md;
});

const closeIconSizeClasses = computed(() => {
  const iconSizeMap = {
    xs: 'h-2 w-2',
    sm: 'h-3 w-3',
    md: 'h-3 w-3',
    lg: 'h-3 w-3',
    xl: 'h-4 w-4'
  };
  return iconSizeMap[props.size] || iconSizeMap.md;
});

const interactiveClasses = computed(() => {
  if (!props.interactive && !props.clickable) return '';

  return 'cursor-pointer hover:scale-105 hover:shadow-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brandPrimary/50 active:scale-95';
});

const animationClasses = computed(() => {
  if (!props.animate) return '';
  return 'animate-fade-in-up';
});

const accessibilityClasses = computed(() => {
  return 'focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-brandPrimary/50';
});

// Methods
const handleClick = (event: MouseEvent) => {
  if (props.clickable || props.interactive) {
    emit('click', event);
  }
};

const handleKeydown = (event: KeyboardEvent) => {
  if ((props.clickable || props.interactive) && (event.key === 'Enter' || event.key === ' ')) {
    event.preventDefault();
    emit('click', event as any);
  }
};

const handleClose = () => {
  emit('close');
};
</script>

<style scoped>
/* Enhanced animations for badge components */
@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(4px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  animation: fade-in-up 0.3s ease-out;
}

/* Smooth transitions for all interactive states */
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 200ms;
}

/* Enhanced focus states for accessibility */
.focus-visible\:ring-2:focus-visible {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .animate-fade-in-up,
  .animate-pulse {
    animation: none;
  }

  .transition-all {
    transition: none;
  }
}
</style>