# GlobalPageHeader Filters Integration Guide

This guide shows how to integrate the robust filters component with existing pages in your Legal SaaS application.

## Quick Integration Steps

### 1. Update Your Page Template

Replace your existing page header or add the GlobalPageHeader with filters:

```vue
<template>
  <div class="space-y-6 py-6">
    <!-- Replace existing header with GlobalPageHeader -->
    <GlobalPageHeader
      title="Your Page Title"
      description="Page description"
      icon="heroicons:your-icon"
      :show-filters="true"
      :filter-configs="filterConfigs"
      :advanced-filter-configs="advancedFilterConfigs"
      :initial-filters="currentFilters"
      :stats="pageStats"
      :actions="headerActions"
      @filter-change="handleFilterChange"
      @filters-change="handleFiltersChange"
      @clear-filters="handleClearFilters"
    />
    
    <!-- Your existing content -->
    <YourExistingContent :items="filteredItems" />
  </div>
</template>
```

### 2. Add Filter Configuration

```vue
<script setup lang="ts">
// State for filters
const currentFilters = ref<Record<string, any>>({})

// Basic filters configuration
const filterConfigs = [
  {
    key: 'status',
    options: [
      { label: 'Active', value: 'active' },
      { label: 'Inactive', value: 'inactive' }
    ],
    placeholder: 'All Status'
  },
  {
    key: 'category',
    options: [
      { label: 'Legal', value: 'legal' },
      { label: 'Business', value: 'business' }
    ],
    placeholder: 'All Categories'
  }
]

// Advanced filters configuration
const advancedFilterConfigs = [
  {
    key: 'dateRange',
    type: 'dateRange' as const,
    label: 'Date Range'
  },
  {
    key: 'customFilter',
    type: 'custom' as const,
    label: 'Custom Filter'
  }
]
</script>
```

### 3. Implement Filter Logic

```vue
<script setup lang="ts">
// Computed property for filtered data
const filteredItems = computed(() => {
  let items = [...yourDataStore.items]
  
  Object.entries(currentFilters.value).forEach(([key, value]) => {
    if (value && value !== '') {
      switch (key) {
        case 'status':
        case 'category':
          items = items.filter(item => item[key] === value)
          break
        case 'dateRange':
          if (typeof value === 'object' && value.start && value.end) {
            items = items.filter(item => {
              const itemDate = new Date(item.createdAt).toISOString().split('T')[0]
              return itemDate >= value.start && itemDate <= value.end
            })
          }
          break
      }
    }
  })
  
  return items
})

// Event handlers
const handleFilterChange = (data: { key: string; value: any }) => {
  console.log('Filter changed:', data.key, data.value)
}

const handleFiltersChange = (filters: Record<string, any>) => {
  currentFilters.value = { ...filters }
}

const handleClearFilters = () => {
  currentFilters.value = {}
}
</script>
```

## Real-World Example: Tenant Management

See `pages/dashboard/tenants/manage-with-filters.vue` for a complete implementation that shows:

### ✅ **Complete Integration Features**
- **Basic dropdown filters** for status and plan
- **Advanced filters** with date ranges and custom components
- **Visual plan selection** with custom slot
- **User count range slider** with custom implementation
- **Active filters summary** with removable filter tags
- **Real-time filtering** of table and card views
- **Export functionality** integration
- **Statistics display** in header
- **Empty states** handling

### ✅ **Advanced Custom Filters**

#### Visual Plan Selection
```vue
<template #advanced-filter-planType="{ filter, value }">
  <div class="grid grid-cols-3 gap-2">
    <UiButton
      v-for="plan in planTypes"
      :key="plan.value"
      @click="setPlanFilter(plan.value)"
      :variant="value === plan.value ? 'contained' : 'outline'"
      :color="getPlanColor(plan.value)"
      size="sm"
      class="flex flex-col items-center p-3 h-auto"
    >
      <Icon :name="plan.icon" class="h-5 w-5 mb-1" />
      <span class="text-xs">{{ plan.label }}</span>
    </UiButton>
  </div>
</template>
```

#### Range Slider
```vue
<template #advanced-filter-userRange="{ filter, value }">
  <div class="space-y-3">
    <div class="flex justify-between text-sm">
      <span>{{ userRangeValue.min }} users</span>
      <span>{{ userRangeValue.max }} users</span>
    </div>
    <input
      type="range"
      v-model="userRangeValue.min"
      @input="updateUserRange"
      class="w-full"
    />
    <input
      type="range"
      v-model="userRangeValue.max"
      @input="updateUserRange"
      class="w-full"
    />
  </div>
</template>
```

### ✅ **Filter State Management**

```vue
<script setup lang="ts">
// Reactive filter state
const currentFilters = ref<Record<string, any>>({})

// Computed properties for filter analysis
const hasActiveFilters = computed(() => {
  return Object.values(currentFilters.value).some(value => 
    value !== null && value !== undefined && value !== ''
  )
})

const activeFiltersCount = computed(() => {
  return Object.values(currentFilters.value).filter(value => 
    value !== null && value !== undefined && value !== ''
  ).length
})

// Filter application logic
const filteredTenants = computed(() => {
  let tenants = [...tenantStore.getTenants]
  
  Object.entries(currentFilters.value).forEach(([key, value]) => {
    if (value && value !== '') {
      // Apply different filter types
      switch (key) {
        case 'status':
        case 'plan':
          tenants = tenants.filter(tenant => tenant[key] === value)
          break
        case 'createdDate':
          // Handle date range filtering
          break
        case 'userRange':
          // Handle number range filtering
          break
      }
    }
  })
  
  return tenants
})
</script>
```

## Migration from Existing Pages

### If you have definePageMeta configuration:

**Before:**
```vue
definePageMeta({
  layout: 'dashboard',
  title: 'Manage Tenants',
  showPageHeader: true,
  pageHeaderActions: () => [...]
})
```

**After:**
```vue
definePageMeta({
  layout: 'dashboard',
  // Remove showPageHeader and pageHeaderActions
  // These are now handled by GlobalPageHeader component
})

// Add in template:
<GlobalPageHeader
  title="Manage Tenants"
  :actions="headerActions"
  :show-filters="true"
  ...
/>
```

### If you have existing search/filter components:

1. **Remove** old search/filter components
2. **Extract** filter configurations to the new format
3. **Update** event handlers to use new filter events
4. **Migrate** custom filters to slot-based approach

## Best Practices

### 1. **Filter Configuration**
- Keep basic filters simple and commonly used
- Use advanced filters for complex scenarios
- Provide clear, descriptive labels

### 2. **Performance**
- Use computed properties for filtering
- Debounce API calls if filtering server-side
- Consider pagination for large datasets

### 3. **User Experience**
- Show active filter count and summary
- Provide clear filter removal options
- Handle empty states gracefully
- Persist filter state when appropriate

### 4. **Accessibility**
- Ensure proper ARIA labels
- Support keyboard navigation
- Provide screen reader friendly descriptions

## Testing Your Integration

1. **Basic Functionality**
   - ✅ Filters apply correctly
   - ✅ Clear filters works
   - ✅ Advanced filters toggle

2. **Edge Cases**
   - ✅ Empty filter values
   - ✅ Invalid date ranges
   - ✅ No results found

3. **Performance**
   - ✅ Large datasets filter smoothly
   - ✅ No memory leaks on filter changes
   - ✅ Responsive on mobile devices

## Need Help?

- Check the complete example in `pages/dashboard/tenants/manage-with-filters.vue`
- Review the component documentation in `README-GlobalPageHeaderFilters.md`
- Test your implementation with `pages/dashboard/test-page-header-filters.vue`
