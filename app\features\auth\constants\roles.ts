/**
 * Authentication Roles and Permissions
 * 
 * Centralized role and permission definitions for the authentication system
 */

export enum PlatformRoles {
  SUPER_ADMIN = 'super-admin',
  SUPPORT = 'support'
}

export enum TenantRoles {
  TENANT_OWNER = 'tenant-owner',
  ADMIN = 'admin',
  LAWYER = 'lawyer',
  PARALEGAL = 'paralegal',
  CLIENT = 'client',
  GUEST = 'guest'
}

export enum Permissions {
  // User management
  USER_CREATE = 'user:create',
  USER_READ = 'user:read',
  USER_UPDATE = 'user:update',
  USER_DELETE = 'user:delete',
  USER_INVITE = 'user:invite',
  
  // Case management
  CASE_CREATE = 'case:create',
  CASE_READ = 'case:read',
  CASE_UPDATE = 'case:update',
  CASE_DELETE = 'case:delete',
  CASE_ASSIGN = 'case:assign',
  
  // Document management
  DOCUMENT_CREATE = 'document:create',
  DOCUMENT_READ = 'document:read',
  DOCUMENT_UPDATE = 'document:update',
  DOCUMENT_DELETE = 'document:delete',
  DOCUMENT_SHARE = 'document:share',
  
  // Template management
  TEMPLATE_CREATE = 'template:create',
  TEMPLATE_READ = 'template:read',
  TEMPLATE_UPDATE = 'template:update',
  TEMPLATE_DELETE = 'template:delete',
  TEMPLATE_PUBLISH = 'template:publish',
  
  // Settings management
  SETTINGS_READ = 'settings:read',
  SETTINGS_UPDATE = 'settings:update',
  SETTINGS_PLATFORM = 'settings:platform',
  
  // Tenant management
  TENANT_CREATE = 'tenant:create',
  TENANT_READ = 'tenant:read',
  TENANT_UPDATE = 'tenant:update',
  TENANT_DELETE = 'tenant:delete',
  TENANT_BILLING = 'tenant:billing',
  
  // Audit and compliance
  AUDIT_READ = 'audit:read',
  COMPLIANCE_MANAGE = 'compliance:manage',
  
  // Notifications
  NOTIFICATION_SEND = 'notification:send',
  NOTIFICATION_MANAGE = 'notification:manage'
}

// Role-Permission mapping
export const ROLE_PERMISSIONS: Record<PlatformRoles | TenantRoles, Permissions[]> = {
  [PlatformRoles.SUPER_ADMIN]: Object.values(Permissions),
  [PlatformRoles.SUPPORT]: Object.values(Permissions),
  [TenantRoles.TENANT_OWNER]: [
    Permissions.USER_CREATE,
    Permissions.USER_READ,
    Permissions.USER_UPDATE,
    Permissions.USER_DELETE,
    Permissions.USER_INVITE,
    Permissions.CASE_CREATE,
    Permissions.CASE_READ,
    Permissions.CASE_UPDATE,
    Permissions.CASE_DELETE,
    Permissions.CASE_ASSIGN,
    Permissions.DOCUMENT_CREATE,
    Permissions.DOCUMENT_READ,
    Permissions.DOCUMENT_UPDATE,
    Permissions.DOCUMENT_DELETE,
    Permissions.DOCUMENT_SHARE,
    Permissions.TEMPLATE_CREATE,
    Permissions.TEMPLATE_READ,
    Permissions.TEMPLATE_UPDATE,
    Permissions.TEMPLATE_DELETE,
    Permissions.TEMPLATE_PUBLISH,
    Permissions.SETTINGS_READ,
    Permissions.SETTINGS_UPDATE,
    Permissions.TENANT_READ,
    Permissions.TENANT_UPDATE,
    Permissions.TENANT_BILLING,
    Permissions.AUDIT_READ,
    Permissions.COMPLIANCE_MANAGE,
    Permissions.NOTIFICATION_SEND,
    Permissions.NOTIFICATION_MANAGE
  ],
  
  [TenantRoles.ADMIN]: [
    Permissions.USER_CREATE,
    Permissions.USER_READ,
    Permissions.USER_UPDATE,
    Permissions.USER_INVITE,
    Permissions.CASE_CREATE,
    Permissions.CASE_READ,
    Permissions.CASE_UPDATE,
    Permissions.CASE_ASSIGN,
    Permissions.DOCUMENT_CREATE,
    Permissions.DOCUMENT_READ,
    Permissions.DOCUMENT_UPDATE,
    Permissions.DOCUMENT_SHARE,
    Permissions.TEMPLATE_CREATE,
    Permissions.TEMPLATE_READ,
    Permissions.TEMPLATE_UPDATE,
    Permissions.TEMPLATE_PUBLISH,
    Permissions.SETTINGS_READ,
    Permissions.AUDIT_READ,
    Permissions.NOTIFICATION_SEND
  ],
  
  [TenantRoles.LAWYER]: [
    Permissions.USER_READ,
    Permissions.CASE_CREATE,
    Permissions.CASE_READ,
    Permissions.CASE_UPDATE,
    Permissions.DOCUMENT_CREATE,
    Permissions.DOCUMENT_READ,
    Permissions.DOCUMENT_UPDATE,
    Permissions.DOCUMENT_SHARE,
    Permissions.TEMPLATE_READ,
    Permissions.TEMPLATE_UPDATE,
    Permissions.SETTINGS_READ,
    Permissions.NOTIFICATION_SEND
  ],
  
  [TenantRoles.PARALEGAL]: [
    Permissions.USER_READ,
    Permissions.CASE_READ,
    Permissions.CASE_UPDATE,
    Permissions.DOCUMENT_CREATE,
    Permissions.DOCUMENT_READ,
    Permissions.DOCUMENT_UPDATE,
    Permissions.TEMPLATE_READ,
    Permissions.SETTINGS_READ
  ],
  
  [TenantRoles.CLIENT]: [
    Permissions.CASE_READ,
    Permissions.DOCUMENT_READ,
    Permissions.SETTINGS_READ
  ],
  
  [TenantRoles.GUEST]: [
    Permissions.CASE_READ,
    Permissions.DOCUMENT_READ
  ]
}

// Role hierarchy (higher roles inherit permissions from lower roles)
export const ROLE_HIERARCHY: Record<PlatformRoles | TenantRoles, number> = {
  [PlatformRoles.SUPER_ADMIN]: 100,
  [PlatformRoles.SUPPORT]: 95, // Added SUPPORT role
  [TenantRoles.TENANT_OWNER]: 90,
  [TenantRoles.ADMIN]: 80,
  [TenantRoles.LAWYER]: 70,
  [TenantRoles.PARALEGAL]: 60,
  [TenantRoles.CLIENT]: 50,
  [TenantRoles.GUEST]: 10
}

// Role display names
export const ROLE_LABELS: Record<PlatformRoles | TenantRoles, string> = {
  [PlatformRoles.SUPER_ADMIN]: 'Super Administrator',
  [PlatformRoles.SUPPORT]: 'Platform Support',
  [TenantRoles.TENANT_OWNER]: 'Tenant Owner',
  [TenantRoles.ADMIN]: 'Administrator',
  [TenantRoles.LAWYER]: 'Lawyer',
  [TenantRoles.PARALEGAL]: 'Paralegal',
  [TenantRoles.CLIENT]: 'Client',
  [TenantRoles.GUEST]: 'Guest'
}

// Permission display names
export const PERMISSION_LABELS: Record<Permissions, string> = {
  [Permissions.USER_CREATE]: 'Create Users',
  [Permissions.USER_READ]: 'View Users',
  [Permissions.USER_UPDATE]: 'Edit Users',
  [Permissions.USER_DELETE]: 'Delete Users',
  [Permissions.USER_INVITE]: 'Invite Users',
  [Permissions.CASE_CREATE]: 'Create Cases',
  [Permissions.CASE_READ]: 'View Cases',
  [Permissions.CASE_UPDATE]: 'Edit Cases',
  [Permissions.CASE_DELETE]: 'Delete Cases',
  [Permissions.CASE_ASSIGN]: 'Assign Cases',
  [Permissions.DOCUMENT_CREATE]: 'Create Documents',
  [Permissions.DOCUMENT_READ]: 'View Documents',
  [Permissions.DOCUMENT_UPDATE]: 'Edit Documents',
  [Permissions.DOCUMENT_DELETE]: 'Delete Documents',
  [Permissions.DOCUMENT_SHARE]: 'Share Documents',
  [Permissions.TEMPLATE_CREATE]: 'Create Templates',
  [Permissions.TEMPLATE_READ]: 'View Templates',
  [Permissions.TEMPLATE_UPDATE]: 'Edit Templates',
  [Permissions.TEMPLATE_DELETE]: 'Delete Templates',
  [Permissions.TEMPLATE_PUBLISH]: 'Publish Templates',
  [Permissions.SETTINGS_READ]: 'View Settings',
  [Permissions.SETTINGS_UPDATE]: 'Edit Settings',
  [Permissions.SETTINGS_PLATFORM]: 'Platform Settings',
  [Permissions.TENANT_CREATE]: 'Create Tenants',
  [Permissions.TENANT_READ]: 'View Tenants',
  [Permissions.TENANT_UPDATE]: 'Edit Tenants',
  [Permissions.TENANT_DELETE]: 'Delete Tenants',
  [Permissions.TENANT_BILLING]: 'Manage Billing',
  [Permissions.AUDIT_READ]: 'View Audit Logs',
  [Permissions.COMPLIANCE_MANAGE]: 'Manage Compliance',
  [Permissions.NOTIFICATION_SEND]: 'Send Notifications',
  [Permissions.NOTIFICATION_MANAGE]: 'Manage Notifications'
}
