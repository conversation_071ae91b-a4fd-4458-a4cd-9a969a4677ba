<template>
  <div class="space-y-6">
    <!-- Header Section -->
    <div class="text-center">
      <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100 dark:bg-green-900/20">
        <Icon name="material-symbols:mail-outline" class="h-6 w-6 text-green-600 dark:text-green-400" />
      </div>
      <h2 class="mt-6 text-3xl font-bold tracking-tight text-gray-900 dark:text-white">
        {{ $t("Accept Invitation") }}
      </h2>
      <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">
        {{
          inviteInfo?.message ||
          $t("Welcome! Please set your password to activate your account.")
        }}
      </p>
    </div>

    <!-- Loading State -->
    <div v-if="isValidatingToken" class="flex justify-center py-8">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-brandPrimary"></div>
    </div>

    <!-- Invalid Token State -->
    <div v-else-if="tokenError" class="text-center py-8">
      <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 dark:bg-red-900/20">
        <Icon name="material-symbols:error-outline" class="h-6 w-6 text-red-600 dark:text-red-400" />
      </div>
      <h3 class="mt-4 text-lg font-medium text-gray-900 dark:text-white">
        {{ $t("Invalid or Expired Invitation") }}
      </h3>
      <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">
        {{ tokenError }}
      </p>
      <div class="mt-6">
        <UiButton @click="$router.push('/auth/login')" variant="outline">
          {{ $t("Back to Login") }}
        </UiButton>
      </div>
    </div>

    <!-- Accept Invite Form -->
    <div v-else-if="inviteInfo">
      <!-- User Information Display -->
      <div class="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4 mb-6">
        <div class="flex items-center space-x-3">
          <div class="flex-shrink-0">
            <div class="h-10 w-10 rounded-full bg-brandPrimary/10 flex items-center justify-center">
              <Icon name="material-symbols:person" class="h-5 w-5 text-brandPrimary" />
            </div>
          </div>
          <div class="flex-1 min-w-0">
            <p class="text-sm font-medium text-gray-900 dark:text-white">
              {{ inviteInfo.email }}
            </p>
            <p class="text-sm text-gray-500 dark:text-gray-400">
              {{ $t("Invited to join") }}
              {{ inviteInfo.tenantName || $t("the platform") }}
              <span v-if="inviteInfo.role" class="ml-1">
                {{ $t("as") }} {{ formatRole(inviteInfo.role) }}
              </span>
            </p>
          </div>
        </div>
      </div>

      <!-- Form -->
      <forms-form ref="formRef" :schema="acceptInviteSchema" :initial-values="initialValues"
        :validation-schema="validationSchema" @values="handleValues" @submit="handleAcceptInvite"
        @errors="handleFormErrors" />

      <!-- Submit Button -->
      <div class="mt-6">
        <UiButton @click="submitForm" :loading="isSubmitting" :disabled="!canSubmit" variant="contained" color="primary"
          size="lg" class="w-full">
          <Icon name="material-symbols:lock" class="h-5 w-5 mr-2" />
          {{ $t("Activate Account") }}
        </UiButton>
      </div>

      <!-- Help Text -->
      <div class="mt-6 text-center">
        <p class="text-xs text-gray-500 dark:text-gray-400">
          {{ $t("By activating your account, you agree to our") }}
          <NuxtLink to="/terms" class="text-brandPrimary hover:text-brandPrimary/80">
            {{ $t("Terms of Service") }}
          </NuxtLink>
          {{ $t("and") }}
          <NuxtLink to="/privacy" class="text-brandPrimary hover:text-brandPrimary/80">
            {{ $t("Privacy Policy") }}
          </NuxtLink>
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import { z } from "zod";
import { formatRole } from "~/app/features/auth/utils/formatters";
import { VALIDATION_PATTERNS } from "~/app/features/auth/constants/auth";
import { grid } from "@unovis/ts/components/axis/style";

// Types
interface InviteInfo {
  email: string;
  tenantName?: string;
  role?: string;
  message?: string;
  expiresAt?: string;
}

interface AcceptInviteFormData {
  firstName: string;
  lastName: string;
  password: string;
  confirmPassword: string;
}

definePageMeta({
  layout: "invite",
  title: "Accept Invitation",
  subtitle: "Join the platform",
    middleware: ['guest'],
  meta: [
    {
      name: "description",
      content: "Accept your invitation to join the Legal SaaS Platform.",
    },
  ],
});

// Composables
const route = useRoute();
const router = useRouter();

// Reactive state
const formRef = ref<any>(null);
const isValidatingToken = ref(true);
const isSubmitting = ref(false);
const tokenError = ref<string | null>(null);
const inviteInfo = ref<InviteInfo | null>(null);
const formErrors = ref<Record<string, string>>({});
const values = ref<Record<string, string>>({})

// Get token from route
const token = computed(() => route.params.token as string);

// Initial form values
const initialValues = ref<AcceptInviteFormData>({
  firstName: "",
  lastName: "",
  password: "",
  confirmPassword: "",
});

const handleValues = (data: any) => {
  console.log(data);

  values.value = data;
};

// Validation schema
const validationSchema = z
  .object({
    firstName: z.string().min(1, "First name is required").max(50, "First name is too long"),
    lastName: z.string().min(1, "Last name is required").max(50, "Last name is too long"),
    password: z
      .string()
      .min(8, "Password must be at least 8 characters")
      .regex(
        VALIDATION_PATTERNS.PASSWORD_STRONG,
        "Password must contain uppercase, lowercase, number, and special character"
      ),
    confirmPassword: z.string().min(1, "Please confirm your password"),

  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords do not match",
    path: ["confirmPassword"],
  });

// Form schema for forms-form component
const acceptInviteSchema = [
  {
    grid: {
      cols: 2,
      content: [
        {
          name: "firstName", label: "First Name", component: "UiInput", rules: z.string().min(1, "First name is required").max(50, "First name is too long"), props: {
            type: "text",
            placeholder: "John",
            leadingIcon: "material-symbols:person",
            autocomplete: "given-name",
            helpText: "First name",
          },
        },
        {
          name: "lastName", label: "Last Name", component: "UiInput", rules: z.string().min(1, "Last name is required").max(50, "Last name is too long"), props: {
            type: "text",
            placeholder: "Doe",
            leadingIcon: "material-symbols:person",
            autocomplete: "family-name",
            helpText: "Last name",
          },
        },
      ]
    }
  },
  {
    asyncComponent: defineAsyncComponent(() => import("@/components/ui/UiHr.vue")), props: { size: "xs" }
  },
  {
    name: "password",
    label: "Password",
    component: "UiInput",
    rules: z
      .string()
      .min(8, "Password must be at least 8 characters")
      .regex(
        VALIDATION_PATTERNS.PASSWORD_STRONG,
        "Password must contain uppercase, lowercase, number, and special character"
      ),
    props: {
      type: "password",
      placeholder: "Create a strong password",
      leadingIcon: "material-symbols:lock",
      autocomplete: "new-password",
      helpText:
        "Password must be at least 8 characters with uppercase, lowercase, number, and special character",
    },
  },
  {
    name: "confirmPassword",
    label: "Confirm Password",
    component: "UiInput",
    rules: z.string().min(1, "Please confirm your password"),
    props: {
      type: "password",
      placeholder: "Confirm your password",
      leadingIcon: "material-symbols:lock",
      autocomplete: "new-password",
    },
  },

];

// Computed properties
const canSubmit = computed(() => {
  if (!values.value) return false;

  return (
    Object.keys(formErrors.value).length === 0 &&
    values.value.firstName &&
    values.value.lastName &&
    values.value.password &&
    values.value.confirmPassword
  );
});

// Methods
const validateInviteToken = async () => {
  if (!token.value) {
    tokenError.value = "Invalid invitation token";
    isValidatingToken.value = false;
    return;
  }

  try {
    isValidatingToken.value = true;

    // Call API directly to validate token and get invite info
    const api = useApi();
    const response = await api.get(`/auth/invite/validate/${token.value}`);
    if (!(response as any).valid)
      throw new Error((response as any).error);

    inviteInfo.value = response as InviteInfo;
  } catch (error: any) {
    console.error("Token validation error:", error);

    if (error.response?.status === 404) {
      tokenError.value = "This invitation link is invalid or has been used already";
    } else if (error.response?.status === 410) {
      tokenError.value = "This invitation has expired. Please request a new invitation";
    } else {
      tokenError.value =
        error.response?.data?.message ||
        "Unable to validate invitation. Please try again or contact support";
    }
  } finally {
    isValidatingToken.value = false;
  }
};

const handleAcceptInvite = async (formData: AcceptInviteFormData) => {
  if (!token.value || !inviteInfo.value) {
    return;
  }

  isSubmitting.value = true;

  try {
    // Prepare payload for accept invite API
    const payload = {
      firstName: formData.firstName,
      lastName: formData.lastName,
      token: token.value,
      password: formData.password
    };

    // Call API directly to accept invitation
    const api = useApi();
    await api.post("/auth/accept-invite", payload);

    // Show success notification
    const { $notification } = useNuxtApp();
    if ($notification) {
      ($notification as any).success({
        title: "Account Activated",
        message: "Your account has been activated successfully! You can now log in.",
      });
    }

    // Redirect to login with success message
    await router.push({
      path: "/auth/login",
      query: {
        message: "account_activated",
        email: inviteInfo.value.email,
      },
    });
  } catch (error: any) {
    console.error("Accept invite error:", error);

    const { $notification } = useNuxtApp();

    if (error.response?.status === 400) {
      if ($notification) {
        ($notification as any).error({
          title: "Invalid Data",
          message: error.response.data?.message || "Invalid invitation data",
        });
      }
    } else if (error.response?.status === 410) {
      if ($notification) {
        ($notification as any).error({
          title: "Invitation Expired",
          message: "This invitation has expired",
        });
      }
      tokenError.value = "This invitation has expired. Please request a new invitation";
    } else {
      if ($notification) {
        ($notification as any).error({
          title: "Activation Failed",
          message:
            error.response?.data?.message ||
            "Failed to activate account. Please try again or contact support",
        });
      }
    }
  } finally {
    isSubmitting.value = false;
  }
};

const handleFormErrors = (errors: Record<string, string>) => {
  console.log("Form errors:", errors);
  formErrors.value = errors;
};

const submitForm = () => {
  if (formRef.value) {
    formRef.value.submitForm();
  }
};

// Lifecycle
onMounted(() => {
  validateInviteToken();
});
</script>
