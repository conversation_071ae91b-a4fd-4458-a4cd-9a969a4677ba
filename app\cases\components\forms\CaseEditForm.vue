<!--
  Case Edit Form Component
  
  Form for editing existing legal cases with pre-populated data
-->

<template>
  <div class="max-w-4xl mx-auto space-y-6">
    <!-- Loading State -->
    <div v-if="isLoading" class="flex items-center justify-center py-12">
      <UiSpinner size="lg" />
      <span class="ml-3 text-gray-600">Loading case...</span>
    </div>

    <!-- Case Not Found -->
    <div v-else-if="!case_" class="text-center py-12">
      <Icon name="heroicons:exclamation-triangle" class="mx-auto h-12 w-12 text-gray-400" />
      <h3 class="mt-2 text-sm font-medium text-gray-900">Case not found</h3>
      <p class="mt-1 text-sm text-gray-500">The case you're trying to edit doesn't exist.</p>
      <div class="mt-6">
        <NuxtLink
          to="/dashboard/cases"
          class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-indigo-600 bg-indigo-100 hover:bg-indigo-200"
        >
          Back to Cases
        </NuxtLink>
      </div>
    </div>

    <!-- Edit Form -->
    <template v-else>
      <!-- Header -->
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">Edit Case</h1>
          <p class="mt-1 text-sm text-gray-500">
            Update the details for case #{{ case_.caseNumber }}
          </p>
        </div>
        
        <div class="flex space-x-3">
          <NuxtLink
            :to="`/dashboard/cases/${case_.id}`"
            class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
          >
            <Icon name="heroicons:eye" class="h-4 w-4 mr-2" />
            View Case
          </NuxtLink>
          
          <NuxtLink
            to="/dashboard/cases"
            class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
          >
            <Icon name="heroicons:arrow-left" class="h-4 w-4 mr-2" />
            Back to Cases
          </NuxtLink>
        </div>
      </div>

      <!-- Form -->
      <form @submit.prevent="handleSubmit" class="space-y-6">
        <!-- Case Information -->
        <div class="bg-white shadow rounded-lg p-6">
          <h2 class="text-lg font-medium text-gray-900 mb-6">Case Information</h2>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Case Title -->
            <div class="md:col-span-2">
              <label for="title" class="block text-sm font-medium text-gray-700 mb-2">
                Case Title *
              </label>
              <input
                id="title"
                v-model="form.title"
                type="text"
                required
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              />
            </div>

            <!-- Case Type -->
            <div>
              <label for="type" class="block text-sm font-medium text-gray-700 mb-2">
                Case Type *
              </label>
              <select
                id="type"
                v-model="form.type"
                required
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              >
                <option value="contract">Contract Law</option>
                <option value="employment">Employment Law</option>
                <option value="corporate">Corporate Law</option>
                <option value="litigation">Litigation</option>
                <option value="intellectual-property">Intellectual Property</option>
                <option value="real-estate">Real Estate</option>
                <option value="family">Family Law</option>
                <option value="criminal">Criminal Law</option>
              </select>
            </div>

            <!-- Priority -->
            <div>
              <label for="priority" class="block text-sm font-medium text-gray-700 mb-2">
                Priority *
              </label>
              <select
                id="priority"
                v-model="form.priority"
                required
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              >
                <option value="low">Low</option>
                <option value="medium">Medium</option>
                <option value="high">High</option>
                <option value="urgent">Urgent</option>
              </select>
            </div>

            <!-- Status -->
            <div>
              <label for="status" class="block text-sm font-medium text-gray-700 mb-2">
                Status *
              </label>
              <select
                id="status"
                v-model="form.status"
                required
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              >
                <option value="active">Active</option>
                <option value="pending">Pending</option>
                <option value="closed">Closed</option>
                <option value="archived">Archived</option>
              </select>
            </div>

            <!-- Description -->
            <div class="md:col-span-2">
              <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                Case Description
              </label>
              <textarea
                id="description"
                v-model="form.description"
                rows="4"
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              ></textarea>
            </div>
          </div>
        </div>

        <!-- Client Information -->
        <div class="bg-white shadow rounded-lg p-6">
          <h2 class="text-lg font-medium text-gray-900 mb-6">Client Information</h2>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Client Name -->
            <div>
              <label for="clientName" class="block text-sm font-medium text-gray-700 mb-2">
                Client Name *
              </label>
              <input
                id="clientName"
                v-model="form.client.name"
                type="text"
                required
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              />
            </div>

            <!-- Client Email -->
            <div>
              <label for="clientEmail" class="block text-sm font-medium text-gray-700 mb-2">
                Client Email *
              </label>
              <input
                id="clientEmail"
                v-model="form.client.email"
                type="email"
                required
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              />
            </div>

            <!-- Client Phone -->
            <div>
              <label for="clientPhone" class="block text-sm font-medium text-gray-700 mb-2">
                Client Phone
              </label>
              <input
                id="clientPhone"
                v-model="form.client.phone"
                type="tel"
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              />
            </div>

            <!-- Client Company -->
            <div>
              <label for="clientCompany" class="block text-sm font-medium text-gray-700 mb-2">
                Company/Organization
              </label>
              <input
                id="clientCompany"
                v-model="form.client.company"
                type="text"
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              />
            </div>
          </div>
        </div>

        <!-- Case Details -->
        <div class="bg-white shadow rounded-lg p-6">
          <h2 class="text-lg font-medium text-gray-900 mb-6">Case Details</h2>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Start Date -->
            <div>
              <label for="startDate" class="block text-sm font-medium text-gray-700 mb-2">
                Start Date
              </label>
              <input
                id="startDate"
                v-model="form.startDate"
                type="date"
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              />
            </div>

            <!-- Expected End Date -->
            <div>
              <label for="expectedEndDate" class="block text-sm font-medium text-gray-700 mb-2">
                Expected End Date
              </label>
              <input
                id="expectedEndDate"
                v-model="form.expectedEndDate"
                type="date"
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              />
            </div>

            <!-- Billing Rate -->
            <div>
              <label for="billingRate" class="block text-sm font-medium text-gray-700 mb-2">
                Hourly Billing Rate ($)
              </label>
              <input
                id="billingRate"
                v-model="form.billingRate"
                type="number"
                min="0"
                step="0.01"
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              />
            </div>

            <!-- Estimated Hours -->
            <div>
              <label for="estimatedHours" class="block text-sm font-medium text-gray-700 mb-2">
                Estimated Hours
              </label>
              <input
                id="estimatedHours"
                v-model="form.estimatedHours"
                type="number"
                min="0"
                step="0.5"
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              />
            </div>
          </div>
        </div>

        <!-- Form Actions -->
        <div class="flex justify-end space-x-3">
          <NuxtLink
            :to="`/dashboard/cases/${case_.id}`"
            class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
          >
            Cancel
          </NuxtLink>
          
          <button
            type="submit"
            :disabled="isSubmitting"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <UiSpinner v-if="isSubmitting" size="sm" class="mr-2" />
            <Icon v-else name="heroicons:check" class="h-4 w-4 mr-2" />
            {{ isSubmitting ? 'Saving...' : 'Save Changes' }}
          </button>
        </div>
      </form>
    </template>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'

// Composables
const route = useRoute()
const router = useRouter()

// Types
interface CaseData {
  id: string
  title: string
  caseNumber: string
  description: string
  status: string
  priority: string
  type: string
  client: {
    name: string
    email: string
    phone: string
    company: string
  }
  startDate: string
  expectedEndDate: string
  billingRate: number
  estimatedHours: number
}

// State
const isLoading = ref(true)
const isSubmitting = ref(false)
const case_ = ref<CaseData | null>(null)

const form = reactive({
  title: '',
  type: '',
  priority: '',
  status: '',
  description: '',
  client: {
    name: '',
    email: '',
    phone: '',
    company: ''
  },
  startDate: '',
  expectedEndDate: '',
  billingRate: '',
  estimatedHours: ''
})

// Mock data
const mockCase = {
  id: '1',
  title: 'Contract Dispute Resolution',
  caseNumber: 'CASE-2024-001',
  description: 'Legal dispute regarding breach of contract terms.',
  status: 'active',
  priority: 'high',
  type: 'contract',
  client: {
    name: 'Acme Corporation',
    email: '<EMAIL>',
    phone: '+****************',
    company: 'Acme Corporation'
  },
  startDate: '2024-01-15',
  expectedEndDate: '2024-06-15',
  billingRate: 350,
  estimatedHours: 100
}

// Methods
const loadCase = async () => {
  try {
    const caseId = route.params.id
    console.log('Loading case for edit:', caseId)
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    case_.value = mockCase
    
    // Populate form with case data
    Object.assign(form, {
      title: case_.value.title,
      type: case_.value.type,
      priority: case_.value.priority,
      status: case_.value.status,
      description: case_.value.description,
      client: { ...case_.value.client },
      startDate: case_.value.startDate,
      expectedEndDate: case_.value.expectedEndDate,
      billingRate: case_.value.billingRate?.toString() || '',
      estimatedHours: case_.value.estimatedHours?.toString() || ''
    })
    
  } catch (error) {
    console.error('Error loading case:', error)
  } finally {
    isLoading.value = false
  }
}

const handleSubmit = async () => {
  try {
    isSubmitting.value = true
    
    // Validate form
    if (!form.title || !form.type || !form.priority || !form.status || !form.client.name || !form.client.email) {
      alert('Please fill in all required fields')
      return
    }
    
    // Update case data
    const updatedCase = {
      ...form,
      billingRate: form.billingRate ? parseFloat(form.billingRate) : null,
      estimatedHours: form.estimatedHours ? parseFloat(form.estimatedHours) : null,
      updatedAt: new Date().toISOString()
    }
    
    // TODO: Replace with actual API call
    console.log('Updating case:', updatedCase)
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // Success - redirect to case detail
    router.push(`/dashboard/cases/${case_.value.id}`)
    
  } catch (error) {
    console.error('Error updating case:', error)
    alert('Failed to update case. Please try again.')
  } finally {
    isSubmitting.value = false
  }
}

// Lifecycle
onMounted(() => {
  loadCase()
})
</script>
