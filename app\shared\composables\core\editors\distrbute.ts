import { Extension, mergeAttributes } from "@tiptap/core";

export interface DistributeOptions {
  types: string[];
}

declare module "@tiptap/core" {
  interface Commands<ReturnType> {
    distribute: {
      /**
       * Toggle text-align-last: justify for distributing text
       */
      distribute: () => ReturnType;
    };
  }
}

export const Distribute = Extension.create<DistributeOptions>({
  name: "distribute",
  addOptions() {
    return {
      types: ["heading", "paragraph"],
    };
  },
  addCommands() {
    return {
      distribute: () => ({ commands, state }) => {
        const { selection } = state;
        const { $from } = selection;
        const node = $from.node($from.depth);

        if (!node) return false;

        // Get the current node type
        const nodeType = $from.parent.type.name;

        // Check if the current node type is in the allowed types
        if (!this.options.types.includes(nodeType)) {
          return false;
        }

        // Toggle the textAlignLast attribute
        const currentValue = node.attrs.textAlignLast;
        // If it's currently set to 'justify', remove it, otherwise set it to 'justify'
        const newValue = currentValue === 'justify' ? null : 'justify';

        // Update the attribute for the current node type
        return commands.updateAttributes(nodeType, { textAlignLast: newValue });
      },
    };
  },
  addGlobalAttributes() {
    return [
      {
        types: this.options.types,
        attributes: {
          textAlignLast: {
            default: null,
            parseHTML: (element) => {
              const styleValue = element.style.textAlignLast || null;
              return styleValue === 'justify' ? 'justify' : null;
            },
            renderHTML: (attributes) => {
              if (!attributes.textAlignLast) {
                return {};
              }
              return {
                style: `text-align-last: ${attributes.textAlignLast}`,
              };
            },
          },
        },
      },
    ];
  },
});
