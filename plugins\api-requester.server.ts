import { defineNuxtPlugin, useRequestEvent } from '#app';
import { createApiInstance } from '~/server/utils/apiInstance'; // Import the shared utility

export default defineNuxtPlugin(async (nuxtApp) => {
  // On the server-side (during SSR or in Nitro routes if this plugin were universal, which it isn't),
  // we can try to get the H3 event.
  // For a '.server.ts' plugin, `useRequestEvent()` should reliably provide the event.
  const event = typeof window === 'undefined' ? useRequestEvent() : undefined;

  // Create the API instance using the shared utility.
  // Pass the event if available, so it can handle cookies server-side.
  // If 'event' is undefined (e.g., purely client-side navigation after initial load),
  // the createApiInstance will create a basic Axios instance without server-side cookie handling,
  // which is fine as client-side requests will use browser cookies automatically.
  const apiRequester = createApiInstance(event);

  // Provide the apiRequester to the Nuxt app context
  return {
    provide: {
      apiRequester
    }
  };
});