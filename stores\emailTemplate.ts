/**
 * Email Template Store
 * 
 * Pinia store for managing email templates with CRUD operations,
 * template saving/loading, and state management
 */

import { defineStore } from 'pinia'
import { useApi } from '~/composables/useApi'
import type {
  EmailTemplate,
  EmailTemplateCategory,
  EmailTemplateStatus,
  // EmailTemplateVariable, // Unused
  EmailEditorConfig
} from '~/app/shared/types';
import { useNuxtApp } from '#app';

interface EmailTemplateState {
  // Templates
  templates: EmailTemplate[]
  currentTemplate: EmailTemplate | null
  
  // Editor state
  editorInstance: any | null
  editorConfig: EmailEditorConfig | null
  isEditorReady: boolean
  
  // UI state
  isLoading: boolean
  isSaving: boolean
  isPreviewMode: boolean
  selectedCategory: EmailTemplateCategory | 'all'
  searchQuery: string
  
  // Filters and pagination
  filters: {
    status: EmailTemplateStatus | 'all'
    category: EmailTemplateCategory | 'all'
    tags: string[]
  }
  pagination: {
    page: number
    limit: number
    total: number
  }
  
  // Error handling
  error: string | null
  validationErrors: Record<string, string[]>
}

export const useEmailTemplateStore = defineStore('emailTemplate', {
  state: (): EmailTemplateState => ({
    // Templates
    templates: [],
    currentTemplate: null,
    
    // Editor state
    editorInstance: null,
    editorConfig: null,
    isEditorReady: false,
    
    // UI state
    isLoading: false,
    isSaving: false,
    isPreviewMode: false,
    selectedCategory: 'all',
    searchQuery: '',
    
    // Filters and pagination
    filters: {
      status: 'all',
      category: 'all',
      tags: []
    },
    pagination: {
      page: 1,
      limit: 12,
      total: 0
    },
    
    // Error handling
    error: null,
    validationErrors: {}
  }),

  getters: {
    // Filtered templates
    filteredTemplates: (state) => {
      let filtered = [...state.templates]
      
      // Apply search filter
      if (state.searchQuery) {
        const query = state.searchQuery.toLowerCase()
        filtered = filtered.filter(template => 
          template.name.toLowerCase().includes(query) ||
          template.description?.toLowerCase().includes(query) ||
          template.subject.toLowerCase().includes(query)
        )
      }
      
      // Apply status filter
      if (state.filters.status !== 'all') {
        filtered = filtered.filter(template => template.status === state.filters.status)
      }
      
      // Apply category filter
      if (state.filters.category !== 'all') {
        filtered = filtered.filter(template => template.category === state.filters.category)
      }
      
      // Apply tags filter
      if (state.filters.tags.length > 0) {
        filtered = filtered.filter(template => 
          state.filters.tags.some(tag => template.metadata.tags.includes(tag))
        )
      }
      
      return filtered
    },

    // Paginated templates
    paginatedTemplates: (state) => {
      const filtered = (state as any).filteredTemplates
      const start = (state.pagination.page - 1) * state.pagination.limit
      const end = start + state.pagination.limit
      return filtered.slice(start, end)
    },

    // Template categories with counts
    categoriesWithCounts: (state) => {
      const counts: Record<string, number> = {}
      state.templates.forEach((template: EmailTemplate) => {
        counts[template.category] = (counts[template.category] || 0) + 1
      })
      return counts
    },

    // Template statistics
    templateStats: (state) => ({
      total: state.templates.length,
      active: state.templates.filter((t: EmailTemplate) => t.status === 'active').length,
      draft: state.templates.filter((t: EmailTemplate) => t.status === 'draft').length,
      archived: state.templates.filter((t: EmailTemplate) => t.status === 'archived').length
    }),

    // Current template validation
    isCurrentTemplateValid: (state) => {
      if (!state.currentTemplate) return false
      return (
        state.currentTemplate.name.trim() !== '' &&
        state.currentTemplate.subject.trim() !== '' &&
        state.currentTemplate.content.html.trim() !== ''
      )
    },

    // Editor ready state
    canSaveTemplate: (state) => {
      return state.isEditorReady && !state.isSaving && state.currentTemplate !== null
    }
  },

  actions: {
    // ========================================================================
    // TEMPLATE CRUD OPERATIONS
    // ========================================================================

    /**
     * Fetch all templates
     */
    async fetchTemplates() {
      try {
        this.isLoading = true
        this.error = null

        const { $api } = useNuxtApp();
        if (!$api) {
          throw new Error('API client not available');
        }

        // TODO: Replace with actual API call
        const { get } = useApi();
        const response = await get('/email-templates', {
          params: {
            page: this.pagination.page,
            limit: this.pagination.limit,
            status: this.filters.status !== 'all' ? this.filters.status : undefined,
            category: this.filters.category !== 'all' ? this.filters.category : undefined,
            search: this.searchQuery || undefined
          }
        })

        // Assuming response is { data: EmailTemplate[], total: number }
        const responseData = response.data as { data: EmailTemplate[], total: number };
        this.templates = responseData.data || []
        this.pagination.total = responseData.total || 0

      } catch (error) {
        this.error = 'Failed to fetch email templates'
        console.error('Error fetching templates:', error)
      } finally {
        this.isLoading = false
      }
    },

    /**
     * Fetch single template by ID
     */
    async fetchTemplate(id: string) {
      try {
        this.isLoading = true
        this.error = null

        const { $api } = useNuxtApp();
        // TODO: Replace with actual API call
        const { get } = useApi();
        const response = await get(`/email-templates/${id}`)

        this.currentTemplate = response.data as EmailTemplate
        return response.data

      } catch (error) {
        this.error = 'Failed to fetch email template'
        console.error('Error fetching template:', error)
        throw error
      } finally {
        this.isLoading = false
      }
    },

    /**
     * Create new template
     */
    async createTemplate(templateData: Partial<EmailTemplate>) {
      try {
        this.isSaving = true
        this.error = null
        this.validationErrors = {}

        const { $api } = useNuxtApp();
        if (!$api) {
          throw new Error('API client not available');
        }

        // TODO: Replace with actual API call
        const { post } = useApi();
        const response = await post('/email-templates', templateData)

        this.templates.unshift(response.data as EmailTemplate)
        this.currentTemplate = response.data as EmailTemplate

        return response.data

      } catch (error: any) {
        if (error.response?.status === 422) {
          this.validationErrors = error.response.data?.errors || {}
        } else {
          this.error = 'Failed to create email template'
        }
        console.error('Error creating template:', error)
        throw error
      } finally {
        this.isSaving = false
      }
    },

    /**
     * Update existing template
     */
    async updateTemplate(id: string, templateData: Partial<EmailTemplate>) {
      try {
        this.isSaving = true
        this.error = null
        this.validationErrors = {}
        
        const { $api } = useNuxtApp();
        if (!$api) {
          throw new Error('API client not available');
        }

        // TODO: Replace with actual API call
        const { put } = useApi();
        const response = await put(`/email-templates/${id}`, templateData)
        const updatedTemplate = response.data
        
        const index = this.templates.findIndex((t: EmailTemplate) => t.id === id)
        if (index !== -1) {
          this.templates[index] = updatedTemplate as EmailTemplate
        }
        
        if (this.currentTemplate?.id === id) {
          this.currentTemplate = updatedTemplate as EmailTemplate
        }
        
        return updatedTemplate
        
      } catch (error: any) {
        if (error.statusCode === 422) {
          this.validationErrors = error.data?.errors || {}
        } else {
          this.error = 'Failed to update email template'
        }
        console.error('Error updating template:', error)
        throw error
      } finally {
        this.isSaving = false
      }
    },

    /**
     * Delete template
     */
    async deleteTemplate(id: string) {
      try {
        this.isLoading = true
        this.error = null
        
        const { $api } = useNuxtApp();
        // TODO: Replace with actual API call
        const { delete: deleteApi } = useApi();
        await deleteApi(`/email-templates/${id}`)
        
        this.templates = this.templates.filter((t: EmailTemplate) => t.id !== id)
        
        if (this.currentTemplate?.id === id) {
          this.currentTemplate = null
        }
        
      } catch (error) {
        this.error = 'Failed to delete email template'
        console.error('Error deleting template:', error)
        throw error
      } finally {
        this.isLoading = false
      }
    },

    /**
     * Duplicate template
     */
    async duplicateTemplate(id: string) {
      try {
        const original = this.templates.find((t: EmailTemplate) => t.id === id)
        if (!original) throw new Error('Template not found')

        const duplicateData = {
          ...original,
          id: undefined,
          name: `${original.name} (Copy)`,
          status: 'draft' as EmailTemplateStatus,
          createdAt: undefined,
          updatedAt: undefined
        }

        return await this.createTemplate(duplicateData)

      } catch (error) {
        console.error('Error duplicating template:', error)
        throw error
      }
    },

    // ========================================================================
    // EDITOR OPERATIONS
    // ========================================================================

    /**
     * Initialize GrapesJS editor
     */
    initializeEditor(config: EmailEditorConfig) {
      this.editorConfig = config
      this.isEditorReady = false
    },

    /**
     * Set editor instance
     */
    setEditorInstance(editor: any) {
      this.editorInstance = editor
      this.isEditorReady = true
    },

    /**
     * Save current editor content
     */
    async saveEditorContent() {
      if (!this.editorInstance || !this.currentTemplate) return

      try {
        this.isSaving = true

        const html = this.editorInstance.getHtml()
        const css = this.editorInstance.getCss()
        const components = this.editorInstance.getComponents()

        const updatedTemplate = {
          ...this.currentTemplate,
          content: {
            html,
            css,
            components: components.toJSON(),
            assets: this.currentTemplate.content.assets
          },
          updatedAt: new Date().toISOString()
        }

        await this.updateTemplate(this.currentTemplate.id, updatedTemplate)

      } catch (error) {
        console.error('Error saving editor content:', error)
        throw error
      } finally {
        this.isSaving = false
      }
    },

    /**
     * Load template into editor
     */
    loadTemplateIntoEditor(template: EmailTemplate) {
      if (!this.editorInstance) return

      try {
        this.currentTemplate = template

        // Load components and styles
        this.editorInstance.setComponents(template.content.components || [])
        this.editorInstance.setStyle(template.content.css || '')

      } catch (error) {
        console.error('Error loading template into editor:', error)
        throw error
      }
    },

    /**
     * Toggle preview mode
     */
    togglePreviewMode() {
      this.isPreviewMode = !this.isPreviewMode

      if (this.editorInstance) {
        const command = this.isPreviewMode ? 'preview' : 'preview-stop'
        this.editorInstance.runCommand(command)
      }
    },

    /**
     * Export template
     */
    exportTemplate(format: 'html' | 'json' = 'html') {
      if (!this.editorInstance || !this.currentTemplate) return null

      if (format === 'html') {
        const html = this.editorInstance.getHtml()
        const css = this.editorInstance.getCss()
        return `<style>${css}</style>${html}`
      } else {
        return {
          html: this.editorInstance.getHtml(),
          css: this.editorInstance.getCss(),
          components: this.editorInstance.getComponents().toJSON()
        }
      }
    },

    // ========================================================================
    // UI STATE MANAGEMENT
    // ========================================================================

    /**
     * Set search query
     */
    setSearchQuery(query: string) {
      this.searchQuery = query
      this.pagination.page = 1 // Reset to first page
    },

    /**
     * Set filters
     */
    setFilters(filters: Partial<typeof this.filters>) {
      this.filters = { ...this.filters, ...filters }
      this.pagination.page = 1 // Reset to first page
    },

    /**
     * Set pagination
     */
    setPagination(pagination: Partial<typeof this.pagination>) {
      this.pagination = { ...this.pagination, ...pagination }
    },

    /**
     * Clear current template
     */
    clearCurrentTemplate() {
      this.currentTemplate = null
      this.isPreviewMode = false
    },

    /**
     * Clear errors
     */
    clearErrors() {
      this.error = null
      this.validationErrors = {}
    },

    /**
     * Reset store state
     */
    resetState() {
      this.templates = []
      this.currentTemplate = null
      this.editorInstance = null
      this.editorConfig = null
      this.isEditorReady = false
      this.isLoading = false
      this.isSaving = false
      this.isPreviewMode = false
      this.selectedCategory = 'all'
      this.searchQuery = ''
      this.filters = {
        status: 'all',
        category: 'all',
        tags: []
      }
      this.pagination = {
        page: 1,
        limit: 12,
        total: 0
      }
      this.error = null
      this.validationErrors = {}
    }
  }
})
