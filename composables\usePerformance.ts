/**
 * Performance Composable
 * 
 * Simplified performance utilities for components and layouts.
 * Provides convenient methods for performance timing and monitoring.
 */

import { ref, computed, onMounted, onUnmounted, readonly } from 'vue'
import { usePerformanceMonitoring } from '~/app/shared/composables/monitoring/usePerformanceMonitoring'

// ============================================================================
// TYPES
// ============================================================================

export interface PerformanceTimings {
  [key: string]: {
    start: number
    end?: number
    duration?: number
  }
}

export interface PerformanceMetrics {
  componentMountTime: number
  renderTime: number
  interactionTime: number
  memoryUsage?: number
}

// ============================================================================
// COMPOSABLE
// ============================================================================

export function usePerformance() {
  // ============================================================================
  // STATE
  // ============================================================================
  
  const timings = ref<PerformanceTimings>({})
  const isMonitoring = ref(false)
  
  // Use the comprehensive performance monitoring system
  const performanceMonitoring = usePerformanceMonitoring({
    enabled: true,
    enableUserTiming: true,
    enableMemoryMonitoring: true
  })
  
  // ============================================================================
  // COMPUTED
  // ============================================================================
  
  const activeTimings = computed(() => {
    return Object.entries(timings.value).filter(([_, timing]) => !timing.end)
  })
  
  const completedTimings = computed(() => {
    return Object.entries(timings.value)
      .filter(([_, timing]) => timing.end && timing.duration)
      .map(([name, timing]) => ({
        name,
        duration: timing.duration!,
        start: timing.start,
        end: timing.end!
      }))
  })
  
  const averageRenderTime = computed(() => {
    const renderTimings = completedTimings.value.filter(t => t.name.includes('render'))
    if (renderTimings.length === 0) return 0
    
    const total = renderTimings.reduce((sum, timing) => sum + timing.duration, 0)
    return Math.round(total / renderTimings.length)
  })
  
  // ============================================================================
  // PERFORMANCE TIMING METHODS
  // ============================================================================
  
  const markStart = (name: string): void => {
    const timestamp = performance.now()
    
    timings.value[name] = {
      start: timestamp
    }
    
    // Use browser Performance API if available
    if (typeof performance !== 'undefined' && performance.mark) {
      performance.mark(`${name}-start`)
    }
    
    // Record with comprehensive monitoring system
    performanceMonitoring.startTiming(name)
  }
  
  const markEnd = (name: string): number => {
    const timestamp = performance.now()
    const timing = timings.value[name]
    
    if (!timing) {
      console.warn(`Performance timing '${name}' was not started`)
      return 0
    }
    
    const duration = timestamp - timing.start
    
    // Update timing record
    timings.value[name] = {
      ...timing,
      end: timestamp,
      duration
    }
    
    // Use browser Performance API if available
    if (typeof performance !== 'undefined' && performance.mark && performance.measure) {
      performance.mark(`${name}-end`)
      performance.measure(name, `${name}-start`, `${name}-end`)
    }
    
    // Record with comprehensive monitoring system
    performanceMonitoring.endTiming(name)
    
    return duration
  }
  
  const measure = (name: string, startMark?: string, endMark?: string): number => {
    if (typeof performance === 'undefined' || !performance.measure) {
      return 0
    }
    
    try {
      const measureName = `measure-${name}`
      performance.measure(measureName, startMark, endMark)
      
      const entries = performance.getEntriesByName(measureName, 'measure')
      const entry = entries[entries.length - 1]
      
      return entry ? entry.duration : 0
    } catch (error) {
      console.warn('Performance measurement failed:', error)
      return 0
    }
  }
  
  // ============================================================================
  // COMPONENT PERFORMANCE TRACKING
  // ============================================================================
  
  const trackComponentMount = (componentName: string) => {
    markStart(`component-mount-${componentName}`)
    
    onMounted(() => {
      const duration = markEnd(`component-mount-${componentName}`)
      
      // Record custom metric
      performanceMonitoring.recordMetric(`component-mount-${componentName}`, duration)
    })
  }
  
  const trackRender = (renderName: string) => {
    markStart(`render-${renderName}`)
    
    return () => {
      const duration = markEnd(`render-${renderName}`)
      performanceMonitoring.recordMetric(`render-${renderName}`, duration)
      return duration
    }
  }
  
  const trackInteraction = (interactionName: string) => {
    markStart(`interaction-${interactionName}`)
    
    return () => {
      const duration = markEnd(`interaction-${interactionName}`)
      performanceMonitoring.recordMetric(`interaction-${interactionName}`, duration)
      return duration
    }
  }
  
  // ============================================================================
  // MEMORY MONITORING
  // ============================================================================
  
  const getMemoryUsage = (): number => {
    if (typeof performance !== 'undefined' && 'memory' in performance) {
      const memory = (performance as any).memory
      return Math.round((memory.usedJSHeapSize / memory.totalJSHeapSize) * 100)
    }
    return 0
  }
  
  const trackMemoryUsage = (label: string) => {
    const usage = getMemoryUsage()
    performanceMonitoring.recordMetric(`memory-${label}`, usage)
    return usage
  }
  
  // ============================================================================
  // UTILITY METHODS
  // ============================================================================
  
  const clearTimings = () => {
    timings.value = {}
    
    // Clear performance marks and measures
    if (typeof performance !== 'undefined' && performance.clearMarks) {
      performance.clearMarks()
      performance.clearMeasures()
    }
  }
  
  const getTiming = (name: string) => {
    return timings.value[name]
  }
  
  const getAllTimings = () => {
    return { ...timings.value }
  }
  
  const getPerformanceReport = () => {
    return {
      timings: completedTimings.value,
      averageRenderTime: averageRenderTime.value,
      memoryUsage: getMemoryUsage(),
      activeTimings: activeTimings.value.length,
      totalTimings: Object.keys(timings.value).length
    }
  }
  
  // ============================================================================
  // LIFECYCLE
  // ============================================================================
  
  const startMonitoring = () => {
    if (isMonitoring.value) return
    
    isMonitoring.value = true
    performanceMonitoring.startMonitoring()
  }
  
  const stopMonitoring = () => {
    if (!isMonitoring.value) return
    
    isMonitoring.value = false
    performanceMonitoring.stopMonitoring()
  }
  
  // Auto-cleanup on unmount
  onUnmounted(() => {
    stopMonitoring()
    clearTimings()
  })
  
  // ============================================================================
  // RETURN INTERFACE
  // ============================================================================
  
  return {
    // State
    timings: readonly(timings),
    isMonitoring: readonly(isMonitoring),
    
    // Computed
    activeTimings,
    completedTimings,
    averageRenderTime,
    
    // Core timing methods
    markStart,
    markEnd,
    measure,
    
    // Component tracking
    trackComponentMount,
    trackRender,
    trackInteraction,
    
    // Memory monitoring
    getMemoryUsage,
    trackMemoryUsage,
    
    // Utility methods
    clearTimings,
    getTiming,
    getAllTimings,
    getPerformanceReport,
    
    // Lifecycle
    startMonitoring,
    stopMonitoring,
    
    // Access to comprehensive monitoring
    monitoring: performanceMonitoring
  }
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Performance decorator for methods
 */
export function withPerformanceTracking<T extends (...args: any[]) => any>(
  fn: T,
  name: string
): T {
  return ((...args: any[]) => {
    const { markStart, markEnd } = usePerformance()
    
    markStart(name)
    const result = fn(...args)
    
    if (result instanceof Promise) {
      return result.finally(() => markEnd(name))
    } else {
      markEnd(name)
      return result
    }
  }) as T
}

/**
 * Performance timing for async operations
 */
export async function withAsyncPerformanceTracking<T>(
  operation: () => Promise<T>,
  name: string
): Promise<T> {
  const { markStart, markEnd } = usePerformance()
  
  markStart(name)
  try {
    const result = await operation()
    markEnd(name)
    return result
  } catch (error) {
    markEnd(name)
    throw error
  }
}
