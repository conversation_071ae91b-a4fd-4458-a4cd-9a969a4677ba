# useGlobalActions Composable

A powerful Vue 3 composable for managing global add and remove functions and properties across your Legal SaaS application. This composable provides a consistent, type-safe way to handle CRUD operations with built-in validation, callbacks, and state management.

## Features

- **Type-Safe**: Full TypeScript support with generic types
- **Global State**: Shared state across components using a unique key
- **Validation**: Built-in item validation with custom validators
- **Callbacks**: Lifecycle hooks for add, remove, update, and clear operations
- **Bulk Operations**: Support for batch add, remove, and update operations
- **Utility Methods**: Find, exists, toggle, and other helper methods
- **Error Handling**: Comprehensive error handling and reporting
- **Loading States**: Built-in loading state management
- **Configurable**: Flexible configuration options

## Basic Usage

```typescript
import { useGlobalActions } from '~/app/shared/composables'
import type { GlobalActionItem } from '~/app/shared/composables'

interface MyItem extends GlobalActionItem {
  id: string
  name: string
  description?: string
}

export function useMyItems() {
  const { items, count, isEmpty, actions } = useGlobalActions<MyItem>({
    key: 'my-items', // Unique key for global state
    initialItems: [],
    maxItems: 100,
    allowDuplicates: false,
    validateItem: (item) => {
      if (!item.name) return 'Name is required'
      return true
    }
  })

  return {
    items,
    count,
    isEmpty,
    actions
  }
}
```

## Configuration Options

```typescript
interface GlobalActionConfig<T> {
  key: string                                    // Unique identifier for global state
  initialItems?: T[]                            // Initial items to populate
  maxItems?: number                             // Maximum number of items (default: 1000)
  allowDuplicates?: boolean                     // Allow duplicate items (default: false)
  validateItem?: (item: T) => boolean | string // Item validation function
  onAdd?: (item: T, items: T[]) => void | Promise<void>     // Called after item is added
  onRemove?: (item: T, items: T[]) => void | Promise<void>  // Called after item is removed
  onUpdate?: (item: T, items: T[]) => void | Promise<void>  // Called after item is updated
  onClear?: (items: T[]) => void | Promise<void>            // Called after items are cleared
}
```

## Available Methods

### Core Methods

```typescript
// Add single item or array of items
await actions.add(item)
await actions.add([item1, item2, item3])

// Remove by ID or predicate function
await actions.remove('item-id')
await actions.remove(item => item.name === 'test')

// Update item by ID
await actions.update('item-id', { name: 'New Name' })

// Clear all items
await actions.clear()

// Toggle item (add if not exists, remove if exists)
await actions.toggle(item)
```

### Utility Methods

```typescript
// Find item by ID
const item = actions.find('item-id')

// Get item index by ID
const index = actions.findIndex('item-id')

// Check if item exists
const exists = actions.exists('item-id')
```

### Bulk Operations

```typescript
// Add multiple items
await actions.addMany([item1, item2, item3])

// Remove multiple items by ID
await actions.removeMany(['id1', 'id2', 'id3'])

// Update multiple items
await actions.updateMany([
  { id: 'id1', data: { name: 'New Name 1' } },
  { id: 'id2', data: { name: 'New Name 2' } }
])
```

### State Management

```typescript
// Reset all state
actions.reset()

// Set items directly
actions.setItems([item1, item2])

// Set loading state
actions.setLoading(true)

// Set error state
actions.setError('Something went wrong')
```

## Reactive State

```typescript
const {
  items,        // ComputedRef<T[]> - All items
  count,        // ComputedRef<number> - Number of items
  isEmpty,      // ComputedRef<boolean> - Whether items array is empty
  isLoading,    // Ref<boolean> - Loading state
  error,        // Ref<string | null> - Error message
  lastAction    // ComputedRef<LastAction> - Information about last action
} = useGlobalActions(config)
```

## Global Utility Functions

```typescript
import { 
  getGlobalActions,
  hasGlobalActions,
  removeGlobalActions,
  clearAllGlobalActions,
  getGlobalActionsKeys
} from '~/app/shared/composables'

// Get global actions instance by key
const state = getGlobalActions('my-items')

// Check if instance exists
const exists = hasGlobalActions('my-items')

// Remove instance
removeGlobalActions('my-items')

// Clear all instances
clearAllGlobalActions()

// Get all keys
const keys = getGlobalActionsKeys()
```

## Real-World Examples

### Tenant Management

```typescript
interface TenantItem extends GlobalActionItem {
  id: string
  name: string
  slug: string
  plan: 'basic' | 'pro' | 'enterprise'
  status: 'active' | 'inactive'
}

export function useTenantActions() {
  const { items, actions } = useGlobalActions<TenantItem>({
    key: 'tenants',
    validateItem: (tenant) => {
      if (!tenant.name) return 'Name is required'
      if (!tenant.slug) return 'Slug is required'
      return true
    },
    onAdd: async (tenant) => {
      // API call to create tenant
      await $fetch('/api/tenants', {
        method: 'POST',
        body: tenant
      })
    }
  })

  const addTenant = async (data: Omit<TenantItem, 'id'>) => {
    const tenant: TenantItem = {
      ...data,
      id: `tenant-${Date.now()}`
    }
    return await actions.add(tenant)
  }

  return { tenants: items, actions, addTenant }
}
```

### Document Management

```typescript
interface DocumentItem extends GlobalActionItem {
  id: string
  name: string
  type: 'contract' | 'template' | 'form'
  size: number
  tags: string[]
}

export function useDocumentActions() {
  const { items, actions } = useGlobalActions<DocumentItem>({
    key: 'documents',
    maxItems: 5000,
    validateItem: (doc) => {
      if (doc.size > 50 * 1024 * 1024) {
        return 'File size cannot exceed 50MB'
      }
      return true
    }
  })

  const addTag = async (docId: string, tag: string) => {
    const doc = actions.find(docId)
    if (!doc) return false
    
    const updatedTags = [...new Set([...doc.tags, tag])]
    return await actions.update(docId, { tags: updatedTags })
  }

  return { documents: items, actions, addTag }
}
```

## Best Practices

1. **Use Unique Keys**: Always use unique keys for different data types
2. **Validate Items**: Implement proper validation to prevent invalid data
3. **Handle Errors**: Always check return values and handle errors appropriately
4. **Use Callbacks**: Leverage lifecycle callbacks for side effects like API calls
5. **Type Safety**: Define proper interfaces extending `GlobalActionItem`
6. **Cleanup**: Remove global instances when no longer needed to prevent memory leaks

## Integration with Stores

The composable works well alongside Pinia stores:

```typescript
// In your store
export const useMyStore = defineStore('my-store', () => {
  const { items, actions } = useGlobalActions<MyItem>({
    key: 'store-items',
    onAdd: async (item) => {
      // Sync with backend
      await api.create(item)
    }
  })

  return { items, actions }
})
```

This composable provides a robust foundation for managing global state with add/remove functionality throughout your Legal SaaS application.
