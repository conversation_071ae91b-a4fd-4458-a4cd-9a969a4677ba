<template>
  <div
    class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700"
  >
    <div class="px-4 sm:px-6 lg:px-8 py-6">
      <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <!-- Filters Section -->
        <div v-if="showFilters" class="lg:w-auto">
          <GlobalPageHeaderFilters
            :filter-configs="filterConfigs"
            :advanced-filter-configs="advancedFilterConfigs"
            :initial-filters="initialFilters"
            :show-advanced-toggle="showAdvancedFilters"
            :compact-mode="compactFilters"
            @filter-change="$emit('filter-change', $event)"
            @filters-change="$emit('filters-change', $event)"
            @clear-filters="$emit('clear-filters')"
            @advanced-filters-toggle="$emit('advanced-filters-toggle', $event)"
          >
            <!-- Pass through custom filter slots -->
            <template v-for="(_, name) in $slots" :key="name" #[name]="slotData">
              <slot :name="name" v-bind="slotData" />
            </template>
          </GlobalPageHeaderFilters>
        </div>

        <!-- Title and Stats -->
        <div class="flex-1">
          <div class="flex items-center gap-3 mb-2">
            <!-- Logo Image -->
            <div
              v-if="!!logoImage"
              class="w-20 h-20 rounded-xl overflow-hidden shadow-lg"
            >
              <img
                :src="logoImage"
                :alt="`${title} logo`"
                class="w-full h-full object-cover"
                @error="handleImageError"
              />
            </div>
            <!-- Icon -->
            <div v-else-if="icon" class="p-4 bg-brandPrimary/10 rounded-lg">
              <Icon :name="icon" size="calc(var(--spacing) * 7)" class=" text-brandPrimary" />
            </div>
            <!-- Title and Description -->
            <div>
              <div class="flex align-center mb-2">
        
                <h1 v-if="title && props.showTitle" class="text-2xl font-bold text-gray-900 dark:text-white" >
                  {{ title }}
                </h1>
                <div v-if="!!itemStats" class="flex flex-wrap gap-4 ms-2">
                  <div
                    class="flex items-center gap-2 px-3 py-1 rounded-full"
                    :class="getStatClasses(itemStats?.color)"
                  >
                    <div
                      class="w-1.5 h-1.5 rounded-full"
                      :class="getStatDotClasses(itemStats?.color)"
                    ></div>
                    <span
                      class="text-xs tracking-tight font-small"
                      :class="getStatTextClasses(itemStats?.color)"
                    >
                      {{ itemStats?.label }}
                    </span>
                  </div>
                </div>
              </div>

              <p v-if="description" class="text-sm text-gray-600 dark:text-gray-400">
                {{ description }}
              </p>

              <div
                v-if="getComputedLabels && getComputedLabels.length > 0"
                class="flex text-sm text-gray-600 dark:text-gray-400 mt-2 space-x-1.5"
              >
                <template v-for="(label, index) in getComputedLabels" :key="index">
                  <UiTooltip :content="label.label" :index="label.key">
                    <template #trigger>
                      <div class="flex items-center gap-1">
                        <Icon :name="label.icon" size="calc(var(--spacing) * 4)" />
                        <span>{{ label.value }}</span>
                      </div>
                    </template>
                  </UiTooltip>
                </template>
              </div>

              <div class="flex items-center space-x-2 mt-2" v-if="getComputedTags && getComputedTags.length > 0">
                <div class="flex items-center gap-1" v-for="tag in getComputedTags" :key="tag.key">
                  <Icon
                    :name="tag.icon"
                    size="calc(var(--spacing) * 4.5)"
                    :class="tag.class"
                  />
                  <span class="font-medium text-gray-900 dark:text-white" 
                    >{{ tag.label }}</span
                  >
                </div>
                
              </div>
            </div>
          </div>

          <!-- Quick Stats -->
          <div
            v-if="getComputedStats && getComputedStats.length > 0"
            class="flex flex-wrap gap-4 mt-4"
          >
            <div
              v-for="stat in getComputedStats"
              :key="stat.key"
              class="flex items-center gap-2 px-3 py-1.5 rounded-full"
              :class="getStatClasses(stat.color)"
            >
              <div
                class="w-2 h-2 rounded-full"
                :class="getStatDotClasses(stat.color)"
              ></div>
              <span class="text-sm font-medium" :class="getStatTextClasses(stat.color)">
                {{ stat.value }} {{ stat.label }}
              </span>
            </div>
          </div>
          
        </div>
        <div class="flex flex-col items-center space-y-8">
          <!-- Action Buttons -->
          <div class="flex items-center gap-3">
            <!-- Custom Actions Slot -->
            <slot name="actions" />

            <!-- Header Actions Prop -->
            <template v-if="getComputedActions && getComputedActions.length > 0">
              <UiButton
                v-for="(action, index) in getComputedActions"
                :key="index"
                @click="action.click"
                :variant="action?.variant || 'contained'"
                :color="action?.color || 'primary'"
                :size="action?.size || 'sm'"
                :disabled="loading || isLoading || getProp(action?.disabled)"
                :loading="getProp(action?.loading)"
              >
                <Icon v-if="action.icon" :name="action.icon" class="h-4 w-4 mr-2" />
                {{ action.label }}
              </UiButton>
            </template>

            <!-- Default Actions (only if headerActions are not provided or empty) -->
            <template
              v-if="
                showDefaultActions &&
                (!getComputedActions || getComputedActions.length === 0)
              "
            >
              <!-- Export Button -->
              <UiButton
                v-if="showExport"
                @click="$emit('export')"
                variant="outline"
                size="sm"
                :disabled="loading || isLoading"
              >
                <Icon name="material-symbols:download" class="h-4 w-4 mr-2" />
                Export
              </UiButton>

              <!-- Create Button -->
              <UiButton
                v-if="showCreate"
                @click="$emit('create')"
                variant="primary"
                size="sm"
                :disabled="loading || isLoading"
              >
                <Icon :name="createIcon" class="h-4 w-4 mr-2" />
                {{ createLabel }}
              </UiButton>
            </template>
          </div>
          <!-- Real-time Status -->
          <div v-if="showRealTimeStatus" class="flex items-center gap-3">
            <RealTimeStatusIndicator
              :auto-refresh="autoRefreshEnabled"
              :refresh-interval="refreshInterval"
              :is-refreshing="loading || isLoading"
              @refresh="$emit('refresh')"
              @auto-refresh-changed="$emit('auto-refresh-changed', $event)"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type UiTooltip from "components/ui/UiTooltip.vue";
import RealTimeStatusIndicator from "~/components/platform/RealTimeStatusIndicator";
import GlobalPageHeaderFilters from "./GlobalPageHeaderFilters.vue";
const app = useNuxtApp();
interface Action {
  label: string;
  icon: string;
  color?: 'primary' | 'secondary' | 'success' | 'warning' | 'danger' | 'info' | 'gray' | 'blue';
  variant?: 'contained' | 'outline' | 'ghost' | 'flat' | 'gradient' | 'primary' | 'danger';
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  disabled?: boolean | (() => boolean);
  loading?: boolean | (() => boolean);
  click: () => void;
}
interface Stat {
  key: string;
  label: string;
  value: string | number;
  color: "green" | "blue" | "purple" | "yellow" | "red" | "gray";
}
interface ItemStat {
  key: string;
  label: string;
  color: "green" | "blue" | "purple" | "yellow" | "red" | "gray";
}

interface Labels {
  key: string;
  label: string;
  icon: string;
  value: string | number;
}

interface Tags {
  key: string;
  label: string;
  class: string;
  icon: string;
}

interface FilterOption {
  label: string;
  value: string | number;
}

interface FilterConfig {
  key: string;
  options: FilterOption[];
  placeholder: string;
  class?: string;
}

interface AdvancedFilterConfig {
  key: string;
  type: 'dateRange' | 'numberRange' | 'select' | 'custom';
  label: string;
  placeholder?: string;
  minPlaceholder?: string;
  maxPlaceholder?: string;
  options?: FilterOption[];
}

interface Props {
  title: string;
  description?: string;
  logoImage?: string;
  icon?: string;
  itemStats?: ItemStat | ((...args: any[]) => ItemStat);
  stats?: Stat[] | ((...args: any[]) => Stat[]);
  labels?: Labels[] | ((...args: any[]) => Labels[]);
  tags?: Tags[] | ((...args: any[]) => Tags[]);
  loading?: boolean;
  showDefaultActions?: boolean;
  showExport?: boolean;
  showCreate?: boolean;
  createLabel?: string;
  createIcon?: string;
  actions?: Action[] | ((...args: any[]) => Action[]);
  showTitle?: boolean;
  // Real-time status props
  showRealTimeStatus?: boolean;
  autoRefreshEnabled?: boolean;
  refreshInterval?: number;
  isLoading?: boolean;
  // Filter props
  showFilters?: boolean;
  filterConfigs?: FilterConfig[];
  advancedFilterConfigs?: AdvancedFilterConfig[];
  initialFilters?: Record<string, any>;
  showAdvancedFilters?: boolean;
  compactFilters?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  description: "",
  logoImage: "",
  icon: "",
  stats: () => [],
  labels: () => [],
  tags: () => [],
  loading: false,
  showDefaultActions: true,
  showExport: true,
  showCreate: true,
  createLabel: "Create",
  createIcon: "material-symbols:add",
  actions: () => [],
  showTitle: true,
  // Real-time status defaults
  showRealTimeStatus: false,
  autoRefreshEnabled: false,
  refreshInterval: 30000,
  isLoading: false,
  // Filter defaults
  showFilters: false,
  filterConfigs: () => [],
  advancedFilterConfigs: () => [],
  initialFilters: () => ({}),
  showAdvancedFilters: true,
  compactFilters: false,
});


const getComputedStats = computed(() => {
  if (typeof props.stats === "function") {
    return props.stats(app);
  }
  return props.stats;
});

const getComputedLabels = computed(() => {
  if (typeof props.labels === "function") {
    return props.labels(app);
  }
  return props.labels;
});

const getComputedTags = computed(() => {
  if (typeof props.tags === "function") {
    return props.tags(app);
  }
  return props.tags;
});

const getComputedActions = computed(() => {
  if (typeof props.actions === "function") {
    return props.actions(app);
  }
  return props.actions;
});

console.log("attrs", props.showDefaultActions);

interface Emits {
  (e: "export"): void;
  (e: "create"): void;
  (e: "refresh"): void;
  (e: "auto-refresh-changed", value: boolean): void;
  (e: "filter-change", data: { key: string; value: any }): void;
  (e: "filters-change", filters: Record<string, any>): void;
  (e: "clear-filters"): void;
  (e: "advanced-filters-toggle", show: boolean): void;
}

defineEmits<Emits>();

// Color mapping for stats
const getStatClasses = (color: string) => {
  const colorMap = {
    green:
      "bg-brandSuccess-50 dark:bg-brandSuccess-900/20 border border-brandSuccess-200 dark:border-brandSuccess-700",
    blue: "bg-blue-50 dark:bg-blue-900/20",
    purple: "bg-purple-50 dark:bg-purple-900/20",
    yellow: "bg-yellow-50 dark:bg-yellow-900/20",
    red: "bg-red-50 dark:bg-red-900/20",
    gray: "bg-gray-50 dark:bg-gray-900/20",
  };
  return colorMap[color as keyof typeof colorMap] || colorMap.gray;
};

const getStatDotClasses = (color: string) => {
  const colorMap = {
    green: "bg-brandSuccess-500",
    blue: "bg-blue-500",
    purple: "bg-purple-500",
    yellow: "bg-yellow-500",
    red: "bg-red-500",
    gray: "bg-gray-500",
  };
  return colorMap[color as keyof typeof colorMap] || colorMap.gray;
};

const getStatTextClasses = (color: string) => {
  const colorMap = {
    green: "text-brandSuccess-700 dark:text-brandSuccess-300",
    blue: "text-blue-700 dark:text-blue-300",
    purple: "text-purple-700 dark:text-purple-300",
    yellow: "text-yellow-700 dark:text-yellow-300",
    red: "text-red-700 dark:text-red-300",
    gray: "text-gray-700 dark:text-gray-300",
  };
  return colorMap[color as keyof typeof colorMap] || colorMap.gray;
};

const handleImageError = (event: Event) => {
  const imgElement = event.target as HTMLImageElement;
  imgElement.src = `https://ui-avatars.com/api/?name=${props.title}&background=6366f1&color=fff`;
};

const getProp = (prop: any) => {
  if (typeof prop === "function") {
    return prop(app);
  }
  return prop;
};
</script>
