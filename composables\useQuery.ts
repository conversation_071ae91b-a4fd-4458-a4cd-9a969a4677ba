// composables/useQuery.ts

import { useDebounceFn } from '@vueuse/core'
import { useRoute, useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { reactive, toRaw, watch } from 'vue'

export type SortItem = { key: string; direction: 'asc' | 'desc' }
export type QueryState = Record<string, any>
export type Presets = Record<string, Partial<QueryState>>

// Define a type for the options for better reusability and clarity.
type UseQueryOptions<T extends QueryState> = {
  debounceDelay?: number
  presets?: Presets
  translateKeys?: Record<string, string>
  history?: 'push' | 'replace'
  onQuerySynced?: (args: { query: Record<string, any>; state: T }) => void
}

export function useQuery<T extends QueryState>(
  defaults: T,
  options?: UseQueryOptions<T>
) {
  const route = useRoute()
  const router = useRouter()
  const { t } = useI18n()

  const state = reactive({ ...defaults })


  const parseSort = (str?: string): SortItem[] =>
    str
      ?.split(',')
      .map((s) => {
        const [key, dir] = s.split(':')
        return { key, direction: (dir || 'asc') as 'asc' | 'desc' }
      }) || []

  const stringifySort = (arr: SortItem[]): string =>
    arr.map((s) => `${s.key}:${s.direction}`).join(',')

  const parseArray = (val: any): string[] =>
    Array.isArray(val)
      ? val
      : typeof val === 'string' && val
      ? val.split(',')
      : []

  const encodeArray = (val: string[]) =>
    Array.isArray(val) ? val.join(',') : ''

  const translate = (val: string): string =>
    options?.translateKeys?.[val] ? t(options.translateKeys[val]) : val

  const syncFromRoute = () => {
    for (const key in defaults) {
      const rawVal = route.query[key]
      const defaultVal = defaults[key]

      if (rawVal === undefined || rawVal === null) {
        ;(state as any)[key] = defaultVal
        continue
      }

      if (key === 'sort' && typeof rawVal === 'string') {
        state.sort = parseSort(rawVal)
      } else if (Array.isArray(defaultVal)) {
        ;(state as any)[key] = parseArray(rawVal)
      } else if (typeof defaultVal === 'boolean') {
        ;(state as any)[key] = rawVal === 'true'
      } else if (typeof defaultVal === 'number') {
        const num = Number(rawVal)
        ;(state as any)[key] = isNaN(num) ? defaultVal : num
      } else {
        ;(state as any)[key] = rawVal ?? defaultVal
      }
    }
  }

  const syncToRoute = (syncOptions?: Pick<UseQueryOptions<T>, 'history'>) => {
    const query: Record<string, string> = {}
    for (const key in state) {
      const val = toRaw(state[key])
      const defaultVal = toRaw(defaults[key])

      // Skip empty, null, or default values
      if (
        val == null ||
        val === '' ||
        (Array.isArray(val) && val.length === 0)
      )
        continue
      if (
        key in defaults &&
        JSON.stringify(val) === JSON.stringify(defaultVal)
      )
        continue

      if (key === 'sort' && Array.isArray(val)) {
        query.sort = stringifySort(val)
      } else if (Array.isArray(val)) {
        query[key] = encodeArray(val)
      } else {
        query[key] = String(val)
      }
    }

    // Avoid unnecessary navigation
    if (JSON.stringify(route.query) !== JSON.stringify(query)) {
      if (
        (syncOptions?.history ?? options?.history) === 'push'
      ) {
        router.push({ query })
      } else {
        router.replace({ query })
      }
    } else {
      // If the query is unchanged, the watcher won't fire, so we invoke the callback manually.
      options?.onQuerySynced?.({ query, state: toRaw(state) as T })
    }
  }

  const setQuery = (
    updates: Partial<T>,
    options?: { resetPage?: boolean; history?: 'push' | 'replace' }
  ) => {
    Object.assign(state, updates)
    if (options?.resetPage && 'page' in defaults) {
      ;(state as any).page = defaults.page
    }
    syncToRoute(options?.history ? { history: options.history } : undefined)
  }

  const setQueryDebounced = useDebounceFn(setQuery, options?.debounceDelay ?? 500)

  watch(
    () => route.query,
    (query) => {
      syncFromRoute()
      options?.onQuerySynced?.({ query, state: toRaw(state) as T })
    },
    { immediate: true, deep: true }
  )

  // Utilities
  const resetQuery = (keys?: (keyof T)[]) => {
    if (Array.isArray(keys)) {
      keys.forEach((key) => ((state as any)[key] = defaults[key]))
    } else {
      for (const key in defaults) (state as any)[key] = defaults[key]
    }
    syncToRoute({ history: 'replace' })
  }

  const toggleSort = (column: string) => {
    const existing = state.sort.find((s: SortItem) => s.key === column)
    if (existing) {
      existing.direction = existing.direction === 'asc' ? 'desc' : 'asc'
    } else {
      state.sort.push({ key: column, direction: 'asc' })
    }
    setQuery({ sort: state.sort } as unknown as Partial<T>, { resetPage: true })
  }

  const removeSort = (column: string) => {
    state.sort = state.sort.filter((s: SortItem) => s.key !== column)
    setQuery({ sort: state.sort } as unknown as Partial<T>, { resetPage: true })
  }

  const setFilter = (key: keyof T, values: string[]) => {
    setQuery({ [key]: values } as Partial<T>, { resetPage: true })
  }

  const addFilterValue = (key: keyof T, value: string) => {
    if (!(state as any)[key].includes(value)) {
      const newValues = [...(state as any)[key], value]
      setQuery({ [key]: newValues } as Partial<T>, { resetPage: true })
    }
  }

  const removeFilterValue = (key: keyof T, value: string) => {
    const newValues = (state as any)[key].filter((v: string) => v !== value)
    setQuery({ [key]: newValues } as Partial<T>, { resetPage: true })
  }

  const applyPreset = (name: string) => {
    const preset = options?.presets?.[name]
    if (preset) {
      setQuery(preset as Partial<T>, { resetPage: true })
    }
  }

  return {
    state,
    setQuery,
    setQueryDebounced,
    resetQuery,
    toggleSort,
    removeSort,
    setFilter,
    addFilterValue,
    removeFilterValue,
    applyPreset,
    translate,
    querySync: syncToRoute
  }
}
