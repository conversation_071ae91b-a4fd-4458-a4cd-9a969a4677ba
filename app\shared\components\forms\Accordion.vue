<template>
  <div class="space-y-2">
    <div v-for="(accordion, index) in accordions" :key="accordion.name" class="border border-gray-200 rounded-md">
      <button
        @click="toggleAccordion(accordion.name)"
        class="w-full flex justify-between items-center p-4 text-left text-gray-800 bg-gray-50 hover:bg-gray-100 focus:outline-none"
      >
        <span class="flex items-center">
          <span class="font-medium">{{ accordion.label }}</span>
          <span
            v-if="accordionHasErrors(accordion.fields)"
            class="ml-2 hidden md:inline-block bg-red-100 text-red-600 py-0.5 px-2 rounded-full text-xs font-medium"
          >
            Error
          </span>
        </span>
        <svg
          class="w-5 h-5 transform transition-transform"
          :class="{ 'rotate-180': isOpen(accordion.name) }"
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 20 20"
          fill="currentColor"
        >
          <path
            fill-rule="evenodd"
            d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
            clip-rule="evenodd"
          />
        </svg>
      </button>
      <div v-show="isOpen(accordion.name)" class="p-4 border-t border-gray-200">
        <slot :name="accordion.name" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useFormErrors } from 'vee-validate'

const props = defineProps({
  accordions: {
    type: Array as () => Array<{ name: string; label: string; fields: string[] }>,
    required: true,
  },
  modelValue: {
    type: [String, Array],
    default: null,
  },
  multiple: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['update:modelValue'])
const errors = useFormErrors()

// const activeAccordion = computed({
//   get: () => props.modelValue,
//   set: (value) => emit('update:modelValue', value),
// })
const activeAccordion = ref(props.modelValue)

const isOpen = (name: string) => {
  if (props.multiple) {
    return Array.isArray(activeAccordion.value) && activeAccordion.value.includes(name)
  }
  return activeAccordion.value === name
}

const toggleAccordion = (name: string) => {
  if (props.multiple) {
    let openAccordions = Array.isArray(activeAccordion.value) ? [...activeAccordion.value] : []
    if (openAccordions.includes(name)) {
      openAccordions = openAccordions.filter((item) => item !== name)
    } else {
      openAccordions.push(name)
    }
    activeAccordion.value = openAccordions
  } else {
    activeAccordion.value = activeAccordion.value === name ? null : name
  }
}

const accordionHasErrors = (fields: string[]) => {
  if (!fields) {
    return false
  }
  return fields.some(field => errors.value[field])
}
</script>