import { dateFormatters } from './dateFormatters'

export const formatters = {
  dateTime: (value: string | Date | undefined | null): string => {
    // Use the new moment-based formatter with fallback
    return dateFormatters.formatDateTime(value)
  },

  date: (value: string | Date | undefined | null): string => {
    // Use the new moment-based formatter with fallback
    return dateFormatters.formatDateDisplay(value)
  },

  // Add other formatters as needed
  // e.g., currency, capitalize, etc.
};

// Optional: Export individual formatters if preferred
export const { dateTime, date } = formatters;