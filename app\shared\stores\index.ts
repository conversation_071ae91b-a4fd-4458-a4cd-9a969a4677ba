/**
 * Modular Store Architecture
 * 
 * Central store management with composition patterns,
 * performance optimizations, and selective hydration
 */

import { createPinia, setActivePinia, defineStore } from 'pinia' // Added defineStore
import type { App } from 'vue'
import { useLogger } from '@shared/composables/useLogger'

// ============================================================================
// STORE REGISTRY
// ============================================================================

interface StoreModule {
  name: string
  store: any
  priority: number
  lazy: boolean
  dependencies: string[]
}

interface StoreConfig {
  enableDevtools: boolean
  enablePersistence: boolean
  enableHydration: boolean
  enableMetrics: boolean
  logLevel: 'debug' | 'info' | 'warn' | 'error'
}

class StoreRegistry {
  public modules = new Map<string, StoreModule>() // Made public for external access, consider refactor
  private initialized = new Set<string>()
  private logger = useLogger('StoreRegistry')
  
  register(module: StoreModule) {
    this.modules.set(module.name, module)
    this.logger.debug('Store module registered', { name: module.name })
  }
  
  async initialize(_config: StoreConfig) { // Prefixed unused config
    this.logger.info('Initializing store modules')
    
    // Sort by priority and dependencies
    const sortedModules = this.topologicalSort()
    
    for (const module of sortedModules) {
      if (!module.lazy) {
        await this.initializeModule(module)
      }
    }
    
    this.logger.info('Store modules initialized')
  }
  
  async initializeModule(module: StoreModule) {
    if (this.initialized.has(module.name)) return
    
    try {
      // Initialize dependencies first
      for (const depName of module.dependencies) {
        const dep = this.modules.get(depName)
        if (dep && !this.initialized.has(depName)) {
          await this.initializeModule(dep)
        }
      }
      
      // Initialize the module
      if (typeof module.store.initialize === 'function') {
        await module.store.initialize()
      }
      
      this.initialized.add(module.name)
      this.logger.debug('Store module initialized', { name: module.name })
    } catch (error) {
      this.logger.error('Store module initialization failed', { 
        name: module.name, 
        error 
      })
    }
  }
  
  private topologicalSort(): StoreModule[] {
    const visited = new Set<string>()
    const result: StoreModule[] = []
    
    const visit = (moduleName: string) => {
      if (visited.has(moduleName)) return
      
      const module = this.modules.get(moduleName)
      if (!module) return
      
      visited.add(moduleName)
      
      // Visit dependencies first
      for (const dep of module.dependencies) {
        visit(dep)
      }
      
      result.push(module)
    }
    
    // Sort by priority first
    const moduleNames = Array.from(this.modules.keys())
      .sort((a, b) => {
        const moduleA = this.modules.get(a)!
        const moduleB = this.modules.get(b)!
        return moduleA.priority - moduleB.priority
      })
    
    for (const name of moduleNames) {
      visit(name)
    }
    
    return result
  }
}

// ============================================================================
// STORE COMPOSITION UTILITIES
// ============================================================================

/**
 * Store composition helper for creating modular stores
 */
export function defineModularStore<T extends Record<string, any>>(
  name: string,
  composables: T,
  options: {
    priority?: number
    lazy?: boolean
    dependencies?: string[]
    persist?: boolean
    hydrate?: boolean
  } = {}
) {
  const {
    priority = 0,
    lazy = false,
    dependencies = []
    // persist and hydrate were unused and removed from destructuring
  } = options
  
  const store = defineStore(name, () => {
    const logger = useLogger(`Store:${name}`)
    
    // Compose all provided composables
    const composed = {} as Record<string, any> // Use Record for easier assignment
    for (const [key, composable] of Object.entries(composables)) {
      if (typeof composable === 'function') {
        composed[key] = composable();
      } else {
        composed[key] = composable;
      }
    }
    
    // Add common store methods
    const initialize = async () => {
      logger.info('Initializing store')
      
      // Call initialize on all composables that have it
      for (const [_key, value] of Object.entries(composed)) { // Prefixed unused key
        if (value && typeof value.initialize === 'function') {
          await value.initialize()
        }
      }
      
      logger.info('Store initialized')
    }
    
    const destroy = async () => {
      logger.info('Destroying store')
      
      // Call destroy on all composables that have it
      for (const [_key, value] of Object.entries(composed)) { // Prefixed unused key
        if (value && typeof value.destroy === 'function') {
          await value.destroy()
        }
      }
      
      logger.info('Store destroyed')
    }
    
    const getMetrics = () => {
      const metrics: Record<string, any> = {}
      
      for (const [_key, value] of Object.entries(composed)) { // Prefixed unused key
        if (value && typeof value.getMetrics === 'function') {
          metrics[_key] = value.getMetrics(); // Use _key if it were used
        }
      }
      
      return metrics
    }
    
    return {
      ...composed,
      initialize,
      destroy,
      getMetrics
    }
  })
  
  // Register the store module
  registry.register({
    name,
    store,
    priority,
    lazy,
    dependencies
  })
  
  return store
}

/**
 * Store persistence helper
 */
export function useStorePersistence<T>(
  key: string,
  state: T,
  options: {
    storage?: 'localStorage' | 'sessionStorage'
    serializer?: {
      serialize: (value: T) => string
      deserialize: (value: string) => T
    }
    beforeSave?: (state: T) => T
    afterLoad?: (state: T) => T
  } = {}
) {
  const {
    storage = 'localStorage',
    serializer = {
      serialize: JSON.stringify,
      deserialize: JSON.parse
    },
    beforeSave,
    afterLoad
  } = options
  
  const logger = useLogger('StorePersistence')
  
  const getStorage = () => {
    if (typeof window === 'undefined') return null
    return storage === 'localStorage' ? window.localStorage : window.sessionStorage
  }
  
  const save = () => {
    try {
      const storageInstance = getStorage()
      if (!storageInstance) return
      
      let dataToSave = state
      if (beforeSave) {
        dataToSave = beforeSave(state)
      }
      
      const serialized = serializer.serialize(dataToSave)
      storageInstance.setItem(key, serialized)
      
      logger.debug('State persisted', { key })
    } catch (error) {
      logger.error('Failed to persist state', { key, error })
    }
  }
  
  const load = (): T | null => {
    try {
      const storageInstance = getStorage()
      if (!storageInstance) return null
      
      const serialized = storageInstance.getItem(key)
      if (!serialized) return null
      
      let data = serializer.deserialize(serialized)
      if (afterLoad) {
        data = afterLoad(data)
      }
      
      logger.debug('State loaded', { key })
      return data
    } catch (error) {
      logger.error('Failed to load state', { key, error })
      return null
    }
  }
  
  const remove = () => {
    try {
      const storageInstance = getStorage()
      if (!storageInstance) return
      
      storageInstance.removeItem(key)
      logger.debug('State removed', { key })
    } catch (error) {
      logger.error('Failed to remove state', { key, error })
    }
  }
  
  return {
    save,
    load,
    remove
  }
}

/**
 * Store hydration helper for SSR
 */
export function useStoreHydration() {
  const logger = useLogger('StoreHydration')
  
  const hydrate = async (stores: string[] = []) => {
    logger.info('Starting store hydration', { stores })
    
    try {
      // Hydrate specified stores or all registered stores
      const storesToHydrate = stores.length > 0 
        ? stores 
        : Array.from(registry.modules.keys())
      
      for (const storeName of storesToHydrate) {
        const module = registry.modules.get(storeName)
        if (module && typeof module.store.hydrate === 'function') {
          await module.store.hydrate()
          logger.debug('Store hydrated', { store: storeName })
        }
      }
      
      logger.info('Store hydration completed')
    } catch (error) {
      logger.error('Store hydration failed', { error })
    }
  }
  
  const dehydrate = async (stores: string[] = []) => {
    logger.info('Starting store dehydration', { stores })
    
    try {
      const storesToDehydrate = stores.length > 0 
        ? stores 
        : Array.from(registry.modules.keys())
      
      for (const storeName of storesToDehydrate) {
        const module = registry.modules.get(storeName)
        if (module && typeof module.store.dehydrate === 'function') {
          await module.store.dehydrate()
          logger.debug('Store dehydrated', { store: storeName })
        }
      }
      
      logger.info('Store dehydration completed')
    } catch (error) {
      logger.error('Store dehydration failed', { error })
    }
  }
  
  return {
    hydrate,
    dehydrate
  }
}

// ============================================================================
// STORE METRICS AND MONITORING
// ============================================================================

/**
 * Store metrics collector
 */
export function useStoreMetrics() {
  const logger = useLogger('StoreMetrics')
  
  const collectMetrics = () => {
    const metrics: Record<string, any> = {}
    
    for (const [name, module] of registry.modules.entries()) {
      try {
        if (typeof module.store.getMetrics === 'function') {
          metrics[name] = module.store.getMetrics()
        }
      } catch (error) {
        logger.error('Failed to collect metrics', { store: name, error })
      }
    }
    
    return metrics
  }
  
  const startMetricsCollection = (interval = 60000) => {
    return setInterval(() => {
      const metrics = collectMetrics()
      logger.debug('Store metrics collected', { metrics })
    }, interval)
  }
  
  return {
    collectMetrics,
    startMetricsCollection
  }
}

// ============================================================================
// GLOBAL STORE REGISTRY
// ============================================================================

const registry = new StoreRegistry()

// ============================================================================
// PINIA SETUP
// ============================================================================

export function setupStores(app: App, config: StoreConfig = {
  enableDevtools: process.env.NODE_ENV === 'development',
  enablePersistence: true,
  enableHydration: true,
  enableMetrics: true,
  logLevel: 'info'
}) {
  const pinia = createPinia()
  
  // Configure Pinia
  if (config.enableDevtools && typeof window !== 'undefined') {
    // Enable Vue devtools integration
    pinia.use(({ store }) => {
      store.$id = store.$id || 'unknown'
    })
  }
  
  app.use(pinia)
  setActivePinia(pinia)
  
  // Initialize store registry
  registry.initialize(config)
  
  return pinia
}

// ============================================================================
// EXPORTS
// ============================================================================

// Removed redundant export block, functions are already exported.

// Re-export commonly used stores
export { useAuthStore, useAuthCacheStore } from '../features/auth/stores/authStore.js' // Added .js
