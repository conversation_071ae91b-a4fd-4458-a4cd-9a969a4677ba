<!--
  User Settings Form Component
  
  Form for managing user account settings
-->

<template>
  <div class="max-w-4xl mx-auto space-y-6">
    <!-- Header -->
    <div>
      <h1 class="text-2xl font-bold text-gray-900">Account Settings</h1>
      <p class="mt-1 text-sm text-gray-500">
        Manage your account preferences and security settings
      </p>
    </div>

    <!-- Profile Settings -->
    <div class="bg-white shadow rounded-lg p-6">
      <h2 class="text-lg font-medium text-gray-900 mb-6">Profile Information</h2>
      
      <form @submit.prevent="saveProfile" class="space-y-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Name -->
          <div>
            <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
              Full Name
            </label>
            <input
              id="name"
              v-model="profileForm.name"
              type="text"
              class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
            />
          </div>

          <!-- Email -->
          <div>
            <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
              Email Address
            </label>
            <input
              id="email"
              v-model="profileForm.email"
              type="email"
              class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
            />
          </div>

          <!-- Phone -->
          <div>
            <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">
              Phone Number
            </label>
            <input
              id="phone"
              v-model="profileForm.phone"
              type="tel"
              class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
            />
          </div>

          <!-- Department -->
          <div>
            <label for="department" class="block text-sm font-medium text-gray-700 mb-2">
              Department
            </label>
            <input
              id="department"
              v-model="profileForm.department"
              type="text"
              class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
            />
          </div>
        </div>

        <div class="flex justify-end">
          <button
            type="submit"
            :disabled="isSavingProfile"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <UiSpinner v-if="isSavingProfile" size="sm" color="white" class="mr-2" />
            {{ isSavingProfile ? 'Saving...' : 'Save Profile' }}
          </button>
        </div>
      </form>
    </div>

    <!-- Security Settings -->
    <div class="bg-white shadow rounded-lg p-6">
      <h2 class="text-lg font-medium text-gray-900 mb-6">Security</h2>
      
      <form @submit.prevent="changePassword" class="space-y-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Current Password -->
          <div>
            <label for="currentPassword" class="block text-sm font-medium text-gray-700 mb-2">
              Current Password
            </label>
            <input
              id="currentPassword"
              v-model="passwordForm.currentPassword"
              type="password"
              class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
            />
          </div>

          <div></div>

          <!-- New Password -->
          <div>
            <label for="newPassword" class="block text-sm font-medium text-gray-700 mb-2">
              New Password
            </label>
            <input
              id="newPassword"
              v-model="passwordForm.newPassword"
              type="password"
              class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
            />
          </div>

          <!-- Confirm Password -->
          <div>
            <label for="confirmPassword" class="block text-sm font-medium text-gray-700 mb-2">
              Confirm New Password
            </label>
            <input
              id="confirmPassword"
              v-model="passwordForm.confirmPassword"
              type="password"
              class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
            />
          </div>
        </div>

        <div class="flex justify-end">
          <button
            type="submit"
            :disabled="isSavingPassword"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <UiSpinner v-if="isSavingPassword" size="sm" color="white" class="mr-2" />
            {{ isSavingPassword ? 'Updating...' : 'Update Password' }}
          </button>
        </div>
      </form>
    </div>

    <!-- Notification Settings -->
    <div class="bg-white shadow rounded-lg p-6">
      <h2 class="text-lg font-medium text-gray-900 mb-6">Notifications</h2>
      
      <div class="space-y-4">
        <div class="flex items-center justify-between">
          <div>
            <h3 class="text-sm font-medium text-gray-900">Email Notifications</h3>
            <p class="text-sm text-gray-500">Receive notifications via email</p>
          </div>
          <input
            v-model="notificationSettings.email"
            type="checkbox"
            class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
          />
        </div>

        <div class="flex items-center justify-between">
          <div>
            <h3 class="text-sm font-medium text-gray-900">Case Updates</h3>
            <p class="text-sm text-gray-500">Get notified about case status changes</p>
          </div>
          <input
            v-model="notificationSettings.caseUpdates"
            type="checkbox"
            class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
          />
        </div>

        <div class="flex items-center justify-between">
          <div>
            <h3 class="text-sm font-medium text-gray-900">Document Sharing</h3>
            <p class="text-sm text-gray-500">Notifications when documents are shared with you</p>
          </div>
          <input
            v-model="notificationSettings.documentSharing"
            type="checkbox"
            class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
          />
        </div>
      </div>

      <div class="mt-6 flex justify-end">
        <button
          @click="saveNotifications"
          :disabled="isSavingNotifications"
          class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <UiSpinner v-if="isSavingNotifications" size="sm" color="white" class="mr-2" />
          {{ isSavingNotifications ? 'Saving...' : 'Save Preferences' }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'

// State
const isSavingProfile = ref(false)
const isSavingPassword = ref(false)
const isSavingNotifications = ref(false)

const profileForm = reactive({
  name: 'John Doe',
  email: '<EMAIL>',
  phone: '+****************',
  department: 'Legal'
})

const passwordForm = reactive({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
})

const notificationSettings = reactive({
  email: true,
  caseUpdates: true,
  documentSharing: false
})

// Methods
const saveProfile = async () => {
  try {
    isSavingProfile.value = true
    
    // TODO: Replace with actual API call
    console.log('Saving profile:', profileForm)
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    alert('Profile updated successfully!')
    
  } catch (error) {
    console.error('Error saving profile:', error)
    alert('Failed to save profile. Please try again.')
  } finally {
    isSavingProfile.value = false
  }
}

const changePassword = async () => {
  try {
    isSavingPassword.value = true
    
    // Validate passwords
    if (passwordForm.newPassword !== passwordForm.confirmPassword) {
      alert('New passwords do not match')
      return
    }
    
    // TODO: Replace with actual API call
    console.log('Changing password')
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // Clear form
    Object.assign(passwordForm, {
      currentPassword: '',
      newPassword: '',
      confirmPassword: ''
    })
    
    alert('Password updated successfully!')
    
  } catch (error) {
    console.error('Error changing password:', error)
    alert('Failed to update password. Please try again.')
  } finally {
    isSavingPassword.value = false
  }
}

const saveNotifications = async () => {
  try {
    isSavingNotifications.value = true
    
    // TODO: Replace with actual API call
    console.log('Saving notification settings:', notificationSettings)
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    alert('Notification preferences saved!')
    
  } catch (error) {
    console.error('Error saving notifications:', error)
    alert('Failed to save preferences. Please try again.')
  } finally {
    isSavingNotifications.value = false
  }
}

// Lifecycle
onMounted(() => {
  // Load current user settings
})
</script>
