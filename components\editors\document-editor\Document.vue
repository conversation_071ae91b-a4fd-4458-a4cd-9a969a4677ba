<template>
  <div class="document-editor-container" :class="containerClasses">
    <!-- Document Canvas -->
    <div class="document-canvas" ref="canvasRef" :style="{ '--zoom-factor': zoomLevel / 100 }">
      <div class="document-page" :class="pageClasses" :style="pageStyles" ref="pageRef">

        <!-- Document Content Area -->
        <div class="document-content" :class="contentClasses">
           
            <Canvas />
           
        </div>
      </div>
    </div>

    <!-- Zoom Controls Overlay -->
    <div class="zoom-controls-overlay" v-if="showZoomControls">
      <div class="zoom-controls">
        <button
          class="zoom-button"
          @click="zoomOut"
          :disabled="zoomLevel <= minZoom"
          :title="t('documents.editor.document.zoomOut')"
        >
          <Icon name="heroicons:minus" size="calc(var(--spacing) * 4)" />
        </button>

        <div class="zoom-display">{{ Math.round(zoomLevel) }}%</div>

        <button
          class="zoom-button"
          @click="zoomIn"
          :disabled="zoomLevel >= maxZoom"
          :title="t('documents.editor.document.zoomIn')"
        >
          <Icon name="heroicons:plus" size="calc(var(--spacing) * 4)" />
        </button>
      </div>
    </div>

    <!-- Scroll Indicators -->
    <div class="scroll-indicators" v-if="showScrollIndicators">
      <div class="scroll-indicator vertical" :style="verticalScrollStyle"></div>
      <div class="scroll-indicator horizontal" :style="horizontalScrollStyle"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { watch, ref, computed, onMounted, onBeforeUnmount } from "vue";
import Canvas from './document/Canvas.vue'


// Props
interface Props {
  modelValue?: string;
  editable?: boolean;
  documentTitle?: string;
  documentSubtitle?: string;
  showHeader?: boolean;
  showFooter?: boolean;
  showZoomControls?: boolean;
  showScrollIndicators?: boolean;
  initialZoom?: number;
  zoom: number;
  minZoom?: number;
  maxZoom?: number;
  pageWidth?: number;
  pageHeight?: number;
  currentPage?: number;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: "",
  editable: true,
  documentTitle: "Untitled Document",
  documentSubtitle: "Legal Document",
  showHeader: false,
  showFooter: false,
  showZoomControls: false,
  showScrollIndicators: true,
  initialZoom: 100,
  zoom: 100,
  minZoom: 25,
  maxZoom: 500,
  pageWidth: 816, // A4 width in pixels at 96 DPI
  pageHeight: 1056, // A4 height in pixels at 96 DPI
  currentPage: 1,
});

// Emits
const emit = defineEmits<{
  "update:modelValue": [value: string];
  "zoom-change": [zoom: number];
  focus: [];
  blur: [];
  scroll: [event: Event];
}>();

// Composables
const { t } = useI18n();
const { locale } = useI18n();


// RTL languages
const rtlLanguages = ["he", "ar"];
const isRTL = computed(() => rtlLanguages.includes(locale.value));



// Local reactive state for UI
const canvasRef = ref<HTMLElement | null>(null);
const pageRef = ref<HTMLElement | null>(null);
const editorRef = ref<HTMLElement | null>(null);
const scrollTop = ref(0);
const scrollLeft = ref(0);
const canvasHeight = ref(0);
const canvasWidth = ref(0);

// Computed properties
const containerClasses = computed(() => ({
  rtl: isRTL.value,
  ltr: !isRTL.value,
  editable: props.editable,
  readonly: !props.editable,
}));

const pageClasses = computed(() => ({
  rtl: isRTL.value,
  ltr: !isRTL.value,
}));

const contentClasses = computed(() => ({
  rtl: isRTL.value,
  ltr: !isRTL.value,
}));

const pageStyles = computed(() => ({
  width: `${props.pageWidth}px`,
  height: `${props.pageHeight}px`,
  transform: `scale(${zoomLevel.value / 100})`,
  transformOrigin: "top center",
  '--zoom-factor': zoomLevel.value / 100,
}));

const verticalScrollStyle = computed(() => {
  if (!canvasHeight.value) return {};
  const percentage = (scrollTop.value / canvasHeight.value) * 100;
  return {
    top: `${percentage}%`,
  };
});

const horizontalScrollStyle = computed(() => {
  if (!canvasWidth.value) return {};
  const percentage = (scrollLeft.value / canvasWidth.value) * 100;
  return {
    left: `${percentage}%`,
  };
});

watch(
  () => props.zoom,
  (newZoom, oldZoom) => {
    let zoom = newZoom;
    if (newZoom > oldZoom) {
      zoom = Math.min(newZoom + 25, props.maxZoom);
    } else if (newZoom < oldZoom) {
      zoom = Math.max(newZoom - 25, props.minZoom);
    }
    setZoom(zoom);
    console.log("Zoom changed to:", zoom);
  }
);


const handleInput = (event: Event) => {
  const target = event.target as HTMLElement;
  emit("update:modelValue", target.innerHTML);
};

const handleFocus = () => {
  emit("focus");
};

const handleBlur = () => {
  emit("blur");
};

const handleScroll = (event: Event) => {
  if (canvasRef.value) {
    scrollTop.value = canvasRef.value.scrollTop;
    scrollLeft.value = canvasRef.value.scrollLeft;
  }
  emit("scroll", event);
};

const updateCanvasDimensions = () => {
  if (canvasRef.value) {
    canvasHeight.value = canvasRef.value.scrollHeight;
    canvasWidth.value = canvasRef.value.scrollWidth;
  }
};

// Lifecycle
onMounted(() => {
  if (canvasRef.value) {
    canvasRef.value.addEventListener("scroll", handleScroll);
    updateCanvasDimensions();
  }

  // Set initial content
  if (editorRef.value && props.modelValue) {
    editorRef.value.innerHTML = props.modelValue;
  }
});

onBeforeUnmount(() => {
  if (canvasRef.value) {
    canvasRef.value.removeEventListener("scroll", handleScroll);
  }
});



// Watch for content changes
watch(
  () => props.modelValue,
  (newValue) => {
    if (editorRef.value && editorRef.value.innerHTML !== newValue) {
      editorRef.value.innerHTML = newValue;
    }
  }
);

const zoomLevel = ref(props.initialZoom);

// Expose methods for parent components
defineExpose({

});
</script>

<style scoped>
@reference '~/assets/css/tailwind.css';

.document-editor-container {
  @apply relative w-full h-full bg-gray-100 dark:bg-gray-900 overflow-hidden;
}

.document-canvas {
  @apply w-full h-full overflow-auto bg-gray-100 dark:bg-gray-900;
  scroll-behavior: smooth;
  /* Dynamic padding based on zoom level to ensure document is always visible */
  padding: calc(2rem * var(--zoom-factor, 1));
  display: flex;
  justify-content: center;
  align-items: flex-start;
  /* Minimum height to accommodate scaled document */
  min-height: calc(100vh + 200px);
}

.document-page {
  @apply bg-white dark:bg-gray-800 shadow-lg relative;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(0, 0, 0, 0.1);
  /* Fixed dimensions - zoom will be handled by transform scale */
  width: 816px; /* A4 width */
  min-height: 1056px; /* A4 height */
  /* Ensure proper spacing around scaled document */
  margin: 2rem auto;
}

.document-header {
  @apply border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800;
  padding: 24px 48px;
}

.header-content {
  @apply flex items-center justify-between;
}

.document-title {
  @apply flex items-center gap-4;
}

.title-icon {
  @apply flex-shrink-0;
}

.title-text {
  @apply flex flex-col;
}

.document-name {
  @apply text-2xl font-bold text-gray-900 dark:text-white mb-1;
}

.document-subtitle {
  @apply text-sm text-gray-600 dark:text-gray-400;
}

.document-content {
  @apply flex-1 relative;
  min-height: 1000px; /* Fixed minimum height for consistent layout */
}

.editor-content {
  @apply w-full h-full text-gray-900 dark:text-gray-100 leading-relaxed;
  font-family: "Times New Roman", serif;
  font-size: 12pt;
  line-height: 1.6;
  outline: none;
  padding: 3rem; /* 48px equivalent */
  box-sizing: border-box;
}

.editor-content:focus {
  @apply outline-none;
}

.default-content h1 {
  @apply text-3xl font-bold text-gray-900 dark:text-white mb-6;
}

.default-content p {
  @apply text-base text-gray-700 dark:text-gray-300 mb-4;
}

.document-footer {
  @apply border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800;
  padding: 12px 48px;
}

.footer-content {
  @apply flex items-center justify-center;
}

.page-info {
  @apply text-sm text-gray-600 dark:text-gray-400;
}

/* Zoom Controls */
.zoom-controls-overlay {
  @apply absolute bottom-6 right-6 z-10;
}

.zoom-controls {
  @apply flex items-center bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg;
}

.zoom-button {
  @apply p-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed;
}

.zoom-button:first-child {
  @apply rounded-l-lg;
}

.zoom-button:last-child {
  @apply rounded-r-lg;
}

.zoom-display {
  @apply px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 border-x border-gray-300 dark:border-gray-600 min-w-16 text-center;
}

/* Scroll Indicators */
.scroll-indicators {
  @apply absolute inset-0 pointer-events-none z-5;
}

.scroll-indicator {
  @apply absolute bg-blue-500 opacity-50 transition-all duration-200;
}

.scroll-indicator.vertical {
  @apply right-2 w-1 h-4 rounded-full;
}

.scroll-indicator.horizontal {
  @apply bottom-2 h-1 w-4 rounded-full;
}

/* Print Styles */
@media print {
  .document-editor-container {
    @apply bg-white;
  }

  .document-canvas {
    @apply p-0 bg-white;
  }

  .document-page {
    @apply shadow-none border-none;
    transform: none !important;
  }

  .zoom-controls-overlay,
  .scroll-indicators {
    @apply hidden;
  }
}

/* RTL Support */
.rtl .document-header {
  @apply text-right;
}

.rtl .document-title {
  @apply flex-row-reverse;
}

.rtl .header-content {
  @apply flex-row-reverse;
}

.rtl .footer-content {
  @apply flex-row-reverse;
}

.rtl .zoom-controls-overlay {
  @apply left-6 right-auto;
}

.rtl .scroll-indicator.vertical {
  @apply left-2 right-auto;
}

/* Responsive Design */
@media (max-width: 768px) {
  .document-canvas {
    @apply p-4;
  }

  .document-header {
    padding: 16px 24px;
  }

  .editor-content {
    @apply p-6;
  }

  .document-footer {
    padding: 8px 24px;
  }

  .zoom-controls-overlay {
    @apply bottom-4 right-4;
  }
}

/* High DPI Support */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .document-page {
    border-width: 0.5px;
  }
}

/* Focus States */
.editable .editor-content:focus {
  @apply ring-2 ring-blue-500/50;
}

/* Selection Styles */
.editor-content ::selection {
  @apply bg-blue-200 dark:bg-blue-800;
}

/* Smooth Transitions */
.document-page {
  transition: transform 0.3s ease-in-out;
  /* Ensure the document maintains its space during zoom */
  transform-origin: top center;
}

.zoom-controls {
  transition: opacity 0.2s ease-in-out, transform 0.2s ease-in-out;
}

.zoom-controls:hover {
  @apply shadow-xl;
  transform: translateY(-1px);
}
</style>
