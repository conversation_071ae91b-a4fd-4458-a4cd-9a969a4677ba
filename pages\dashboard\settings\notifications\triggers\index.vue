<template>
  <div class="container mx-auto px-4 py-8 sm:px-6 lg:px-8">
 

    <div class="flex justify-between items-center mb-6 mt-4">
      <h1 class="text-3xl font-bold text-gray-900">Notification Triggers</h1>
      <NuxtLink to="/dashboard/settings/notifications/triggers/create">
        <UiButton variant="primary">
          <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"
            aria-hidden="true">
            <path fill-rule="evenodd"
              d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z"
              clip-rule="evenodd" />
          </svg>
          Create New Trigger
        </UiButton>
      </NuxtLink>
    </div>

    <UiCard>
      <div class="mb-4">
        <UiInput id="searchTriggers" name="searchTriggers" v-model="searchQuery"
          placeholder="Search triggers by name or event type..." @input="debouncedSearch" />
      </div>

      <div v-if="isLoading" class="text-center py-8">
        <UiSpinner class="h-10 w-10 text-primary-500 mx-auto" />
        <p class="mt-2 text-gray-600">Loading notification triggers...</p>
      </div>

      <div v-else-if="error" class="py-8">
        <UiAlert type="error" title="Error Loading Triggers">
          {{ error }}
          <p class="mt-1">Please try refreshing the page or contact support.</p>
        </UiAlert>
      </div>

      <div v-else-if="filteredTriggers.length === 0" class="py-8 text-center text-gray-500">
        <p>No notification triggers found. {{ searchQuery ? 'Try adjusting your search filters.' : `Click "Create New
          Trigger" to get started!` }}</p>
      </div>

      <UiTable v-else :headers="tableHeaders" :items="filteredTriggers">
        <template #item.name="{ item }">
          <span class="font-medium text-gray-900">{{ item.name }}</span>
        </template>
        <template #item.event="{ item }">
          <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
            {{ item.event }}
          </span>
        </template>
        <template #item.conditions="{ item }">
          <span class="text-sm text-gray-700 truncate max-w-xs" :title="JSON.stringify(item.conditions, null, 2)">
            {{ formatConditions(item.conditions) }}
          </span>
        </template>
        <template #item.recipients="{ item }">
          <!-- Assuming item.recipients is an array of strings. If not, this needs adjustment -->
          <span class="text-sm text-gray-700">{{ Array.isArray(item.recipients) ? item.recipients.join(', ') : 'N/A'
          }}</span>
        </template>
        <template #item.channel="{ item }">
          <span class="text-sm text-gray-700">{{ item.channel }}</span>
        </template>
        <template #item.isActive="{ item }">
          <UiCheckbox :id="`status-toggle-${item.id}`" :model-value="item.isActive"
            @update:model-value="toggleTriggerStatus(item, $event)" :aria-label="`Toggle status for ${item.name}`" />
        </template>
        <template #item.updatedAt="{ item }">
          <div class="text-sm text-gray-700">
            <div>{{ new Date(item.updatedAt || item.createdAt).toLocaleDateString() }}</div>
            <!-- TODO: Add 'by whom' if data is available -->
            <!-- <div v-if="item.updatedBy" class="text-xs text-gray-500">by {{ item.updatedBy }}</div> -->
          </div>
        </template>
        <template #item.actions="{ item }">
          <div class="flex items-center space-x-1 justify-end">
            <NuxtLink :to="`/dashboard/settings/notifications/triggers/${item.id}/edit`"
              class="text-indigo-600 hover:text-indigo-900">
              <UiButton size="sm" variant="ghost" aria-label="Edit">
                Edit
              </UiButton>
            </NuxtLink>
            <UiButton size="sm" variant="ghost" @click="handleDuplicateTrigger(item)" aria-label="Duplicate"
              class="text-gray-700 hover:text-gray-900">
              Duplicate
            </UiButton>
            <UiButton size="sm" variant="ghost" @click="handleManualEventTrigger(item)" aria-label="Trigger Event"
              class="text-blue-600 hover:text-blue-900">
              Trigger
            </UiButton>
            <NuxtLink :to="`/dashboard/settings/notifications/triggers/${item.id}/preview`"
              class="text-green-600 hover:text-green-900">
              <UiButton size="sm" variant="ghost" aria-label="Preview">
                Preview
              </UiButton>
            </NuxtLink>
            <UiButton size="sm" variant="ghost" @click="confirmDeleteTrigger(item)" aria-label="Delete"
              class="text-red-600 hover:text-red-800">
              Delete
            </UiButton>
          </div>
        </template>
      </UiTable>
    </UiCard>

    <!-- NotificationTriggerForm modal is removed -->

    <UiModal :show="showDeleteConfirmModal" title="Confirm Deletion" confirm-button-text="Delete"
      cancel-button-text="Cancel" confirm-button-variant="danger" :loading-confirm="isLoading"
      @confirm="executeDeleteTrigger" @cancel="closeDeleteConfirmModal" icon="IconWarning" icon-type="error">
      <p class="text-sm text-gray-500">
        Are you sure you want to delete the notification trigger "<strong>{{ triggerToDelete?.name }}</strong>"?
        This action cannot be undone.
      </p>
    </UiModal>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue';
 
import UiTable from '~/components/ui/UiTable.vue';
import UiButton from '~/components/ui/UiButton.vue';
import UiCard from '~/components/ui/UiCard.vue';
import UiInput from '~/components/ui/UiInput.vue';
import UiModal from '~/components/ui/UiModal.vue';
import UiSpinner from '~/components/ui/UiSpinner.vue';
import UiAlert from '~/components/ui/UiAlert.vue';
import IconWarning from '~/components/icons/IconWarning.vue'; // For delete confirmation modal
import UiCheckbox from '~/components/ui/UiCheckbox.vue';
// NotificationTriggerForm import removed
import { useNotificationTriggerStore } from '~/stores/notificationTrigger';
import { useAuthStore } from '~/stores/auth'; // To get current user's tenant ID and roles
import { PlatformRoles, TenantRoles } from '~/app/features/auth/constants/roles';// For RBAC
import type { NotificationTrigger } from '~/types/notification'; // Corrected path
import { useDebounceFn } from '@vueuse/core';
import { useApi } from '~/composables/useApi'; // Added for manual trigger
import { useToast } from '~/composables/useToast'; // Added for manual trigger feedback

// Define page metadata and layout
definePageMeta({
  layout: 'dashboard',
  middleware: ['rbac'], // Apply RBAC middleware (auth is global)
  // roles: [TenantRoles.TENANT_OWNER, TenantRoles.ADMIN], // Only Tenant Owners and Admins can manage triggers
  title: 'Notification Triggers',
});

// Stores
const notificationTriggerStore = useNotificationTriggerStore();
const authStore = useAuthStore();

// Reactive State
const searchQuery = ref<string>('');
// showTriggerFormModal and currentTriggerForEdit removed
const showDeleteConfirmModal = ref<boolean>(false);
const triggerToDelete = ref<NotificationTrigger | null>(null);

// Computed Properties for UI elements
const isLoading = computed(() => notificationTriggerStore.getIsLoading);
const error = computed(() => notificationTriggerStore.getError);
const allTriggers = computed(() => notificationTriggerStore.allTriggers);

const filteredTriggers = computed(() => {
  if (!searchQuery.value) {
    return allTriggers.value;
  }
  const lowerCaseQuery = searchQuery.value.toLowerCase();
  return allTriggers.value.filter(trigger =>
    trigger.name.toLowerCase().includes(lowerCaseQuery) ||
    (trigger.event && trigger.event.toLowerCase().includes(lowerCaseQuery))
  );
});

const tableHeaders = [
  { key: 'name', label: 'Trigger Name', sortable: true },
  { key: 'event', label: 'Event', sortable: true },
  { key: 'conditions', label: 'Conditions' },
  { key: 'recipients', label: 'Recipients' }, // Assuming recipients data will be available
  { key: 'channel', label: 'Delivery Method', sortable: true },
  { key: 'isActive', label: 'Status', sortable: true },
  { key: 'updatedAt', label: 'Last Modified', sortable: true },
  { key: 'actions', label: 'Actions', class: 'text-right' },
];

const navBreadcrumbs = computed(() => [
  { label: 'Dashboard', href: '/dashboard' },
  { label: 'Settings', href: '/dashboard/settings' },
  { label: 'Notifications', href: '/dashboard/settings/notifications' }, // Assuming this page exists or will be created
  { label: 'Triggers', current: true },
]);

// Methods
// openCreateTriggerModal, openEditTriggerModal, closeTriggerFormModal removed

const confirmDeleteTrigger = (trigger: NotificationTrigger) => {
  triggerToDelete.value = trigger;
  showDeleteConfirmModal.value = true;
};

const closeDeleteConfirmModal = () => {
  showDeleteConfirmModal.value = false;
  triggerToDelete.value = null;
};

const executeDeleteTrigger = async () => {
  if (triggerToDelete.value?.id) {
    try {
      await notificationTriggerStore.deleteNotificationTrigger(triggerToDelete.value.id);
      closeDeleteConfirmModal();
      // Triggers array is already updated by the store action
      // A success toast is shown by the store action itself.
    } catch (error) {
      // Error toast is shown by the store action itself.
      // Additional error handling specific to this page can be done here if needed.
      console.error('Failed to delete notification trigger from page:', error);
    }
  }
};

const previewTrigger = async (trigger: NotificationTrigger) => {
  if (trigger.id) {
    await notificationTriggerStore.previewNotificationTrigger(trigger.id);
  }
};

const formatConditions = (conditions: Record<string, any>): string => {
  if (!conditions || Object.keys(conditions).length === 0) return 'N/A';
  // Simple summary: take first 2-3 conditions
  return Object.entries(conditions)
    .slice(0, 2)
    .map(([key, value]) => `${key}: ${value}`)
    .join('; ') + (Object.keys(conditions).length > 2 ? '...' : '');
};

const toggleTriggerStatus = async (trigger: NotificationTrigger, newStatus: boolean) => {
  if (trigger.id) {
    try {
      await notificationTriggerStore.updateNotificationTrigger(trigger.id, { isActive: newStatus });
      // The store action should show a toast and update the local state,
      // so no need to re-fetch or show toast here unless specific behavior is needed.
      // If the store doesn't update local state reactively, you might need:
      // notificationTriggerStore.fetchNotificationTriggers();
    } catch (error) {
      console.error('Failed to update trigger status from page:', error);
      // Error toast is likely handled by the store action.
      // Revert checkbox if update failed and store doesn't handle it:
      // const originalTrigger = allTriggers.value.find(t => t.id === trigger.id);
      // if (originalTrigger) originalTrigger.isActive = !newStatus;
    }
  }
};

const handleDuplicateTrigger = (trigger: NotificationTrigger) => {
  // Placeholder for duplication logic
  // This would typically involve:
  // 1. Navigating to the create page with form pre-filled from `trigger`
  //    (excluding 'id', 'createdAt', 'updatedAt')
  // 2. Or, a direct API call to a duplicate endpoint if available.
  console.log('Duplicate trigger:', trigger.name);
  const router = useRouter();
  // Example: Pass relevant data as query params to the create page
  const { id, createdAt, updatedAt, tenantId, ...duplicationPayload } = trigger;
  router.push({
    path: '/dashboard/settings/notifications/triggers/create',
    query: { duplicateData: JSON.stringify(duplicationPayload) }
  });
  // A toast message can be shown here or on the create page upon successful pre-fill.
  const { showToast } = useToast(); // Assuming useToast is available
  showToast({
    type: 'info',
    title: 'Duplicating Trigger',
    message: `Pre-filling form to duplicate '${trigger.name}'. Please review and save.`,
    timeout: 5000,
  });
};

const handleManualEventTrigger = async (trigger: NotificationTrigger) => {
  const { post } = useApi();
  const { showToast } = useToast();

  if (!authStore.user) {
    showToast({
      type: 'error',
      title: 'Authentication Error',
      message: 'User information not available. Please log in again.',
    });
    return;
  }

  const payload = {
    event: trigger.event,
    payload: {
      userId: authStore.user.id,
      tenantId: authStore.user.tenantId, // Using current user's tenantId for the event payload
      email: authStore.user.email,
      
      // You might want to add other generic or specific data here
      // depending on what the /events/trigger endpoint expects for various events.
      // For now, this matches the example structure with current user data.
      triggeredBy: authStore.user.id, // Good to know who manually triggered it
      triggerName: trigger.name, // Context for the event
      notificationTriggerId: trigger.id, // Context for the event
    },
  };

  try {
    // isLoading.value = true; // Optionally set a local loading state if needed for this specific action
    await post('/events/trigger', payload);
    showToast({
      type: 'success',
      title: 'Event Triggered',
      message: `Event '${trigger.event}' for trigger '${trigger.name}' sent successfully.`,
      timeout: 5000,
    });
  } catch (err: any) {
    console.error('Failed to trigger event:', err);
    const errorMessage = err.response?.data?.message || err.message || 'An unknown error occurred.';
    showToast({
      type: 'error',
      title: 'Event Trigger Failed',
      message: `Could not send event '${trigger.event}': ${errorMessage}`,
      timeout: 7000,
    });
  } finally {
    // isLoading.value = false;
  }
};

const handleTriggerSaved = () => {
  notificationTriggerStore.fetchNotificationTriggers(); // Re-fetch all triggers to update the list
};

// Debounced search for better performance on input
const debouncedSearch = useDebounceFn(() => {
  // No need to call fetch here, as `filteredTriggers` computed property handles filtering
  // based on `searchQuery.value`.
  // If backend search is desired, then `notificationTriggerStore.fetchNotificationTriggers()`
  // would be called here with a search parameter.
}, 300);

// Lifecycle Hook
onMounted(() => {
  notificationTriggerStore.fetchNotificationTriggers(); // Initial fetch of triggers
});
</script>

<style scoped>
/* No specific scoped styles needed, Tailwind CSS handles most of the styling */
</style>