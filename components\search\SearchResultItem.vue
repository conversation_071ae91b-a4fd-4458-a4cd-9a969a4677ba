<template>
  <div
    :class="[
      'group relative bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700',
      'hover:shadow-md hover:border-gray-300 dark:hover:border-gray-600 transition-all duration-200',
      'cursor-pointer overflow-hidden',
      viewMode === 'grid' ? 'p-6' : 'p-4',
      compactMode ? 'py-3' : ''
    ]"
    @click="handleClick"
  >
    <!-- List View Layout -->
    <div v-if="viewMode === 'list'" class="flex items-start gap-4">
      <!-- Icon/Thumbnail -->
      <div class="flex-shrink-0">
        <div
          v-if="showThumbnail && result.thumbnail"
          class="w-12 h-12 rounded-lg overflow-hidden bg-gray-100 dark:bg-gray-700"
        >
          <img
            :src="result.thumbnail"
            :alt="result.title"
            class="w-full h-full object-cover"
          />
        </div>
        <div
          v-else
          :class="[
            'w-12 h-12 rounded-lg flex items-center justify-center',
            `bg-${resultColor}-100 dark:bg-${resultColor}-900/20`
          ]"
        >
          <Icon
            :name="resultIcon"
            :class="[
              'h-6 w-6',
              `text-${resultColor}-600 dark:text-${resultColor}-400`
            ]"
          />
        </div>
      </div>

      <!-- Content -->
      <div class="flex-1 min-w-0">
        <div class="flex items-start justify-between">
          <div class="flex-1 min-w-0">
            <!-- Title -->
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white truncate group-hover:text-brandPrimary transition-colors">
              <span v-if="highlightQuery" v-html="highlightText(result.title, highlightQuery)"></span>
              <span v-else>{{ result.title }}</span>
            </h3>

            <!-- Description -->
            <p v-if="result.description" class="mt-1 text-sm text-gray-600 dark:text-gray-400 line-clamp-2">
              <span v-if="highlightQuery" v-html="highlightText(result.description, highlightQuery)"></span>
              <span v-else>{{ result.description }}</span>
            </p>

            <!-- Metadata -->
            <div v-if="showMetadata" class="mt-2 flex items-center gap-4 text-xs text-gray-500 dark:text-gray-400">
              <span class="flex items-center gap-1">
                <Icon name="heroicons:calendar" class="h-3 w-3" />
                {{ formatDate(result.updatedAt || result.createdAt) }}
              </span>
              <span v-if="result.status" :class="getStatusColor(result.status)" class="px-2 py-1 rounded-full font-medium">
                {{ result.status }}
              </span>
              <span class="capitalize">{{ result.type }}</span>
            </div>
          </div>

          <!-- Actions -->
          <div v-if="showActions" class="flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity">
            <UiButton
              size="sm"
              variant="ghost"
              @click.stop="$emit('action', 'favorite', result)"
            >
              <Icon name="heroicons:heart" class="h-4 w-4" />
            </UiButton>
            <UiButton
              size="sm"
              variant="ghost"
              @click.stop="$emit('action', 'share', result)"
            >
              <Icon name="heroicons:share" class="h-4 w-4" />
            </UiButton>
          </div>
        </div>

        <!-- Tags -->
        <div v-if="showTags && result.tags?.length" class="mt-3 flex flex-wrap gap-1">
          <span
            v-for="tag in result.tags.slice(0, 3)"
            :key="tag"
            class="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300"
          >
            {{ tag }}
          </span>
          <span
            v-if="result.tags.length > 3"
            class="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-500 dark:text-gray-400"
          >
            +{{ result.tags.length - 3 }}
          </span>
        </div>
      </div>
    </div>

    <!-- Grid View Layout -->
    <div v-else-if="viewMode === 'grid'" class="text-center">
      <!-- Icon/Thumbnail -->
      <div class="mx-auto mb-4">
        <div
          v-if="showThumbnail && result.thumbnail"
          class="w-16 h-16 mx-auto rounded-xl overflow-hidden bg-gray-100 dark:bg-gray-700"
        >
          <img
            :src="result.thumbnail"
            :alt="result.title"
            class="w-full h-full object-cover"
          />
        </div>
        <div
          v-else
          :class="[
            'w-16 h-16 mx-auto rounded-xl flex items-center justify-center',
            `bg-${resultColor}-100 dark:bg-${resultColor}-900/20`
          ]"
        >
          <Icon
            :name="resultIcon"
            :class="[
              'h-8 w-8',
              `text-${resultColor}-600 dark:text-${resultColor}-400`
            ]"
          />
        </div>
      </div>

      <!-- Content -->
      <div>
        <!-- Title -->
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white truncate group-hover:text-brandPrimary transition-colors">
          <span v-if="highlightQuery" v-html="highlightText(result.title, highlightQuery)"></span>
          <span v-else>{{ result.title }}</span>
        </h3>

        <!-- Description -->
        <p v-if="result.description" class="mt-2 text-sm text-gray-600 dark:text-gray-400 line-clamp-3">
          <span v-if="highlightQuery" v-html="highlightText(result.description, highlightQuery)"></span>
          <span v-else>{{ result.description }}</span>
        </p>

        <!-- Metadata -->
        <div v-if="showMetadata" class="mt-3 flex items-center justify-center gap-2 text-xs text-gray-500 dark:text-gray-400">
          <span>{{ formatDate(result.updatedAt || result.createdAt) }}</span>
          <span v-if="result.status" :class="getStatusColor(result.status)" class="px-2 py-1 rounded-full font-medium">
            {{ result.status }}
          </span>
        </div>

        <!-- Tags -->
        <div v-if="showTags && result.tags?.length" class="mt-3 flex flex-wrap justify-center gap-1">
          <span
            v-for="tag in result.tags.slice(0, 2)"
            :key="tag"
            class="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300"
          >
            {{ tag }}
          </span>
        </div>
      </div>

      <!-- Actions -->
      <div v-if="showActions" class="mt-4 flex items-center justify-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity">
        <UiButton
          size="sm"
          variant="ghost"
          @click.stop="$emit('action', 'favorite', result)"
        >
          <Icon name="heroicons:heart" class="h-4 w-4" />
        </UiButton>
        <UiButton
          size="sm"
          variant="ghost"
          @click.stop="$emit('action', 'share', result)"
        >
          <Icon name="heroicons:share" class="h-4 w-4" />
        </UiButton>
      </div>
    </div>

    <!-- Table View Layout -->
    <div v-else class="flex items-center gap-3 py-2">
      <!-- Icon -->
      <div class="flex-shrink-0">
        <div
          :class="[
            'w-8 h-8 rounded-lg flex items-center justify-center',
            `bg-${resultColor}-100 dark:bg-${resultColor}-900/20`
          ]"
        >
          <Icon
            :name="resultIcon"
            :class="[
              'h-4 w-4',
              `text-${resultColor}-600 dark:text-${resultColor}-400`
            ]"
          />
        </div>
      </div>

      <!-- Content -->
      <div class="flex-1 min-w-0">
        <div class="flex items-center justify-between">
          <div class="flex-1 min-w-0">
            <h4 class="text-sm font-medium text-gray-900 dark:text-white truncate">
              <span v-if="highlightQuery" v-html="highlightText(result.title, highlightQuery)"></span>
              <span v-else>{{ result.title }}</span>
            </h4>
            <p v-if="result.description" class="text-xs text-gray-500 dark:text-gray-400 truncate">
              {{ result.description }}
            </p>
          </div>
          <div class="flex items-center gap-2 text-xs text-gray-500 dark:text-gray-400">
            <span>{{ formatDate(result.updatedAt || result.createdAt) }}</span>
            <span v-if="result.status" :class="getStatusColor(result.status)" class="px-2 py-1 rounded-full font-medium">
              {{ result.status }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- Hover overlay for better click feedback -->
    <div class="absolute inset-0 bg-brandPrimary/5 opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none rounded-xl"></div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { GlobalSearchResultItem, SearchViewMode } from '~/app/shared/types/search'

interface Props {
  result: GlobalSearchResultItem
  viewMode: SearchViewMode
  showThumbnail?: boolean
  showMetadata?: boolean
  showTags?: boolean
  showActions?: boolean
  highlightQuery?: string
  compactMode?: boolean
}

interface Emits {
  (e: 'click', result: GlobalSearchResultItem): void
  (e: 'action', action: string, result: GlobalSearchResultItem): void
}

const props = withDefaults(defineProps<Props>(), {
  showThumbnail: true,
  showMetadata: true,
  showTags: true,
  showActions: true,
  compactMode: false
})

const emit = defineEmits<Emits>()

// Computed properties
const resultIcon = computed(() => {
  if (props.result.icon) return props.result.icon
  
  const iconMap = {
    cases: 'heroicons:briefcase',
    documents: 'heroicons:document-text',
    users: 'heroicons:users',
    clients: 'heroicons:user-group',
    templates: 'heroicons:document-duplicate'
  }
  
  return iconMap[props.result.type] || 'heroicons:document'
})

const resultColor = computed(() => {
  const colorMap = {
    cases: 'blue',
    documents: 'green',
    users: 'purple',
    clients: 'orange',
    templates: 'indigo'
  }
  
  return colorMap[props.result.type] || 'gray'
})

// Methods
function handleClick() {
  emit('click', props.result)
}

function highlightText(text: string, query: string): string {
  if (!query || !text) return text
  
  const regex = new RegExp(`(${query})`, 'gi')
  return text.replace(regex, '<mark class="bg-yellow-200 dark:bg-yellow-800 px-1 rounded">$1</mark>')
}

function formatDate(date?: string): string {
  if (!date) return ''
  
  const now = new Date()
  const resultDate = new Date(date)
  const diffTime = Math.abs(now.getTime() - resultDate.getTime())
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  
  if (diffDays === 1) return 'Today'
  if (diffDays === 2) return 'Yesterday'
  if (diffDays <= 7) return `${diffDays} days ago`
  
  return resultDate.toLocaleDateString()
}

function getStatusColor(status: string): string {
  const statusColors = {
    active: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400',
    inactive: 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400',
    pending: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400',
    closed: 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400',
    draft: 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400'
  }
  
  return statusColors[status as keyof typeof statusColors] || 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
}
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

:deep(mark) {
  background-color: rgb(254 240 138);
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
}

:deep(.dark mark) {
  background-color: rgb(133 77 14);
}
</style>
