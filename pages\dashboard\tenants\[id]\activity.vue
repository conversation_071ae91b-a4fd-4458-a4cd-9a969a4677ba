<template>
  <div class="space-y-6 py-6" >
    <!-- Activity Overview Cards -->
 
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <UiCard
        v-for="metric in activityMetrics"
        :key="metric.key"
        class="hover:shadow-lg transition-shadow duration-200"
        :class="metric.cardClass"
      >
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">
              {{ metric.label }}
            </p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white mt-1">
              {{ metric.value }}
            </p>
            <p
              v-if="metric.change"
              :class="[
                'text-xs mt-1 flex items-center gap-1',
                metric.changeType === 'positive' ? 'text-green-600 dark:text-green-400' :
                metric.changeType === 'negative' ? 'text-red-600 dark:text-red-400' :
                'text-gray-500 dark:text-gray-400'
              ]"
            >
              <Icon
                :name="metric.changeType === 'positive' ? 'material-symbols:trending-up' :
                       metric.changeType === 'negative' ? 'material-symbols:trending-down' :
                       'material-symbols:trending-flat'"
                class="h-3 w-3"
              />
              {{ metric.change }}
            </p>
          </div>
          <div
            :class="[
              'w-12 h-12 rounded-xl flex items-center justify-center',
              metric.iconBg
            ]"
          >
            <Icon :name="metric.icon" :class="['h-6 w-6', metric.iconColor]" />
          </div>
        </div>
      </UiCard>
    </div>

    <!-- Activity Chart -->
    <UiCard>
      <template #header>
        <div class="flex items-center justify-between">
          <div>
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Activity Trends</h3>
            <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
              Activity patterns over the last {{ chartTimeRange }}
            </p>
          </div>
          <div class="flex items-center gap-2">
            <UiButton
              v-for="range in chartTimeRanges"
              :key="range.value"
              size="sm"
              :variant="chartTimeRange === range.value ? 'primary' : 'ghost'"
              @click="chartTimeRange = range.value"
            >
              {{ range.label }}
            </UiButton>
          </div>
        </div>
      </template>
      <div class="h-80">
        <UiAreaChart
          :data="activityChartData"
          :categories="activityChartCategories"
          :x-formatter="formatChartDate"
          :y-formatter="formatChartValue"
          :key="colorMode.value"
          curve="smooth"
          show-grid-lines
          show-legend
          legend-position="top"
        />
      </div>
    </UiCard>

    <!-- Search and Filters -->
    <GlobalSearchFilters
      ref="filtersRef"
      search-placeholder="Search activities by action, user, or description..."
      :filter-configs="filterConfigs"
      :advanced-filter-configs="advancedFilterConfigs"
      :show-view-toggle="true"
      :initial-search="searchQuery"
      :initial-filters="activeFilters"
      @search="handleSearch"
      @filter-change="handleFilterChange"
      @filters-change="handleFiltersChange"
      @clear-filters="handleClearFilters"
      @view-change="handleViewChange"
      @advanced-filters-toggle="handleAdvancedToggle"
    >
      <!-- Custom Export Actions -->
      <template #header-actions>
        <div class="flex items-center gap-2">
          <UiButton
            variant="outline"
            size="sm"
            @click="exportActivities"
            :loading="isExporting"
          >
            <Icon name="material-symbols:download" class="h-4 w-4 mr-2" />
            Export
          </UiButton>
          <UiButton
            variant="ghost"
            size="sm"
            @click="refreshActivities"
            :loading="isRefreshing"
          >
            <Icon name="material-symbols:refresh" class="h-4 w-4 mr-2" />
            Refresh
          </UiButton>
        </div>
      </template>
    </GlobalSearchFilters>

    <!-- Activity Content -->
    <div v-if="currentView === 'table'">
      <!-- Table View -->
      <UiTable
        :headers="tableHeaders"
        :items="filteredActivities"
        :loading="isLoading"
        item-key="id"
        :pagination="pagination"
        :selectable="true"
        @sort="handleSort"
        @selection-change="handleSelectionChange"
        @page-change="handlePageChange"
        @row-click="handleActivityClick"
      >
        <!-- Custom columns -->
        <template #cell-type="{ item }">
          <div class="flex items-center gap-2">
            <div
              :class="[
                'w-8 h-8 rounded-full flex items-center justify-center',
                getActivityIconBg(item.activityType)
              ]"
            >
              <Icon
                :name="getActivityIcon(item.activityType)"
                :class="['h-4 w-4', getActivityIconColor(item.activityType)]"
              />
            </div>
            <UiBadge
              :variant="getActivityBadgeVariant(item.activityType)"
              size="sm"
            >
              {{ formatActivityType(item.activityType) }}
            </UiBadge>
          </div>
        </template>

        <template #cell-user="{ item }">
        {{item}}
          <div v-if="item.user" class="flex items-center gap-3">
            <div class="w-8 h-8 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
              <img
                v-if="item.user.avatarUrl"
                :src="item.user.avatarUrl"
                :alt="item.user.name"
                class="w-8 h-8 rounded-full object-cover"
              />
              <Icon
                v-else
                name="material-symbols:person"
                class="h-4 w-4 text-gray-500 dark:text-gray-400"
              />
            </div>
            <div>
              <p class="text-sm font-medium text-gray-900 dark:text-white">
                {{ item.user.name }}
              </p>
              <p class="text-xs text-gray-500 dark:text-gray-400">
                {{ item.user.email }}
              </p>
            </div>
          </div>
          <span v-else class="text-sm text-gray-500 dark:text-gray-400">System</span>
        </template>

        <template #cell-createdAt="{ item }">
          <div class="text-sm">

          |{{item}}
            <p class="text-gray-900 dark:text-white">
              {{ formatDate(item.createdAt) }}
            </p>
            <p class="text-xs text-gray-500 dark:text-gray-400">
              {{ formatTime(item.createdAt) }}
            </p>
          </div>
        </template>

        <template #cell-details="{ item }">
          <div class="max-w-xs">
            <p class="text-sm text-gray-900 dark:text-white truncate">
              {{ item.description }}
            </p>
            <div v-if="item.metadata" class="flex flex-wrap gap-1 mt-1">
              <UiBadge
                v-for="(value, key) in item.metadata"
                :key="key"
                variant="neutral"
                size="xs"
              >
                {{ key }}: {{ value }}
              </UiBadge>
            </div>
          </div>
        </template>

        <template #cell-actions="{ item }">
          <div class="flex items-center gap-1">
            <UiButton
              v-if="item.actionUrl"
              size="sm"
              variant="ghost"
              @click.stop="navigateToActivity(item)"
            >
              <Icon name="material-symbols:open-in-new" class="h-4 w-4" />
            </UiButton>
            <UiDropdown>
              <template #trigger>
                <UiButton size="sm" variant="ghost">
                  <Icon name="material-symbols:more-vert" class="h-4 w-4" />
                </UiButton>
              </template>
              <template #content>
                <div class="py-1">
                  <button
                    @click="viewActivityDetails(item)"
                    class="w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                  >
                    <Icon name="material-symbols:info" class="h-4 w-4 mr-2 inline" />
                    View Details
                  </button>
                  <button
                    v-if="item.user"
                    @click="viewUserProfile(item.user)"
                    class="w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                  >
                    <Icon name="material-symbols:person" class="h-4 w-4 mr-2 inline" />
                    View User
                  </button>
                  <button
                    @click="copyActivityLink(item)"
                    class="w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                  >
                    <Icon name="material-symbols:link" class="h-4 w-4 mr-2 inline" />
                    Copy Link
                  </button>
                </div>
              </template>
            </UiDropdown>
          </div>
        </template>

        <!-- Bulk Actions -->
        <template #bulk-actions="{ selectedItems }">
          <div class="flex items-center gap-2">
            <UiButton
              size="sm"
              variant="outline"
              @click="exportSelectedActivities(selectedItems)"
            >
              <Icon name="material-symbols:download" class="h-4 w-4 mr-2" />
              Export Selected
            </UiButton>
            <UiButton
              size="sm"
              variant="outline"
              @click="markAsReviewed(selectedItems)"
            >
              <Icon name="material-symbols:check" class="h-4 w-4 mr-2" />
              Mark as Reviewed
            </UiButton>
          </div>
        </template>

        <!-- Empty State -->
        <template #empty-state>
          <div class="text-center py-12">
            <Icon name="material-symbols:timeline" class="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
              No activities found
            </h3>
            <p class="text-gray-500 dark:text-gray-400 mb-4">
              {{ hasActiveFilters ? 'Try adjusting your filters to see more activities.' : 'This tenant hasn\'t performed any activities yet.' }}
            </p>
            <UiButton
              v-if="hasActiveFilters"
              variant="outline"
              @click="clearAllFilters"
            >
              Clear Filters
            </UiButton>
          </div>
        </template>
      </UiTable>
    </div>

    <!-- Timeline View -->
    <div v-else-if="currentView === 'cards'">
      <UiCard>
        <template #header>
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Activity Timeline</h3>
            <div class="flex items-center gap-2">
              <UiButton
                v-for="filter in timelineFilters"
                :key="filter.value"
                size="sm"
                :variant="timelineFilter === filter.value ? 'primary' : 'ghost'"
                @click="timelineFilter = filter.value"
              >
                {{ filter.label }}
              </UiButton>
            </div>
          </div>
        </template>

        <!-- Timeline Content -->
        <div class="relative">
          <!-- Timeline Line -->
          <div class="absolute left-6 top-0 bottom-0 w-0.5 bg-gray-200 dark:bg-gray-700"></div>

          <!-- Timeline Items -->
          <div class="space-y-6">
            <div
              v-for="(activity, index) in filteredActivities"
              :key="activity.id"
              class="relative flex items-start gap-6 group hover:bg-gray-50 dark:hover:bg-gray-800/50 rounded-lg p-4 -ml-4 transition-colors duration-200"
            >
              <!-- Timeline Dot -->
              <div class="relative z-10 flex-shrink-0">
                <div
                  :class="[
                    'w-12 h-12 rounded-full flex items-center justify-center border-4 border-white dark:border-gray-900',
                    getActivityIconBg(activity.type)
                  ]"
                >
                  <Icon
                    :name="getActivityIcon(activity.type)"
                    :class="['h-5 w-5', getActivityIconColor(activity.type)]"
                  />
                </div>
              </div>

              <!-- Activity Content -->
              <div class="flex-1 min-w-0">
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 group-hover:shadow-md transition-shadow duration-200">
                  <!-- Activity Header -->
                  <div class="flex items-start justify-between mb-4">
                    <div class="flex items-center gap-3">
                      <div>
                        <h4 class="text-base font-semibold text-gray-900 dark:text-white">
                          {{ activity.title }}
                        </h4>
                        <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                          {{ activity.description }}
                        </p>
                      </div>
                      <UiBadge
                        :variant="getActivityBadgeVariant(activity.type)"
                        size="sm"
                      >
                        {{ formatActivityType(activity.type) }}
                      </UiBadge>
                    </div>
                    <time class="text-sm text-gray-500 dark:text-gray-400 flex-shrink-0">
                      {{ $moment(activity.timestamp).format('MMM DD, HH:mm') }}
                    </time>
                  </div>

                  <!-- User Info -->
                  <div v-if="activity.user" class="flex items-center gap-3 mb-4">
                    <div class="w-8 h-8 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
                      <img
                        v-if="activity.user.avatarUrl"
                        :src="activity.user.avatarUrl"
                        :alt="activity.user.name"
                        class="w-8 h-8 rounded-full object-cover"
                      />
                      <Icon
                        v-else
                        name="material-symbols:person"
                        class="h-4 w-4 text-gray-500 dark:text-gray-400"
                      />
                    </div>
                    <div>
                      <p class="text-sm font-medium text-gray-900 dark:text-white">
                        {{ activity.user.name }}
                      </p>
                      <p class="text-xs text-gray-500 dark:text-gray-400">
                        {{ activity.user.email }}
                      </p>
                    </div>
                  </div>

                  <!-- Activity Metadata -->
                  <div v-if="activity.metadata || activity.ipAddress || activity.device" class="space-y-2 mb-4">
                    <div v-if="activity.ipAddress || activity.device" class="flex flex-wrap gap-2">
                      <UiBadge v-if="activity.ipAddress" variant="neutral" size="xs">
                        <Icon name="material-symbols:location-on" class="h-3 w-3 mr-1" />
                        {{ activity.ipAddress }}
                      </UiBadge>
                      <UiBadge v-if="activity.device" variant="neutral" size="xs">
                        <Icon name="material-symbols:devices" class="h-3 w-3 mr-1" />
                        {{ activity.device }}
                      </UiBadge>
                    </div>
                    <div v-if="activity.metadata" class="flex flex-wrap gap-1">
                      <UiBadge
                        v-for="(value, key) in activity.metadata"
                        :key="key"
                        variant="neutral"
                        size="xs"
                      >
                        {{ key }}: {{ value }}
                      </UiBadge>
                    </div>
                  </div>

                  <!-- Activity Actions -->
                  <div class="flex items-center justify-between">
                    <div class="flex items-center gap-2">
                      <UiButton
                        v-if="activity.actionUrl"
                        size="sm"
                        variant="ghost"
                        @click="navigateToActivity(activity)"
                      >
                        <Icon name="material-symbols:open-in-new" class="h-4 w-4 mr-2" />
                        View Details
                      </UiButton>
                    </div>
                    <div class="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                      <UiButton
                        size="sm"
                        variant="ghost"
                        @click="viewActivityDetails(activity)"
                      >
                        <Icon name="material-symbols:info" class="h-4 w-4" />
                      </UiButton>
                      <UiButton
                        size="sm"
                        variant="ghost"
                        @click="copyActivityLink(activity)"
                      >
                        <Icon name="material-symbols:link" class="h-4 w-4" />
                      </UiButton>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Load More Button -->
          <div v-if="hasMoreActivities" class="text-center mt-8">
            <UiButton
              @click="loadMoreActivities"
              variant="outline"
              :loading="isLoadingMore"
            >
              <Icon name="material-symbols:expand-more" class="h-4 w-4 mr-2" />
              Load More Activities
            </UiButton>
          </div>

          <!-- Empty State -->
          <div v-if="filteredActivities.length === 0" class="text-center py-12">
            <Icon name="material-symbols:timeline" class="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
              No activities found
            </h3>
            <p class="text-gray-500 dark:text-gray-400 mb-4">
              {{ hasActiveFilters ? 'Try adjusting your filters to see more activities.' : 'This tenant hasn\'t performed any activities yet.' }}
            </p>
            <UiButton
              v-if="hasActiveFilters"
              variant="outline"
              @click="clearAllFilters"
            >
              Clear Filters
            </UiButton>
          </div>
        </div>
      </UiCard>
    </div>

    <!-- Activity Details Modal -->
    <UiModal
        :show="showActivityModal"
      v-model="showActivityModal"
      :title="selectedActivity?.title || 'Activity Details'"
      size="lg"
    >
      <div v-if="selectedActivity" class="space-y-6">
        <!-- Activity Overview -->
        <div class="flex items-start gap-4">
          <div
            :class="[
              'w-12 h-12 rounded-full flex items-center justify-center',
              getActivityIconBg(selectedActivity.type)
            ]"
          >
            <Icon
              :name="getActivityIcon(selectedActivity.type)"
              :class="['h-6 w-6', getActivityIconColor(selectedActivity.type)]"
            />
          </div>
          <div class="flex-1">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
              {{ selectedActivity.title }}
            </h3>
            <p class="text-gray-600 dark:text-gray-400 mt-1">
              {{ selectedActivity.description }}
            </p>
            <div class="flex items-center gap-2 mt-2">
              <UiBadge
                :variant="getActivityBadgeVariant(selectedActivity.type)"
                size="sm"
              >
                {{ formatActivityType(selectedActivity.type) }}
              </UiBadge>
              <span class="text-sm text-gray-500 dark:text-gray-400">
                {{ $moment(selectedActivity.timestamp).format('MMMM DD, YYYY [at] HH:mm:ss') }}
              </span>
            </div>
          </div>
        </div>

        <!-- User Information -->
        <div v-if="selectedActivity.user" class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
          <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-3">User Information</h4>
          <div class="flex items-center gap-3">
            <div class="w-10 h-10 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
              <img
                v-if="selectedActivity.user.avatarUrl"
                :src="selectedActivity.user.avatarUrl"
                :alt="selectedActivity.user.name"
                class="w-10 h-10 rounded-full object-cover"
              />
              <Icon
                v-else
                name="material-symbols:person"
                class="h-5 w-5 text-gray-500 dark:text-gray-400"
              />
            </div>
            <div>
              <p class="font-medium text-gray-900 dark:text-white">
                {{ selectedActivity.user.name }}
              </p>
              <p class="text-sm text-gray-500 dark:text-gray-400">
                {{ selectedActivity.user.email }}
              </p>
            </div>
          </div>
        </div>

        <!-- Technical Details -->
        <div v-if="selectedActivity.ipAddress || selectedActivity.device || selectedActivity.metadata" class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
          <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-3">Technical Details</h4>
          <div class="space-y-2">
            <div v-if="selectedActivity.ipAddress" class="flex items-center gap-2">
              <Icon name="material-symbols:location-on" class="h-4 w-4 text-gray-500" />
              <span class="text-sm text-gray-600 dark:text-gray-400">IP Address:</span>
              <span class="text-sm font-mono text-gray-900 dark:text-white">{{ selectedActivity.ipAddress }}</span>
            </div>
            <div v-if="selectedActivity.device" class="flex items-center gap-2">
              <Icon name="material-symbols:devices" class="h-4 w-4 text-gray-500" />
              <span class="text-sm text-gray-600 dark:text-gray-400">Device:</span>
              <span class="text-sm text-gray-900 dark:text-white">{{ selectedActivity.device }}</span>
            </div>
            <div v-if="selectedActivity.metadata" class="space-y-1">
              <div
                v-for="(value, key) in selectedActivity.metadata"
                :key="key"
                class="flex items-center gap-2"
              >
                <Icon name="material-symbols:info" class="h-4 w-4 text-gray-500" />
                <span class="text-sm text-gray-600 dark:text-gray-400">{{ key }}:</span>
                <span class="text-sm text-gray-900 dark:text-white">{{ value }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="flex items-center justify-between">
          <div>
            <UiButton
              v-if="selectedActivity?.actionUrl"
              variant="outline"
              @click="navigateToActivity(selectedActivity)"
            >
              <Icon name="material-symbols:open-in-new" class="h-4 w-4 mr-2" />
              View Related Item
            </UiButton>
          </div>
          <div class="flex items-center gap-2">
            <UiButton
              variant="ghost"
              @click="copyActivityLink(selectedActivity)"
            >
              <Icon name="material-symbols:link" class="h-4 w-4 mr-2" />
              Copy Link
            </UiButton>
            <UiButton
              variant="primary"
              @click="showActivityModal = false"
            >
              Close
            </UiButton>
          </div>
        </div>
      </template>
    </UiModal>
  </div>
</template>

<script setup lang="ts">
import { PlatformRoles } from "~/app/features/auth/constants/roles"

// Composables
const userStore = useUserStore()
const tenantStore = useTenantStore()
const colorMode = useColorMode()


// Page Meta
definePageMeta({
  layout: "dashboard",
  title: "Tenant Activity",
  description: "Tenant activity and audit logs",
  pageHeaderIcon: "material-symbols:history",
  showRealTimeStatus: false,
  autoRefreshEnabled: false,
  refreshInterval: 0,
  isLoading: false,
  showActionsMenu: true,
  showKeyboardShortcuts: true,
  middleware: ["rbac"],
  roles: [PlatformRoles.SUPER_ADMIN],
  breadcrumbs: async () => {
    const tenantStore = useTenantStore();
    if (!tenantStore.selectedTenant) {
      await tenantStore.fetchTenantById(useRoute().params.id);
    }
    const currentTenant = tenantStore.selectedTenant;

    return [
      { label: "Dashboard", href: "/dashboard" },
      { label: "Tenants", href: "/dashboard/tenants" },
      { label: currentTenant?.name, href: "/dashboard/tenants/[id]" },
      { label: "Activity" },
    ];
  },
});

// Types
interface TenantActivity {
  id: string
  activityType: 'login' | 'logout' | 'document_upload' | 'document_download' | 'case_created' | 'case_updated' | 'case_closed' | 'user_invited' | 'user_activated' | 'user_deactivated' | 'settings_updated' | 'payment_processed' | 'subscription_updated' | 'security_alert' | 'backup_created' | 'data_export' | 'template_created' | 'template_updated' | 'notification_sent'
  title: string
  description: string
  createdAt: string
  user?: {
    id: string
    name: string
    email: string
    avatarUrl?: string
  }
  ipAddress?: string
  device?: string
  metadata?: Record<string, any>
  actionUrl?: string
  severity?: 'low' | 'medium' | 'high' | 'critical'
}

interface ActivityMetric {
  key: string
  label: string
  value: string | number
  change?: string
  changeType?: 'positive' | 'negative' | 'neutral'
  icon: string
  iconBg: string
  iconColor: string
  cardClass?: string
}

// Route
const route = useRoute()
const tenantId = route.params.id as string

// State
const isLoading = ref(false)
const isRefreshing = ref(false)
const isExporting = ref(false)
const isLoadingMore = ref(false)
const hasMoreActivities = ref(true)
const showActivityModal = ref(false)
const selectedActivity = ref<TenantActivity | null>(null)

// View and Filter State
const currentView = ref<'table' | 'cards'>('table')
const searchQuery = ref('')
const activeFilters = ref<Record<string, any>>({})
const chartTimeRange = ref('7d')
const timelineFilter = ref('all')

// Pagination
const currentPage = ref(1)
const itemsPerPage = ref(25)
const totalItems = ref(0)

// Sample Data (In real app, this would come from API)
const activities = ref<TenantActivity[]>([])



// Computed Properties
const activityMetrics = computed<ActivityMetric[]>(() => [])

const chartTimeRanges = [
  { label: '7D', value: '7d' },
  { label: '30D', value: '30d' },
  { label: '90D', value: '90d' }
]

const timelineFilters = [
  { label: 'All', value: 'all' },
  { label: 'Today', value: 'today' },
  { label: 'This Week', value: 'week' },
  { label: 'This Month', value: 'month' }
]

const activityChartData = computed(() => {
  // Generate sample chart data based on activities
  const days = chartTimeRange.value === '7d' ? 7 : chartTimeRange.value === '30d' ? 30 : 90
  const data = []

  for (let i = days - 1; i >= 0; i--) {
    const date = new Date()
    date.setDate(date.getDate() - i)
    const dayActivities = activities.value.filter(activity => {
      const activityDate = new Date(activity.createdAt)
      return activityDate.toDateString() === date.toDateString()
    })

    data.push({
      date: formatDate(date),
      value: dayActivities.length
    })
  }

  return data
})

const activityChartCategories = [
  { name: 'Activities', color: '#3B82F6' }
]

const filteredActivities = computed(() => {
  let filtered = [...activities.value]

  // Apply search filter
  if (searchQuery.value.trim()) {
    const query = searchQuery.value.toLowerCase().trim()
    filtered = filtered.filter(activity =>
      activity.title.toLowerCase().includes(query) ||
      activity.description.toLowerCase().includes(query) ||
      activity.user?.name.toLowerCase().includes(query) ||
      activity.user?.email.toLowerCase().includes(query) ||
      activity.type.toLowerCase().includes(query)
    )
  }

  // Apply timeline filter
  if (timelineFilter.value !== 'all') {
    const now = new Date()
    let cutoffDate: Date

    switch (timelineFilter.value) {
      case 'today':
        cutoffDate = new Date(now.getFullYear(), now.getMonth(), now.getDate())
        break
      case 'week':
        cutoffDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
        break
      case 'month':
        cutoffDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
        break
      default:
        cutoffDate = new Date(0)
    }

    filtered = filtered.filter(activity =>
      new Date(activity.timestamp) >= cutoffDate
    )
  }

  // Apply other filters
  Object.entries(activeFilters.value).forEach(([key, value]) => {
    if (value && value !== '') {
      if (key === 'type') {
        filtered = filtered.filter(activity => activity.type === value)
      } else if (key === 'severity') {
        filtered = filtered.filter(activity => activity.severity === value)
      } else if (key === 'user') {
        filtered = filtered.filter(activity => activity.user?.id === value)
      }
    }
  })

  // Sort by timestamp (newest first)
  filtered.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())

  return filtered
})

const hasActiveFilters = computed(() => {
  return searchQuery.value !== '' ||
         Object.values(activeFilters.value).some(value => value !== null && value !== undefined && value !== '') ||
         timelineFilter.value !== 'all'
})

const pagination = computed(() => ({
  currentPage: currentPage.value,
  perPage: itemsPerPage.value,
  total: totalItems.value,
  totalPages: Math.ceil(totalItems.value / itemsPerPage.value)
}))

// Filter Configurations
const filterConfigs = [
  {
    key: 'type',
    label: 'Activity Type',
    type: 'select' as const,
    options: [
      { label: 'All Types', value: '' },
      { label: 'Login/Logout', value: 'login' },
      { label: 'Document Actions', value: 'document_upload' },
      { label: 'Case Management', value: 'case_created' },
      { label: 'User Management', value: 'user_invited' },
      { label: 'Security Events', value: 'security_alert' },
      { label: 'Payments', value: 'payment_processed' }
    ]
  },
  {
    key: 'severity',
    label: 'Severity',
    type: 'select' as const,
    options: [
      { label: 'All Severities', value: '' },
      { label: 'Low', value: 'low' },
      { label: 'Medium', value: 'medium' },
      { label: 'High', value: 'high' },
      { label: 'Critical', value: 'critical' }
    ]
  }
]

const advancedFilterConfigs = [
  {
    key: 'dateRange',
    label: 'Date Range',
    type: 'dateRange' as const
  },
  {
    key: 'user',
    label: 'User',
    type: 'select' as const,
    options: [
      { label: 'All Users', value: '' },
      ...Array.from(new Set(activities.value.filter(a => a.user).map(a => a.user!)))
        .map(user => ({ label: user.name, value: user.id }))
    ]
  },
  {
    key: 'ipAddress',
    label: 'IP Address',
    type: 'text' as const,
    placeholder: 'Enter IP address...'
  }
]

// Table Configuration
const tableHeaders = [
  { key: 'type', label: 'Type', sortable: true, width: '200px' },
  { key: 'title', label: 'Activity', sortable: true },
  { key: 'user', label: 'User', sortable: false, width: '200px' },
  { key: 'timestamp', label: 'Date & Time', sortable: true, width: '150px' },
  { key: 'details', label: 'Details', sortable: false },
  { key: 'actions', label: 'Actions', sortable: false, width: '100px' }
]

// Methods
const formatChartDate = (date: string) => {
  return new Date(date).toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric'
  })
}

const formatChartValue = (value: number) => {
  return value.toString()
}

const getActivityIcon = (type: TenantActivity['type']) => {
  const icons = {
    login: 'material-symbols:login',
    logout: 'material-symbols:logout',
    document_upload: 'material-symbols:upload-file',
    document_download: 'material-symbols:download',
    case_created: 'material-symbols:gavel',
    case_updated: 'material-symbols:edit',
    case_closed: 'material-symbols:check-circle',
    user_invited: 'material-symbols:person-add',
    user_activated: 'material-symbols:person-check',
    user_deactivated: 'material-symbols:person-off',
    settings_updated: 'material-symbols:settings',
    payment_processed: 'material-symbols:payment',
    subscription_updated: 'material-symbols:subscriptions',
    security_alert: 'material-symbols:security',
    backup_created: 'material-symbols:backup',
    data_export: 'material-symbols:download',
    template_created: 'material-symbols:note-add',
    template_updated: 'material-symbols:edit-note',
    notification_sent: 'material-symbols:notifications'
  }
  return icons[type] || 'material-symbols:info'
}

const getActivityIconBg = (type: TenantActivity['type']) => {
  const backgrounds = {
    login: 'bg-green-100 dark:bg-green-900/20',
    logout: 'bg-gray-100 dark:bg-gray-900/20',
    document_upload: 'bg-blue-100 dark:bg-blue-900/20',
    document_download: 'bg-indigo-100 dark:bg-indigo-900/20',
    case_created: 'bg-purple-100 dark:bg-purple-900/20',
    case_updated: 'bg-yellow-100 dark:bg-yellow-900/20',
    case_closed: 'bg-green-100 dark:bg-green-900/20',
    user_invited: 'bg-blue-100 dark:bg-blue-900/20',
    user_activated: 'bg-green-100 dark:bg-green-900/20',
    user_deactivated: 'bg-red-100 dark:bg-red-900/20',
    settings_updated: 'bg-gray-100 dark:bg-gray-900/20',
    payment_processed: 'bg-green-100 dark:bg-green-900/20',
    subscription_updated: 'bg-blue-100 dark:bg-blue-900/20',
    security_alert: 'bg-red-100 dark:bg-red-900/20',
    backup_created: 'bg-cyan-100 dark:bg-cyan-900/20',
    data_export: 'bg-orange-100 dark:bg-orange-900/20',
    template_created: 'bg-purple-100 dark:bg-purple-900/20',
    template_updated: 'bg-yellow-100 dark:bg-yellow-900/20',
    notification_sent: 'bg-blue-100 dark:bg-blue-900/20'
  }
  return backgrounds[type] || 'bg-gray-100 dark:bg-gray-900/20'
}

const getActivityIconColor = (type: TenantActivity['type']) => {
  const colors = {
    login: 'text-green-600 dark:text-green-400',
    logout: 'text-gray-600 dark:text-gray-400',
    document_upload: 'text-blue-600 dark:text-blue-400',
    document_download: 'text-indigo-600 dark:text-indigo-400',
    case_created: 'text-purple-600 dark:text-purple-400',
    case_updated: 'text-yellow-600 dark:text-yellow-400',
    case_closed: 'text-green-600 dark:text-green-400',
    user_invited: 'text-blue-600 dark:text-blue-400',
    user_activated: 'text-green-600 dark:text-green-400',
    user_deactivated: 'text-red-600 dark:text-red-400',
    settings_updated: 'text-gray-600 dark:text-gray-400',
    payment_processed: 'text-green-600 dark:text-green-400',
    subscription_updated: 'text-blue-600 dark:text-blue-400',
    security_alert: 'text-red-600 dark:text-red-400',
    backup_created: 'text-cyan-600 dark:text-cyan-400',
    data_export: 'text-orange-600 dark:text-orange-400',
    template_created: 'text-purple-600 dark:text-purple-400',
    template_updated: 'text-yellow-600 dark:text-yellow-400',
    notification_sent: 'text-blue-600 dark:text-blue-400'
  }
  return colors[type] || 'text-gray-600 dark:text-gray-400'
}

const getActivityBadgeVariant = (type: TenantActivity['type']) => {
  const variants = {
    login: 'success',
    logout: 'neutral',
    document_upload: 'primary',
    document_download: 'info',
    case_created: 'secondary',
    case_updated: 'warning',
    case_closed: 'success',
    user_invited: 'primary',
    user_activated: 'success',
    user_deactivated: 'danger',
    settings_updated: 'neutral',
    payment_processed: 'success',
    subscription_updated: 'primary',
    security_alert: 'danger',
    backup_created: 'info',
    data_export: 'warning',
    template_created: 'secondary',
    template_updated: 'warning',
    notification_sent: 'primary'
  }
  return variants[type] || 'neutral'
}

const formatActivityType = (type: TenantActivity['type']) => {
  const labels = {
    login: 'Login',
    logout: 'Logout',
    document_upload: 'Document Upload',
    document_download: 'Document Download',
    case_created: 'Case Created',
    case_updated: 'Case Updated',
    case_closed: 'Case Closed',
    user_invited: 'User Invited',
    user_activated: 'User Activated',
    user_deactivated: 'User Deactivated',
    settings_updated: 'Settings Updated',
    payment_processed: 'Payment Processed',
    subscription_updated: 'Subscription Updated',
    security_alert: 'Security Alert',
    backup_created: 'Backup Created',
    data_export: 'Data Export',
    template_created: 'Template Created',
    template_updated: 'Template Updated',
    notification_sent: 'Notification Sent'
  }
  return labels[type] || type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
}

// Event Handlers
const handleSearch = (search: string) => {
  searchQuery.value = search
}

const handleFilterChange = (key: string, value: any) => {
  activeFilters.value[key] = value
}

const handleFiltersChange = (filters: Record<string, any>) => {
  activeFilters.value = { ...filters }
}

const handleClearFilters = () => {
  searchQuery.value = ''
  activeFilters.value = {}
  timelineFilter.value = 'all'
}

const clearAllFilters = () => {
  handleClearFilters()
}

const handleViewChange = (view: 'table' | 'cards') => {
  currentView.value = view
}

const handleAdvancedToggle = (show: boolean) => {
  // Handle advanced filters toggle
  console.log('Advanced filters toggled:', show)
}

const handleSort = (sortConfig: { field: string; direction: 'asc' | 'desc' }) => {
  // Handle table sorting
  console.log('Sort changed:', sortConfig)
}

const handleSelectionChange = (selectedItems: TenantActivity[]) => {
  // Handle table selection change
  console.log('Selection changed:', selectedItems)
}

const handlePageChange = (page: number) => {
  currentPage.value = page
}

const handleActivityClick = (activity: TenantActivity) => {
  selectedActivity.value = activity
  showActivityModal.value = true
}

const viewActivityDetails = (activity: TenantActivity) => {
  selectedActivity.value = activity
  showActivityModal.value = true
}

const navigateToActivity = (activity: TenantActivity) => {
  if (activity.actionUrl) {
    return navigateTo(activity.actionUrl)
  }
}

const viewUserProfile = (user: TenantActivity['user']) => {
  if (user) {
    return navigateTo(`/dashboard/users/${user.id}`)
  }
}

const copyActivityLink = async (activity: TenantActivity | null) => {
  if (!activity) return

  try {
    const url = `${window.location.origin}/dashboard/tenants/${tenantId}/activity?id=${activity.id}`
    await navigator.clipboard.writeText(url)
    // Show success toast (you can implement toast notification here)
    console.log('Activity link copied to clipboard')
  } catch (error) {
    console.error('Failed to copy link:', error)
  }
}

const exportActivities = async () => {
  isExporting.value = true
  try {
    // Simulate export process
    await new Promise(resolve => setTimeout(resolve, 2000))

    // In real app, this would generate and download a file
    const data = filteredActivities.value.map(activity => ({
      timestamp: activity.timestamp,
      type: formatActivityType(activity.type),
      title: activity.title,
      description: activity.description,
      user: activity.user?.name || 'System',
      userEmail: activity.user?.email || '',
      ipAddress: activity.ipAddress || '',
      device: activity.device || '',
      severity: activity.severity || 'low'
    }))

    const csv = [
      Object.keys(data[0]).join(','),
      ...data.map(row => Object.values(row).map(val => `"${val}"`).join(','))
    ].join('\n')

    const blob = new Blob([csv], { type: 'text/csv' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `tenant-${tenantId}-activities-${new Date().toISOString().split('T')[0]}.csv`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)

    console.log('Activities exported successfully')
  } catch (error) {
    console.error('Export failed:', error)
  } finally {
    isExporting.value = false
  }
}

const exportSelectedActivities = async (selectedItems: TenantActivity[]) => {
  // Similar to exportActivities but only for selected items
  console.log('Exporting selected activities:', selectedItems)
}

const markAsReviewed = async (selectedItems: TenantActivity[]) => {
  // Mark selected activities as reviewed
  console.log('Marking as reviewed:', selectedItems)
}

const refreshActivities = async () => {
  isRefreshing.value = true
  try {
    // Simulate API call to refresh data
    await new Promise(resolve => setTimeout(resolve, 1000))

    // In real app, this would fetch fresh data from API
    await fetchTenantActivities()

    console.log('Activities refreshed')
  } catch (error) {
    console.error('Refresh failed:', error)
  } finally {
    isRefreshing.value = false
  }
}

const loadMoreActivities = async () => {
  isLoadingMore.value = true
  try {
    // Simulate loading more activities
    await new Promise(resolve => setTimeout(resolve, 1000))

    // In real app, this would load more data from API
    hasMoreActivities.value = false // For demo, disable after first load

    console.log('More activities loaded')
  } catch (error) {
    console.error('Load more failed:', error)
  } finally {
    isLoadingMore.value = false
  }
}



const fetchTenantActivities = async (tenantId: string) => {
  isLoading.value = true
  try {

  
    await userStore.fetchUserActivity({ tenantId })
    await userStore.fetchUserOverviewActivity({ tenantId })


   
    currentPage.value = userStore.userActivityMeta?.page || 1
    itemsPerPage.value = userStore.userActivityMeta?.limit || 25
    totalItems.value = userStore.userActivityMeta?.total || 0
 
   
      activities.value = userStore.userActivity
     

    // For now, we're using mock data
    console.log('Fetching user activities for tenant:', tenantId)
  } catch (error) {
    console.error('Failed to fetch user activities:', error)
  } finally {
    isLoading.value = false
  }
}   

// Lifecycle
onMounted(async () => {
  await fetchTenantActivities(tenantId)
})

// Watchers
watch(chartTimeRange, () => {
  // Refetch chart data when time range changes
  console.log('Chart time range changed:', chartTimeRange.value)
})

watch(filteredActivities, (newActivities) => {
  totalItems.value = newActivities.length
}, { immediate: true })
</script>
