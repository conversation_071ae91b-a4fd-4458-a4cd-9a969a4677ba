<template>
  <div :class="gridClasses">
    <slot />
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { useFormsProps } from "~/app/shared/composables/useFormsProps";

defineOptions({
  inheritAttrs: false,
});

interface Props {
  cols?: number | string;
  gap?: string;
  responsive?: boolean;
  autoFit?: boolean;
  minWidth?: string;
  maxCols?: number;
  breakpoints?: {
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
    '2xl'?: number;
  };
  props?: any;
}

const props = withDefaults(defineProps<Props>(), {
  cols: 1,
  gap: '6',
  responsive: true,
  autoFit: false,
  minWidth: '250px',
  maxCols: 6,
  breakpoints: () => ({}),
  props: () => ({}),
});

const { props: gridProps } = useFormsProps(props);

const gridClasses = computed(() => {
  const classes = ['grid'];
  
  // Handle gap
  const gapClass = `gap-${props.gap}`;
  classes.push(gapClass);
  
  // Handle auto-fit grid
  if (props.autoFit) {
    classes.push(`grid-cols-[repeat(auto-fit,minmax(${props.minWidth},1fr))]`);
    return classes.join(' ');
  }
  
  // Handle responsive grid
  if (props.responsive && typeof props.cols === 'number') {
    const responsiveClasses = generateResponsiveClasses(props.cols, props.breakpoints);
    classes.push(...responsiveClasses);
  } else if (typeof props.cols === 'string') {
    // Custom grid classes
    classes.push(props.cols);
  } else {
    // Simple grid
    classes.push(`grid-cols-${props.cols}`);
  }
  
  return classes.join(' ');
});

function generateResponsiveClasses(baseCols: number, breakpoints: Props['breakpoints']) {
  const classes = [];
  
  // Base mobile (1 column for mobile-first approach)
  classes.push('grid-cols-1');
  
  // Small screens
  const smCols = breakpoints.sm || Math.min(baseCols, 2);
  if (smCols > 1) {
    classes.push(`sm:grid-cols-${smCols}`);
  }
  
  // Medium screens
  const mdCols = breakpoints.md || Math.min(baseCols, 3);
  if (mdCols > smCols) {
    classes.push(`md:grid-cols-${mdCols}`);
  }
  
  // Large screens
  const lgCols = breakpoints.lg || Math.min(baseCols, 4);
  if (lgCols > mdCols) {
    classes.push(`lg:grid-cols-${lgCols}`);
  }
  
  // Extra large screens
  const xlCols = breakpoints.xl || baseCols;
  if (xlCols > lgCols && xlCols <= props.maxCols) {
    classes.push(`xl:grid-cols-${xlCols}`);
  }
  
  // 2XL screens
  const xxlCols = breakpoints['2xl'] || baseCols;
  if (xxlCols > xlCols && xxlCols <= props.maxCols) {
    classes.push(`2xl:grid-cols-${xxlCols}`);
  }
  
  return classes;
}
</script>
