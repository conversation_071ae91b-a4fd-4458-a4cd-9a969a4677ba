// types/notification.d.ts

import { Recipient } from './recipient';
export interface NotificationTrigger {
  id: string;
  name: string;
  description?: string; // Optional description
  event: string; // e.g., 'CASE_CREATED', 'DOCUMENT_UPLOADED', 'DEADLINE_APPROACHING'
  channel: string; // e.g., 'email', 'sms', 'in_app'
  recipients: Recipient[]; // Array of recipient types or specific user IDs/roles
  templateId: string; // ID of the email/in-app notification template
  conditions: Record<string, any>; // JSON object for trigger-specific conditions (e.g., { caseType: 'Litigation', priority: 'High' })
  isActive: boolean;
  tenantId: string; // ID of the tenant this trigger belongs to
  createdAt: string;
  updatedAt: string;
  // Add any other fields relevant to your notification trigger model
}

export interface NotificationTriggerPayload {
  name: string;
  description?: string;
  event: string;
  channel: string;
  recipients: Recipient[];
  templateId: string;
  conditions: Record<string, any>;
  isActive: boolean;
}