<template>
  <div class="space-y-8">
    <!-- Platform Health Overview -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <!-- System Status -->
      <UiCard
        icon="material-symbols:health-and-safety"
        icon-color="green"
        :loading="isLoading"
        hover-effect
      >
        <template #header>
          <div class="flex items-center justify-between w-full">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">System Health</h3>
            <UiBadge :variant="systemHealth.status === 'healthy' ? 'success' : 'warning'">
              {{ systemHealth.status }}
            </UiBadge>
          </div>
        </template>
        <div class="space-y-3">
          <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ systemHealth.score }}%</p>
          <p class="text-sm text-gray-500 dark:text-gray-400">
            {{ systemHealth.healthyServices }}/{{ systemHealth.totalServices }} services operational
          </p>
          <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div 
              :class="[
                'h-2 rounded-full transition-all duration-300',
                systemHealth.score >= 95 ? 'bg-green-600' : 
                systemHealth.score >= 80 ? 'bg-yellow-600' : 'bg-red-600'
              ]"
              :style="{ width: `${systemHealth.score}%` }"
            ></div>
          </div>
        </div>
      </UiCard>

      <!-- Performance Metrics -->
      <UiCard
        icon="material-symbols:speed"
        icon-color="blue"
        :loading="isLoading"
        hover-effect
      >
        <template #header>
          <div class="flex items-center justify-between w-full">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Performance</h3>
            <UiBadge variant="info">{{ performance.responseTime }}ms</UiBadge>
          </div>
        </template>
        <div class="space-y-3">
          <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ performance.uptime }}%</p>
          <p class="text-sm text-gray-500 dark:text-gray-400">
            Avg response: {{ performance.responseTime }}ms
          </p>
          <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div 
              class="bg-blue-600 h-2 rounded-full transition-all duration-300"
              :style="{ width: `${performance.uptime}%` }"
            ></div>
          </div>
        </div>
      </UiCard>

      <!-- Resource Usage -->
      <UiCard
        icon="material-symbols:memory"
        icon-color="purple"
        :loading="isLoading"
        hover-effect
      >
        <template #header>
          <div class="flex items-center justify-between w-full">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Resources</h3>
            <UiBadge :variant="resources.cpuUsage > 80 ? 'warning' : 'success'">
              CPU {{ resources.cpuUsage }}%
            </UiBadge>
          </div>
        </template>
        <div class="space-y-3">
          <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ resources.memoryUsage }}%</p>
          <p class="text-sm text-gray-500 dark:text-gray-400">
            Memory usage
          </p>
          <div class="space-y-2">
            <div class="flex justify-between text-xs">
              <span>CPU</span>
              <span>{{ resources.cpuUsage }}%</span>
            </div>
            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1">
              <div 
                :class="[
                  'h-1 rounded-full transition-all duration-300',
                  resources.cpuUsage > 80 ? 'bg-red-600' : 
                  resources.cpuUsage > 60 ? 'bg-yellow-600' : 'bg-green-600'
                ]"
                :style="{ width: `${resources.cpuUsage}%` }"
              ></div>
            </div>
          </div>
        </div>
      </UiCard>

      <!-- Security Status -->
      <UiCard
        icon="material-symbols:security"
        icon-color="red"
        :loading="isLoading"
        hover-effect
      >
        <template #header>
          <div class="flex items-center justify-between w-full">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Security</h3>
            <UiBadge :variant="security.threatLevel === 'low' ? 'success' : 'warning'">
              {{ security.threatLevel }}
            </UiBadge>
          </div>
        </template>
        <div class="space-y-3">
          <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ security.score }}%</p>
          <p class="text-sm text-gray-500 dark:text-gray-400">
            {{ security.blockedThreats }} threats blocked today
          </p>
          <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div 
              class="bg-green-600 h-2 rounded-full transition-all duration-300"
              :style="{ width: `${security.score}%` }"
            ></div>
          </div>
        </div>
      </UiCard>
    </div>

    <!-- Platform Statistics -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Service Status -->
      <UiCard>
        <template #header>
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Service Status</h3>
            <UiButton @click="refreshServices" variant="ghost" size="sm">
              <Icon name="material-symbols:refresh" class="h-4 w-4" />
            </UiButton>
          </div>
        </template>
        <div class="space-y-4">
          <div
            v-for="service in services"
            :key="service.name"
            class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg"
          >
            <div class="flex items-center gap-3">
              <div :class="[
                'w-3 h-3 rounded-full',
                service.status === 'operational' ? 'bg-green-500' :
                service.status === 'degraded' ? 'bg-yellow-500' :
                'bg-red-500'
              ]"></div>
              <div>
                <p class="text-sm font-medium text-gray-900 dark:text-white">{{ service.name }}</p>
                <p class="text-xs text-gray-500 dark:text-gray-400">{{ service.description }}</p>
              </div>
            </div>
            <div class="text-right">
              <p class="text-sm text-gray-900 dark:text-white">{{ service.uptime }}%</p>
              <p class="text-xs text-gray-500 dark:text-gray-400">{{ service.responseTime }}ms</p>
            </div>
          </div>
        </div>
      </UiCard>

      <!-- Recent Alerts -->
      <UiCard>
        <template #header>
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Recent Alerts</h3>
            <UiButton @click="navigateTo('/dashboard/platform/alerts')" variant="ghost" size="sm">
              View All
            </UiButton>
          </div>
        </template>
        <div class="space-y-3">
          <div
            v-for="alert in recentAlerts"
            :key="alert.id"
            class="flex items-start gap-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg"
          >
            <div class="flex-shrink-0">
              <div :class="[
                'w-8 h-8 rounded-full flex items-center justify-center',
                alert.severity === 'critical' ? 'bg-red-100 dark:bg-red-900/20' :
                alert.severity === 'warning' ? 'bg-yellow-100 dark:bg-yellow-900/20' :
                'bg-blue-100 dark:bg-blue-900/20'
              ]">
                <Icon :name="alert.icon" :class="[
                  'h-4 w-4',
                  alert.severity === 'critical' ? 'text-red-600 dark:text-red-400' :
                  alert.severity === 'warning' ? 'text-yellow-600 dark:text-yellow-400' :
                  'text-blue-600 dark:text-blue-400'
                ]" />
              </div>
            </div>
            <div class="flex-1 min-w-0">
              <p class="text-sm text-gray-900 dark:text-white">{{ alert.message }}</p>
              <p class="text-xs text-gray-500 dark:text-gray-400">{{ alert.timestamp }}</p>
            </div>
          </div>

          <div v-if="recentAlerts.length === 0" class="text-center py-6">
            <Icon name="material-symbols:check-circle" class="h-8 w-8 text-green-500 mx-auto mb-2" />
            <p class="text-sm text-gray-500 dark:text-gray-400">No recent alerts</p>
          </div>
        </div>
      </UiCard>
    </div>

    <!-- Quick Actions -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <UiCard
        hover-effect
        clickable
        @click="navigateTo('/dashboard/platform/health')"
        class="group cursor-pointer"
      >
        <div class="text-center p-6">
          <div class="w-16 h-16 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-200">
            <Icon name="material-symbols:monitor-heart" class="h-8 w-8 text-green-600 dark:text-green-400" />
          </div>
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">System Health</h3>
          <p class="text-sm text-gray-500 dark:text-gray-400">Monitor system performance</p>
        </div>
      </UiCard>

      <UiCard
        hover-effect
        clickable
        @click="navigateTo('/dashboard/platform/audit-logs')"
        class="group cursor-pointer"
      >
        <div class="text-center p-6">
          <div class="w-16 h-16 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-200">
            <Icon name="material-symbols:fact-check" class="h-8 w-8 text-blue-600 dark:text-blue-400" />
          </div>
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Audit Logs</h3>
          <p class="text-sm text-gray-500 dark:text-gray-400">Review system activity</p>
        </div>
      </UiCard>

      <UiCard
        hover-effect
        clickable
        @click="navigateTo('/dashboard/platform/settings')"
        class="group cursor-pointer"
      >
        <div class="text-center p-6">
          <div class="w-16 h-16 bg-purple-100 dark:bg-purple-900/20 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-200">
            <Icon name="material-symbols:settings" class="h-8 w-8 text-purple-600 dark:text-purple-400" />
          </div>
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Settings</h3>
          <p class="text-sm text-gray-500 dark:text-gray-400">Configure platform settings</p>
        </div>
      </UiCard>

      <UiCard
        hover-effect
        clickable
        @click="navigateTo('/dashboard/platform/analytics/usage')"
        class="group cursor-pointer"
      >
        <div class="text-center p-6">
          <div class="w-16 h-16 bg-yellow-100 dark:bg-yellow-900/20 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-200">
            <Icon name="material-symbols:analytics" class="h-8 w-8 text-yellow-600 dark:text-yellow-400" />
          </div>
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Analytics</h3>
          <p class="text-sm text-gray-500 dark:text-gray-400">View platform analytics</p>
        </div>
      </UiCard>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { PlatformRoles } from '~/app/features/auth/constants/roles'

// Page meta configuration
definePageMeta({
  layout: 'dashboard',
  title: 'Platform Overview',
  description: 'Comprehensive overview of platform health, performance, and system status',
  pageHeaderIcon: 'material-symbols:dashboard',
  pageHeaderStats: [
    { key: 'health', label: 'System Health', value: '98%', color: 'green' },
    { key: 'uptime', label: 'Uptime', value: '99.9%', color: 'blue' },
    { key: 'services', label: 'Services', value: '12/12', color: 'purple' },
    { key: 'alerts', label: 'Active Alerts', value: '0', color: 'yellow' }
  ],
  showRealTimeStatus: true,
  autoRefreshEnabled: true,
  refreshInterval: 30000, // 30 seconds
  middleware: ['rbac'],
  roles: [PlatformRoles.SUPER_ADMIN],
  breadcrumb: [
    { label: 'Dashboard', href: '/dashboard' },
    { label: 'Platform', href: '/dashboard/platform' },
    { label: 'Overview' },
  ],
})

// Reactive state
const isLoading = ref(true)
const refreshInterval = ref<NodeJS.Timeout | null>(null)

// Mock system metrics (in real app, these would come from monitoring APIs)
const systemHealth = ref({
  status: 'healthy',
  score: 98,
  healthyServices: 12,
  totalServices: 12
})

const performance = ref({
  uptime: 99.9,
  responseTime: 145
})

const resources = ref({
  cpuUsage: 45,
  memoryUsage: 62,
  diskUsage: 38
})

const security = ref({
  score: 96,
  threatLevel: 'low',
  blockedThreats: 23
})

// Services status
const services = ref([
  {
    name: 'API Gateway',
    description: 'Core API services',
    status: 'operational',
    uptime: 99.9,
    responseTime: 120
  },
  {
    name: 'Database Cluster',
    description: 'Primary database services',
    status: 'operational',
    uptime: 99.8,
    responseTime: 45
  },
  {
    name: 'File Storage',
    description: 'Document storage service',
    status: 'operational',
    uptime: 99.7,
    responseTime: 89
  },
  {
    name: 'Email Service',
    description: 'Notification delivery',
    status: 'operational',
    uptime: 99.5,
    responseTime: 234
  },
  {
    name: 'Search Engine',
    description: 'Document search indexing',
    status: 'operational',
    uptime: 98.9,
    responseTime: 156
  },
  {
    name: 'Authentication',
    description: 'User authentication service',
    status: 'operational',
    uptime: 99.9,
    responseTime: 78
  }
])

// Recent alerts
const recentAlerts = ref([
  {
    id: 1,
    message: 'High memory usage detected on server cluster-02',
    severity: 'warning',
    icon: 'material-symbols:warning',
    timestamp: '15 minutes ago'
  },
  {
    id: 2,
    message: 'Backup completed successfully for all tenant data',
    severity: 'info',
    icon: 'material-symbols:backup',
    timestamp: '2 hours ago'
  },
  {
    id: 3,
    message: 'Security scan completed - no vulnerabilities found',
    severity: 'info',
    icon: 'material-symbols:security',
    timestamp: '4 hours ago'
  }
])

// Methods
const refreshServices = async () => {
  try {
    isLoading.value = true

    // Simulate API call to refresh service status
    await new Promise(resolve => setTimeout(resolve, 1000))

    // In a real app, you would fetch fresh data from monitoring APIs
    console.log('Services refreshed')

  } catch (error) {
    console.error('Error refreshing services:', error)
  } finally {
    isLoading.value = false
  }
}

const fetchSystemMetrics = async () => {
  try {
    // In a real app, fetch from monitoring APIs
    // const metrics = await $api.get('/platform/metrics')

    // Simulate some variation in metrics
    systemHealth.value.score = Math.max(95, Math.min(100, systemHealth.value.score + (Math.random() - 0.5) * 2))
    performance.value.responseTime = Math.max(100, Math.min(200, performance.value.responseTime + (Math.random() - 0.5) * 20))
    resources.value.cpuUsage = Math.max(20, Math.min(80, resources.value.cpuUsage + (Math.random() - 0.5) * 10))
    resources.value.memoryUsage = Math.max(30, Math.min(90, resources.value.memoryUsage + (Math.random() - 0.5) * 10))

  } catch (error) {
    console.error('Error fetching system metrics:', error)
  }
}

// Lifecycle hooks
onMounted(async () => {
  // Initial data load
  await fetchSystemMetrics()
  isLoading.value = false

  // Set up auto-refresh interval
  refreshInterval.value = setInterval(fetchSystemMetrics, 30000)
})

onUnmounted(() => {
  if (refreshInterval.value) {
    clearInterval(refreshInterval.value)
  }
})
</script>

<style scoped>
/* Enhanced animations and transitions */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* Card hover effects */
.group:hover .group-hover\:scale-110 {
  transform: scale(1.1);
}

/* Progress bar animations */
.transition-all {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Status indicator pulse */
@keyframes pulse-dot {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.bg-green-500 {
  animation: pulse-dot 2s infinite;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .grid-cols-1 {
    gap: 1rem;
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
</style>
