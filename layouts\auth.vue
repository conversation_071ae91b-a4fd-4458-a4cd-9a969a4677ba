<template>
    <!-- Enhanced Cover Style Auth Layout -->
    <div class="min-h-screen flex relative overflow-hidden">
        <!-- Enhanced Left Side - Brand Cover -->
        <div class="hidden lg:flex lg:w-1/2 bg-gradient-to-br from-brandPrimary-600 via-brandPrimary-700 to-brandPrimary-800 relative overflow-hidden">
            <!-- Animated Background Pattern -->
            <div class="absolute inset-0 opacity-10">
                <div class="absolute inset-0 animate-float" style="background-image: radial-gradient(circle at 1px 1px, rgba(255, 255, 255, 0.3) 1px, transparent 0); background-size: 20px 20px;"></div>
            </div>

            <!-- Enhanced Decorative Elements -->
            <div class="absolute bottom-0 left-0 w-96 h-96 bg-gray-500/20 rounded-full blur-3xl transform -translate-x-1/2 translate-y-1/2 animate-pulse-glow"></div>
            <div class="absolute top-0 right-0 w-64 h-64 bg-gray-400/20 rounded-full blur-2xl transform translate-x-1/2 -translate-y-1/2 animate-float"></div>
            <div class="absolute top-1/2 left-1/4 w-32 h-32 bg-gray-600/10 rounded-full blur-xl animate-bounce-slow"></div>

            <!-- Floating Particles -->
            <div class="absolute inset-0 overflow-hidden">
                <div class="particle particle-1"></div>
                <div class="particle particle-2"></div>
                <div class="particle particle-3"></div>
                <div class="particle particle-4"></div>
                <div class="particle particle-5"></div>
            </div>

            <!-- Enhanced Content -->
            <div class="relative z-10 flex flex-col justify-center px-12 py-12 text-white">
                <div class=" animate-fade-in-up">
                    <!-- Enhanced Logo -->
                    <div class="mb-8 animate-fade-in-scale">
                    <Icon name="heroicons:scale" size="calc(var(--spacing) * 20)" class=" text-gray-50" />
                        <!-- <img
                            class="h-14 w-auto transition-transform duration-300 hover:scale-105"
                            src="/logos/legal-saas-logo-white.svg"
                            alt="Legal SaaS Platform"
                            onerror="this.src='/logos/legal-saas-logo-full.svg'"
                        /> -->
                    </div>

                    <!-- Enhanced Main Heading -->
                    <h1 class="text-5xl font-bold mb-6 leading-tight animate-fade-in-up animate-delay-200">
                        <span class="block text-transparent bg-clip-text bg-gradient-to-r from-white to-blue-100">
                            Legal Practice
                        </span>
                        <span class="block text-transparent bg-clip-text bg-gradient-to-r from-blue-100 to-white">
                            Management
                        </span>
                    </h1>

                    <!-- Enhanced Description -->
                    <p class="text-xl text-blue-100 mb-8 leading-relaxed animate-fade-in-up animate-delay-300">
                        The most comprehensive platform for legal professionals at scale
                    </p>

                    <!-- Enhanced Features List -->
                    <div class="mb-8 space-y-3 animate-fade-in-up animate-delay-400">
                        <div class="flex items-center text-blue-100">
                            <Icon name="material-symbols:check-circle" size="calc(var(--spacing) * 6)" class=" text-brandSuccess mr-3 flex-shrink-0" />
                            <span class="text-sm">Secure & Compliant</span>
                        </div>
                        <div class="flex items-center text-blue-100">
                            <Icon name="material-symbols:security" size="calc(var(--spacing) * 6)" class=" text-brandSuccess mr-3 flex-shrink-0" />
                            <span class="text-sm">Enterprise-Grade Security</span>
                        </div>
                        <div class="flex items-center text-blue-100">
                            <Icon name="material-symbols:support-agent" size="calc(var(--spacing) * 6)" class=" text-brandSuccess mr-3 flex-shrink-0" />
                            <span class="text-sm">24/7 Expert Support</span>
                        </div>
                    </div>

                    <!-- Enhanced CTA Button -->
                    <button class="group inline-flex items-center px-8 py-4 bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl text-white font-semibold hover:bg-white/20 hover:scale-105 transition-all duration-300 animate-fade-in-up animate-delay-500">
                        <span>Learn More</span>
                        <Icon name="material-symbols:arrow-forward" class="ml-3 h-5 w-5 transition-transform duration-300 group-hover:translate-x-1" />
                    </button>
                </div>

                <!-- Trust Indicators -->
                <div class="absolute bottom-8 left-12 right-12 animate-fade-in-up animate-delay-700">
                    <div class="flex items-center justify-between text-blue-200 text-sm">
                        <div class="flex items-center">
                            <Icon name="material-symbols:shield-lock" class="h-4 w-4 mr-2" />
                            <span>SOC 2 Compliant</span>
                        </div>
                        <div class="flex items-center">
                            <Icon name="material-symbols:verified-user" class="h-4 w-4 mr-2" />
                            <span>GDPR Ready</span>
                        </div>
                        <div class="flex items-center">
                            <Icon name="material-symbols:encrypted" class="h-4 w-4 mr-2" />
                            <span>256-bit SSL</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Enhanced Right Side - Form Area -->
        <div class="flex-1 flex flex-col justify-center py-8 px-4 sm:px-6 lg:px-20 xl:px-24 bg-gradient-to-br from-gray-50 to-white lg:bg-white relative">
            <!-- Background Pattern for Mobile -->
            <div class="lg:hidden absolute inset-0 opacity-5">
                <div class="absolute inset-0" style="background-image: radial-gradient(circle at 2px 2px, rgba(59, 130, 246, 0.3) 1px, transparent 0); background-size: 30px 30px;"></div>
            </div>

            <!-- Form Container -->
            <div class="relative z-10 mx-auto w-full ">
                <!-- Enhanced Mobile Logo -->
                <div class="lg:hidden text-center mb-8 animate-fade-in-scale">
                    <div class="inline-flex items-center justify-center w-16 h-16 bg-brandPrimary rounded-2xl mb-4 shadow-lg">
                        <img class="h-8 w-auto" src="/logos/legal-saas-logo-white.svg" alt="Legal SaaS Platform" onerror="this.src='/logos/legal-saas-logo-full.svg'" />
                    </div>
                    <h2 class="text-2xl font-bold text-gray-900 mb-2">Welcome Back</h2>
                    <p class="text-gray-600">Sign in to your account</p>
                </div>

                <!-- Enhanced Success Message -->
                <Transition name="success-slide">
                    <div v-if="successMessage"
                         class="mb-6 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-xl relative shadow-sm"
                         role="alert">
                        <div class="flex items-start">
                            <Icon name="material-symbols:check-circle" class="h-5 w-5 text-green-500 mt-0.5 mr-3 flex-shrink-0" />
                            <div class="flex-1">
                                <strong class="font-semibold">Success!</strong>
                                <p class="text-sm mt-1">{{ successMessage }}</p>
                            </div>
                            <button @click="clearSuccess"
                                    class="ml-3 flex-shrink-0 p-1 rounded-lg hover:bg-green-100 transition-colors duration-200">
                                <Icon name="material-symbols:close" class="h-4 w-4 text-green-500" />
                            </button>
                        </div>
                    </div>
                </Transition>

                <!-- Enhanced Error Display -->
                <Transition name="error-slide">
                    <div v-if="authError"
                         class="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-xl relative shadow-sm animate-shake"
                         role="alert">
                        <div class="flex items-start">
                            <Icon name="material-symbols:error" class="h-5 w-5 text-red-500 mt-0.5 mr-3 flex-shrink-0" />
                            <div class="flex-1">
                                <strong class="font-semibold">Authentication Error</strong>
                                <p class="text-sm mt-1">{{ authError }}</p>
                            </div>
                            <button @click="clearError"
                                    class="ml-3 flex-shrink-0 p-1 rounded-lg hover:bg-red-100 transition-colors duration-200">
                                <Icon name="material-symbols:close" class="h-4 w-4 text-red-500" />
                            </button>
                        </div>
                    </div>
                </Transition>

                <!-- Enhanced Warning Display -->
                <Transition name="warning-slide">
                    <div v-if="warningMessage"
                         class="mb-6 bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded-xl relative shadow-sm"
                         role="alert">
                        <div class="flex items-start">
                            <Icon name="material-symbols:warning" class="h-5 w-5 text-yellow-500 mt-0.5 mr-3 flex-shrink-0" />
                            <div class="flex-1">
                                <strong class="font-semibold">Notice</strong>
                                <p class="text-sm mt-1">{{ warningMessage }}</p>
                            </div>
                            <button @click="clearWarning"
                                    class="ml-3 flex-shrink-0 p-1 rounded-lg hover:bg-yellow-100 transition-colors duration-200">
                                <Icon name="material-symbols:close" class="h-4 w-4 text-yellow-500" />
                            </button>
                        </div>
                    </div>
                </Transition>

                <!-- Page Content with Animation -->
                <div class="animate-fade-in-up animate-delay-300">
                    <NuxtPage />
                </div>

                <!-- Help Links -->
                <div class="mt-8 text-center animate-fade-in-up animate-delay-500">
                    <div class="flex items-center justify-center space-x-6 text-sm text-gray-500">
                        <NuxtLink to="/help" class="hover:text-brandPrimary transition-colors duration-200 flex items-center">
                            <Icon name="material-symbols:help" class="h-4 w-4 mr-1" />
                            Help Center
                        </NuxtLink>
                        <NuxtLink to="/privacy" class="hover:text-brandPrimary transition-colors duration-200 flex items-center">
                            <Icon name="material-symbols:privacy-tip" class="h-4 w-4 mr-1" />
                            Privacy
                        </NuxtLink>
                        <NuxtLink to="/terms" class="hover:text-brandPrimary transition-colors duration-200 flex items-center">
                            <Icon name="material-symbols:description" class="h-4 w-4 mr-1" />
                            Terms
                        </NuxtLink>
                    </div>
                </div>

                <!-- Security Badge -->
                <div class="mt-6 text-center animate-fade-in-up animate-delay-700">
                    <div class="inline-flex items-center px-3 py-2 bg-gray-100 rounded-lg text-xs text-gray-600">
                        <Icon name="material-symbols:shield-lock" class="h-4 w-4 mr-2 text-green-500" />
                        <span>Your data is protected with enterprise-grade security</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, onUnmounted, provide } from 'vue';

// Enhanced state management for notifications
const authError = ref<string | null>(null);
const successMessage = ref<string | null>(null);
const warningMessage = ref<string | null>(null);
const isLoading = ref(false);

// Auto-clear timers
let errorTimer: NodeJS.Timeout | null = null;
let successTimer: NodeJS.Timeout | null = null;
let warningTimer: NodeJS.Timeout | null = null;

// Enhanced notification methods
const clearError = () => {
    authError.value = null;
    if (errorTimer) {
        clearTimeout(errorTimer);
        errorTimer = null;
    }
};

const clearSuccess = () => {
    successMessage.value = null;
    if (successTimer) {
        clearTimeout(successTimer);
        successTimer = null;
    }
};

const clearWarning = () => {
    warningMessage.value = null;
    if (warningTimer) {
        clearTimeout(warningTimer);
        warningTimer = null;
    }
};

// Auto-clear messages after timeout
const setError = (message: string, timeout = 5000) => {
    authError.value = message;
    if (timeout > 0) {
        errorTimer = setTimeout(clearError, timeout);
    }
};

const setSuccess = (message: string, timeout = 4000) => {
    successMessage.value = message;
    if (timeout > 0) {
        successTimer = setTimeout(clearSuccess, timeout);
    }
};

const setWarning = (message: string, timeout = 6000) => {
    warningMessage.value = message;
    if (timeout > 0) {
        warningTimer = setTimeout(clearWarning, timeout);
    }
};

// Provide methods to child components
provide('authNotifications', {
    setError,
    setSuccess,
    setWarning,
    clearError,
    clearSuccess,
    clearWarning,
    isLoading
});

// Cleanup timers on unmount
onUnmounted(() => {
    if (errorTimer) clearTimeout(errorTimer);
    if (successTimer) clearTimeout(successTimer);
    if (warningTimer) clearTimeout(warningTimer);
});

// Enhanced SEO and meta configuration
useHead({
    title: 'Authentication - Legal SaaS Platform',
    meta: [
        { name: 'description', content: 'Secure access to your legal practice management platform with enterprise-grade security' },
        { name: 'robots', content: 'noindex, nofollow' },
        { name: 'viewport', content: 'width=device-width, initial-scale=1' },
        { property: 'og:title', content: 'Legal SaaS Platform - Secure Authentication' },
        { property: 'og:description', content: 'Access your legal practice management platform securely' },
        { property: 'og:type', content: 'website' },
        { name: 'theme-color', content: '#1a56db' },
    ],
    link: [
        { rel: 'preconnect', href: 'https://fonts.googleapis.com' },
        { rel: 'preconnect', href: 'https://fonts.gstatic.com', crossorigin: '' },
    ]
});
</script>

<style scoped>
/* Enhanced Cover Style Auth Layout Styles */

/* Custom backdrop blur for better browser support */
.backdrop-blur-sm {
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
}

/* Enhanced smooth transitions for all interactive elements */
* {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 200ms;
}

/* Enhanced focus styles for accessibility */
button:focus-visible,
a:focus-visible {
  outline: 2px solid var(--color-brandPrimary, #1a56db);
  outline-offset: 2px;
  border-radius: 4px;
}

/* Floating particles animation */
.particle {
  position: absolute;
  background: rgba(255, 255, 255, 0.4);
  border-radius: 50%;
  pointer-events: none;
}

.particle-1 {
  width: 4px;
  height: 4px;
  top: 20%;
  left: 20%;
  animation: float-particle 6s ease-in-out infinite;
}

.particle-2 {
  width: 6px;
  height: 6px;
  top: 60%;
  left: 80%;
  animation: float-particle 8s ease-in-out infinite reverse;
}

.particle-3 {
  width: 3px;
  height: 3px;
  top: 80%;
  left: 30%;
  animation: float-particle 7s ease-in-out infinite;
  animation-delay: 2s;
}

.particle-4 {
  width: 5px;
  height: 5px;
  top: 30%;
  left: 70%;
  animation: float-particle 9s ease-in-out infinite reverse;
  animation-delay: 1s;
}

.particle-5 {
  width: 2px;
  height: 2px;
  top: 50%;
  left: 10%;
  animation: float-particle 5s ease-in-out infinite;
  animation-delay: 3s;
}

@keyframes float-particle {
  0%, 100% {
    transform: translateY(0px) translateX(0px);
    opacity: 0.3;
  }
  25% {
    transform: translateY(-20px) translateX(10px);
    opacity: 0.6;
  }
  50% {
    transform: translateY(-10px) translateX(-5px);
    opacity: 0.8;
  }
  75% {
    transform: translateY(-30px) translateX(15px);
    opacity: 0.4;
  }
}

@keyframes bounce-slow {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

.animate-bounce-slow {
  animation: bounce-slow 4s ease-in-out infinite;
}

/* Enhanced notification animations */
.error-slide-enter-active,
.success-slide-enter-active,
.warning-slide-enter-active {
  transition: all 0.3s ease-out;
}

.error-slide-leave-active,
.success-slide-leave-active,
.warning-slide-leave-active {
  transition: all 0.3s ease-in;
}

.error-slide-enter-from,
.success-slide-enter-from,
.warning-slide-enter-from {
  opacity: 0;
  transform: translateY(-10px) scale(0.95);
}

.error-slide-leave-to,
.success-slide-leave-to,
.warning-slide-leave-to {
  opacity: 0;
  transform: translateY(-5px) scale(0.98);
}

/* Enhanced gradient text effect */
.bg-clip-text {
  background-clip: text;
  -webkit-background-clip: text;
}

/* Enhanced hover effects */
button:hover {
  transform: translateY(-1px);
}

button:active {
  transform: translateY(0);
}

/* Enhanced responsive adjustments */
@media (max-width: 1024px) {
  .min-h-screen {
    background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
  }

  .particle {
    display: none;
  }
}

@media (max-width: 640px) {
  .particle {
    display: none;
  }

  .text-5xl {
    font-size: 2.5rem;
    line-height: 1.2;
  }
}

/* Enhanced accessibility */
@media (prefers-reduced-motion: reduce) {
  .particle,
  .animate-float,
  .animate-pulse-glow,
  .animate-bounce-slow,
  .animate-fade-in-up,
  .animate-fade-in-scale {
    animation: none !important;
  }

  * {
    transition: none !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .bg-gradient-to-br {
    background: #1a56db;
  }

  .text-blue-100 {
    color: #ffffff;
  }

  .border-white\/20 {
    border-color: #ffffff;
  }
}

/* Dark mode enhancements */
@media (prefers-color-scheme: dark) {
  .bg-gray-50 {
    background-color: #111827;
  }

  .text-gray-900 {
    color: #f9fafb;
  }

  .text-gray-600 {
    color: #d1d5db;
  }

  .text-gray-500 {
    color: #9ca3af;
  }
}

/* Print styles */
@media print {
  .particle,
  .animate-float,
  .animate-pulse-glow {
    display: none;
  }

  .bg-gradient-to-br {
    background: #1a56db;
    color: white;
  }
}

/* Loading state styles */
.loading-overlay {
  position: absolute;
  inset: 0;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(2px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 50;
}

/* Enhanced form container */
.form-container {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Enhanced security badge */
.security-badge {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(0, 0, 0, 0.1);
}
</style>