<template>
  <div class="filters-container relative">
    <!-- Compact Mode - Horizontal Layout -->
    <div v-if="compactMode" class="flex items-center gap-3 flex-wrap">
      <!-- Quick Filters -->
      <div class="flex items-center gap-2">
        <template v-for="filter in filterConfigs" :key="filter.key">
          <UiSelect 
            :id="`compact-${filter.key}Filter`"
            v-model="filterValues[filter.key]"
            :options="filter.options"
            :placeholder="filter.placeholder"
            :class="['min-w-[120px]', filter.class]"
            size="sm"
            @update:model-value="handleFilterChange(filter.key, $event)"
          />
        </template>
      </div>

      <!-- Advanced Filters Toggle -->
      <UiButton
        v-if="showAdvancedToggle && advancedFilterConfigs.length > 0"
        @click="toggleAdvancedFilters"
        variant="ghost"
        size="md"
        :class="[
          'transition-all duration-200',
          showAdvancedFilters ? 'text-brandPrimary bg-brandPrimary/10' : 'text-gray-600 dark:text-gray-400'
        ]"
      >
        <Icon name="heroicons:adjustments-horizontal" class="h-4 w-4 mr-1" />
        Filters
        <Icon 
          :name="showAdvancedFilters ? 'heroicons:chevron-up' : 'heroicons:chevron-down'" 
          class="h-3 w-3 ml-1" 
        />
      </UiButton>

      <!-- Clear Filters -->
      <UiButton
        v-if="hasActiveFilters"
        @click="clearAllFilters"
        variant="ghost"
        size="sm"
        class="text-gray-500 hover:text-red-600 dark:text-gray-400 dark:hover:text-red-400"
      >
        <Icon name="heroicons:x-mark" class="h-4 w-4 mr-1" />
        Clear
      </UiButton>
    </div>

    <!-- Full Mode - Vertical Layout -->
    <div v-else class="space-y-4">
      <!-- Quick Filters Row -->
      <div v-if="filterConfigs.length > 0" class="flex flex-wrap gap-3">
        <template v-for="filter in filterConfigs" :key="filter.key">
          <div class="flex flex-col">
            <label 
              :for="`${filter.key}Filter`" 
              class="text-xs font-medium text-gray-700 dark:text-gray-300 mb-1"
            >
              {{ filter.placeholder }}
            </label>
            <UiSelect 
              :id="`${filter.key}Filter`"
              v-model="filterValues[filter.key]"
              :options="filter.options"
              :placeholder="filter.placeholder"
              :class="['min-w-[140px]', filter.class]"
              @update:model-value="handleFilterChange(filter.key, $event)"
            />
          </div>
        </template>

        <!-- Advanced Toggle & Clear -->
        <div class="flex items-end gap-2">
          <UiButton
            v-if="showAdvancedToggle && advancedFilterConfigs.length > 0"
            @click="toggleAdvancedFilters"
            variant="outline"
            size="md"
            :class="[
              'transition-all duration-200',
              showAdvancedFilters ? 'border-brandPrimary text-brandPrimary bg-brandPrimary/5' : ''
            ]"
          >
            <Icon name="heroicons:adjustments-horizontal" class="h-4 w-4 mr-2" />
            Advanced Filters
            <Icon 
              :name="showAdvancedFilters ? 'heroicons:chevron-up' : 'heroicons:chevron-down'" 
              class="h-3 w-3 ml-2" 
            />
          </UiButton>

          <UiButton
            v-if="hasActiveFilters"
            @click="clearAllFilters"
            variant="ghost"
            size="sm"
            class="text-gray-500 hover:text-red-600 dark:text-gray-400 dark:hover:text-red-400"
          >
            <Icon name="heroicons:x-mark" class="h-4 w-4 mr-1" />
            Clear All
          </UiButton>
        </div>
      </div>
    </div>

    <!-- Advanced Filters Panel -->
    <Transition
      enter-active-class="transition-all duration-300 ease-out"
      enter-from-class="opacity-0 transform -translate-y-2"
      enter-to-class="opacity-100 transform translate-y-0"
      leave-active-class="transition-all duration-200 ease-in"
      leave-from-class="opacity-100 transform translate-y-0"
      leave-to-class="opacity-0 transform -translate-y-2"
    >
      <div
        v-if="showAdvancedFilters && advancedFilterConfigs.length > 0"
        class="advanced-filters-panel absolute top-full z-50 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 border-t-2 border-t-brandPrimary shadow-xl rounded-b-lg"
      >
        <div class="p-6">
          <!-- Advanced Filters Header -->
          <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
              Advanced Filters
            </h3>
            <button
              @click="toggleAdvancedFilters"
              class="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
            >
              <Icon name="heroicons:x-mark" class="h-5 w-5" />
            </button>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <template v-for="filter in advancedFilterConfigs" :key="filter.key">
              <!-- Date Range Filter -->
              <div v-if="filter.type === 'dateRange'" class="space-y-2">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  {{ filter.label }}
                </label>
                <div class="grid grid-cols-2 gap-2">
                  <input
                    v-model="filterValues[`${filter.key}_start`]"
                    type="date"
                    :placeholder="filter.minPlaceholder || 'Start Date'"
                    class="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-sm focus:ring-2 focus:ring-brandPrimary focus:border-brandPrimary dark:bg-gray-800 dark:text-white"
                    @change="handleAdvancedFilterChange(filter.key, 'dateRange')"
                  />
                  <input
                    v-model="filterValues[`${filter.key}_end`]"
                    type="date"
                    :placeholder="filter.maxPlaceholder || 'End Date'"
                    class="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-sm focus:ring-2 focus:ring-brandPrimary focus:border-brandPrimary dark:bg-gray-800 dark:text-white"
                    @change="handleAdvancedFilterChange(filter.key, 'dateRange')"
                  />
                </div>
              </div>

              <!-- Number Range Filter -->
              <div v-else-if="filter.type === 'numberRange'" class="space-y-2">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  {{ filter.label }}
                </label>
                <div class="grid grid-cols-2 gap-2">
                  <input
                    v-model.number="filterValues[`${filter.key}_min`]"
                    type="number"
                    :placeholder="filter.minPlaceholder || 'Min'"
                    class="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-sm focus:ring-2 focus:ring-brandPrimary focus:border-brandPrimary dark:bg-gray-800 dark:text-white"
                    @input="handleAdvancedFilterChange(filter.key, 'numberRange')"
                  />
                  <input
                    v-model.number="filterValues[`${filter.key}_max`]"
                    type="number"
                    :placeholder="filter.maxPlaceholder || 'Max'"
                    class="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-sm focus:ring-2 focus:ring-brandPrimary focus:border-brandPrimary dark:bg-gray-800 dark:text-white"
                    @input="handleAdvancedFilterChange(filter.key, 'numberRange')"
                  />
                </div>
              </div>

              <!-- Select Filter -->
              <div v-else-if="filter.type === 'select'" class="space-y-2">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  {{ filter.label }}
                </label>
                <UiSelect 
                  :id="`advanced${filter.key}Filter`"
                  v-model="filterValues[filter.key]"
                  :options="filter.options"
                  :placeholder="filter.placeholder"
                  @update:model-value="handleFilterChange(filter.key, $event)"
                />
              </div>

              <!-- Custom Slot for Advanced Filters -->
              <div v-else-if="filter.type === 'custom'" class="space-y-2">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  {{ filter.label }}
                </label>
                <slot :name="`advanced-filter-${filter.key}`" :filter="filter" :value="filterValues[filter.key]" />
              </div>
            </template>
          </div>
        </div>
      </div>
    </Transition>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'

interface FilterOption {
  label: string
  value: string | number
}

interface FilterConfig {
  key: string
  options: FilterOption[]
  placeholder: string
  class?: string
}

interface AdvancedFilterConfig {
  key: string
  type: 'dateRange' | 'numberRange' | 'select' | 'custom'
  label: string
  placeholder?: string
  minPlaceholder?: string
  maxPlaceholder?: string
  options?: FilterOption[]
}

interface Props {
  filterConfigs?: FilterConfig[]
  advancedFilterConfigs?: AdvancedFilterConfig[]
  initialFilters?: Record<string, any>
  showAdvancedToggle?: boolean
  compactMode?: boolean
}

interface Emits {
  (e: 'filter-change', data: { key: string; value: any }): void
  (e: 'filters-change', filters: Record<string, any>): void
  (e: 'clear-filters'): void
  (e: 'advanced-filters-toggle', show: boolean): void
}

const props = withDefaults(defineProps<Props>(), {
  filterConfigs: () => [],
  advancedFilterConfigs: () => [],
  initialFilters: () => ({}),
  showAdvancedToggle: true,
  compactMode: false
})

const emit = defineEmits<Emits>()

// State
const filterValues = ref<Record<string, any>>({ ...props.initialFilters })
const showAdvancedFilters = ref(false)

// Computed
const hasActiveFilters = computed(() => {
  return Object.values(filterValues.value).some(value => 
    value !== null && value !== undefined && value !== ''
  )
})

// Methods
const handleFilterChange = (key: string, value: any) => {
  filterValues.value[key] = value
  emit('filter-change', { key, value })
  emit('filters-change', { ...filterValues.value })
}

const handleAdvancedFilterChange = (key: string, type: 'dateRange' | 'numberRange') => {
  if (type === 'dateRange') {
    const startValue = filterValues.value[`${key}_start`]
    const endValue = filterValues.value[`${key}_end`]
    const rangeValue = startValue && endValue ? { start: startValue, end: endValue } : null
    emit('filter-change', { key, value: rangeValue })
  } else if (type === 'numberRange') {
    const minValue = filterValues.value[`${key}_min`]
    const maxValue = filterValues.value[`${key}_max`]
    const rangeValue = (minValue !== undefined && minValue !== '') || (maxValue !== undefined && maxValue !== '') 
      ? { min: minValue, max: maxValue } : null
    emit('filter-change', { key, value: rangeValue })
  }
  emit('filters-change', { ...filterValues.value })
}

const clearAllFilters = () => {
  filterValues.value = {}
  emit('clear-filters')
  emit('filters-change', {})
}

const toggleAdvancedFilters = () => {
  showAdvancedFilters.value = !showAdvancedFilters.value
  emit('advanced-filters-toggle', showAdvancedFilters.value)
}

// Exposed methods
defineExpose({
  clearFilters: clearAllFilters,
  setFilter: (key: string, value: any) => {
    filterValues.value[key] = value
    emit('filter-change', { key, value })
    emit('filters-change', { ...filterValues.value })
  },
  toggleAdvanced: toggleAdvancedFilters
})

// Watch for external filter changes
watch(() => props.initialFilters, (newFilters) => {
  filterValues.value = { ...newFilters }
}, { deep: true })
</script>

<style scoped>
.filters-container {
  width: 100%;
  position: relative;
}

/* Advanced filters panel styling */
.advanced-filters-panel {
  /* Ensure panel extends to full content width */
  left: calc(-1 * var(--page-padding, 1.5rem));
  right: calc(-1 * var(--page-padding, 1.5rem));
  margin-left: 0;
  margin-right: 0;
  /* Add backdrop blur effect */
  backdrop-filter: blur(1px);
}

/* Custom scrollbar for advanced filters */
.overflow-hidden {
  scrollbar-width: thin;
  scrollbar-color: var(--color-gray-400) var(--color-gray-100);
}

.overflow-hidden::-webkit-scrollbar {
  width: 6px;
}

.overflow-hidden::-webkit-scrollbar-track {
  background-color: var(--color-gray-100);
}

.dark .overflow-hidden::-webkit-scrollbar-track {
  background-color: var(--color-gray-800);
}

.overflow-hidden::-webkit-scrollbar-thumb {
  background-color: var(--color-gray-400);
  border-radius: 9999px;
}

.dark .overflow-hidden::-webkit-scrollbar-thumb {
  background-color: var(--color-gray-600);
}

.overflow-hidden::-webkit-scrollbar-thumb:hover {
  background-color: var(--color-gray-500);
}

.dark .overflow-hidden::-webkit-scrollbar-thumb:hover {
  background-color: var(--color-gray-500);
}
</style>
