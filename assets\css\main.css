/* Enhanced Legal SaaS Frontend Styles - Main CSS Entry Point */

/* Import all CSS modules */
@import "./tailwind.css";
@import "./animations.css";
@import "./utilities.css";

/* RTL/LTR Direction Support */
html[dir="rtl"] {
  direction: rtl;
}

html[dir="ltr"] {
  direction: ltr;
}

/* Body classes for direction */
body.rtl {
  direction: rtl;
}

body.ltr {
  direction: ltr;
}

/* CSS Custom Properties for direction */
:root {
  --text-direction: ltr;
}

html[dir="rtl"] {
  --text-direction: rtl;
}
@import "./public-enhancements.css";
@import "./rtl.css";

/* ============================================================================
   CSS CUSTOM PROPERTIES (DESIGN TOKENS)
   ============================================================================ */

:root {
  /* Enhanced Menu Design Variables */
  --color-menu-bg: #ffffff;
  --color-menu-bg-mobile: rgba(255, 255, 255, 0.95);
  --color-menu-border: #f3f4f6;
  --color-menu-text: #374151;
  --color-menu-text-hover: #111827;
  --color-menu-active-bg: rgba(26, 86, 219, 0.1);
  --color-menu-active-text: #1a56db;
  --color-menu-hover-bg: rgba(249, 250, 251, 0.8);

  /* Enhanced Shadow Variables */
  --shadow-menu: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-menu-mobile: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-dropdown: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* Animation Variables */
  --transition-menu: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-menu-fast: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

  /* Enhanced Component Design Tokens */

  /* Button Design Tokens */
  --button-border-radius: 0.75rem;
  --button-border-radius-sm: 0.5rem;
  --button-border-radius-lg: 1rem;
  --button-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --button-shadow-hover: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --button-shadow-focus: 0 0 0 3px rgba(var(--color-brandPrimary-rgb), 0.1);

  /* Input Design Tokens */
  --input-border-radius: 0.75rem;
  --input-border-width: 1px;
  --input-border-color: #d1d5db;
  --input-border-color-focus: var(--color-brandPrimary);
  --input-border-color-error: var(--color-brandDanger);
  --input-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --input-shadow-focus: 0 0 0 3px rgba(var(--color-brandPrimary-rgb), 0.1);
  --input-shadow-error: 0 0 0 3px rgba(var(--color-brandDanger-rgb), 0.1);

  /* Card Design Tokens */
  --card-border-radius: 1rem;
  --card-border-radius-sm: 0.75rem;
  --card-border-radius-lg: 1.25rem;
  --card-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --card-shadow-hover: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --card-shadow-elevated: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* Modal Design Tokens */
  --modal-backdrop: rgba(0, 0, 0, 0.5);
  --modal-border-radius: 1rem;
  --modal-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

  /* Tooltip Design Tokens */
  --tooltip-bg: #1f2937;
  --tooltip-text: #ffffff;
  --tooltip-border-radius: 0.5rem;
  --tooltip-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

  /* Badge Design Tokens */
  --badge-border-radius: 9999px;
  --badge-border-radius-square: 0.25rem;
  --badge-font-weight: 500;

  /* Table Design Tokens */
  --table-border-color: #e5e7eb;
  --table-header-bg: #f9fafb;
  --table-row-hover-bg: #f9fafb;
  --table-row-selected-bg: rgba(var(--color-brandPrimary-rgb), 0.05);

  /* Spacing Design Tokens */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  --spacing-3xl: 4rem;

  /* Typography Design Tokens */
  --font-family-sans: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  --font-family-mono: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  /* Line Height Tokens */
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;

  /* Letter Spacing Tokens */
  --letter-spacing-tight: -0.025em;
  --letter-spacing-normal: 0em;
  --letter-spacing-wide: 0.025em;

  /* Border Radius Tokens */
  --border-radius-none: 0;
  --border-radius-sm: 0.25rem;
  --border-radius-md: 0.375rem;
  --border-radius-lg: 0.5rem;
  --border-radius-xl: 0.75rem;
  --border-radius-2xl: 1rem;
  --border-radius-3xl: 1.5rem;
  --border-radius-full: 9999px;

  /* Z-Index Tokens */
  --z-index-dropdown: 1000;
  --z-index-sticky: 1020;
  --z-index-fixed: 1030;
  --z-index-modal-backdrop: 1040;
  --z-index-modal: 1050;
  --z-index-popover: 1060;
  --z-index-tooltip: 1070;
  --z-index-toast: 1080;

  /* Transition Tokens */
  --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 200ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 300ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-bounce: 500ms cubic-bezier(0.68, -0.55, 0.265, 1.55);

  /* Easing Functions */
  --ease-linear: linear;
  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);

  /* RGB Color Values for Alpha Transparency */
  --color-brandPrimary-rgb: 26, 86, 219;
  --color-brandSecondary-rgb: 6, 182, 212;
  --color-brandSuccess-rgb: 16, 185, 129;
  --color-brandDanger-rgb: 239, 68, 68;
  --color-brandWarning-rgb: 245, 158, 11;
  --color-brandInfo-rgb: 59, 130, 246;

  /* Dark Mode Color Overrides */
  --color-dark-bg-primary: #0f172a;
  --color-dark-bg-secondary: #1e293b;
  --color-dark-bg-tertiary: #334155;
  --color-dark-text-primary: #f8fafc;
  --color-dark-text-secondary: #cbd5e1;
  --color-dark-text-tertiary: #94a3b8;
  --color-dark-border: #334155;

  /* Component State Colors */
  --color-state-hover: rgba(0, 0, 0, 0.05);
  --color-state-active: rgba(0, 0, 0, 0.1);
  --color-state-focus: rgba(var(--color-brandPrimary-rgb), 0.1);
  --color-state-disabled: rgba(0, 0, 0, 0.3);

  /* Font Families */
    --font-family-calibri: 'Calibri', sans-serif;
  --font-family-times-new-roman: 'Times New Roman', serif;
  --font-family-arial: 'Arial', sans-serif;
  --font-family-verdana: 'Verdana', sans-serif;
  --font-family-helvetica: 'Helvetica', sans-serif;
  --font-family-tahoma: 'Tahoma', sans-serif;
  --font-family-georgia: 'Georgia', serif;
  --font-family-garamond: 'Garamond', serif;
  --font-family-comic-sans-ms: 'Comic Sans MS', cursive;
  --font-family-impact: 'Impact', sans-serif;
  --font-family-courier-new: 'Courier New', monospace;
  --font-family-lucida-console: 'Lucida Console', monospace;
  --font-family-symbol: 'Symbol', fantasy;
  --font-family-webdings: 'Webdings', fantasy;

}

/* ============================================================================
   GLOBAL STYLES
   ============================================================================ */

/* Base styles for the application */
html {
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-family-sans);
  line-height: var(--line-height-normal);
  color: #374151;
  background-color: #ffffff;
}

/* ============================================================================
   RTL SUPPORT
   ============================================================================ */

/* RTL Base Styles */
[dir="rtl"] {
  direction: rtl;
}

[dir="rtl"] body {
  text-align: right;
}

/* RTL Typography */
[dir="rtl"] .font-arabic {
  font-family: 'Noto Sans Arabic', 'Arial Unicode MS', sans-serif;
}

[dir="rtl"] .font-hebrew {
  font-family: 'Noto Sans Hebrew', 'Arial Hebrew', sans-serif;
}

/* RTL Layout Adjustments */
[dir="rtl"] .text-left {
  text-align: right;
}

[dir="rtl"] .text-right {
  text-align: left;
}

[dir="rtl"] .float-left {
  float: right;
}

[dir="rtl"] .float-right {
  float: left;
}

/* RTL Margin and Padding Adjustments */
[dir="rtl"] .ml-auto {
  margin-left: auto;
  margin-right: 0;
}

[dir="rtl"] .mr-auto {
  margin-right: auto;
  margin-left: 0;
}

[dir="rtl"] .pl-4 {
  padding-left: 0;
  padding-right: 1rem;
}

[dir="rtl"] .pr-4 {
  padding-right: 0;
  padding-left: 1rem;
}

/* RTL Border Radius Adjustments */
[dir="rtl"] .rounded-l-lg {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-top-right-radius: 0.5rem;
  border-bottom-right-radius: 0.5rem;
}

[dir="rtl"] .rounded-r-lg {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-top-left-radius: 0.5rem;
  border-bottom-left-radius: 0.5rem;
}

/* RTL Transform Adjustments */
[dir="rtl"] .transform {
  transform: scaleX(-1);
}

[dir="rtl"] .transform .transform-content {
  transform: scaleX(-1);
}

/* RTL Flexbox Adjustments */
[dir="rtl"] .flex-row-reverse {
  flex-direction: row;
}

[dir="rtl"] .flex-row {
  flex-direction: row-reverse;
}

/* RTL Position Adjustments */
[dir="rtl"] .left-0 {
  left: auto;
  right: 0;
}

[dir="rtl"] .right-0 {
  right: auto;
  left: 0;
}

[dir="rtl"] .left-4 {
  left: auto;
  right: 1rem;
}

[dir="rtl"] .right-4 {
  right: auto;
  left: 1rem;
}

/* RTL Shadow Adjustments */
[dir="rtl"] .shadow-lg {
  box-shadow: -0.625rem 0.625rem 1.25rem -0.3125rem rgba(0, 0, 0, 0.1), -0.25rem 0.25rem 0.625rem -0.125rem rgba(0, 0, 0, 0.05);
}

/* RTL Animation Adjustments */
[dir="rtl"] .animate-slide-in-right {
  animation: slideInLeft 0.3s ease-out;
}

[dir="rtl"] .animate-slide-in-left {
  animation: slideInRight 0.3s ease-out;
}

@keyframes slideInLeft {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  body {
    color: var(--color-dark-text-primary);
    background-color: var(--color-dark-bg-primary);
  }
}

/* Focus styles for accessibility */
/* *:focus-visible {
  outline: 2px solid var(--color-brandPrimary);
  outline-offset: 2px;
} */

/* Smooth transitions for all elements */
* {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Component Styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  border-radius: 0.125rem;
  border: 1px solid transparent;
  transition: all 0.2s ease-in-out;
  cursor: pointer;
  text-decoration: none;
}

.btn:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn--primary {
  background-color: rgb(37, 99, 235);
  color: white;
}

.btn--primary:hover:not(:disabled) {
  background-color: rgb(29, 78, 216);
}

.btn--secondary {
  background-color: rgb(229, 231, 235);
  color: rgb(17, 24, 39);
}

.btn--secondary:hover:not(:disabled) {
  background-color: rgb(209, 213, 219);
}

.btn--danger {
  background-color: rgb(239, 68, 68);
  color: white;
}

.btn--danger:hover:not(:disabled) {
  background-color: rgb(220, 38, 38);
}

.btn--ghost {
  background-color: transparent;
  color: rgb(55, 65, 81);
}

.btn--ghost:hover:not(:disabled) {
  background-color: rgb(243, 244, 246);
}

.btn--xs {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
}

.btn--sm {
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
}

.btn--md {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
}

.btn--lg {
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
}

.btn--xl {
  padding: 1rem 2rem;
  font-size: 1.125rem;
}

/* Enhanced Auth Page Animations and Utilities */
.auth-fade-in {
  animation: authFadeIn 0.6s ease-out;
}

.auth-slide-up {
  animation: authSlideUp 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.auth-scale-in {
  animation: authScaleIn 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.float-animation {
  animation: float 6s ease-in-out infinite;
}

.pulse-glow {
  animation: pulseGlow 2s ease-in-out infinite;
}

.shake-error {
  animation: shakeError 0.5s ease-in-out;
}

.gradient-border {
  position: relative;
  background: linear-gradient(white, white) padding-box,
              linear-gradient(135deg, var(--color-brandPrimary), var(--color-brandSecondary)) border-box;
  border: 2px solid transparent;
}

/* Keyframe Animations */
@keyframes authFadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes authSlideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes authScaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulseGlow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(26, 86, 219, 0.1);
  }
  50% {
    box-shadow: 0 0 30px rgba(26, 86, 219, 0.2);
  }
}

@keyframes shakeError {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-5px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(5px);
  }
}

/* Enhanced Focus States */
.focus-ring {
  outline: none;
}

.focus-ring:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(26, 86, 219, 0.2), 0 0 0 4px rgba(26, 86, 219, 0.1);
}

.focus-ring-danger {
  outline: none;
}

.focus-ring-danger:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2), 0 0 0 4px rgba(239, 68, 68, 0.1);
}

/* Enhanced Button Hover Effects */
.btn-hover-lift {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-hover-lift:hover {
  transform: translateY(-1px);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Loading State Animations */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin-slow {
  animation: spin 2s linear infinite;
}

/* Enhanced Card Styles */
.card-elevated {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-elevated:hover {
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  transform: translateY(-2px);
}