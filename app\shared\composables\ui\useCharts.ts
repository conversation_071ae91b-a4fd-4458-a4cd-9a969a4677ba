/**
 * Chart Utilities Composable
 * 
 * Provides utility functions and helpers for chart components
 */

import { ref, computed, watch, type Ref } from 'vue'
import type { 
  ChartConfiguration, 
  DonutDataItem, 
  TrackerItem,
  ChartTheme,
  ColorPalette
} from '~/app/shared/components/ui/charts/types'

export interface UseChartsOptions {
  theme?: ChartTheme
  colorPalette?: string[]
  animationDuration?: number
  responsive?: boolean
}

export interface UseChartsReturn {
  // Theme management
  currentTheme: Ref<ChartTheme>
  isDarkMode: Ref<boolean>
  setTheme: (theme: ChartTheme) => void
  
  // Color utilities
  getColor: (index: number) => string
  generateColorPalette: (count: number) => string[]
  hexToRgba: (hex: string, alpha: number) => string
  
  // Data utilities
  formatValue: (value: number, options?: Intl.NumberFormatOptions) => string
  formatPercentage: (value: number, total: number) => string
  generateRandomData: (count: number, min: number, max: number) => number[]
  
  // Chart calculations
  calculateTotal: (data: DonutDataItem[]) => number
  calculatePercentages: (data: DonutDataItem[]) => Array<DonutDataItem & { percentage: number }>
  calculateProgress: (items: TrackerItem[]) => { completed: number; total: number; percentage: number }
  
  // Export utilities
  exportChartData: (data: any, filename: string, format: 'json' | 'csv') => void
  downloadChart: (chartElement: HTMLElement, filename: string) => void
  
  // Animation utilities
  createStaggeredAnimation: (delay: number) => string
  getAnimationClass: (type: 'fadeIn' | 'slideUp' | 'scaleIn') => string
}

export const useCharts = (options: UseChartsOptions = {}): UseChartsReturn => {
  // Default configuration
  const defaultColors = [
    '#3b82f6', // blue-500
    '#10b981', // emerald-500
    '#f59e0b', // amber-500
    '#ef4444', // red-500
    '#8b5cf6', // violet-500
    '#06b6d4', // cyan-500
    '#84cc16', // lime-500
    '#f97316', // orange-500
    '#ec4899', // pink-500
    '#6b7280'  // gray-500
  ]

  // State
  const currentTheme = ref<ChartTheme>(options.theme || 'auto')
  const colorPalette = ref<string[]>(options.colorPalette || defaultColors)
  
  // Computed
  const isDarkMode = computed(() => {
    if (currentTheme.value === 'auto') {
      // Check system preference or use a global dark mode state
      return process.client && window.matchMedia('(prefers-color-scheme: dark)').matches
    }
    return currentTheme.value === 'dark'
  })

  // Theme management
  const setTheme = (theme: ChartTheme) => {
    currentTheme.value = theme
  }

  // Color utilities
  const getColor = (index: number): string => {
    return colorPalette.value[index % colorPalette.value.length]
  }

  const generateColorPalette = (count: number): string[] => {
    const colors: string[] = []
    const baseColors = colorPalette.value
    
    for (let i = 0; i < count; i++) {
      if (i < baseColors.length) {
        colors.push(baseColors[i])
      } else {
        // Generate variations of existing colors
        const baseIndex = i % baseColors.length
        const baseColor = baseColors[baseIndex]
        const variation = generateColorVariation(baseColor, Math.floor(i / baseColors.length))
        colors.push(variation)
      }
    }
    
    return colors
  }

  const hexToRgba = (hex: string, alpha: number): string => {
    const r = parseInt(hex.slice(1, 3), 16)
    const g = parseInt(hex.slice(3, 5), 16)
    const b = parseInt(hex.slice(5, 7), 16)
    return `rgba(${r}, ${g}, ${b}, ${alpha})`
  }

  const generateColorVariation = (baseColor: string, variation: number): string => {
    // Simple color variation by adjusting lightness
    const hsl = hexToHsl(baseColor)
    const lightnessDelta = (variation + 1) * 0.1
    const newLightness = Math.max(0.1, Math.min(0.9, hsl.l + lightnessDelta))
    return hslToHex(hsl.h, hsl.s, newLightness)
  }

  const hexToHsl = (hex: string) => {
    const r = parseInt(hex.slice(1, 3), 16) / 255
    const g = parseInt(hex.slice(3, 5), 16) / 255
    const b = parseInt(hex.slice(5, 7), 16) / 255

    const max = Math.max(r, g, b)
    const min = Math.min(r, g, b)
    let h = 0, s = 0, l = (max + min) / 2

    if (max !== min) {
      const d = max - min
      s = l > 0.5 ? d / (2 - max - min) : d / (max + min)
      
      switch (max) {
        case r: h = (g - b) / d + (g < b ? 6 : 0); break
        case g: h = (b - r) / d + 2; break
        case b: h = (r - g) / d + 4; break
      }
      h /= 6
    }

    return { h, s, l }
  }

  const hslToHex = (h: number, s: number, l: number): string => {
    const hue2rgb = (p: number, q: number, t: number) => {
      if (t < 0) t += 1
      if (t > 1) t -= 1
      if (t < 1/6) return p + (q - p) * 6 * t
      if (t < 1/2) return q
      if (t < 2/3) return p + (q - p) * (2/3 - t) * 6
      return p
    }

    let r, g, b

    if (s === 0) {
      r = g = b = l
    } else {
      const q = l < 0.5 ? l * (1 + s) : l + s - l * s
      const p = 2 * l - q
      r = hue2rgb(p, q, h + 1/3)
      g = hue2rgb(p, q, h)
      b = hue2rgb(p, q, h - 1/3)
    }

    const toHex = (c: number) => {
      const hex = Math.round(c * 255).toString(16)
      return hex.length === 1 ? '0' + hex : hex
    }

    return `#${toHex(r)}${toHex(g)}${toHex(b)}`
  }

  // Data utilities
  const formatValue = (value: number, options: Intl.NumberFormatOptions = {}): string => {
    const defaultOptions: Intl.NumberFormatOptions = {
      notation: 'compact',
      maximumFractionDigits: 1,
      ...options
    }
    
    return new Intl.NumberFormat('en-US', defaultOptions).format(value)
  }

  const formatPercentage = (value: number, total: number): string => {
    if (total === 0) return '0%'
    const percentage = (value / total) * 100
    return `${Math.round(percentage)}%`
  }

  const generateRandomData = (count: number, min: number, max: number): number[] => {
    return Array.from({ length: count }, () => 
      Math.floor(Math.random() * (max - min + 1)) + min
    )
  }

  // Chart calculations
  const calculateTotal = (data: DonutDataItem[]): number => {
    return data.reduce((sum, item) => sum + item.value, 0)
  }

  const calculatePercentages = (data: DonutDataItem[]) => {
    const total = calculateTotal(data)
    return data.map(item => ({
      ...item,
      percentage: total > 0 ? Math.round((item.value / total) * 100) : 0
    }))
  }

  const calculateProgress = (items: TrackerItem[]) => {
    const total = items.length
    const completed = items.filter(item => item.status === 'completed').length
    const percentage = total > 0 ? (completed / total) * 100 : 0
    
    return { completed, total, percentage }
  }

  // Export utilities
  const exportChartData = (data: any, filename: string, format: 'json' | 'csv') => {
    let content: string
    let mimeType: string

    if (format === 'json') {
      content = JSON.stringify(data, null, 2)
      mimeType = 'application/json'
    } else {
      // Convert to CSV
      content = convertToCSV(data)
      mimeType = 'text/csv'
    }

    const blob = new Blob([content], { type: mimeType })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `${filename}.${format}`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
  }

  const convertToCSV = (data: any): string => {
    if (Array.isArray(data) && data.length > 0) {
      const headers = Object.keys(data[0]).join(',')
      const rows = data.map(item => 
        Object.values(item).map(value => 
          typeof value === 'string' ? `"${value}"` : value
        ).join(',')
      )
      return [headers, ...rows].join('\n')
    }
    return ''
  }

  const downloadChart = (chartElement: HTMLElement, filename: string) => {
    // This would require html2canvas or similar library
    // For now, just log the action
    console.log('Downloading chart:', filename, chartElement)
  }

  // Animation utilities
  const createStaggeredAnimation = (delay: number): string => {
    return `animation-delay: ${delay}ms`
  }

  const getAnimationClass = (type: 'fadeIn' | 'slideUp' | 'scaleIn'): string => {
    const animations = {
      fadeIn: 'animate-fade-in',
      slideUp: 'animate-slide-up',
      scaleIn: 'animate-scale-in'
    }
    return animations[type] || animations.fadeIn
  }

  // Watch for theme changes
  if (process.client) {
    watch(currentTheme, (newTheme) => {
      if (newTheme === 'auto') {
        const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
        const handleChange = () => {
          // Trigger reactivity
          currentTheme.value = 'auto'
        }
        mediaQuery.addEventListener('change', handleChange)
      }
    }, { immediate: true })
  }

  return {
    // Theme management
    currentTheme,
    isDarkMode,
    setTheme,
    
    // Color utilities
    getColor,
    generateColorPalette,
    hexToRgba,
    
    // Data utilities
    formatValue,
    formatPercentage,
    generateRandomData,
    
    // Chart calculations
    calculateTotal,
    calculatePercentages,
    calculateProgress,
    
    // Export utilities
    exportChartData,
    downloadChart,
    
    // Animation utilities
    createStaggeredAnimation,
    getAnimationClass
  }
}
