// types/auth.d.ts
import { PlatformRoles, TenantRoles } from '~/app/features/auth/constants/roles';

export interface TenantRoleAssignment {
  tenantId: string;
  roles: TenantRoles[];
}

declare module '~/types/auth' {
  export interface Credentials {
    email: string;
    password: string;
  }

  export interface User {
    id: string;
    name: string;
    email: string;
    phone?: string;
    address?: string;
    platformRoles: PlatformRoles[];
    tenantAccess: TenantRoleAssignment[]; // Array of tenant-specific role assignments
    activeTenantId?: string | null; // Optional: if you want to track the currently active tenant context
    otpEnabled?: boolean;
    // Add other user profile fields as needed
  }
}