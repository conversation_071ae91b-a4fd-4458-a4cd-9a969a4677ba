<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <!-- Loading State -->
    <div v-if="isLoading" class="flex items-center justify-center min-h-screen">
      <div class="text-center">
        <UiSpinner size="lg" />
        <p class="mt-4 text-gray-600 dark:text-gray-400">Loading template...</p>
      </div>
    </div>

    <!-- Template Not Found -->
    <div v-else-if="!template" class="flex items-center justify-center min-h-screen">
      <div class="text-center">
        <Icon name="heroicons:document-x-mark" class="w-16 h-16 text-gray-400 mx-auto mb-4" />
        <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">Template Not Found</h2>
        <p class="text-gray-600 dark:text-gray-400 mb-6">The template you're looking for doesn't exist or has been deleted.</p>
        <UiButton @click="$router.push('/dashboard/templates/document')">
          <Icon name="heroicons:arrow-left" class="w-4 h-4 mr-2" />
          Back to Templates
        </UiButton>
      </div>
    </div>

    <!-- Template Content -->
    <div v-else>
      <!-- Header -->
      <div class="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="py-6">
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-4">
                <div class="flex items-center justify-center w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-xl">
                  <Icon :name="categoryIcon" class="w-6 h-6 text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                  <div class="flex items-center space-x-3">
                    <h1 class="text-2xl font-bold text-gray-900 dark:text-white">{{ template.name }}</h1>
                    <div class="flex items-center space-x-2">
                      <span
                        :class="[
                          'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                          template.isDraft 
                            ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                            : 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                        ]"
                      >
                        <Icon 
                          :name="template.isDraft ? 'heroicons:pencil' : 'heroicons:check-circle'" 
                          class="w-3 h-3 mr-1" 
                        />
                        {{ template.isDraft ? 'Draft' : 'Published' }}
                      </span>
                      <span
                        :class="[
                          'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                          `bg-${categoryColor}-100 text-${categoryColor}-800 dark:bg-${categoryColor}-900 dark:text-${categoryColor}-200`
                        ]"
                      >
                        {{ categoryName }}
                      </span>
                    </div>
                  </div>
                  <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                    {{ template.description || 'No description provided' }}
                  </p>
                  <div class="flex items-center space-x-4 mt-2 text-xs text-gray-500 dark:text-gray-400">
                    <span>Created {{ formatDate(template.createdAt) }}</span>
                    <span>•</span>
                    <span>Updated {{ formatDate(template.updatedAt) }}</span>
                    <span>•</span>
                    <span>{{ template.usageCount || 0 }} uses</span>
                  </div>
                </div>
              </div>
              
              <div class="flex items-center space-x-3">
                <UiButton @click="duplicateTemplate" variant="outline">
                  <Icon name="heroicons:document-duplicate" class="w-4 h-4 mr-2" />
                  Duplicate
                </UiButton>
                <UiButton @click="downloadTemplate" variant="outline">
                  <Icon name="heroicons:arrow-down-tray" class="w-4 h-4 mr-2" />
                  Download
                </UiButton>
                <UiButton @click="editTemplate" class="bg-blue-600 hover:bg-blue-700">
                  <Icon name="heroicons:pencil" class="w-4 h-4 mr-2" />
                  Edit Template
                </UiButton>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Main Content -->
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
          <!-- Template Preview -->
          <div class="lg:col-span-3">
            <UiCard class="p-6">
              <div class="flex items-center justify-between mb-6">
                <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Template Preview</h2>
                <div class="flex items-center space-x-2">
                  <UiButton @click="togglePreviewMode" variant="outline" size="sm">
                    <Icon :name="previewMode === 'rendered' ? 'heroicons:code-bracket' : 'heroicons:eye'" class="w-4 h-4 mr-1" />
                    {{ previewMode === 'rendered' ? 'View Source' : 'View Rendered' }}
                  </UiButton>
                  <UiButton @click="printTemplate" variant="outline" size="sm">
                    <Icon name="heroicons:printer" class="w-4 h-4 mr-1" />
                    Print
                  </UiButton>
                </div>
              </div>

              <!-- Rendered Preview -->
              <div v-if="previewMode === 'rendered'" class="prose prose-lg max-w-none dark:prose-invert">
                <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-8 shadow-sm">
                  <div v-html="renderedContent" class="template-content"></div>
                </div>
              </div>

              <!-- Source Code Preview -->
              <div v-else class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
                <pre class="text-sm text-gray-800 dark:text-gray-200 whitespace-pre-wrap">{{ template.content }}</pre>
              </div>
            </UiCard>
          </div>

          <!-- Sidebar -->
          <div class="lg:col-span-1 space-y-6">
            <!-- Template Info -->
            <UiCard class="p-4">
              <h3 class="text-sm font-medium text-gray-900 dark:text-white mb-4">Template Information</h3>
              <div class="space-y-3">
                <div>
                  <dt class="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">Category</dt>
                  <dd class="mt-1 text-sm text-gray-900 dark:text-white">{{ categoryName }}</dd>
                </div>
                <div>
                  <dt class="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">Type</dt>
                  <dd class="mt-1 text-sm text-gray-900 dark:text-white capitalize">{{ template.type }}</dd>
                </div>
                <div>
                  <dt class="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">Status</dt>
                  <dd class="mt-1">
                    <span
                      :class="[
                        'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium',
                        template.isActive 
                          ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                          : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                      ]"
                    >
                      {{ template.isActive ? 'Active' : 'Inactive' }}
                    </span>
                  </dd>
                </div>
                <div>
                  <dt class="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">Usage Count</dt>
                  <dd class="mt-1 text-sm text-gray-900 dark:text-white">{{ template.usageCount || 0 }} times</dd>
                </div>
              </div>
            </UiCard>

            <!-- Template Variables -->
            <UiCard class="p-4" v-if="template.variables && template.variables.length > 0">
              <h3 class="text-sm font-medium text-gray-900 dark:text-white mb-4">Template Variables</h3>
              <div class="space-y-2">
                <div
                  v-for="variable in template.variables"
                  :key="variable"
                  class="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-700 rounded-lg"
                >
                  <span class="text-sm font-mono text-gray-700 dark:text-gray-300">{{ variable }}</span>
                  <UiButton @click="copyVariable(variable)" variant="ghost" size="sm">
                    <Icon name="heroicons:clipboard" class="w-3 h-3" />
                  </UiButton>
                </div>
              </div>
            </UiCard>

            <!-- Template Metadata -->
            <UiCard class="p-4" v-if="template.metadata && Object.keys(template.metadata).length > 0">
              <h3 class="text-sm font-medium text-gray-900 dark:text-white mb-4">Additional Information</h3>
              <div class="space-y-3">
                <div v-for="(value, key) in template.metadata" :key="key">
                  <dt class="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">
                    {{ formatMetadataKey(key) }}
                  </dt>
                  <dd class="mt-1 text-sm text-gray-900 dark:text-white">
                    <span v-if="typeof value === 'boolean'">
                      <Icon :name="value ? 'heroicons:check-circle' : 'heroicons:x-circle'" 
                            :class="value ? 'text-green-500' : 'text-red-500'" 
                            class="w-4 h-4" />
                    </span>
                    <span v-else>{{ value }}</span>
                  </dd>
                </div>
              </div>
            </UiCard>

            <!-- Actions -->
            <UiCard class="p-4">
              <h3 class="text-sm font-medium text-gray-900 dark:text-white mb-4">Actions</h3>
              <div class="space-y-2">
                <UiButton @click="useTemplate" class="w-full justify-start" variant="outline">
                  <Icon name="heroicons:play" class="w-4 h-4 mr-2" />
                  Use Template
                </UiButton>
                <UiButton @click="shareTemplate" class="w-full justify-start" variant="outline">
                  <Icon name="heroicons:share" class="w-4 h-4 mr-2" />
                  Share Template
                </UiButton>
                <UiButton @click="exportTemplate" class="w-full justify-start" variant="outline">
                  <Icon name="heroicons:arrow-up-tray" class="w-4 h-4 mr-2" />
                  Export Template
                </UiButton>
                <UiButton @click="showDeleteModal = true" class="w-full justify-start text-red-600 hover:text-red-700" variant="outline">
                  <Icon name="heroicons:trash" class="w-4 h-4 mr-2" />
                  Delete Template
                </UiButton>
              </div>
            </UiCard>
          </div>
        </div>
      </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <UiModal v-if="showDeleteModal" @close="showDeleteModal = false">
      <div class="p-6">
        <div class="flex items-center space-x-3 mb-4">
          <div class="w-10 h-10 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center">
            <Icon name="heroicons:exclamation-triangle" class="w-5 h-5 text-red-600 dark:text-red-400" />
          </div>
          <div>
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Delete Template</h3>
            <p class="text-sm text-gray-600 dark:text-gray-400">This action cannot be undone.</p>
          </div>
        </div>
        
        <p class="text-gray-700 dark:text-gray-300 mb-6">
          Are you sure you want to delete "<strong>{{ template?.name }}</strong>"? 
          This will permanently remove the template and all its associated data.
        </p>
        
        <div class="flex items-center justify-end space-x-3">
          <UiButton @click="showDeleteModal = false" variant="outline">Cancel</UiButton>
          <UiButton @click="deleteTemplate" class="bg-red-600 hover:bg-red-700" :disabled="isDeleting">
            <Icon name="heroicons:trash" class="w-4 h-4 mr-2" />
            {{ isDeleting ? 'Deleting...' : 'Delete Template' }}
          </UiButton>
        </div>
      </div>
    </UiModal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useTemplateStore } from '~/stores/template'
import { getTemplateCategoryById } from '~/utils/templateCategories'
import { PlatformRoles, TenantRoles } from '~/app/features/auth/constants/roles'

// Page meta
definePageMeta({
  layout: 'dashboard',
  title: 'View Document Template',
  middleware: ['rbac'],
  roles: [PlatformRoles.SUPER_ADMIN, TenantRoles.TENANT_OWNER, TenantRoles.ADMIN, TenantRoles.LAWYER],
  breadcrumb: [
    { label: 'Dashboard', href: '/dashboard' },
    { label: 'Templates', href: '/dashboard/templates' },
    { label: 'Document Templates', href: '/dashboard/templates/document' },
    { label: 'View' },
  ],
})

// Composables
const route = useRoute()
const router = useRouter()
const templateStore = useTemplateStore()

// State
const isLoading = ref(true)
const isDeleting = ref(false)
const showDeleteModal = ref(false)
const previewMode = ref<'rendered' | 'source'>('rendered')
const template = ref(null)

// Computed
const templateId = computed(() => route.params.id as string)

const selectedCategory = computed(() =>
  template.value?.category ? getTemplateCategoryById(template.value.category) : null
)

const categoryName = computed(() =>
  selectedCategory.value?.name || 'Unknown Category'
)

const categoryIcon = computed(() =>
  selectedCategory.value?.icon || 'heroicons:document-text'
)

const categoryColor = computed(() =>
  selectedCategory.value?.color || 'gray'
)

const renderedContent = computed(() => {
  if (!template.value?.content) return ''

  // Simple template rendering - replace variables with placeholder values
  let content = template.value.content

  if (template.value.variables) {
    template.value.variables.forEach(variable => {
      const placeholder = `[${variable.toUpperCase()}]`
      content = content.replace(new RegExp(`{${variable}}`, 'g'), placeholder)
    })
  }

  return content
})

// Methods
const loadTemplate = async () => {
  try {
    isLoading.value = true
    template.value = await templateStore.getTemplate(templateId.value)
  } catch (error) {
    console.error('Error loading template:', error)
    template.value = null
  } finally {
    isLoading.value = false
  }
}

const editTemplate = () => {
  router.push(`/dashboard/templates/document/${templateId.value}/edit`)
}

const duplicateTemplate = async () => {
  if (!template.value) return

  try {
    const duplicatedTemplate = await templateStore.createTemplate({
      ...template.value,
      name: `${template.value.name} (Copy)`,
      isDraft: true,
    })

    if (duplicatedTemplate) {
      router.push(`/dashboard/templates/document/${duplicatedTemplate.id}/edit`)
    }
  } catch (error) {
    console.error('Error duplicating template:', error)
  }
}

const deleteTemplate = async () => {
  if (!template.value) return

  try {
    isDeleting.value = true
    await templateStore.deleteTemplate(templateId.value)
    router.push('/dashboard/templates/document')
  } catch (error) {
    console.error('Error deleting template:', error)
  } finally {
    isDeleting.value = false
    showDeleteModal.value = false
  }
}

const downloadTemplate = () => {
  if (!template.value) return

  const dataStr = JSON.stringify(template.value, null, 2)
  const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr)

  const exportFileDefaultName = `${template.value.name.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.json`

  const linkElement = document.createElement('a')
  linkElement.setAttribute('href', dataUri)
  linkElement.setAttribute('download', exportFileDefaultName)
  linkElement.click()
}

const printTemplate = () => {
  const printWindow = window.open('', '_blank')
  if (printWindow && template.value) {
    printWindow.document.write(`
      <html>
        <head>
          <title>${template.value.name}</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 40px; }
            .header { border-bottom: 2px solid #333; padding-bottom: 20px; margin-bottom: 30px; }
            .content { line-height: 1.6; }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>${template.value.name}</h1>
            <p>${template.value.description || ''}</p>
          </div>
          <div class="content">
            ${renderedContent.value}
          </div>
        </body>
      </html>
    `)
    printWindow.document.close()
    printWindow.print()
  }
}

const togglePreviewMode = () => {
  previewMode.value = previewMode.value === 'rendered' ? 'source' : 'rendered'
}

const useTemplate = () => {
  // Navigate to document creation with this template
  router.push(`/dashboard/documents/create?template=${templateId.value}`)
}

const shareTemplate = async () => {
  if (!template.value) return

  const shareUrl = `${window.location.origin}/dashboard/templates/document/${templateId.value}`

  if (navigator.share) {
    try {
      await navigator.share({
        title: template.value.name,
        text: template.value.description || 'Check out this document template',
        url: shareUrl,
      })
    } catch (error) {
      console.log('Error sharing:', error)
    }
  } else {
    // Fallback to clipboard
    await navigator.clipboard.writeText(shareUrl)
    // Show notification that link was copied
  }
}

const exportTemplate = () => {
  downloadTemplate()
}

const copyVariable = async (variable: string) => {
  await navigator.clipboard.writeText(`{${variable}}`)
  // Show notification that variable was copied
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  })
}

const formatMetadataKey = (key: string) => {
  return key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())
}

// Lifecycle
onMounted(() => {
  loadTemplate()
})
</script>

<style scoped>
.template-content {
  @apply text-gray-900 dark:text-gray-100;
}

.template-content h1,
.template-content h2,
.template-content h3,
.template-content h4,
.template-content h5,
.template-content h6 {
  @apply font-semibold mb-4 mt-6;
}

.template-content p {
  @apply mb-4;
}

.template-content ul,
.template-content ol {
  @apply mb-4 pl-6;
}

.template-content li {
  @apply mb-2;
}

.template-content strong {
  @apply font-semibold;
}

.template-content em {
  @apply italic;
}
</style>
