/**
 * Comprehensive Composables Library
 *
 * Central export point for all composables with organized categories
 * and performance-optimized imports
 */

// ============================================================================
// CORE COMPOSABLES
// ============================================================================

// API and HTTP
export { useApi } from './core/useApi.js'
export { useAsyncData } from './core/useAsyncData.js'
export { useWebSocket } from './core/useWebSocket.js'
export { useSSE } from './core/useSSE.js'

// Search
export { useGlobalSearch } from './core/useGlobalSearch.js'
export type { UseGlobalSearchOptions, UseGlobalSearchReturn } from '../types/search.js'

// Global Properties
export { useGlobalProperties } from './core/useGlobalActions.js'
export type { GlobalPropertyValue, GlobalState, UseGlobalPropertiesReturn } from './core/useGlobalActions.js'

// Storage and Persistence
export { useLocalStorage } from './core/useLocalStorage.js'
export { useSessionStorage } from './core/useSessionStorage.js'
export { useIndexedDB } from './core/useIndexedDB.js'
export { useCache } from './core/useCache.js'

// Utilities
export { useLogger } from './core/useLogger.js'
export { useClipboard } from './core/useClipboard.js'
export { useEventListener } from './core/useEventListener.js'
export { useDebounce } from './core/useDebounce.js'
export { useThrottle } from './core/useThrottle.js'
export { useRetry } from './core/useRetry.js'
export { useEncryption } from './core/useEncryption.js'

// ============================================================================
// UI COMPOSABLES
// ============================================================================

// Layout and Navigation
export { useModal } from './ui/useModal.js'
export { useDrawer } from './ui/useDrawer.js'
export { useToast } from './ui/useToast.js'
export { useNotification } from './ui/useNotification.js'
export { useBreadcrumbs } from './ui/useBreadcrumbs.js'
export { useMenu } from './ui/useMenu.js'

// Interactions
export { useDragAndDrop } from './ui/useDragAndDrop.js'
export { useResizable } from './ui/useResizable.js'
export { useSortable } from './ui/useSortable.js'
export { useClickOutside } from './ui/useClickOutside.js'
export { useHover } from './ui/useHover.js'
export { useFocus } from './ui/useFocus.js'

// Accessibility
export { useFocusTrap } from './ui/useFocusTrap.js'
export { useScreenReader } from './ui/useScreenReader.js'
export { useKeyboardNavigation } from './ui/useKeyboardNavigation.js'
export { useAriaLive } from './ui/useAriaLive.js'

// Visual Effects
export { useAnimation } from './ui/useAnimation.js'
export { useTransition } from './ui/useTransition.js'
export { useScrollLock } from './ui/useScrollLock.js'
export { useVirtualScroll } from './ui/useVirtualScroll.js'

// Charts
export { useCharts } from './ui/useCharts.js'
export type { UseChartsOptions, UseChartsReturn } from './ui/useCharts.js'
