/**
 * Event system types and interfaces for the Legal SaaS Frontend
 * Provides type-safe event handling across the application
 */

// ============================================================================
// CORE EVENT TYPES
// ============================================================================

export interface BaseEvent {
  timestamp: string
  source?: string
  userId?: string
  tenantId?: string
  sessionId?: string
}

export interface EventPayload extends BaseEvent {
  [key: string]: any
}

// ============================================================================
// BUSINESS DOMAIN EVENTS
// ============================================================================

// Case Events
export interface CaseEvent extends BaseEvent {
  caseId: string
  caseTitle?: string
  caseType?: string
  status?: string
}

export interface CaseCreatedEvent extends CaseEvent {
  clientId: string
  assignedTo?: string[]
}

export interface CaseUpdatedEvent extends CaseEvent {
  changes: Record<string, any>
  updatedBy: string
}

export interface CaseStatusChangedEvent extends CaseEvent {
  previousStatus: string
  newStatus: string
  reason?: string
}

// Document Events
export interface DocumentEvent extends BaseEvent {
  documentId: string
  documentName?: string
  documentType?: string
  caseId?: string
}

export interface DocumentUploadedEvent extends DocumentEvent {
  fileSize: number
  mimeType: string
  uploadedBy: string
}

export interface DocumentSharedEvent extends DocumentEvent {
  sharedWith: string[]
  permissions: string[]
  sharedBy: string
}

// Template Events
export interface TemplateEvent extends BaseEvent {
  templateId: string
  templateName?: string
  templateType?: string
  category?: string
}

export interface TemplateCreatedEvent extends TemplateEvent {
  createdBy: string
  isPublic: boolean
}

export interface TemplateUsedEvent extends TemplateEvent {
  usedBy: string
  context?: string
  caseId?: string
}

// User Events
export interface UserEvent extends BaseEvent {
  targetUserId: string
  targetUserEmail?: string
  role?: string
}

export interface UserInvitedEvent extends UserEvent {
  invitedBy: string
  permissions: string[]
}

export interface UserRoleChangedEvent extends UserEvent {
  previousRole: string
  newRole: string
  changedBy: string
}

// Notification Events
export interface NotificationEvent extends BaseEvent {
  notificationId: string
  type: 'info' | 'success' | 'warning' | 'error'
  title: string
  message: string
  priority?: 'low' | 'medium' | 'high' | 'urgent'
  channels?: string[]
  recipients?: string[]
}

// ============================================================================
// UI EVENTS
// ============================================================================

export interface UIEvent extends BaseEvent {
  component?: string
  action: string
  element?: string
}

export interface NavigationEvent extends UIEvent {
  from: string
  to: string
  method: 'click' | 'keyboard' | 'programmatic'
}

export interface SearchEvent extends UIEvent {
  query: string
  filters?: Record<string, any>
  resultsCount?: number
}

export interface FilterEvent extends UIEvent {
  filterType: string
  filterValue: any
  activeFilters: Record<string, any>
}

export interface BulkActionEvent extends UIEvent {
  action: string
  itemCount: number
  itemIds: string[]
  entityType: string
}

export interface ViewModeChangedEvent extends UIEvent {
  previousView: string
  newView: string
  context?: string
}

// ============================================================================
// SYSTEM EVENTS
// ============================================================================

export interface SystemEvent extends BaseEvent {
  level: 'info' | 'warning' | 'error' | 'critical'
  category: string
  message: string
}

export interface ErrorEvent extends SystemEvent {
  error: Error | string
  stack?: string
  context?: Record<string, any>
  severity: 'low' | 'medium' | 'high' | 'critical'
}

export interface PerformanceEvent extends SystemEvent {
  metric: string
  value: number
  unit: string
  threshold?: number
}

export interface AuthEvent extends BaseEvent {
  action: 'login' | 'logout' | 'token_refresh' | 'session_expired'
  method?: string
  success: boolean
  reason?: string
}

// ============================================================================
// REAL-TIME EVENTS
// ============================================================================

export interface RealtimeEvent extends BaseEvent {
  channel: string
  eventType: string
  data: any
}

export interface WebSocketEvent extends RealtimeEvent {
  connectionId: string
  messageId: string
}

export interface ServerSentEvent extends RealtimeEvent {
  eventId: string
  retry?: number
}

// ============================================================================
// EVENT MAP FOR TYPE SAFETY
// ============================================================================

export interface EventMap {
  // Index signature to satisfy mitt constraint
  [key: string]: any

  // Case events
  'case:created': CaseCreatedEvent
  'case:updated': CaseUpdatedEvent
  'case:status-changed': CaseStatusChangedEvent
  'case:deleted': CaseEvent
  'case:assigned': CaseEvent & { assignedTo: string[], assignedBy: string }
  
  // Document events
  'document:uploaded': DocumentUploadedEvent
  'document:shared': DocumentSharedEvent
  'document:downloaded': DocumentEvent & { downloadedBy: string }
  'document:deleted': DocumentEvent
  'document:version-created': DocumentEvent & { version: string, createdBy: string }
  
  // Template events
  'template:created': TemplateCreatedEvent
  'template:updated': TemplateEvent & { updatedBy: string, changes: Record<string, any> }
  'template:used': TemplateUsedEvent
  'template:deleted': TemplateEvent
  'template:published': TemplateEvent & { publishedBy: string }
  
  // User events
  'user:invited': UserInvitedEvent
  'user:role-changed': UserRoleChangedEvent
  'user:activated': UserEvent
  'user:deactivated': UserEvent
  'user:login': AuthEvent
  'user:logout': AuthEvent
  
  // Notification events
  'notification:created': NotificationEvent
  'notification:read': NotificationEvent & { readBy: string, readAt: string }
  'notification:dismissed': NotificationEvent & { dismissedBy: string }
  
  // UI events
  'ui:navigation': NavigationEvent
  'ui:search': SearchEvent
  'ui:filter': FilterEvent
  'ui:bulk-action': BulkActionEvent
  'ui:view-changed': ViewModeChangedEvent
  'ui:modal-opened': UIEvent & { modalId: string }
  'ui:modal-closed': UIEvent & { modalId: string, reason?: string }
  
  // System events
  'system:error': ErrorEvent
  'system:performance': PerformanceEvent
  'system:maintenance': SystemEvent & { startTime: string, endTime?: string }
  
  // Real-time events
  'realtime:connected': RealtimeEvent
  'realtime:disconnected': RealtimeEvent
  'realtime:message': RealtimeEvent
  'realtime:error': RealtimeEvent & { error: string }
  
  // Generic events
  'app:ready': BaseEvent
  'app:error': ErrorEvent
  'app:theme-changed': BaseEvent & { theme: 'light' | 'dark' }
  'app:language-changed': BaseEvent & { language: string, isRTL: boolean }
}

// ============================================================================
// EVENT LISTENER TYPES
// ============================================================================

export type EventListener<T = any> = (event: T) => void | Promise<void>
export type EventUnsubscribe = () => void

export interface EventListenerOptions {
  once?: boolean
  priority?: number
  debounce?: number
  throttle?: number
}

// ============================================================================
// EVENT EMITTER INTERFACE
// ============================================================================

export interface EventEmitter {
  emit<K extends keyof EventMap>(event: K, data: EventMap[K]): void
  on<K extends keyof EventMap>(event: K, listener: EventListener<EventMap[K]>, options?: EventListenerOptions): EventUnsubscribe
  off<K extends keyof EventMap>(event: K, listener?: EventListener<EventMap[K]>): void
  once<K extends keyof EventMap>(event: K, listener: EventListener<EventMap[K]>): EventUnsubscribe
  clear(): void
  listenerCount(event?: keyof EventMap): number
}
