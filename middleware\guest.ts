import { defineNuxtRouteMiddleware, navigateTo } from '#app'
import { useAuth } from '~/composables/useAuth';
import type { RouteLocationNormalized } from 'vue-router';
 
export default defineNuxtRouteMiddleware((_to: RouteLocationNormalized, _from: RouteLocationNormalized) => {
  const { isAuthenticated } = useAuth()
 
  if (isAuthenticated.value) {
    // If the user is authenticated, redirect them to the dashboard
    // or any other appropriate authenticated route.
    if (typeof window !== 'undefined') {
      return navigateTo('/dashboard')
    }
  }
  // If the user is not authenticated, allow access to the route.
})