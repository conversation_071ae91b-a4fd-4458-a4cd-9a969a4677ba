<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <div class="container mx-auto py-8">
      <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">
          {{ t('documents.documentEditor') }} - {{ t('documents.editor.document.welcomeTitle') }}
        </h1>
        <p class="text-gray-600 dark:text-gray-400">
          Professional document editor with zoom functionality and clean design
        </p>
      </div>

      <!-- Language Switcher -->
      <div class="mb-6">
        <div class="flex items-center gap-4">
          <span class="text-sm font-medium text-gray-700 dark:text-gray-300">
            {{ t('common.language') }}:
          </span>
          <UiButton
            v-for="locale in availableLocales"
            :key="locale.code"
            @click="setLocale(locale.code)"
            :variant="currentLocale === locale.code ? 'contained' : 'outline'"
            size="sm"
          >
            {{ locale.flag }} {{ locale.nativeName }}
          </UiButton>
        </div>
      </div>

      <!-- Document Editor -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden" style="height: 800px;">
        <DocumentEditor
          v-model="documentContent"
          :editable="true"
          :document-title="documentTitle"
          :document-subtitle="documentSubtitle"
          :show-header="true"
          :show-footer="true"
          :show-zoom-controls="true"
          :show-scroll-indicators="true"
          :initial-zoom="100"
          @zoom-change="handleZoomChange"
          @view-change="handleViewChange"
          @language-change="handleLanguageChange"
        />
      </div>

      <!-- Features Overview -->
      <div class="mt-8 grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            {{ t('documents.editor.document.welcomeTitle') }}
          </h3>
          <ul class="space-y-2 text-sm text-gray-600 dark:text-gray-400">
            <li>• Professional document layout with proper margins</li>
            <li>• Built-in zoom controls for comfortable viewing</li>
            <li>• Clean, Microsoft Word-inspired interface</li>
            <li>• Print-ready document formatting</li>
            <li>• Responsive design for all screen sizes</li>
          </ul>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Multi-language Support
          </h3>
          <ul class="space-y-2 text-sm text-gray-600 dark:text-gray-400">
            <li>• Full RTL support for Hebrew and Arabic</li>
            <li>• Dynamic language switching</li>
            <li>• Contextual text direction adaptation</li>
            <li>• Professional typography for all languages</li>
            <li>• Seamless interface translation</li>
          </ul>
        </div>
      </div>

      <!-- Zoom Controls Info -->
      <div class="mt-6 bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
        <div class="flex items-start gap-3">
          <Icon name="heroicons:information-circle" class="w-5 h-5 text-blue-500 mt-0.5" />
          <div>
            <h4 class="text-sm font-medium text-blue-900 dark:text-blue-100 mb-1">
              Zoom Controls
            </h4>
            <p class="text-sm text-blue-700 dark:text-blue-200">
              Use the zoom controls in the bottom-right corner to adjust the document size. 
              You can also use keyboard shortcuts: Ctrl/Cmd + Plus to zoom in, Ctrl/Cmd + Minus to zoom out.
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import DocumentEditor from '~/components/editors/document-editor/DocumentEditor.vue'

// Composables
const { t, locale, setLocale } = useI18n()

// Available locales
const availableLocales = [
  { code: 'en', flag: '🇺🇸', nativeName: 'English' },
  { code: 'he', flag: '🇮🇱', nativeName: 'עברית' },
  { code: 'ar', flag: '🇸🇦', nativeName: 'العربية' }
]

// Current locale
const currentLocale = computed(() => locale.value)

// Document state
const documentContent = ref(`
  <div style="text-align: center; margin-bottom: 2rem;">
    <h1 style="color: #4F46E5; font-size: 2.5rem; margin-bottom: 0.5rem;">LMO</h1>
    <h2 style="color: #6B7280; font-size: 1.5rem; margin-bottom: 2rem;">Legal Management Office</h2>
  </div>

  <h1>Introduction</h1>
  
  <p>LMO Editor is an open-source document editor based on Vue3 and TipTap. It provides powerful document editing capabilities and AI-assisted creation features. LMO Editor supports pagination, Markdown syntax, rich text editing, insertion of various node types, page style settings, document export, and printing. Additionally, it supports custom extensions, multi-language settings, and a dark theme.</p>

  <p>LMO Editor's largest feature is that its code is <strong>completely open source and controllable</strong>. It supports private deployment, allowing you to use it in an intranet environment without worrying about data security issues. At the same time, LMO Editor is based on Vue3 and TipTap, both of which have rich ecosystems and communities, making it easy to solve problems when needed.</p>

  <div style="background-color: #E0F2FE; padding: 1rem; border-radius: 0.5rem; margin: 1.5rem 0;">
    <p style="margin: 0; color: #0369A1;">
      <strong>LMO Editor 是一个基于 Vue3 的开源文档编辑器，使用简单本土化开发体验</strong>
    </p>
  </div>

  <h2>Key Features</h2>
  
  <ul>
    <li><strong>Professional Layout:</strong> Clean document interface with proper margins and typography</li>
    <li><strong>Zoom Functionality:</strong> Built-in zoom controls for comfortable viewing and editing</li>
    <li><strong>Multi-language Support:</strong> Full RTL support for Hebrew and Arabic languages</li>
    <li><strong>Responsive Design:</strong> Adapts to different screen sizes and devices</li>
    <li><strong>Print-ready:</strong> Optimized for both screen viewing and printing</li>
    <li><strong>Dark Mode:</strong> Complete dark theme support</li>
    <li><strong>Extensible:</strong> Built on TipTap with rich extension ecosystem</li>
  </ul>

  <h2>Getting Started</h2>
  
  <p>To start using the LMO Editor, simply begin typing in this document. You can:</p>
  
  <ol>
    <li>Use the zoom controls to adjust the document size</li>
    <li>Switch languages to see RTL support in action</li>
    <li>Try different formatting options</li>
    <li>Experience the clean, professional interface</li>
  </ol>

  <p>The editor provides a Microsoft Word-like experience with modern web technologies, making it perfect for legal document creation and management.</p>
`)

const documentTitle = ref('LMO Legal Document Editor')
const documentSubtitle = ref('Professional Document Editing Experience')

// Event handlers
const handleZoomChange = (zoom: number) => {
  console.log('Zoom changed to:', zoom)
}

const handleViewChange = (view: string) => {
  console.log('View changed to:', view)
}

const handleLanguageChange = (language: string) => {
  console.log('Language changed to:', language)
  setLocale(language)
}

// Page meta
definePageMeta({
  title: 'Document Editor Demo',
  description: 'Professional document editor with zoom functionality and clean design',
  layout: 'canvas'
})
</script>

<style scoped>
@reference '~/assets/css/tailwind.css';

.container {
  max-width: 1200px;
}
</style>
