<!--
  Template Editor Component
  
  Advanced editor for legal document templates with preview
-->

<template>
  <div class="h-screen flex flex-col">
    <!-- Loading State -->
    <div v-if="isLoading" class="flex-1 flex items-center justify-center">
      <UiSpinner size="lg" />
      <span class="ml-3 text-gray-600">Loading template...</span>
    </div>

    <!-- Template Not Found -->
    <div v-else-if="!template" class="flex-1 flex items-center justify-center">
      <div class="text-center">
        <Icon name="heroicons:exclamation-triangle" class="mx-auto h-12 w-12 text-gray-400" />
        <h3 class="mt-2 text-sm font-medium text-gray-900">Template not found</h3>
        <p class="mt-1 text-sm text-gray-500">The template you're looking for doesn't exist.</p>
        <div class="mt-6">
          <NuxtLink
            to="/dashboard/templates"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-indigo-600 bg-indigo-100 hover:bg-indigo-200"
          >
            Back to Templates
          </NuxtLink>
        </div>
      </div>
    </div>

    <!-- Template Editor -->
    <template v-else>
      <!-- Header -->
      <div class="bg-white border-b border-gray-200 px-6 py-4">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <NuxtLink
              to="/dashboard/templates"
              class="inline-flex items-center text-sm text-gray-500 hover:text-gray-700"
            >
              <Icon name="heroicons:arrow-left" class="h-4 w-4 mr-1" />
              Back to Templates
            </NuxtLink>
            
            <div class="h-4 w-px bg-gray-300"></div>
            
            <div>
              <h1 class="text-lg font-medium text-gray-900">{{ template.name }}</h1>
              <p class="text-sm text-gray-500">{{ template.description }}</p>
            </div>
          </div>

          <div class="flex items-center space-x-3">
            <!-- View Mode Toggle -->
            <div class="flex items-center space-x-1 bg-gray-100 rounded-md p-1">
              <button
                @click="viewMode = 'edit'"
                :class="[
                  viewMode === 'edit' ? 'bg-white shadow-sm' : '',
                  'px-3 py-1 text-sm font-medium rounded transition-colors duration-200'
                ]"
              >
                Edit
              </button>
              <button
                @click="viewMode = 'preview'"
                :class="[
                  viewMode === 'preview' ? 'bg-white shadow-sm' : '',
                  'px-3 py-1 text-sm font-medium rounded transition-colors duration-200'
                ]"
              >
                Preview
              </button>
              <button
                @click="viewMode = 'split'"
                :class="[
                  viewMode === 'split' ? 'bg-white shadow-sm' : '',
                  'px-3 py-1 text-sm font-medium rounded transition-colors duration-200'
                ]"
              >
                Split
              </button>
            </div>

            <!-- Actions -->
            <button
              @click="saveTemplate"
              :disabled="isSaving"
              class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <UiSpinner v-if="isSaving" size="sm" color="white" class="mr-2" />
              <Icon v-else name="heroicons:check" class="h-4 w-4 mr-2" />
              {{ isSaving ? 'Saving...' : 'Save' }}
            </button>
          </div>
        </div>
      </div>

      <!-- Editor Content -->
      <div class="flex-1 flex overflow-hidden">
        <!-- Edit View -->
        <div v-if="viewMode === 'edit' || viewMode === 'split'" :class="viewMode === 'split' ? 'w-1/2' : 'w-full'" class="flex flex-col">
          <!-- Editor Toolbar -->
          <div class="bg-gray-50 border-b border-gray-200 px-4 py-2">
            <div class="flex items-center space-x-4">
              <h3 class="text-sm font-medium text-gray-900">Template Editor</h3>
              <div class="flex items-center space-x-2 text-sm text-gray-500">
                <span>Variables:</span>
                <div class="flex flex-wrap gap-1">
                  <span
                    v-for="variable in templateVariables"
                    :key="variable"
                    class="inline-flex px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded"
                  >
                    {{ variable }}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <!-- Editor -->
          <div class="flex-1 p-4">
            <textarea
              v-model="templateContent"
              class="w-full h-full resize-none border border-gray-300 rounded-md p-4 font-mono text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
              placeholder="Enter your template content here..."
            ></textarea>
          </div>
        </div>

        <!-- Preview View -->
        <div v-if="viewMode === 'preview' || viewMode === 'split'" :class="viewMode === 'split' ? 'w-1/2 border-l border-gray-200' : 'w-full'" class="flex flex-col">
          <!-- Preview Toolbar -->
          <div class="bg-gray-50 border-b border-gray-200 px-4 py-2">
            <div class="flex items-center justify-between">
              <h3 class="text-sm font-medium text-gray-900">Preview</h3>
              <button
                @click="showVariableForm = !showVariableForm"
                class="inline-flex items-center px-3 py-1 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50"
              >
                <Icon name="heroicons:variable" class="h-3 w-3 mr-1" />
                Test Variables
              </button>
            </div>
          </div>

          <!-- Variable Form -->
          <div v-if="showVariableForm" class="bg-yellow-50 border-b border-yellow-200 p-4">
            <h4 class="text-sm font-medium text-gray-900 mb-3">Test Variables</h4>
            <div class="grid grid-cols-2 gap-3">
              <div v-for="variable in templateVariables" :key="variable">
                <label :for="variable" class="block text-xs font-medium text-gray-700 mb-1">
                  {{ variable }}
                </label>
                <input
                  :id="variable"
                  v-model="testVariables[variable]"
                  type="text"
                  class="block w-full px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                  :placeholder="`Enter ${variable}`"
                />
              </div>
            </div>
          </div>

          <!-- Preview Content -->
          <div class="flex-1 p-4 overflow-auto">
            <div class="bg-white border border-gray-200 rounded-lg p-6 shadow-sm">
              <div class="prose max-w-none" v-html="renderedTemplate"></div>
            </div>
          </div>
        </div>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'

// Composables
const route = useRoute()
const router = useRouter()

// State
const isLoading = ref(true)
const isSaving = ref(false)
const template = ref(null)
const templateContent = ref('')
const viewMode = ref('edit')
const showVariableForm = ref(false)
const testVariables = reactive({})

// Mock template data
const mockTemplate = {
  id: '1',
  name: 'Service Agreement Template',
  description: 'Standard service agreement for client engagements',
  category: 'contract',
  status: 'active',
  content: `SERVICE AGREEMENT

This Service Agreement ("Agreement") is entered into on {{date}} between {{client_name}} ("Client") and {{company_name}} ("Service Provider").

1. SERVICES
The Service Provider agrees to provide the following services:
{{services_description}}

2. COMPENSATION
The Client agrees to pay {{amount}} for the services described above.

3. PAYMENT TERMS
Payment is due {{payment_terms}}.

4. TERM
This Agreement shall commence on {{start_date}} and continue until {{end_date}}.

Client Signature: ___________________ Date: ___________
{{client_name}}

Service Provider Signature: ___________________ Date: ___________
{{company_name}}`,
  variables: ['date', 'client_name', 'company_name', 'services_description', 'amount', 'payment_terms', 'start_date', 'end_date']
}

// Computed
const templateVariables = computed(() => {
  if (!template.value) return []
  return template.value.variables || []
})

const renderedTemplate = computed(() => {
  let content = templateContent.value
  
  // Replace variables with test values
  templateVariables.value.forEach(variable => {
    const value = testVariables[variable] || `[${variable}]`
    const regex = new RegExp(`{{${variable}}}`, 'g')
    content = content.replace(regex, value)
  })
  
  // Convert to HTML (basic formatting)
  return content
    .replace(/\n\n/g, '</p><p>')
    .replace(/\n/g, '<br>')
    .replace(/^/, '<p>')
    .replace(/$/, '</p>')
})

// Methods
const loadTemplate = async () => {
  try {
    const templateId = route.query.id
    console.log('Loading template for edit:', templateId)
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    template.value = mockTemplate
    templateContent.value = template.value.content
    
    // Initialize test variables
    templateVariables.value.forEach(variable => {
      testVariables[variable] = ''
    })
    
  } catch (error) {
    console.error('Error loading template:', error)
  } finally {
    isLoading.value = false
  }
}

const saveTemplate = async () => {
  try {
    isSaving.value = true
    
    const updatedTemplate = {
      ...template.value,
      content: templateContent.value,
      updatedAt: new Date().toISOString()
    }
    
    // TODO: Replace with actual API call
    console.log('Saving template:', updatedTemplate)
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // Update local template
    template.value.content = templateContent.value
    
  } catch (error) {
    console.error('Error saving template:', error)
    alert('Failed to save template. Please try again.')
  } finally {
    isSaving.value = false
  }
}

// Watch for content changes to extract variables
watch(templateContent, (newContent) => {
  if (newContent && template.value) {
    // Extract variables from content
    const variableMatches = newContent.match(/{{(\w+)}}/g)
    if (variableMatches) {
      const variables = variableMatches.map(match => match.replace(/[{}]/g, ''))
      const uniqueVariables = [...new Set(variables)]
      
      // Update template variables
      template.value.variables = uniqueVariables
      
      // Initialize new test variables
      uniqueVariables.forEach(variable => {
        if (!(variable in testVariables)) {
          testVariables[variable] = ''
        }
      })
    }
  }
})

// Lifecycle
onMounted(() => {
  loadTemplate()
})
</script>
