/**
 * Vue 3 Composable for Event Listeners
 * Provides reactive event handling with automatic cleanup
 */

import { onUnmounted, ref, computed, type Ref } from 'vue'
import { getEventBus } from '~/utils/eventBus'
import type { 
  EventMap, 
  EventListener, 
  EventUnsubscribe, 
  EventListenerOptions 
} from '~/types/events'

// ============================================================================
// COMPOSABLE INTERFACE
// ============================================================================

export interface UseEventListenersReturn {
  // Event listening methods
  on: <K extends keyof EventMap>(
    event: K, 
    listener: EventListener<EventMap[K]>, 
    options?: EventListenerOptions
  ) => EventUnsubscribe
  
  once: <K extends keyof EventMap>(
    event: K, 
    listener: EventListener<EventMap[K]>
  ) => EventUnsubscribe
  
  off: <K extends keyof EventMap>(
    event: K, 
    listener?: EventListener<EventMap[K]>
  ) => void
  
  emit: <K extends keyof EventMap>(event: K, data: EventMap[K]) => void
  
  // State management
  clear: () => void
  pause: () => void
  resume: () => void
  
  // Reactive state
  isListening: Ref<boolean>
  listenerCount: Ref<number>
  activeEvents: Ref<string[]>
}

// ============================================================================
// MAIN COMPOSABLE
// ============================================================================

export function useEventListeners(): UseEventListenersReturn {
  const eventBus = getEventBus()
  const unsubscribeFunctions = new Set<EventUnsubscribe>()
  const isListening = ref(true)
  const activeListeners = ref(new Map<string, Set<EventListener>>())

  // ============================================================================
  // COMPUTED PROPERTIES
  // ============================================================================

  const listenerCount = computed(() => {
    let total = 0
    for (const listeners of activeListeners.value.values()) {
      total += listeners.size
    }
    return total
  })

  const activeEvents = computed(() => {
    return Array.from(activeListeners.value.keys())
  })

  // ============================================================================
  // EVENT METHODS
  // ============================================================================

  const on = <K extends keyof EventMap>(
    event: K,
    listener: EventListener<EventMap[K]>,
    options: EventListenerOptions = {}
  ): EventUnsubscribe => {
    if (!isListening.value) {
      console.warn('Event listeners are paused. Call resume() to enable listening.')
      return () => {}
    }

    // Wrap listener to track it
    const wrappedListener = (data: EventMap[K]) => {
      if (isListening.value) {
        listener(data)
      }
    }

    // Track the listener
    const eventKey = String(event)
    if (!activeListeners.value.has(eventKey)) {
      activeListeners.value.set(eventKey, new Set())
    }
    activeListeners.value.get(eventKey)!.add(wrappedListener)

    // Register with event bus
    const unsubscribe = eventBus.on(event, wrappedListener, options)
    unsubscribeFunctions.add(unsubscribe)

    // Return enhanced unsubscribe function
    return () => {
      unsubscribe()
      unsubscribeFunctions.delete(unsubscribe)
      activeListeners.value.get(eventKey)?.delete(wrappedListener)
      
      // Clean up empty event sets
      if (activeListeners.value.get(eventKey)?.size === 0) {
        activeListeners.value.delete(eventKey)
      }
    }
  }

  const once = <K extends keyof EventMap>(
    event: K,
    listener: EventListener<EventMap[K]>
  ): EventUnsubscribe => {
    return on(event, listener, { once: true })
  }

  const off = <K extends keyof EventMap>(
    event: K,
    listener?: EventListener<EventMap[K]>
  ): void => {
    const eventKey = String(event)
    
    if (listener) {
      // Remove specific listener
      eventBus.off(event, listener)
      activeListeners.value.get(eventKey)?.delete(listener)
    } else {
      // Remove all listeners for event
      eventBus.off(event)
      activeListeners.value.delete(eventKey)
    }
  }

  const emit = <K extends keyof EventMap>(event: K, data: EventMap[K]): void => {
    eventBus.emit(event, data)
  }

  // ============================================================================
  // STATE MANAGEMENT
  // ============================================================================

  const clear = (): void => {
    // Unsubscribe all listeners
    unsubscribeFunctions.forEach(unsubscribe => unsubscribe())
    unsubscribeFunctions.clear()
    activeListeners.value.clear()
  }

  const pause = (): void => {
    isListening.value = false
  }

  const resume = (): void => {
    isListening.value = true
  }

  // ============================================================================
  // LIFECYCLE MANAGEMENT
  // ============================================================================

  // Auto cleanup on component unmount
  onUnmounted(() => {
    clear()
  })

  // ============================================================================
  // RETURN INTERFACE
  // ============================================================================

  return {
    // Methods
    on,
    once,
    off,
    emit,
    clear,
    pause,
    resume,
    
    // Reactive state
    isListening,
    listenerCount,
    activeEvents
  }
}

// ============================================================================
// SPECIALIZED COMPOSABLES
// ============================================================================

/**
 * Composable for listening to specific event categories
 */
export function useCaseEvents() {
  const { on, once, off, emit } = useEventListeners()

  return {
    onCaseCreated: (listener: EventListener<EventMap['case:created']>) => 
      on('case:created', listener),
    
    onCaseUpdated: (listener: EventListener<EventMap['case:updated']>) => 
      on('case:updated', listener),
    
    onCaseStatusChanged: (listener: EventListener<EventMap['case:status-changed']>) => 
      on('case:status-changed', listener),
    
    onCaseDeleted: (listener: EventListener<EventMap['case:deleted']>) => 
      on('case:deleted', listener),

    emitCaseCreated: (data: EventMap['case:created']) => 
      emit('case:created', data),
    
    emitCaseUpdated: (data: EventMap['case:updated']) => 
      emit('case:updated', data),
    
    emitCaseStatusChanged: (data: EventMap['case:status-changed']) => 
      emit('case:status-changed', data),
    
    emitCaseDeleted: (data: EventMap['case:deleted']) => 
      emit('case:deleted', data)
  }
}

/**
 * Composable for listening to document events
 */
export function useDocumentEvents() {
  const { on, once, off, emit } = useEventListeners()

  return {
    onDocumentUploaded: (listener: EventListener<EventMap['document:uploaded']>) => 
      on('document:uploaded', listener),
    
    onDocumentShared: (listener: EventListener<EventMap['document:shared']>) => 
      on('document:shared', listener),
    
    onDocumentDeleted: (listener: EventListener<EventMap['document:deleted']>) => 
      on('document:deleted', listener),

    emitDocumentUploaded: (data: EventMap['document:uploaded']) => 
      emit('document:uploaded', data),
    
    emitDocumentShared: (data: EventMap['document:shared']) => 
      emit('document:shared', data),
    
    emitDocumentDeleted: (data: EventMap['document:deleted']) => 
      emit('document:deleted', data)
  }
}

/**
 * Composable for listening to UI events
 */
export function useUIEvents() {
  const { on, once, off, emit } = useEventListeners()

  return {
    onNavigation: (listener: EventListener<EventMap['ui:navigation']>) => 
      on('ui:navigation', listener),
    
    onSearch: (listener: EventListener<EventMap['ui:search']>) => 
      on('ui:search', listener),
    
    onFilter: (listener: EventListener<EventMap['ui:filter']>) => 
      on('ui:filter', listener),
    
    onBulkAction: (listener: EventListener<EventMap['ui:bulk-action']>) => 
      on('ui:bulk-action', listener),

    emitNavigation: (data: EventMap['ui:navigation']) => 
      emit('ui:navigation', data),
    
    emitSearch: (data: EventMap['ui:search']) => 
      emit('ui:search', data),
    
    emitFilter: (data: EventMap['ui:filter']) => 
      emit('ui:filter', data),
    
    emitBulkAction: (data: EventMap['ui:bulk-action']) => 
      emit('ui:bulk-action', data)
  }
}
