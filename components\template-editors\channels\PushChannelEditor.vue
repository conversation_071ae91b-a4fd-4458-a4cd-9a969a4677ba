<template>
  <div class="push-channel-editor space-y-6">
    <!-- Push Notification Content -->
    <div class="space-y-4">
      <div class="flex items-center justify-between">
        <h3 class="text-lg font-medium text-gray-900 dark:text-white">Push Notification</h3>
        <div class="flex items-center space-x-2">
          <UiButton
            @click="showTemplateLibrary = true"
            variant="outline"
            size="sm"
          >
            <Icon name="heroicons:book-open" class="w-4 h-4 mr-2" />
            Templates
          </UiButton>
          <UiButton
            @click="showVariableModal = true"
            variant="outline"
            size="sm"
          >
            <Icon name="heroicons:variable" class="w-4 h-4 mr-2" />
            Variables
          </UiButton>
        </div>
      </div>

      <!-- Title -->
      <div class="space-y-2">
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
          Title
        </label>
        <UiInput
          v-model="localContent.title"
          placeholder="Notification title..."
          @input="handleContentChange"
        />
        <div class="text-xs text-gray-500 dark:text-gray-400">
          Character count: {{ titleLength }} (recommended: under 50 characters)
        </div>
      </div>

      <!-- Message -->
      <div class="space-y-2">
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
          Message
        </label>
        <UiTextarea
          v-model="localContent.message"
          placeholder="Notification message..."
          :rows="4"
          @input="handleContentChange"
        />
        <div class="text-xs text-gray-500 dark:text-gray-400">
          Character count: {{ messageLength }} (recommended: under 120 characters)
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="space-y-4">
        <div class="flex items-center justify-between">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Action Buttons
          </label>
          <UiButton
            @click="addActionButton"
            variant="outline"
            size="sm"
            :disabled="localContent.actions.length >= 3"
          >
            <Icon name="heroicons:plus" class="w-4 h-4 mr-1" />
            Add Button
          </UiButton>
        </div>
        
        <div v-if="localContent.actions.length > 0" class="space-y-3">
          <div
            v-for="(action, index) in localContent.actions"
            :key="index"
            class="flex items-center space-x-3 p-3 border border-gray-200 dark:border-gray-700 rounded-lg"
          >
            <div class="flex-1 grid grid-cols-1 md:grid-cols-2 gap-3">
              <UiInput
                v-model="action.title"
                placeholder="Button text"
                @input="handleContentChange"
              />
              <UiInput
                v-model="action.url"
                placeholder="Action URL"
                @input="handleContentChange"
              />
            </div>
            <UiButton
              @click="removeActionButton(index)"
              variant="ghost"
              size="sm"
              class="text-red-600 hover:text-red-700"
            >
              <Icon name="heroicons:trash" class="w-4 h-4" />
            </UiButton>
          </div>
        </div>
        
        <div v-else class="text-sm text-gray-500 dark:text-gray-400 text-center py-4 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg">
          No action buttons added. Click "Add Button" to create interactive notifications.
        </div>
      </div>
    </div>

    <!-- Push Notification Preview -->
    <UiCard class="p-4">
      <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-4">Push Notification Preview</h4>
      
      <!-- Desktop Preview -->
      <div class="space-y-4">
        <div>
          <h5 class="text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">Desktop</h5>
          <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg p-4 max-w-sm">
            <div class="flex items-start space-x-3">
              <div class="w-8 h-8 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center flex-shrink-0">
                <Icon name="heroicons:bell" class="w-4 h-4 text-purple-600 dark:text-purple-400" />
              </div>
              <div class="flex-1 min-w-0">
                <div class="text-sm font-medium text-gray-900 dark:text-white truncate">
                  {{ renderedTitle || 'Notification Title' }}
                </div>
                <div class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                  {{ renderedMessage || 'Notification message will appear here...' }}
                </div>
                <div v-if="localContent.actions.length > 0" class="flex space-x-2 mt-3">
                  <button
                    v-for="action in localContent.actions"
                    :key="action.title"
                    class="px-3 py-1 text-xs bg-purple-100 text-purple-700 rounded-md hover:bg-purple-200 transition-colors"
                  >
                    {{ action.title }}
                  </button>
                </div>
              </div>
              <div class="text-xs text-gray-400">{{ currentTime }}</div>
            </div>
          </div>
        </div>

        <!-- Mobile Preview -->
        <div>
          <h5 class="text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">Mobile</h5>
          <div class="bg-gray-900 rounded-2xl p-2 max-w-xs">
            <div class="bg-white dark:bg-gray-800 rounded-xl overflow-hidden">
              <div class="p-3">
                <div class="flex items-center space-x-2 mb-2">
                  <div class="w-6 h-6 bg-purple-100 dark:bg-purple-900 rounded-md flex items-center justify-center">
                    <Icon name="heroicons:bell" class="w-3 h-3 text-purple-600 dark:text-purple-400" />
                  </div>
                  <div class="text-xs font-medium text-gray-900 dark:text-white">
                    {{ renderedTitle || 'Notification Title' }}
                  </div>
                  <div class="text-xs text-gray-400 ml-auto">{{ currentTime }}</div>
                </div>
                <div class="text-xs text-gray-600 dark:text-gray-400">
                  {{ renderedMessage || 'Notification message...' }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </UiCard>

    <!-- Push Settings -->
    <UiCard class="p-4">
      <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-4">Push Settings</h4>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Priority
          </label>
          <select
            v-model="localContent.settings.priority"
            @change="handleContentChange"
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm"
          >
            <option value="low">Low</option>
            <option value="normal">Normal</option>
            <option value="high">High</option>
          </select>
        </div>
        
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Badge Count
          </label>
          <UiInput
            v-model.number="localContent.settings.badge"
            type="number"
            min="0"
            placeholder="0"
            @input="handleContentChange"
          />
        </div>
      </div>
      
      <div class="mt-4 space-y-3">
        <label class="flex items-center">
          <input
            type="checkbox"
            v-model="localContent.settings.requireInteraction"
            @change="handleContentChange"
            class="rounded border-gray-300 text-purple-600 focus:ring-purple-500"
          />
          <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Require user interaction</span>
        </label>
        
        <label class="flex items-center">
          <input
            type="checkbox"
            v-model="localContent.settings.silent"
            @change="handleContentChange"
            class="rounded border-gray-300 text-purple-600 focus:ring-purple-500"
          />
          <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Silent notification</span>
        </label>
        
        <label class="flex items-center">
          <input
            type="checkbox"
            v-model="localContent.settings.enableAnalytics"
            @change="handleContentChange"
            class="rounded border-gray-300 text-purple-600 focus:ring-purple-500"
          />
          <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Enable click tracking</span>
        </label>
      </div>
    </UiCard>

    <!-- Template Library Modal -->
    <PushTemplateLibraryModal
      v-if="showTemplateLibrary"
      @close="showTemplateLibrary = false"
      @select="insertTemplate"
    />

    <!-- Variable Insert Modal -->
    <VariableInsertModal
      v-if="showVariableModal"
      @close="showVariableModal = false"
      @insert="insertVariable"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, defineAsyncComponent } from 'vue'

// Lazy load modals
const PushTemplateLibraryModal = defineAsyncComponent(() => import('../../template-modals/PushTemplateLibraryModal.vue'))
const VariableInsertModal = defineAsyncComponent(() => import('../../template-modals/VariableInsertModal.vue'))

// Props
interface Props {
  content: any
  variables: string[]
  templateData: any
  category?: any
}

const props = withDefaults(defineProps<Props>(), {
  content: () => ({}),
  variables: () => [],
})

// Emits
const emit = defineEmits<{
  'update:content': [content: any]
  'update:variables': [variables: string[]]
  'content-change': [content: any, variables: string[]]
}>()

// State
const showTemplateLibrary = ref(false)
const showVariableModal = ref(false)

// Local content with defaults
const localContent = ref({
  title: '',
  message: '',
  actions: [],
  settings: {
    priority: 'normal',
    badge: 0,
    requireInteraction: false,
    silent: false,
    enableAnalytics: true,
  },
  ...props.content
})

const localVariables = ref([...props.variables])

// Computed
const titleLength = computed(() => localContent.value.title?.length || 0)
const messageLength = computed(() => localContent.value.message?.length || 0)

const renderedTitle = computed(() => {
  let title = localContent.value.title || ''
  localVariables.value.forEach(variable => {
    const sampleValue = getSampleValue(variable)
    title = title.replace(new RegExp(`{${variable}}`, 'g'), sampleValue)
  })
  return title
})

const renderedMessage = computed(() => {
  let message = localContent.value.message || ''
  localVariables.value.forEach(variable => {
    const sampleValue = getSampleValue(variable)
    message = message.replace(new RegExp(`{${variable}}`, 'g'), sampleValue)
  })
  return message
})

const currentTime = computed(() => {
  return new Date().toLocaleTimeString('en-US', { 
    hour: 'numeric', 
    minute: '2-digit',
    hour12: true 
  })
})

// Watchers
watch(() => props.content, (newContent) => {
  localContent.value = {
    title: '',
    message: '',
    actions: [],
    settings: {
      priority: 'normal',
      badge: 0,
      requireInteraction: false,
      silent: false,
      enableAnalytics: true,
    },
    ...newContent
  }
}, { deep: true, immediate: true })

watch(() => props.variables, (newVariables) => {
  localVariables.value = [...newVariables]
}, { immediate: true })

// Methods
const handleContentChange = () => {
  emit('update:content', localContent.value)
  emit('content-change', localContent.value, localVariables.value)
}

const addActionButton = () => {
  if (localContent.value.actions.length < 3) {
    localContent.value.actions.push({
      title: '',
      url: ''
    })
    handleContentChange()
  }
}

const removeActionButton = (index: number) => {
  localContent.value.actions.splice(index, 1)
  handleContentChange()
}

const insertTemplate = (template: any) => {
  localContent.value.title = template.title || localContent.value.title
  localContent.value.message = template.message || localContent.value.message
  
  if (template.actions) {
    localContent.value.actions = [...template.actions]
  }
  
  // Merge variables
  if (template.variables) {
    const newVariables = [...new Set([...localVariables.value, ...template.variables])]
    localVariables.value = newVariables
    emit('update:variables', newVariables)
  }
  
  showTemplateLibrary.value = false
  handleContentChange()
}

const insertVariable = (variable: string) => {
  // Insert into title or message based on focus (simplified)
  const variableText = `{${variable}}`
  localContent.value.message += variableText
  
  // Add variable to list if not already present
  if (!localVariables.value.includes(variable)) {
    localVariables.value.push(variable)
    emit('update:variables', localVariables.value)
  }
  
  showVariableModal.value = false
  handleContentChange()
}

const getSampleValue = (variable: string) => {
  const sampleValues: Record<string, string> = {
    clientName: 'John Doe',
    companyName: 'Legal Services',
    appointmentDate: 'Jan 15',
    appointmentTime: '2:00 PM',
    amount: '$1,250',
    caseNumber: 'CASE-001',
    attorneyName: 'Jane Smith',
  }
  
  return sampleValues[variable] || `[${variable.toUpperCase()}]`
}
</script>
