<template>
  <UiModal @close="$emit('close')" size="lg">
    <div class="p-6">
      <!-- Header -->
      <div class="flex items-center space-x-3 mb-6">
        <div class="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
          <Icon name="heroicons:paper-airplane" class="w-5 h-5 text-blue-600 dark:text-blue-400" />
        </div>
        <div>
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Test Notification</h3>
          <p class="text-sm text-gray-600 dark:text-gray-400">
            Send a test notification to verify your template
          </p>
        </div>
      </div>

      <!-- Template Info -->
      <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 mb-6">
        <div class="flex items-center space-x-3">
          <Icon name="heroicons:bell" class="w-5 h-5 text-gray-600 dark:text-gray-400" />
          <div>
            <h4 class="text-sm font-medium text-gray-900 dark:text-white">{{ template?.name }}</h4>
            <p class="text-xs text-gray-500 dark:text-gray-400">
              {{ channel ? `${getChannelName(channel)} Channel` : 'All Channels' }}
            </p>
          </div>
        </div>
      </div>

      <!-- Test Configuration -->
      <div class="space-y-6">
        <!-- Channel Selection (if testing all channels) -->
        <div v-if="!channel && template?.channels?.length > 1" class="space-y-3">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Select Channels to Test
          </label>
          <div class="grid grid-cols-2 gap-3">
            <label
              v-for="channelId in template.channels"
              :key="channelId"
              class="flex items-center space-x-2 p-3 border border-gray-200 dark:border-gray-700 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800"
            >
              <input
                type="checkbox"
                :value="channelId"
                v-model="selectedChannels"
                class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <Icon :name="getChannelIcon(channelId)" class="w-4 h-4 text-gray-600 dark:text-gray-400" />
              <span class="text-sm text-gray-700 dark:text-gray-300">{{ getChannelName(channelId) }}</span>
            </label>
          </div>
        </div>

        <!-- Recipient Information -->
        <div class="space-y-4">
          <h4 class="text-sm font-medium text-gray-900 dark:text-white">Test Recipient</h4>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <UiInput
              v-model="testData.recipientName"
              label="Recipient Name"
              placeholder="John Doe"
              required
            />
            <UiInput
              v-model="testData.recipientEmail"
              label="Email Address"
              placeholder="<EMAIL>"
              type="email"
              required
            />
          </div>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <UiInput
              v-model="testData.recipientPhone"
              label="Phone Number (for SMS)"
              placeholder="+****************"
            />
            <UiInput
              v-model="testData.recipientUserId"
              label="User ID (for in-app)"
              placeholder="user123"
            />
          </div>
        </div>

        <!-- Variable Values -->
        <div v-if="template?.variables?.length > 0" class="space-y-4">
          <h4 class="text-sm font-medium text-gray-900 dark:text-white">Variable Values</h4>
          <p class="text-xs text-gray-500 dark:text-gray-400">
            Provide test values for template variables
          </p>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <UiInput
              v-for="variable in template.variables"
              :key="variable"
              v-model="testData.variables[variable]"
              :label="variable"
              :placeholder="`Enter ${variable} value`"
            />
          </div>
        </div>

        <!-- Test Options -->
        <div class="space-y-4">
          <h4 class="text-sm font-medium text-gray-900 dark:text-white">Test Options</h4>
          
          <div class="space-y-3">
            <label class="flex items-center space-x-2">
              <input
                type="checkbox"
                v-model="testData.options.trackDelivery"
                class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span class="text-sm text-gray-700 dark:text-gray-300">Track delivery status</span>
            </label>
            
            <label class="flex items-center space-x-2">
              <input
                type="checkbox"
                v-model="testData.options.includeMetadata"
                class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span class="text-sm text-gray-700 dark:text-gray-300">Include test metadata</span>
            </label>
            
            <label class="flex items-center space-x-2">
              <input
                type="checkbox"
                v-model="testData.options.logResults"
                class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span class="text-sm text-gray-700 dark:text-gray-300">Log test results</span>
            </label>
          </div>
        </div>

        <!-- Test Notes -->
        <div class="space-y-2">
          <UiTextarea
            v-model="testData.notes"
            label="Test Notes (Optional)"
            placeholder="Add any notes about this test..."
            :rows="3"
          />
        </div>
      </div>

      <!-- Warning -->
      <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4 mt-6">
        <div class="flex items-start space-x-3">
          <Icon name="heroicons:exclamation-triangle" class="w-5 h-5 text-yellow-600 dark:text-yellow-400 mt-0.5 flex-shrink-0" />
          <div>
            <h4 class="text-sm font-medium text-yellow-800 dark:text-yellow-200">Test Notification</h4>
            <p class="text-sm text-yellow-700 dark:text-yellow-300 mt-1">
              This will send actual notifications to the specified recipient. Make sure the contact information is correct.
            </p>
          </div>
        </div>
      </div>

      <!-- Actions -->
      <div class="flex items-center justify-between mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
        <UiButton @click="$emit('close')" variant="outline">
          Cancel
        </UiButton>
        
        <div class="flex items-center space-x-3">
          <UiButton @click="previewTest" variant="outline">
            <Icon name="heroicons:eye" class="w-4 h-4 mr-2" />
            Preview
          </UiButton>
          <UiButton @click="sendTest" :disabled="!canSendTest || isSending" class="bg-blue-600 hover:bg-blue-700">
            <Icon name="heroicons:paper-airplane" class="w-4 h-4 mr-2" />
            {{ isSending ? 'Sending...' : 'Send Test' }}
          </UiButton>
        </div>
      </div>
    </div>
  </UiModal>
</template>

<script setup lang="ts">
import { ref, computed, reactive, onMounted } from 'vue'

// Props
interface Props {
  template: any
  channel?: string | null
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  close: []
  send: [testData: any]
}>()

// State
const isSending = ref(false)
const selectedChannels = ref<string[]>([])

const testData = reactive({
  recipientName: '',
  recipientEmail: '',
  recipientPhone: '',
  recipientUserId: '',
  variables: {} as Record<string, string>,
  options: {
    trackDelivery: true,
    includeMetadata: false,
    logResults: true,
  },
  notes: '',
})

// Available channels
const availableChannels = [
  { id: 'email', name: 'Email', icon: 'heroicons:envelope' },
  { id: 'sms', name: 'SMS', icon: 'heroicons:device-phone-mobile' },
  { id: 'push', name: 'Push Notification', icon: 'heroicons:bell' },
  { id: 'in_app', name: 'In-App', icon: 'heroicons:computer-desktop' },
  { id: 'log', name: 'System Log', icon: 'heroicons:document-text' },
]

// Computed
const canSendTest = computed(() => {
  const hasRecipient = testData.recipientName.trim() && testData.recipientEmail.trim()
  const hasChannels = props.channel || selectedChannels.value.length > 0
  return hasRecipient && hasChannels && !isSending.value
})

// Methods
const getChannelName = (channelId: string) => {
  return availableChannels.find(ch => ch.id === channelId)?.name || channelId
}

const getChannelIcon = (channelId: string) => {
  return availableChannels.find(ch => ch.id === channelId)?.icon || 'heroicons:bell'
}

const previewTest = () => {
  const previewData = {
    template: props.template,
    channel: props.channel,
    channels: props.channel ? [props.channel] : selectedChannels.value,
    recipient: {
      name: testData.recipientName,
      email: testData.recipientEmail,
      phone: testData.recipientPhone,
      userId: testData.recipientUserId,
    },
    variables: testData.variables,
    options: testData.options,
    notes: testData.notes,
  }
  
  sessionStorage.setItem('notificationTestPreview', JSON.stringify(previewData))
  window.open('/dashboard/templates/notification/test-preview', '_blank')
}

const sendTest = async () => {
  if (!canSendTest.value) return
  
  isSending.value = true
  
  try {
    const testPayload = {
      templateId: props.template?.id,
      channels: props.channel ? [props.channel] : selectedChannels.value,
      recipient: {
        name: testData.recipientName,
        email: testData.recipientEmail,
        phone: testData.recipientPhone,
        userId: testData.recipientUserId,
      },
      variables: testData.variables,
      options: testData.options,
      notes: testData.notes,
      isTest: true,
    }
    
    emit('send', testPayload)
  } catch (error) {
    console.error('Error sending test notification:', error)
  } finally {
    isSending.value = false
  }
}

// Lifecycle
onMounted(() => {
  // Initialize selected channels
  if (props.channel) {
    selectedChannels.value = [props.channel]
  } else if (props.template?.channels) {
    selectedChannels.value = [...props.template.channels]
  }
  
  // Initialize variables with sample values
  if (props.template?.variables) {
    props.template.variables.forEach((variable: string) => {
      testData.variables[variable] = getSampleValue(variable)
    })
  }
  
  // Set default recipient info for testing
  testData.recipientName = 'Test User'
  testData.recipientEmail = '<EMAIL>'
  testData.recipientPhone = '+****************'
  testData.recipientUserId = 'test-user-123'
})

// Helper function to generate sample values
const getSampleValue = (variable: string) => {
  const sampleValues: Record<string, string> = {
    clientName: 'John Doe',
    companyName: 'Acme Legal Services',
    caseTitle: 'Contract Dispute Case',
    appointmentDate: new Date().toLocaleDateString(),
    appointmentTime: '2:00 PM',
    invoiceNumber: 'INV-001',
    amount: '$1,250.00',
    dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toLocaleDateString(),
    attorneyName: 'Jane Smith',
    documentTitle: 'Legal Agreement',
    caseNumber: 'CASE-2024-001',
  }
  
  return sampleValues[variable] || `Sample ${variable}`
}
</script>
