import { computed, ref } from 'vue'
import { useAuth } from '~/composables/useAuth'
import platformNavigationConfig from '~/config/platformNavigation'
import tenantNavigationConfig from '~/config/tenantNavigation'

// Types
export interface MenuBadge {
  text: string
  type: 'success' | 'warning' | 'error' | 'info'
}

export interface MenuItem {
  id: string
  label: string
  icon: string
  path?: string
  roles?: string[]
  tenantRoles?: string[]
  requiresTenant?: boolean
  badge?: MenuBadge
  order?: number
  children?: MenuItem[]
}

export interface NavigationConfig {
  menuItems: MenuItem[]
  roleHierarchy: Record<string, string[]>
  tenantRoleHierarchy: Record<string, string[]>
}

export interface UseNavigationOptions {
  userRoles?: string[]
  tenantRoles?: string[]
  activeTenantId?: string | null
}

/**
 * Navigation composable for managing role-based menu items
 */
export const useNavigation = (options: UseNavigationOptions = {}) => {
  const auth = useAuth()
  const navigationConfig = !auth.isPlatformUser.value ? tenantNavigationConfig : platformNavigationConfig
  
  const {
    userRoles = ['user'],
    tenantRoles = [],
    activeTenantId = null
  } = options

  // Load navigation configuration
  const config = navigationConfig as NavigationConfig

  // State
  const expandedItems = ref<string[]>([])

  // Helper functions
  const getEffectiveRoles = (userRole: string, hierarchy: Record<string, string[]>): string[] => {
    const roles = [userRole]
    const inheritedRoles = hierarchy[userRole] || []
    return [...roles, ...inheritedRoles]
  }

  const hasRole = (requiredRoles?: string[]): boolean => {
    if (!requiredRoles || requiredRoles.length === 0) return true
    
    // Get all effective roles including inherited ones
    const effectiveRoles = userRoles.flatMap(role => 
      getEffectiveRoles(role, config.roleHierarchy)
    )
    
    return requiredRoles.some(role => effectiveRoles.includes(role))
  }

  const hasTenantRole = (requiredTenantRoles?: string[]): boolean => {
    if (!requiredTenantRoles || requiredTenantRoles.length === 0) return true
    
    // Get all effective tenant roles including inherited ones
    const effectiveTenantRoles = tenantRoles.flatMap(role => 
      getEffectiveRoles(role, config.tenantRoleHierarchy)
    )
    
    return requiredTenantRoles.some(role => effectiveTenantRoles.includes(role))
  }

  const canAccessItem = (item: MenuItem): boolean => {
    // console.debug('Checking access for item', item)
    // Check if user has required platform roles
    if (!hasRole(item.roles)) return false
    
    // Check if item requires tenant and user has active tenant
    if (item.requiresTenant && !activeTenantId) return false
    
    // Check if user has required tenant roles (when tenant is active)
    if (item.tenantRoles && activeTenantId && !hasTenantRole(item.tenantRoles)) return false
    
    return true
  }

  const sortMenuItems = (items: MenuItem[]): MenuItem[] => {
    return items.sort((a, b) => (a.order || 999) - (b.order || 999))
  }

  const filterMenuItems = (items: MenuItem[]): MenuItem[] => {
    return sortMenuItems(items.filter(item => {
      if (!canAccessItem(item)) return false
      
      // If item has children, filter them recursively
      if (item.children) {
        item.children = filterMenuItems(item.children)
        // Only show parent if it has accessible children or a path itself
        return item.children.length > 0 || item.path
      }
      
      return true
    }))
  }

  // Computed properties
  const filteredMenuItems = computed(() => {
    return filterMenuItems(config.menuItems)
  })

  const menuItemsFlat = computed(() => {
    const flattenItems = (items: MenuItem[]): MenuItem[] => {
      const result: MenuItem[] = []
      items.forEach(item => {
        result.push(item)
        if (item.children) {
          result.push(...flattenItems(item.children))
        }
      })
      return result
    }
    return flattenItems(filteredMenuItems.value)
  })

  // Methods
  const findMenuItem = (id: string): MenuItem | undefined => {
    return menuItemsFlat.value.find(item => item.id === id)
  }

  const findMenuItemByPath = (path: string): MenuItem | undefined => {
    return menuItemsFlat.value.find(item => item.path === path)
  }

  const getMenuItemPath = (id: string): string[] => {
    const findPath = (items: MenuItem[], targetId: string, currentPath: string[] = []): string[] | null => {
      for (const item of items) {
        const newPath = [...currentPath, item.id]
        
        if (item.id === targetId) {
          return newPath
        }
        
        if (item.children) {
          const childPath = findPath(item.children, targetId, newPath)
          if (childPath) return childPath
        }
      }
      return null
    }
    
    return findPath(filteredMenuItems.value, id) || []
  }

  const isMenuItemActive = (item: MenuItem, currentPath: string): boolean => {
    if (item.path && currentPath.startsWith(item.path)) return true
    
    if (item.children) {
      return item.children.some(child => isMenuItemActive(child, currentPath))
    }
    
    return false
  }

  const hasActiveChild = (item: MenuItem, currentPath: string): boolean => {
    if (!item.children) return false
    
    return item.children.some(child => {
      if (child.path && currentPath.startsWith(child.path)) return true
      return hasActiveChild(child, currentPath)
    })
  }

  const toggleSubmenu = (itemId: string) => {
    const index = expandedItems.value.indexOf(itemId)
    if (index > -1) {
      expandedItems.value.splice(index, 1)
    } else {
      expandedItems.value.push(itemId)
    }
  }

  const expandToItem = (itemId: string) => {
    const path = getMenuItemPath(itemId)
    // Expand all parent items except the item itself
    const parentsToExpand = path.slice(0, -1)
    
    parentsToExpand.forEach(parentId => {
      if (!expandedItems.value.includes(parentId)) {
        expandedItems.value.push(parentId)
      }
    })
  }

  const expandToPath = (currentPath: string) => {
    const activeItem = findMenuItemByPath(currentPath)
    if (activeItem) {
      expandToItem(activeItem.id)
    } else {
      // Try to find the closest matching item
      const matchingItem = menuItemsFlat.value.find(item => 
        item.path && currentPath.startsWith(item.path)
      )
      if (matchingItem) {
        expandToItem(matchingItem.id)
      }
    }
  }

  const collapseAll = () => {
    expandedItems.value = []
  }

  const expandAll = () => {
    const getAllItemIds = (items: MenuItem[]): string[] => {
      const ids: string[] = []
      items.forEach(item => {
        if (item.children && item.children.length > 0) {
          ids.push(item.id)
          ids.push(...getAllItemIds(item.children))
        }
      })
      return ids
    }
    
    expandedItems.value = getAllItemIds(filteredMenuItems.value)
  }

  // Utility functions for external use
  const getUserPermissions = () => ({
    roles: userRoles,
    tenantRoles,
    activeTenantId,
    effectiveRoles: userRoles.flatMap(role => 
      getEffectiveRoles(role, config.roleHierarchy)
    ),
    effectiveTenantRoles: tenantRoles.flatMap(role => 
      getEffectiveRoles(role, config.tenantRoleHierarchy)
    )
  })

  return {
    // State
    expandedItems,
    
    // Computed
    filteredMenuItems,
    menuItemsFlat,
    
    // Methods
    findMenuItem,
    findMenuItemByPath,
    getMenuItemPath,
    isMenuItemActive,
    hasActiveChild,
    toggleSubmenu,
    expandToItem,
    expandToPath,
    collapseAll,
    expandAll,
    canAccessItem,
    hasRole,
    hasTenantRole,
    getUserPermissions,
    
    // Config
    config
  }
}

export default useNavigation
