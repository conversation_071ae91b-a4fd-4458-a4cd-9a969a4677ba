<template>
  <div class="export-toolbar-tab">
    <div class="export-toolbar-col">
      <div class="flex">
        <Button
          label="Image"
          show-label
          icon-size="xl"
          icon="fluent:image-24-regular"
          command="image"
        />
        <Button
          label="PDF"
          show-label
          icon-size="xl"
          icon="mdi:file-pdf-outline"
          command="pdf"
        />
        <Button
          label="Word"
          show-label
          icon-size="xl"
          icon="mdi:file-word-outline"
          command="word"
        />
        <Button
          label="Text"
          show-label
          icon-size="xl"
          icon="mdi:file-text-outline"
          command="text"
        />
      </div>
    </div>
    <Divider />
    <div class="export-toolbar-col">
      <div class="flex">
        <Button
          label="Share"
          show-label
          icon-size="xl"
          icon="heroicons:share"
          command="share"
        />
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import Button from "./Button.vue"; // Import Button component
import Divider from "./Divider.vue"; // Import Divider component
</script>

<style scoped>
@reference '~/assets/css/tailwind.css';
.export-toolbar-tab {
  @apply flex align-middle justify-center;
}
.export-toolbar-col {
  @apply flex flex-col space-y-2 align-middle justify-center;
}
</style>
