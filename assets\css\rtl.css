/**
 * RTL (Right-to-Left) Support Utilities
 * Provides CSS utilities for Hebrew and Arabic language support
 */

/* RTL Direction Classes */
.rtl {
  direction: rtl;
}

.ltr {
  direction: ltr;
}

/* Language-specific body classes for targeting */
.lang-he,
.lang-ar {
  direction: rtl;
}

.lang-en {
  direction: ltr;
}

/* RTL-aware spacing utilities */
.rtl .ml-auto {
  margin-left: unset;
  margin-right: auto;
}

.rtl .mr-auto {
  margin-right: unset;
  margin-left: auto;
}

.rtl .text-left {
  text-align: right;
}

.rtl .text-right {
  text-align: left;
}

/* RTL-aware flexbox utilities */
.rtl .justify-start {
  justify-content: flex-end;
}

.rtl .justify-end {
  justify-content: flex-start;
}

.rtl .items-start {
  align-items: flex-end;
}

.rtl .items-end {
  align-items: flex-start;
}

/* RTL-aware positioning */
.rtl .left-0 {
  left: unset;
  right: 0;
}

.rtl .right-0 {
  right: unset;
  left: 0;
}

.rtl .left-4 {
  left: unset;
  right: 1rem;
}

.rtl .right-4 {
  right: unset;
  left: 1rem;
}

/* RTL-aware borders */
.rtl .border-l {
  border-left: none;
  border-right: 1px solid;
}

.rtl .border-r {
  border-right: none;
  border-left: 1px solid;
}

.rtl .border-l-2 {
  border-left: none;
  border-right: 2px solid;
}

.rtl .border-r-2 {
  border-right: none;
  border-left: 2px solid;
}

/* RTL-aware rounded corners */
.rtl .rounded-l {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-top-right-radius: 0.25rem;
  border-bottom-right-radius: 0.25rem;
}

.rtl .rounded-r {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-top-left-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem;
}

.rtl .rounded-tl {
  border-top-left-radius: 0;
  border-top-right-radius: 0.25rem;
}

.rtl .rounded-tr {
  border-top-right-radius: 0;
  border-top-left-radius: 0.25rem;
}

.rtl .rounded-bl {
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0.25rem;
}

.rtl .rounded-br {
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0.25rem;
}

/* RTL-aware transforms */
.rtl .rotate-90 {
  transform: rotate(-90deg);
}

.rtl .rotate-180 {
  transform: rotate(180deg);
}

.rtl .-rotate-90 {
  transform: rotate(90deg);
}

/* RTL-aware icons and arrows */
.rtl .transform-flip-x {
  transform: scaleX(-1);
}

/* Common RTL patterns */
.rtl .dropdown-menu {
  left: unset;
  right: 0;
}

.rtl .sidebar {
  left: unset;
  right: 0;
}

.rtl .tooltip-left {
  left: unset;
  right: 100%;
}

.rtl .tooltip-right {
  right: unset;
  left: 100%;
}

/* Form elements RTL support */
.rtl input[type="text"],
.rtl input[type="email"],
.rtl input[type="password"],
.rtl textarea,
.rtl select {
  text-align: right;
}

.rtl input[type="number"] {
  text-align: left; /* Numbers should remain LTR */
}

/* Navigation RTL support */
.rtl .breadcrumb-separator::before {
  content: "\\";
  transform: scaleX(-1);
}

.rtl .chevron-right {
  transform: scaleX(-1);
}

.rtl .chevron-left {
  transform: scaleX(-1);
}

/* Table RTL support */
.rtl table {
  direction: rtl;
}

.rtl th,
.rtl td {
  text-align: right;
}

.rtl th:first-child,
.rtl td:first-child {
  border-radius: 0 0.375rem 0.375rem 0;
}

.rtl th:last-child,
.rtl td:last-child {
  border-radius: 0.375rem 0 0 0.375rem;
}

/* Animation adjustments for RTL */
.rtl .slide-enter-from {
  transform: translateX(100%);
}

.rtl .slide-leave-to {
  transform: translateX(100%);
}

.rtl .slide-left-enter-from {
  transform: translateX(100%);
}

.rtl .slide-left-leave-to {
  transform: translateX(100%);
}

.rtl .slide-right-enter-from {
  transform: translateX(-100%);
}

.rtl .slide-right-leave-to {
  transform: translateX(-100%);
}

/* Custom utilities for common RTL patterns */
.start-0 {
  inset-inline-start: 0;
}

.end-0 {
  inset-inline-end: 0;
}

.start-4 {
  inset-inline-start: 1rem;
}

.end-4 {
  inset-inline-end: 1rem;
}

.ms-auto {
  margin-inline-start: auto;
}

.me-auto {
  margin-inline-end: auto;
}

.ps-4 {
  padding-inline-start: 1rem;
}

.pe-4 {
  padding-inline-end: 1rem;
}

.border-s {
  border-inline-start-width: 1px;
}

.border-e {
  border-inline-end-width: 1px;
}

.rounded-s {
  border-start-start-radius: 0.25rem;
  border-end-start-radius: 0.25rem;
}

.rounded-e {
  border-start-end-radius: 0.25rem;
  border-end-end-radius: 0.25rem;
}
