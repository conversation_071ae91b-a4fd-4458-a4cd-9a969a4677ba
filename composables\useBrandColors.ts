/**
 * Brand Colors Composable
 * 
 * Provides easy access to brand color values and utilities
 * for the Legal SaaS platform.
 */

export interface BrandColorShade {
  50: string
  100: string
  200: string
  300: string
  400: string
  500: string
  600: string
  700: string
  800: string
  900: string
  950: string
}

export interface BrandColors {
  primary: BrandColorShade
  secondary: BrandColorShade
  success: BrandColorShade
  danger: BrandColorShade
  warning: BrandColorShade
  info: BrandColorShade
}

export const useBrandColors = () => {
  // Brand color definitions
  const brandColors: BrandColors = {
    primary: {
      50: '#eff6ff',
      100: '#dbeafe',
      200: '#bfdbfe',
      300: '#93c5fd',
      400: '#60a5fa',
      500: '#3b82f6',
      600: '#1a56db', // Default primary
      700: '#1e40af',
      800: '#1e3a8a',
      900: '#1e2a5e',
      950: '#172554'
    },
    secondary: {
      50: '#ecfeff',
      100: '#cffafe',
      200: '#a5f3fc',
      300: '#67e8f9',
      400: '#22d3ee',
      500: '#06b6d4', // Default secondary
      600: '#0891b2',
      700: '#0e7490',
      800: '#155e75',
      900: '#164e63',
      950: '#083344'
    },
    success: {
      50: '#ecfdf5',
      100: '#d1fae5',
      200: '#a7f3d0',
      300: '#6ee7b7',
      400: '#34d399',
      500: '#10b981', // Default success
      600: '#059669',
      700: '#047857',
      800: '#065f46',
      900: '#064e3b',
      950: '#022c22'
    },
    danger: {
      50: '#fef2f2',
      100: '#fee2e2',
      200: '#fecaca',
      300: '#fca5a5',
      400: '#f87171',
      500: '#ef4444', // Default danger
      600: '#dc2626',
      700: '#b91c1c',
      800: '#991b1b',
      900: '#7f1d1d',
      950: '#450a0a'
    },
    warning: {
      50: '#fffbeb',
      100: '#fef3c7',
      200: '#fde68a',
      300: '#fcd34d', // Default warning
      400: '#fbbf24',
      500: '#f59e0b',
      600: '#d97706',
      700: '#b45309',
      800: '#92400e',
      900: '#78350f',
      950: '#451a03'
    },
    info: {
      50: '#eff6ff',
      100: '#dbeafe',
      200: '#bfdbfe',
      300: '#93c5fd',
      400: '#60a5fa',
      500: '#3b82f6', // Default info
      600: '#2563eb',
      700: '#1d4ed8',
      800: '#1e40af',
      900: '#1e3a8a',
      950: '#172554'
    }
  }

  // Default color values (most commonly used shades)
  const defaultColors = {
    primary: brandColors.primary[600],
    secondary: brandColors.secondary[500],
    success: brandColors.success[500],
    danger: brandColors.danger[500],
    warning: brandColors.warning[300],
    info: brandColors.info[500]
  }

  /**
   * Get a specific color shade
   * @param color - Color name (primary, secondary, success, danger, warning, info)
   * @param shade - Shade number (50, 100, 200, ..., 950)
   * @returns Hex color value
   */
  const getColor = (color: keyof BrandColors, shade: keyof BrandColorShade): string => {
    return brandColors[color][shade]
  }

  /**
   * Get the default color for a brand color
   * @param color - Color name
   * @returns Hex color value
   */
  const getDefaultColor = (color: keyof BrandColors): string => {
    return defaultColors[color]
  }

  /**
   * Get CSS variable name for a color
   * @param color - Color name
   * @param shade - Shade number
   * @returns CSS variable name
   */
  const getCSSVariable = (color: keyof BrandColors, shade: keyof BrandColorShade): string => {
    const colorMap = {
      primary: 'brandPrimary',
      secondary: 'brandSecondary',
      success: 'brandSuccess',
      danger: 'brandDanger',
      warning: 'brandWarning',
      info: 'brandInfo'
    }
    return `--color-${colorMap[color]}-${shade}`
  }

  /**
   * Get Tailwind class name for a color
   * @param color - Color name
   * @param shade - Shade number
   * @param type - CSS property type (bg, text, border)
   * @returns Tailwind class name
   */
  const getTailwindClass = (
    color: keyof BrandColors, 
    shade: keyof BrandColorShade, 
    type: 'bg' | 'text' | 'border' = 'bg'
  ): string => {
    const colorMap = {
      primary: 'brandPrimary',
      secondary: 'brandSecondary',
      success: 'brandSuccess',
      danger: 'brandDanger',
      warning: 'brandWarning',
      info: 'brandInfo'
    }
    return `${type}-${colorMap[color]}-${shade}`
  }

  /**
   * Get appropriate text color for a background color
   * @param color - Background color name
   * @param shade - Background shade number
   * @returns Object with text color and Tailwind class
   */
  const getContrastTextColor = (color: keyof BrandColors, shade: keyof BrandColorShade) => {
    const shadeNumber = parseInt(shade.toString())
    
    // Light backgrounds (50-300) use dark text
    if (shadeNumber <= 300) {
      return {
        color: brandColors[color][800],
        tailwindClass: getTailwindClass(color, 800, 'text')
      }
    }
    // Medium backgrounds (400-600) use white text
    else if (shadeNumber <= 600) {
      return {
        color: '#ffffff',
        tailwindClass: 'text-white'
      }
    }
    // Dark backgrounds (700-950) use light text
    else {
      return {
        color: brandColors[color][50],
        tailwindClass: getTailwindClass(color, 50, 'text')
      }
    }
  }

  /**
   * Generate a complete color palette for a specific color
   * @param color - Color name
   * @returns Object with all shades and utilities
   */
  const getColorPalette = (color: keyof BrandColors) => {
    const palette = brandColors[color]
    const utilities = (Object.keys(palette) as unknown as Array<keyof BrandColorShade>).reduce((acc, shadeKey) => {
      acc[shadeKey] = {
        hex: palette[shadeKey],
        cssVariable: getCSSVariable(color, shadeKey),
        tailwind: {
          bg: getTailwindClass(color, shadeKey, 'bg'),
          text: getTailwindClass(color, shadeKey, 'text'),
          border: getTailwindClass(color, shadeKey, 'border')
        },
        contrast: getContrastTextColor(color, shadeKey)
      };
      return acc;
    }, {} as Record<keyof BrandColorShade, {
      hex: string;
      cssVariable: string;
      tailwind: { bg: string; text: string; border: string };
      contrast: { color: string; tailwindClass: string };
    }>);

    return {
      name: color,
      default: getDefaultColor(color),
      shades: palette,
      utilities
    }
  }

  /**
   * Get color value from CSS custom property
   * @param color - Color name
   * @param shade - Shade number
   * @returns Computed color value from CSS
   */
  const getComputedColor = (color: keyof BrandColors, shade: keyof BrandColorShade): string => {
    if (typeof window !== 'undefined') {
      const cssVar = getCSSVariable(color, shade)
      return getComputedStyle(document.documentElement).getPropertyValue(cssVar).trim()
    }
    return getColor(color, shade)
  }

  /**
   * Set a color value dynamically
   * @param color - Color name
   * @param shade - Shade number
   * @param value - New color value
   */
  const setColor = (color: keyof BrandColors, shade: keyof BrandColorShade, value: string): void => {
    if (typeof window !== 'undefined') {
      const cssVar = getCSSVariable(color, shade)
      document.documentElement.style.setProperty(cssVar, value)
    }
  }

  return {
    // Color data
    brandColors,
    defaultColors,
    
    // Utility functions
    getColor,
    getDefaultColor,
    getCSSVariable,
    getTailwindClass,
    getContrastTextColor,
    getColorPalette,
    getComputedColor,
    setColor
  }
}

// Type exports for external use
