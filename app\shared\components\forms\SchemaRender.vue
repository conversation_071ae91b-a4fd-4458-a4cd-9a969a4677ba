<template>
  <div v-for="(item, index) in schema" :key="index" class="space-y-8">
  

    <!-- Render FormsField -->
    <forms-field
      v-if="item.component"
      :name="item.name"
      :label="item.label"
      :component="item.component"
      :rules="item.rules"
      :props="item.props"
      :events="item.events"
      :showIf="item.showIf"
      :initialValue="item.initialValue"
      :slots="item.slots"
    />
    
 

    <!-- Render Async Components -->
    <forms-async-component
      v-if="item.asyncComponent"
      :component="item.asyncComponent"
      :props="item.props"
      :events="item.events"
      :showIf="item.showIf"
      :slots="item.slots"
    />

    <!-- Render FormsTabs -->
    <forms-tabs
      v-if="item.tabs"
      v-bind="item.tabs"
      :events="item.events"
      :showIf="item.showIf"
    >
      <template v-for="tab in item.tabs.tabs" :key="tab.value" v-slot:[tab.value]>
        <forms-schema-render :schema="tab.content" />
      </template>
    </forms-tabs>

    <forms-card
      v-if="item.card"
      v-bind="item.card"
      :events="item.events"
      :showIf="item.showIf"
    >
      <forms-schema-render :schema="item.card.content" />
    </forms-card>

    <forms-accordion
      v-if="item.accordion"
      v-bind="item.accordion"
      :events="item.events"
      :showIf="item.showIf"
    >
      <template
        v-for="accordion in item.accordion.accordions"
        :key="accordion.name"
        v-slot:[accordion.name]
      >
        <forms-schema-render :schema="accordion.content" />
      </template>
    </forms-accordion>

    <forms-grid
      v-if="item.grid"
      v-bind="item.grid"
      :events="item.events"
      :showIf="item.showIf"
    >
      <forms-schema-render :schema="item.grid.content" />
    </forms-grid>
  </div>
</template>
<script lang="ts" setup>
import type { PropType } from "vue";

const props = defineProps({
  schema: {
    type: Array as PropType<any[]>,
    required: true,
  },
});
</script>
