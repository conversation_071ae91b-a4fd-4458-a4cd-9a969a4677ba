// composables/useResponsive.ts
import { useUiStore } from '~/stores/ui';
import { onMounted, onUnmounted, ref, computed } from 'vue';

/**
 * Composable for handling responsive behavior and screen size detection
 */
export const useResponsive = () => {
  const uiStore = useUiStore();

  // Reactive refs for immediate access
  const screenWidth = ref(0);
  const screenHeight = ref(0);

  let resizeObserver: ResizeObserver | null = null;
  let timeoutId: number | null = null;

  // Debounced resize handler to avoid excessive updates
  const handleResize = () => {
    if (timeoutId) {
      clearTimeout(timeoutId);
    }

    timeoutId = window.setTimeout(() => {
      if (typeof window !== 'undefined') {
        const width = window.innerWidth;
        const height = window.innerHeight;

        screenWidth.value = width;
        screenHeight.value = height;
        uiStore.updateScreenSize(width, height);
      }
    }, 100); // 100ms debounce
  };

  // Initialize responsive behavior
  const initializeResponsive = () => {
    if (typeof window === 'undefined') return;

    // Set initial values
    screenWidth.value = window.innerWidth;
    screenHeight.value = window.innerHeight;

    // Update the UI store with initial screen size
    uiStore.updateScreenSize(window.innerWidth, window.innerHeight);

    // Add resize listener
    window.addEventListener('resize', handleResize, { passive: true });

    // Use ResizeObserver for more accurate detection if available
    if (typeof ResizeObserver !== 'undefined') {
      resizeObserver = new ResizeObserver((entries) => {
        for (const entry of entries) {
          const { width, height } = entry.contentRect;
          screenWidth.value = width;
          screenHeight.value = height;
          uiStore.updateScreenSize(width, height);
        }
      });

      resizeObserver.observe(document.documentElement);
    }
  };

  // Cleanup function
  const cleanupResponsive = () => {
    if (typeof window !== 'undefined') {
      window.removeEventListener('resize', handleResize);
    }

    if (resizeObserver) {
      resizeObserver.disconnect();
      resizeObserver = null;
    }

    if (timeoutId) {
      clearTimeout(timeoutId);
      timeoutId = null;
    }
  };

  // Auto-initialize on mount and cleanup on unmount
  onMounted(() => {
    initializeResponsive();
  });

  onUnmounted(() => {
    cleanupResponsive();
  });

  // Computed properties for reactive access to store state
  const isMobile = computed(() => uiStore.isMobile);
  const isTablet = computed(() => uiStore.isTablet);
  const isDesktop = computed(() => uiStore.isDesktop);

  const isBreakpoint = (breakpoint: string) => {
    const width = screenWidth.value || uiStore.screenWidth;
    switch (breakpoint) {
      case 'xs': return width < 640;
      case 'sm': return width >= 640 && width < 768;
      case 'md': return width >= 768 && width < 1024;
      case 'lg': return width >= 1024 && width < 1280;
      case 'xl': return width >= 1280 && width < 1536;
      case '2xl': return width >= 1536;
      default: return false;
    }
  };

  const isBreakpointAndUp = (breakpoint: string) => {
    const width = screenWidth.value || uiStore.screenWidth;
    switch (breakpoint) {
      case 'sm': return width >= 640;
      case 'md': return width >= 768;
      case 'lg': return width >= 1024;
      case 'xl': return width >= 1280;
      case '2xl': return width >= 1536;
      default: return true;
    }
  };

  const isBreakpointAndDown = (breakpoint: string) => {
    const width = screenWidth.value || uiStore.screenWidth;
    switch (breakpoint) {
      case 'xs': return width < 640;
      case 'sm': return width < 768;
      case 'md': return width < 1024;
      case 'lg': return width < 1280;
      case 'xl': return width < 1536;
      default: return false;
    }
  };

  return {
    // Reactive state
    screenWidth: computed(() => screenWidth.value || uiStore.screenWidth),
    screenHeight: computed(() => screenHeight.value || uiStore.screenHeight),

    // Store getters (reactive)
    isMobile,
    isTablet,
    isDesktop,

    // Utility functions
    isBreakpoint,
    isBreakpointAndUp,
    isBreakpointAndDown,

    // Manual initialization (if needed)
    initializeResponsive,
    cleanupResponsive,

    // Direct access to store
    uiStore,
  };
};

/**
 * Simple hook for getting current screen size without full responsive setup
 */
export const useScreenSize = () => {
  const uiStore = useUiStore();

  return {
    width: computed(() => uiStore.screenWidth),
    height: computed(() => uiStore.screenHeight),
    isMobile: computed(() => uiStore.isMobile),
    isTablet: computed(() => uiStore.isTablet),
    isDesktop: computed(() => uiStore.isDesktop),
    breakpoint: computed(() => uiStore.breakpoint),
  };
};
