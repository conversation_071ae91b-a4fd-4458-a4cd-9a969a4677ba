<template>
  <div class="space-y-8">
    <!-- Performance Header -->
    <div class="bg-gradient-to-r from-green-600 to-blue-600 rounded-xl p-8 text-white">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-3xl font-bold mb-2">Performance Analytics</h1>
          <p class="text-green-100 text-lg">
            Monitor system performance, response times, and service reliability
          </p>
        </div>
        <div class="hidden md:flex items-center gap-4">
          <UiSelect
            v-model="selectedTimeRange"
            :options="timeRangeOptions"
            class="bg-white/10 border-white/20 text-white"
          />
          <UiButton @click="runPerformanceTest" variant="outline" class="border-white/20 text-white hover:bg-white/10" :loading="runningTest">
            <Icon name="material-symbols:speed" class="h-4 w-4 mr-2" />
            Run Test
          </UiButton>
        </div>
      </div>
    </div>

    <!-- Performance Metrics -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <!-- Response Time -->
      <UiCard
        icon="material-symbols:timer"
        icon-color="green"
        :loading="isLoading"
        hover-effect
      >
        <template #header>
          <div class="flex items-center justify-between w-full">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Avg Response</h3>
            <UiBadge :variant="getResponseTimeVariant(performance.responseTime.avg)">
              {{ performance.responseTime.avg }}ms
            </UiBadge>
          </div>
        </template>
        <div class="space-y-3">
          <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ performance.responseTime.avg }}ms</p>
          <div class="space-y-2">
            <div class="flex justify-between text-sm">
              <span class="text-gray-500 dark:text-gray-400">P95</span>
              <span class="text-gray-900 dark:text-white">{{ performance.responseTime.p95 }}ms</span>
            </div>
            <div class="flex justify-between text-sm">
              <span class="text-gray-500 dark:text-gray-400">P99</span>
              <span class="text-gray-900 dark:text-white">{{ performance.responseTime.p99 }}ms</span>
            </div>
          </div>
        </div>
      </UiCard>

      <!-- Throughput -->
      <UiCard
        icon="material-symbols:trending-up"
        icon-color="blue"
        :loading="isLoading"
        hover-effect
      >
        <template #header>
          <div class="flex items-center justify-between w-full">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Throughput</h3>
            <UiBadge variant="info">{{ performance.throughput.current }} req/s</UiBadge>
          </div>
        </template>
        <div class="space-y-3">
          <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ performance.throughput.current }}</p>
          <p class="text-sm text-gray-500 dark:text-gray-400">requests per second</p>
          <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div 
              class="bg-blue-600 h-2 rounded-full transition-all duration-300"
              :style="{ width: `${Math.min((performance.throughput.current / performance.throughput.max) * 100, 100)}%` }"
            ></div>
          </div>
        </div>
      </UiCard>

      <!-- Error Rate -->
      <UiCard
        icon="material-symbols:error"
        icon-color="red"
        :loading="isLoading"
        hover-effect
      >
        <template #header>
          <div class="flex items-center justify-between w-full">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Error Rate</h3>
            <UiBadge :variant="getErrorRateVariant(performance.errorRate.current)">
              {{ performance.errorRate.current }}%
            </UiBadge>
          </div>
        </template>
        <div class="space-y-3">
          <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ performance.errorRate.current }}%</p>
          <div class="space-y-2">
            <div class="flex justify-between text-sm">
              <span class="text-gray-500 dark:text-gray-400">24h Avg</span>
              <span class="text-gray-900 dark:text-white">{{ performance.errorRate.avg24h }}%</span>
            </div>
            <div class="flex justify-between text-sm">
              <span class="text-gray-500 dark:text-gray-400">7d Avg</span>
              <span class="text-gray-900 dark:text-white">{{ performance.errorRate.avg7d }}%</span>
            </div>
          </div>
        </div>
      </UiCard>

      <!-- Uptime -->
      <UiCard
        icon="material-symbols:schedule"
        icon-color="purple"
        :loading="isLoading"
        hover-effect
      >
        <template #header>
          <div class="flex items-center justify-between w-full">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Uptime</h3>
            <UiBadge variant="success">{{ performance.uptime.current }}%</UiBadge>
          </div>
        </template>
        <div class="space-y-3">
          <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ performance.uptime.current }}%</p>
          <div class="space-y-2">
            <div class="flex justify-between text-sm">
              <span class="text-gray-500 dark:text-gray-400">This Month</span>
              <span class="text-gray-900 dark:text-white">{{ performance.uptime.thisMonth }}%</span>
            </div>
            <div class="flex justify-between text-sm">
              <span class="text-gray-500 dark:text-gray-400">This Year</span>
              <span class="text-gray-900 dark:text-white">{{ performance.uptime.thisYear }}%</span>
            </div>
          </div>
        </div>
      </UiCard>
    </div>

    <!-- Performance Charts -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Response Time Trends -->
      <UiCard>
        <template #header>
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Response Time Trends</h3>
            <div class="flex items-center gap-2">
              <UiButton @click="refreshCharts" variant="ghost" size="sm" :loading="refreshingCharts">
                <Icon name="material-symbols:refresh" class="h-4 w-4" />
              </UiButton>
              <UiSelect
                v-model="chartTimeRange"
                :options="chartTimeRangeOptions"
                size="sm"
              />
            </div>
          </div>
        </template>
        <div class="h-80">
          <!-- Chart placeholder -->
          <div class="w-full h-full bg-gradient-to-br from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20 rounded-lg flex items-center justify-center">
            <div class="text-center">
              <Icon name="material-symbols:show-chart" class="h-16 w-16 text-green-500 mx-auto mb-4" />
              <p class="text-gray-600 dark:text-gray-400">Response Time Chart</p>
              <p class="text-sm text-gray-500 dark:text-gray-500">Real-time performance monitoring</p>
            </div>
          </div>
        </div>
      </UiCard>

      <!-- Throughput Analysis -->
      <UiCard>
        <template #header>
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Throughput Analysis</h3>
            <UiBadge variant="info">Peak: {{ performance.throughput.peak }} req/s</UiBadge>
          </div>
        </template>
        <div class="h-80">
          <!-- Chart placeholder -->
          <div class="w-full h-full bg-gradient-to-br from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-lg flex items-center justify-center">
            <div class="text-center">
              <Icon name="material-symbols:analytics" class="h-16 w-16 text-blue-500 mx-auto mb-4" />
              <p class="text-gray-600 dark:text-gray-400">Throughput Chart</p>
              <p class="text-sm text-gray-500 dark:text-gray-500">Request volume over time</p>
            </div>
          </div>
        </div>
      </UiCard>
    </div>

    <!-- Service Performance -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Service Response Times -->
      <UiCard>
        <template #header>
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Service Performance</h3>
            <UiButton @click="refreshServices" variant="ghost" size="sm" :loading="refreshingServices">
              <Icon name="material-symbols:refresh" class="h-4 w-4" />
            </UiButton>
          </div>
        </template>
        <div class="space-y-4">
          <div
            v-for="service in services"
            :key="service.name"
            class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg"
          >
            <div class="flex items-center gap-3">
              <div :class="[
                'w-3 h-3 rounded-full',
                service.status === 'healthy' ? 'bg-green-500' :
                service.status === 'degraded' ? 'bg-yellow-500' :
                'bg-red-500'
              ]"></div>
              <div>
                <p class="text-sm font-medium text-gray-900 dark:text-white">{{ service.name }}</p>
                <p class="text-xs text-gray-500 dark:text-gray-400">{{ service.description }}</p>
              </div>
            </div>
            <div class="text-right">
              <p class="text-sm font-medium text-gray-900 dark:text-white">{{ service.responseTime }}ms</p>
              <p class="text-xs text-gray-500 dark:text-gray-400">{{ service.requests }}/min</p>
            </div>
          </div>
        </div>
      </UiCard>

      <!-- Performance Alerts -->
      <UiCard>
        <template #header>
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Performance Alerts</h3>
            <UiBadge :variant="activeAlerts.length > 0 ? 'warning' : 'success'">
              {{ activeAlerts.length }} active
            </UiBadge>
          </div>
        </template>
        <div class="space-y-3">
          <div
            v-for="alert in performanceAlerts"
            :key="alert.id"
            class="flex items-start gap-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg"
          >
            <div class="flex-shrink-0">
              <div :class="[
                'w-8 h-8 rounded-full flex items-center justify-center',
                alert.severity === 'critical' ? 'bg-red-100 dark:bg-red-900/20' :
                alert.severity === 'warning' ? 'bg-yellow-100 dark:bg-yellow-900/20' :
                'bg-blue-100 dark:bg-blue-900/20'
              ]">
                <Icon :name="alert.icon" :class="[
                  'h-4 w-4',
                  alert.severity === 'critical' ? 'text-red-600 dark:text-red-400' :
                  alert.severity === 'warning' ? 'text-yellow-600 dark:text-yellow-400' :
                  'text-blue-600 dark:text-blue-400'
                ]" />
              </div>
            </div>
            <div class="flex-1 min-w-0">
              <p class="text-sm text-gray-900 dark:text-white">{{ alert.message }}</p>
              <p class="text-xs text-gray-500 dark:text-gray-400">{{ alert.timestamp }}</p>
            </div>
            <UiBadge :variant="alert.severity === 'critical' ? 'error' : alert.severity === 'warning' ? 'warning' : 'info'">
              {{ alert.severity }}
            </UiBadge>
          </div>

          <div v-if="performanceAlerts.length === 0" class="text-center py-6">
            <Icon name="material-symbols:check-circle" class="h-8 w-8 text-green-500 mx-auto mb-2" />
            <p class="text-sm text-gray-500 dark:text-gray-400">No performance alerts</p>
          </div>
        </div>
      </UiCard>
    </div>

    <!-- Performance Recommendations -->
    <UiCard>
      <template #header>
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Performance Recommendations</h3>
          <UiBadge variant="info">{{ recommendations.length }} recommendations</UiBadge>
        </div>
      </template>
      <div class="space-y-4">
        <div
          v-for="recommendation in recommendations"
          :key="recommendation.id"
          class="flex items-start gap-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg"
        >
          <div :class="[
            'w-10 h-10 rounded-full flex items-center justify-center flex-shrink-0',
            recommendation.priority === 'high' ? 'bg-red-100 dark:bg-red-900/20' :
            recommendation.priority === 'medium' ? 'bg-yellow-100 dark:bg-yellow-900/20' :
            'bg-blue-100 dark:bg-blue-900/20'
          ]">
            <Icon :name="recommendation.icon" :class="[
              'h-5 w-5',
              recommendation.priority === 'high' ? 'text-red-600 dark:text-red-400' :
              recommendation.priority === 'medium' ? 'text-yellow-600 dark:text-yellow-400' :
              'text-blue-600 dark:text-blue-400'
            ]" />
          </div>
          <div class="flex-1">
            <div class="flex items-center gap-2 mb-1">
              <h4 class="text-sm font-medium text-gray-900 dark:text-white">{{ recommendation.title }}</h4>
              <UiBadge :variant="recommendation.priority === 'high' ? 'error' : recommendation.priority === 'medium' ? 'warning' : 'info'" size="sm">
                {{ recommendation.priority }}
              </UiBadge>
            </div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">{{ recommendation.description }}</p>
            <div class="flex items-center gap-2">
              <UiButton @click="handleRecommendationAction(recommendation)" variant="outline" size="sm">
                {{ recommendation.action }}
              </UiButton>
              <span class="text-xs text-gray-500 dark:text-gray-400">
                Impact: {{ recommendation.impact }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </UiCard>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { PlatformRoles } from '~/app/features/auth/constants/roles'

// Page meta configuration
definePageMeta({
  layout: 'dashboard',
  title: 'Performance Analytics',
  description: 'Monitor system performance, response times, and service reliability',
  pageHeaderIcon: 'material-symbols:speed',
  pageHeaderStats: [
    { key: 'response', label: 'Avg Response', value: '145ms', color: 'green' },
    { key: 'throughput', label: 'Throughput', value: '2.4K req/s', color: 'blue' },
    { key: 'uptime', label: 'Uptime', value: '99.9%', color: 'purple' },
    { key: 'errors', label: 'Error Rate', value: '0.05%', color: 'red' }
  ],
  showRealTimeStatus: true,
  autoRefreshEnabled: true,
  refreshInterval: 30000, // 30 seconds
  middleware: ['rbac'],
  roles: [PlatformRoles.SUPER_ADMIN],
  breadcrumb: [
    { label: 'Dashboard', href: '/dashboard' },
    { label: 'Platform', href: '/dashboard/platform' },
    { label: 'Analytics', href: '/dashboard/platform/analytics' },
    { label: 'Performance' },
  ],
})

// Reactive state
const isLoading = ref(true)
const refreshingCharts = ref(false)
const refreshingServices = ref(false)
const runningTest = ref(false)
const selectedTimeRange = ref('24h')
const chartTimeRange = ref('1h')
const refreshInterval = ref<NodeJS.Timeout | null>(null)

// Time range options
const timeRangeOptions = [
  { value: '1h', label: 'Last hour' },
  { value: '24h', label: 'Last 24 hours' },
  { value: '7d', label: 'Last 7 days' },
  { value: '30d', label: 'Last 30 days' }
]

const chartTimeRangeOptions = [
  { value: '15m', label: '15 minutes' },
  { value: '1h', label: '1 hour' },
  { value: '6h', label: '6 hours' },
  { value: '24h', label: '24 hours' }
]

// Performance metrics
const performance = reactive({
  responseTime: {
    avg: 145,
    p95: 280,
    p99: 450
  },
  throughput: {
    current: 2400,
    max: 5000,
    peak: 3200
  },
  errorRate: {
    current: 0.05,
    avg24h: 0.08,
    avg7d: 0.12
  },
  uptime: {
    current: 99.9,
    thisMonth: 99.8,
    thisYear: 99.7
  }
})

// Services performance data
const services = ref([
  {
    name: 'API Gateway',
    description: 'Core API routing',
    status: 'healthy',
    responseTime: 120,
    requests: 1200
  },
  {
    name: 'Database',
    description: 'Primary database cluster',
    status: 'healthy',
    responseTime: 45,
    requests: 800
  },
  {
    name: 'File Storage',
    description: 'Document storage service',
    status: 'degraded',
    responseTime: 280,
    requests: 450
  },
  {
    name: 'Search Engine',
    description: 'Document indexing',
    status: 'healthy',
    responseTime: 156,
    requests: 320
  },
  {
    name: 'Email Service',
    description: 'Notification delivery',
    status: 'healthy',
    responseTime: 234,
    requests: 150
  },
  {
    name: 'Authentication',
    description: 'User auth service',
    status: 'healthy',
    responseTime: 78,
    requests: 600
  }
])

// Performance alerts
const performanceAlerts = ref([
  {
    id: 1,
    message: 'File Storage service response time above threshold (280ms)',
    severity: 'warning',
    icon: 'material-symbols:warning',
    timestamp: '5 minutes ago'
  },
  {
    id: 2,
    message: 'Database connection pool utilization at 85%',
    severity: 'warning',
    icon: 'material-symbols:database',
    timestamp: '12 minutes ago'
  }
])

// Performance recommendations
const recommendations = ref([
  {
    id: 1,
    title: 'Optimize Database Queries',
    description: 'Several slow queries detected in the user management module. Consider adding indexes or optimizing query structure.',
    priority: 'high',
    icon: 'material-symbols:database',
    action: 'Review Queries',
    impact: 'High - 30% response time improvement'
  },
  {
    id: 2,
    title: 'Scale File Storage Service',
    description: 'File storage service is experiencing high latency. Consider adding more storage nodes or implementing caching.',
    priority: 'medium',
    icon: 'material-symbols:storage',
    action: 'Scale Service',
    impact: 'Medium - 20% latency reduction'
  },
  {
    id: 3,
    title: 'Implement CDN for Static Assets',
    description: 'Static assets are being served directly from the application server. A CDN would improve load times.',
    priority: 'low',
    icon: 'material-symbols:cloud',
    action: 'Setup CDN',
    impact: 'Low - 10% faster page loads'
  },
  {
    id: 4,
    title: 'Enable Response Compression',
    description: 'API responses are not compressed. Enabling gzip compression would reduce bandwidth usage.',
    priority: 'medium',
    icon: 'material-symbols:compress',
    action: 'Enable Compression',
    impact: 'Medium - 40% bandwidth reduction'
  }
])

// Computed properties
const activeAlerts = computed(() =>
  performanceAlerts.value.filter(alert => alert.severity === 'critical' || alert.severity === 'warning')
)

// Utility functions
const getResponseTimeVariant = (responseTime: number) => {
  if (responseTime <= 200) return 'success'
  if (responseTime <= 500) return 'warning'
  return 'error'
}

const getErrorRateVariant = (errorRate: number) => {
  if (errorRate <= 0.1) return 'success'
  if (errorRate <= 1) return 'warning'
  return 'error'
}

// Methods
const runPerformanceTest = async () => {
  try {
    runningTest.value = true

    // Simulate performance test
    console.log('Running performance test...')
    await new Promise(resolve => setTimeout(resolve, 3000))

    // In a real app, you would trigger a performance test
    // const testResults = await $api.post('/platform/performance/test')

    // Simulate test results affecting metrics
    performance.responseTime.avg = Math.max(100, performance.responseTime.avg + (Math.random() - 0.5) * 50)
    performance.throughput.current = Math.max(1000, performance.throughput.current + (Math.random() - 0.5) * 500)

    console.log('Performance test completed')

  } catch (error) {
    console.error('Error running performance test:', error)
  } finally {
    runningTest.value = false
  }
}

const refreshCharts = async () => {
  try {
    refreshingCharts.value = true

    // Simulate API call to refresh chart data
    await new Promise(resolve => setTimeout(resolve, 1500))

    // In a real app, you would fetch fresh performance data
    // const chartData = await $api.get('/platform/performance/charts', { timeRange: chartTimeRange.value })

    console.log('Performance charts refreshed')

  } catch (error) {
    console.error('Error refreshing charts:', error)
  } finally {
    refreshingCharts.value = false
  }
}

const refreshServices = async () => {
  try {
    refreshingServices.value = true

    // Simulate API call to refresh service performance
    await new Promise(resolve => setTimeout(resolve, 1000))

    // In a real app, you would fetch fresh service metrics
    // const serviceMetrics = await $api.get('/platform/services/performance')

    // Simulate some metric updates
    services.value.forEach(service => {
      if (Math.random() > 0.8) {
        service.responseTime = Math.max(50, service.responseTime + (Math.random() - 0.5) * 100)
        service.requests = Math.max(100, service.requests + (Math.random() - 0.5) * 200)
      }
    })

    console.log('Service performance refreshed')

  } catch (error) {
    console.error('Error refreshing services:', error)
  } finally {
    refreshingServices.value = false
  }
}

const handleRecommendationAction = (recommendation: any) => {
  console.log('Handling recommendation action:', recommendation.action)

  switch (recommendation.action) {
    case 'Review Queries':
      navigateTo('/dashboard/platform/database/queries')
      break
    case 'Scale Service':
      navigateTo('/dashboard/platform/services/scaling')
      break
    case 'Setup CDN':
      navigateTo('/dashboard/platform/cdn/setup')
      break
    case 'Enable Compression':
      navigateTo('/dashboard/platform/settings#performance')
      break
    default:
      console.log('Unknown action:', recommendation.action)
  }
}

const fetchPerformanceData = async () => {
  try {
    // In a real app, fetch performance data from monitoring APIs
    // const data = await $api.get('/platform/performance/metrics', {
    //   timeRange: selectedTimeRange.value
    // })

    // Simulate real-time metric updates
    performance.responseTime.avg = Math.max(100, Math.min(300, performance.responseTime.avg + (Math.random() - 0.5) * 20))
    performance.throughput.current = Math.max(1000, Math.min(5000, performance.throughput.current + (Math.random() - 0.5) * 200))
    performance.errorRate.current = Math.max(0, Math.min(2, performance.errorRate.current + (Math.random() - 0.5) * 0.1))

  } catch (error) {
    console.error('Error fetching performance data:', error)
  }
}

// Lifecycle hooks
onMounted(async () => {
  // Initial data load
  await fetchPerformanceData()
  isLoading.value = false

  // Set up auto-refresh interval
  refreshInterval.value = setInterval(fetchPerformanceData, 30000)
})

onUnmounted(() => {
  if (refreshInterval.value) {
    clearInterval(refreshInterval.value)
  }
})
</script>
