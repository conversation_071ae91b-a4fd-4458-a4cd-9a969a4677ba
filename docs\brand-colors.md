# Brand Color System

This document outlines the comprehensive brand color system for the Legal SaaS platform, featuring complete color palettes from 50 to 950 for all brand colors.

## Color Palettes

### Brand Primary (Blue)
The primary brand color used for main actions, links, and brand elements.

| Shade | Hex Code | CSS Variable | Tailwind Class | Usage |
|-------|----------|--------------|----------------|-------|
| 50 | `#eff6ff` | `--color-brandPrimary-50` | `bg-brandPrimary-50` | Very light backgrounds |
| 100 | `#dbeafe` | `--color-brandPrimary-100` | `bg-brandPrimary-100` | Light backgrounds, badges |
| 200 | `#bfdbfe` | `--color-brandPrimary-200` | `bg-brandPrimary-200` | Borders, dividers |
| 300 | `#93c5fd` | `--color-brandPrimary-300` | `bg-brandPrimary-300` | Disabled states |
| 400 | `#60a5fa` | `--color-brandPrimary-400` | `bg-brandPrimary-400` | Hover states |
| 500 | `#3b82f6` | `--color-brandPrimary-500` | `bg-brandPrimary-500` | Default buttons |
| **600** | `#1a56db` | `--color-brandPrimary-600` | `bg-brandPrimary-600` | **Primary brand color** |
| 700 | `#1e40af` | `--color-brandPrimary-700` | `bg-brandPrimary-700` | Hover states, dark mode |
| 800 | `#1e3a8a` | `--color-brandPrimary-800` | `bg-brandPrimary-800` | Text on light backgrounds |
| 900 | `#1e2a5e` | `--color-brandPrimary-900` | `bg-brandPrimary-900` | Dark text |
| 950 | `#172554` | `--color-brandPrimary-950` | `bg-brandPrimary-950` | Very dark text |

### Brand Secondary (Cyan)
Secondary brand color for accents and complementary elements.

| Shade | Hex Code | CSS Variable | Tailwind Class | Usage |
|-------|----------|--------------|----------------|-------|
| 50 | `#ecfeff` | `--color-brandSecondary-50` | `bg-brandSecondary-50` | Very light backgrounds |
| 100 | `#cffafe` | `--color-brandSecondary-100` | `bg-brandSecondary-100` | Light backgrounds |
| 200 | `#a5f3fc` | `--color-brandSecondary-200` | `bg-brandSecondary-200` | Borders |
| 300 | `#67e8f9` | `--color-brandSecondary-300` | `bg-brandSecondary-300` | Accents |
| 400 | `#22d3ee` | `--color-brandSecondary-400` | `bg-brandSecondary-400` | Highlights |
| **500** | `#06b6d4` | `--color-brandSecondary-500` | `bg-brandSecondary-500` | **Secondary brand color** |
| 600 | `#0891b2` | `--color-brandSecondary-600` | `bg-brandSecondary-600` | Hover states |
| 700 | `#0e7490` | `--color-brandSecondary-700` | `bg-brandSecondary-700` | Text on white |
| 800 | `#155e75` | `--color-brandSecondary-800` | `bg-brandSecondary-800` | Dark text |
| 900 | `#164e63` | `--color-brandSecondary-900` | `bg-brandSecondary-900` | Very dark text |
| 950 | `#083344` | `--color-brandSecondary-950` | `bg-brandSecondary-950` | Darkest shade |

### Brand Success (Green)
Used for success states, confirmations, and positive actions.

| Shade | Hex Code | CSS Variable | Tailwind Class |
|-------|----------|--------------|----------------|
| 50 | `#ecfdf5` | `--color-brandSuccess-50` | `bg-brandSuccess-50` |
| 100 | `#d1fae5` | `--color-brandSuccess-100` | `bg-brandSuccess-100` |
| 200 | `#a7f3d0` | `--color-brandSuccess-200` | `bg-brandSuccess-200` |
| 300 | `#6ee7b7` | `--color-brandSuccess-300` | `bg-brandSuccess-300` |
| 400 | `#34d399` | `--color-brandSuccess-400` | `bg-brandSuccess-400` |
| **500** | `#10b981` | `--color-brandSuccess-500` | `bg-brandSuccess-500` |
| 600 | `#059669` | `--color-brandSuccess-600` | `bg-brandSuccess-600` |
| 700 | `#047857` | `--color-brandSuccess-700` | `bg-brandSuccess-700` |
| 800 | `#065f46` | `--color-brandSuccess-800` | `bg-brandSuccess-800` |
| 900 | `#064e3b` | `--color-brandSuccess-900` | `bg-brandSuccess-900` |
| 950 | `#022c22` | `--color-brandSuccess-950` | `bg-brandSuccess-950` |

### Brand Danger (Red)
Used for error states, warnings, and destructive actions.

| Shade | Hex Code | CSS Variable | Tailwind Class |
|-------|----------|--------------|----------------|
| 50 | `#fef2f2` | `--color-brandDanger-50` | `bg-brandDanger-50` |
| 100 | `#fee2e2` | `--color-brandDanger-100` | `bg-brandDanger-100` |
| 200 | `#fecaca` | `--color-brandDanger-200` | `bg-brandDanger-200` |
| 300 | `#fca5a5` | `--color-brandDanger-300` | `bg-brandDanger-300` |
| 400 | `#f87171` | `--color-brandDanger-400` | `bg-brandDanger-400` |
| **500** | `#ef4444` | `--color-brandDanger-500` | `bg-brandDanger-500` |
| 600 | `#dc2626` | `--color-brandDanger-600` | `bg-brandDanger-600` |
| 700 | `#b91c1c` | `--color-brandDanger-700` | `bg-brandDanger-700` |
| 800 | `#991b1b` | `--color-brandDanger-800` | `bg-brandDanger-800` |
| 900 | `#7f1d1d` | `--color-brandDanger-900` | `bg-brandDanger-900` |
| 950 | `#450a0a` | `--color-brandDanger-950` | `bg-brandDanger-950` |

### Brand Warning (Yellow)
Used for warning states and cautionary messages.

| Shade | Hex Code | CSS Variable | Tailwind Class |
|-------|----------|--------------|----------------|
| 50 | `#fffbeb` | `--color-brandWarning-50` | `bg-brandWarning-50` |
| 100 | `#fef3c7` | `--color-brandWarning-100` | `bg-brandWarning-100` |
| 200 | `#fde68a` | `--color-brandWarning-200` | `bg-brandWarning-200` |
| **300** | `#fcd34d` | `--color-brandWarning-300` | `bg-brandWarning-300` |
| 400 | `#fbbf24` | `--color-brandWarning-400` | `bg-brandWarning-400` |
| 500 | `#f59e0b` | `--color-brandWarning-500` | `bg-brandWarning-500` |
| 600 | `#d97706` | `--color-brandWarning-600` | `bg-brandWarning-600` |
| 700 | `#b45309` | `--color-brandWarning-700` | `bg-brandWarning-700` |
| 800 | `#92400e` | `--color-brandWarning-800` | `bg-brandWarning-800` |
| 900 | `#78350f` | `--color-brandWarning-900` | `bg-brandWarning-900` |
| 950 | `#451a03` | `--color-brandWarning-950` | `bg-brandWarning-950` |

### Brand Info (Blue)
Used for informational messages and neutral states.

| Shade | Hex Code | CSS Variable | Tailwind Class |
|-------|----------|--------------|----------------|
| 50 | `#eff6ff` | `--color-brandInfo-50` | `bg-brandInfo-50` |
| 100 | `#dbeafe` | `--color-brandInfo-100` | `bg-brandInfo-100` |
| 200 | `#bfdbfe` | `--color-brandInfo-200` | `bg-brandInfo-200` |
| 300 | `#93c5fd` | `--color-brandInfo-300` | `bg-brandInfo-300` |
| 400 | `#60a5fa` | `--color-brandInfo-400` | `bg-brandInfo-400` |
| **500** | `#3b82f6` | `--color-brandInfo-500` | `bg-brandInfo-500` |
| 600 | `#2563eb` | `--color-brandInfo-600` | `bg-brandInfo-600` |
| 700 | `#1d4ed8` | `--color-brandInfo-700` | `bg-brandInfo-700` |
| 800 | `#1e40af` | `--color-brandInfo-800` | `bg-brandInfo-800` |
| 900 | `#1e3a8a` | `--color-brandInfo-900` | `bg-brandInfo-900` |
| 950 | `#172554` | `--color-brandInfo-950` | `bg-brandInfo-950` |

## Usage Guidelines

### Buttons
```html
<!-- Primary buttons -->
<button class="bg-brandPrimary-600 hover:bg-brandPrimary-700 text-white">Primary</button>
<button class="bg-brandPrimary-500 hover:bg-brandPrimary-600 text-white">Primary Light</button>

<!-- Secondary buttons -->
<button class="bg-brandSecondary-500 hover:bg-brandSecondary-600 text-white">Secondary</button>

<!-- State buttons -->
<button class="bg-brandSuccess-500 hover:bg-brandSuccess-600 text-white">Success</button>
<button class="bg-brandDanger-500 hover:bg-brandDanger-600 text-white">Danger</button>
<button class="bg-brandWarning-500 hover:bg-brandWarning-600 text-white">Warning</button>
```

### Badges and Tags
```html
<!-- Light badges -->
<span class="bg-brandPrimary-100 text-brandPrimary-800 px-3 py-1 rounded-full">Primary</span>
<span class="bg-brandSuccess-100 text-brandSuccess-800 px-3 py-1 rounded-full">Success</span>
<span class="bg-brandDanger-100 text-brandDanger-800 px-3 py-1 rounded-full">Error</span>

<!-- Solid badges -->
<span class="bg-brandPrimary-600 text-white px-3 py-1 rounded-full">Primary</span>
```

### Alerts and Notifications
```html
<!-- Success alert -->
<div class="bg-brandSuccess-50 border border-brandSuccess-200 text-brandSuccess-800 p-4 rounded-lg">
  Success message
</div>

<!-- Error alert -->
<div class="bg-brandDanger-50 border border-brandDanger-200 text-brandDanger-800 p-4 rounded-lg">
  Error message
</div>

<!-- Warning alert -->
<div class="bg-brandWarning-50 border border-brandWarning-200 text-brandWarning-800 p-4 rounded-lg">
  Warning message
</div>
```

### Text Colors
```html
<!-- Primary text -->
<p class="text-brandPrimary-600">Primary text</p>
<p class="text-brandPrimary-800">Dark primary text</p>

<!-- State text -->
<p class="text-brandSuccess-600">Success text</p>
<p class="text-brandDanger-600">Error text</p>
<p class="text-brandWarning-600">Warning text</p>
```

### Borders
```html
<!-- Colored borders -->
<div class="border border-brandPrimary-200">Primary border</div>
<div class="border-l-4 border-brandSuccess-500">Success left border</div>
<div class="border-t-2 border-brandDanger-400">Danger top border</div>
```

## Accessibility

### Contrast Ratios
All color combinations meet WCAG 2.1 AA standards:

- **Light backgrounds (50-200)**: Use dark text (700-950)
- **Medium backgrounds (300-600)**: Use white or very dark text
- **Dark backgrounds (700-950)**: Use light text (50-200) or white

### Color Blindness
The color palette is designed to be distinguishable for users with color vision deficiencies:

- **Primary (Blue)** and **Danger (Red)** provide sufficient contrast
- **Success (Green)** and **Warning (Yellow)** are distinguishable
- Always use additional visual cues (icons, text) alongside color

## Implementation

### CSS Variables
All colors are available as CSS custom properties:

```css
/* Use in custom CSS */
.custom-element {
  background-color: var(--color-brandPrimary-600);
  color: var(--color-brandPrimary-50);
  border-color: var(--color-brandPrimary-200);
}
```

### Tailwind Classes
All colors are available as Tailwind utility classes:

```html
<!-- Background colors -->
<div class="bg-brandPrimary-50">Very light background</div>
<div class="bg-brandPrimary-600">Primary background</div>

<!-- Text colors -->
<p class="text-brandPrimary-800">Primary text</p>

<!-- Border colors -->
<div class="border border-brandPrimary-200">Primary border</div>
```

### JavaScript/TypeScript
Colors can be accessed programmatically:

```typescript
// Get computed color value
const primaryColor = getComputedStyle(document.documentElement)
  .getPropertyValue('--color-brandPrimary-600')

// Set dynamic colors
document.documentElement.style.setProperty(
  '--color-brandPrimary-600', 
  '#1a56db'
)
```

## Demo
Visit `/dashboard/brand-colors-demo` to see all colors in action with interactive examples.
