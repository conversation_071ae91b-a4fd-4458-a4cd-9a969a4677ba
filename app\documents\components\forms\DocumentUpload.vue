<!--
  Document Upload Component
  
  Drag and drop file upload with metadata form
-->

<template>
  <div class="max-w-4xl mx-auto space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">Upload Document</h1>
        <p class="mt-1 text-sm text-gray-500">
          Upload and organize your legal documents
        </p>
      </div>
      
      <NuxtLink
        to="/dashboard/documents"
        class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200"
      >
        <Icon name="heroicons:arrow-left" class="h-4 w-4 mr-2" />
        Back to Documents
      </NuxtLink>
    </div>

    <!-- Upload Area -->
    <div class="bg-white shadow rounded-lg p-6">
      <h2 class="text-lg font-medium text-gray-900 mb-6">Select Files</h2>
      
      <!-- Drag and Drop Area -->
      <div
        @drop="handleDrop"
        @dragover="handleDragOver"
        @dragenter="handleDragEnter"
        @dragleave="handleDragLeave"
        :class="[
          'border-2 border-dashed rounded-lg p-8 text-center transition-colors duration-200',
          isDragOver ? 'border-indigo-500 bg-indigo-50' : 'border-gray-300 hover:border-gray-400'
        ]"
      >
        <Icon name="heroicons:cloud-arrow-up" class="mx-auto h-12 w-12 text-gray-400" />
        <div class="mt-4">
          <p class="text-lg font-medium text-gray-900">
            {{ $t('ui.dropFilesHere') }}
          </p>
          <p class="mt-2 text-sm text-gray-500">
            {{ $t('ui.supportedFileTypes') }}
          </p>
        </div>
        
        <input
          ref="fileInput"
          type="file"
          multiple
          accept=".pdf,.doc,.docx,.txt"
          @change="handleFileSelect"
          class="hidden"
        />
        
        <button
          type="button"
          @click="$refs.fileInput.click()"
          class="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-indigo-600 bg-indigo-100 hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200"
        >
          <Icon name="heroicons:folder-open" class="h-4 w-4 mr-2" />
          Browse Files
        </button>
      </div>

      <!-- Selected Files -->
      <div v-if="selectedFiles.length > 0" class="mt-6">
        <h3 class="text-sm font-medium text-gray-900 mb-4">Selected Files ({{ selectedFiles.length }})</h3>
        
        <div class="space-y-3">
          <div
            v-for="(file, index) in selectedFiles"
            :key="index"
            class="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
          >
            <div class="flex items-center space-x-3">
              <Icon :name="getFileIcon(file.type)" class="h-6 w-6 text-gray-400" />
              <div>
                <p class="text-sm font-medium text-gray-900">{{ file.name }}</p>
                <p class="text-xs text-gray-500">{{ formatFileSize(file.size) }}</p>
              </div>
            </div>
            
            <button
              @click="removeFile(index)"
              class="p-1 rounded-md text-gray-400 hover:text-red-600 hover:bg-red-50 transition-colors duration-200"
            >
              <Icon name="heroicons:x-mark" class="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Document Metadata Form -->
    <div v-if="selectedFiles.length > 0" class="bg-white shadow rounded-lg p-6">
      <h2 class="text-lg font-medium text-gray-900 mb-6">Document Information</h2>
      
      <form @submit.prevent="handleUpload" class="space-y-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Document Type -->
          <div>
            <label for="type" class="block text-sm font-medium text-gray-700 mb-2">
              Document Type *
            </label>
            <select
              id="type"
              v-model="form.type"
              required
              class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
            >
              <option value="">Select document type</option>
              <option value="contract">Contract</option>
              <option value="legal-brief">Legal Brief</option>
              <option value="correspondence">Correspondence</option>
              <option value="evidence">Evidence</option>
              <option value="template">Template</option>
              <option value="other">Other</option>
            </select>
          </div>

          <!-- Associated Case -->
          <div>
            <label for="caseId" class="block text-sm font-medium text-gray-700 mb-2">
              Associated Case
            </label>
            <select
              id="caseId"
              v-model="form.caseId"
              class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
            >
              <option value="">No case association</option>
              <option value="1">CASE-2024-001 - Contract Dispute</option>
              <option value="2">CASE-2024-002 - Employment Law</option>
            </select>
          </div>

          <!-- Tags -->
          <div class="md:col-span-2">
            <label for="tags" class="block text-sm font-medium text-gray-700 mb-2">
              Tags
            </label>
            <input
              id="tags"
              v-model="form.tags"
              type="text"
              placeholder="Enter tags separated by commas"
              class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
            />
            <p class="mt-1 text-xs text-gray-500">
              Example: contract, confidential, client-review
            </p>
          </div>

          <!-- Description -->
          <div class="md:col-span-2">
            <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
              Description
            </label>
            <textarea
              id="description"
              v-model="form.description"
              rows="3"
              placeholder="Brief description of the document"
              class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
            ></textarea>
          </div>
        </div>

        <!-- Privacy Settings -->
        <div class="border-t border-gray-200 pt-6">
          <h3 class="text-sm font-medium text-gray-900 mb-4">Privacy & Access</h3>
          
          <div class="space-y-4">
            <!-- Confidential -->
            <div class="flex items-center">
              <input
                id="confidential"
                v-model="form.confidential"
                type="checkbox"
                class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
              />
              <label for="confidential" class="ml-2 block text-sm text-gray-900">
                Mark as confidential
              </label>
            </div>

            <!-- Client Access -->
            <div class="flex items-center">
              <input
                id="clientAccess"
                v-model="form.clientAccess"
                type="checkbox"
                class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
              />
              <label for="clientAccess" class="ml-2 block text-sm text-gray-900">
                Allow client access
              </label>
            </div>
          </div>
        </div>

        <!-- Upload Progress -->
        <div v-if="isUploading" class="border-t border-gray-200 pt-6">
          <div class="flex items-center justify-between mb-2">
            <span class="text-sm font-medium text-gray-900">Uploading...</span>
            <span class="text-sm text-gray-500">{{ uploadProgress }}%</span>
          </div>
          <div class="w-full bg-gray-200 rounded-full h-2">
            <div
              class="bg-indigo-600 h-2 rounded-full transition-all duration-300"
              :style="{ width: `${uploadProgress}%` }"
            ></div>
          </div>
        </div>

        <!-- Form Actions -->
        <div class="flex justify-end space-x-3 border-t border-gray-200 pt-6">
          <NuxtLink
            to="/dashboard/documents"
            class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200"
          >
            Cancel
          </NuxtLink>
          
          <button
            type="submit"
            :disabled="isUploading || selectedFiles.length === 0"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
          >
            <UiSpinner v-if="isUploading" size="sm" color="white" class="mr-2" />
            <Icon v-else name="heroicons:cloud-arrow-up" class="h-4 w-4 mr-2" />
            {{ isUploading ? 'Uploading...' : 'Upload Documents' }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'

// Composables
const router = useRouter()

// State
const isDragOver = ref(false)
const selectedFiles = ref<File[]>([])
const isUploading = ref(false)
const uploadProgress = ref(0)

const form = reactive({
  type: '',
  caseId: '',
  tags: '',
  description: '',
  confidential: false,
  clientAccess: false
})

// Methods
const handleDragOver = (e: DragEvent) => {
  e.preventDefault()
  isDragOver.value = true
}

const handleDragEnter = (e: DragEvent) => {
  e.preventDefault()
  isDragOver.value = true
}

const handleDragLeave = (e: DragEvent) => {
  e.preventDefault()
  isDragOver.value = false
}

const handleDrop = (e: DragEvent) => {
  e.preventDefault()
  isDragOver.value = false
  
  const files = Array.from(e.dataTransfer?.files || [])
  addFiles(files)
}

const handleFileSelect = (e: Event) => {
  const target = e.target as HTMLInputElement
  const files = Array.from(target.files || [])
  addFiles(files)
}

const addFiles = (files: File[]) => {
  const validFiles = files.filter(file => {
    // Check file type
    const validTypes = ['.pdf', '.doc', '.docx', '.txt']
    const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase()
    
    if (!validTypes.includes(fileExtension)) {
      alert(`File type not supported: ${file.name}`)
      return false
    }
    
    // Check file size (10MB limit)
    if (file.size > 10 * 1024 * 1024) {
      alert(`File too large: ${file.name}. Maximum size is 10MB.`)
      return false
    }
    
    return true
  })
  
  selectedFiles.value.push(...validFiles)
}

const removeFile = (index: number) => {
  selectedFiles.value.splice(index, 1)
}

const getFileIcon = (type: string) => {
  if (type.includes('pdf')) return 'heroicons:document-text'
  if (type.includes('word') || type.includes('doc')) return 'heroicons:document'
  return 'heroicons:document'
}

const formatFileSize = (bytes: number) => {
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  if (bytes === 0) return '0 Bytes'
  const i = Math.floor(Math.log(bytes) / Math.log(1024))
  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
}

const handleUpload = async () => {
  try {
    isUploading.value = true
    uploadProgress.value = 0
    
    // Validate form
    if (!form.type) {
      alert('Please select a document type')
      return
    }
    
    // Simulate upload progress
    const progressInterval = setInterval(() => {
      uploadProgress.value += 10
      if (uploadProgress.value >= 100) {
        clearInterval(progressInterval)
      }
    }, 200)
    
    // TODO: Replace with actual upload logic
    console.log('Uploading files:', selectedFiles.value)
    console.log('Form data:', form)
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // Success - redirect to documents list
    router.push('/dashboard/documents')
    
  } catch (error) {
    console.error('Upload error:', error)
    alert('Failed to upload documents. Please try again.')
  } finally {
    isUploading.value = false
    uploadProgress.value = 0
  }
}
</script>
