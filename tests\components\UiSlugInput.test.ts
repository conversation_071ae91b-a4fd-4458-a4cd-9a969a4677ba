import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { nextTick } from 'vue'
import UiSlugInput from '~/components/ui/UiSlugInput.vue'

// Mock the Icon component
vi.mock('#components', () => ({
  Icon: {
    name: 'Icon',
    template: '<span data-testid="icon" :class="$attrs.class"><slot /></span>'
  }
}))

// Mock vee-validate
vi.mock('vee-validate', () => ({
  useField: () => ({
    value: { value: '' },
    errorMessage: { value: null },
    setValue: vi.fn()
  })
}))

// Mock @vueuse/core
vi.mock('@vueuse/core', () => ({
  useDebounceFn: (fn: Function) => fn
}))

describe('UiSlugInput', () => {
  const defaultProps = {
    id: 'test-slug',
    name: 'testSlug'
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders correctly with basic props', () => {
    const wrapper = mount(UiSlugInput, {
      props: {
        ...defaultProps,
        label: 'Test Slug',
        placeholder: 'Enter slug'
      }
    })

    expect(wrapper.find('label').text()).toBe('Test Slug')
    expect(wrapper.find('input').attributes('placeholder')).toBe('Enter slug')
    expect(wrapper.find('input').attributes('id')).toBe('test-slug')
  })

  it('shows required indicator when required prop is true', () => {
    const wrapper = mount(UiSlugInput, {
      props: {
        ...defaultProps,
        label: 'Test Slug',
        required: true
      }
    })

    const label = wrapper.find('label')
    expect(label.classes()).toContain('after:content-[\'*\']')
  })

  it('transforms input to valid slug format', async () => {
    const wrapper = mount(UiSlugInput, {
      props: defaultProps
    })

    const input = wrapper.find('input')
    
    // Test space transformation
    await input.setValue('hello world')
    await input.trigger('input')
    
    // The component should transform spaces to hyphens
    expect(wrapper.emitted('update:modelValue')).toBeTruthy()
  })

  it('prevents invalid characters from being entered', async () => {
    const wrapper = mount(UiSlugInput, {
      props: defaultProps
    })

    const input = wrapper.find('input')
    
    // Test keydown event with invalid character
    const event = new KeyboardEvent('keydown', { key: '@' })
    Object.defineProperty(event, 'preventDefault', {
      value: vi.fn(),
      writable: true
    })
    
    await input.trigger('keydown', { key: '@' })
    
    // Should prevent default for invalid characters
    expect(wrapper.emitted('keydown')).toBeTruthy()
  })

  it('shows loading state during validation', async () => {
    const mockCheckUniqueness = vi.fn().mockResolvedValue(true)
    
    const wrapper = mount(UiSlugInput, {
      props: {
        ...defaultProps,
        checkUniqueness: mockCheckUniqueness
      }
    })

    // Initially should not show loading
    expect(wrapper.find('[data-testid="icon"]').exists()).toBe(true)
  })

  it('displays help text when provided', () => {
    const wrapper = mount(UiSlugInput, {
      props: {
        ...defaultProps,
        helpText: 'This is help text'
      }
    })

    expect(wrapper.text()).toContain('This is help text')
  })

  it('shows character count when enabled', () => {
    const wrapper = mount(UiSlugInput, {
      props: {
        ...defaultProps,
        showCharCount: true,
        maxlength: 50
      }
    })

    expect(wrapper.text()).toContain('0/50')
  })

  it('applies correct size classes', () => {
    const wrapper = mount(UiSlugInput, {
      props: {
        ...defaultProps,
        size: 'lg'
      }
    })

    const input = wrapper.find('input')
    expect(input.classes()).toContain('px-5')
    expect(input.classes()).toContain('py-4')
  })

  it('applies correct variant classes', () => {
    const wrapper = mount(UiSlugInput, {
      props: {
        ...defaultProps,
        variant: 'filled'
      }
    })

    const input = wrapper.find('input')
    expect(input.classes()).toContain('bg-gray-50')
  })

  it('disables input when disabled prop is true', () => {
    const wrapper = mount(UiSlugInput, {
      props: {
        ...defaultProps,
        disabled: true
      }
    })

    const input = wrapper.find('input')
    expect(input.attributes('disabled')).toBeDefined()
  })

  it('shows leading icon when provided', () => {
    const wrapper = mount(UiSlugInput, {
      props: {
        ...defaultProps,
        leadingIcon: 'test-icon'
      }
    })

    expect(wrapper.find('[data-testid="icon"]').exists()).toBe(true)
  })

  it('emits validation-change event', async () => {
    const wrapper = mount(UiSlugInput, {
      props: defaultProps
    })

    // Trigger validation change
    await wrapper.vm.$emit('validation-change', true, false)
    
    expect(wrapper.emitted('validation-change')).toBeTruthy()
  })

  it('handles custom allowed pattern', async () => {
    const customPattern = /^[a-z0-9_-]*$/
    
    const wrapper = mount(UiSlugInput, {
      props: {
        ...defaultProps,
        allowedPattern: customPattern
      }
    })

    // The component should use the custom pattern
    expect(wrapper.props('allowedPattern')).toEqual(customPattern)
  })

  it('handles transform spaces option', () => {
    const wrapper = mount(UiSlugInput, {
      props: {
        ...defaultProps,
        transformSpaces: false
      }
    })

    expect(wrapper.props('transformSpaces')).toBe(false)
  })

  it('exposes focus method', () => {
    const wrapper = mount(UiSlugInput, {
      props: defaultProps
    })

    expect(typeof wrapper.vm.focus).toBe('function')
  })
})
