<template>
  <div class="space-y-6 py-6">
  

    <!-- Main Content -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- Invite Form -->
      <div class="lg:col-span-2">
        <UiCard>
          <template #header>
            <div class="flex items-center justify-between">
              <div class="flex items-center gap-3">
                <div class="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
                  <Icon name="material-symbols:person-add" class="h-5 w-5 text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                  <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Send Invitations</h3>
                  <p class="text-sm text-gray-500 dark:text-gray-400">Invite users to join specific tenants</p>
                </div>
              </div>
              <UiBadge v-if="inviteQueue.length > 0" variant="primary">
                {{ inviteQueue.length }} queued
              </UiBadge>
            </div>
          </template>

          <form @submit.prevent="handleSubmit" class="space-y-6">
            <!-- Tenant Selection -->
            <div>
              <UiSelect
                id="tenantId"
                v-model="formData.tenantId"
                label="Select Tenant"
                placeholder="Choose a tenant to invite users to"
                :options="tenantOptions"
                :loading="tenantStore.isLoading"
                required
                leading-icon="material-symbols:business"
                help-text="Select the tenant where the invited users will be added"
              />
            </div>

            <!-- Email Input -->
            <div>
              <label class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2">
                Email Addresses
                <span class="text-red-500">*</span>
              </label>
              <div class="space-y-3">
                <div
                  v-for="(email, index) in formData.emails"
                  :key="index"
                  class="flex items-center gap-3"
                >
                  <div class="flex-1">
                    <UiInput
                      :id="`email-${index}`"
                      :name="`email-${index}`"
                      v-model="formData.emails[index]"
                      type="email"
                      placeholder="Enter email address"
                      :error="emailErrors[index]"
                      leading-icon="material-symbols:mail"
                      required
                    />
                  </div>
                  <UiButton
                    v-if="formData.emails.length > 1"
                    type="button"
                    variant="ghost"
                    size="sm"
                    shape="circle"
                    @click="removeEmail(index)"
                    aria-label="Remove email"
                  >
                    <Icon name="material-symbols:close" class="h-4 w-4" />
                  </UiButton>
                </div>
                <UiButton
                  type="button"
                  variant="ghost"
                  size="sm"
                  leading-icon="material-symbols:add"
                  @click="addEmail"
                  class="w-full"
                >
                  Add Another Email
                </UiButton>
              </div>
              <p class="text-sm text-gray-500 dark:text-gray-400 mt-2">
                You can invite multiple users at once. Each will receive a separate invitation.
              </p>
            </div>

            <!-- Role Selection -->
            <div>
              <UiSelect
                id="role"
                v-model="formData.role"
                label="Assign Role"
                placeholder="Select a role for the invited users"
                :options="roleOptions"
                required
                leading-icon="material-symbols:admin-panel-settings"
                help-text="All invited users will be assigned this role in the selected tenant"
              />
            </div>

            <!-- Custom Message -->
            <div>
              <UiTextarea
                id="customMessage"
                v-model="formData.customMessage"
                label="Custom Message (Optional)"
                placeholder="Add a personal message to the invitation email..."
                :rows="4"
                help-text="This message will be included in the invitation email to provide context"
              />
            </div>

            <!-- Invitation Options -->
            <div class="space-y-4">
              <h4 class="text-sm font-semibold text-gray-700 dark:text-gray-300">Invitation Options</h4>
              <div class="space-y-3">
                <UiCheckbox
                  id="sendWelcomeEmail"
                  v-model="formData.sendWelcomeEmail"
                  label="Send welcome email after account creation"
                />
                <UiCheckbox
                  id="requirePasswordReset"
                  v-model="formData.requirePasswordReset"
                  label="Require password reset on first login"
                />
                <UiCheckbox
                  id="notifyOnAccept"
                  v-model="formData.notifyOnAccept"
                  label="Notify me when invitation is accepted"
                />
              </div>
            </div>

            <!-- Error Display -->
            <div v-if="error" class="mt-4">
              <UiAlert type="error" :title="error">
                Please review the form and try again.
              </UiAlert>
            </div>

            <!-- Success Display -->
            <div v-if="successMessage" class="mt-4">
              <UiAlert type="success" :title="successMessage">
                Invitations have been sent successfully.
              </UiAlert>
            </div>

            <!-- Form Actions -->
            <div class="flex flex-col sm:flex-row gap-3 pt-4 border-t border-gray-200 dark:border-gray-700">
              <UiButton
                type="submit"
                :loading="isSubmitting"
                :disabled="!canSubmit"
                class="flex-1 sm:flex-none"
              >
                <template v-if="formData.emails.filter(e => e.trim()).length > 1">
                  Send {{ formData.emails.filter(e => e.trim()).length }} Invitations
                </template>
                <template v-else>
                  Send Invitation
                </template>
              </UiButton>
              <UiButton
                type="button"
                variant="secondary"
                @click="resetForm"
                :disabled="isSubmitting"
              >
                Reset Form
              </UiButton>
              <UiButton
                type="button"
                variant="ghost"
                @click="saveDraft"
                :disabled="isSubmitting"
              >
                Save Draft
              </UiButton>
            </div>
          </form>
        </UiCard>
      </div>

      <!-- Sidebar -->
      <div class="space-y-6">
        <!-- Quick Stats -->
        <UiCard>
          <template #header>
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Invitation Stats</h3>
          </template>
          <div class="space-y-4">
            <div class="flex items-center justify-between">
              <span class="text-sm text-gray-600 dark:text-gray-400">Pending Invites</span>
              <UiBadge variant="warning">{{ stats.pending }}</UiBadge>
            </div>
            <div class="flex items-center justify-between">
              <span class="text-sm text-gray-600 dark:text-gray-400">Accepted Today</span>
              <UiBadge variant="success">{{ stats.acceptedToday }}</UiBadge>
            </div>
            <div class="flex items-center justify-between">
              <span class="text-sm text-gray-600 dark:text-gray-400">Total Sent</span>
              <UiBadge variant="neutral">{{ stats.totalSent }}</UiBadge>
            </div>
          </div>
        </UiCard>

        <!-- Recent Invitations -->
        <UiCard>
          <template #header>
            <div class="flex items-center justify-between">
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Recent Invitations</h3>
              <UiButton variant="ghost" size="sm" @click="viewAllInvites">
                View All
              </UiButton>
            </div>
          </template>
          <div class="space-y-3">
            <div
              v-for="invite in recentInvites"
              :key="invite.id"
              class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg"
            >
              <div class="flex-1 min-w-0">
                <p class="text-sm font-medium text-gray-900 dark:text-white truncate">
                  {{ invite.email }}
                </p>
                <p class="text-xs text-gray-500 dark:text-gray-400">
                  {{ invite.tenantName }} • {{ invite.role }}
                </p>
              </div>
              <UiBadge
                :variant="getInviteStatusVariant(invite.status)"
                size="sm"
              >
                {{ invite.status }}
              </UiBadge>
            </div>
            <div v-if="recentInvites.length === 0" class="text-center py-4">
              <p class="text-sm text-gray-500 dark:text-gray-400">No recent invitations</p>
            </div>
          </div>
        </UiCard>

        <!-- Bulk Actions -->
        <UiCard>
          <template #header>
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Bulk Actions</h3>
          </template>
          <div class="space-y-3">
            <UiButton
              variant="outline"
              size="sm"
              leading-icon="material-symbols:upload-file"
              @click="showBulkImport = true"
              class="w-full"
            >
              Import from CSV
            </UiButton>
            <UiButton
              variant="outline"
              size="sm"
              leading-icon="material-symbols:content-copy"
              @click="showTemplates = true"
              class="w-full"
            >
              Use Template
            </UiButton>
            <UiButton
              variant="outline"
              size="sm"
              leading-icon="material-symbols:schedule-send"
              @click="showScheduler = true"
              class="w-full"
            >
              Schedule Invites
            </UiButton>
          </div>
        </UiCard>
      </div>
    </div>

    <!-- Bulk Import Modal -->
    <UiModal
      v-model:show="showBulkImport"
      title="Import Invitations from CSV"
      width="lg"
    >
      <div class="space-y-4">
        <p class="text-sm text-gray-600 dark:text-gray-400">
          Upload a CSV file with email addresses and roles to send bulk invitations.
        </p>
        <div class="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-6 text-center">
          <Icon name="material-symbols:upload-file" class="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">
            Drop your CSV file here or click to browse
          </p>
          <UiButton variant="outline" size="sm">
            Choose File
          </UiButton>
        </div>
        <div class="text-xs text-gray-500 dark:text-gray-400">
          <p class="font-medium mb-1">CSV Format:</p>
          <p>email,role,tenant_id</p>
          <p><EMAIL>,admin,tenant-123</p>
        </div>
      </div>
      <template #footer>
        <div class="flex justify-end gap-3">
          <UiButton variant="secondary" @click="showBulkImport = false">
            Cancel
          </UiButton>
          <UiButton>
            Import Invitations
          </UiButton>
        </div>
      </template>
    </UiModal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useTenantStore, type TenantInvitePayload, type Tenant } from '~/stores/tenant'
import { PlatformRoles, TenantRoles } from '~/app/features/auth/constants/roles'

 
// Page meta
definePageMeta({
  layout: 'dashboard',
  title: 'Invite Users to Tenant',
  description: 'Send invitations to new users and assign them to specific tenants with appropriate roles',
  pageHeaderIcon: 'material-symbols:person-add',
  showRealTimeStatus: false,
  autoRefreshEnabled: false,
  refreshInterval: 0,
  isLoading: false,
  showActionsMenu: true,
  showKeyboardShortcuts: true,
  middleware: ['rbac'],
  roles: [PlatformRoles.SUPER_ADMIN],
  breadcrumbs: [
    { label: 'Dashboard', href: '/dashboard' },
    { label: 'Tenants', href: '/dashboard/tenants' },
    { label: 'Invite Users' },
  ],
})

// Stores and composables
const tenantStore = useTenantStore()
const router = useRouter()

// Form data
interface InviteFormData {
  tenantId: string
  emails: string[]
  role: string
  customMessage: string
  sendWelcomeEmail: boolean
  requirePasswordReset: boolean
  notifyOnAccept: boolean
}

const formData = ref<InviteFormData>({
  tenantId: '',
  emails: [''],
  role: '',
  customMessage: '',
  sendWelcomeEmail: true,
  requirePasswordReset: true,
  notifyOnAccept: true,
})

// State
const isSubmitting = ref(false)
const error = ref('')
const successMessage = ref('')
const emailErrors = ref<string[]>([])
const inviteQueue = ref<any[]>([])
const showBulkImport = ref(false)
const showTemplates = ref(false)
const showScheduler = ref(false)

// Mock stats (replace with real data from store)
const stats = ref({
  pending: 12,
  acceptedToday: 5,
  totalSent: 147,
})

// Mock recent invites (replace with real data from store)
const recentInvites = ref([
  {
    id: '1',
    email: '<EMAIL>',
    tenantName: 'Acme Corp',
    role: 'Admin',
    status: 'pending',
  },
  {
    id: '2',
    email: '<EMAIL>',
    tenantName: 'Tech Solutions',
    role: 'User',
    status: 'accepted',
  },
])

// Computed properties
const tenantOptions = computed(() => {
  return tenantStore.tenants.map((tenant: Tenant) => ({
    value: tenant.id,
    label: tenant.name,
  }))
})

const roleOptions = computed(() => [
  { value: TenantRoles.TENANT_OWNER, label: 'Tenant Owner' },
  { value: TenantRoles.ADMIN, label: 'Administrator' },
  { value: TenantRoles.LAWYER, label: 'Lawyer' },
  { value: TenantRoles.PARALEGAL, label: 'Paralegal' },
  { value: TenantRoles.CLIENT, label: 'Client' },
])

const canSubmit = computed(() => {
  return (
    formData.value.tenantId &&
    formData.value.role &&
    formData.value.emails.some(email => email.trim()) &&
    !isSubmitting.value
  )
})

// Methods
const addEmail = () => {
  formData.value.emails.push('')
}

const removeEmail = (index: number) => {
  if (formData.value.emails.length > 1) {
    formData.value.emails.splice(index, 1)
    emailErrors.value.splice(index, 1)
  }
}

const validateEmails = () => {
  emailErrors.value = []
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/

  formData.value.emails.forEach((email, index) => {
    if (email.trim() && !emailRegex.test(email.trim())) {
      emailErrors.value[index] = 'Please enter a valid email address'
    }
  })

  return emailErrors.value.every(error => !error)
}

const handleSubmit = async () => {
  error.value = ''
  successMessage.value = ''

  if (!validateEmails()) {
    error.value = 'Please fix email validation errors'
    return
  }

  const validEmails = formData.value.emails.filter(email => email.trim())
  if (validEmails.length === 0) {
    error.value = 'Please enter at least one email address'
    return
  }

  isSubmitting.value = true

  try {
    // Send invitations for each email
    const invitePromises = validEmails.map(email => {
      const payload: TenantInvitePayload = {
        email: email.trim(),
        role: formData.value.role,
        tenantId: formData.value.tenantId,
      }
      return tenantStore.sendInvitesToTenant(payload)
    })

    await Promise.all(invitePromises)

    successMessage.value = `Successfully sent ${validEmails.length} invitation${validEmails.length > 1 ? 's' : ''}`

    // Reset form after successful submission
    setTimeout(() => {
      resetForm()
    }, 2000)

  } catch (err: any) {
    error.value = err.message || 'Failed to send invitations'
  } finally {
    isSubmitting.value = false
  }
}

const resetForm = () => {
  formData.value = {
    tenantId: '',
    emails: [''],
    role: '',
    customMessage: '',
    sendWelcomeEmail: true,
    requirePasswordReset: true,
    notifyOnAccept: true,
  }
  emailErrors.value = []
  error.value = ''
  successMessage.value = ''
}

const saveDraft = () => {
  // Implementation for saving draft
  console.log('Saving draft...', formData.value)
}

const viewInviteHistory = () => {
  router.push('/dashboard/tenants/invites/history')
}

const exportInvites = () => {
  // Implementation for exporting invites
  console.log('Exporting invites...')
}

const viewAllInvites = () => {
  router.push('/dashboard/tenants/invites')
}

const getInviteStatusVariant = (status: string) => {
  switch (status) {
    case 'pending':
      return 'warning'
    case 'accepted':
      return 'success'
    case 'expired':
      return 'danger'
    default:
      return 'neutral'
  }
}

// Lifecycle
onMounted(async () => {
  // Fetch tenants if not already loaded
  if (!tenantStore.hasTenants) {
    await tenantStore.fetchAllTenants('invite.vue')
  }
})

// Watch for tenant store errors
watch(
  () => tenantStore.error,
  (newError) => {
    if (newError) {
      error.value = newError
    }
  }
)
</script>
