<template>
  <UiModal @close="$emit('close')" size="xl">
    <div class="p-6">
      <!-- Header -->
      <div class="flex items-center space-x-3 mb-6">
        <div class="w-10 h-10 bg-indigo-100 dark:bg-indigo-900 rounded-full flex items-center justify-center">
          <Icon name="heroicons:computer-desktop" class="w-5 h-5 text-indigo-600 dark:text-indigo-400" />
        </div>
        <div>
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">In-App Notification Template Library</h3>
          <p class="text-sm text-gray-600 dark:text-gray-400">Choose from pre-built in-app notification templates</p>
        </div>
      </div>

      <!-- Search and Filter -->
      <div class="flex items-center space-x-4 mb-6">
        <div class="flex-1">
          <UiInput
            v-model="searchQuery"
            placeholder="Search in-app templates..."
            class="w-full"
          >
            <template #prefix>
              <Icon name="heroicons:magnifying-glass" class="w-4 h-4 text-gray-400" />
            </template>
          </UiInput>
        </div>
        <div class="flex items-center space-x-2">
          <select
            v-model="selectedCategory"
            class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm"
          >
            <option value="">All Categories</option>
            <option value="onboarding">Onboarding</option>
            <option value="updates">Updates</option>
            <option value="alerts">Alerts</option>
            <option value="promotions">Promotions</option>
          </select>
          <select
            v-model="selectedType"
            class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm"
          >
            <option value="">All Types</option>
            <option value="banner">Banner</option>
            <option value="toast">Toast</option>
            <option value="modal">Modal</option>
            <option value="sidebar">Sidebar</option>
            <option value="inline">Inline</option>
          </select>
        </div>
      </div>

      <!-- Template Grid -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4 max-h-96 overflow-y-auto">
        <div
          v-for="template in filteredTemplates"
          :key="template.id"
          class="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:border-indigo-300 dark:hover:border-indigo-600 cursor-pointer transition-colors"
          @click="selectTemplate(template)"
        >
          <div class="flex items-start justify-between mb-3">
            <div>
              <h4 class="text-sm font-medium text-gray-900 dark:text-white">{{ template.name }}</h4>
              <p class="text-xs text-gray-500 dark:text-gray-400">{{ template.category }} • {{ template.type }}</p>
            </div>
            <div class="flex items-center space-x-2">
              <span
                :class="[
                  'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium',
                  getCategoryColor(template.category)
                ]"
              >
                {{ template.category }}
              </span>
              <span
                :class="[
                  'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium',
                  getTypeColor(template.type)
                ]"
              >
                {{ template.type }}
              </span>
            </div>
          </div>
          
          <p class="text-sm text-gray-600 dark:text-gray-300 mb-3">{{ template.description }}</p>
          
          <!-- In-App Notification Preview -->
          <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-3 mb-3 relative overflow-hidden">
            <div class="text-xs text-gray-500 dark:text-gray-400 mb-2">{{ template.type }} Preview:</div>
            
            <!-- Mock notification based on type -->
            <div
              :class="[
                'bg-white dark:bg-gray-700 rounded border border-gray-200 dark:border-gray-600 p-3',
                getPreviewClasses(template.type)
              ]"
            >
              <div class="flex items-start justify-between">
                <div class="flex-1">
                  <h5 class="text-sm font-medium text-gray-900 dark:text-white">
                    {{ template.title }}
                  </h5>
                  <div class="text-sm text-gray-600 dark:text-gray-300 mt-1">
                    {{ template.content }}
                  </div>
                </div>
                <button class="ml-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200">
                  <Icon name="heroicons:x-mark" class="w-3 h-3" />
                </button>
              </div>
              
              <div v-if="template.actions.length > 0" class="mt-3 flex items-center space-x-2">
                <button
                  v-for="action in template.actions.slice(0, 2)"
                  :key="action.title"
                  :class="[
                    'px-2 py-1 text-xs font-medium rounded transition-colors',
                    getActionButtonClasses(action.style)
                  ]"
                >
                  {{ action.title }}
                </button>
              </div>
            </div>
          </div>
          
          <div v-if="template.variables.length > 0" class="pt-3 border-t border-gray-200 dark:border-gray-700">
            <div class="text-xs text-gray-500 dark:text-gray-400 mb-1">Variables:</div>
            <div class="flex flex-wrap gap-1">
              <span
                v-for="variable in template.variables.slice(0, 3)"
                :key="variable"
                class="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-mono bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200"
              >
                {{ variable }}
              </span>
              <span
                v-if="template.variables.length > 3"
                class="text-xs text-gray-500 dark:text-gray-400"
              >
                +{{ template.variables.length - 3 }} more
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- Actions -->
      <div class="flex items-center justify-end space-x-3 mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
        <UiButton @click="$emit('close')" variant="outline">
          Cancel
        </UiButton>
      </div>
    </div>
  </UiModal>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

// Emits
const emit = defineEmits<{
  close: []
  select: [template: any]
}>()

// State
const searchQuery = ref('')
const selectedCategory = ref('')
const selectedType = ref('')

// In-app notification templates library
const inAppTemplates = [
  {
    id: 'welcome-banner',
    name: 'Welcome Banner',
    category: 'onboarding',
    type: 'banner',
    description: 'Welcome new users with a prominent banner',
    title: 'Welcome to {companyName}',
    content: 'Get started by exploring your legal dashboard and setting up your profile.',
    actions: [
      { title: 'Get Started', style: 'primary', url: '/onboarding' },
      { title: 'Skip Tour', style: 'secondary', url: '/dashboard' }
    ],
    variables: ['companyName']
  },
  {
    id: 'case-update-toast',
    name: 'Case Update Toast',
    category: 'updates',
    type: 'toast',
    description: 'Subtle notification for case updates',
    title: 'Case Updated',
    content: 'New activity in {caseTitle}. Click to view details.',
    actions: [
      { title: 'View', style: 'primary', url: '/cases/{caseId}' }
    ],
    variables: ['caseTitle', 'caseId']
  },
  {
    id: 'document-approval-modal',
    name: 'Document Approval Modal',
    category: 'alerts',
    type: 'modal',
    description: 'Important document requiring immediate attention',
    title: 'Document Requires Your Approval',
    content: '{documentTitle} needs your review and approval before proceeding with {caseTitle}.',
    actions: [
      { title: 'Review Now', style: 'primary', url: '/documents/{documentId}' },
      { title: 'Remind Later', style: 'secondary', url: '#' }
    ],
    variables: ['documentTitle', 'caseTitle', 'documentId']
  },
  {
    id: 'payment-reminder-sidebar',
    name: 'Payment Reminder Sidebar',
    category: 'alerts',
    type: 'sidebar',
    description: 'Persistent payment reminder in sidebar',
    title: 'Payment Due',
    content: 'Invoice #{invoiceNumber} for ${amount} is due {dueDate}. Avoid late fees by paying today.',
    actions: [
      { title: 'Pay Now', style: 'primary', url: '/invoices/{invoiceId}/pay' },
      { title: 'View Invoice', style: 'secondary', url: '/invoices/{invoiceId}' }
    ],
    variables: ['invoiceNumber', 'amount', 'dueDate', 'invoiceId']
  },
  {
    id: 'feature-announcement-inline',
    name: 'Feature Announcement',
    category: 'promotions',
    type: 'inline',
    description: 'Announce new features within content',
    title: 'New Feature: Document Collaboration',
    content: 'You can now collaborate on documents in real-time with your legal team. Try it out!',
    actions: [
      { title: 'Try Now', style: 'primary', url: '/documents/collaborate' },
      { title: 'Learn More', style: 'secondary', url: '/help/collaboration' }
    ],
    variables: []
  },
  {
    id: 'appointment-reminder-banner',
    name: 'Appointment Reminder Banner',
    category: 'updates',
    type: 'banner',
    description: 'Remind about upcoming appointments',
    title: 'Appointment Tomorrow',
    content: 'You have an appointment with {attorneyName} tomorrow at {appointmentTime}.',
    actions: [
      { title: 'View Details', style: 'primary', url: '/appointments/{appointmentId}' },
      { title: 'Reschedule', style: 'secondary', url: '/appointments/{appointmentId}/reschedule' }
    ],
    variables: ['attorneyName', 'appointmentTime', 'appointmentId']
  },
  {
    id: 'onboarding-progress-toast',
    name: 'Onboarding Progress',
    category: 'onboarding',
    type: 'toast',
    description: 'Show onboarding completion progress',
    title: 'Profile Setup Complete!',
    content: 'Great job! You\'ve completed {completionPercentage}% of your profile setup.',
    actions: [
      { title: 'Continue', style: 'primary', url: '/onboarding/next' }
    ],
    variables: ['completionPercentage']
  },
  {
    id: 'deadline-alert-modal',
    name: 'Deadline Alert Modal',
    category: 'alerts',
    type: 'modal',
    description: 'Critical deadline approaching',
    title: 'URGENT: Deadline Approaching',
    content: '{documentTitle} deadline is in 24 hours. Immediate action required to avoid case delays.',
    actions: [
      { title: 'Take Action', style: 'danger', url: '/documents/{documentId}' },
      { title: 'Contact Attorney', style: 'secondary', url: '/contact' }
    ],
    variables: ['documentTitle', 'documentId']
  },
  {
    id: 'consultation-feedback-inline',
    name: 'Consultation Feedback',
    category: 'updates',
    type: 'inline',
    description: 'Request feedback after consultations',
    title: 'How was your consultation?',
    content: 'Help us improve by rating your consultation with {attorneyName}.',
    actions: [
      { title: 'Rate Experience', style: 'primary', url: '/feedback/{consultationId}' },
      { title: 'Skip', style: 'secondary', url: '#' }
    ],
    variables: ['attorneyName', 'consultationId']
  },
  {
    id: 'security-alert-banner',
    name: 'Security Alert',
    category: 'alerts',
    type: 'banner',
    description: 'Important security notifications',
    title: 'Security Alert',
    content: 'New login detected from {location}. If this wasn\'t you, please secure your account immediately.',
    actions: [
      { title: 'Secure Account', style: 'danger', url: '/security' },
      { title: 'This Was Me', style: 'secondary', url: '#' }
    ],
    variables: ['location']
  }
]

// Computed
const filteredTemplates = computed(() => {
  let templates = inAppTemplates
  
  // Filter by category
  if (selectedCategory.value) {
    templates = templates.filter(template => template.category === selectedCategory.value)
  }
  
  // Filter by type
  if (selectedType.value) {
    templates = templates.filter(template => template.type === selectedType.value)
  }
  
  // Filter by search query
  if (searchQuery.value.trim()) {
    const query = searchQuery.value.toLowerCase()
    templates = templates.filter(template =>
      template.name.toLowerCase().includes(query) ||
      template.description.toLowerCase().includes(query) ||
      template.title.toLowerCase().includes(query) ||
      template.content.toLowerCase().includes(query)
    )
  }
  
  return templates
})

// Methods
const selectTemplate = (template: any) => {
  emit('select', template)
}

const getCategoryColor = (category: string) => {
  const colors = {
    'onboarding': 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
    'updates': 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
    'alerts': 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
    'promotions': 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200',
  }
  return colors[category as keyof typeof colors] || 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
}

const getTypeColor = (type: string) => {
  const colors = {
    'banner': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
    'toast': 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200',
    'modal': 'bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-200',
    'sidebar': 'bg-teal-100 text-teal-800 dark:bg-teal-900 dark:text-teal-200',
    'inline': 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200',
  }
  return colors[type as keyof typeof colors] || 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
}

const getPreviewClasses = (type: string) => {
  switch (type) {
    case 'banner':
      return 'border-l-4 border-blue-500'
    case 'toast':
      return 'shadow-md'
    case 'modal':
      return 'shadow-lg border-2'
    case 'sidebar':
      return 'border-r-4 border-teal-500'
    case 'inline':
      return 'border border-dashed'
    default:
      return ''
  }
}

const getActionButtonClasses = (style: string) => {
  switch (style) {
    case 'primary':
      return 'bg-indigo-600 text-white hover:bg-indigo-700'
    case 'secondary':
      return 'bg-gray-200 text-gray-900 hover:bg-gray-300 dark:bg-gray-600 dark:text-white dark:hover:bg-gray-500'
    case 'danger':
      return 'bg-red-600 text-white hover:bg-red-700'
    case 'success':
      return 'bg-green-600 text-white hover:bg-green-700'
    default:
      return 'bg-indigo-600 text-white hover:bg-indigo-700'
  }
}
</script>
