<template>
  <button
    :disabled="props.disabled"
    :class="[
      'w-full flex items-center px-3 py-2 text-sm text-left transition-colors duration-150',
      'hover:bg-gray-100 dark:hover:bg-gray-700',
      'focus:outline-none focus:bg-gray-100 dark:focus:bg-gray-700',
      'disabled:opacity-50 disabled:cursor-not-allowed',
      props.disabled ? 'cursor-not-allowed' : 'cursor-pointer',
      props.itemClass
    ]"
    @click="handleClick"
  >
    <!-- Icon -->
    <Icon
      v-if="props.icon"
      :name="props.icon"
      :class="[
        'w-4 h-4 mr-3 flex-shrink-0',
        props.iconClass
      ]"
    />
    
    <!-- Label -->
    <span class="flex-1 truncate">{{ props.label }}</span>
    
    <!-- Badge or Secondary Text -->
    <span
      v-if="props.badge || props.shortcut"
      :class="[
        'ml-2 text-xs flex-shrink-0',
        props.badge ? 'bg-gray-200 dark:bg-gray-600 px-2 py-0.5 rounded-full' : 'text-gray-500 dark:text-gray-400'
      ]"
    >
      {{ props.badge || props.shortcut }}
    </span>
    
    <!-- Chevron for submenu -->
    <Icon
      v-if="props.hasSubmenu"
      name="heroicons:chevron-right"
      class="w-4 h-4 ml-2 flex-shrink-0 text-gray-400"
    />
  </button>
</template>

<script setup lang="ts">
interface Props {
  label: string
  icon?: string
  disabled?: boolean
  command?: string
  value?: any
  badge?: string | number
  shortcut?: string
  hasSubmenu?: boolean
  itemClass?: string
  iconClass?: string
}

interface Emits {
  (e: 'click', payload: { command?: string; value?: any; label: string }): void
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false,
  hasSubmenu: false
})

const emit = defineEmits<Emits>()

const handleClick = () => {
  if (props.disabled) return
  
  emit('click', {
    command: props.command,
    value: props.value,
    label: props.label
  })
}
</script>
