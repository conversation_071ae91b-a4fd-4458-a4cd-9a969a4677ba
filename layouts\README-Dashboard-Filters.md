# Dashboard Layout Filter Integration

The dashboard layout now supports filter configuration through `definePageMeta`, allowing you to configure filters declaratively without adding them directly to your page templates.

## How It Works

The dashboard layout automatically reads filter configuration from `route.meta` and passes them to the `GlobalPageHeader` component. Filter events are emitted globally so pages can listen and respond to filter changes.

## Configuration Properties

Add these properties to your page's `definePageMeta`:

```typescript
definePageMeta({
  layout: 'dashboard',
  
  // Filter configuration
  showFilters: true,                    // Enable/disable filters
  filterConfigs: [...],                 // Basic dropdown filters
  advancedFilterConfigs: [...],         // Advanced filters panel
  initialFilters: {},                   // Initial filter values
  showAdvancedFilters: true,            // Show advanced filters toggle
  compactFilters: false,                // Use compact layout
})
```

## Default Values

When not specified, the layout uses these defaults:

```typescript
{
  showFilters: false,
  filterConfigs: [],
  advancedFilterConfigs: [],
  initialFilters: {},
  showAdvancedFilters: true,
  compactFilters: false
}
```

## Basic Usage Example

```vue
<script setup lang="ts">
definePageMeta({
  layout: 'dashboard',
  title: 'My Page',
  showPageHeader: true,
  
  // Enable filters
  showFilters: true,
  filterConfigs: [
    {
      key: 'status',
      options: [
        { label: 'Active', value: 'active' },
        { label: 'Inactive', value: 'inactive' }
      ],
      placeholder: 'All Status'
    },
    {
      key: 'plan',
      options: [
        { label: 'Basic', value: 'basic' },
        { label: 'Pro', value: 'pro' },
        { label: 'Enterprise', value: 'enterprise' }
      ],
      placeholder: 'All Plans'
    }
  ],
  advancedFilterConfigs: [
    {
      key: 'createdDate',
      type: 'dateRange',
      label: 'Created Date Range'
    },
    {
      key: 'itemCount',
      type: 'numberRange',
      label: 'Item Count',
      minPlaceholder: 'Min Items',
      maxPlaceholder: 'Max Items'
    }
  ]
})
</script>
```

## Event Handling

The layout emits global events that your pages can listen to:

```vue
<script setup lang="ts">
import { onMounted, onUnmounted } from 'vue'
import { useEventEmitters } from '~/composables/useEventEmitters'

const { on, off } = useEventEmitters()

const handleFilterChange = (data: { key: string; value: any }) => {
  console.log('Filter changed:', data.key, data.value)
  // Handle individual filter changes
}

const handleFiltersChange = (filters: Record<string, any>) => {
  console.log('All filters:', filters)
  // Handle bulk filter changes
}

const handleClearFilters = () => {
  console.log('Filters cleared')
  // Handle filter clearing
}

const handleAdvancedToggle = (show: boolean) => {
  console.log('Advanced filters toggled:', show)
  // Handle advanced filters toggle
}

onMounted(() => {
  // Listen to filter events
  on('filter-change', handleFilterChange)
  on('filters-change', handleFiltersChange)
  on('clear-filters', handleClearFilters)
  on('advanced-filters-toggle', handleAdvancedToggle)
})

onUnmounted(() => {
  // Clean up event listeners
  off('filter-change', handleFilterChange)
  off('filters-change', handleFiltersChange)
  off('clear-filters', handleClearFilters)
  off('advanced-filters-toggle', handleAdvancedToggle)
})
</script>
```

## Available Events

| Event | Payload | Description |
|-------|---------|-------------|
| `filter-change` | `{ key: string, value: any }` | Single filter changed |
| `filters-change` | `Record<string, any>` | All current filters |
| `clear-filters` | `void` | All filters cleared |
| `advanced-filters-toggle` | `boolean` | Advanced panel toggled |

## Filter Configuration Types

### Basic Filters (filterConfigs)

```typescript
interface FilterConfig {
  key: string;           // Unique filter identifier
  options: FilterOption[]; // Available options
  placeholder: string;   // Placeholder text
  class?: string;       // Additional CSS classes
}

interface FilterOption {
  label: string;        // Display text
  value: string | number; // Filter value
}
```

### Advanced Filters (advancedFilterConfigs)

```typescript
interface AdvancedFilterConfig {
  key: string;                    // Unique identifier
  type: 'dateRange' | 'numberRange' | 'select' | 'custom';
  label: string;                  // Display label
  placeholder?: string;           // For select type
  minPlaceholder?: string;        // For range types
  maxPlaceholder?: string;        // For range types
  options?: FilterOption[];       // For select type
}
```

## Custom Filter Slots

For custom advanced filters, use the slot pattern in your page:

```vue
<template>
  <!-- Your page content -->
</template>

<script setup lang="ts">
definePageMeta({
  // ... other config
  advancedFilterConfigs: [
    {
      key: 'priority',
      type: 'custom',
      label: 'Priority Level'
    }
  ]
})
</script>

<!-- Note: Custom slots need to be handled differently with meta-based approach -->
<!-- Consider using direct GlobalPageHeader integration for complex custom filters -->
```

## Benefits of Meta-Based Configuration

### ✅ **Declarative Configuration**
- Filters defined alongside other page metadata
- No template clutter
- Consistent configuration pattern

### ✅ **Automatic Integration**
- Layout handles all filter rendering
- No need to import/configure GlobalPageHeader
- Consistent filter placement across pages

### ✅ **Global Event System**
- Centralized filter event handling
- Easy to implement cross-component communication
- Consistent event patterns

### ✅ **Maintainability**
- Filter configuration co-located with page metadata
- Easy to enable/disable filters per page
- Consistent defaults across application

## When to Use Direct Integration

Consider using direct `GlobalPageHeader` integration instead of meta-based configuration when:

- You need complex custom filter slots
- You require fine-grained control over filter rendering
- You need to pass dynamic filter configurations
- You want to handle filter events locally within the component

## Migration from Direct Integration

### Before (Direct Integration)
```vue
<template>
  <GlobalPageHeader
    :show-filters="true"
    :filter-configs="filterConfigs"
    @filter-change="handleFilterChange"
  />
</template>
```

### After (Meta-Based)
```vue
<script setup lang="ts">
definePageMeta({
  showFilters: true,
  filterConfigs: [...]
})

// Listen to global events instead
onMounted(() => {
  on('filter-change', handleFilterChange)
})
</script>
```

## Complete Example

See `pages/dashboard/example-with-meta-filters.vue` for a complete working example that demonstrates:

- ✅ Meta-based filter configuration
- ✅ Global event handling
- ✅ Filter state management
- ✅ Real-time filtering of data
- ✅ Event logging and debugging

This approach provides a clean, maintainable way to add filtering capabilities to any dashboard page with minimal boilerplate code.
