<template>
  <div class="space-y-6">
    <div>
      <h2 class="text-md font-semibold text-gray-800 dark:text-gray-100 mb-4">Outbound Communication & API Keys</h2>
      <p class="text-sm text-gray-600 dark:text-gray-400">Configure SMTP for outbound emails, manage platform API keys, and set up webhook endpoints.</p>
    </div>

    <!-- SMTP Server Configuration Section -->
    <UiCard title="SMTP Server Configuration (for Outbound Email)">
      <div class="space-y-4">
        <UiToggle v-model="smtpSettings.enableCustomSmtp" label="Enable Custom SMTP Server" description="If disabled, the platform's default mailer will be used." />
        
        <div v-if="smtpSettings.enableCustomSmtp" class="space-y-4 pt-2 border-t mt-4">
          <div>
            <label for="smtpHost" class="block text-sm font-medium text-gray-700 dark:text-gray-300">SMTP Host</label>
            <UiInput id="smtpHost" :name="'smtpHost'" v-model="smtpSettings.host" type="text" placeholder="smtp.example.com" class="mt-1" />
          </div>
          <div>
            <label for="smtpPort" class="block text-sm font-medium text-gray-700 dark:text-gray-300">SMTP Port</label>
            <UiInput id="smtpPort" :name="'smtpPort'" v-model.number="smtpSettings.port" type="number" placeholder="e.g., 587" class="mt-1" />
          </div>
          <div>
            <label for="smtpUsername" class="block text-sm font-medium text-gray-700 dark:text-gray-300">SMTP Username</label>
            <UiInput id="smtpUsername" :name="'smtpUsername'" v-model="smtpSettings.username" type="text" placeholder="your_username" class="mt-1" />
          </div>
          <div>
            <label for="smtpPassword" class="block text-sm font-medium text-gray-700 dark:text-gray-300">SMTP Password</label>
            <UiInput id="smtpPassword" :name="'smtpPassword'" v-model="smtpSettings.password" type="password" placeholder="your_password" class="mt-1" />
          </div>
          <div>
            <label for="smtpEncryption" class="block text-sm font-medium text-gray-700 dark:text-gray-300">SMTP Encryption</label>
            <UiSelect id="smtpEncryption" :name="'smtpEncryption'" v-model="smtpSettings.encryption" :options="smtpEncryptionOptions" class="mt-1" />
          </div>
          <UiButton variant="outline" @click="sendTestEmail" :disabled="!smtpSettings.host || !smtpSettings.port">Send Test Email</UiButton>
        </div>
        
        <div>
          <label for="defaultFromName" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Default "From" Name</label>
          <UiInput id="defaultFromName" :name="'defaultFromName'" v-model="smtpSettings.defaultFromName" type="text" placeholder="e.g., Your Platform Name" class="mt-1" />
        </div>
        <div>
          <label for="defaultFromEmail" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Default "From" Email Address</label>
          <UiInput id="defaultFromEmail" :name="'defaultFromEmail'" v-model="smtpSettings.defaultFromEmail" type="email" placeholder="e.g., <EMAIL>" class="mt-1" />
        </div>
      </div>
    </UiCard>

    <!-- Platform API Key Management Section -->
    <UiCard title="Platform API Key Management">
      <div class="space-y-3">
        <p class="text-sm text-gray-600 dark:text-gray-400">Manage API keys for programmatic access to the platform.</p>
        <!-- Placeholder for API Key List -->
        <div class="border rounded-md p-4 text-center text-gray-500 dark:text-gray-400">
          <p>[API Key List Placeholder]</p>
          <p class="text-xs">(Display existing keys: Name, Partial Key, Created, Last Used, Status)</p>
        </div>
        <UiButton @click="generateApiKey">Generate New API Key</UiButton>
        <!-- TODO: Implement API key generation modal and list display with actions (Revoke, Edit) -->
      </div>
    </UiCard>

    <!-- Webhook Endpoints Section -->
    <UiCard title="Webhook Endpoints (for Outbound Notifications)">
      <div class="space-y-3">
        <p class="text-sm text-gray-600 dark:text-gray-400">Configure webhook endpoints to send platform events to external services.</p>
        <!-- Placeholder for Webhook List -->
        <div class="border rounded-md p-4 text-center text-gray-500 dark:text-gray-400">
          <p>[Webhook List Placeholder]</p>
          <p class="text-xs">(Display configured webhooks: Name, URL, Events, Status)</p>
        </div>
        <UiButton @click="addWebhook">Add New Webhook</UiButton>
        <!-- TODO: Implement webhook configuration modal and list display with actions (Edit, Disable, Delete, Test) -->
      </div>
    </UiCard>

  </div>
</template>

<script setup lang="ts">
import { reactive } from 'vue';
import UiCard from '~/components/ui/UiCard.vue';
import UiToggle from '~/components/ui/UiToggle.vue';
import UiInput from '~/components/ui/UiInput.vue';
import UiSelect from '~/components/ui/UiSelect.vue';
import UiButton from '~/components/ui/UiButton.vue';

// SMTP Server Configuration
const smtpSettings = reactive({
  enableCustomSmtp: false,
  host: '',
  port: 587,
  username: '',
  password: '',
  encryption: 'STARTTLS', // 'None', 'SSL/TLS', 'STARTTLS'
  defaultFromName: '',
  defaultFromEmail: '',
});

const smtpEncryptionOptions = [
  { label: 'None', value: 'None' },
  { label: 'SSL/TLS', value: 'SSL/TLS' },
  { label: 'STARTTLS', value: 'STARTTLS' },
];

const sendTestEmail = () => {
  // TODO: Implement test email functionality
  alert(`Test email to be sent using: ${smtpSettings.host}:${smtpSettings.port} (Placeholder)`);
};

// Platform API Key Management
const generateApiKey = () => {
  // TODO: Implement API key generation modal
  alert('Generate New API Key modal placeholder.');
};

// Webhook Endpoints
const addWebhook = () => {
  // TODO: Implement Add New Webhook modal
  alert('Add New Webhook modal placeholder.');
};

// TODO: Load initial values from backend and implement save logic for these settings
// TODO: Implement full CRUD for API Keys and Webhooks, likely involving modals and more state.
</script>
