<template>
  <div class="chart-examples space-y-8">
    <!-- Basic Bar Chart -->
    <div class="example-section">
      <h3 class="text-lg font-semibold mb-4">Basic Bar Chart</h3>
      <div class="bg-white dark:bg-gray-800 p-6 rounded-lg border">
        <UiBarChart
          title="Monthly Sales"
          :labels="['Jan', 'Feb', 'Mar', 'Apr', 'May']"
          :datasets="[{
            label: 'Sales',
            data: [12, 19, 15, 25, 22],
            color: '#3b82f6'
          }]"
          height="300px"
        />
      </div>
      <details class="mt-4">
        <summary class="cursor-pointer text-sm font-medium text-gray-600 dark:text-gray-400">
          Show Code
        </summary>
        <pre class="mt-2 p-4 bg-gray-100 dark:bg-gray-900 rounded text-sm overflow-x-auto"><code>&lt;UiBarChart
  title="Monthly Sales"
  :labels="['Jan', 'Feb', 'Mar', 'Apr', 'May']"
  :datasets="[{
    label: 'Sales',
    data: [12, 19, 15, 25, 22],
    color: '#3b82f6'
  }]"
  height="300px"
/&gt;</code></pre>
      </details>
    </div>

    <!-- Area Chart with Multiple Series -->
    <div class="example-section">
      <h3 class="text-lg font-semibold mb-4">Area Chart with Multiple Series</h3>
      <div class="bg-white dark:bg-gray-800 p-6 rounded-lg border">
        <UiAreaChart
          title="User Growth"
          :labels="['Week 1', 'Week 2', 'Week 3', 'Week 4']"
          :datasets="[
            {
              label: 'New Users',
              data: [100, 150, 200, 180],
              color: '#10b981'
            },
            {
              label: 'Returning Users',
              data: [80, 120, 160, 140],
              color: '#f59e0b'
            }
          ]"
          :smooth="true"
          fill-opacity="0.2"
          height="300px"
        />
      </div>
    </div>

    <!-- Line Chart with Styling -->
    <div class="example-section">
      <h3 class="text-lg font-semibold mb-4">Styled Line Chart</h3>
      <div class="bg-white dark:bg-gray-800 p-6 rounded-lg border">
        <UiLineChart
          title="Performance Metrics"
          :labels="['00:00', '06:00', '12:00', '18:00']"
          :datasets="[{
            label: 'Response Time (ms)',
            data: [120, 150, 180, 140],
            color: '#8b5cf6',
            borderDash: [5, 5]
          }]"
          :smooth="false"
          point-size="6"
          height="300px"
        />
      </div>
    </div>

    <!-- Donut Chart with Center Content -->
    <div class="example-section">
      <h3 class="text-lg font-semibold mb-4">Donut Chart</h3>
      <div class="bg-white dark:bg-gray-800 p-6 rounded-lg border">
        <div class="max-w-md mx-auto">
          <UiDonutChart
            title="Project Status"
            :data="[
              { label: 'Completed', value: 45, color: '#10b981' },
              { label: 'In Progress', value: 30, color: '#f59e0b' },
              { label: 'Pending', value: 25, color: '#ef4444' }
            ]"
            :center-value="100"
            center-label="Total Tasks"
            height="350px"
          />
        </div>
      </div>
    </div>

    <!-- Progress Circles -->
    <div class="example-section">
      <h3 class="text-lg font-semibold mb-4">Progress Circles</h3>
      <div class="bg-white dark:bg-gray-800 p-6 rounded-lg border">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <UiProgressCircle
            :value="75"
            label="Completion"
            unit="%"
            size="120px"
            progress-color="#10b981"
          />
          
          <UiProgressCircle
            :value="450"
            :target="600"
            label="Revenue"
            unit="K"
            size="120px"
            progress-color="#3b82f6"
            :show-details="true"
          />
          
          <UiProgressCircle
            :value="8"
            :target="10"
            label="Goals"
            size="120px"
            progress-color="#f59e0b"
            :gradient="{ from: '#f59e0b', to: '#ef4444' }"
          />
        </div>
      </div>
    </div>

    <!-- Project Tracker -->
    <div class="example-section">
      <h3 class="text-lg font-semibold mb-4">Project Tracker</h3>
      <div class="bg-white dark:bg-gray-800 p-6 rounded-lg border">
        <UiTracker
          title="Development Milestones"
          :items="trackerItems"
          :show-progress-bar="true"
          :show-actions="false"
        />
      </div>
    </div>

    <!-- Interactive Example -->
    <div class="example-section">
      <h3 class="text-lg font-semibold mb-4">Interactive Chart</h3>
      <div class="bg-white dark:bg-gray-800 p-6 rounded-lg border">
        <div class="mb-4 flex gap-2">
          <UiButton @click="updateData" size="sm">
            Update Data
          </UiButton>
          <UiButton @click="toggleAnimation" variant="secondary" size="sm">
            {{ animated ? 'Disable' : 'Enable' }} Animation
          </UiButton>
        </div>
        
        <UiBarChart
          title="Interactive Chart"
          subtitle="Click buttons above to interact"
          :labels="interactiveLabels"
          :datasets="interactiveDatasets"
          :animated="animated"
          height="300px"
          @chart-click="onChartClick"
        />
        
        <div v-if="lastClickInfo" class="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded">
          <p class="text-sm text-blue-800 dark:text-blue-200">
            Last clicked: {{ lastClickInfo }}
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'

// Interactive state
const animated = ref(true)
const lastClickInfo = ref('')

// Interactive chart data
const interactiveLabels = ref(['A', 'B', 'C', 'D', 'E'])
const interactiveDatasets = ref([{
  label: 'Values',
  data: [10, 20, 15, 25, 18],
  color: '#3b82f6'
}])

// Tracker data
const trackerItems = ref([
  {
    id: 1,
    title: 'Requirements Analysis',
    description: 'Gather and analyze project requirements',
    status: 'completed' as const,
    priority: 'high' as const,
    dueDate: '2024-01-15'
  },
  {
    id: 2,
    title: 'Design Phase',
    description: 'Create UI/UX designs and prototypes',
    status: 'completed' as const,
    priority: 'high' as const,
    dueDate: '2024-01-30'
  },
  {
    id: 3,
    title: 'Development',
    description: 'Implement features and functionality',
    status: 'in-progress' as const,
    priority: 'high' as const,
    dueDate: '2024-02-28'
  },
  {
    id: 4,
    title: 'Testing',
    description: 'Quality assurance and testing',
    status: 'pending' as const,
    priority: 'medium' as const,
    dueDate: '2024-03-15'
  }
])

// Methods
const updateData = () => {
  interactiveDatasets.value[0].data = interactiveDatasets.value[0].data.map(() => 
    Math.floor(Math.random() * 30) + 5
  )
}

const toggleAnimation = () => {
  animated.value = !animated.value
}

const onChartClick = (event: any, elements: any[]) => {
  if (elements.length > 0) {
    const element = elements[0]
    const label = interactiveLabels.value[element.index]
    const value = interactiveDatasets.value[0].data[element.index]
    lastClickInfo.value = `${label}: ${value}`
  }
}
</script>

<style scoped>
.example-section {
  @apply border-b border-gray-200 dark:border-gray-700 pb-8 last:border-b-0;
}

details summary {
  @apply hover:text-gray-800 dark:hover:text-gray-200 transition-colors;
}

details[open] summary {
  @apply mb-2;
}

pre code {
  @apply text-gray-800 dark:text-gray-200;
}
</style>
