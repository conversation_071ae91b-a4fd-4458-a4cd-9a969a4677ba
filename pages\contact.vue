<template>
  <div class="contact-page">
    <!-- Hero Section -->
    <HeroSection
      title="Contact Our Team"
      subtitle="We're here to help! Get in touch with our experts for personalized support and guidance."
      :badge="{
        text: '🚀 Quick Response Guaranteed',
        variant: 'success'
      }"
      :actions="[
        {
          text: 'Schedule a Call',
          variant: 'primary',
          to: '#contact-form',
          icon: 'material-symbols:call'
        },
        {
          text: 'Live Chat',
          variant: 'outline',
          href: '#',
          icon: 'material-symbols:chat'
        }
      ]"
      variant="primary"
      size="md"
    />

    <!-- Contact Information Cards -->
    <section class="py-16 bg-white">
      <div class="container mx-auto px-6">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
          <!-- Email Card -->
          <div class="text-center p-8 bg-gray-50 rounded-xl hover:shadow-lg transition-shadow duration-300">
            <div class="w-16 h-16 bg-brandPrimary-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Icon name="material-symbols:mail" class="w-8 h-8 text-brandPrimary-600" />
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-2">Email Us</h3>
            <p class="text-gray-600 mb-4">Get in touch via email for detailed inquiries</p>
            <a href="mailto:<EMAIL>" class="text-brandPrimary-600 hover:text-brandPrimary-700 font-medium">
              <EMAIL>
            </a>
          </div>

          <!-- Phone Card -->
          <div class="text-center p-8 bg-gray-50 rounded-xl hover:shadow-lg transition-shadow duration-300">
            <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Icon name="material-symbols:call" class="w-8 h-8 text-green-600" />
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-2">Call Us</h3>
            <p class="text-gray-600 mb-4">Speak directly with our support team</p>
            <a href="tel:******-123-4567" class="text-green-600 hover:text-green-700 font-medium">
              +****************
            </a>
          </div>

          <!-- Office Card -->
          <div class="text-center p-8 bg-gray-50 rounded-xl hover:shadow-lg transition-shadow duration-300">
            <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Icon name="material-symbols:location-on" class="w-8 h-8 text-purple-600" />
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-2">Visit Us</h3>
            <p class="text-gray-600 mb-4">Our headquarters location</p>
            <address class="text-purple-600 not-italic">
              123 Legal Street<br>
              San Francisco, CA 94105
            </address>
          </div>
        </div>

        <!-- Contact Form -->
        <div id="contact-form" class="max-w-2xl mx-auto">
          <div class="bg-white rounded-xl shadow-lg p-8">
            <h3 class="text-2xl font-bold text-gray-900 text-center mb-8">Send Us a Message</h3>

            <!-- Success/Error Messages -->
            <div v-if="submissionStatus === 'success'" class="mb-6 bg-green-50 border border-green-200 text-green-800 px-4 py-3 rounded-lg relative" role="alert">
              <div class="flex items-center">
                <Icon name="material-symbols:check-circle" class="w-5 h-5 mr-2" />
                <div>
                  <strong class="font-semibold">Success!</strong>
                  <span class="block sm:inline ml-1">Your message has been sent. We'll get back to you shortly.</span>
                </div>
                <button @click="clearStatus" class="absolute top-3 right-3 text-green-600 hover:text-green-800">
                  <Icon name="material-symbols:close" class="w-5 h-5" />
                </button>
              </div>
            </div>

            <div v-else-if="submissionStatus === 'error'" class="mb-6 bg-red-50 border border-red-200 text-red-800 px-4 py-3 rounded-lg relative" role="alert">
              <div class="flex items-center">
                <Icon name="material-symbols:error" class="w-5 h-5 mr-2" />
                <div>
                  <strong class="font-semibold">Error!</strong>
                  <span class="block sm:inline ml-1">{{ errorMessage }}</span>
                </div>
                <button @click="clearStatus" class="absolute top-3 right-3 text-red-600 hover:text-red-800">
                  <Icon name="material-symbols:close" class="w-5 h-5" />
                </button>
              </div>
            </div>

            <form class="space-y-6" @submit.prevent="handleSubmit">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                    Your Name *
                  </label>
                  <input
                    id="name"
                    name="name"
                    type="text"
                    autocomplete="name"
                    required
                    v-model="formData.name"
                    :disabled="loading"
                    class="w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-brandPrimary-500 focus:border-brandPrimary-500 transition-colors duration-200"
                    placeholder="Enter your full name"
                  />
                </div>

                <div>
                  <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                    Email Address *
                  </label>
                  <input
                    id="email"
                    name="email"
                    type="email"
                    autocomplete="email"
                    required
                    v-model="formData.email"
                    :disabled="loading"
                    class="w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-brandPrimary-500 focus:border-brandPrimary-500 transition-colors duration-200"
                    placeholder="Enter your email address"
                  />
                </div>
              </div>

              <div>
                <label for="subject" class="block text-sm font-medium text-gray-700 mb-2">
                  Subject
                </label>
                <input
                  id="subject"
                  name="subject"
                  type="text"
                  v-model="formData.subject"
                  :disabled="loading"
                  class="w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-brandPrimary-500 focus:border-brandPrimary-500 transition-colors duration-200"
                  placeholder="What's this about?"
                />
              </div>

              <div>
                <label for="message" class="block text-sm font-medium text-gray-700 mb-2">
                  Your Message *
                </label>
                <textarea
                  id="message"
                  name="message"
                  rows="5"
                  required
                  v-model="formData.message"
                  :disabled="loading"
                  class="w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-brandPrimary-500 focus:border-brandPrimary-500 transition-colors duration-200 resize-none"
                  placeholder="Tell us how we can help you..."
                ></textarea>
              </div>

              <div>
                <UiButton
                  type="submit"
                  :disabled="loading"
                  :loading="loading"
                  variant="primary"
                  size="lg"
                  class="w-full"
                >
                  <Icon v-if="!loading" name="material-symbols:send" class="w-5 h-5 mr-2" />
                  {{ loading ? 'Sending...' : 'Send Message' }}
                </UiButton>
              </div>
            </form>
          </div>
        </div>
      </div>
    </section>

    <!-- Support Hours & Additional Info -->
    <section class="py-16 bg-gray-50">
      <div class="container mx-auto px-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
          <div>
            <h3 class="text-2xl font-bold text-gray-900 mb-6">Support Hours</h3>
            <div class="space-y-4">
              <div class="flex items-center">
                <Icon name="material-symbols:schedule" class="w-5 h-5 text-brandPrimary-600 mr-3" />
                <span class="text-gray-700">Monday - Friday: 9:00 AM - 6:00 PM (EST)</span>
              </div>
              <div class="flex items-center">
                <Icon name="material-symbols:weekend" class="w-5 h-5 text-brandPrimary-600 mr-3" />
                <span class="text-gray-700">Saturday: 10:00 AM - 4:00 PM (EST)</span>
              </div>
              <div class="flex items-center">
                <Icon name="material-symbols:event-busy" class="w-5 h-5 text-gray-400 mr-3" />
                <span class="text-gray-500">Sunday: Closed</span>
              </div>
            </div>
            <div class="mt-8">
              <h4 class="text-lg font-semibold text-gray-900 mb-4">Emergency Support</h4>
              <p class="text-gray-600">For critical issues outside business hours, Enterprise customers can reach our emergency support line.</p>
            </div>
          </div>

          <div>
            <h3 class="text-2xl font-bold text-gray-900 mb-6">Response Times</h3>
            <div class="space-y-4">
              <div class="bg-white p-4 rounded-lg border border-gray-200">
                <div class="flex items-center justify-between">
                  <span class="font-medium text-gray-900">General Inquiries</span>
                  <span class="text-sm bg-blue-100 text-blue-800 px-2 py-1 rounded">24 hours</span>
                </div>
              </div>
              <div class="bg-white p-4 rounded-lg border border-gray-200">
                <div class="flex items-center justify-between">
                  <span class="font-medium text-gray-900">Technical Support</span>
                  <span class="text-sm bg-green-100 text-green-800 px-2 py-1 rounded">4 hours</span>
                </div>
              </div>
              <div class="bg-white p-4 rounded-lg border border-gray-200">
                <div class="flex items-center justify-between">
                  <span class="font-medium text-gray-900">Enterprise Support</span>
                  <span class="text-sm bg-purple-100 text-purple-800 px-2 py-1 rounded">1 hour</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <div class="mt-8 text-center text-sm text-gray-500">
      <p>&copy; {{ new Date().getFullYear() }} Legal SaaS Inc. All rights reserved.</p>
      <div class="mt-2 space-x-4">
        <NuxtLink to="/privacy-policy" class="font-medium text-brandPrimary-600 hover:text-brandPrimary-500">Privacy Policy</NuxtLink>
        <NuxtLink to="/terms-of-service" class="font-medium text-brandPrimary-600 hover:text-brandPrimary-500">Terms of Service</NuxtLink>
      </div>
    </div>
    <!-- Final CTA Section -->
    <CTASection
      title="Ready to Get Started?"
      subtitle="Join thousands of legal professionals who trust LegalFlow to manage their practice efficiently."
      :actions="[
        {
          text: 'Start Free Trial',
          variant: 'primary',
          to: '/auth/signup',
          icon: 'material-symbols:rocket-launch',
          size: 'xl'
        },
        {
          text: 'Schedule Demo',
          variant: 'outline',
          to: '#contact-form',
          icon: 'material-symbols:calendar-month'
        }
      ]"
      :features="[
        '14-day free trial',
        'No credit card required',
        'Full feature access',
        'Expert onboarding'
      ]"
      variant="primary"
      size="md"
    />
  </div>
</template>

<script lang="ts" setup>
// Import components
import HeroSection from '~/components/public/HeroSection.vue'
import CTASection from '~/components/public/CTASection.vue'

// Define page metadata
definePageMeta({
  layout: 'default',
  middleware: ['guest']
})

interface ContactForm {
  name: string
  email: string
  subject: string
  message: string
}

const formData = reactive<ContactForm>({
  name: '',
  email: '',
  subject: '',
  message: ''
})

const loading = ref<boolean>(false)
const submissionStatus = ref<'idle' | 'success' | 'error'>('idle')
const errorMessage = ref<string>('')

const clearStatus = () => {
  submissionStatus.value = 'idle';
  errorMessage.value = '';
};

const handleSubmit = async () => {
  loading.value = true;
  clearStatus(); // Clear any previous status messages

  // In a real application, you'd send this data to a backend API,
  // e.g., for a contact form service or direct email sending.
  // For this example, we'll simulate an asynchronous request.
  console.log('Submitting contact form:', formData);

  try {
    // Simulate API call for 2 seconds
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Simulate a successful response
    submissionStatus.value = 'success';
    // Clear form after successful submission
    formData.name = '';
    formData.email = '';
    formData.subject = '';
    formData.message = '';

    // If simulating an error:
    // throw new Error('Failed to send message. Please try again.');

  } catch (error: any) {
    submissionStatus.value = 'error';
    errorMessage.value = error.message || 'There was an issue sending your message. Please try again later.';
    console.error('Contact form submission error:', error);
  } finally {
    loading.value = false;
  }
};

// useHead for SEO/page title
useHead({
  title: 'Contact Us - Legal SaaS Platform',
  meta: [
    {
      name: 'description',
      content: 'Get in touch with the Legal SaaS team for support, sales inquiries, or general questions.',
    },
  ],
});
</script>

<style scoped>
/* No specific styles needed here, relying entirely on Tailwind CSS */
</style>