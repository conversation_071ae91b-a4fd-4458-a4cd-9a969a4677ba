<template>
  <div class="px-4 sm:px-6 lg:px-8 py-6">
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
      <div class="flex flex-col lg:flex-row gap-4">
        <!-- Search -->
        <div class="flex-1">
          <div class="relative">
            <Icon 
              name="material-symbols:search"
              class="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" 
            />
            <UiInput 
              :id="searchId"
              :name="searchId"
              v-model="searchValue"
              :placeholder="searchPlaceholder"
              class="pl-10 w-full"
              @input="handleSearchInput"
            />
          </div>
        </div>

        <!-- Filters -->
        <div class="flex flex-wrap gap-3">
          <!-- Dynamic Filter Selects -->
          <template v-for="filter in filterConfigs" :key="filter.key">
            <UiSelect 
              :id="`${filter.key}Filter`"
              v-model="filterValues[filter.key]"
              :options="filter.options"
              :placeholder="filter.placeholder"
              :class="filter.class || 'min-w-[120px]'"
              @update:model-value="handleFilterChange(filter.key, $event)"
            />
          </template>

          <!-- Advanced Filters Toggle -->
          <UiButton 
            @click="toggleAdvancedFilters"
            variant="outline" 
            size="sm"
            :class="{ 'bg-brandPrimary/10 text-brandPrimary border-brandPrimary': showAdvancedFilters }"
          >
            <Icon name="material-symbols:tune" class="h-4 w-4 mr-2" />
            Filters
            <Icon 
              :name="showAdvancedFilters ? 'material-symbols:expand-less' : 'material-symbols:expand-more'"
              class="h-4 w-4 ml-1" 
            />
          </UiButton>

          <!-- Clear Filters -->
          <UiButton 
            v-if="hasActiveFilters"
            @click="clearAllFilters"
            variant="ghost" 
            size="sm"
            class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
          >
            <Icon name="material-symbols:clear" class="h-4 w-4 mr-1" />
            Clear
          </UiButton>

          <!-- View Toggle (if enabled) -->
          <div v-if="showViewToggle" class="flex gap-1 border border-gray-200 dark:border-gray-600 rounded-lg p-1">
            <UiButton 
              @click="handleViewChange('table')"
              :variant="currentView === 'table' ? 'primary' : 'ghost'"
              size="sm"
              class="px-2"
            >
              <Icon name="material-symbols:table-rows" class="h-4 w-4" />
            </UiButton>
            <UiButton 
              @click="handleViewChange('cards')"
              :variant="currentView === 'cards' ? 'primary' : 'ghost'"
              size="sm"
              class="px-2"
            >
              <Icon name="material-symbols:grid-view" class="h-4 w-4" />
            </UiButton>
          </div>
        </div>
      </div>

      <!-- Advanced Filters Panel -->
      <Transition 
        enter-active-class="transition-all duration-300 ease-out"
        enter-from-class="opacity-0 max-h-0"
        enter-to-class="opacity-100 max-h-96"
        leave-active-class="transition-all duration-300 ease-in"
        leave-from-class="opacity-100 max-h-96"
        leave-to-class="opacity-0 max-h-0"
      >
        <div 
          v-if="showAdvancedFilters !== undefined && advancedFilterConfigs.length > 0"
          class="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700 overflow-hidden"
        >
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <!-- Dynamic Advanced Filters -->
            <template v-for="filter in advancedFilterConfigs" :key="filter.key">
              <!-- Date Range Filter -->
              <div v-if="filter.type === 'dateRange'">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {{ filter.label }}
                </label>
                <div class="flex gap-2">
                  <UiInput 
                    :id="`${filter.key}-FromFilter`"
                    :name="`${filter.key}-FromFilter`"
                    type="date"
                    v-model="filterValues[`${filter.key}From`]"
                    placeholder="From"
                    class="flex-1"
                    @input="handleFilterChange(`${filter.key}From`, $event.target.value)"
                  />
                  <UiInput 
                    :id="`${filter.key}-ToFilter`"
                    :name="`${filter.key}-ToFilter`"
                    type="date"
                    v-model="filterValues[`${filter.key}To`]"
                    placeholder="To"
                    class="flex-1"
                    @input="handleFilterChange(`${filter.key}To`, $event.target.value)"
                  />
                </div>
              </div>

              <!-- Number Range Filter -->
              <div v-else-if="filter.type === 'numberRange'">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {{ filter.label }}
                </label>
                <div class="flex gap-2">
                  <UiInput 
                    :id="`${filter.key}-MinFilter`"
                    :name="`${filter.key}-MinFilter`"
                    type="number"
                    v-model="filterValues[`${filter.key}Min`]"
                    :placeholder="filter.minPlaceholder || 'Min'"
                    class="flex-1"
                    @input="handleFilterChange(`${filter.key}Min`, $event.target.value)"
                  />
                  <UiInput 
                    :id="`${filter.key}-MaxFilter`"
                    :name="`${filter.key}-MaxFilter}`"
                    type="number"
                    v-model="filterValues[`${filter.key}Max`]"
                    :placeholder="filter.maxPlaceholder || 'Max'"
                    class="flex-1"
                    @input="handleFilterChange(`${filter.key}Max`, $event.target.value)"
                  />
                </div>
              </div>

              <!-- Select Filter -->
              <div v-else-if="filter.type === 'select'">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {{ filter.label }}
                </label>
                <UiSelect 
                  :id="`advanced${filter.key}Filter`"
                  v-model="filterValues[filter.key]"
                  :options="filter.options"
                  :placeholder="filter.placeholder"
                  @update:model-value="handleFilterChange(filter.key, $event)"
                />
              </div>

              <!-- Custom Slot for Advanced Filters -->
              <div v-else-if="filter.type === 'custom'">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {{ filter.label }}
                </label>
                <slot :name="`advanced-filter-${filter.key}`" :filter="filter" :value="filterValues[filter.key]" />
              </div>
            </template>
          </div>
        </div>
      </Transition>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'

interface FilterOption {
  label: string
  value: string | number
}

interface FilterConfig {
  key: string
  options: FilterOption[]
  placeholder: string
  class?: string
}

interface AdvancedFilterConfig {
  key: string
  type: 'dateRange' | 'numberRange' | 'select' | 'custom'
  label: string
  placeholder?: string
  minPlaceholder?: string
  maxPlaceholder?: string
  options?: FilterOption[]
}

interface Props {
  // Search configuration
  searchPlaceholder?: string
  searchId?: string
  
  // Filter configurations
  filterConfigs?: FilterConfig[]
  advancedFilterConfigs?: AdvancedFilterConfig[]
  
  // View toggle
  showViewToggle?: boolean
  defaultView?: 'table' | 'cards'
  
  // Initial values
  initialSearch?: string
  initialFilters?: Record<string, any>
  
  // Debounce settings
  searchDebounce?: number
}

interface Emits {
  (e: 'search', value: string): void
  (e: 'filter-change', key: string, value: any): void
  (e: 'filters-change', filters: Record<string, any>): void
  (e: 'clear-filters'): void
  (e: 'view-change', view: 'table' | 'cards'): void
  (e: 'advanced-filters-toggle', show: boolean): void
}

const props = withDefaults(defineProps<Props>(), {
  searchPlaceholder: 'Search...',
  searchId: 'globalSearch',
  filterConfigs: () => [],
  advancedFilterConfigs: () => [],
  showViewToggle: false,
  defaultView: 'table',
  initialSearch: '',
  initialFilters: () => ({}),
  searchDebounce: 300
})

const emit = defineEmits<Emits>()

// State
const searchValue = ref(props.initialSearch)
const filterValues = ref<Record<string, any>>({ ...props.initialFilters })
const showAdvancedFilters = ref(false)
const currentView = ref(props.defaultView)

// Computed
const hasActiveFilters = computed(() => {
  return Object.values(filterValues.value).some(value => 
    value !== null && value !== undefined && value !== ''
  ) || searchValue.value !== ''
})

// Methods
const handleSearchInput = (event: Event) => {
  const target = event.target as HTMLInputElement
  searchValue.value = target.value
}

const handleFilterChange = (key: string, value: any) => {
  filterValues.value[key] = value
  emit('filter-change', key, value)
  emit('filters-change', { ...filterValues.value })
}

const clearAllFilters = () => {
  searchValue.value = ''
  filterValues.value = {}
  emit('clear-filters')
  emit('filters-change', {})
}

const toggleAdvancedFilters = () => {
  showAdvancedFilters.value = !showAdvancedFilters.value
  emit('advanced-filters-toggle', showAdvancedFilters.value)
}

const handleViewChange = (view: 'table' | 'cards') => {
  currentView.value = view
  emit('view-change', view)
}

// Watchers
let searchTimeout: NodeJS.Timeout

watch(searchValue, (newValue) => {
  clearTimeout(searchTimeout)
  searchTimeout = setTimeout(() => {
    emit('search', newValue)
  }, props.searchDebounce)
})

// Expose methods for parent components
defineExpose({
  clearFilters: clearAllFilters,
  setSearch: (value: string) => { searchValue.value = value },
  setFilter: (key: string, value: any) => { filterValues.value[key] = value },
  toggleAdvanced: toggleAdvancedFilters
})
</script>
