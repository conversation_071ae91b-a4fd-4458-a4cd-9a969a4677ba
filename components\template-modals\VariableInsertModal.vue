<template>
  <UiModal @close="$emit('close')" size="lg">
    <div class="p-6">
      <!-- Header -->
      <div class="flex items-center space-x-3 mb-6">
        <div class="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
          <Icon name="heroicons:variable" class="w-5 h-5 text-blue-600 dark:text-blue-400" />
        </div>
        <div>
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Insert Variable</h3>
          <p class="text-sm text-gray-600 dark:text-gray-400">Add dynamic variables to your template</p>
        </div>
      </div>

      <!-- Search -->
      <div class="mb-6">
        <UiInput
          v-model="searchQuery"
          placeholder="Search variables..."
          class="w-full"
        >
          <template #prefix>
            <Icon name="heroicons:magnifying-glass" class="w-4 h-4 text-gray-400" />
          </template>
        </UiInput>
      </div>

      <!-- Variable Categories -->
      <div class="space-y-6">
        <div
          v-for="category in filteredCategories"
          :key="category.name"
          class="space-y-3"
        >
          <h4 class="text-sm font-medium text-gray-900 dark:text-white flex items-center space-x-2">
            <Icon :name="category.icon" class="w-4 h-4" />
            <span>{{ category.name }}</span>
            <span class="text-xs text-gray-500 dark:text-gray-400">({{ category.variables.length }})</span>
          </h4>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-2">
            <button
              v-for="variable in category.variables"
              :key="variable.name"
              @click="selectVariable(variable.name)"
              class="flex items-start space-x-3 p-3 text-left rounded-lg border border-gray-200 dark:border-gray-700 hover:border-blue-300 dark:hover:border-blue-600 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors"
            >
              <div class="flex-1 min-w-0">
                <div class="text-sm font-medium text-gray-900 dark:text-white">
                  {{ variable.name }}
                </div>
                <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  {{ variable.description }}
                </div>
                <div class="text-xs font-mono text-blue-600 dark:text-blue-400 mt-1">
                  {{ variable.name }}
                </div>
              </div>
            </button>
          </div>
        </div>
      </div>

      <!-- Custom Variable -->
      <div class="border-t border-gray-200 dark:border-gray-700 pt-6 mt-6">
        <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-3">Create Custom Variable</h4>
        <div class="flex items-center space-x-3">
          <UiInput
            v-model="customVariableName"
            placeholder="Enter variable name (e.g., clientName)"
            class="flex-1"
            @keyup.enter="createCustomVariable"
          />
          <UiButton @click="createCustomVariable" :disabled="!customVariableName.trim()">
            <Icon name="heroicons:plus" class="w-4 h-4 mr-1" />
            Add
          </UiButton>
        </div>
        <p class="text-xs text-gray-500 dark:text-gray-400 mt-2">
          Use camelCase for variable names (e.g., firstName, companyAddress)
        </p>
      </div>

      <!-- Actions -->
      <div class="flex items-center justify-end space-x-3 mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
        <UiButton @click="$emit('close')" variant="outline">
          Cancel
        </UiButton>
      </div>
    </div>
  </UiModal>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

// Emits
const emit = defineEmits<{
  close: []
  insert: [variable: string]
}>()

// State
const searchQuery = ref('')
const customVariableName = ref('')

// Variable categories with common template variables
const variableCategories = [
  {
    name: 'Client Information',
    icon: 'heroicons:user',
    variables: [
      { name: 'clientName', description: 'Full name of the client' },
      { name: 'clientFirstName', description: 'Client\'s first name' },
      { name: 'clientLastName', description: 'Client\'s last name' },
      { name: 'clientEmail', description: 'Client\'s email address' },
      { name: 'clientPhone', description: 'Client\'s phone number' },
      { name: 'clientAddress', description: 'Client\'s full address' },
      { name: 'clientCompany', description: 'Client\'s company name' },
      { name: 'clientTitle', description: 'Client\'s job title' },
    ]
  },
  {
    name: 'Case Information',
    icon: 'heroicons:scale',
    variables: [
      { name: 'caseNumber', description: 'Unique case identifier' },
      { name: 'caseTitle', description: 'Title or name of the case' },
      { name: 'caseType', description: 'Type of legal case' },
      { name: 'caseStatus', description: 'Current status of the case' },
      { name: 'caseDescription', description: 'Brief description of the case' },
      { name: 'courtName', description: 'Name of the court' },
      { name: 'judgeAssigned', description: 'Assigned judge name' },
      { name: 'filingDate', description: 'Date the case was filed' },
    ]
  },
  {
    name: 'Company Information',
    icon: 'heroicons:building-office',
    variables: [
      { name: 'companyName', description: 'Your law firm name' },
      { name: 'companyAddress', description: 'Law firm address' },
      { name: 'companyPhone', description: 'Law firm phone number' },
      { name: 'companyEmail', description: 'Law firm email address' },
      { name: 'companyWebsite', description: 'Law firm website URL' },
      { name: 'barNumber', description: 'Attorney bar number' },
      { name: 'attorneyName', description: 'Attorney name' },
      { name: 'attorneyTitle', description: 'Attorney title' },
    ]
  },
  {
    name: 'Document Information',
    icon: 'heroicons:document-text',
    variables: [
      { name: 'documentTitle', description: 'Title of the document' },
      { name: 'documentDate', description: 'Date the document was created' },
      { name: 'documentNumber', description: 'Document reference number' },
      { name: 'documentVersion', description: 'Document version number' },
      { name: 'effectiveDate', description: 'Date the document takes effect' },
      { name: 'expirationDate', description: 'Date the document expires' },
      { name: 'signatureDate', description: 'Date of signature' },
      { name: 'witnessName', description: 'Name of witness' },
    ]
  },
  {
    name: 'Financial Information',
    icon: 'heroicons:currency-dollar',
    variables: [
      { name: 'invoiceNumber', description: 'Invoice reference number' },
      { name: 'invoiceDate', description: 'Date of invoice' },
      { name: 'dueDate', description: 'Payment due date' },
      { name: 'amount', description: 'Total amount' },
      { name: 'subtotal', description: 'Subtotal before tax' },
      { name: 'taxAmount', description: 'Tax amount' },
      { name: 'hourlyRate', description: 'Attorney hourly rate' },
      { name: 'totalHours', description: 'Total billable hours' },
    ]
  },
  {
    name: 'Date & Time',
    icon: 'heroicons:calendar',
    variables: [
      { name: 'currentDate', description: 'Today\'s date' },
      { name: 'currentTime', description: 'Current time' },
      { name: 'currentYear', description: 'Current year' },
      { name: 'currentMonth', description: 'Current month' },
      { name: 'appointmentDate', description: 'Scheduled appointment date' },
      { name: 'appointmentTime', description: 'Scheduled appointment time' },
      { name: 'deadlineDate', description: 'Important deadline date' },
      { name: 'reminderDate', description: 'Reminder date' },
    ]
  }
]

// Computed
const filteredCategories = computed(() => {
  if (!searchQuery.value.trim()) {
    return variableCategories
  }
  
  const query = searchQuery.value.toLowerCase()
  return variableCategories.map(category => ({
    ...category,
    variables: category.variables.filter(variable =>
      variable.name.toLowerCase().includes(query) ||
      variable.description.toLowerCase().includes(query)
    )
  })).filter(category => category.variables.length > 0)
})

// Methods
const selectVariable = (variableName: string) => {
  emit('insert', variableName)
}

const createCustomVariable = () => {
  const variableName = customVariableName.value.trim()
  if (!variableName) return
  
  // Validate variable name (camelCase, no spaces, no special characters)
  const isValidVariableName = /^[a-zA-Z][a-zA-Z0-9]*$/.test(variableName)
  if (!isValidVariableName) {
    alert('Variable name must be in camelCase format (e.g., firstName, companyAddress)')
    return
  }
  
  emit('insert', variableName)
  customVariableName.value = ''
}
</script>
