# 🚀 Dynamic Forms System

A comprehensive, production-ready dynamic form system for Vue 3 applications with TypeScript, built for the Legal SaaS Frontend. This system provides powerful form generation capabilities with conditional logic, validation, accessibility, and state management.

## ✨ Features

### 🎯 Core Features
- **15+ Field Types** - Text, email, password, number, textarea, select, multiselect, checkbox, radio, switch, date, time, datetime, file, image, slider, rating, color
- **Dynamic Conditional Logic** - Show/hide fields based on complex conditions with support for nested dependencies
- **Comprehensive Validation** - Built-in validation rules with VeeValidate and Zod integration, plus custom validation support
- **Real-time Feedback** - Instant validation feedback with user-friendly error messages
- **Form State Management** - Complete form state tracking with persistence and auto-save capabilities

### 🎨 User Experience
- **Accessibility First** - WCAG 2.1 compliant with full ARIA support and keyboard navigation
- **Responsive Design** - Mobile-first responsive layouts with touch device optimization
- **Multiple Themes** - Built-in themes (default, minimal, bordered, filled) with custom styling support
- **Loading States** - Visual feedback for submission processes and async operations
- **Error Handling** - Comprehensive error display with summary and inline messages

### 🛠️ Developer Experience
- **TypeScript Support** - Full type safety with comprehensive interfaces and IntelliSense
- **Modular Architecture** - Clean, maintainable component structure
- **Extensive Testing** - Unit tests for all components and composables
- **Rich Documentation** - Complete API documentation with examples
- **Event Hooks** - Lifecycle hooks for external state tracking and custom logic

## 🚀 Quick Start

### Installation

The dynamic forms system is already integrated into your Legal SaaS Frontend. All dependencies are included in your `package.json`.

### Basic Usage

```vue
<template>
  <DynamicForm
    :config="formConfig"
    v-model="formData"
    @submit="handleSubmit"
    @field-change="handleFieldChange"
  />
</template>

<script setup lang="ts">
import { ref } from 'vue'
import type { DynamicFormConfig } from '~/app/shared/types'

const formData = ref({})

const formConfig: DynamicFormConfig = {
  id: 'contact-form',
  title: 'Contact Us',
  description: 'Get in touch with our team',
  fields: [
    {
      id: 'name',
      name: 'name',
      label: 'Full Name',
      type: 'text',
      required: true,
      validation: {
        required: true,
        minLength: 2,
        maxLength: 100
      }
    },
    {
      id: 'email',
      name: 'email',
      label: 'Email Address',
      type: 'email',
      required: true,
      validation: {
        required: true,
        email: true
      }
    },
    {
      id: 'message',
      name: 'message',
      label: 'Message',
      type: 'textarea',
      required: true,
      validation: {
        required: true,
        minLength: 10,
        maxLength: 1000
      }
    }
  ],
  layout: {
    columns: 1,
    spacing: 'md',
    labelPosition: 'top'
  }
}

const handleSubmit = async (data: Record<string, any>) => {
  console.log('Form submitted:', data)
  // Handle form submission
}

const handleFieldChange = (fieldName: string, value: any) => {
  console.log(`Field ${fieldName} changed:`, value)
}
</script>
```

## 📚 Examples

### Basic Contact Form
```vue
<!-- See: components/forms/examples/BasicFormExample.vue -->
<BasicFormExample />
```

### Conditional Registration Form
```vue
<!-- See: components/forms/examples/ConditionalFormExample.vue -->
<ConditionalFormExample />
```

## 🔧 Configuration

### Field Types

| Type | Description | Props |
|------|-------------|-------|
| `text` | Basic text input | `placeholder`, `maxlength` |
| `email` | Email input with validation | `placeholder`, `autocomplete` |
| `password` | Password with strength indicator | `showStrengthIndicator`, `showRequirements` |
| `number` | Numeric input | `min`, `max`, `step` |
| `textarea` | Multi-line text | `rows`, `showCharCount` |
| `select` | Dropdown selection | `options`, `placeholder` |
| `multiselect` | Multiple selection | `options` |
| `checkbox` | Checkbox or group | `options` |
| `radio` | Radio button group | `options` |
| `switch` | Toggle switch | - |
| `date` | Date picker | `min`, `max` |
| `time` | Time picker | - |
| `datetime` | Date and time picker | - |
| `file` | File upload | `accept`, `multiple` |
| `image` | Image upload | `accept` |
| `slider` | Range slider | `min`, `max`, `step` |
| `rating` | Star rating | `maxRating`, `showValue` |
| `color` | Color picker | - |

### Validation Rules

```typescript
{
  validation: {
    required: true,
    minLength: 2,
    maxLength: 100,
    min: 0,
    max: 999,
    pattern: /^[A-Za-z\s]+$/,
    email: true,
    url: true,
    numeric: true,
    integer: true,
    positive: true,
    custom: (value, formData) => {
      if (value === 'forbidden') {
        return 'This value is not allowed'
      }
      return null
    },
    customAsync: async (value, formData) => {
      const isAvailable = await checkAvailability(value)
      return isAvailable ? null : 'This value is already taken'
    },
    messages: {
      required: 'This field is required',
      minLength: 'Minimum 2 characters required'
    }
  }
}
```

### Conditional Logic

```typescript
{
  conditional: {
    show: [
      { field: 'accountType', operator: 'equals', value: 'business' }
    ],
    hide: [
      { field: 'accountType', operator: 'equals', value: 'personal' }
    ],
    require: [
      { field: 'contactMethod', operator: 'contains', value: 'phone' }
    ],
    disable: [
      { field: 'isMinor', operator: 'equals', value: true }
    ]
  }
}
```

### Available Operators

- `equals` / `not_equals` - Exact value comparison
- `contains` / `not_contains` - String/array contains
- `in` / `not_in` - Value in array
- `greater_than` / `less_than` - Numeric comparison
- `greater_equal` / `less_equal` - Numeric comparison with equality
- `empty` / `not_empty` - Empty value check
- `regex` - Regular expression match

## 🎨 Styling and Theming

### Built-in Themes

```typescript
{
  styling: {
    theme: 'default' | 'minimal' | 'bordered' | 'filled',
    size: 'sm' | 'md' | 'lg',
    variant: 'default' | 'outlined' | 'filled' | 'ghost',
    colorScheme: 'blue' | 'green' | 'purple' | 'red'
  }
}
```

### Custom Styling

```css
.dynamic-form--custom {
  @apply bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-xl;
}

.dynamic-form--custom .dynamic-form-field__label {
  @apply text-indigo-700 font-semibold;
}
```

## 🧪 Testing

Run the comprehensive test suite:

```bash
# Run all tests
npm run test

# Run form-specific tests
npm run test tests/components/forms/
npm run test tests/composables/useDynamicForm

# Run with coverage
npm run test:coverage
```

### Test Coverage

- ✅ Component rendering and props
- ✅ Form state management
- ✅ Validation logic and error handling
- ✅ Conditional field visibility
- ✅ Event emission and handling
- ✅ Accessibility features
- ✅ Keyboard navigation
- ✅ Loading and submission states

## 📖 API Reference

### Components

- `DynamicForm` - Main form component
- `DynamicFormField` - Individual field wrapper
- `DynamicFormField*` - Specific field type components

### Composables

- `useDynamicFormValidation` - Validation logic
- `useDynamicFormConditionals` - Conditional field logic
- `useDynamicFormState` - Form state management
- `useDynamicFormAccessibility` - Accessibility features

### Types

See `app/shared/types/index.ts` for complete TypeScript interfaces:
- `DynamicFormConfig`
- `DynamicFormField`
- `DynamicFormValidation`
- `DynamicFormConditional`

## 🚀 Advanced Usage

### Form Persistence

```typescript
{
  persistence: {
    enabled: true,
    key: 'my-form-data',
    storage: 'localStorage',
    debounceMs: 1000,
    clearOnSubmit: true
  }
}
```

### Auto-save

```typescript
const formOptions = {
  autoSave: {
    enabled: true,
    debounceMs: 2000,
    onSave: async (data) => {
      await api.saveFormDraft(data)
    }
  }
}
```

### Custom Field Components

```typescript
{
  id: 'customField',
  name: 'customField',
  type: 'custom',
  component: 'MyCustomFieldComponent',
  props: {
    customProp: 'value'
  }
}
```

## 🤝 Contributing

1. Follow the existing code style and patterns
2. Add tests for new features
3. Update documentation for API changes
4. Ensure accessibility compliance
5. Test with keyboard navigation and screen readers

## 📄 License

This dynamic forms system is part of the Legal SaaS Frontend project and follows the same licensing terms.

---

**Built with ❤️ for the Legal SaaS Frontend**

For detailed documentation, see `docs/DYNAMIC_FORMS.md`
