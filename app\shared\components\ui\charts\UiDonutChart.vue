<template>
  <div class="ui-donut-chart relative mt-28">
   <ClientOnly> 
    <DonutChart
      :data="chartData"
      :height="height"
      :labels="chartLabels"
      :hide-legend="hideLegend"
      :radius="radius"
    >
    
      <!-- Center Content Slot -->
      <div v-if="$slots.center || centerLabel || centerValue" class="absolute inset-0 flex items-center justify-center text-center w-full h-full z-10">
        <slot name="center">
          <div class="flex flex-col items-center justify-center align-center ">
            <div v-if="centerValue !== undefined" class="font-semibold text-lg text-gray-900 dark:text-white">
              {{ formatCenterValue(centerValue) }}
            </div>
            <div v-if="centerLabel" class="text-sm text-(--ui-text-muted)">
              {{ centerLabel }}
            </div>
          </div>
        </slot>
      </div>
    </DonutChart>
 </ClientOnly>
    <!-- Custom Legend -->
    <div v-if="showLegend && !hideLegend" class="mt-4 z-0 flex justify-center items-center w-full">
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2">
        <div
          v-for="(item, index) in data"
          :key="index"
          class="flex items-center gap-3 p-2 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors cursor-pointer"
          @click="toggleSegment(index)"
        >
          <div
            class="w-4 h-4 rounded-full flex-shrink-0"
            :style="{ backgroundColor: item.color }"
          />
          <div class="flex-1 min-w-0">
            <div class="text-sm font-medium text-gray-900 dark:text-white truncate">
              {{ item.name }}
            </div>
            <div class="text-xs text-gray-600 dark:text-gray-400">
              {{ formatValue(item.value) }} ({{ calculatePercentage(item.value) }}%)
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

// Types
interface DonutDataItem {
  color: string
  name: string
  value: number
}

interface Props {
  data: DonutDataItem[]
  height?: number
  hideLegend?: boolean
  radius?: number
  showLegend?: boolean
  centerValue?: number | string
  centerLabel?: string
  centerValueFormatter?: (value: number | string) => string
  valueFormatter?: (value: number) => string
}

// Props with defaults
const props = withDefaults(defineProps<Props>(), {
  height: 275,
  hideLegend: true,
  radius: 0,
  showLegend: false
})

// Emits
const emit = defineEmits<{
  segmentToggle: [index: number]
}>()

// Computed
const chartData = computed(() => props.data.map(item => item.value))

const chartLabels = computed(() => props.data.map(item => ({
  color: item.color,
  name: item.name,
  value: item.value
})))

const totalValue = computed(() =>
  props.data.reduce((sum, item) => sum + item.value, 0)
)

// Methods
const formatValue = (value: number): string => {
  if (props.valueFormatter) {
    return props.valueFormatter(value)
  }
  return value.toLocaleString()
}

const formatCenterValue = (value: number | string): string => {
  if (props.centerValueFormatter) {
    return props.centerValueFormatter(value)
  }
  if (typeof value === 'number') {
    return formatValue(value)
  }
  return String(value)
}

const calculatePercentage = (value: number): number => {
  if (totalValue.value === 0) return 0
  return Math.round((value / totalValue.value) * 100)
}

const toggleSegment = (index: number) => {
  emit('segmentToggle', index)
}
</script>

<style scoped>
.ui-donut-chart {
  width: 100%;
}
</style>
