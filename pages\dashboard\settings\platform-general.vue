<template>
    <div class="space-y-6">
    
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
      <h2 class="text-xl font-semibold text-gray-800 dark:text-gray-100 mb-4">Locale & Region</h2>
      <PlatformLocaleSettings />
    </div>

    <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
      <h2 class="text-xl font-semibold text-gray-800 dark:text-gray-100 mb-4">System Notifications</h2>
      <PlatformNotificationSettings />
    </div>

    <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
      <h2 class="text-xl font-semibold text-gray-800 dark:text-gray-100 mb-4">Default Behaviors</h2>
      <PlatformDefaultBehaviors />
    </div>

    </div>
</template>

<script setup lang="ts">
// import UiCard from '~/components/ui/UiCard.vue';
import { PlatformRoles, TenantRoles } from'~/app/features/auth/constants/roles';
// Import necessary components
import PlatformLocaleSettings from '~/components/settings/platform/general/PlatformLocaleSettings.vue';
import PlatformNotificationSettings from '~/components/settings/platform/general/PlatformNotificationSettings.vue';
import PlatformDefaultBehaviors from '~/components/settings/platform/general/PlatformDefaultBehaviors.vue';


definePageMeta({
  layout: 'dashboard',
  title: 'Platform General Settings',
  subtitle: 'Manage global configurations for the application.',
  middleware: ['rbac'],
  roles: [PlatformRoles.SUPER_ADMIN],
  breadcrumb: [
    { label: 'Dashboard', href: '/dashboard' },
    { label: 'Settings', href: '/dashboard/settings' },
    { label: 'Platform General' },
  ],
});
</script>

<style scoped>
/* Page-specific styles can go here */
</style>