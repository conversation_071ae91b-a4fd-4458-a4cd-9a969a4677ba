<template>
  <div class="space-y-8">
    <!-- Notification Templates Head<PERSON> -->
    <div class="bg-gradient-to-r from-orange-600 to-red-600 rounded-xl p-8 text-white">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-3xl font-bold mb-2">Notification Templates</h1>
          <p class="text-orange-100 text-lg">
            Create and manage email and SMS notification templates for automated communications
          </p>
        </div>
        <div class="hidden md:flex items-center gap-4">
          <UiButton @click="navigateTo('/dashboard/templates/notification/create')" class="bg-white/10 border-white/20 text-white hover:bg-white/20">
            <Icon name="material-symbols:add" class="h-4 w-4 mr-2" />
            Create Template
          </UiButton>
          <UiButton @click="testNotification" variant="outline" class="border-white/20 text-white hover:bg-white/10">
            <Icon name="material-symbols:send" class="h-4 w-4 mr-2" />
            Test Send
          </UiButton>
        </div>
      </div>
    </div>

    <!-- Templates Overview -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <!-- Total Templates -->
      <UiCard
        icon="material-symbols:notifications"
        icon-color="orange"
        :loading="isLoading"
        hover-effect
      >
        <template #header>
          <div class="flex items-center justify-between w-full">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Total Templates</h3>
            <UiBadge variant="info">{{ notificationTemplates.length }}</UiBadge>
          </div>
        </template>
        <div class="space-y-3">
          <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ notificationTemplates.length }}</p>
          <p class="text-sm text-gray-500 dark:text-gray-400">
            {{ activeTemplates }} active, {{ inactiveTemplates }} inactive
          </p>
          <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div 
              class="bg-orange-600 h-2 rounded-full transition-all duration-300"
              :style="{ width: `${(activeTemplates / notificationTemplates.length) * 100}%` }"
            ></div>
          </div>
        </div>
      </UiCard>

      <!-- Sent This Month -->
      <UiCard
        icon="material-symbols:send"
        icon-color="green"
        :loading="isLoading"
        hover-effect
      >
        <template #header>
          <div class="flex items-center justify-between w-full">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Sent This Month</h3>
            <UiBadge variant="success">+{{ deliveryGrowth }}%</UiBadge>
          </div>
        </template>
        <div class="space-y-3">
          <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ formatNumber(monthlySent) }}</p>
          <p class="text-sm text-gray-500 dark:text-gray-400">
            notifications delivered
          </p>
          <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div 
              class="bg-green-600 h-2 rounded-full transition-all duration-300"
              :style="{ width: `${Math.min((monthlySent / monthlyTarget) * 100, 100)}%` }"
            ></div>
          </div>
        </div>
      </UiCard>

      <!-- Delivery Rate -->
      <UiCard
        icon="material-symbols:check-circle"
        icon-color="blue"
        :loading="isLoading"
        hover-effect
      >
        <template #header>
          <div class="flex items-center justify-between w-full">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Delivery Rate</h3>
            <UiBadge :variant="deliveryRate >= 95 ? 'success' : deliveryRate >= 90 ? 'warning' : 'error'">
              {{ deliveryRate }}%
            </UiBadge>
          </div>
        </template>
        <div class="space-y-3">
          <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ deliveryRate }}%</p>
          <p class="text-sm text-gray-500 dark:text-gray-400">
            successful deliveries
          </p>
          <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div 
              :class="[
                'h-2 rounded-full transition-all duration-300',
                deliveryRate >= 95 ? 'bg-green-600' :
                deliveryRate >= 90 ? 'bg-yellow-600' : 'bg-red-600'
              ]"
              :style="{ width: `${deliveryRate}%` }"
            ></div>
          </div>
        </div>
      </UiCard>

      <!-- Open Rate -->
      <UiCard
        icon="material-symbols:visibility"
        icon-color="purple"
        :loading="isLoading"
        hover-effect
      >
        <template #header>
          <div class="flex items-center justify-between w-full">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Open Rate</h3>
            <UiBadge :variant="openRate >= 20 ? 'success' : openRate >= 15 ? 'warning' : 'error'">
              {{ openRate }}%
            </UiBadge>
          </div>
        </template>
        <div class="space-y-3">
          <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ openRate }}%</p>
          <p class="text-sm text-gray-500 dark:text-gray-400">
            email open rate
          </p>
          <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div 
              class="bg-purple-600 h-2 rounded-full transition-all duration-300"
              :style="{ width: `${openRate * 4}%` }"
            ></div>
          </div>
        </div>
      </UiCard>
    </div>

    <!-- Template Types -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Email Templates -->
      <UiCard>
        <template #header>
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-3">
              <div class="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center text-white">
                <Icon name="material-symbols:email" class="h-5 w-5" />
              </div>
              <div>
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Email Templates</h3>
                <p class="text-sm text-gray-500 dark:text-gray-400">Automated email notifications</p>
              </div>
            </div>
            <UiBadge variant="info">{{ emailTemplates.length }}</UiBadge>
          </div>
        </template>
        <div class="space-y-3">
          <div
            v-for="template in emailTemplates.slice(0, 5)"
            :key="template.id"
            class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors duration-200 cursor-pointer"
            @click="viewTemplate(template)"
          >
            <div class="flex items-center gap-3">
              <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded flex items-center justify-center">
                <Icon name="material-symbols:email" class="h-4 w-4 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <p class="text-sm font-medium text-gray-900 dark:text-white">{{ template.name }}</p>
                <p class="text-xs text-gray-500 dark:text-gray-400">{{ template.sent }} sent</p>
              </div>
            </div>
            <div class="flex items-center gap-2">
              <UiBadge :variant="template.isActive ? 'success' : 'neutral'" size="sm">
                {{ template.isActive ? 'Active' : 'Inactive' }}
              </UiBadge>
              <UiButton @click.stop="editTemplate(template)" variant="ghost" size="sm">
                <Icon name="material-symbols:edit" class="h-4 w-4" />
              </UiButton>
            </div>
          </div>
          
          <UiButton 
            @click="createEmailTemplate"
            variant="outline" 
            size="sm" 
            class="w-full"
          >
            <Icon name="material-symbols:add" class="h-4 w-4 mr-2" />
            Create Email Template
          </UiButton>
        </div>
      </UiCard>

      <!-- SMS Templates -->
      <UiCard>
        <template #header>
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-3">
              <div class="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center text-white">
                <Icon name="material-symbols:sms" class="h-5 w-5" />
              </div>
              <div>
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">SMS Templates</h3>
                <p class="text-sm text-gray-500 dark:text-gray-400">Text message notifications</p>
              </div>
            </div>
            <UiBadge variant="info">{{ smsTemplates.length }}</UiBadge>
          </div>
        </template>
        <div class="space-y-3">
          <div
            v-for="template in smsTemplates.slice(0, 5)"
            :key="template.id"
            class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors duration-200 cursor-pointer"
            @click="viewTemplate(template)"
          >
            <div class="flex items-center gap-3">
              <div class="w-8 h-8 bg-green-100 dark:bg-green-900/20 rounded flex items-center justify-center">
                <Icon name="material-symbols:sms" class="h-4 w-4 text-green-600 dark:text-green-400" />
              </div>
              <div>
                <p class="text-sm font-medium text-gray-900 dark:text-white">{{ template.name }}</p>
                <p class="text-xs text-gray-500 dark:text-gray-400">{{ template.sent }} sent</p>
              </div>
            </div>
            <div class="flex items-center gap-2">
              <UiBadge :variant="template.isActive ? 'success' : 'neutral'" size="sm">
                {{ template.isActive ? 'Active' : 'Inactive' }}
              </UiBadge>
              <UiButton @click.stop="editTemplate(template)" variant="ghost" size="sm">
                <Icon name="material-symbols:edit" class="h-4 w-4" />
              </UiButton>
            </div>
          </div>
          
          <UiButton 
            @click="createSmsTemplate"
            variant="outline" 
            size="sm" 
            class="w-full"
          >
            <Icon name="material-symbols:add" class="h-4 w-4 mr-2" />
            Create SMS Template
          </UiButton>
        </div>
      </UiCard>
    </div>

    <!-- Popular Templates -->
    <UiCard>
      <template #header>
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Most Used Templates</h3>
          <UiButton @click="viewAllTemplates" variant="ghost" size="sm">
            View All
          </UiButton>
        </div>
      </template>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <div
          v-for="template in popularTemplates"
          :key="template.id"
          class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors duration-200 cursor-pointer"
          @click="viewTemplate(template)"
        >
          <div class="flex items-center gap-3">
            <div :class="[
              'w-12 h-12 rounded-lg flex items-center justify-center text-white',
              template.type === 'email' ? 'bg-blue-500' : 'bg-green-500'
            ]">
              <Icon :name="template.type === 'email' ? 'material-symbols:email' : 'material-symbols:sms'" class="h-6 w-6" />
            </div>
            <div>
              <p class="text-sm font-medium text-gray-900 dark:text-white">{{ template.name }}</p>
              <p class="text-xs text-gray-500 dark:text-gray-400">{{ template.type.toUpperCase() }} • {{ template.sent }} sent</p>
            </div>
          </div>
          <div class="flex items-center gap-2">
            <UiBadge variant="success" size="sm">Popular</UiBadge>
            <UiButton @click.stop="sendTestNotification(template)" variant="outline" size="sm">
              <Icon name="material-symbols:send" class="h-4 w-4 mr-1" />
              Test
            </UiButton>
          </div>
        </div>
      </div>
    </UiCard>

    <!-- Notification Analytics -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Delivery Statistics -->
      <UiCard>
        <template #header>
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Delivery Statistics</h3>
            <UiButton @click="refreshStats" variant="ghost" size="sm" :loading="refreshingStats">
              <Icon name="material-symbols:refresh" class="h-4 w-4" />
            </UiButton>
          </div>
        </template>
        <div class="space-y-4">
          <div class="space-y-2">
            <div class="flex justify-between text-sm">
              <span class="text-gray-500 dark:text-gray-400">Delivered</span>
              <span class="text-gray-900 dark:text-white">{{ formatNumber(deliveryStats.delivered) }}</span>
            </div>
            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div class="bg-green-600 h-2 rounded-full" :style="{ width: `${(deliveryStats.delivered / deliveryStats.total) * 100}%` }"></div>
            </div>
          </div>
          
          <div class="space-y-2">
            <div class="flex justify-between text-sm">
              <span class="text-gray-500 dark:text-gray-400">Failed</span>
              <span class="text-gray-900 dark:text-white">{{ formatNumber(deliveryStats.failed) }}</span>
            </div>
            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div class="bg-red-600 h-2 rounded-full" :style="{ width: `${(deliveryStats.failed / deliveryStats.total) * 100}%` }"></div>
            </div>
          </div>
          
          <div class="space-y-2">
            <div class="flex justify-between text-sm">
              <span class="text-gray-500 dark:text-gray-400">Pending</span>
              <span class="text-gray-900 dark:text-white">{{ formatNumber(deliveryStats.pending) }}</span>
            </div>
            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div class="bg-yellow-600 h-2 rounded-full" :style="{ width: `${(deliveryStats.pending / deliveryStats.total) * 100}%` }"></div>
            </div>
          </div>
        </div>
      </UiCard>

      <!-- Recent Activity -->
      <UiCard>
        <template #header>
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Recent Activity</h3>
            <UiButton @click="viewActivityLog" variant="ghost" size="sm">
              View Full Log
            </UiButton>
          </div>
        </template>
        <div class="space-y-3">
          <div
            v-for="activity in recentActivity"
            :key="activity.id"
            class="flex items-start gap-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg"
          >
            <div class="flex-shrink-0">
              <div :class="[
                'w-8 h-8 rounded-full flex items-center justify-center',
                activity.type === 'sent' ? 'bg-green-100 dark:bg-green-900/20' :
                activity.type === 'created' ? 'bg-blue-100 dark:bg-blue-900/20' :
                activity.type === 'modified' ? 'bg-yellow-100 dark:bg-yellow-900/20' :
                'bg-gray-100 dark:bg-gray-900/20'
              ]">
                <Icon :name="activity.icon" :class="[
                  'h-4 w-4',
                  activity.type === 'sent' ? 'text-green-600 dark:text-green-400' :
                  activity.type === 'created' ? 'text-blue-600 dark:text-blue-400' :
                  activity.type === 'modified' ? 'text-yellow-600 dark:text-yellow-400' :
                  'text-gray-600 dark:text-gray-400'
                ]" />
              </div>
            </div>
            <div class="flex-1 min-w-0">
              <p class="text-sm text-gray-900 dark:text-white">{{ activity.description }}</p>
              <div class="flex items-center gap-2 mt-1">
                <p class="text-xs text-gray-500 dark:text-gray-400">{{ activity.timestamp }}</p>
                <span class="text-xs text-gray-400">•</span>
                <p class="text-xs text-gray-500 dark:text-gray-400">{{ activity.user }}</p>
              </div>
            </div>
          </div>

          <div v-if="recentActivity.length === 0" class="text-center py-6">
            <Icon name="material-symbols:notifications" class="h-8 w-8 text-gray-400 mx-auto mb-2" />
            <p class="text-sm text-gray-500 dark:text-gray-400">No recent activity</p>
          </div>
        </div>
      </UiCard>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { PlatformRoles } from '~/app/features/auth/constants/roles'

// Page meta configuration
definePageMeta({
  layout: 'dashboard',
  title: 'Notification Templates',
  description: 'Create and manage email and SMS notification templates for automated communications',
  pageHeaderIcon: 'material-symbols:notifications',
  pageHeaderStats: [
    { key: 'templates', label: 'Total Templates', value: '28', color: 'orange' },
    { key: 'sent', label: 'Sent This Month', value: '15.2K', color: 'green' },
    { key: 'delivery', label: 'Delivery Rate', value: '98.5%', color: 'blue' },
    { key: 'open', label: 'Open Rate', value: '24.3%', color: 'purple' }
  ],
  showRealTimeStatus: false,
  autoRefreshEnabled: false,
  middleware: ['rbac'],
  roles: [PlatformRoles.SUPER_ADMIN],
  breadcrumb: [
    { label: 'Dashboard', href: '/dashboard' },
    { label: 'Templates', href: '/dashboard/templates' },
    { label: 'Notification Templates' },
  ],
})

// Reactive state
const isLoading = ref(true)
const refreshingStats = ref(false)
const monthlySent = ref(15234)
const monthlyTarget = ref(20000)
const deliveryGrowth = ref(8.7)
const deliveryRate = ref(98.5)
const openRate = ref(24.3)

// Email templates data
const emailTemplates = ref([
  { id: 1, name: 'Welcome Email', type: 'email', isActive: true, sent: 1234 },
  { id: 2, name: 'Password Reset', type: 'email', isActive: true, sent: 567 },
  { id: 3, name: 'Case Update Notification', type: 'email', isActive: true, sent: 890 },
  { id: 4, name: 'Document Ready', type: 'email', isActive: true, sent: 445 },
  { id: 5, name: 'Payment Reminder', type: 'email', isActive: true, sent: 678 },
  { id: 6, name: 'Appointment Confirmation', type: 'email', isActive: true, sent: 234 },
  { id: 7, name: 'Invoice Generated', type: 'email', isActive: true, sent: 345 },
  { id: 8, name: 'Account Suspended', type: 'email', isActive: false, sent: 12 }
])

// SMS templates data
const smsTemplates = ref([
  { id: 9, name: 'Appointment Reminder', type: 'sms', isActive: true, sent: 2345 },
  { id: 10, name: 'Verification Code', type: 'sms', isActive: true, sent: 1567 },
  { id: 11, name: 'Case Status Update', type: 'sms', isActive: true, sent: 789 },
  { id: 12, name: 'Payment Received', type: 'sms', isActive: true, sent: 456 },
  { id: 13, name: 'Document Signed', type: 'sms', isActive: true, sent: 234 },
  { id: 14, name: 'Emergency Alert', type: 'sms', isActive: false, sent: 5 }
])

// All notification templates
const notificationTemplates = computed(() => [...emailTemplates.value, ...smsTemplates.value])

// Delivery statistics
const deliveryStats = reactive({
  total: 15234,
  delivered: 15000,
  failed: 134,
  pending: 100
})

// Recent activity data
const recentActivity = ref([
  {
    id: 1,
    type: 'sent',
    icon: 'material-symbols:send',
    description: 'Sent 245 "Case Update Notification" emails',
    timestamp: '1 hour ago',
    user: 'System'
  },
  {
    id: 2,
    type: 'created',
    icon: 'material-symbols:add',
    description: 'New "Court Date Reminder" SMS template created',
    timestamp: '3 hours ago',
    user: 'Sarah Admin'
  },
  {
    id: 3,
    type: 'modified',
    icon: 'material-symbols:edit',
    description: 'Updated "Welcome Email" template with new branding',
    timestamp: '5 hours ago',
    user: 'Mike Designer'
  },
  {
    id: 4,
    type: 'sent',
    icon: 'material-symbols:send',
    description: 'Sent 89 "Appointment Reminder" SMS messages',
    timestamp: '1 day ago',
    user: 'System'
  }
])

// Computed properties
const activeTemplates = computed(() => notificationTemplates.value.filter(t => t.isActive).length)
const inactiveTemplates = computed(() => notificationTemplates.value.filter(t => !t.isActive).length)

const popularTemplates = computed(() =>
  notificationTemplates.value
    .sort((a, b) => b.sent - a.sent)
    .slice(0, 6)
)

// Utility functions
const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

// Methods
const viewTemplate = (template: any) => {
  navigateTo(`/dashboard/templates/${template.id}`)
}

const editTemplate = (template: any) => {
  navigateTo(`/dashboard/templates/${template.id}/edit`)
}

const createEmailTemplate = () => {
  navigateTo('/dashboard/templates/create/email')
}

const createSmsTemplate = () => {
  navigateTo('/dashboard/templates/create/sms')
}

const viewAllTemplates = () => {
  navigateTo('/dashboard/templates?type=notification')
}

const viewActivityLog = () => {
  navigateTo('/dashboard/templates/activity?type=notification')
}

const testNotification = async () => {
  try {
    console.log('Opening test notification dialog...')

    // In a real app, you would open a modal to select template and recipient
    const templateId = prompt('Enter template ID to test:')
    const recipient = prompt('Enter recipient email/phone:')

    if (templateId && recipient) {
      console.log(`Sending test notification from template ${templateId} to ${recipient}`)

      // Simulate API call
      // await $api.post('/notifications/test', { templateId, recipient })

      alert('Test notification sent successfully!')
    }

  } catch (error) {
    console.error('Error sending test notification:', error)
    alert('Failed to send test notification')
  }
}

const sendTestNotification = async (template: any) => {
  try {
    console.log(`Sending test notification for template: ${template.name}`)

    const recipient = prompt(`Enter ${template.type === 'email' ? 'email' : 'phone number'} for test:`)

    if (recipient) {
      // Simulate API call
      // await $api.post('/notifications/test', { templateId: template.id, recipient })

      alert(`Test ${template.type} sent successfully to ${recipient}!`)
    }

  } catch (error) {
    console.error('Error sending test notification:', error)
    alert('Failed to send test notification')
  }
}

const refreshStats = async () => {
  try {
    refreshingStats.value = true

    // Simulate API call to refresh statistics
    await new Promise(resolve => setTimeout(resolve, 1000))

    // In a real app, you would fetch fresh stats
    // const stats = await $api.get('/notifications/stats')

    // Simulate some updates
    deliveryStats.delivered = Math.max(14000, deliveryStats.delivered + Math.floor((Math.random() - 0.5) * 100))
    deliveryStats.failed = Math.max(50, deliveryStats.failed + Math.floor((Math.random() - 0.5) * 10))
    deliveryStats.pending = Math.max(50, deliveryStats.pending + Math.floor((Math.random() - 0.5) * 20))
    deliveryStats.total = deliveryStats.delivered + deliveryStats.failed + deliveryStats.pending

    console.log('Notification statistics refreshed')

  } catch (error) {
    console.error('Error refreshing statistics:', error)
  } finally {
    refreshingStats.value = false
  }
}

// Lifecycle hooks
onMounted(async () => {
  // Initial data load
  isLoading.value = false
  console.log('Notification templates loaded')
})
</script>

<style scoped>
/* Enhanced animations and transitions */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* Card hover effects */
.hover\:bg-gray-100:hover {
  background-color: rgb(243 244 246);
}

.dark .hover\:bg-gray-600:hover {
  background-color: rgb(75 85 99);
}

/* Progress bar animations */
.transition-all {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .grid-cols-1 {
    gap: 1rem;
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
</style>
