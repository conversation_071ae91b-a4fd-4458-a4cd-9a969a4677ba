<template>
  <div class="p-6 space-y-4">
    <div class="text-center">
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
        Modal Component Example
      </h3>
      <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
        This component demonstrates how to expose data and methods to parent modals
      </p>
    </div>

    <div class="space-y-4">
      <!-- Form inputs -->
      <div>
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
          Name
        </label>
        <input
          v-model="formData.name"
          type="text"
          class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
          placeholder="Enter your name"
        />
      </div>

      <div>
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
          Email
        </label>
        <input
          v-model="formData.email"
          type="email"
          class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
          placeholder="Enter your email"
        />
      </div>

      <div>
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
          Message
        </label>
        <textarea
          v-model="formData.message"
          rows="3"
          class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
          placeholder="Enter your message"
        />
      </div>

      <!-- Status display -->
      <div class="bg-gray-50 dark:bg-gray-800 p-3 rounded-md">
        <p class="text-sm text-gray-600 dark:text-gray-400">
          Status: <span class="font-medium">{{ status }}</span>
        </p>
        <p class="text-sm text-gray-600 dark:text-gray-400">
          Counter: <span class="font-medium">{{ counter }}</span>
        </p>
      </div>

      <!-- Action buttons -->
      <div class="flex gap-2">
        <button
          @click="incrementCounter"
          class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          Increment Counter
        </button>
        <button
          @click="validateForm"
          class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500"
        >
          Validate Form
        </button>
        <button
          @click="resetForm"
          class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500"
        >
          Reset Form
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'

// Props (if any)
interface Props {
  initialData?: {
    name?: string
    email?: string
    message?: string
  }
}

const props = withDefaults(defineProps<Props>(), {
  initialData: () => ({})
})

// Reactive form data
const formData = reactive({
  name: props.initialData.name || '',
  email: props.initialData.email || '',
  message: props.initialData.message || ''
})

// Other reactive state
const counter = ref(0)
const status = ref('ready')

// Computed properties
const isFormValid = computed(() => {
  return formData.name.trim() && 
         formData.email.trim() && 
         formData.email.includes('@') &&
         formData.message.trim()
})

// Methods that can be called from parent
const incrementCounter = () => {
  counter.value++
  status.value = `Counter incremented to ${counter.value}`
}

const validateForm = () => {
  if (isFormValid.value) {
    status.value = 'Form is valid!'
    return true
  } else {
    status.value = 'Form has validation errors'
    return false
  }
}

const resetForm = () => {
  formData.name = ''
  formData.email = ''
  formData.message = ''
  counter.value = 0
  status.value = 'Form reset'
}

const getFormData = () => {
  return { ...formData }
}

const setFormData = (data: Partial<typeof formData>) => {
  Object.assign(formData, data)
  status.value = 'Form data updated'
}

const submitForm = () => {
  if (validateForm()) {
    status.value = 'Form submitted successfully!'
    return getFormData()
  }
  return null
}

// Expose methods and data to parent components
defineExpose({
  // Data
  formData,
  counter,
  status,
  isFormValid,
  
  // Methods
  incrementCounter,
  validateForm,
  resetForm,
  getFormData,
  setFormData,
  submitForm
})

// Emits
const emit = defineEmits<{
  submit: [data: typeof formData]
  close: []
}>()
</script>
