import { defineStore } from 'pinia';
import { useApi } from '~/composables/useApi';
import { useToastStore } from '~/stores/toast';
import type { GetRequestQuery } from '~/types/api'; // Import directly from the source

// Define interfaces for Template
export interface Template {
  id: string;
  createdAt: string;
  updatedAt: string;
  createdById: string | null;
  updatedById: string | null;
  isDeleted: boolean;
  deletedAt: string | null;
  name: string;
  description: string | null;
  content: string; // Can be JSON, HTML, or plain text depending on use case
  type: string; // e.g., 'email', 'document', 'contract'
  category: string; // New: e.g., "contract", "invoice"
  variables: string[]; // New: e.g., ["name", "company_name"]
  locale: string; // New: e.g., "en", "es"
  channel: string | null; // New: e.g., "email", "sms", or null if not applicable
  tenantId: string | null; // Null if it's a global/system template
  isGlobal: boolean;
  isActive: boolean;
  isDraft?: boolean; // Added for draft status
}

export interface CreateTemplatePayload {
  name: string;
  description?: string | null;
  content: string; // This might become more complex, e.g., JSON for structured content
  type: string;
  category: string; // New: e.g., "contract", "invoice"
  variables?: string[]; // New: e.g., ["name", "company_name"]
  locale?: string; // New: e.g., "en", "es"
  channel?: string | null; // New: e.g., "email", "sms", or null if not applicable
  tenantId?: string | null;
  isGlobal?: boolean; // Retained, though not in example, might be backend-derived or for specific admin use
  isActive?: boolean; // Retained, will be set based on publish/draft action
  isDraft?: boolean; // Retained, UI flag
}

export interface UpdateTemplatePayload {
  name?: string;
  description?: string | null;
  content?: string;
  type?: string;
  category?: string;
  variables?: string[];
  locale?: string;
  channel?: string | null;
  tenantId?: string | null;
  isGlobal?: boolean;
  isActive?: boolean;
  isDraft?: boolean;
}

interface TemplatesResponse {
  data: Template[];
  meta: {
    total: number;
    page: number;
    limit: number;
  };
}

export const useTemplateStore = defineStore('template', {
  state: () => ({
    selectedTemplate: null as Template | null,
    templates: [] as Template[],
    templatesMeta: null as TemplatesResponse['meta'] | null,
    isLoading: false,
    error: null as string | null,
  }),

  getters: {
    hasTemplates: (state) => state.templates.length > 0,
    isLoadingTemplates: (state) => state.isLoading,
  },

  actions: {
    /**
     * Creates a new template.
     * API: POST /templates
     * @param payload The data for the new template.
     * @returns The created template object.
     */
    async createTemplate(payload: CreateTemplatePayload): Promise<Template | null> {
      this.isLoading = true;
      this.error = null;
      try {
        const api = useApi();
        const newTemplate = await api.post<Template>('/templates', payload);
        // Optionally add to local list if needed, or refetch
        // this.templates.push(newTemplate);
        this.showSuccessNotification('Template created successfully.');
        return newTemplate;
      } catch (err: any) {
        this.error = this.handleApiError(err, 'Failed to create template.');
        console.error('Error creating template:', err);
        return null;
      } finally {
        this.isLoading = false;
      }
    },

    /**
     * Lists all templates.
     * API: GET /templates
     * @param query Optional query parameters for pagination, sorting, and filtering.
     */
    async fetchAllTemplates(query?: GetRequestQuery): Promise<void> {
      this.isLoading = true;
      this.error = null;
      try {
        const api = useApi();
        const response = await api.get<TemplatesResponse>('/templates', { params: query });
        this.templates = response.data;
        this.templatesMeta = response.meta;
        console.log('All templates fetched with query:', query, this.templates, this.templatesMeta);
      } catch (err: any) {
        this.error = this.handleApiError(err, 'Failed to fetch all templates.');
        this.templates = [];
        this.templatesMeta = null;
        console.error('Error fetching all templates:', err);
      } finally {
        this.isLoading = false;
      }
    },

    /**
     * Fetches a single template by its ID.
     * API: GET /templates/:id
     * @param id The ID of the template to fetch.
     * @returns The template object or null if not found.
     */
    async fetchTemplateById(id: string): Promise<Template | null> {
      this.isLoading = true;
      this.error = null;
      this.selectedTemplate = null;
      try {
        const api = useApi();
        const template = await api.get<Template>(`/templates/${id}`);
        this.selectedTemplate = template;
        return template;
      } catch (err: any) {
        this.error = this.handleApiError(err, `Failed to fetch template with ID: ${id}.`);
        console.error('Error fetching template by ID:', err);
        return null;
      } finally {
        this.isLoading = false;
      }
    },

    /**
     * Updates a specific template by ID.
     * API: PATCH /templates/:id
     * @param id The ID of the template to update.
     * @param payload The data to update.
     * @returns The updated template object or null on failure.
     */
    async updateTemplate(id: string, payload: UpdateTemplatePayload): Promise<Template | null> {
      this.isLoading = true;
      this.error = null;
      try {
        const api = useApi();
        const updatedTemplate = await api.patch<Template>(`/templates/${id}`, payload);
        // Update the template in the local list if it exists
        const index = this.templates.findIndex(t => t.id === id);
        if (index !== -1) {
          this.templates[index] = { ...this.templates[index], ...updatedTemplate };
        }
        if (this.selectedTemplate?.id === id) {
          this.selectedTemplate = { ...this.selectedTemplate, ...updatedTemplate };
        }
        this.showSuccessNotification('Template updated successfully.');
        return updatedTemplate;
      } catch (err: any) {
        this.error = this.handleApiError(err, `Failed to update template with ID: ${id}.`);
        console.error('Error updating template:', err);
        return null;
      } finally {
        this.isLoading = false;
      }
    },

    /**
     * Deletes a template by ID. (Consider if soft delete is needed)
     * API: DELETE /templates/:id
     * @param id The ID of the template to delete.
     */
    async deleteTemplate(id: string): Promise<boolean> {
      this.isLoading = true;
      this.error = null;
      try {
        const api = useApi();
        await api.delete(`/templates/${id}`);
        this.templates = this.templates.filter(t => t.id !== id);
        if (this.selectedTemplate?.id === id) {
          this.selectedTemplate = null;
        }
        this.showSuccessNotification('Template deleted successfully.');
        return true;
      } catch (err: any) {
        this.error = this.handleApiError(err, `Failed to delete template with ID: ${id}.`);
        console.error('Error deleting template:', err);
        return false;
      } finally {
        this.isLoading = false;
      }
    },

    /**
     * Helper to handle API errors consistently.
     */
    handleApiError(err: any, defaultMessage: string): string {
      if (err.response && err.response.data && err.response.data.message) {
        if (Array.isArray(err.response.data.message)) {
          return err.response.data.message.join(', ');
        }
        return err.response.data.message;
      }
      return err.message || defaultMessage;
    },

    /**
     * Show success notification using toast store.
     */
    showSuccessNotification(message: string) {
      const toastStore = useToastStore();
      toastStore.success(message);
    },
  },
});