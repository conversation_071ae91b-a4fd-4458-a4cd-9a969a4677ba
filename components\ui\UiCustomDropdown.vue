<template>
  <div class="relative inline-block text-left" ref="dropdownRef">
    <!-- Trigger <PERSON>ton -->
    <UiButton
      @click="toggleDropdown"
      :variant="buttonVariant"
      :size="buttonSize"
      :class="triggerClass"
    >
      <slot name="trigger">
        <Icon v-if="triggerIcon" :name="triggerIcon" class="h-4 w-4 mr-1" />
        {{ triggerText }}
        <Icon :name="chevronIcon" class="h-4 w-4 ml-1" />
      </slot>
    </UiButton>

    <!-- Dropdown Menu -->
    <Transition
      enter-active-class="transition ease-out duration-200"
      enter-from-class="transform opacity-0 scale-95"
      enter-to-class="transform opacity-100 scale-100"
      leave-active-class="transition ease-in duration-150"
      leave-from-class="transform opacity-100 scale-100"
      leave-to-class="transform opacity-0 scale-95"
      @before-enter="calculatePosition"
    >
      <div
        v-if="isOpen"
        ref="menuRef"
        :class="[
          'absolute z-20 rounded-lg shadow-lg border py-2',
          'bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700',
          widthClass,
          positionClass
        ]"
        :style="menuStyle"
      >
        <slot :close="closeDropdown"></slot>
      </div>
    </Transition>
  </div>
</template>

<script setup lang="ts">
import UiButton from './UiButton.vue';
import { computed, ref, nextTick, onMounted, onUnmounted } from 'vue';

export interface CustomDropdownProps {
  align?: 'left' | 'right' | 'auto';
  width?: '48' | '56' | '64' | '72' | '80' | 'auto';
  triggerClass?: string;
  preventOverflow?: boolean;
  offset?: number;
  triggerText?: string;
  triggerIcon?: string;
  buttonVariant?: 'contained' | 'outline' | 'ghost' | 'flat' | 'gradient';
  buttonSize?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
}

const props = withDefaults(defineProps<CustomDropdownProps>(), {
  align: 'auto',
  width: '64',
  triggerClass: '',
  preventOverflow: true,
  offset: 8,
  triggerText: 'Options',
  buttonVariant: 'outline',
  buttonSize: 'sm'
});

// Dropdown state
const isOpen = ref(false);
const dropdownRef = ref<HTMLElement>();
const menuRef = ref<HTMLElement>();

// Reactive positioning
const menuPosition = ref({
  top: 0,
  left: 0,
  right: 'auto',
  bottom: 'auto',
  transform: ''
});

// Computed classes
const widthClass = computed(() => {
  const widthMap = {
    '48': 'w-48',
    '56': 'w-56',
    '64': 'w-64',
    '72': 'w-72',
    '80': 'w-80',
    'auto': 'w-auto min-w-[12rem]'
  }
  return widthMap[props.width] || 'w-64'
});

const positionClass = computed(() => {
  if (!props.preventOverflow) {
    // Use simple positioning if overflow prevention is disabled
    if (props.align === 'left') return 'left-0';
    if (props.align === 'right') return 'right-0';
    return 'right-0'; // Default to right for better UX
  }

  // Use dynamic positioning when overflow prevention is enabled
  return '';
});

const menuStyle = computed(() => {
  if (!props.preventOverflow) return {};

  return {
    top: `${menuPosition.value.top}px`,
    left: menuPosition.value.left !== 'auto' ? `${menuPosition.value.left}px` : 'auto',
    right: menuPosition.value.right !== 'auto' ? `${menuPosition.value.right}px` : 'auto',
    bottom: menuPosition.value.bottom !== 'auto' ? `${menuPosition.value.bottom}px` : 'auto',
    transform: menuPosition.value.transform
  };
});

const chevronIcon = computed(() => {
  return isOpen.value ? 'material-symbols:expand-less' : 'material-symbols:expand-more';
});

// Methods
const toggleDropdown = () => {
  isOpen.value = !isOpen.value;
};

const closeDropdown = () => {
  isOpen.value = false;
};

// Edge detection and positioning
const calculatePosition = async () => {
  if (!props.preventOverflow || !dropdownRef.value) return;

  await nextTick();

  const trigger = dropdownRef.value.querySelector('button');
  const menu = menuRef.value;

  if (!trigger || !menu) return;

  const triggerRect = trigger.getBoundingClientRect();
  const menuRect = menu.getBoundingClientRect();
  const viewport = {
    width: window.innerWidth,
    height: window.innerHeight
  };

  const spacing = props.offset;

  // Calculate initial position (below trigger, aligned based on prop)
  let top = triggerRect.bottom + spacing;
  let left = triggerRect.left;
  let right = 'auto';
  let bottom = 'auto';
  let transform = '';

  // Adjust horizontal position based on align prop and viewport
  if (props.align === 'right' || (props.align === 'auto' && triggerRect.left > viewport.width / 2)) {
    // Align to right edge of trigger
    left = triggerRect.right - menuRect.width;

    // Ensure it doesn't go off the left edge
    if (left < spacing) {
      left = spacing;
    }
  } else {
    // Align to left edge of trigger (default)
    left = triggerRect.left;

    // Ensure it doesn't go off the right edge
    if (left + menuRect.width > viewport.width - spacing) {
      left = viewport.width - menuRect.width - spacing;
    }
  }

  // Check if menu would go below viewport
  if (top + menuRect.height > viewport.height - spacing) {
    // Position above trigger instead
    top = triggerRect.top - menuRect.height - spacing;

    // If it still doesn't fit above, position at the top of viewport
    if (top < spacing) {
      top = spacing;
    }
  }

  // Ensure menu doesn't go above viewport
  if (top < spacing) {
    top = spacing;
  }

  // Update position
  menuPosition.value = {
    top,
    left,
    right,
    bottom,
    transform
  };
};

// Click outside handler
const handleClickOutside = (event: MouseEvent) => {
  if (dropdownRef.value && !dropdownRef.value.contains(event.target as Node)) {
    closeDropdown();
  }
};

// Lifecycle hooks
onMounted(() => {
  document.addEventListener('click', handleClickOutside);
});

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside);
});
</script>

<style scoped>
/* Additional styles if needed */
</style>
