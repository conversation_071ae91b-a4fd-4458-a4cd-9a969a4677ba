/* Enhanced Animation Utilities for Legal SaaS Frontend */

/* Core Animation Keyframes */
@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fade-out {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fade-in-down {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fade-in-left {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fade-in-right {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fade-in-scale {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slide-in-up {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

@keyframes slide-in-down {
  from {
    transform: translateY(-100%);
  }
  to {
    transform: translateY(0);
  }
}

@keyframes slide-in-left {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes slide-in-right {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes scale-in {
  from {
    opacity: 0;
    transform: scale(0);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes scale-out {
  from {
    opacity: 1;
    transform: scale(1);
  }
  to {
    opacity: 0;
    transform: scale(0);
  }
}

@keyframes bounce-in {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-10px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(10px);
  }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(var(--color-brandPrimary-rgb), 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(var(--color-brandPrimary-rgb), 0.8);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

@keyframes ripple {
  0% {
    transform: scale(0);
    opacity: 0.6;
  }
  100% {
    transform: scale(1);
    opacity: 0;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes rotate-in {
  from {
    opacity: 0;
    transform: rotate(-180deg);
  }
  to {
    opacity: 1;
    transform: rotate(0deg);
  }
}

@keyframes flip-in-x {
  from {
    opacity: 0;
    transform: perspective(400px) rotateX(90deg);
  }
  to {
    opacity: 1;
    transform: perspective(400px) rotateX(0deg);
  }
}

@keyframes flip-in-y {
  from {
    opacity: 0;
    transform: perspective(400px) rotateY(90deg);
  }
  to {
    opacity: 1;
    transform: perspective(400px) rotateY(0deg);
  }
}

/* Stagger Animation Utilities */
@keyframes stagger-fade-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Animation Classes */
.animate-fade-in {
  animation: fade-in 0.3s ease-out;
}

.animate-fade-out {
  animation: fade-out 0.3s ease-out;
}

.animate-fade-in-up {
  animation: fade-in-up 0.5s ease-out;
}

.animate-fade-in-down {
  animation: fade-in-down 0.5s ease-out;
}

.animate-fade-in-left {
  animation: fade-in-left 0.5s ease-out;
}

.animate-fade-in-right {
  animation: fade-in-right 0.5s ease-out;
}

.animate-fade-in-scale {
  animation: fade-in-scale 0.3s ease-out;
}

.animate-slide-in-up {
  animation: slide-in-up 0.4s ease-out;
}

.animate-slide-in-down {
  animation: slide-in-down 0.4s ease-out;
}

.animate-slide-in-left {
  animation: slide-in-left 0.4s ease-out;
}

.animate-slide-in-right {
  animation: slide-in-right 0.4s ease-out;
}

.animate-scale-in {
  animation: scale-in 0.3s ease-out;
}

.animate-scale-out {
  animation: scale-out 0.3s ease-out;
}

.animate-bounce-in {
  animation: bounce-in 0.6s ease-out;
}

.animate-shake {
  animation: shake 0.5s ease-in-out;
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

.animate-shimmer {
  animation: shimmer 1.5s ease-in-out infinite;
}

.animate-ripple {
  animation: ripple 0.6s ease-out;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-rotate-in {
  animation: rotate-in 0.5s ease-out;
}

.animate-flip-in-x {
  animation: flip-in-x 0.6s ease-out;
}

.animate-flip-in-y {
  animation: flip-in-y 0.6s ease-out;
}

/* Stagger Animation Classes */
.animate-stagger-children > * {
  animation: stagger-fade-in 0.5s ease-out;
}

.animate-stagger-children > *:nth-child(1) { animation-delay: 0ms; }
.animate-stagger-children > *:nth-child(2) { animation-delay: 100ms; }
.animate-stagger-children > *:nth-child(3) { animation-delay: 200ms; }
.animate-stagger-children > *:nth-child(4) { animation-delay: 300ms; }
.animate-stagger-children > *:nth-child(5) { animation-delay: 400ms; }
.animate-stagger-children > *:nth-child(6) { animation-delay: 500ms; }
.animate-stagger-children > *:nth-child(7) { animation-delay: 600ms; }
.animate-stagger-children > *:nth-child(8) { animation-delay: 700ms; }
.animate-stagger-children > *:nth-child(9) { animation-delay: 800ms; }
.animate-stagger-children > *:nth-child(10) { animation-delay: 900ms; }

/* Duration Utilities */
.animate-duration-75 { animation-duration: 75ms; }
.animate-duration-100 { animation-duration: 100ms; }
.animate-duration-150 { animation-duration: 150ms; }
.animate-duration-200 { animation-duration: 200ms; }
.animate-duration-300 { animation-duration: 300ms; }
.animate-duration-500 { animation-duration: 500ms; }
.animate-duration-700 { animation-duration: 700ms; }
.animate-duration-1000 { animation-duration: 1000ms; }

/* Delay Utilities */
.animate-delay-75 { animation-delay: 75ms; }
.animate-delay-100 { animation-delay: 100ms; }
.animate-delay-150 { animation-delay: 150ms; }
.animate-delay-200 { animation-delay: 200ms; }
.animate-delay-300 { animation-delay: 300ms; }
.animate-delay-500 { animation-delay: 500ms; }
.animate-delay-700 { animation-delay: 700ms; }
.animate-delay-1000 { animation-delay: 1000ms; }

/* Easing Utilities */
.animate-ease-linear { animation-timing-function: linear; }
.animate-ease-in { animation-timing-function: cubic-bezier(0.4, 0, 1, 1); }
.animate-ease-out { animation-timing-function: cubic-bezier(0, 0, 0.2, 1); }
.animate-ease-in-out { animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1); }

/* Fill Mode Utilities */
.animate-fill-none { animation-fill-mode: none; }
.animate-fill-forwards { animation-fill-mode: forwards; }
.animate-fill-backwards { animation-fill-mode: backwards; }
.animate-fill-both { animation-fill-mode: both; }

/* Iteration Utilities */
.animate-infinite { animation-iteration-count: infinite; }
.animate-once { animation-iteration-count: 1; }
.animate-twice { animation-iteration-count: 2; }

/* Direction Utilities */
.animate-normal { animation-direction: normal; }
.animate-reverse { animation-direction: reverse; }
.animate-alternate { animation-direction: alternate; }
.animate-alternate-reverse { animation-direction: alternate-reverse; }

/* Play State Utilities */
.animate-paused { animation-play-state: paused; }
.animate-running { animation-play-state: running; }

/* Hover Animations */
.hover-animate-scale:hover {
  transform: scale(1.05);
  transition: transform 0.2s ease-out;
}

.hover-animate-scale-sm:hover {
  transform: scale(1.02);
  transition: transform 0.2s ease-out;
}

.hover-animate-scale-lg:hover {
  transform: scale(1.1);
  transition: transform 0.2s ease-out;
}

.hover-animate-lift:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease-out;
}

.hover-animate-glow:hover {
  box-shadow: 0 0 20px rgba(var(--color-brandPrimary-rgb), 0.4);
  transition: box-shadow 0.3s ease-out;
}

/* Focus Animations */
.focus-animate-ring:focus {
  box-shadow: 0 0 0 3px rgba(var(--color-brandPrimary-rgb), 0.3);
  transition: box-shadow 0.2s ease-out;
}

/* Loading Animations */
.animate-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

.animate-dots::after {
  content: '';
  animation: dots 1.5s infinite;
}

@keyframes dots {
  0%, 20% { content: ''; }
  40% { content: '.'; }
  60% { content: '..'; }
  80%, 100% { content: '...'; }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  .animate-stagger-children > * {
    animation-delay: 0ms !important;
  }

  .hover-animate-scale:hover,
  .hover-animate-scale-sm:hover,
  .hover-animate-scale-lg:hover,
  .hover-animate-lift:hover {
    transform: none;
  }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .animate-pulse-glow,
  .hover-animate-glow:hover {
    box-shadow: none;
  }
}

/* Print Styles */
@media print {
  * {
    animation: none !important;
    transition: none !important;
  }
}
