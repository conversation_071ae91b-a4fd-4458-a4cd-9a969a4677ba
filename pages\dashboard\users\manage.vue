<template>
  <div class="space-y-6 py-6">
    <UiCard v-if="currentView == 'table'">
      <UiTable :headers="tableHeaders" :items="userStore.users" :loading="userStore.isLoading" :selectable="true"
        :expandable="true" item-key="id" @sort="handleSort" @selection-change="handleSelectionChange">
        <template #item.name="{ item }">
          <div class="flex items-center gap-3">
            <div class="flex-shrink-0">
              <img v-if="item.avatarUrl" :src="item.avatarUrl" :alt="`${item.fullName} avatar`"
                class="w-10 h-10 rounded-full object-cover" />
              <div v-else class="w-10 h-10 rounded-full bg-brandPrimary/10 flex items-center justify-center">
                <Icon name="material-symbols:person" class="text-brandPrimary" size="calc(var(--spacing) * 5)" />
              </div>
            </div>
            <div>
              <p class="font-medium text-gray-900 dark:text-white">{{ item.fullName }}</p>
              <p class="text-sm text-gray-500 dark:text-gray-400">{{ item.email }}</p>
            </div>
          </div>
        </template>

        <template #item.tenant.name="{ item }">
          <div class="flex items-center gap-3">
            <div class="flex-shrink-0">
              <img v-if="tenantLogo(item.tenant)" :src="tenantLogo(item.tenant)" :alt="`${item.tenant.name} logo`"
                class="w-10 h-10 rounded-md object-cover" />
              <div v-else class="w-10 h-10 rounded-full bg-brandPrimary/10 flex items-center justify-center">
                <Icon name="material-symbols:business" class="h-5 w-5 text-brandPrimary" />
              </div>
            </div>
            <div>
              <p class="font-medium text-gray-900 dark:text-white">
                {{ item.tenant.name }}
              </p>
              <p class="text-sm text-gray-500 dark:text-gray-400">
                {{ item.tenant.slug }}
              </p>
            </div>
          </div>
        </template>
        <template #item.roles="{
          item
        }">
          <div class="flex items-center gap-2">
            <UiBadge v-for="role in item.roles" :key="role" color="gray" variant="outline" size="xs" leading-icon="i-heroicons:tag">
              {{ role }}
            </UiBadge>
          </div>
        </template>

        <template #item.isActive=" { item }">
          <UiBadge variant="outline" :color="item.isActive ? 'success' : 'error'" size="sm" show-status-dot>
           
            {{ item.isActive ? "Active" : "Inactive" }}
          </UiBadge>
        </template>
        <template #item.lastLogin="{ item }">
          <div class="text-sm">
            <p class="text-gray-900 dark:text-white">
              {{ formatDate(item.lastLogin) }}
            </p>
            <p class="text-xs text-gray-500 dark:text-gray-400">
              {{ formatTime(item.lastLogin) }}
            </p>
          </div>
        </template>

        <template #item.createdAt="{ item }">
          <div class="text-sm">
            <p class="text-gray-900 dark:text-white">
              {{ formatDate(item.createdAt) }}
            </p>
            <p class="text-xs text-gray-500 dark:text-gray-400">
              {{ formatTime(item.createdAt) }}
            </p>
          </div>
        </template>

        <!-- Expandable Row Content -->
        <template #expanded-row="{ item }">
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-6 justify-between">
            <!-- User Details -->
            <div class="space-y-3">
              <h4 class="text-sm font-semibold text-gray-900 dark:text-white">
                User Details
              </h4>
              <div class="space-y-2 text-sm justify-between">
                <div class="flex justify-between">
                  <span class="text-gray-500 dark:text-gray-400">ID:</span>
                  <span class="text-gray-900 dark:text-white font-mono">{{
                    item.id
                    }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-500 dark:text-gray-400">Roles:</span>
                  <span class="text-gray-900 dark:text-white">{{ item.roles.join(", ") }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-500 dark:text-gray-400">Last Login:</span>
                  <span class="text-gray-900 dark:text-white">{{
                    formatDate(item.lastLogin) + " " + formatTime(item.lastLogin) 
                    }}</span> 
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-500 dark:text-gray-400">Last Updated:</span>
                  <span class="text-gray-900 dark:text-white">{{
                    formatDate(item.updatedAt) + " " + formatTime(item.updatedAt) 
                    }}</span>
                </div>
              </div>
            </div>

            <!-- Quick Actions -->
            <div class="space-y-3">
              <h4 class="text-sm font-semibold text-gray-900 dark:text-white">
                Quick Actions
              </h4>
              <div class="grid grid-cols-2 gap-2">
                <UiButton @click="handleUserAction({ action: 'view', item })" size="sm" variant="outline"
                  class="w-full">
                  <Icon name="material-symbols:visibility" size="calc(var(--spacing) * 5)" class="mr-1" />
                  View
                </UiButton>
                <UiButton @click="handleUserAction({ action: 'edit', item })" size="sm" variant="outline"
                  class="w-full">
                  <Icon name="material-symbols:edit" size="calc(var(--spacing) * 5)" class="mr-1" />
                  Edit
                </UiButton>
              </div>

              <!-- Danger Zone -->
              <div class="pt-3 border-t border-gray-200 dark:border-gray-600">
                <UiButton @click="
                    handleUserAction({
                      action: item.isActive ? 'deactivate' : 'activate',
                      item,
                    })
                  " size="sm" :variant="item.isActive ? 'outline' : 'primary'" :class="
                    item.isActive
                      ? 'text-red-600 border-red-300 hover:bg-red-50'
                      : 'text-green-600 border-green-300 hover:bg-green-50'
                  " class="w-full">
                  <Icon :name="
                      item.isActive
                        ? 'material-symbols:pause'
                        : 'material-symbols:play-arrow'
                    " size="calc(var(--spacing) * 5)" class="mr-1" />
                  {{ item.isActive ? "Deactivate" : "Activate" }}
                </UiButton>
              </div>
            </div>
          </div>
        </template>
      </UiTable>
    </UiCard>

    <!-- Cards View -->
    <div v-else-if="currentView === 'cards'">
      <UiCardsGrid :items="userStore.users" :loading="userStore.isLoading" :card-config="cardConfig" item-key="id"
        empty-state-title="No users found" empty-state-description="Get started by creating your first user."
        empty-state-icon="material-symbols:person-add" empty-action-label="Create User"
        default-icon="material-symbols:person" @item-action="handleUserAction" @empty-action="handleCreateUser" />
    </div>

    <UiPagination v-if="userStore.usersMeta && userStore.usersMeta.total > 0" :current-page="parseInt(query.page)"
      :total-pages="userStore.usersMeta.totalPages" :total-items="userStore.usersMeta.total"
      :items-per-page="parseInt(query.limit)" @page-change="handlePageChange" />
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from "vue";
import { PlatformRoles } from "../../../app/features/auth/constants/roles";
import type { User } from "../../../app/features/auth/types/models";
import { useEventListeners } from "../../../composables/useEventListeners";
import { useUserStore } from "@/stores/user";

const { $global } = useNuxtApp();
const { on, off } = useEventListeners();

const currentView = ref("table");

definePageMeta({
  layout: "dashboard",
  middleware: ["rbac"],
  roles: [PlatformRoles.SUPER_ADMIN],
  breadcrumbs: [
    { label: "Dashboard", href: "/dashboard" },
    { label: "Users", href: "/dashboard/users" },
    { label: "Manage" },
  ],
  showPageHeader: true,
  title: "Manage Users",
  showPageHeaderTitle: false,
  pageHeaderActions: () => {
    return [
      {
        label: "Invite Users",
        icon: "i-heroicons:user-plus",
        variant: "outline",
        color: "info",
        click: () => navigateTo("/dashboard/users/invite"),
      },
      {
        label: "Refresh",
        icon: "i-mdi:refresh",
        variant: "outline",
        color: "secondary",
        disabled: () => useUserStore().isLoading || useUserStore().isRefreshing,
        loading: () => useUserStore().isLoading || useUserStore().isRefreshing,
        click: () => {
          const { $global } = useNuxtApp();
          if ($global.get("refreshData")) {
            $global.get("refreshData")();
          }
        },
      },
    ].reverse();
  },
  showActionsMenu: false,
  actionsMenu: () => {
    return [
      {
        key: "export",
        label: "Export All Data",
        icon: "material-symbols-light:export-notes-outline-sharp",
      },
      {
        key: "import",
        label: "Bulk Import",
        icon: "material-symbols-light:add-notes-outline-rounded",
      },
      { type: "divider" },
      {
        key: "settings",
        label: "Settings",
        icon: "material-symbols-light:settings-outline",
      },
      {
        key: "support",
        label: "Help & Support",
        icon: "material-symbols-light:help-outline-rounded",
      },
    ];
  },
  showViewToggle: true,

  showFilters: true,
  filterConfigs: [
    {
      key: "isActive",
      options: [
        { label: "All Statuses", value: "" },
        { label: "Active", value: true },
        { label: "Inactive", value: false },
      ],
      placeholder: "Status",
    },
    {
      key: "role",
      options: [
        { label: "All Roles", value: "" },
        { label: "Admin", value: "admin" },
        { label: "User", value: "user" },
      ],
      placeholder: "Role",
    },
    {
      key: "sort",
      type: "select",
      placeholder: "Sort By",
      options: [
        { label: "Default", value: "" },
        { label: "Name A-Z", value: "name:asc" },
        { label: "Name Z-A", value: "name:desc" },
        { label: "Newest First", value: "created:desc" },
        { label: "Oldest First", value: "created:asc" },
      ],
    },
  ],
  advancedFilterConfigs: [
    {
      key: "createdAt",
      type: "dateRange",
      label: "Created Date Range",
    },
  ],
  initialFilters: {},
  showAdvancedFilters: true,
  compactFilters: true,
});

const { formatDate } = useDateFormatters();
const userStore = useUserStore();
const fetchUsers = async (q: any) => {
  try {
    const parsedQuery = JSON.parse(JSON.stringify(q));
    if (parsedQuery.sort.length > 0) {
      parsedQuery.sort = parsedQuery.sort
        .map((s: any) => `${s.key}:${s.direction.toUpperCase()}`)
        .join(",");
    }
    await userStore.fetchAllUsers(parsedQuery);
  } catch (error) {
    console.error("Error fetching users:", error);
  }
};

console.log(userStore);


const { state: query, setQuery, setQueryDebounced, querySync } = useQuery(
  {
    page: 1,
    limit: 10,
    search: "",
    sort: [],
  },
  {
    debounceDelay: 500,
    history: "push",
    onQuerySynced: ({ state }: { state: any }) => {
      fetchUsers(state);
    },
  }
);

const tableHeaders = [
  { key: "name", label: "Name", sortable: true },
  { key: "roles", label: "Role", sortable: true },
  { key: "tenant.name", label: "Tenant", sortable: true },
  { key: "isActive", label: "Status", sortable: true },
  {
    key: "lastLogin",
    label: "Last Login",
    sortable: true,
    format: (value: string) => formatDate(value),
  },
  {
    key: "createdAt",
    label: "Created",
    sortable: true,
    format: (value: string) => formatDate(value),
  },
];

// Cards configuration
const cardConfig = computed(() => ({
  title: (user: User) => user.fullName,
  subtitle: (user: User) => user.email,
  image: (user: User) => user.avatarUrl,
  badges: (user: User) => [
    {
      text: user.role,
      variant: "info",
    },
    {
      text: user.isActive ? "Active" : "Inactive",
      variant: user.isActive ? "success" : "error",
    },
  ],
  metadata: (user: User) => [
    {
      label: "Last Login",
      value: formatDate(user.lastLogin),
      icon: "material-symbols:login",
    },
    {
      label: "Created",
      value: formatDate(user.createdAt),
      icon: "material-symbols:calendar-today",
    },
  ],
  actions: [
    {
      key: "view",
      label: "View",
      icon: "material-symbols:visibility",
      variant: "primary",
    },
    {
      key: "edit",
      label: "Edit",
      icon: "material-symbols:edit",
      variant: "secondary",
    },
    {
      key: "delete",
      label: "Delete",
      icon: "material-symbols:delete",
      variant: "danger",
    },
  ],
}));

const handleSort = (sort: any) => {
  setQuery({ sort });
};

const handleSelectionChange = (selected: any[]) => {
  console.log("Selected items:", selected);
};

const handlePageChange = (page: number) => {
  setQuery({ page });
};

const handleUserAction = (payload: { action: string; item: User }) => {
  console.log("Action:", payload.action, "Item:", payload.item);
  // Add logic for different actions
  switch (payload.action) {
    case "view":
      navigateTo(`/dashboard/users/${payload.item.id}`);
      break;
    case "edit":
      navigateTo(`/dashboard/users/${payload.item.id}/edit`);
      break;
    case "delete":
      handleDeleteUser(payload.item);
      break;
    case "deactivate":
      handleDeactivateUser(payload.item);
      break;
    case "activate":
      handleActivateUser(payload.item);
      break;
    default:
      console.warn("Unknown action:", payload.action);
  }
};

const handleDeleteUser = async (user: User) => {
  try {
    await userStore.deleteUser(user.id);
  } catch (error) {
    console.error("Error deleting user:", error);
  }
};

const handleDeactivateUser = async (user: User) => {
  try {
    await userStore.deactivateUser(user.id);
  } catch (error) {
    console.error("Error deactivating user:", error);
  }
};

const handleActivateUser = async (user: User) => {
  try {
    await userStore.activateUser(user.id);
  } catch (error) {
    console.error("Error activating user:", error);
  }
};

const handleViewChanged = (view: string) => {
  currentView.value = view;
};

const tenantLogo = (tenant) => {
  return tenant?.logoUrl
    ? "/api/storage" + tenant?.logoUrl
    : `https://ui-avatars.com/api/?name=${tenant?.name}&background=1a56db&color=fff`;
};

onMounted(() => {
  on("view-changed", handleViewChanged);
});

onUnmounted(() => {
  off("view-changed", handleViewChanged);
});
</script>
