<!--
  Settings Overview Component
  
  Main settings dashboard with navigation to different setting categories
-->

<template>
  <div class="space-y-6">
    <!-- Header -->
    <div>
      <h1 class="text-2xl font-bold text-gray-900">Settings</h1>
      <p class="mt-1 text-sm text-gray-500">
        Manage your platform settings and preferences
      </p>
    </div>

    <!-- Settings Categories -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <!-- Platform Settings -->
      <NuxtLink
        to="/dashboard/settings/platform"
        class="group relative bg-white p-6 rounded-lg shadow hover:shadow-md transition-shadow duration-200 border border-gray-200 hover:border-indigo-300"
      >
        <div class="flex items-center space-x-4">
          <div class="flex-shrink-0">
            <div class="flex items-center justify-center h-12 w-12 rounded-lg bg-indigo-100 group-hover:bg-indigo-200 transition-colors duration-200">
              <Icon name="heroicons:cog-6-tooth" class="h-6 w-6 text-indigo-600" />
            </div>
          </div>
          <div class="flex-1">
            <h3 class="text-lg font-medium text-gray-900 group-hover:text-indigo-600 transition-colors duration-200">
              Platform Settings
            </h3>
            <p class="text-sm text-gray-500">
              Configure general platform preferences and defaults
            </p>
          </div>
        </div>
        <div class="absolute top-4 right-4">
          <Icon name="heroicons:arrow-right" class="h-5 w-5 text-gray-400 group-hover:text-indigo-600 transition-colors duration-200" />
        </div>
      </NuxtLink>

      <!-- Security Settings -->
      <NuxtLink
        to="/dashboard/settings/security"
        class="group relative bg-white p-6 rounded-lg shadow hover:shadow-md transition-shadow duration-200 border border-gray-200 hover:border-indigo-300"
      >
        <div class="flex items-center space-x-4">
          <div class="flex-shrink-0">
            <div class="flex items-center justify-center h-12 w-12 rounded-lg bg-red-100 group-hover:bg-red-200 transition-colors duration-200">
              <Icon name="heroicons:shield-check" class="h-6 w-6 text-red-600" />
            </div>
          </div>
          <div class="flex-1">
            <h3 class="text-lg font-medium text-gray-900 group-hover:text-indigo-600 transition-colors duration-200">
              Security Settings
            </h3>
            <p class="text-sm text-gray-500">
              Manage security policies and access controls
            </p>
          </div>
        </div>
        <div class="absolute top-4 right-4">
          <Icon name="heroicons:arrow-right" class="h-5 w-5 text-gray-400 group-hover:text-indigo-600 transition-colors duration-200" />
        </div>
      </NuxtLink>

      <!-- User Management -->
      <NuxtLink
        to="/dashboard/users"
        class="group relative bg-white p-6 rounded-lg shadow hover:shadow-md transition-shadow duration-200 border border-gray-200 hover:border-indigo-300"
      >
        <div class="flex items-center space-x-4">
          <div class="flex-shrink-0">
            <div class="flex items-center justify-center h-12 w-12 rounded-lg bg-green-100 group-hover:bg-green-200 transition-colors duration-200">
              <Icon name="heroicons:users" class="h-6 w-6 text-green-600" />
            </div>
          </div>
          <div class="flex-1">
            <h3 class="text-lg font-medium text-gray-900 group-hover:text-indigo-600 transition-colors duration-200">
              User Management
            </h3>
            <p class="text-sm text-gray-500">
              Manage users, roles, and permissions
            </p>
          </div>
        </div>
        <div class="absolute top-4 right-4">
          <Icon name="heroicons:arrow-right" class="h-5 w-5 text-gray-400 group-hover:text-indigo-600 transition-colors duration-200" />
        </div>
      </NuxtLink>

      <!-- Billing & Subscription -->
      <div class="group relative bg-white p-6 rounded-lg shadow border border-gray-200 opacity-75">
        <div class="flex items-center space-x-4">
          <div class="flex-shrink-0">
            <div class="flex items-center justify-center h-12 w-12 rounded-lg bg-yellow-100">
              <Icon name="heroicons:credit-card" class="h-6 w-6 text-yellow-600" />
            </div>
          </div>
          <div class="flex-1">
            <h3 class="text-lg font-medium text-gray-900">
              Billing & Subscription
            </h3>
            <p class="text-sm text-gray-500">
              Manage billing information and subscription plans
            </p>
            <span class="inline-flex px-2 py-1 text-xs font-semibold bg-gray-100 text-gray-800 rounded-full mt-2">
              Coming Soon
            </span>
          </div>
        </div>
      </div>

      <!-- Integrations -->
      <div class="group relative bg-white p-6 rounded-lg shadow border border-gray-200 opacity-75">
        <div class="flex items-center space-x-4">
          <div class="flex-shrink-0">
            <div class="flex items-center justify-center h-12 w-12 rounded-lg bg-purple-100">
              <Icon name="heroicons:puzzle-piece" class="h-6 w-6 text-purple-600" />
            </div>
          </div>
          <div class="flex-1">
            <h3 class="text-lg font-medium text-gray-900">
              Integrations
            </h3>
            <p class="text-sm text-gray-500">
              Connect with third-party services and tools
            </p>
            <span class="inline-flex px-2 py-1 text-xs font-semibold bg-gray-100 text-gray-800 rounded-full mt-2">
              Coming Soon
            </span>
          </div>
        </div>
      </div>

      <!-- API & Webhooks -->
      <div class="group relative bg-white p-6 rounded-lg shadow border border-gray-200 opacity-75">
        <div class="flex items-center space-x-4">
          <div class="flex-shrink-0">
            <div class="flex items-center justify-center h-12 w-12 rounded-lg bg-blue-100">
              <Icon name="heroicons:code-bracket" class="h-6 w-6 text-blue-600" />
            </div>
          </div>
          <div class="flex-1">
            <h3 class="text-lg font-medium text-gray-900">
              API & Webhooks
            </h3>
            <p class="text-sm text-gray-500">
              Manage API keys and webhook configurations
            </p>
            <span class="inline-flex px-2 py-1 text-xs font-semibold bg-gray-100 text-gray-800 rounded-full mt-2">
              Coming Soon
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- Quick Actions -->
    <div class="bg-white shadow rounded-lg p-6">
      <h2 class="text-lg font-medium text-gray-900 mb-6">Quick Actions</h2>
      
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <button
          @click="exportData"
          class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-200"
        >
          <Icon name="heroicons:arrow-down-tray" class="h-5 w-5 text-gray-400 mr-3" />
          <span class="text-sm font-medium text-gray-900">Export Data</span>
        </button>

        <button
          @click="backupData"
          class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-200"
        >
          <Icon name="heroicons:cloud-arrow-up" class="h-5 w-5 text-gray-400 mr-3" />
          <span class="text-sm font-medium text-gray-900">Backup Data</span>
        </button>

        <button
          @click="viewLogs"
          class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-200"
        >
          <Icon name="heroicons:document-text" class="h-5 w-5 text-gray-400 mr-3" />
          <span class="text-sm font-medium text-gray-900">View Logs</span>
        </button>

        <button
          @click="contactSupport"
          class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-200"
        >
          <Icon name="heroicons:question-mark-circle" class="h-5 w-5 text-gray-400 mr-3" />
          <span class="text-sm font-medium text-gray-900">Contact Support</span>
        </button>
      </div>
    </div>

    <!-- System Status -->
    <div class="bg-white shadow rounded-lg p-6">
      <h2 class="text-lg font-medium text-gray-900 mb-6">System Status</h2>
      
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="text-center">
          <div class="flex items-center justify-center h-12 w-12 mx-auto mb-3 rounded-full bg-green-100">
            <Icon name="heroicons:check-circle" class="h-6 w-6 text-green-600" />
          </div>
          <h3 class="text-sm font-medium text-gray-900">System Health</h3>
          <p class="text-sm text-green-600">All systems operational</p>
        </div>

        <div class="text-center">
          <div class="flex items-center justify-center h-12 w-12 mx-auto mb-3 rounded-full bg-blue-100">
            <Icon name="heroicons:server" class="h-6 w-6 text-blue-600" />
          </div>
          <h3 class="text-sm font-medium text-gray-900">Database</h3>
          <p class="text-sm text-blue-600">Connected</p>
        </div>

        <div class="text-center">
          <div class="flex items-center justify-center h-12 w-12 mx-auto mb-3 rounded-full bg-purple-100">
            <Icon name="heroicons:cloud" class="h-6 w-6 text-purple-600" />
          </div>
          <h3 class="text-sm font-medium text-gray-900">Storage</h3>
          <p class="text-sm text-purple-600">85% available</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// Methods
const exportData = () => {
  console.log('Export data')
  alert('Data export functionality would be implemented here')
}

const backupData = () => {
  console.log('Backup data')
  alert('Data backup functionality would be implemented here')
}

const viewLogs = () => {
  console.log('View logs')
  alert('System logs viewer would be implemented here')
}

const contactSupport = () => {
  console.log('Contact support')
  alert('Support contact functionality would be implemented here')
}
</script>
