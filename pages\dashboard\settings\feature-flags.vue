<template>
  <div class="space-y-6">
    <!-- Create New Feature Flag Card -->
    <UiCard title="Create New Feature Flag">
      <form @submit.prevent="handleCreateFeatureFlag" class="space-y-4">
        <div>
          <UiInput
            id="newFlagKey"
            name="newFlagKey"
            label="Flag Key (e.g., newReportingDashboard)"
            v-model="newFlagForm.key"
            required
            placeholder="unique-feature-key"
          />
        </div>
        <div>
          <UiTextarea
            id="newFlagDescription"
            name="newFlagDescription"
            label="Description"
            v-model="newFlagForm.description"
            placeholder="Briefly describe this feature"
            :rows="3"
          />
        </div>
        <div>
          <UiSelect
            id="newFlagScope"
            name="newFlagScope"
            label="Scope"
            v-model="newFlagForm.scope"
            :options="[{label: 'Platform-wide', value: 'PLATFORM'}, {label: 'Tenant-specific', value: 'TENANT'}]"
            required
          />
        </div>
        <div>
          <UiTextarea
            id="newFlagRollout"
            name="newFlagRollout"
            label="Rollout Rules (JSON)"
            v-model="newFlagForm.rollout"
            placeholder='e.g., {"percentage": 10, "tenants": ["id1", "id2"]}'
            :rows="3"
          />
          <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Define rollout criteria like percentage or specific tenant IDs in JSON format.</p>
        </div>
        <div class="grid grid-cols-1 sm:grid-cols-3 gap-4">
          <UiToggle v-model="newFlagForm.isEnabled" label="Enable this flag initially?" />
          <UiToggle v-model="newFlagForm.isExperimental" label="Mark as Experimental?" />
          <UiToggle v-model="newFlagForm.isBeta" label="Mark as Beta?" />
        </div>
        <UiButton type="submit" color="primary" :disabled="creatingFlag">
          {{ creatingFlag ? 'Creating...' : 'Create Flag' }}
        </UiButton>
      </form>
    </UiCard>

    <!-- Platform Features List Card -->
    <UiCard title="Platform Features">
      <div v-if="loading" class="flex justify-center items-center py-8">
        <UiSpinner />
        <p class="ml-2 text-primary-500 dark:text-primary-400">Loading feature flags...</p>
      </div>
      <div v-else-if="error" class="text-red-500 dark:text-red-400 py-8 text-center">
        <p>Error loading feature flags: {{ error.message }}</p>
        <UiButton @click="fetchFeatureFlags" class="mt-4" color="primary">Retry</UiButton>
      </div>
      <div v-else-if="featureFlags.length === 0" class="text-gray-500 dark:text-gray-400 py-8 text-center">
        <p>No feature flags found. You can create one above.</p>
      </div>
      <div v-else class="space-y-2">
        <div v-for="flag in featureFlags" :key="flag.key"
             class="flex flex-col sm:flex-row sm:items-center sm:justify-between py-4 border-b border-gray-200 dark:border-gray-700 last:border-b-0">
          <div class="flex-grow">
            <h3 class="text-lg font-medium text-gray-800 dark:text-gray-100 flex items-center">
              {{ flag.key }}
              <UiBadge :color="flag.scope === 'PLATFORM' ? 'purple' : 'green'" variant="subtle" class="ml-2">{{ flag.scope }}</UiBadge>
              <UiBadge v-if="flag.isExperimental" color="orange" variant="subtle" class="ml-2">Experimental</UiBadge>
              <UiBadge v-if="flag.isBeta" color="blue" variant="subtle" class="ml-2">Beta</UiBadge>
            </h3>
            <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">{{ flag.description || 'No description provided.' }}</p>
          </div>
          <div class="mt-3 sm:mt-0 sm:ml-4 flex items-center space-x-3">
            <UiButton size="sm" variant="outline" @click="openEditModal(flag)" :disabled="updatingFlags[flag.key]">Edit</UiButton>
            <span class="text-sm text-gray-700 dark:text-gray-300">{{ flag.isEnabled ? 'Enabled' : 'Disabled' }}</span>
            <UiToggle
              v-model="flag.isEnabled"
              @update:modelValue="toggleFeatureFlag(flag, $event)"
              :disabled="updatingFlags[flag.key]"
              :aria-label="`Toggle ${flag.key}`"
            />
          </div>
        </div>
      </div>
    </UiCard>

    <!-- Edit Feature Flag Modal -->
    <UiModal :show="showEditModal" @update:show="showEditModal = $event" title="Edit Feature Flag" max-width="lg">
      <form v-if="editingFlag" @submit.prevent="handleUpdateFeatureFlagDetails" class="space-y-4">
        <div>
          <UiInput
            id="editFlagKey"
            name="editFlagKey"
            label="Flag Key"
            :modelValue="editingFlag.key"
            disabled
          />
        </div>
        <div>
          <UiTextarea
            id="editFlagDescription"
            name="editFlagDescription"
            label="Description"
            v-model="editingFlag.description"
            :rows="3"
          />
        </div>
        <div>
          <UiSelect
            id="editFlagScope"
            name="editFlagScope"
            label="Scope"
            v-model="editingFlag.scope"
            :options="[{label: 'Platform-wide', value: 'PLATFORM'}, {label: 'Tenant-specific', value: 'TENANT'}]"
            required
          />
        </div>
        <div>
          <UiTextarea
            id="editFlagRollout"
            name="editFlagRollout"
            label="Rollout Rules (JSON)"
            v-model="editingFlag.rollout"
            placeholder='e.g., {"percentage": 10, "tenants": ["id1", "id2"]}'
            :rows="3"
          />
        </div>
        <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <UiToggle v-model="editingFlag.isExperimental" label="Mark as Experimental?" />
          <UiToggle v-model="editingFlag.isBeta" label="Mark as Beta?" />
        </div>
        <div class="flex justify-end space-x-3 pt-4">
          <UiButton type="button" variant="ghost" @click="closeEditModal">Cancel</UiButton>
          <UiButton type="submit" color="primary" :disabled="savingEditFlag">
            {{ savingEditFlag ? 'Saving...' : 'Save Changes' }}
          </UiButton>
        </div>
      </form>
    </UiModal>
  </div>
</template>

<script setup lang="ts">
import { useToast } from '~/composables/useToast';
import { useApi } from '~/composables/useApi';
import { ref, onMounted, reactive } from '#imports';
import { PlatformRoles, TenantRoles } from'~/app/features/auth/constants/roles';
import UiCard from '~/components/ui/UiCard.vue';
import UiInput from '~/components/ui/UiInput.vue';
import UiTextarea from '~/components/ui/UiTextarea.vue';
import UiToggle from '~/components/ui/UiToggle.vue';
import UiButton from '~/components/ui/UiButton.vue';
import UiBadge from '~/components/ui/UiBadge.vue';
import UiModal from '~/components/ui/UiModal.vue';
import UiSpinner from '~/components/ui/UiSpinner.vue';
import UiSelect from '~/components/ui/UiSelect.vue'; // Added missing import for UiSelect

definePageMeta({
  layout: 'dashboard',
  title: 'Feature Flag Management',
  subtitle: 'Control feature availability across the application.',
  middleware: ['rbac'],
  roles: [PlatformRoles.SUPER_ADMIN],
  breadcrumb: [
    { label: 'Dashboard', href: '/dashboard' },
    { label: 'Settings', href: '/dashboard/settings' },
    { label: 'Feature Flags' },
  ],
});

interface FeatureFlag {
  key: string;
  isEnabled: boolean;
  description?: string;
  isExperimental: boolean;
  isBeta: boolean;
  scope: 'PLATFORM' | 'TENANT';
  rollout?: string;
}

const { get, post, patch } = useApi();
const { showToast } = useToast();

const featureFlags = ref<FeatureFlag[]>([]);
const loading = ref(true);
const error = ref<Error | null>(null);
const updatingFlags = reactive<{ [key: string]: boolean }>({});

const newFlagForm = reactive<FeatureFlag>({
  key: '',
  description: '',
  isEnabled: false,
  isExperimental: false,
  isBeta: false,
  scope: 'PLATFORM',
  rollout: '{}',
});
const creatingFlag = ref(false);

const editingFlag = ref<FeatureFlag | null>(null);
const showEditModal = ref(false);
const savingEditFlag = ref(false);

onMounted(() => {
  fetchFeatureFlags();
});

const fetchFeatureFlags = async () => {
  loading.value = true;
  error.value = null;
  try {
    const response = await get('/feature-flags') as any[];
    featureFlags.value = response.map(apiFlag => ({
      key: String(apiFlag.key),
      isEnabled: typeof apiFlag.isEnabled === 'boolean' ? apiFlag.isEnabled : false,
      description: apiFlag.description ? String(apiFlag.description) : undefined,
      isExperimental: typeof apiFlag.isExperimental === 'boolean' ? apiFlag.isExperimental : false,
      isBeta: typeof apiFlag.isBeta === 'boolean' ? apiFlag.isBeta : false,
      scope: (apiFlag.scope === 'TENANT' || apiFlag.scope === 'PLATFORM') ? apiFlag.scope : 'PLATFORM',
      rollout: typeof apiFlag.rollout === 'string' ? apiFlag.rollout : (apiFlag.rollout ? JSON.stringify(apiFlag.rollout) : '{}'),
    }));
  } catch (err: any) {
    error.value = new Error(err.message || 'Failed to fetch feature flags.');
    console.error('Error fetching feature flags:', err);
    showToast({ title: 'Error', message: error.value.message, type: 'error' });
  } finally {
    loading.value = false;
  }
};

const toggleFeatureFlag = async (flag: FeatureFlag, newIsEnabledState: boolean) => {
  const originalIsEnabled = flag.isEnabled;
  flag.isEnabled = newIsEnabledState; 
  updatingFlags[flag.key] = true;

  try {
    await patch(`/feature-flags/${flag.key}`, { isEnabled: flag.isEnabled });
    showToast({ title: 'Success', message: `Feature '${flag.key}' has been ${flag.isEnabled ? 'enabled' : 'disabled'}.`, type: 'success' });
  } catch (err: any) {
    console.error(`Failed to toggle feature flag '${flag.key}':`, err);
    showToast({ title: 'Error', message: `Failed to update feature '${flag.key}': ${err.message || 'Unknown error'}`, type: 'error' });
    flag.isEnabled = originalIsEnabled; 
  } finally {
    updatingFlags[flag.key] = false;
  }
};

const handleCreateFeatureFlag = async () => {
  if (!newFlagForm.key) {
    showToast({ title: 'Error', message: 'Flag Key is required.', type: 'error' });
    return;
  }
  creatingFlag.value = true;
  try {
    let rolloutPayload: any = {};
    if (newFlagForm.rollout && newFlagForm.rollout.trim() !== '') {
      try {
        rolloutPayload = JSON.parse(newFlagForm.rollout);
      } catch (e) {
        showToast({ title: 'Error', message: 'Rollout rules must be valid JSON.', type: 'error' });
        creatingFlag.value = false;
        return;
      }
    }
    const flagToCreate = { ...newFlagForm, rollout: rolloutPayload };
    await post('/feature-flags', flagToCreate);
    showToast({ title: 'Success', message: `Feature flag '${newFlagForm.key}' created.`, type: 'success' });
    newFlagForm.key = '';
    newFlagForm.description = '';
    newFlagForm.isEnabled = false;
    newFlagForm.isExperimental = false;
    newFlagForm.isBeta = false;
    newFlagForm.scope = 'PLATFORM';
    newFlagForm.rollout = '{}';
    await fetchFeatureFlags(); 
  } catch (err: any) {
    console.error('Error creating feature flag:', err);
    showToast({ title: 'Error', message: `Failed to create flag: ${err.data?.message || err.message || 'Unknown error'}`, type: 'error' });
  } finally {
    creatingFlag.value = false;
  }
};

const openEditModal = (flag: FeatureFlag) => {
  editingFlag.value = {
    ...flag,
    isExperimental: flag.isExperimental ?? false, // Ensure boolean
    isBeta: flag.isBeta ?? false,             // Ensure boolean
    rollout: flag.rollout ?? '{}'
  };
  showEditModal.value = true;
};

const closeEditModal = () => {
  showEditModal.value = false;
  editingFlag.value = null;
};

const handleUpdateFeatureFlagDetails = async () => {
  if (!editingFlag.value) return;
  savingEditFlag.value = true;
  const { key, description, isExperimental, isBeta, scope, rollout } = editingFlag.value;
  try {
    let rolloutData: any = rollout; 
    try {
      if (typeof rollout === 'string' && rollout.trim() !== '') {
        rolloutData = JSON.parse(rollout); 
      } else if (rollout === '' || rollout === undefined || rollout === null) {
        rolloutData = {}; 
      }
    } catch (jsonError) {
      showToast({ title: 'Error', message: 'Rollout rules must be valid JSON or empty.', type: 'error' });
      savingEditFlag.value = false;
      return;
    }
    await patch(`/feature-flags/${key}`, { description, isExperimental, isBeta, scope, rollout: rolloutData });
    showToast({ title: 'Success', message: `Details for '${key}' updated.`, type: 'success' });
    closeEditModal();
    await fetchFeatureFlags(); 
  } catch (err: any) {
    console.error(`Error updating feature flag details for '${key}':`, err);
    showToast({ title: 'Error', message: `Failed to update details: ${err.data?.message || err.message || 'Unknown error'}`, type: 'error' });
  } finally {
    savingEditFlag.value = false;
  }
};

</script>

<style scoped>
/* Page-specific styles can go here */
</style>