<template>
  <div class="sms-channel-editor space-y-6">
    <!-- SMS Content -->
    <div class="space-y-4">
      <div class="flex items-center justify-between">
        <h3 class="text-lg font-medium text-gray-900 dark:text-white">SMS Message</h3>
        <div class="flex items-center space-x-2">
          <UiButton
            @click="showTemplateLibrary = true"
            variant="outline"
            size="sm"
          >
            <Icon name="heroicons:book-open" class="w-4 h-4 mr-2" />
            Templates
          </UiButton>
          <UiButton
            @click="showVariableModal = true"
            variant="outline"
            size="sm"
          >
            <Icon name="heroicons:variable" class="w-4 h-4 mr-2" />
            Variables
          </UiButton>
        </div>
      </div>

      <!-- Message Input -->
      <div class="relative">
        <UiTextarea
          v-model="localContent.message"
          placeholder="Enter your SMS message..."
          :rows="6"
          class="resize-none"
          @input="handleContentChange"
        />
        
        <!-- Character Counter -->
        <div class="absolute bottom-3 right-3 text-xs font-medium" :class="characterCountColor">
          {{ characterCount }}/{{ maxCharacters }}
        </div>
      </div>

      <!-- Message Segments Info -->
      <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
        <div class="flex items-start space-x-3">
          <Icon name="heroicons:information-circle" class="w-5 h-5 text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0" />
          <div>
            <h4 class="text-sm font-medium text-blue-900 dark:text-blue-100">Message Segments</h4>
            <p class="text-sm text-blue-700 dark:text-blue-300 mt-1">
              Your message will be sent as <strong>{{ messageSegments }}</strong> SMS segment{{ messageSegments > 1 ? 's' : '' }}.
              {{ messageSegments > 1 ? 'Long messages may be split across multiple SMS.' : 'This fits in a single SMS.' }}
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- SMS Preview -->
    <UiCard class="p-4">
      <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-4">SMS Preview</h4>
      
      <!-- Mobile Phone Mockup -->
      <div class="max-w-sm mx-auto">
        <div class="bg-gray-900 rounded-3xl p-2">
          <div class="bg-white dark:bg-gray-800 rounded-2xl overflow-hidden">
            <!-- Phone Header -->
            <div class="bg-gray-100 dark:bg-gray-700 px-4 py-2 text-center">
              <div class="text-xs text-gray-600 dark:text-gray-400">SMS Preview</div>
            </div>
            
            <!-- Message Bubble -->
            <div class="p-4 space-y-3">
              <div class="flex justify-end">
                <div class="bg-blue-500 text-white rounded-2xl rounded-br-md px-4 py-2 max-w-xs">
                  <p class="text-sm whitespace-pre-wrap">{{ renderedMessage || 'Your SMS message will appear here...' }}</p>
                  <div class="text-xs opacity-75 mt-1">{{ currentTime }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </UiCard>

    <!-- SMS Settings -->
    <UiCard class="p-4">
      <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-4">SMS Settings</h4>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Sender ID
          </label>
          <UiInput
            v-model="localContent.settings.senderId"
            placeholder="Your Company"
            @input="handleContentChange"
          />
          <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">
            Max 11 characters for alphanumeric sender ID
          </div>
        </div>
        
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Message Type
          </label>
          <select
            v-model="localContent.settings.messageType"
            @change="handleContentChange"
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm"
          >
            <option value="transactional">Transactional</option>
            <option value="promotional">Promotional</option>
            <option value="otp">OTP/Verification</option>
          </select>
        </div>
      </div>
      
      <div class="mt-4 space-y-3">
        <label class="flex items-center">
          <input
            type="checkbox"
            v-model="localContent.settings.enableDeliveryReport"
            @change="handleContentChange"
            class="rounded border-gray-300 text-green-600 focus:ring-green-500"
          />
          <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Enable delivery reports</span>
        </label>
        
        <label class="flex items-center">
          <input
            type="checkbox"
            v-model="localContent.settings.enableUnicodeSupport"
            @change="handleContentChange"
            class="rounded border-gray-300 text-green-600 focus:ring-green-500"
          />
          <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Enable Unicode support</span>
        </label>
      </div>
    </UiCard>

    <!-- SMS Best Practices -->
    <UiCard class="p-4">
      <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-3">SMS Best Practices</h4>
      <div class="space-y-2 text-sm text-gray-600 dark:text-gray-400">
        <div class="flex items-start space-x-2">
          <Icon name="heroicons:check-circle" class="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
          <span>Keep messages under 160 characters for single SMS</span>
        </div>
        <div class="flex items-start space-x-2">
          <Icon name="heroicons:check-circle" class="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
          <span>Include clear call-to-action and contact information</span>
        </div>
        <div class="flex items-start space-x-2">
          <Icon name="heroicons:check-circle" class="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
          <span>Use variables for personalization</span>
        </div>
        <div class="flex items-start space-x-2">
          <Icon name="heroicons:check-circle" class="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
          <span>Test messages before sending to large groups</span>
        </div>
      </div>
    </UiCard>

    <!-- Template Library Modal -->
    <SmsTemplateLibraryModal
      v-if="showTemplateLibrary"
      @close="showTemplateLibrary = false"
      @select="insertTemplate"
    />

    <!-- Variable Insert Modal -->
    <VariableInsertModal
      v-if="showVariableModal"
      @close="showVariableModal = false"
      @insert="insertVariable"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, defineAsyncComponent } from 'vue'

// Lazy load modals
const SmsTemplateLibraryModal = defineAsyncComponent(() => import('../../template-modals/SmsTemplateLibraryModal.vue'))
const VariableInsertModal = defineAsyncComponent(() => import('../../template-modals/VariableInsertModal.vue'))

// Props
interface Props {
  content: any
  variables: string[]
  templateData: any
  category?: any
}

const props = withDefaults(defineProps<Props>(), {
  content: () => ({}),
  variables: () => [],
})

// Emits
const emit = defineEmits<{
  'update:content': [content: any]
  'update:variables': [variables: string[]]
  'content-change': [content: any, variables: string[]]
}>()

// State
const showTemplateLibrary = ref(false)
const showVariableModal = ref(false)

// Local content with defaults
const localContent = ref({
  message: '',
  settings: {
    senderId: '',
    messageType: 'transactional',
    enableDeliveryReport: true,
    enableUnicodeSupport: false,
  },
  ...props.content
})

const localVariables = ref([...props.variables])

// Constants
const maxCharacters = 160
const maxSegmentCharacters = 153 // For multi-part SMS

// Computed
const characterCount = computed(() => localContent.value.message?.length || 0)

const characterCountColor = computed(() => {
  const count = characterCount.value
  if (count > maxCharacters) return 'text-red-600 dark:text-red-400'
  if (count > maxCharacters * 0.8) return 'text-yellow-600 dark:text-yellow-400'
  return 'text-gray-500 dark:text-gray-400'
})

const messageSegments = computed(() => {
  const count = characterCount.value
  if (count === 0) return 1
  if (count <= maxCharacters) return 1
  return Math.ceil(count / maxSegmentCharacters)
})

const renderedMessage = computed(() => {
  let message = localContent.value.message || ''
  
  // Replace variables with sample values
  localVariables.value.forEach(variable => {
    const sampleValue = getSampleValue(variable)
    message = message.replace(new RegExp(`{${variable}}`, 'g'), sampleValue)
  })
  
  return message
})

const currentTime = computed(() => {
  return new Date().toLocaleTimeString('en-US', { 
    hour: 'numeric', 
    minute: '2-digit',
    hour12: true 
  })
})

// Watchers
watch(() => props.content, (newContent) => {
  localContent.value = {
    message: '',
    settings: {
      senderId: '',
      messageType: 'transactional',
      enableDeliveryReport: true,
      enableUnicodeSupport: false,
    },
    ...newContent
  }
}, { deep: true, immediate: true })

watch(() => props.variables, (newVariables) => {
  localVariables.value = [...newVariables]
}, { immediate: true })

// Methods
const handleContentChange = () => {
  emit('update:content', localContent.value)
  emit('content-change', localContent.value, localVariables.value)
}

const insertTemplate = (template: any) => {
  localContent.value.message = template.content || localContent.value.message
  
  // Merge variables
  if (template.variables) {
    const newVariables = [...new Set([...localVariables.value, ...template.variables])]
    localVariables.value = newVariables
    emit('update:variables', newVariables)
  }
  
  showTemplateLibrary.value = false
  handleContentChange()
}

const insertVariable = (variable: string) => {
  const variableText = `{${variable}}`
  localContent.value.message += variableText
  
  // Add variable to list if not already present
  if (!localVariables.value.includes(variable)) {
    localVariables.value.push(variable)
    emit('update:variables', localVariables.value)
  }
  
  showVariableModal.value = false
  handleContentChange()
}

const getSampleValue = (variable: string) => {
  const sampleValues: Record<string, string> = {
    clientName: 'John Doe',
    companyName: 'Legal Services',
    appointmentDate: 'Jan 15',
    appointmentTime: '2:00 PM',
    amount: '$1,250',
    caseNumber: 'CASE-001',
    attorneyName: 'Jane Smith',
    companyPhone: '(*************',
  }
  
  return sampleValues[variable] || `[${variable.toUpperCase()}]`
}
</script>
