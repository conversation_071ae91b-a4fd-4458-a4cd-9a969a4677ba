<template>
  <UiModal @close="$emit('close')" size="xl">
    <div class="p-6">
      <!-- Header -->
      <div class="flex items-center space-x-3 mb-6">
        <div class="w-10 h-10 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center">
          <Icon name="heroicons:currency-dollar" class="w-5 h-5 text-green-600 dark:text-green-400" />
        </div>
        <div>
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Invoice Template Library</h3>
          <p class="text-sm text-gray-600 dark:text-gray-400">Choose from professional invoice templates</p>
        </div>
      </div>

      <!-- Search and Filter -->
      <div class="flex items-center space-x-4 mb-6">
        <div class="flex-1">
          <UiInput
            v-model="searchQuery"
            placeholder="Search invoice templates..."
            class="w-full"
          >
            <template #prefix>
              <Icon name="heroicons:magnifying-glass" class="w-4 h-4 text-gray-400" />
            </template>
          </UiInput>
        </div>
        <div>
          <select
            v-model="selectedCategory"
            class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm"
          >
            <option value="">All Categories</option>
            <option value="legal-services">Legal Services</option>
            <option value="consultation">Consultation</option>
            <option value="retainer">Retainer</option>
            <option value="court-fees">Court Fees</option>
            <option value="expenses">Expenses</option>
          </select>
        </div>
      </div>

      <!-- Template Grid -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6 max-h-96 overflow-y-auto">
        <div
          v-for="template in filteredTemplates"
          :key="template.id"
          class="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:border-green-300 dark:hover:border-green-600 cursor-pointer transition-colors"
          @click="selectTemplate(template)"
        >
          <div class="flex items-start justify-between mb-3">
            <div>
              <h4 class="text-sm font-medium text-gray-900 dark:text-white">{{ template.name }}</h4>
              <p class="text-xs text-gray-500 dark:text-gray-400">{{ template.category }}</p>
            </div>
            <span
              :class="[
                'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium',
                getCategoryColor(template.category)
              ]"
            >
              {{ template.category }}
            </span>
          </div>
          
          <p class="text-sm text-gray-600 dark:text-gray-300 mb-4">{{ template.description }}</p>
          
          <!-- Invoice Preview -->
          <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-3 mb-4 text-xs">
            <div class="text-center mb-3">
              <h5 class="font-bold text-gray-900 dark:text-white">INVOICE</h5>
              <p class="text-gray-600 dark:text-gray-400">{{ template.preview.companyName }}</p>
            </div>
            
            <div class="flex justify-between mb-3 text-xs">
              <div>
                <p><strong>Bill To:</strong></p>
                <p>{{ template.preview.clientName }}</p>
              </div>
              <div class="text-right">
                <p><strong>Invoice #:</strong> {{ template.preview.invoiceNumber }}</p>
                <p><strong>Date:</strong> {{ template.preview.date }}</p>
              </div>
            </div>
            
            <div class="border-t border-gray-200 dark:border-gray-700 pt-2 mb-2">
              <div v-for="item in template.preview.items" :key="item.description" class="flex justify-between text-xs">
                <span>{{ item.description }}</span>
                <span>{{ formatCurrency(item.amount) }}</span>
              </div>
            </div>
            
            <div class="border-t border-gray-200 dark:border-gray-700 pt-2 text-right">
              <p class="font-bold">Total: {{ formatCurrency(template.preview.total) }}</p>
            </div>
          </div>
          
          <!-- Template Features -->
          <div class="space-y-2">
            <div class="text-xs text-gray-500 dark:text-gray-400">Features:</div>
            <div class="flex flex-wrap gap-1">
              <span
                v-for="feature in template.features"
                :key="feature"
                class="inline-flex items-center px-1.5 py-0.5 rounded text-xs bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
              >
                {{ feature }}
              </span>
            </div>
          </div>
          
          <div v-if="template.variables.length > 0" class="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
            <div class="text-xs text-gray-500 dark:text-gray-400 mb-1">Variables:</div>
            <div class="flex flex-wrap gap-1">
              <span
                v-for="variable in template.variables.slice(0, 4)"
                :key="variable"
                class="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-mono bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
              >
                {{ variable }}
              </span>
              <span
                v-if="template.variables.length > 4"
                class="text-xs text-gray-500 dark:text-gray-400"
              >
                +{{ template.variables.length - 4 }} more
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- Actions -->
      <div class="flex items-center justify-end space-x-3 mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
        <UiButton @click="$emit('close')" variant="outline">
          Cancel
        </UiButton>
      </div>
    </div>
  </UiModal>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

// Emits
const emit = defineEmits<{
  close: []
  select: [template: any]
}>()

// State
const searchQuery = ref('')
const selectedCategory = ref('')

// Invoice templates library
const invoiceTemplates = [
  {
    id: 'standard-legal',
    name: 'Standard Legal Services',
    category: 'legal-services',
    description: 'Professional invoice for general legal services with hourly billing',
    content: `
      <div style="max-width: 800px; margin: 0 auto; font-family: Arial, sans-serif;">
        <header style="text-align: center; margin-bottom: 40px; border-bottom: 2px solid #1f2937; padding-bottom: 20px;">
          <h1 style="color: #1f2937; margin-bottom: 10px; font-size: 2em;">INVOICE</h1>
          <div style="color: #6b7280;">
            <p style="font-size: 1.2em; font-weight: bold;">{companyName}</p>
            <p>{companyAddress}</p>
            <p>{companyPhone} | {companyEmail}</p>
          </div>
        </header>
        
        <div style="display: flex; justify-content: space-between; margin-bottom: 30px;">
          <div>
            <h3 style="color: #1f2937;">Bill To:</h3>
            <p><strong>{clientName}</strong></p>
            <p>{clientAddress}</p>
          </div>
          <div style="text-align: right;">
            <p><strong>Invoice #:</strong> {invoiceNumber}</p>
            <p><strong>Date:</strong> {invoiceDate}</p>
            <p><strong>Due Date:</strong> {dueDate}</p>
            <p><strong>Case:</strong> {caseTitle}</p>
          </div>
        </div>
        
        {lineItemsTable}
        
        <footer style="margin-top: 40px; padding-top: 20px; border-top: 1px solid #e5e7eb;">
          <p><strong>Payment Terms:</strong> Net 30 Days</p>
          <p><strong>Remit Payment To:</strong> {companyName}</p>
          <p>Thank you for choosing our legal services.</p>
        </footer>
      </div>
    `,
    features: ['Hourly Billing', 'Tax Calculation', 'Professional Layout'],
    preview: {
      companyName: 'Smith & Associates Law',
      clientName: 'ABC Corporation',
      invoiceNumber: 'INV-2024-001',
      date: '2024-01-15',
      items: [
        { description: 'Legal Consultation', amount: 500 },
        { description: 'Document Review', amount: 750 }
      ],
      total: 1250
    },
    variables: ['companyName', 'companyAddress', 'companyPhone', 'companyEmail', 'clientName', 'clientAddress', 'invoiceNumber', 'invoiceDate', 'dueDate', 'caseTitle'],
    settings: {
      enableTax: true,
      enableDiscount: false,
      currency: 'USD',
      paymentTerms: '30'
    }
  },
  {
    id: 'consultation-invoice',
    name: 'Consultation Invoice',
    category: 'consultation',
    description: 'Simple invoice template for legal consultations',
    content: `
      <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif;">
        <div style="text-align: center; margin-bottom: 30px;">
          <h1 style="color: #059669;">CONSULTATION INVOICE</h1>
          <p style="color: #6b7280;">{companyName}</p>
        </div>
        
        <div style="background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
          <div style="display: flex; justify-content: space-between;">
            <div>
              <p><strong>Client:</strong> {clientName}</p>
              <p><strong>Date:</strong> {consultationDate}</p>
            </div>
            <div style="text-align: right;">
              <p><strong>Invoice #:</strong> {invoiceNumber}</p>
              <p><strong>Duration:</strong> {consultationDuration}</p>
            </div>
          </div>
        </div>
        
        <div style="margin-bottom: 20px;">
          <h3>Consultation Details:</h3>
          <p>{consultationDescription}</p>
        </div>
        
        <div style="text-align: right; font-size: 1.2em;">
          <p><strong>Total Amount: {totalAmount}</strong></p>
        </div>
        
        <div style="margin-top: 30px; text-align: center; color: #6b7280;">
          <p>Payment due upon receipt</p>
          <p>Thank you for your consultation</p>
        </div>
      </div>
    `,
    features: ['Consultation Focus', 'Simple Layout', 'Quick Payment'],
    preview: {
      companyName: 'Legal Advisors LLC',
      clientName: 'John Doe',
      invoiceNumber: 'CONS-001',
      date: '2024-01-15',
      items: [
        { description: 'Initial Consultation (1 hour)', amount: 300 }
      ],
      total: 300
    },
    variables: ['companyName', 'clientName', 'consultationDate', 'invoiceNumber', 'consultationDuration', 'consultationDescription', 'totalAmount'],
    settings: {
      enableTax: false,
      enableDiscount: false,
      currency: 'USD',
      paymentTerms: 'immediate'
    }
  },
  {
    id: 'retainer-invoice',
    name: 'Retainer Agreement Invoice',
    category: 'retainer',
    description: 'Invoice template for retainer fee collection',
    content: `
      <div style="max-width: 700px; margin: 0 auto; font-family: Arial, sans-serif;">
        <header style="border-bottom: 3px solid #dc2626; padding-bottom: 20px; margin-bottom: 30px;">
          <h1 style="color: #dc2626; text-align: center;">RETAINER INVOICE</h1>
          <div style="text-align: center; color: #6b7280;">
            <p><strong>{companyName}</strong></p>
            <p>{companyAddress} | {companyPhone}</p>
          </div>
        </header>
        
        <div style="background-color: #fef2f2; border: 1px solid #fecaca; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
          <h3 style="color: #dc2626; margin-top: 0;">Retainer Agreement</h3>
          <p>This invoice represents the retainer fee for legal services as outlined in our retainer agreement dated {agreementDate}.</p>
        </div>
        
        <div style="display: flex; justify-content: space-between; margin-bottom: 30px;">
          <div>
            <h3>Client Information:</h3>
            <p><strong>{clientName}</strong></p>
            <p>{clientAddress}</p>
            <p>Case: {caseTitle}</p>
          </div>
          <div style="text-align: right;">
            <p><strong>Invoice #:</strong> {invoiceNumber}</p>
            <p><strong>Date:</strong> {invoiceDate}</p>
            <p><strong>Retainer Period:</strong> {retainerPeriod}</p>
          </div>
        </div>
        
        <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
          <tr style="background-color: #f9fafb;">
            <th style="padding: 12px; text-align: left; border: 1px solid #d1d5db;">Description</th>
            <th style="padding: 12px; text-align: right; border: 1px solid #d1d5db;">Amount</th>
          </tr>
          <tr>
            <td style="padding: 12px; border: 1px solid #d1d5db;">Retainer Fee - {retainerDescription}</td>
            <td style="padding: 12px; text-align: right; border: 1px solid #d1d5db;">{retainerAmount}</td>
          </tr>
        </table>
        
        <div style="text-align: right; font-size: 1.3em; margin-bottom: 30px;">
          <p><strong>Total Retainer: {retainerAmount}</strong></p>
        </div>
        
        <div style="background-color: #f0f9ff; border: 1px solid #bae6fd; padding: 15px; border-radius: 8px;">
          <p><strong>Payment Terms:</strong> Retainer fee is due immediately upon receipt of this invoice.</p>
          <p><strong>Note:</strong> This retainer will be held in our client trust account and applied against future legal services.</p>
        </div>
      </div>
    `,
    features: ['Retainer Focus', 'Trust Account Notice', 'Agreement Reference'],
    preview: {
      companyName: 'Corporate Law Partners',
      clientName: 'Tech Startup Inc.',
      invoiceNumber: 'RET-2024-001',
      date: '2024-01-15',
      items: [
        { description: 'Retainer Fee', amount: 5000 }
      ],
      total: 5000
    },
    variables: ['companyName', 'companyAddress', 'companyPhone', 'clientName', 'clientAddress', 'caseTitle', 'invoiceNumber', 'invoiceDate', 'agreementDate', 'retainerPeriod', 'retainerDescription', 'retainerAmount'],
    settings: {
      enableTax: false,
      enableDiscount: false,
      currency: 'USD',
      paymentTerms: 'immediate'
    }
  },
  {
    id: 'court-fees-invoice',
    name: 'Court Fees & Expenses',
    category: 'court-fees',
    description: 'Invoice for court filing fees and legal expenses',
    content: `
      <div style="max-width: 750px; margin: 0 auto; font-family: Arial, sans-serif;">
        <div style="text-align: center; margin-bottom: 30px;">
          <h1 style="color: #7c3aed;">COURT FEES & EXPENSES</h1>
          <p style="color: #6b7280; font-size: 1.1em;">{companyName}</p>
        </div>
        
        <div style="display: flex; justify-content: space-between; margin-bottom: 30px;">
          <div>
            <h3>Client:</h3>
            <p><strong>{clientName}</strong></p>
            <p>Case: {caseTitle}</p>
            <p>Court: {courtName}</p>
          </div>
          <div style="text-align: right;">
            <p><strong>Invoice #:</strong> {invoiceNumber}</p>
            <p><strong>Date:</strong> {invoiceDate}</p>
            <p><strong>Case #:</strong> {caseNumber}</p>
          </div>
        </div>
        
        <h3 style="color: #7c3aed; border-bottom: 2px solid #7c3aed; padding-bottom: 5px;">Itemized Expenses</h3>
        
        {lineItemsTable}
        
        <div style="background-color: #faf5ff; border: 1px solid #d8b4fe; padding: 15px; border-radius: 8px; margin-top: 20px;">
          <p><strong>Note:</strong> All court fees and expenses are billed at cost with no markup.</p>
          <p><strong>Payment Terms:</strong> Due within 15 days of receipt.</p>
        </div>
      </div>
    `,
    features: ['Expense Tracking', 'Court Fee Focus', 'No Markup Policy'],
    preview: {
      companyName: 'Litigation Specialists',
      clientName: 'Business Corp',
      invoiceNumber: 'EXP-001',
      date: '2024-01-15',
      items: [
        { description: 'Court Filing Fee', amount: 350 },
        { description: 'Service of Process', amount: 125 },
        { description: 'Document Copies', amount: 45 }
      ],
      total: 520
    },
    variables: ['companyName', 'clientName', 'caseTitle', 'courtName', 'invoiceNumber', 'invoiceDate', 'caseNumber'],
    settings: {
      enableTax: false,
      enableDiscount: false,
      currency: 'USD',
      paymentTerms: '15'
    }
  },
  {
    id: 'monthly-statement',
    name: 'Monthly Statement',
    category: 'legal-services',
    description: 'Comprehensive monthly billing statement',
    content: `
      <div style="max-width: 850px; margin: 0 auto; font-family: Arial, sans-serif;">
        <header style="background-color: #1f2937; color: white; padding: 20px; text-align: center; margin-bottom: 30px;">
          <h1 style="margin: 0; font-size: 2em;">MONTHLY STATEMENT</h1>
          <p style="margin: 10px 0 0 0;">{companyName}</p>
        </header>
        
        <div style="display: flex; justify-content: space-between; margin-bottom: 30px;">
          <div>
            <h3>Statement For:</h3>
            <p><strong>{clientName}</strong></p>
            <p>{clientAddress}</p>
          </div>
          <div style="text-align: right;">
            <p><strong>Statement #:</strong> {statementNumber}</p>
            <p><strong>Period:</strong> {billingPeriod}</p>
            <p><strong>Date:</strong> {statementDate}</p>
          </div>
        </div>
        
        <div style="background-color: #f9fafb; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
          <h3 style="margin-top: 0;">Account Summary</h3>
          <div style="display: flex; justify-content: space-between;">
            <span>Previous Balance:</span>
            <span>{previousBalance}</span>
          </div>
          <div style="display: flex; justify-content: space-between;">
            <span>Payments Received:</span>
            <span>({paymentsReceived})</span>
          </div>
          <div style="display: flex; justify-content: space-between; font-weight: bold; border-top: 1px solid #d1d5db; padding-top: 10px; margin-top: 10px;">
            <span>Current Charges:</span>
            <span>{currentCharges}</span>
          </div>
        </div>
        
        <h3>Current Period Activity</h3>
        {lineItemsTable}
        
        <div style="text-align: right; font-size: 1.3em; margin-top: 20px; padding: 15px; background-color: #fee2e2; border-radius: 8px;">
          <p><strong>Total Amount Due: {totalDue}</strong></p>
          <p style="font-size: 0.8em; color: #dc2626;">Due Date: {dueDate}</p>
        </div>
      </div>
    `,
    features: ['Monthly Summary', 'Account History', 'Payment Tracking'],
    preview: {
      companyName: 'Premier Legal Services',
      clientName: 'Enterprise Client',
      invoiceNumber: 'STMT-001',
      date: '2024-01-31',
      items: [
        { description: 'Legal Services - January', amount: 2500 },
        { description: 'Court Appearances', amount: 800 },
        { description: 'Document Preparation', amount: 450 }
      ],
      total: 3750
    },
    variables: ['companyName', 'clientName', 'clientAddress', 'statementNumber', 'billingPeriod', 'statementDate', 'previousBalance', 'paymentsReceived', 'currentCharges', 'totalDue', 'dueDate'],
    settings: {
      enableTax: true,
      enableDiscount: true,
      currency: 'USD',
      paymentTerms: '30'
    }
  }
]

// Computed
const filteredTemplates = computed(() => {
  let templates = invoiceTemplates
  
  // Filter by category
  if (selectedCategory.value) {
    templates = templates.filter(template => template.category === selectedCategory.value)
  }
  
  // Filter by search query
  if (searchQuery.value.trim()) {
    const query = searchQuery.value.toLowerCase()
    templates = templates.filter(template =>
      template.name.toLowerCase().includes(query) ||
      template.description.toLowerCase().includes(query)
    )
  }
  
  return templates
})

// Methods
const selectTemplate = (template: any) => {
  emit('select', template)
}

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(amount)
}

const getCategoryColor = (category: string) => {
  const colors = {
    'legal-services': 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
    'consultation': 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
    'retainer': 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
    'court-fees': 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200',
    'expenses': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
  }
  return colors[category as keyof typeof colors] || 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
}
</script>
