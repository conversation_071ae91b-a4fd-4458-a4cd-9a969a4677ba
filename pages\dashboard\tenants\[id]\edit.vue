<template>
  <div class="space-y-6">
    <!-- <PERSON> Header -->
    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
      <div class="flex items-center gap-4">
        <!-- Tenant Logo -->
        <div class="flex-shrink-0">
          <div v-if="selectedTenant?.logoUrl" class="w-12 h-12 rounded-lg overflow-hidden">
            <img
              :src="selectedTenant.logoUrl"
              :alt="`${selectedTenant.name} logo`"
              class="w-full h-full object-cover"
            />
          </div>
          <div v-else class="w-12 h-12 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
            <Icon name="material-symbols:edit" class="h-6 w-6 text-blue-600 dark:text-blue-400" />
          </div>
        </div>
        <div>
          <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
            Edit {{ selectedTenant?.name || 'Tenant' }}
          </h1>
          <p class="text-gray-600 dark:text-gray-400 mt-1">
            Update tenant details, settings, and configuration
          </p>
        </div>
      </div>
      <div class="flex items-center gap-3">
        <UiButton
          variant="ghost"
          size="sm"
          leading-icon="material-symbols:arrow-back"
          @click="goBackToTenant"
        >
          Back to Tenant
        </UiButton>
        <UiButton
          variant="secondary"
          size="sm"
          leading-icon="material-symbols:preview"
          @click="previewChanges"
          :disabled="!hasChanges"
        >
          Preview
        </UiButton>
      </div>
    </div>

    <!-- Form Container -->
    <form @submit.prevent="handleSubmit" class="space-y-6">
      <!-- Main Content Grid -->
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Form -->
        <div class="lg:col-span-2 space-y-6">
          <!-- Basic Information -->
          <UiCard>
            <template #header>
              <div class="flex items-center gap-3">
                <div class="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
                  <Icon name="material-symbols:info" class="h-5 w-5 text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                  <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Basic Information</h3>
                  <p class="text-sm text-gray-500 dark:text-gray-400">Update tenant name, slug, and basic details</p>
                </div>
              </div>
            </template>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <UiInput
                  id="tenantName"
                  name="tenantName"
                  v-model="formData.name"
                  label="Tenant Name"
                  placeholder="Enter tenant name"
                  required
                  :error="errors.name"
                  leading-icon="material-symbols:business"
                />
              </div>
              <div>
                <UiInput
                  id="tenantSlug"
                  name="tenantSlug"
                  v-model="formData.slug"
                  label="Tenant Slug"
                  placeholder="Enter tenant slug"
                  required
                  :error="errors.slug"
                  leading-icon="material-symbols:link"
                  help-text="Used in URLs and API endpoints"
                />
              </div>
            </div>
          </UiCard>

          <!-- Logo Upload -->
          <UiCard>
            <template #header>
              <div class="flex items-center gap-3">
                <div class="p-2 bg-purple-100 dark:bg-purple-900/20 rounded-lg">
                  <Icon name="material-symbols:image" class="h-5 w-5 text-purple-600 dark:text-purple-400" />
                </div>
                <div>
                  <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Tenant Logo</h3>
                  <p class="text-sm text-gray-500 dark:text-gray-400">Upload or update the tenant logo</p>
                </div>
              </div>
            </template>
            <div class="space-y-4">
              <!-- Current Logo Preview -->
              <div v-if="selectedTenant?.logoUrl || formData.logoPreview" class="flex items-center gap-4">
                <div class="w-16 h-16 rounded-lg overflow-hidden bg-gray-100 dark:bg-gray-700">
                  <img
                    :src="formData.logoPreview || selectedTenant?.logoUrl"
                    :alt="formData.name || 'Tenant logo'"
                    class="w-full h-full object-cover"
                  />
                </div>
                <div>
                  <p class="text-sm font-medium text-gray-900 dark:text-white">Current Logo</p>
                  <p class="text-xs text-gray-500 dark:text-gray-400">
                    {{ formData.logoFile?.name || 'Uploaded logo' }}
                  </p>
                </div>
                <UiButton
                  type="button"
                  variant="ghost"
                  size="sm"
                  @click="removeLogo"
                >
                  Remove
                </UiButton>
              </div>

              <!-- Logo Upload -->
              <UiLogoUpload
                v-model="formData.logoFile"
                label="Upload New Logo"
                :max-size="2 * 1024 * 1024"
                help-text="Upload a logo for the tenant. Recommended size: 200x200px, max 2MB"
                @change="handleLogoChange"
                @error="handleLogoError"
              />
            </div>
          </UiCard>

          <!-- Localization Settings -->
          <UiCard>
            <template #header>
              <div class="flex items-center gap-3">
                <div class="p-2 bg-green-100 dark:bg-green-900/20 rounded-lg">
                  <Icon name="material-symbols:language" class="h-5 w-5 text-green-600 dark:text-green-400" />
                </div>
                <div>
                  <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Localization</h3>
                  <p class="text-sm text-gray-500 dark:text-gray-400">Configure language and timezone settings</p>
                </div>
              </div>
            </template>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <UiSelect
                  id="locale"
                  v-model="formData.locale"
                  label="Language"
                  :options="localeOptions"
                  required
                  :error="errors.locale"
                  leading-icon="material-symbols:translate"
                />
              </div>
              <div>
                <UiSelect
                  id="timezone"
                  v-model="formData.timezone"
                  label="Timezone"
                  :options="timezoneOptions"
                  required
                  :error="errors.timezone"
                  leading-icon="material-symbols:schedule"
                />
              </div>
            </div>
          </UiCard>

          <!-- Plan & Subscription -->
          <UiCard>
            <template #header>
              <div class="flex items-center gap-3">
                <div class="p-2 bg-orange-100 dark:bg-orange-900/20 rounded-lg">
                  <Icon name="material-symbols:workspace-premium" class="h-5 w-5 text-orange-600 dark:text-orange-400" />
                </div>
                <div>
                  <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Plan & Subscription</h3>
                  <p class="text-sm text-gray-500 dark:text-gray-400">Manage tenant plan and subscription settings</p>
                </div>
              </div>
            </template>
            <div class="space-y-6">
              <div>
                <UiSelect
                  id="plan"
                  v-model="formData.plan"
                  label="Subscription Plan"
                  :options="planOptions"
                  required
                  :error="errors.plan"
                  leading-icon="material-symbols:star"
                  help-text="Select the appropriate plan for this tenant"
                />
              </div>

              <!-- Plan Features Preview -->
              <div v-if="selectedPlanFeatures" class="p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-3">Plan Features</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                  <div
                    v-for="feature in selectedPlanFeatures"
                    :key="feature.name"
                    class="flex items-center gap-2 text-sm"
                  >
                    <Icon
                      :name="feature.included ? 'material-symbols:check-circle' : 'material-symbols:cancel'"
                      :class="feature.included ? 'text-green-500' : 'text-gray-400'"
                      class="h-4 w-4"
                    />
                    <span :class="feature.included ? 'text-gray-900 dark:text-white' : 'text-gray-500 dark:text-gray-400'">
                      {{ feature.name }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </UiCard>

          <!-- Status & Permissions -->
          <UiCard>
            <template #header>
              <div class="flex items-center gap-3">
                <div class="p-2 bg-red-100 dark:bg-red-900/20 rounded-lg">
                  <Icon name="material-symbols:security" class="h-5 w-5 text-red-600 dark:text-red-400" />
                </div>
                <div>
                  <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Status & Permissions</h3>
                  <p class="text-sm text-gray-500 dark:text-gray-400">Configure tenant status and access permissions</p>
                </div>
              </div>
            </template>
            <div class="space-y-6">
              <!-- Tenant Status -->
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                  Tenant Status
                </label>
                <div class="space-y-3">
                  <div class="flex items-center gap-3">
                    <UiCheckbox
                      id="isActive"
                      v-model="formData.isActive"
                      label="Active"
                    />
                    <span class="text-sm text-gray-500 dark:text-gray-400">
                      Allow tenant users to access the platform
                    </span>
                  </div>
                  <div v-if="!formData.isActive" class="p-3 bg-yellow-50 dark:bg-yellow-900/10 rounded-lg">
                    <div class="flex items-center gap-2">
                      <Icon name="material-symbols:warning" class="h-4 w-4 text-yellow-600 dark:text-yellow-400" />
                      <p class="text-sm text-yellow-800 dark:text-yellow-200">
                        Deactivating this tenant will prevent all users from accessing their data.
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Additional Settings -->
              <div class="space-y-4">
                <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300">Additional Settings</h4>
                <div class="space-y-3">
                  <UiCheckbox
                    id="allowApiAccess"
                    v-model="formData.allowApiAccess"
                    label="Allow API Access"
                  />
                  <UiCheckbox
                    id="enableAuditLog"
                    v-model="formData.enableAuditLog"
                    label="Enable Audit Logging"
                  />
                  <UiCheckbox
                    id="enableNotifications"
                    v-model="formData.enableNotifications"
                    label="Enable Email Notifications"
                  />
                </div>
              </div>
            </div>
          </UiCard>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
          <!-- Save Actions -->
          <UiCard>
            <template #header>
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Actions</h3>
            </template>
            <div class="space-y-3">
              <UiButton
                type="submit"
                :loading="isSubmitting"
                :disabled="!hasChanges"
                class="w-full"
              >
                Save Changes
              </UiButton>
              <UiButton
                type="button"
                variant="secondary"
                @click="resetForm"
                :disabled="isSubmitting"
                class="w-full"
              >
                Reset Form
              </UiButton>
              <UiButton
                type="button"
                variant="ghost"
                @click="goBackToTenant"
                :disabled="isSubmitting"
                class="w-full"
              >
                Cancel
              </UiButton>
            </div>
          </UiCard>

          <!-- Change Summary -->
          <UiCard v-if="hasChanges">
            <template #header>
              <div class="flex items-center gap-2">
                <Icon name="material-symbols:edit-note" class="h-5 w-5 text-blue-600 dark:text-blue-400" />
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Pending Changes</h3>
              </div>
            </template>
            <div class="space-y-3">
              <div
                v-for="change in pendingChanges"
                :key="change.field"
                class="flex items-center justify-between text-sm"
              >
                <span class="text-gray-600 dark:text-gray-400">{{ change.label }}</span>
                <div class="text-right">
                  <p class="text-gray-900 dark:text-white font-medium">{{ change.newValue }}</p>
                  <p class="text-gray-500 dark:text-gray-400 text-xs line-through">{{ change.oldValue }}</p>
                </div>
              </div>
            </div>
          </UiCard>

          <!-- Current Information -->
          <UiCard>
            <template #header>
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Current Information</h3>
            </template>
            <div class="space-y-4">
              <div>
                <label class="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">Tenant ID</label>
                <p class="text-sm text-gray-900 dark:text-white mt-1 font-mono">{{ selectedTenant?.id }}</p>
              </div>
              <div>
                <label class="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">Created</label>
                <p class="text-sm text-gray-900 dark:text-white mt-1">{{ formatDate(selectedTenant?.createdAt) }}</p>
              </div>
              <div>
                <label class="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">Last Updated</label>
                <p class="text-sm text-gray-900 dark:text-white mt-1">{{ formatDate(selectedTenant?.updatedAt) }}</p>
              </div>
              <div>
                <label class="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">Current Status</label>
                <UiBadge :variant="selectedTenant?.isActive ? 'success' : 'warning'" class="mt-1">
                  {{ selectedTenant?.isActive ? 'Active' : 'Inactive' }}
                </UiBadge>
              </div>
            </div>
          </UiCard>

          <!-- Help & Documentation -->
          <UiCard>
            <template #header>
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Need Help?</h3>
            </template>
            <div class="space-y-3">
              <UiButton
                variant="outline"
                size="sm"
                leading-icon="material-symbols:help"
                @click="openDocumentation"
                class="w-full"
              >
                View Documentation
              </UiButton>
              <UiButton
                variant="outline"
                size="sm"
                leading-icon="material-symbols:support"
                @click="contactSupport"
                class="w-full"
              >
                Contact Support
              </UiButton>
            </div>
          </UiCard>
        </div>
      </div>

      <!-- Error Display -->
      <div v-if="submitError" class="mt-6">
        <UiAlert type="error" :title="submitError">
          Please review the form and try again.
        </UiAlert>
      </div>

      <!-- Success Display -->
      <div v-if="successMessage" class="mt-6">
        <UiAlert type="success" :title="successMessage">
          Tenant has been updated successfully.
        </UiAlert>
      </div>
    </form>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useTenantStore, type UpdateTenantPayload, type Tenant } from '~/stores/tenant'

// Define roles locally
const PlatformRoles = {
  SUPER_ADMIN: 'super_admin',
}

// Page meta
definePageMeta({
  layout: 'dashboard',
  title: 'Edit Tenant',
  description: 'Edit tenant details and settings',
  pageHeaderIcon: 'material-symbols:edit',
  showRealTimeStatus: false,
  autoRefreshEnabled: false,
  refreshInterval: 0,
  isLoading: false,
  showActionsMenu: true,
  // showKeyboardShortcuts: true,
  middleware: ['rbac'],
  roles: [PlatformRoles.SUPER_ADMIN],
  breadcrumb: [
    { label: 'Dashboard', href: '/dashboard' },
    { label: 'Tenants', href: '/dashboard/tenants' },
    { label: 'Edit' },
  ],
})

// Composables
const route = useRoute()
const router = useRouter()
const tenantStore = useTenantStore()

// Get tenant ID from route
const tenantId = computed(() => route.params.id as string)

// Form data interface
interface TenantFormData {
  name: string
  slug: string
  logoFile: File | null
  logoPreview: string | null
  locale: string
  timezone: string
  plan: string
  isActive: boolean
  allowApiAccess: boolean
  enableAuditLog: boolean
  enableNotifications: boolean
}

// Reactive state
const formData = ref<TenantFormData>({
  name: '',
  slug: '',
  logoFile: null,
  logoPreview: null,
  locale: 'en',
  timezone: 'UTC',
  plan: 'basic',
  isActive: true,
  allowApiAccess: true,
  enableAuditLog: true,
  enableNotifications: true,
})

const originalData = ref<TenantFormData | null>(null)
const isSubmitting = ref(false)
const submitError = ref('')
const successMessage = ref('')
const errors = ref<Record<string, string>>({})

// Options
const localeOptions = [
  { value: 'en', label: 'English' },
  { value: 'es', label: 'Spanish' },
  { value: 'fr', label: 'French' },
  { value: 'de', label: 'German' },
  { value: 'it', label: 'Italian' },
  { value: 'pt', label: 'Portuguese' },
]

const timezoneOptions = [
  { value: 'UTC', label: 'Coordinated Universal Time (UTC)' },
  { value: 'America/New_York', label: 'Eastern Time (ET)' },
  { value: 'America/Chicago', label: 'Central Time (CT)' },
  { value: 'America/Denver', label: 'Mountain Time (MT)' },
  { value: 'America/Los_Angeles', label: 'Pacific Time (PT)' },
  { value: 'Europe/London', label: 'Greenwich Mean Time (GMT)' },
  { value: 'Europe/Paris', label: 'Central European Time (CET)' },
  { value: 'Asia/Tokyo', label: 'Japan Standard Time (JST)' },
  { value: 'Asia/Shanghai', label: 'China Standard Time (CST)' },
  { value: 'Australia/Sydney', label: 'Australian Eastern Time (AET)' },
]

const planOptions = [
  { value: 'basic', label: 'Basic Plan' },
  { value: 'professional', label: 'Professional Plan' },
  { value: 'enterprise', label: 'Enterprise Plan' },
  { value: 'custom', label: 'Custom Plan' },
]

// Mock plan features (replace with real data)
const planFeatures = {
  basic: [
    { name: 'Up to 5 users', included: true },
    { name: 'Basic support', included: true },
    { name: 'API access', included: false },
    { name: 'Advanced analytics', included: false },
  ],
  professional: [
    { name: 'Up to 25 users', included: true },
    { name: 'Priority support', included: true },
    { name: 'API access', included: true },
    { name: 'Advanced analytics', included: false },
  ],
  enterprise: [
    { name: 'Unlimited users', included: true },
    { name: 'Dedicated support', included: true },
    { name: 'API access', included: true },
    { name: 'Advanced analytics', included: true },
  ],
  custom: [
    { name: 'Custom user limits', included: true },
    { name: 'Custom support', included: true },
    { name: 'Custom features', included: true },
    { name: 'Custom analytics', included: true },
  ],
}

// Computed properties
const selectedTenant = computed(() => tenantStore.selectedTenant)

const selectedPlanFeatures = computed(() => {
  return planFeatures[formData.value.plan as keyof typeof planFeatures] || []
})

const hasChanges = computed(() => {
  if (!originalData.value) return false

  return Object.keys(formData.value).some(key => {
    const formKey = key as keyof TenantFormData
    if (formKey === 'logoFile' || formKey === 'logoPreview') return false
    return formData.value[formKey] !== originalData.value![formKey]
  }) || formData.value.logoFile !== null
})

const pendingChanges = computed(() => {
  if (!originalData.value || !hasChanges.value) return []

  const changes: Array<{ field: string; label: string; oldValue: string; newValue: string }> = []

  const fieldLabels: Record<string, string> = {
    name: 'Tenant Name',
    slug: 'Slug',
    locale: 'Language',
    timezone: 'Timezone',
    plan: 'Plan',
    isActive: 'Status',
    allowApiAccess: 'API Access',
    enableAuditLog: 'Audit Log',
    enableNotifications: 'Notifications',
  }

  Object.keys(fieldLabels).forEach(field => {
    const formKey = field as keyof TenantFormData
    if (formData.value[formKey] !== originalData.value![formKey]) {
      changes.push({
        field,
        label: fieldLabels[field],
        oldValue: String(originalData.value![formKey]),
        newValue: String(formData.value[formKey]),
      })
    }
  })

  if (formData.value.logoFile) {
    changes.push({
      field: 'logo',
      label: 'Logo',
      oldValue: 'Current logo',
      newValue: 'New logo',
    })
  }

  return changes
})

// Methods
const loadTenantData = () => {
  if (!selectedTenant.value) return

  const tenant = selectedTenant.value
  const data: TenantFormData = {
    name: tenant.name,
    slug: tenant.slug,
    logoFile: null,
    logoPreview: null,
    locale: tenant.locale,
    timezone: tenant.timezone,
    plan: tenant.plan,
    isActive: tenant.isActive,
    allowApiAccess: true, // These would come from tenant settings
    enableAuditLog: true,
    enableNotifications: true,
  }

  formData.value = { ...data }
  originalData.value = { ...data }
}

const validateForm = () => {
  errors.value = {}

  if (!formData.value.name.trim()) {
    errors.value.name = 'Tenant name is required'
  }

  if (!formData.value.slug.trim()) {
    errors.value.slug = 'Tenant slug is required'
  } else if (!/^[a-z0-9-]+$/.test(formData.value.slug)) {
    errors.value.slug = 'Slug can only contain lowercase letters, numbers, and hyphens'
  }

  if (!formData.value.locale) {
    errors.value.locale = 'Language is required'
  }

  if (!formData.value.timezone) {
    errors.value.timezone = 'Timezone is required'
  }

  if (!formData.value.plan) {
    errors.value.plan = 'Plan is required'
  }

  return Object.keys(errors.value).length === 0
}

const handleSubmit = async () => {
  submitError.value = ''
  successMessage.value = ''

  if (!validateForm()) {
    submitError.value = 'Please fix the validation errors'
    return
  }

  isSubmitting.value = true

  try {
    const payload: UpdateTenantPayload = {
      name: formData.value.name,
      slug: formData.value.slug,
      locale: formData.value.locale,
      timezone: formData.value.timezone,
      plan: formData.value.plan,
      isActive: formData.value.isActive,
    }

    // Handle logo upload if there's a new file
    if (formData.value.logoFile) {
      // This would typically use a separate logo upload method
      console.log('Uploading new logo:', formData.value.logoFile.name)
    }

    await tenantStore.updateTenant(tenantId.value, payload)

    successMessage.value = 'Tenant updated successfully'

    // Update original data to reflect saved state
    originalData.value = { ...formData.value, logoFile: null }

    // Redirect after a delay
    setTimeout(() => {
      router.push(`/dashboard/tenants/${tenantId.value}`)
    }, 2000)

  } catch (error: any) {
    submitError.value = error.message || 'Failed to update tenant'
  } finally {
    isSubmitting.value = false
  }
}

const resetForm = () => {
  if (originalData.value) {
    formData.value = { ...originalData.value }
  }
  errors.value = {}
  submitError.value = ''
  successMessage.value = ''
}

const handleLogoChange = (file: File | null) => {
  if (file) {
    const reader = new FileReader()
    reader.onload = (e) => {
      formData.value.logoPreview = e.target?.result as string
    }
    reader.readAsDataURL(file)
  }
}

const handleLogoError = (error: string) => {
  errors.value.logo = error
}

const removeLogo = () => {
  formData.value.logoFile = null
  formData.value.logoPreview = null
}

const previewChanges = () => {
  console.log('Previewing changes:', pendingChanges.value)
}

const goBackToTenant = () => {
  if (hasChanges.value) {
    if (confirm('You have unsaved changes. Are you sure you want to leave?')) {
      router.push(`/dashboard/tenants/${tenantId.value}`)
    }
  } else {
    router.push(`/dashboard/tenants/${tenantId.value}`)
  }
}

const openDocumentation = () => {
  window.open('/docs/tenant-management', '_blank')
}

const contactSupport = () => {
  router.push('/dashboard/support')
}

const formatDate = (dateString: string | undefined) => {
  if (!dateString) return 'N/A'
  return new Date(dateString).toLocaleDateString()
}

// Lifecycle
onMounted(async () => {
  // Fetch tenant details if not already loaded
  if (!selectedTenant.value || selectedTenant.value.id !== tenantId.value) {
    await tenantStore.fetchTenantById(tenantId.value)
  }

  // Load tenant data into form
  loadTenantData()
})

// Watch for tenant changes
watch(selectedTenant, () => {
  if (selectedTenant.value) {
    loadTenantData()
  }
})

// Watch for tenant store errors
watch(
  () => tenantStore.error,
  (newError) => {
    if (newError) {
      submitError.value = newError
    }
  }
)
</script>