<template>
  <button
    @click="handleClick"
    :class="[
      'w-full px-4 py-2 text-left text-sm transition-colors duration-200 flex items-center justify-between',
      'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700',
      'focus:outline-none focus:bg-gray-100 dark:focus:bg-gray-700',
      disabled && 'opacity-50 cursor-not-allowed',
      itemClass
    ]"
    :disabled="disabled"
  >
    <div class="flex items-center">
      <Icon v-if="icon" :name="icon" class="h-4 w-4 mr-2" />
      <slot>{{ label }}</slot>
    </div>
    <div v-if="badge || $slots.badge" class="flex items-center">
      <slot name="badge">
        <span
          v-if="badge"
          class="text-xs text-gray-500 bg-gray-100 dark:bg-gray-700 px-2 py-0.5 rounded-full"
        >
          {{ badge }}
        </span>
      </slot>
    </div>
  </button>
</template>

<script setup lang="ts">
export interface CustomDropdownItemProps {
  label?: string;
  icon?: string;
  badge?: string | number;
  disabled?: boolean;
  itemClass?: string;
}

interface Emits {
  (e: 'click', event: MouseEvent): void;
}

const props = withDefaults(defineProps<CustomDropdownItemProps>(), {
  disabled: false,
  itemClass: ''
});

const emit = defineEmits<Emits>();

const handleClick = (event: MouseEvent) => {
  if (!props.disabled) {
    emit('click', event);
  }
};
</script>

<style scoped>
/* Additional styles if needed */
</style>
