import { ref, onMounted, onUnmounted, watch, nextTick, computed } from 'vue';
import type { Ref } from 'vue';

export interface FocusTrapOptions {
  initialFocus?: Ref<HTMLElement | null> | HTMLElement | string;
  fallbackFocus?: Ref<HTMLElement | null> | HTMLElement | string;
  escapeDeactivates?: boolean;
  clickOutsideDeactivates?: boolean;
  returnFocusOnDeactivate?: boolean;
  allowOutsideClick?: boolean | ((event: MouseEvent | TouchEvent) => boolean);
}

export function useFocusTrap(
  target: Ref<HTMLElement | null>,
  options: FocusTrapOptions = {}
) {
  const {
    initialFocus,
    fallbackFocus,
    escapeDeactivates = true,
    clickOutsideDeactivates = false,
    returnFocusOnDeactivate = true,
    allowOutsideClick = false,
  } = options;

  const isActive = ref(false);
  let lastFocusedElement: HTMLElement | null = null;

  const focusableElementsSelector = [
    'a[href]',
    'button:not([disabled])',
    'input:not([disabled])',
    'textarea:not([disabled])',
    'select:not([disabled])',
    'details',
    '[tabindex]:not([tabindex="-1"])',
  ].join(', ');

  let focusableElements: HTMLElement[] = [];
  let firstFocusableElement: HTMLElement | null = null;
  let lastFocusableElement: HTMLElement | null = null;

  const updateFocusableElements = () => {
    if (!target.value) return;
    focusableElements = Array.from(
      target.value.querySelectorAll(focusableElementsSelector)
    ) as HTMLElement[];
    firstFocusableElement = focusableElements[0] || null;
    lastFocusableElement = focusableElements[focusableElements.length - 1] || null;
  };

  const getElement = (elOrRef: Ref<HTMLElement | null> | HTMLElement | string | undefined): HTMLElement | null => {
    if (!elOrRef) return null;
    if (typeof elOrRef === 'string') return document.querySelector(elOrRef);
    return 'value' in elOrRef ? elOrRef.value : elOrRef;
  };

  const activate = async () => {
    if (isActive.value || !target.value) return;

    lastFocusedElement = document.activeElement as HTMLElement;
    isActive.value = true;
    updateFocusableElements();

    await nextTick();

    let elementToFocus = getElement(initialFocus);
    if (!elementToFocus || !focusableElements.includes(elementToFocus)) {
      elementToFocus = firstFocusableElement;
    }
    if (!elementToFocus) {
      elementToFocus = getElement(fallbackFocus) || target.value;
    }
    elementToFocus?.focus();
  };

  const deactivate = () => {
    if (!isActive.value) return;
    isActive.value = false;
    if (returnFocusOnDeactivate && lastFocusedElement) {
      lastFocusedElement.focus();
    }
  };

  const handleKeyDown = (event: KeyboardEvent) => {
    if (!isActive.value || !target.value) return;

    if (event.key === 'Escape' && escapeDeactivates) {
      event.preventDefault();
      deactivate();
      return;
    }

    if (event.key === 'Tab') {
      updateFocusableElements(); // Ensure list is up-to-date
      if (!firstFocusableElement) { // No focusable elements
        event.preventDefault();
        return;
      }

      if (event.shiftKey) {
        if (document.activeElement === firstFocusableElement) {
          event.preventDefault();
          lastFocusableElement?.focus();
        }
      } else {
        if (document.activeElement === lastFocusableElement) {
          event.preventDefault();
          firstFocusableElement?.focus();
        }
      }
    }
  };

  const handleClickOutside = (event: MouseEvent | TouchEvent) => {
    if (!isActive.value || !target.value || !clickOutsideDeactivates) return;

    if (typeof allowOutsideClick === 'function' && allowOutsideClick(event)) {
      return;
    }
    if (typeof allowOutsideClick === 'boolean' && allowOutsideClick) {
      return;
    }

    const path = event.composedPath && event.composedPath();
    if (path && path.includes(target.value)) {
      return;
    }
    if (target.value.contains(event.target as Node)) {
      return;
    }
    deactivate();
  };

  watch(target, (newTarget, _oldTarget) => { // Prefixed oldTarget
    if (newTarget) {
      // If trap was active and target changes, re-evaluate or reactivate
      if (isActive.value) {
        // Simple approach: deactivate and let external logic reactivate if needed
        deactivate(); 
      }
    } else {
      deactivate();
    }
  });

  onMounted(() => {
    // Auto-activate if target is present? Or require manual activation?
    // For now, require manual activation.
    document.addEventListener('keydown', handleKeyDown);
    if (clickOutsideDeactivates) {
      document.addEventListener('mousedown', handleClickOutside, { capture: true, passive: true });
      document.addEventListener('touchstart', handleClickOutside, { capture: true, passive: true });
    }
  });

  onUnmounted(() => {
    deactivate(); // Ensure trap is deactivated
    document.removeEventListener('keydown', handleKeyDown);
    if (clickOutsideDeactivates) {
      document.removeEventListener('mousedown', handleClickOutside, { capture: true });
      document.removeEventListener('touchstart', handleClickOutside, { capture: true });
    }
  });

  return {
    isActive: computed(() => isActive.value),
    activate,
    deactivate,
    updateFocusableElements,
  };
}