<template>
  <div class="modal-examples p-6 space-y-4">
    <h2 class="text-2xl font-bold text-gray-900 mb-6">Global Modal Examples</h2>
    
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      <!-- Basic Modal -->
      <UiButton @click="openBasicModal" variant="contained">
        Open Basic Modal
      </UiButton>

      <!-- Confirmation Modal -->
      <UiButton @click="openConfirmationModal" variant="outline">
        Open Confirmation
      </UiButton>

      <!-- Alert Modal -->
      <UiButton @click="openModalAlert" variant="ghost">
        Open Alert
      </UiButton>

      <!-- Custom Component Modal -->
      <UiButton @click="openComponentModal" variant="contained" color="success">
        Open Component Modal
      </UiButton>

      <!-- Large Modal -->
      <UiButton @click="openLargeModal" variant="outline" color="warning">
        Open Large Modal
      </UiButton>

      <!-- Full Screen Modal -->
      <UiButton @click="openFullScreenModal" variant="contained" color="error">
        Open Full Screen
      </UiButton>
    </div>

    <!-- Results Display -->
    <div v-if="lastResult" class="mt-6 p-4 bg-gray-100 rounded-lg">
      <h3 class="font-semibold text-gray-900">Last Modal Result:</h3>
      <p class="text-gray-700">{{ lastResult }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useGlobalModal } from '~/composables/useGlobalModal'
import { ComponentSize } from '~/app/shared/types'
import UiButton from '~/components/ui/UiButton.vue'

const { openModal, openConfirmModal, openAlertModal } = useGlobalModal()
const lastResult = ref<string>('')

// Basic modal example
const openBasicModal = () => {
  openModal({
    title: 'Basic Modal',
    size: ComponentSize.MD,
    closable: true,
    maskClosable: true,
    onClose: () => {
      lastResult.value = 'Basic modal was closed'
    }
  })
}

// Confirmation modal example
const openConfirmationModal = async () => {
  const confirmed = await openConfirmModal(
    'Confirm Action',
    'Are you sure you want to proceed with this action?',
    {
      confirmText: 'Yes, Proceed',
      cancelText: 'Cancel',
      type: 'warning',
      onConfirm: () => {
        lastResult.value = 'User confirmed the action'
      },
      onCancel: () => {
        lastResult.value = 'User cancelled the action'
      }
    }
  )
  
  if (!lastResult.value) {
    lastResult.value = confirmed ? 'Confirmed' : 'Cancelled'
  }
}

// Alert modal example
const openModalAlert = async () => {
  await openAlertModal(
    'Information',
    'This is an informational message.',
    'info'
  )
  lastResult.value = 'Alert modal was dismissed'
}

// Component modal example
const openComponentModal = () => {
  // This would open a modal with a custom component
  // For now, we'll use a basic modal
  openModal({
    title: 'Component Modal',
    size: ComponentSize.LG,
    closable: true,
    onClose: () => {
      lastResult.value = 'Component modal was closed'
    }
  })
}

// Large modal example
const openLargeModal = () => {
  openModal({
    title: 'Large Modal',
    size: ComponentSize.XL,
    closable: true,
    centered: true,
    onClose: () => {
      lastResult.value = 'Large modal was closed'
    }
  })
}

// Full screen modal example
const openFullScreenModal = () => {
  openModal({
    title: 'Full Screen Modal',
    size: 'full',
    closable: true,
    animation: 'slide',
    onClose: () => {
      lastResult.value = 'Full screen modal was closed'
    }
  })
}
</script>

<style scoped>
.modal-examples {
  max-width: 1200px;
  margin: 0 auto;
}
</style>
