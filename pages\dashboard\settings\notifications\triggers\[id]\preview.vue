<template>
  <div>
    <AppBreadcrumbs :items="breadcrumbs" />
    <UiCard class="mt-4">
      <template #header>
        <div class="flex justify-between items-center">
          <h2 class="text-lg font-semibold">
            Preview Notification Trigger: {{ notificationTriggerStore.selectedTrigger?.name || triggerId }}
          </h2>
          <UiButton variant="outline" @click="navigateToTriggersList">
            Back to Triggers List
          </UiButton>
        </div>
      </template>

      <div v-if="isLoadingDetails || isLoadingPreview" class="text-center p-8">
        <UiSpinner />
        <p>{{ isLoadingPreview ? 'Sending preview...' : 'Loading trigger details...' }}</p>
      </div>
      <div v-else-if="loadingError" class="mt-4">
        <UiAlert type="error" title="Error">
          {{ loadingError }}
        </UiAlert>
      </div>
      <div v-else-if="notificationTriggerStore.selectedTrigger" class="space-y-4">
        <div>
          <h3 class="text-sm font-medium text-gray-500">Name</h3>
          <p class="mt-1 text-sm text-gray-900">{{ notificationTriggerStore.selectedTrigger.name }}</p>
        </div>
        <div v-if="notificationTriggerStore.selectedTrigger.description">
          <h3 class="text-sm font-medium text-gray-500">Description</h3>
          <p class="mt-1 text-sm text-gray-900">{{ notificationTriggerStore.selectedTrigger.description }}</p>
        </div>
        <div>
          <h3 class="text-sm font-medium text-gray-500">Event Type</h3>
          <p class="mt-1 text-sm text-gray-900">{{ notificationTriggerStore.selectedTrigger.eventType }}</p>
        </div>
        <div>
          <h3 class="text-sm font-medium text-gray-500">Channel</h3>
          <p class="mt-1 text-sm text-gray-900">{{ notificationTriggerStore.selectedTrigger.channel }}</p>
        </div>
        <div>
          <h3 class="text-sm font-medium text-gray-500">Recipients</h3>
          <p class="mt-1 text-sm text-gray-900">{{ notificationTriggerStore.selectedTrigger.recipients.join(', ') }}</p>
        </div>
        <div>
          <h3 class="text-sm font-medium text-gray-500">Template ID</h3>
          <p class="mt-1 text-sm text-gray-900">{{ notificationTriggerStore.selectedTrigger.templateId }}</p>
        </div>
        <div>
          <h3 class="text-sm font-medium text-gray-500">Conditions</h3>
          <pre class="mt-1 text-sm text-gray-900 bg-gray-50 p-3 rounded-md overflow-x-auto">{{ JSON.stringify(notificationTriggerStore.selectedTrigger.conditions, null, 2) }}</pre>
        </div>
        <div>
          <h3 class="text-sm font-medium text-gray-500">Status</h3>
          <p :class="['mt-1 text-sm', notificationTriggerStore.selectedTrigger.isActive ? 'text-green-600' : 'text-red-600']">
            {{ notificationTriggerStore.selectedTrigger.isActive ? 'Active' : 'Inactive' }}
          </p>
        </div>
        <div>
          <h3 class="text-sm font-medium text-gray-500">Created At</h3>
          <p class="mt-1 text-sm text-gray-900">{{ new Date(notificationTriggerStore.selectedTrigger.createdAt).toLocaleString() }}</p>
        </div>
        <div>
          <h3 class="text-sm font-medium text-gray-500">Last Updated At</h3>
          <p class="mt-1 text-sm text-gray-900">{{ new Date(notificationTriggerStore.selectedTrigger.updatedAt).toLocaleString() }}</p>
        </div>

        <div class="pt-4 border-t">
          <UiButton @click="handleSendPreview" :loading="isLoadingPreview" variant="primary">
            Send Test Preview
          </UiButton>
        </div>
      </div>
      <div v-else class="mt-4">
        <UiAlert type="warning" title="Trigger not found">
          The requested notification trigger could not be found. It may have been deleted.
        </UiAlert>
      </div>
    </UiCard>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useRoute, useRouter } from '#app';
import AppBreadcrumbs from '~/components/layout/dashboard/AppBreadcrumbs.vue';
import UiCard from '~/components/ui/UiCard.vue';
import UiButton from '~/components/ui/UiButton.vue';
import UiSpinner from '~/components/ui/UiSpinner.vue';
import UiAlert from '~/components/ui/UiAlert.vue';
import { useNotificationTriggerStore } from '~/stores/notificationTrigger';
import { PlatformRoles, TenantRoles } from'~/app/features/auth/constants/roles';

definePageMeta({
  layout: 'dashboard',
  middleware: ['rbac'], // 'auth' is global
  roles: [TenantRoles.TENANT_OWNER, TenantRoles.ADMIN],
  title: 'Preview Notification Trigger',
});

const router = useRouter();
const route = useRoute();
const notificationTriggerStore = useNotificationTriggerStore();

const triggerId = route.params.id as string;
const isLoadingDetails = ref(false);
const isLoadingPreview = ref(false);
const loadingError = ref<string | null>(null);

onMounted(async () => {
  if (triggerId) {
    isLoadingDetails.value = true;
    loadingError.value = null;
    try {
      const existingTrigger = notificationTriggerStore.getTriggerById(triggerId);
      if (existingTrigger) {
        notificationTriggerStore.setSelectedTrigger(existingTrigger);
      } else {
        if (notificationTriggerStore.triggers.length === 0) {
            await notificationTriggerStore.fetchNotificationTriggers();
        }
        const found = notificationTriggerStore.getTriggerById(triggerId);
        if (found) {
            notificationTriggerStore.setSelectedTrigger(found);
        } else {
            loadingError.value = `Notification trigger with ID ${triggerId} not found.`;
            notificationTriggerStore.setSelectedTrigger(null);
        }
      }
    } catch (e: any) {
      loadingError.value = e.message || 'An unexpected error occurred while fetching trigger details.';
      notificationTriggerStore.setSelectedTrigger(null);
    } finally {
      isLoadingDetails.value = false;
    }
  } else {
    loadingError.value = 'No trigger ID provided.';
  }
});

const breadcrumbs = computed(() => [
  { label: 'Dashboard', href: '/dashboard' },
  { label: 'Settings', href: '/dashboard/settings' },
  { label: 'Notifications', href: '/dashboard/settings/notifications' },
  { label: 'Triggers', href: '/dashboard/settings/notifications/triggers' },
  { label: `Preview: ${notificationTriggerStore.selectedTrigger?.name || triggerId}`, current: true },
]);

const handleSendPreview = async () => {
  if (!triggerId) return;
  isLoadingPreview.value = true;
  try {
    await notificationTriggerStore.previewNotificationTrigger(triggerId);
    // Success toast is handled by the store action.
  } catch (error) {
    // Error toast is handled by the store action.
    console.error('Failed to send trigger preview from page:', error);
  } finally {
    isLoadingPreview.value = false;
  }
};

const navigateToTriggersList = () => {
  router.push('/dashboard/settings/notifications/triggers');
};
</script>