# Global Actions Plugin

A simple Nuxt plugin that provides global access to the `useGlobalActions` composable throughout your application. This plugin makes global add/remove functionality available via the Nuxt app context.

## Features

- **Global Access**: Available throughout the app via `$globalActions`
- **Simple Factory**: Create global action instances with a simple factory method
- **Type Safety**: Full TypeScript support with proper type declarations
- **Development Tools**: Debug helpers in development mode
- **Auto Cleanup**: Automatic cleanup on app unmount
- **Utility Methods**: Helper methods for managing global instances

## Usage

### Accessing the Plugin

```typescript
// In components
<script setup>
const { $globalActions } = useNuxtApp()
</script>

// In composables
export function useMyComposable() {
  const { $globalActions } = useNuxtApp()
  return { globalActions: $globalActions }
}

// In pages
<script setup>
const { $globalActions } = useNuxtApp()
</script>
```

### Basic Usage

The plugin provides a simple factory method to create global action instances:

```typescript
<script setup>
const { $globalActions } = useNuxtApp()

// Define your item type
interface MyItem {
  id: string
  name: string
  description?: string
}

// Create a global actions instance
const myItems = $globalActions.create<MyItem>({
  key: 'my-items', // Unique key for global state
  initialItems: [],
  maxItems: 100,
  allowDuplicates: false,
  validateItem: (item) => {
    if (!item.name) return 'Name is required'
    return true
  },
  onAdd: async (item) => {
    console.log('Item added:', item.name)
  }
})

// Use the instance
await myItems.actions.add({
  id: '1',
  name: 'My First Item',
  description: 'This is a sample item'
})

// Get all items
const allItems = myItems.items.value

// Update an item
await myItems.actions.update('1', { description: 'Updated description' })

// Remove an item
await myItems.actions.remove('1')
</script>
```

### Advanced Example

```typescript
<script setup>
const { $globalActions } = useNuxtApp()

// Create a more complex instance with validation and callbacks
interface TodoItem {
  id: string
  title: string
  completed: boolean
  priority: 'low' | 'medium' | 'high'
  createdAt: Date
}

const todos = $globalActions.create<TodoItem>({
  key: 'todos',
  maxItems: 500,
  allowDuplicates: false,
  validateItem: (todo) => {
    if (!todo.title?.trim()) return 'Title is required'
    if (!['low', 'medium', 'high'].includes(todo.priority)) return 'Invalid priority'
    return true
  },
  onAdd: async (todo) => {
    console.log('Todo added:', todo.title)
    // Could trigger API call or analytics
  },
  onRemove: async (todo) => {
    console.log('Todo removed:', todo.title)
  },
  onUpdate: async (todo) => {
    console.log('Todo updated:', todo.title)
  }
})

// Add a todo
await todos.actions.add({
  id: `todo-${Date.now()}`,
  title: 'Complete project',
  completed: false,
  priority: 'high',
  createdAt: new Date()
})

// Mark as completed
await todos.actions.update('todo-123', { completed: true })

// Get high priority todos
const highPriorityTodos = todos.items.value.filter(t => t.priority === 'high')

// Toggle completion
const todo = todos.actions.find('todo-123')
if (todo) {
  await todos.actions.toggle({
    ...todo,
    completed: !todo.completed
  })
}
</script>
```

### Utility Methods

```typescript
<script setup>
const { $globalActions } = useNuxtApp()

// Check if an instance exists
const hasTenants = $globalActions.has('global-tenants') // true

// Get raw state of an instance
const tenantState = $globalActions.get('global-tenants')

// Get all instance keys
const allKeys = $globalActions.keys() // ['global-tenants', 'global-documents', ...]

// Remove an instance
$globalActions.remove('custom-templates')

// Clear all instances (use with caution)
$globalActions.clear()
</script>
```

## Component Integration

### In Vue Components

```vue
<template>
  <div>
    <h2>Tenants ({{ tenantCount }})</h2>
    <ul>
      <li v-for="tenant in tenants" :key="tenant.id">
        {{ tenant.name }} - {{ tenant.plan }}
        <button @click="removeTenant(tenant.id)">Remove</button>
      </li>
    </ul>
    <button @click="addSampleTenant">Add Sample Tenant</button>
  </div>
</template>

<script setup>
const { $globalActions } = useNuxtApp()

// Access pre-configured tenant actions
const { tenants } = $globalActions

// Reactive state
const tenantList = tenants.items
const tenantCount = tenants.count

// Methods
const addSampleTenant = async () => {
  await tenants.actions.add({
    id: `tenant-${Date.now()}`,
    name: `Sample Tenant ${Date.now()}`,
    slug: `sample-${Date.now()}`,
    plan: 'basic',
    status: 'active',
    locale: 'en',
    timezone: 'UTC',
    createdAt: new Date(),
    updatedAt: new Date()
  })
}

const removeTenant = async (id: string) => {
  await tenants.actions.remove(id)
}
</script>
```

### In Composables

```typescript
// composables/useTenantManagement.ts
export function useTenantManagement() {
  const { $globalActions } = useNuxtApp()
  const { tenants } = $globalActions

  const createTenant = async (data: Omit<TenantActionItem, 'id' | 'createdAt' | 'updatedAt'>) => {
    const tenant: TenantActionItem = {
      ...data,
      id: `tenant-${Date.now()}`,
      createdAt: new Date(),
      updatedAt: new Date()
    }
    return await tenants.actions.add(tenant)
  }

  const activeTenants = computed(() => 
    tenants.items.value.filter(t => t.status === 'active')
  )

  return {
    tenants: tenants.items,
    tenantCount: tenants.count,
    isLoading: tenants.isLoading,
    error: tenants.error,
    activeTenants,
    createTenant,
    actions: tenants.actions
  }
}
```

## Development Tools

In development mode, the plugin exposes global actions to the browser console:

```javascript
// Available in browser console
window.__globalActions.tenants.items.value
window.__globalActions.documents.actions.add(/* ... */)
window.__globalActions.keys()
```

## Analytics Integration

The plugin automatically integrates with your analytics system (if available):

- Tenant events: `tenant_added`, `tenant_removed`
- Document events: `document_added` (via `$trackDocumentEvent`)
- Case events: `case_created` (via `$trackCaseEvent`)

## Best Practices

1. **Use Pre-configured Instances**: Leverage the built-in instances for common entities
2. **Custom Validation**: Always implement proper validation for your data types
3. **Error Handling**: Check return values and handle errors appropriately
4. **Memory Management**: The plugin automatically cleans up on app unmount
5. **Type Safety**: Use proper TypeScript interfaces extending `GlobalActionItem`

This plugin provides a powerful, type-safe way to manage global state with add/remove functionality throughout your Legal SaaS application.
