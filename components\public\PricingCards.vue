<template>
  <section :class="sectionClass">
    <div class="container mx-auto px-6">
      <!-- Section Header -->
      <div v-if="title || subtitle" :class="headerClass">
        <h2 v-if="title" :class="titleClass">
          {{ title }}
        </h2>
        <p v-if="subtitle" :class="subtitleClass">
          {{ subtitle }}
        </p>
      </div>

      <!-- Billing Toggle -->
      <div v-if="showBillingToggle" class="flex justify-center mb-12">
        <div class="bg-gray-100 dark:bg-gray-800 p-1 rounded-lg">
          <button
            :class="[
              'px-4 py-2 rounded-md text-sm font-medium transition-all duration-200',
              billingPeriod === 'monthly' 
                ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm' 
                : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
            ]"
            @click="billingPeriod = 'monthly'"
          >
            Monthly
          </button>
          <button
            :class="[
              'px-4 py-2 rounded-md text-sm font-medium transition-all duration-200',
              billingPeriod === 'yearly' 
                ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm' 
                : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
            ]"
            @click="billingPeriod = 'yearly'"
          >
            Yearly
            <span class="ml-1 text-xs bg-green-100 text-green-800 px-2 py-0.5 rounded-full">
              Save 20%
            </span>
          </button>
        </div>
      </div>

      <!-- Pricing Cards Grid -->
      <div :class="gridClass">
        <div
          v-for="(plan, index) in plans"
          :key="index"
          :class="[
            cardClass,
            plan.featured ? featuredCardClass : '',
            plan.popular ? popularCardClass : ''
          ]"
          :style="{ animationDelay: `${index * 100}ms` }"
        >
          <!-- Popular Badge -->
          <div v-if="plan.popular" class="absolute -top-4 left-1/2 transform -translate-x-1/2">
            <UiBadge variant="primary" size="md" class="px-4 py-1">
              {{ plan.popularText || 'Most Popular' }}
            </UiBadge>
          </div>

          <!-- Plan Header -->
          <div class="text-center mb-8">
            <!-- Plan Icon -->
            <div v-if="plan.icon" class="mb-4">
              <Icon :name="plan.icon" :class="planIconClass" />
            </div>

            <!-- Plan Name -->
            <h3 :class="planNameClass">
              {{ plan.name }}
            </h3>

            <!-- Plan Description -->
            <p v-if="plan.description" :class="planDescriptionClass">
              {{ plan.description }}
            </p>

            <!-- Plan Price -->
            <div class="mt-6">
              <div :class="priceClass">
                <span v-if="plan.currency" class="text-2xl">{{ plan.currency }}</span>
                {{ getCurrentPrice(plan) }}
                <span v-if="plan.period" :class="periodClass">
                  /{{ billingPeriod === 'yearly' ? 'year' : plan.period }}
                </span>
              </div>
              <div v-if="billingPeriod === 'yearly' && plan.yearlyDiscount" class="text-sm text-green-600 dark:text-green-400 mt-1">
                Save {{ plan.yearlyDiscount }}% annually
              </div>
            </div>
          </div>

          <!-- Plan Features -->
          <div class="mb-8">
            <ul class="space-y-3">
              <li
                v-for="(feature, featureIndex) in plan.features"
                :key="featureIndex"
                class="flex items-start"
              >
                <Icon
                  :name="feature.included !== false ? 'material-symbols:check-circle' : 'material-symbols:cancel'"
                  :class="[
                    'w-5 h-5 mr-3 mt-0.5 flex-shrink-0',
                    feature.included !== false ? 'text-green-500' : 'text-gray-400'
                  ]"
                />
                <span :class="[
                  'text-sm',
                  feature.included !== false 
                    ? (props.background === 'dark' ? 'text-gray-300' : 'text-gray-700')
                    : 'text-gray-400 line-through'
                ]">
                  {{ feature.text }}
                </span>
              </li>
            </ul>
          </div>

          <!-- Plan Action -->
          <div class="mt-auto">
            <UiButton
              :variant="plan.featured ? 'primary' : 'outline'"
              :size="'lg'"
              :to="plan.action?.to"
              :href="plan.action?.href"
              class="w-full group-hover:scale-105 transition-transform duration-200"
              @click="plan.action?.onClick"
            >
              {{ plan.action?.text || 'Get Started' }}
            </UiButton>
          </div>

          <!-- Additional Info -->
          <div v-if="plan.additionalInfo" class="mt-4 text-center">
            <p class="text-xs text-gray-500 dark:text-gray-400">
              {{ plan.additionalInfo }}
            </p>
          </div>
        </div>
      </div>

      <!-- Bottom Note -->
      <div v-if="bottomNote" class="text-center mt-12">
        <p class="text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
          {{ bottomNote }}
        </p>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
interface PlanFeature {
  text: string
  included?: boolean
}

interface PlanAction {
  text?: string
  to?: string
  href?: string
  onClick?: () => void
}

interface Plan {
  name: string
  description?: string
  icon?: string
  price: number | string
  yearlyPrice?: number | string
  currency?: string
  period?: string
  yearlyDiscount?: number
  features: PlanFeature[]
  action?: PlanAction
  featured?: boolean
  popular?: boolean
  popularText?: string
  additionalInfo?: string
}

interface Props {
  title?: string
  subtitle?: string
  plans: Plan[]
  background?: 'white' | 'gray' | 'dark'
  textAlign?: 'left' | 'center'
  showBillingToggle?: boolean
  bottomNote?: string
}

const props = withDefaults(defineProps<Props>(), {
  background: 'gray',
  textAlign: 'center',
  showBillingToggle: true
})

// Reactive state
const billingPeriod = ref<'monthly' | 'yearly'>('monthly')

// Methods
const getCurrentPrice = (plan: Plan): string => {
  if (billingPeriod.value === 'yearly' && plan.yearlyPrice !== undefined) {
    return plan.yearlyPrice.toString()
  }
  return plan.price.toString()
}

// Computed classes
const sectionClass = computed(() => {
  const backgrounds = {
    white: 'bg-white',
    gray: 'bg-gray-50',
    dark: 'bg-gray-900 text-white'
  }
  return `py-16 ${backgrounds[props.background]}`
})

const headerClass = computed(() => {
  const alignments = {
    left: 'text-left mb-12',
    center: 'text-center mb-12'
  }
  return alignments[props.textAlign]
})

const titleClass = computed(() => {
  const baseClass = 'font-bold mb-4'
  const colorClass = props.background === 'dark' ? 'text-white' : 'text-gray-800'
  return `${baseClass} text-3xl md:text-4xl ${colorClass}`
})

const subtitleClass = computed(() => {
  const baseClass = 'max-w-3xl'
  const colorClass = props.background === 'dark' ? 'text-gray-300' : 'text-gray-600'
  const alignClass = props.textAlign === 'center' ? 'mx-auto' : ''
  return `${baseClass} text-lg ${colorClass} ${alignClass}`
})

const gridClass = computed(() => {
  const baseClass = 'grid gap-8'
  const columnsClass = props.plans.length <= 2 ? 'grid-cols-1 md:grid-cols-2' : 
                     props.plans.length === 3 ? 'grid-cols-1 md:grid-cols-3' :
                     'grid-cols-1 md:grid-cols-2 lg:grid-cols-4'
  return `${baseClass} ${columnsClass}`
})

const cardClass = computed(() => {
  const baseClass = 'relative group flex flex-col p-8 rounded-xl transition-all duration-300 animate-fade-in-up'
  const backgroundClass = props.background === 'dark' 
    ? 'bg-gray-800 border border-gray-700 hover:border-gray-600' 
    : 'bg-white border border-gray-200 hover:border-gray-300 shadow-sm hover:shadow-lg'
  return `${baseClass} ${backgroundClass}`
})

const featuredCardClass = computed(() => {
  return 'ring-2 ring-brandPrimary-500 ring-opacity-50 scale-105'
})

const popularCardClass = computed(() => {
  return 'border-brandPrimary-500 shadow-lg'
})

const planIconClass = computed(() => {
  const colorClass = props.background === 'dark' ? 'text-brandPrimary-400' : 'text-brandPrimary-600'
  return `w-12 h-12 mx-auto ${colorClass}`
})

const planNameClass = computed(() => {
  const baseClass = 'font-bold text-xl'
  const colorClass = props.background === 'dark' ? 'text-white' : 'text-gray-900'
  return `${baseClass} ${colorClass}`
})

const planDescriptionClass = computed(() => {
  const colorClass = props.background === 'dark' ? 'text-gray-400' : 'text-gray-600'
  return `text-sm mt-2 ${colorClass}`
})

const priceClass = computed(() => {
  const baseClass = 'font-extrabold text-5xl'
  const colorClass = props.background === 'dark' ? 'text-white' : 'text-gray-900'
  return `${baseClass} ${colorClass}`
})

const periodClass = computed(() => {
  const colorClass = props.background === 'dark' ? 'text-gray-400' : 'text-gray-600'
  return `text-lg font-medium ${colorClass}`
})
</script>

<style scoped>
@keyframes fadeInFromBottom {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  animation: fadeInFromBottom 0.6s ease-out forwards;
  opacity: 0;
}
</style>
