/**
 * Enhanced Analytics Composable
 * 
 * Comprehensive analytics system with event tracking, user behavior analysis,
 * performance monitoring, and privacy compliance
 */

import { ref, computed, watch, onMounted, onUnmounted, readonly } from 'vue' // Added readonly
import type { ISODateString, UUID, UserId } from '@shared/types/core'
import { useLogger } from '../core/useLogger.js' // Added .js
import { useLocalStorage } from '../core/useLocalStorage.js' // Added .js

// ============================================================================
// ANALYTICS TYPES
// ============================================================================

export interface AnalyticsConfig {
  enabled: boolean
  debug: boolean
  trackingId?: string
  apiEndpoint?: string
  batchSize: number
  flushInterval: number
  enableAutoTracking: boolean
  enablePerformanceTracking: boolean
  enableErrorTracking: boolean
  enableUserTracking: boolean
  privacyMode: boolean
  consentRequired: boolean
}

export interface AnalyticsEvent {
  id: UUID
  type: EventType
  name: string
  category: EventCategory
  properties: Record<string, any>
  timestamp: ISODateString
  sessionId: string
  userId?: UserId
  pageUrl: string
  userAgent: string
  referrer?: string
  context: EventContext
}

export enum EventType {
  PAGE_VIEW = 'page_view',
  USER_ACTION = 'user_action',
  SYSTEM_EVENT = 'system_event',
  ERROR = 'error',
  PERFORMANCE = 'performance',
  BUSINESS = 'business'
}

export enum EventCategory {
  NAVIGATION = 'navigation',
  AUTHENTICATION = 'authentication',
  CASE_MANAGEMENT = 'case_management',
  DOCUMENT_MANAGEMENT = 'document_management',
  BILLING = 'billing',
  USER_INTERFACE = 'user_interface',
  SYSTEM = 'system',
  ERROR = 'error'
}

export interface EventContext {
  feature?: string
  component?: string
  action?: string
  metadata?: Record<string, any>
  experimentId?: string
  variantId?: string
}

export interface UserProperties {
  userId?: UserId
  role?: string
  tenantId?: string
  plan?: string
  signupDate?: ISODateString
  lastActiveDate?: ISODateString
  totalSessions?: number
  customProperties?: Record<string, any>
}

export interface PerformanceMetrics {
  pageLoadTime: number
  domContentLoaded: number
  firstContentfulPaint: number
  largestContentfulPaint: number
  firstInputDelay: number
  cumulativeLayoutShift: number
  ttfb?: number; // Added ttfb
  memoryUsage?: {
    used: number
    total: number
    limit: number
  }
}

// ============================================================================
// ANALYTICS COMPOSABLE
// ============================================================================

export function useAnalytics(config: Partial<AnalyticsConfig> = {}) {
  // ============================================================================
  // CONFIGURATION
  // ============================================================================
  
  const defaultConfig: AnalyticsConfig = {
    enabled: true,
    debug: process.env.NODE_ENV === 'development',
    batchSize: 10,
    flushInterval: 30000, // 30 seconds
    enableAutoTracking: true,
    enablePerformanceTracking: true,
    enableErrorTracking: true,
    enableUserTracking: true,
    privacyMode: false,
    consentRequired: true
  }
  
  const analyticsConfig = ref({ ...defaultConfig, ...config })
  
  // ============================================================================
  // DEPENDENCIES
  // ============================================================================
  
  const logger = useLogger('Analytics')
  const { value: consent } = useLocalStorage('analytics_consent', false)
  const { value: sessionId } = useLocalStorage('session_id', '')
  
  // ============================================================================
  // STATE
  // ============================================================================
  
  const isInitialized = ref(false)
  const eventQueue = ref<AnalyticsEvent[]>([])
  const userProperties = ref<UserProperties>({})
  const currentPage = ref('')
  // const sessionStartTime = ref(Date.now()) // Unused
  
  // ============================================================================
  // COMPUTED
  // ============================================================================
  
  const canTrack = computed(() => 
    analyticsConfig.value.enabled && 
    (!analyticsConfig.value.consentRequired || consent.value)
  )
  
  const isDebugMode = computed(() => analyticsConfig.value.debug)
  
  // ============================================================================
  // CORE TRACKING METHODS
  // ============================================================================
  
  const track = (
    name: string,
    properties: Record<string, any> = {},
    context: Partial<EventContext> = {}
  ) => {
    if (!canTrack.value) return
    
    const event: AnalyticsEvent = {
      id: generateEventId(),
      type: EventType.USER_ACTION, // Default type, can be overridden by specific trackers
      name,
      category: inferCategory(name, context),
      properties: sanitizeProperties(properties),
      timestamp: new Date().toISOString() as ISODateString,
      sessionId: getSessionId(),
      userId: userProperties.value.userId,
      pageUrl: window.location.href,
      userAgent: navigator.userAgent,
      referrer: document.referrer,
      context: {
        feature: getCurrentFeature(),
        component: getCurrentComponent(),
        ...context
      }
    }
    
    addToQueue(event)
    
    if (isDebugMode.value) {
      logger.debug('Analytics event tracked', { event })
    }
  }
  
  const page = (
    pageName?: string,
    properties: Record<string, any> = {}
  ) => {
    if (!canTrack.value) return
    
    const page = pageName || getCurrentPageName()
    currentPage.value = page
    
    track('page_view', {
      page,
      title: document.title,
      path: window.location.pathname,
      search: window.location.search,
      hash: window.location.hash,
      ...properties
    }); // Category will be inferred by track()
  }
  
  const identify = (userId: UserId, properties: Partial<UserProperties> = {}) => {
    if (!canTrack.value) return
    
    userProperties.value = {
      ...userProperties.value,
      userId,
      ...properties,
      lastActiveDate: new Date().toISOString() as ISODateString
    }
    
    track('user_identified', {
      userId,
      ...properties
    }); // Category will be inferred
    
    logger.debug('User identified', { userId, properties })
  }
  
  const alias = (newUserId: UserId, previousUserId?: UserId) => {
    if (!canTrack.value) return
    
    track('user_alias', {
      newUserId,
      previousUserId: previousUserId || userProperties.value.userId
    }); // Category will be inferred
    
    userProperties.value.userId = newUserId
  }
  
  // ============================================================================
  // BUSINESS EVENT TRACKING
  // ============================================================================
  
  const trackCaseEvent = (
    action: string,
    caseId: string,
    properties: Record<string, any> = {}
  ) => {
    track(`case_${action}`, {
      caseId,
      ...properties
    }, {
      feature: 'cases',
      action
    }); // Category will be inferred
  }
  
  const trackDocumentEvent = (
    action: string,
    documentId: string,
    properties: Record<string, any> = {}
  ) => {
    track(`document_${action}`, {
      documentId,
      ...properties
    }, {
      feature: 'documents',
      action
    }); // Category will be inferred
  }
  
  const trackBillingEvent = (
    action: string,
    properties: Record<string, any> = {}
  ) => {
    track(`billing_${action}`, properties, {
      feature: 'billing',
      action
    }); // Category will be inferred
  }
  
  const trackUIEvent = (
    component: string,
    action: string,
    properties: Record<string, any> = {}
  ) => {
    track(`ui_${component}_${action}`, properties, {
      component,
      action
    }); // Category will be inferred
  }
  
  // ============================================================================
  // PERFORMANCE TRACKING
  // ============================================================================
  
  const trackPerformance = (metrics: Partial<PerformanceMetrics>) => {
    if (!analyticsConfig.value.enablePerformanceTracking || !canTrack.value) return
    
    // For trackPerformance, we want to explicitly set the type and category
    const event: AnalyticsEvent = {
      id: generateEventId(),
      type: EventType.PERFORMANCE,
      name: 'performance_metrics',
      category: EventCategory.SYSTEM,
      properties: sanitizeProperties(metrics),
      timestamp: new Date().toISOString() as ISODateString,
      sessionId: getSessionId(),
      userId: userProperties.value.userId,
      pageUrl: window.location.href,
      userAgent: navigator.userAgent,
      referrer: document.referrer,
      context: {
        feature: getCurrentFeature(),
        component: getCurrentComponent()
      }
    };
    addToQueue(event);
    if (isDebugMode.value) {
      logger.debug('Analytics event tracked', { event });
    }
  }
  
  const trackPagePerformance = () => {
    if (!analyticsConfig.value.enablePerformanceTracking) return
    
    // Use Performance Observer API
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        
        entries.forEach((entry) => {
          if (entry.entryType === 'navigation') {
            const navEntry = entry as PerformanceNavigationTiming
            
            trackPerformance({
              pageLoadTime: navEntry.loadEventEnd - navEntry.loadEventStart,
              domContentLoaded: navEntry.domContentLoadedEventEnd - navEntry.domContentLoadedEventStart,
              firstContentfulPaint: getFirstContentfulPaint(),
              largestContentfulPaint: getLargestContentfulPaint()
            })
          }
        })
      })
      
      observer.observe({ entryTypes: ['navigation', 'paint', 'largest-contentful-paint'] })
    }
  }
  
  const trackError = (error: Error, context: Record<string, any> = {}) => {
    if (!analyticsConfig.value.enableErrorTracking || !canTrack.value) return
    
    track('error_occurred', {
      message: error.message,
      stack: error.stack,
      name: error.name,
      page: currentPage.value,
      ...context
    }); // Type and Category will be inferred by track() or should be set if specific
    // For trackError, explicitly set type and category
    const errorEvent: AnalyticsEvent = {
      id: generateEventId(),
      type: EventType.ERROR,
      name: 'error_occurred',
      category: EventCategory.ERROR,
      properties: sanitizeProperties({
        message: error.message,
        stack: error.stack,
        name: error.name,
        page: currentPage.value,
        ...context
      }),
      timestamp: new Date().toISOString() as ISODateString,
      sessionId: getSessionId(),
      userId: userProperties.value.userId,
      pageUrl: window.location.href,
      userAgent: navigator.userAgent,
      referrer: document.referrer,
      context: {
        feature: getCurrentFeature(),
        component: getCurrentComponent(),
        ...context // Allow overriding feature/component from context if provided
      }
    };
    addToQueue(errorEvent);
    if (isDebugMode.value) {
      logger.debug('Analytics error event tracked', { event: errorEvent });
    }
  }
  
  // ============================================================================
  // QUEUE MANAGEMENT
  // ============================================================================
  
  const addToQueue = (event: AnalyticsEvent) => {
    eventQueue.value.push(event)
    
    if (eventQueue.value.length >= analyticsConfig.value.batchSize) {
      flush()
    }
  }
  
  const flush = async () => {
    if (eventQueue.value.length === 0) return
    
    const events = [...eventQueue.value]
    eventQueue.value = []
    
    try {
      await sendEvents(events)
      logger.debug('Analytics events sent', { count: events.length })
    } catch (error) {
      // Re-queue events on failure
      eventQueue.value.unshift(...events)
      logger.error('Failed to send analytics events', { error })
    }
  }
  
  const sendEvents = async (events: AnalyticsEvent[]) => {
    if (!analyticsConfig.value.apiEndpoint) {
      // Log events in debug mode
      if (isDebugMode.value) {
        console.group('📊 Analytics Events')
        events.forEach(event => console.log(event))
        console.groupEnd()
      }
      return
    }
    
    const response = await fetch(analyticsConfig.value.apiEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        events,
        userProperties: userProperties.value,
        sessionId: getSessionId()
      })
    })
    
    if (!response.ok) {
      throw new Error(`Analytics API error: ${response.status}`)
    }
  }
  
  // ============================================================================
  // UTILITY FUNCTIONS
  // ============================================================================
  
  const generateEventId = (): UUID => {
    return `evt_${Date.now()}_${Math.random().toString(36).substr(2, 9)}` as UUID
  }
  
  const getSessionId = (): string => {
    if (!sessionId.value) {
      const newSessionId = `sess_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      sessionId.value = newSessionId
      return newSessionId
    }
    return sessionId.value
  }
  
  const getCurrentPageName = (): string => {
    const path = window.location.pathname
    const segments = path.split('/').filter(Boolean)
    return segments.join('_') || 'home'
  }
  
  const getCurrentFeature = (): string => {
    const path = window.location.pathname
    const segments = path.split('/').filter(Boolean)
    return segments[0] || 'home'
  }
  
  const getCurrentComponent = (): string => {
    // Try to get component name from Vue devtools or route meta
    return 'unknown'
  }
  
  const inferCategory = (eventName: string, context: Partial<EventContext>): EventCategory => {
    // if (context.category) return context.category; // context.category is not part of EventContext
    
    if (eventName.includes('case')) return EventCategory.CASE_MANAGEMENT
    if (eventName.includes('document')) return EventCategory.DOCUMENT_MANAGEMENT
    if (eventName.includes('billing')) return EventCategory.BILLING
    if (eventName.includes('auth') || eventName.includes('login')) return EventCategory.AUTHENTICATION
    if (eventName.includes('ui_') || eventName.includes('click')) return EventCategory.USER_INTERFACE
    if (eventName.includes('error')) return EventCategory.ERROR
    
    return EventCategory.SYSTEM
  }
  
  const sanitizeProperties = (properties: Record<string, any>): Record<string, any> => {
    const sanitized: Record<string, any> = {}
    
    for (const [key, value] of Object.entries(properties)) {
      // Remove sensitive data
      if (key.toLowerCase().includes('password') || 
          key.toLowerCase().includes('token') ||
          key.toLowerCase().includes('secret')) {
        continue
      }
      
      // Limit string length
      if (typeof value === 'string' && value.length > 1000) {
        sanitized[key] = value.substring(0, 1000) + '...'
      } else {
        sanitized[key] = value
      }
    }
    
    return sanitized
  }
  
  const getFirstContentfulPaint = (): number => {
    const entries = performance.getEntriesByName('first-contentful-paint')
    return entries.length > 0 ? entries[0].startTime : 0
  }
  
  const getLargestContentfulPaint = (): number => {
    const entries = performance.getEntriesByType('largest-contentful-paint')
    return entries.length > 0 ? entries[entries.length - 1].startTime : 0
  }
  
  // ============================================================================
  // CONSENT MANAGEMENT
  // ============================================================================
  
  const grantConsent = () => {
    consent.value = true
    logger.info('Analytics consent granted')
  }

  const revokeConsent = () => {
    consent.value = false
    eventQueue.value = []
    userProperties.value = {}
    logger.info('Analytics consent revoked')
  }
  
  const hasConsent = () => consent.value
  
  // ============================================================================
  // AUTO-TRACKING SETUP
  // ============================================================================
  
  const setupAutoTracking = () => {
    if (!analyticsConfig.value.enableAutoTracking) return
    
    // Track page views on route changes
    if (typeof window !== 'undefined') {
      window.addEventListener('popstate', () => {
        page()
      })
    }
    
    // Track errors
    if (analyticsConfig.value.enableErrorTracking) {
      window.addEventListener('error', (event) => {
        trackError(new Error(event.message), {
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno
        })
      })
      
      window.addEventListener('unhandledrejection', (event) => {
        trackError(new Error(event.reason), {
          type: 'unhandled_promise_rejection'
        })
      })
    }
    
    // Track performance
    if (analyticsConfig.value.enablePerformanceTracking) {
      trackPagePerformance()
    }
  }
  
  // ============================================================================
  // INITIALIZATION
  // ============================================================================
  
  const initialize = () => {
    if (isInitialized.value) return
    
    logger.info('Initializing analytics', { config: analyticsConfig.value })
    
    // Set up auto-tracking
    setupAutoTracking()
    
    // Set up flush interval
    const flushInterval = setInterval(flush, analyticsConfig.value.flushInterval)
    
    // Track initial page view
    if (canTrack.value) {
      page()
    }
    
    isInitialized.value = true
    
    // Cleanup on unmount
    onUnmounted(() => {
      clearInterval(flushInterval)
      flush() // Final flush
    })
  }
  
  // ============================================================================
  // LIFECYCLE
  // ============================================================================
  
  onMounted(() => {
    initialize()
  })
  
  // Watch for consent changes
  watch(consent, (hasConsent) => {
    if (hasConsent && !isInitialized.value) {
      initialize()
    }
  })
  
  // ============================================================================
  // RETURN INTERFACE
  // ============================================================================
  
  return {
    // Configuration
    config: readonly(analyticsConfig),
    isInitialized: readonly(isInitialized),
    canTrack,
    
    // Core tracking
    track,
    page,
    identify,
    alias,
    
    // Business tracking
    trackCaseEvent,
    trackDocumentEvent,
    trackBillingEvent,
    trackUIEvent,
    
    // Performance tracking
    trackPerformance,
    trackError,
    
    // Queue management
    flush,
    
    // Consent management
    grantConsent,
    revokeConsent,
    hasConsent,
    
    // Utilities
    getSessionId,
    getCurrentPageName
  }
}
