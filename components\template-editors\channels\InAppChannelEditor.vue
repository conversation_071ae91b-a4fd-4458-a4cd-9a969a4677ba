<template>
  <div class="in-app-channel-editor space-y-6">
    <!-- In-App Notification Content -->
    <div class="space-y-4">
      <div class="flex items-center justify-between">
        <h3 class="text-lg font-medium text-gray-900 dark:text-white">In-App Notification</h3>
        <div class="flex items-center space-x-2">
          <UiButton
            @click="showTemplateLibrary = true"
            variant="outline"
            size="sm"
          >
            <Icon name="heroicons:book-open" class="w-4 h-4 mr-2" />
            Templates
          </UiButton>
          <UiButton
            @click="showVariableModal = true"
            variant="outline"
            size="sm"
          >
            <Icon name="heroicons:variable" class="w-4 h-4 mr-2" />
            Variables
          </UiButton>
        </div>
      </div>

      <!-- Notification Type -->
      <div class="space-y-2">
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
          Notification Type
        </label>
        <select
          v-model="localContent.type"
          @change="handleContentChange"
          class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm"
        >
          <option value="banner">Banner</option>
          <option value="toast">Toast</option>
          <option value="modal">Modal</option>
          <option value="sidebar">Sidebar</option>
          <option value="inline">Inline</option>
        </select>
      </div>

      <!-- Title -->
      <div class="space-y-2">
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
          Title
        </label>
        <UiInput
          v-model="localContent.title"
          placeholder="Notification title..."
          @input="handleContentChange"
        />
      </div>

      <!-- Content -->
      <div class="space-y-2">
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
          Content
        </label>
        <UiTextarea
          v-model="localContent.content"
          placeholder="Notification content..."
          :rows="4"
          @input="handleContentChange"
        />
      </div>

      <!-- Action Buttons -->
      <div class="space-y-4">
        <div class="flex items-center justify-between">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Action Buttons
          </label>
          <UiButton
            @click="addActionButton"
            variant="outline"
            size="sm"
            :disabled="localContent.actions.length >= 3"
          >
            <Icon name="heroicons:plus" class="w-4 h-4 mr-1" />
            Add Button
          </UiButton>
        </div>
        
        <div v-if="localContent.actions.length > 0" class="space-y-3">
          <div
            v-for="(action, index) in localContent.actions"
            :key="index"
            class="flex items-center space-x-3 p-3 border border-gray-200 dark:border-gray-700 rounded-lg"
          >
            <div class="flex-1 grid grid-cols-1 md:grid-cols-3 gap-3">
              <UiInput
                v-model="action.title"
                placeholder="Button text"
                @input="handleContentChange"
              />
              <select
                v-model="action.style"
                @change="handleContentChange"
                class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm"
              >
                <option value="primary">Primary</option>
                <option value="secondary">Secondary</option>
                <option value="success">Success</option>
                <option value="danger">Danger</option>
              </select>
              <UiInput
                v-model="action.url"
                placeholder="Action URL"
                @input="handleContentChange"
              />
            </div>
            <UiButton
              @click="removeActionButton(index)"
              variant="ghost"
              size="sm"
              class="text-red-600 hover:text-red-700"
            >
              <Icon name="heroicons:trash" class="w-4 h-4" />
            </UiButton>
          </div>
        </div>
        
        <div v-else class="text-sm text-gray-500 dark:text-gray-400 text-center py-4 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg">
          No action buttons added. Click "Add Button" to create interactive notifications.
        </div>
      </div>
    </div>

    <!-- In-App Notification Preview -->
    <UiCard class="p-4">
      <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-4">In-App Notification Preview</h4>
      
      <!-- Preview based on type -->
      <div class="space-y-4">
        <!-- Banner Preview -->
        <div v-if="localContent.type === 'banner'">
          <h5 class="text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">Banner Notification</h5>
          <div class="bg-indigo-50 dark:bg-indigo-900/20 border border-indigo-200 dark:border-indigo-800 rounded-lg p-4">
            <div class="flex items-start space-x-3">
              <Icon name="heroicons:information-circle" class="w-5 h-5 text-indigo-600 dark:text-indigo-400 mt-0.5 flex-shrink-0" />
              <div class="flex-1">
                <h6 class="text-sm font-medium text-indigo-900 dark:text-indigo-100">
                  {{ renderedTitle || 'Notification Title' }}
                </h6>
                <p class="text-sm text-indigo-700 dark:text-indigo-300 mt-1">
                  {{ renderedContent || 'Notification content will appear here...' }}
                </p>
                <div v-if="localContent.actions.length > 0" class="flex space-x-2 mt-3">
                  <button
                    v-for="action in localContent.actions"
                    :key="action.title"
                    :class="getActionButtonClass(action.style)"
                    class="px-3 py-1 text-xs rounded-md transition-colors"
                  >
                    {{ action.title }}
                  </button>
                </div>
              </div>
              <button class="text-indigo-400 hover:text-indigo-600">
                <Icon name="heroicons:x-mark" class="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>

        <!-- Toast Preview -->
        <div v-else-if="localContent.type === 'toast'">
          <h5 class="text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">Toast Notification</h5>
          <div class="max-w-sm ml-auto">
            <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg p-4">
              <div class="flex items-start space-x-3">
                <Icon name="heroicons:bell" class="w-5 h-5 text-indigo-600 dark:text-indigo-400 mt-0.5 flex-shrink-0" />
                <div class="flex-1">
                  <h6 class="text-sm font-medium text-gray-900 dark:text-white">
                    {{ renderedTitle || 'Notification Title' }}
                  </h6>
                  <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                    {{ renderedContent || 'Notification content...' }}
                  </p>
                </div>
                <button class="text-gray-400 hover:text-gray-600">
                  <Icon name="heroicons:x-mark" class="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Modal Preview -->
        <div v-else-if="localContent.type === 'modal'">
          <h5 class="text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">Modal Notification</h5>
          <div class="bg-gray-900/50 rounded-lg p-8 flex items-center justify-center">
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl p-6 max-w-md w-full">
              <div class="flex items-start space-x-3 mb-4">
                <Icon name="heroicons:exclamation-triangle" class="w-6 h-6 text-indigo-600 dark:text-indigo-400 flex-shrink-0" />
                <div class="flex-1">
                  <h6 class="text-lg font-medium text-gray-900 dark:text-white">
                    {{ renderedTitle || 'Notification Title' }}
                  </h6>
                  <p class="text-sm text-gray-600 dark:text-gray-400 mt-2">
                    {{ renderedContent || 'Notification content will appear here...' }}
                  </p>
                </div>
              </div>
              <div v-if="localContent.actions.length > 0" class="flex justify-end space-x-2">
                <button
                  v-for="action in localContent.actions"
                  :key="action.title"
                  :class="getActionButtonClass(action.style)"
                  class="px-4 py-2 text-sm rounded-md transition-colors"
                >
                  {{ action.title }}
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Sidebar Preview -->
        <div v-else-if="localContent.type === 'sidebar'">
          <h5 class="text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">Sidebar Notification</h5>
          <div class="flex">
            <div class="w-64 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 p-4">
              <div class="space-y-3">
                <div class="flex items-center space-x-2">
                  <Icon name="heroicons:bell" class="w-4 h-4 text-indigo-600 dark:text-indigo-400" />
                  <span class="text-xs font-medium text-gray-900 dark:text-white">Notifications</span>
                </div>
                <div class="bg-indigo-50 dark:bg-indigo-900/20 border border-indigo-200 dark:border-indigo-800 rounded-lg p-3">
                  <h6 class="text-sm font-medium text-indigo-900 dark:text-indigo-100">
                    {{ renderedTitle || 'Notification Title' }}
                  </h6>
                  <p class="text-xs text-indigo-700 dark:text-indigo-300 mt-1">
                    {{ renderedContent || 'Content...' }}
                  </p>
                </div>
              </div>
            </div>
            <div class="flex-1 bg-gray-50 dark:bg-gray-900 p-4">
              <div class="text-xs text-gray-500 dark:text-gray-400">Main content area</div>
            </div>
          </div>
        </div>

        <!-- Inline Preview -->
        <div v-else-if="localContent.type === 'inline'">
          <h5 class="text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">Inline Notification</h5>
          <div class="space-y-3">
            <div class="text-xs text-gray-500 dark:text-gray-400">Page content above...</div>
            <div class="bg-indigo-50 dark:bg-indigo-900/20 border-l-4 border-indigo-400 p-4">
              <div class="flex items-start space-x-3">
                <Icon name="heroicons:information-circle" class="w-5 h-5 text-indigo-600 dark:text-indigo-400 mt-0.5 flex-shrink-0" />
                <div class="flex-1">
                  <h6 class="text-sm font-medium text-indigo-900 dark:text-indigo-100">
                    {{ renderedTitle || 'Notification Title' }}
                  </h6>
                  <p class="text-sm text-indigo-700 dark:text-indigo-300 mt-1">
                    {{ renderedContent || 'Notification content will appear here...' }}
                  </p>
                </div>
              </div>
            </div>
            <div class="text-xs text-gray-500 dark:text-gray-400">Page content below...</div>
          </div>
        </div>
      </div>
    </UiCard>

    <!-- In-App Settings -->
    <UiCard class="p-4">
      <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-4">In-App Settings</h4>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Position
          </label>
          <select
            v-model="localContent.settings.position"
            @change="handleContentChange"
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm"
          >
            <option value="top">Top</option>
            <option value="bottom">Bottom</option>
            <option value="top-right">Top Right</option>
            <option value="bottom-right">Bottom Right</option>
            <option value="center">Center</option>
          </select>
        </div>
        
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Auto Dismiss (seconds)
          </label>
          <UiInput
            v-model.number="localContent.settings.autoDismiss"
            type="number"
            min="0"
            placeholder="0 = Manual dismiss"
            @input="handleContentChange"
          />
        </div>
      </div>
      
      <div class="mt-4 space-y-3">
        <label class="flex items-center">
          <input
            type="checkbox"
            v-model="localContent.settings.dismissible"
            @change="handleContentChange"
            class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
          />
          <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">User can dismiss</span>
        </label>
        
        <label class="flex items-center">
          <input
            type="checkbox"
            v-model="localContent.settings.persistent"
            @change="handleContentChange"
            class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
          />
          <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Persistent across sessions</span>
        </label>
        
        <label class="flex items-center">
          <input
            type="checkbox"
            v-model="localContent.settings.enableSound"
            @change="handleContentChange"
            class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
          />
          <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Play notification sound</span>
        </label>
      </div>
    </UiCard>

    <!-- Template Library Modal -->
    <InAppTemplateLibraryModal
      v-if="showTemplateLibrary"
      @close="showTemplateLibrary = false"
      @select="insertTemplate"
    />

    <!-- Variable Insert Modal -->
    <VariableInsertModal
      v-if="showVariableModal"
      @close="showVariableModal = false"
      @insert="insertVariable"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, defineAsyncComponent } from 'vue'

// Lazy load modals
const InAppTemplateLibraryModal = defineAsyncComponent(() => import('../../template-modals/InAppTemplateLibraryModal.vue'))
const VariableInsertModal = defineAsyncComponent(() => import('../../template-modals/VariableInsertModal.vue'))

// Props
interface Props {
  content: any
  variables: string[]
  templateData: any
  category?: any
}

const props = withDefaults(defineProps<Props>(), {
  content: () => ({}),
  variables: () => [],
})

// Emits
const emit = defineEmits<{
  'update:content': [content: any]
  'update:variables': [variables: string[]]
  'content-change': [content: any, variables: string[]]
}>()

// State
const showTemplateLibrary = ref(false)
const showVariableModal = ref(false)

// Local content with defaults
const localContent = ref({
  type: 'banner',
  title: '',
  content: '',
  actions: [],
  settings: {
    position: 'top',
    autoDismiss: 0,
    dismissible: true,
    persistent: false,
    enableSound: false,
  },
  ...props.content
})

const localVariables = ref([...props.variables])

// Computed
const renderedTitle = computed(() => {
  let title = localContent.value.title || ''
  localVariables.value.forEach(variable => {
    const sampleValue = getSampleValue(variable)
    title = title.replace(new RegExp(`{${variable}}`, 'g'), sampleValue)
  })
  return title
})

const renderedContent = computed(() => {
  let content = localContent.value.content || ''
  localVariables.value.forEach(variable => {
    const sampleValue = getSampleValue(variable)
    content = content.replace(new RegExp(`{${variable}}`, 'g'), sampleValue)
  })
  return content
})

// Watchers
watch(() => props.content, (newContent) => {
  localContent.value = {
    type: 'banner',
    title: '',
    content: '',
    actions: [],
    settings: {
      position: 'top',
      autoDismiss: 0,
      dismissible: true,
      persistent: false,
      enableSound: false,
    },
    ...newContent
  }
}, { deep: true, immediate: true })

watch(() => props.variables, (newVariables) => {
  localVariables.value = [...newVariables]
}, { immediate: true })

// Methods
const handleContentChange = () => {
  emit('update:content', localContent.value)
  emit('content-change', localContent.value, localVariables.value)
}

const addActionButton = () => {
  if (localContent.value.actions.length < 3) {
    localContent.value.actions.push({
      title: '',
      style: 'primary',
      url: ''
    })
    handleContentChange()
  }
}

const removeActionButton = (index: number) => {
  localContent.value.actions.splice(index, 1)
  handleContentChange()
}

const getActionButtonClass = (style: string) => {
  const classes = {
    primary: 'bg-indigo-600 text-white hover:bg-indigo-700',
    secondary: 'bg-gray-200 text-gray-900 hover:bg-gray-300',
    success: 'bg-green-600 text-white hover:bg-green-700',
    danger: 'bg-red-600 text-white hover:bg-red-700',
  }
  return classes[style as keyof typeof classes] || classes.primary
}

const insertTemplate = (template: any) => {
  localContent.value.type = template.type || localContent.value.type
  localContent.value.title = template.title || localContent.value.title
  localContent.value.content = template.content || localContent.value.content
  
  if (template.actions) {
    localContent.value.actions = [...template.actions]
  }
  
  // Merge variables
  if (template.variables) {
    const newVariables = [...new Set([...localVariables.value, ...template.variables])]
    localVariables.value = newVariables
    emit('update:variables', newVariables)
  }
  
  showTemplateLibrary.value = false
  handleContentChange()
}

const insertVariable = (variable: string) => {
  const variableText = `{${variable}}`
  localContent.value.content += variableText
  
  // Add variable to list if not already present
  if (!localVariables.value.includes(variable)) {
    localVariables.value.push(variable)
    emit('update:variables', localVariables.value)
  }
  
  showVariableModal.value = false
  handleContentChange()
}

const getSampleValue = (variable: string) => {
  const sampleValues: Record<string, string> = {
    clientName: 'John Doe',
    companyName: 'Legal Services',
    appointmentDate: 'Jan 15',
    appointmentTime: '2:00 PM',
    amount: '$1,250',
    caseNumber: 'CASE-001',
    attorneyName: 'Jane Smith',
  }
  
  return sampleValues[variable] || `[${variable.toUpperCase()}]`
}
</script>
