<template>
  <div class="space-y-8">
    <!-- Analytics Header -->
    <div class="bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl p-8 text-white">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-3xl font-bold mb-2">Usage Analytics</h1>
          <p class="text-blue-100 text-lg">
            Comprehensive insights into platform usage and user engagement
          </p>
        </div>
        <div class="hidden md:flex items-center gap-4">
          <UiSelect
            v-model="selectedTimeRange"
            :options="timeRangeOptions"
            class="bg-white/10 border-white/20 text-white"
          />
          <UiButton @click="exportReport" variant="outline" class="border-white/20 text-white hover:bg-white/10">
            <Icon name="material-symbols:download" class="h-4 w-4 mr-2" />
            Export
          </UiButton>
        </div>
      </div>
    </div>

    <!-- Key Metrics -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <!-- Total Users -->
      <UiCard
        icon="material-symbols:group"
        icon-color="blue"
        :loading="isLoading"
        hover-effect
      >
        <template #header>
          <div class="flex items-center justify-between w-full">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Total Users</h3>
            <UiBadge :variant="metrics.users.growth > 0 ? 'success' : 'neutral'">
              {{ metrics.users.growth > 0 ? '+' : '' }}{{ metrics.users.growth }}%
            </UiBadge>
          </div>
        </template>
        <div class="space-y-3">
          <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ formatNumber(metrics.users.total) }}</p>
          <p class="text-sm text-gray-500 dark:text-gray-400">
            {{ formatNumber(metrics.users.active) }} active this month
          </p>
          <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div 
              class="bg-blue-600 h-2 rounded-full transition-all duration-300"
              :style="{ width: `${(metrics.users.active / metrics.users.total) * 100}%` }"
            ></div>
          </div>
        </div>
      </UiCard>

      <!-- API Calls -->
      <UiCard
        icon="material-symbols:api"
        icon-color="green"
        :loading="isLoading"
        hover-effect
      >
        <template #header>
          <div class="flex items-center justify-between w-full">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">API Calls</h3>
            <UiBadge :variant="metrics.api.growth > 0 ? 'success' : 'neutral'">
              {{ metrics.api.growth > 0 ? '+' : '' }}{{ metrics.api.growth }}%
            </UiBadge>
          </div>
        </template>
        <div class="space-y-3">
          <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ formatNumber(metrics.api.total) }}</p>
          <p class="text-sm text-gray-500 dark:text-gray-400">
            {{ formatNumber(metrics.api.daily) }} today
          </p>
          <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div 
              class="bg-green-600 h-2 rounded-full transition-all duration-300"
              :style="{ width: `${Math.min((metrics.api.daily / metrics.api.dailyLimit) * 100, 100)}%` }"
            ></div>
          </div>
        </div>
      </UiCard>

      <!-- Storage Used -->
      <UiCard
        icon="material-symbols:storage"
        icon-color="purple"
        :loading="isLoading"
        hover-effect
      >
        <template #header>
          <div class="flex items-center justify-between w-full">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Storage Used</h3>
            <UiBadge :variant="metrics.storage.percentage > 80 ? 'warning' : 'success'">
              {{ metrics.storage.percentage }}%
            </UiBadge>
          </div>
        </template>
        <div class="space-y-3">
          <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ metrics.storage.used }}GB</p>
          <p class="text-sm text-gray-500 dark:text-gray-400">
            of {{ metrics.storage.total }}GB total
          </p>
          <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div 
              :class="[
                'h-2 rounded-full transition-all duration-300',
                metrics.storage.percentage > 90 ? 'bg-red-600' :
                metrics.storage.percentage > 80 ? 'bg-yellow-600' : 'bg-purple-600'
              ]"
              :style="{ width: `${metrics.storage.percentage}%` }"
            ></div>
          </div>
        </div>
      </UiCard>

      <!-- Documents Created -->
      <UiCard
        icon="material-symbols:description"
        icon-color="yellow"
        :loading="isLoading"
        hover-effect
      >
        <template #header>
          <div class="flex items-center justify-between w-full">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Documents</h3>
            <UiBadge :variant="metrics.documents.growth > 0 ? 'success' : 'neutral'">
              {{ metrics.documents.growth > 0 ? '+' : '' }}{{ metrics.documents.growth }}%
            </UiBadge>
          </div>
        </template>
        <div class="space-y-3">
          <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ formatNumber(metrics.documents.total) }}</p>
          <p class="text-sm text-gray-500 dark:text-gray-400">
            {{ formatNumber(metrics.documents.thisMonth) }} this month
          </p>
          <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div 
              class="bg-yellow-600 h-2 rounded-full transition-all duration-300"
              :style="{ width: `${Math.min((metrics.documents.thisMonth / metrics.documents.monthlyTarget) * 100, 100)}%` }"
            ></div>
          </div>
        </div>
      </UiCard>
    </div>

    <!-- Charts Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- User Activity Chart -->
      <UiCard>
        <template #header>
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">User Activity Trends</h3>
            <div class="flex items-center gap-2">
              <UiButton @click="refreshCharts" variant="ghost" size="sm" :loading="refreshingCharts">
                <Icon name="material-symbols:refresh" class="h-4 w-4" />
              </UiButton>
              <UiSelect
                v-model="chartTimeRange"
                :options="chartTimeRangeOptions"
                size="sm"
              />
            </div>
          </div>
        </template>
        <div class="h-80">
          <!-- Chart placeholder - in real app, use Chart.js, D3, or similar -->
          <div class="w-full h-full bg-gradient-to-br from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-lg flex items-center justify-center">
            <div class="text-center">
              <Icon name="material-symbols:trending-up" class="h-16 w-16 text-blue-500 mx-auto mb-4" />
              <p class="text-gray-600 dark:text-gray-400">User Activity Chart</p>
              <p class="text-sm text-gray-500 dark:text-gray-500">Chart component would be rendered here</p>
            </div>
          </div>
        </div>
      </UiCard>

      <!-- API Usage Chart -->
      <UiCard>
        <template #header>
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">API Usage Patterns</h3>
            <UiBadge variant="info">{{ metrics.api.successRate }}% success rate</UiBadge>
          </div>
        </template>
        <div class="h-80">
          <!-- Chart placeholder -->
          <div class="w-full h-full bg-gradient-to-br from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20 rounded-lg flex items-center justify-center">
            <div class="text-center">
              <Icon name="material-symbols:api" class="h-16 w-16 text-green-500 mx-auto mb-4" />
              <p class="text-gray-600 dark:text-gray-400">API Usage Chart</p>
              <p class="text-sm text-gray-500 dark:text-gray-500">Chart component would be rendered here</p>
            </div>
          </div>
        </div>
      </UiCard>
    </div>

    <!-- Detailed Analytics Tables -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Top Tenants by Usage -->
      <UiCard>
        <template #header>
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Top Tenants by Usage</h3>
            <UiButton @click="viewAllTenants" variant="ghost" size="sm">
              View All
            </UiButton>
          </div>
        </template>
        <div class="space-y-4">
          <div
            v-for="tenant in topTenants"
            :key="tenant.id"
            class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors duration-200"
          >
            <div class="flex items-center gap-3">
              <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-white font-semibold">
                {{ tenant.name.charAt(0) }}
              </div>
              <div>
                <p class="text-sm font-medium text-gray-900 dark:text-white">{{ tenant.name }}</p>
                <p class="text-xs text-gray-500 dark:text-gray-400">{{ tenant.plan }} plan</p>
              </div>
            </div>
            <div class="text-right">
              <p class="text-sm font-medium text-gray-900 dark:text-white">{{ formatNumber(tenant.apiCalls) }}</p>
              <p class="text-xs text-gray-500 dark:text-gray-400">API calls</p>
            </div>
          </div>
        </div>
      </UiCard>

      <!-- Feature Usage -->
      <UiCard>
        <template #header>
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Feature Usage</h3>
            <UiButton @click="viewFeatureDetails" variant="ghost" size="sm">
              Details
            </UiButton>
          </div>
        </template>
        <div class="space-y-4">
          <div
            v-for="feature in featureUsage"
            :key="feature.name"
            class="space-y-2"
          >
            <div class="flex items-center justify-between">
              <div class="flex items-center gap-2">
                <Icon :name="feature.icon" class="h-5 w-5 text-gray-400" />
                <span class="text-sm font-medium text-gray-900 dark:text-white">{{ feature.name }}</span>
              </div>
              <span class="text-sm text-gray-500 dark:text-gray-400">{{ feature.usage }}%</span>
            </div>
            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div 
                class="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full transition-all duration-300"
                :style="{ width: `${feature.usage}%` }"
              ></div>
            </div>
          </div>
        </div>
      </UiCard>
    </div>

    <!-- Usage Insights -->
    <UiCard>
      <template #header>
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Usage Insights & Recommendations</h3>
          <UiBadge variant="info">{{ insights.length }} insights</UiBadge>
        </div>
      </template>
      <div class="space-y-4">
        <div
          v-for="insight in insights"
          :key="insight.id"
          class="flex items-start gap-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg"
        >
          <div :class="[
            'w-10 h-10 rounded-full flex items-center justify-center flex-shrink-0',
            insight.type === 'positive' ? 'bg-green-100 dark:bg-green-900/20' :
            insight.type === 'warning' ? 'bg-yellow-100 dark:bg-yellow-900/20' :
            'bg-blue-100 dark:bg-blue-900/20'
          ]">
            <Icon :name="insight.icon" :class="[
              'h-5 w-5',
              insight.type === 'positive' ? 'text-green-600 dark:text-green-400' :
              insight.type === 'warning' ? 'text-yellow-600 dark:text-yellow-400' :
              'text-blue-600 dark:text-blue-400'
            ]" />
          </div>
          <div class="flex-1">
            <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-1">{{ insight.title }}</h4>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">{{ insight.description }}</p>
            <UiButton v-if="insight.action" @click="handleInsightAction(insight)" variant="outline" size="sm">
              {{ insight.action }}
            </UiButton>
          </div>
        </div>
      </div>
    </UiCard>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { PlatformRoles } from '~/app/features/auth/constants/roles'

// Page meta configuration
definePageMeta({
  layout: 'dashboard',
  title: 'Usage Analytics',
  description: 'Comprehensive platform usage analytics and insights',
  pageHeaderIcon: 'material-symbols:analytics',
  pageHeaderStats: [
    { key: 'users', label: 'Active Users', value: '2,847', color: 'blue' },
    { key: 'api', label: 'API Calls', value: '156K', color: 'green' },
    { key: 'storage', label: 'Storage Used', value: '245GB', color: 'purple' },
    { key: 'growth', label: 'Growth Rate', value: '+12.5%', color: 'yellow' }
  ],
  showRealTimeStatus: true,
  autoRefreshEnabled: true,
  refreshInterval: 60000, // 1 minute
  middleware: ['rbac'],
  roles: [PlatformRoles.SUPER_ADMIN],
  breadcrumb: [
    { label: 'Dashboard', href: '/dashboard' },
    { label: 'Platform', href: '/dashboard/platform' },
    { label: 'Analytics', href: '/dashboard/platform/analytics' },
    { label: 'Usage' },
  ],
})

// Reactive state
const isLoading = ref(true)
const refreshingCharts = ref(false)
const selectedTimeRange = ref('30d')
const chartTimeRange = ref('7d')
const refreshInterval = ref<NodeJS.Timeout | null>(null)

// Time range options
const timeRangeOptions = [
  { value: '7d', label: 'Last 7 days' },
  { value: '30d', label: 'Last 30 days' },
  { value: '90d', label: 'Last 90 days' },
  { value: '1y', label: 'Last year' }
]

const chartTimeRangeOptions = [
  { value: '24h', label: '24 hours' },
  { value: '7d', label: '7 days' },
  { value: '30d', label: '30 days' }
]

// Analytics metrics
const metrics = reactive({
  users: {
    total: 2847,
    active: 1923,
    growth: 12.5
  },
  api: {
    total: 156420,
    daily: 5240,
    dailyLimit: 10000,
    successRate: 99.2,
    growth: 8.3
  },
  storage: {
    used: 245,
    total: 1000,
    percentage: 24.5
  },
  documents: {
    total: 18945,
    thisMonth: 2340,
    monthlyTarget: 3000,
    growth: 15.7
  }
})

// Top tenants data
const topTenants = ref([
  {
    id: 1,
    name: 'Acme Legal Corp',
    plan: 'Enterprise',
    apiCalls: 45230
  },
  {
    id: 2,
    name: 'Justice & Associates',
    plan: 'Professional',
    apiCalls: 32150
  },
  {
    id: 3,
    name: 'Legal Partners LLC',
    plan: 'Professional',
    apiCalls: 28940
  },
  {
    id: 4,
    name: 'Smith & Johnson Law',
    plan: 'Starter',
    apiCalls: 15670
  },
  {
    id: 5,
    name: 'Corporate Legal Services',
    plan: 'Enterprise',
    apiCalls: 12340
  }
])

// Feature usage data
const featureUsage = ref([
  {
    name: 'Document Management',
    icon: 'material-symbols:description',
    usage: 89
  },
  {
    name: 'Case Management',
    icon: 'material-symbols:gavel',
    usage: 76
  },
  {
    name: 'Template Engine',
    icon: 'material-symbols:article',
    usage: 68
  },
  {
    name: 'User Management',
    icon: 'material-symbols:group',
    usage: 92
  },
  {
    name: 'Reporting',
    icon: 'material-symbols:assessment',
    usage: 54
  },
  {
    name: 'API Access',
    icon: 'material-symbols:api',
    usage: 43
  }
])

// Usage insights
const insights = ref([
  {
    id: 1,
    type: 'positive',
    icon: 'material-symbols:trending-up',
    title: 'API Usage Growth',
    description: 'API calls have increased by 15% this month, indicating strong platform adoption.',
    action: 'View API Analytics'
  },
  {
    id: 2,
    type: 'warning',
    icon: 'material-symbols:warning',
    title: 'Storage Approaching Limit',
    description: 'Some tenants are approaching their storage limits. Consider upgrading their plans.',
    action: 'Review Storage Usage'
  },
  {
    id: 3,
    type: 'info',
    icon: 'material-symbols:lightbulb',
    title: 'Feature Adoption Opportunity',
    description: 'Reporting features have low adoption. Consider providing training or improving UX.',
    action: 'Create Training Materials'
  },
  {
    id: 4,
    type: 'positive',
    icon: 'material-symbols:security',
    title: 'High Security Compliance',
    description: '98% of tenants have enabled two-factor authentication, exceeding industry standards.',
    action: null
  }
])

// Utility functions
const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

// Methods
const refreshCharts = async () => {
  try {
    refreshingCharts.value = true

    // Simulate API call to refresh chart data
    await new Promise(resolve => setTimeout(resolve, 1500))

    // In a real app, you would fetch fresh analytics data
    // const chartData = await $api.get('/platform/analytics/charts', { timeRange: chartTimeRange.value })

    console.log('Charts refreshed')

  } catch (error) {
    console.error('Error refreshing charts:', error)
  } finally {
    refreshingCharts.value = false
  }
}

const exportReport = async () => {
  try {
    // Simulate report generation
    console.log('Exporting usage analytics report...')

    // In a real app, you would generate and download the report
    // const report = await $api.post('/platform/analytics/export', {
    //   type: 'usage',
    //   timeRange: selectedTimeRange.value
    // })

    // Create mock CSV data
    const csvData = [
      ['Metric', 'Value', 'Growth'],
      ['Total Users', metrics.users.total, `${metrics.users.growth}%`],
      ['API Calls', metrics.api.total, `${metrics.api.growth}%`],
      ['Storage Used', `${metrics.storage.used}GB`, 'N/A'],
      ['Documents', metrics.documents.total, `${metrics.documents.growth}%`]
    ]

    const csvContent = csvData.map(row => row.join(',')).join('\n')
    const blob = new Blob([csvContent], { type: 'text/csv' })
    const url = URL.createObjectURL(blob)

    const link = document.createElement('a')
    link.href = url
    link.download = `usage-analytics-${selectedTimeRange.value}-${new Date().toISOString().split('T')[0]}.csv`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    URL.revokeObjectURL(url)

  } catch (error) {
    console.error('Error exporting report:', error)
  }
}

const viewAllTenants = () => {
  navigateTo('/dashboard/tenants/manage')
}

const viewFeatureDetails = () => {
  navigateTo('/dashboard/platform/analytics/features')
}

const handleInsightAction = (insight: any) => {
  console.log('Handling insight action:', insight.action)

  switch (insight.action) {
    case 'View API Analytics':
      navigateTo('/dashboard/platform/analytics/performance')
      break
    case 'Review Storage Usage':
      navigateTo('/dashboard/platform/analytics/storage')
      break
    case 'Create Training Materials':
      navigateTo('/dashboard/platform/training')
      break
    default:
      console.log('Unknown action:', insight.action)
  }
}

const fetchAnalyticsData = async () => {
  try {
    // In a real app, fetch analytics data from API
    // const data = await $api.get('/platform/analytics/usage', {
    //   timeRange: selectedTimeRange.value
    // })

    // Simulate data updates with some variation
    metrics.users.active = Math.max(1500, metrics.users.active + Math.floor((Math.random() - 0.5) * 100))
    metrics.api.daily = Math.max(4000, metrics.api.daily + Math.floor((Math.random() - 0.5) * 500))

  } catch (error) {
    console.error('Error fetching analytics data:', error)
  }
}

// Lifecycle hooks
onMounted(async () => {
  // Initial data load
  await fetchAnalyticsData()
  isLoading.value = false

  // Set up auto-refresh interval
  refreshInterval.value = setInterval(fetchAnalyticsData, 60000)
})

onUnmounted(() => {
  if (refreshInterval.value) {
    clearInterval(refreshInterval.value)
  }
})
</script>
