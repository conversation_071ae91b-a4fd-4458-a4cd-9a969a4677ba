import { defineNuxtConfig } from 'nuxt/config';
import tailwindcss from '@tailwindcss/vite';

// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  compatibilityDate: '2025-05-15',

  // Enhanced module configuration
  modules: ['@pinia/nuxt', '@nuxt/icon', '@vueuse/nuxt', '@nuxt/image', 'nuxt-charts', 'dayjs-nuxt', '@nuxtjs/i18n'],

  // Day.js configuration
  // dayjs: {
  //   locales: ['en', 'he', 'ar'],
  //   plugins: ['relativeTime', 'utc', 'timezone', 'duration', 'customParseFormat'],
  //   defaultLocale: 'en',
  //   defaultTimezone: 'America/New_York'
  // },

  // i18n configuration
  i18n: {
    locales: [
      {
        code: 'en',
        language: 'en-US',
        name: 'English',
        nativeName: 'English',
        flag: '🇺🇸',
        dir: 'ltr'
      },
      {
        code: 'he',
        language: 'he-IL',
        name: 'Hebrew',
        nativeName: 'עברית',
        flag: '🇮🇱',
        dir: 'rtl'
      },
      {
        code: 'ar',
        language: 'ar-SA',
        name: 'Arabic',
        nativeName: 'العربية',
        flag: '🇸🇦',
        dir: 'rtl'
      }
    ],
    lazy: false,
    defaultLocale: 'en',
    strategy: 'prefix_except_default',
    detectBrowserLanguage: {
      useCookie: true,
      cookieKey: 'i18n_redirected',
      redirectOn: 'root',
      alwaysRedirect: false,
      fallbackLocale: 'en'
    },
    bundle: {
      optimizeTranslationDirective: false
    },
    vueI18n: './i18n.config.ts'
  },

  // Enhanced plugin configuration
  plugins: [
    '~/plugins/api-requester.server.ts',
    '~/plugins/responsive.client.ts',
    '~/plugins/performance.client.ts',
    '~/plugins/performance-optimizations.client.ts',
    '~/plugins/globalActions.client.ts',
    '~/plugins/dateFormatters.client.ts',
    '~/plugins/storageFormatters.client.ts',

  ],

  // Enhanced component configuration
  components: {
    dirs: ['~/app/shared/components', '~/app/shared/components/ui/charts', '~/app/shared/components/form', '~/components']
  },

  // Enhanced CSS configuration
  css: [
    '~/assets/css/main.css'
  ],


  vite: {
    plugins: [
      tailwindcss()
    ],
    build: {
      rollupOptions: {
        output: {
          manualChunks: {
            // Vendor chunks
            'vendor-vue': ['vue', 'vue-router', '@vue/runtime-core'],
            'vendor-pinia': ['pinia'],
            'vendor-ui': ['@headlessui/vue', '@vueuse/core'],

            // Feature chunks
            'feature-auth': ['./app/features/auth/index.ts'],
            'feature-cases': ['./app/features/cases/index.ts'],
            'feature-documents': ['./app/features/documents/index.ts'],
            'feature-templates': ['./app/features/templates/index.ts']
          }
        }
      },
      chunkSizeWarningLimit: 500
    },
    optimizeDeps: {
      include: [
        'vue',
        'vue-router',
        'pinia',
        '@vueuse/core',
        '@headlessui/vue',
        '@vee-validate/rules'
      ],
      exclude: [
        'form-data'
      ]
    },
    ssr: {
      noExternal: ['form-data']
    }
  },

  // Enhanced runtime configuration
  runtimeConfig: {
    public: {
      apiBase: process.env.API_BASE || 'http://localhost:3000',
      envMode: process.env.NODE_ENV || 'development',
      baseUrl: process.env.BASE_URL || 'https://legalflow.com',
      performance: {
        monitoring: process.env.NODE_ENV === 'production',
        lazyLoading: true,
        caching: true
      },
      seo: {
        siteName: 'LegalFlow',
        defaultImage: '/images/og-default.jpg',
        twitterHandle: '@legalflow'
      }
    }
  },

  // Enhanced build configuration
  build: {
    analyze: process.env.ANALYZE === 'true',
    transpile: ['@vee-validate/rules'],
  },

  // Enhanced alias configuration
  alias: {
    '@app': './app',
    '@features': './app/features',
    '@shared': './app/shared',
    '@core': './app/core'
  },

  // Enhanced Nitro configuration
  nitro: {
    plugins: [
      // '~/server/plugins/devtool-fix-nitro-plugin.ts'
    ],
    experimental: {
      wasm: true
    },
    // Ensure form-data is handled correctly on server-side
    externals: {
      inline: ['form-data']
    }
  },

  devtools: { enabled: true },
  devServer: {
    port: 8000,
  },

  // Enhanced experimental features
  experimental: {
    payloadExtraction: false,
    componentIslands: true
  },

  // App configuration
  app: {
    head: {
      charset: 'utf-8',
      viewport: 'width=device-width, initial-scale=1',
      title: 'LegalFlow - Secure Legal SaaS Platform',
      meta: [
        { name: 'description', content: 'Transform your legal practice with LegalFlow\'s secure, intelligent SaaS platform for case management, document automation, and client collaboration.' },
        { name: 'format-detection', content: 'telephone=no' },
        { name: 'theme-color', content: '#6366f1' },
        { name: 'msapplication-TileColor', content: '#6366f1' }
      ],
      link: [
        { rel: 'icon', type: 'image/x-icon', href: '/favicon.ico' },
        { rel: 'apple-touch-icon', sizes: '180x180', href: '/apple-touch-icon.png' },
        { rel: 'icon', type: 'image/png', sizes: '32x32', href: '/favicon-32x32.png' },
        { rel: 'icon', type: 'image/png', sizes: '16x16', href: '/favicon-16x16.png' },
        { rel: 'manifest', href: '/site.webmanifest' }
      ]
    }
  }
})