<template>
    <div class="h-screen flex flex-col bg-gray-50">
        <!-- Top Toolbar -->
        <div class=" bg-white border-b border-gray-200 flex  ">
            <!-- Toolbar content area -->
            <Toolbar />
        </div>

        <!-- Main Content Area -->
        <div class="flex-1 flex overflow-hidden">
            <!-- Collapsible Sidebar -->
            <div class="bg-white border-r border-gray-200 transition-all duration-300 ease-in-out"
                :class="sidebarCollapsed ? 'w-0' : 'w-80'">
                <div class="h-full overflow-hidden">
                    <!-- Sidebar content area -->
                </div>
            </div>

            <!-- Sidebar Toggle Button -->
            <div class="flex flex-col flex-1">
                <!-- Document Editor Area -->
                <div class="flex-1 bg-white">
                    <!-- Document content area -->
                    <Document :zoom="zoom" />
                </div>
            </div>
        </div>

        <!-- Bottom Status Bar -->
        <div class=" bg-gray-100 border-t border-gray-200 flex ">
            <!-- Status bar content area -->
            <Footer @zoom-change="handleZoomChange" />
        </div>
    </div>
</template>

<script setup lang="ts">
import Toolbar from './document-editor/Toolbar.vue'
import Footer from './document-editor/Footer.vue'
import Document from './document-editor/Document.vue'

const sidebarCollapsed = ref(true)
const zoom = ref(100)

const handleZoomChange = (zoomLevel: number) => {
  zoom.value = zoomLevel;
}

const toggleSidebar = () => {
  sidebarCollapsed.value = !sidebarCollapsed.value
}
</script>
