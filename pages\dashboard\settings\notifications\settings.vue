<template>
  <div class="space-y-8">
    <!-- Notification Settings Header -->
    <div class="bg-gradient-to-r from-purple-600 to-pink-600 rounded-xl p-8 text-white">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-3xl font-bold mb-2">Notification Settings</h1>
          <p class="text-purple-100 text-lg">
            Configure notification preferences, channels, and delivery settings
          </p>
        </div>
        <div class="hidden md:flex items-center gap-4">
          <UiButton @click="testNotifications" variant="outline" class="border-white/20 text-white hover:bg-white/10">
            <Icon name="material-symbols:send" class="h-4 w-4 mr-2" />
            Test Notifications
          </UiButton>
          <UiButton @click="resetToDefaults" variant="outline" class="border-white/20 text-white hover:bg-white/10">
            <Icon name="material-symbols:refresh" class="h-4 w-4 mr-2" />
            Reset Defaults
          </UiButton>
        </div>
      </div>
    </div>

    <!-- Settings Navigation -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
      <div class="border-b border-gray-200 dark:border-gray-700">
        <nav class="flex space-x-8 px-6" aria-label="Settings">
          <button
            v-for="tab in settingsTabs"
            :key="tab.id"
            @click="activeTab = tab.id"
            :class="[
              'py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200',
              activeTab === tab.id
                ? 'border-purple-500 text-purple-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
            ]"
          >
            <Icon :name="tab.icon" class="h-5 w-5 mr-2 inline" />
            {{ tab.label }}
          </button>
        </nav>
      </div>

      <!-- Settings Content -->
      <div class="p-6">
        <!-- Email Settings -->
        <div v-if="activeTab === 'email'" class="space-y-6">
          <div>
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Email Configuration</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <UiInput
                v-model="emailSettings.smtpHost"
                label="SMTP Host"
                placeholder="smtp.example.com"
                :disabled="saving"
              />
              <UiInput
                v-model="emailSettings.smtpPort"
                label="SMTP Port"
                type="number"
                placeholder="587"
                :disabled="saving"
              />
              <UiInput
                v-model="emailSettings.smtpUsername"
                label="SMTP Username"
                :disabled="saving"
              />
              <UiInput
                v-model="emailSettings.smtpPassword"
                label="SMTP Password"
                type="password"
                :disabled="saving"
              />
              <UiInput
                v-model="emailSettings.fromEmail"
                label="From Email"
                type="email"
                placeholder="<EMAIL>"
                :disabled="saving"
              />
              <UiInput
                v-model="emailSettings.fromName"
                label="From Name"
                placeholder="Legal SaaS Platform"
                :disabled="saving"
              />
            </div>
          </div>

          <div>
            <h4 class="text-md font-medium text-gray-900 dark:text-white mb-3">Email Features</h4>
            <div class="space-y-3">
              <UiCheckbox
                v-model="emailSettings.enableSsl"
                label="Enable SSL/TLS"
                description="Use secure connection for email delivery"
                :disabled="saving"
              />
              <UiCheckbox
                v-model="emailSettings.enableTracking"
                label="Enable Email Tracking"
                description="Track email opens and clicks"
                :disabled="saving"
              />
              <UiCheckbox
                v-model="emailSettings.enableQueue"
                label="Enable Email Queue"
                description="Queue emails for batch processing"
                :disabled="saving"
              />
              <UiCheckbox
                v-model="emailSettings.enableRetry"
                label="Enable Retry on Failure"
                description="Automatically retry failed email deliveries"
                :disabled="saving"
              />
            </div>
          </div>

          <div class="flex items-center gap-4">
            <UiButton @click="testEmailConnection" variant="outline" :loading="testingEmail">
              <Icon name="material-symbols:wifi" class="h-4 w-4 mr-2" />
              Test Connection
            </UiButton>
            <UiButton @click="sendTestEmail" variant="outline" :loading="sendingTestEmail">
              <Icon name="material-symbols:email" class="h-4 w-4 mr-2" />
              Send Test Email
            </UiButton>
          </div>
        </div>

        <!-- SMS Settings -->
        <div v-if="activeTab === 'sms'" class="space-y-6">
          <div>
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">SMS Configuration</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <UiSelect
                v-model="smsSettings.provider"
                label="SMS Provider"
                :options="smsProviders"
                :disabled="saving"
              />
              <UiInput
                v-model="smsSettings.apiKey"
                label="API Key"
                type="password"
                :disabled="saving"
              />
              <UiInput
                v-model="smsSettings.apiSecret"
                label="API Secret"
                type="password"
                :disabled="saving"
              />
              <UiInput
                v-model="smsSettings.fromNumber"
                label="From Number"
                placeholder="+**********"
                :disabled="saving"
              />
              <UiInput
                v-model="smsSettings.webhookUrl"
                label="Webhook URL"
                placeholder="https://example.com/webhook"
                :disabled="saving"
              />
              <UiSelect
                v-model="smsSettings.region"
                label="Region"
                :options="regionOptions"
                :disabled="saving"
              />
            </div>
          </div>

          <div>
            <h4 class="text-md font-medium text-gray-900 dark:text-white mb-3">SMS Features</h4>
            <div class="space-y-3">
              <UiCheckbox
                v-model="smsSettings.enableDeliveryReports"
                label="Enable Delivery Reports"
                description="Receive delivery status updates"
                :disabled="saving"
              />
              <UiCheckbox
                v-model="smsSettings.enableUnicode"
                label="Enable Unicode Support"
                description="Support international characters"
                :disabled="saving"
              />
              <UiCheckbox
                v-model="smsSettings.enableShortUrls"
                label="Enable Short URLs"
                description="Automatically shorten URLs in SMS"
                :disabled="saving"
              />
              <UiCheckbox
                v-model="smsSettings.enableOptOut"
                label="Enable Opt-out Keywords"
                description="Allow users to opt-out with keywords like STOP"
                :disabled="saving"
              />
            </div>
          </div>

          <div class="flex items-center gap-4">
            <UiButton @click="testSmsConnection" variant="outline" :loading="testingSms">
              <Icon name="material-symbols:wifi" class="h-4 w-4 mr-2" />
              Test Connection
            </UiButton>
            <UiButton @click="sendTestSms" variant="outline" :loading="sendingTestSms">
              <Icon name="material-symbols:sms" class="h-4 w-4 mr-2" />
              Send Test SMS
            </UiButton>
          </div>
        </div>

        <!-- Push Notifications -->
        <div v-if="activeTab === 'push'" class="space-y-6">
          <div>
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Push Notification Configuration</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <UiInput
                v-model="pushSettings.fcmServerKey"
                label="FCM Server Key"
                type="password"
                :disabled="saving"
              />
              <UiInput
                v-model="pushSettings.fcmSenderId"
                label="FCM Sender ID"
                :disabled="saving"
              />
              <UiInput
                v-model="pushSettings.apnsKeyId"
                label="APNs Key ID"
                :disabled="saving"
              />
              <UiInput
                v-model="pushSettings.apnsTeamId"
                label="APNs Team ID"
                :disabled="saving"
              />
              <UiInput
                v-model="pushSettings.apnsBundleId"
                label="APNs Bundle ID"
                :disabled="saving"
              />
              <UiSelect
                v-model="pushSettings.environment"
                label="Environment"
                :options="environmentOptions"
                :disabled="saving"
              />
            </div>
          </div>

          <div>
            <h4 class="text-md font-medium text-gray-900 dark:text-white mb-3">Push Features</h4>
            <div class="space-y-3">
              <UiCheckbox
                v-model="pushSettings.enableBadgeCount"
                label="Enable Badge Count"
                description="Update app icon badge with notification count"
                :disabled="saving"
              />
              <UiCheckbox
                v-model="pushSettings.enableSound"
                label="Enable Sound"
                description="Play notification sound"
                :disabled="saving"
              />
              <UiCheckbox
                v-model="pushSettings.enableVibration"
                label="Enable Vibration"
                description="Vibrate device on notification"
                :disabled="saving"
              />
              <UiCheckbox
                v-model="pushSettings.enableRichMedia"
                label="Enable Rich Media"
                description="Support images and videos in notifications"
                :disabled="saving"
              />
            </div>
          </div>

          <div class="flex items-center gap-4">
            <UiButton @click="testPushConnection" variant="outline" :loading="testingPush">
              <Icon name="material-symbols:wifi" class="h-4 w-4 mr-2" />
              Test Connection
            </UiButton>
            <UiButton @click="sendTestPush" variant="outline" :loading="sendingTestPush">
              <Icon name="material-symbols:notifications" class="h-4 w-4 mr-2" />
              Send Test Push
            </UiButton>
          </div>
        </div>

        <!-- General Settings -->
        <div v-if="activeTab === 'general'" class="space-y-6">
          <div>
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">General Notification Settings</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <UiInput
                v-model="generalSettings.maxRetries"
                label="Max Retry Attempts"
                type="number"
                :disabled="saving"
              />
              <UiInput
                v-model="generalSettings.retryDelay"
                label="Retry Delay (minutes)"
                type="number"
                :disabled="saving"
              />
              <UiInput
                v-model="generalSettings.batchSize"
                label="Batch Size"
                type="number"
                :disabled="saving"
              />
              <UiInput
                v-model="generalSettings.rateLimit"
                label="Rate Limit (per minute)"
                type="number"
                :disabled="saving"
              />
              <UiSelect
                v-model="generalSettings.defaultChannel"
                label="Default Channel"
                :options="channelOptions"
                :disabled="saving"
              />
              <UiSelect
                v-model="generalSettings.timezone"
                label="Default Timezone"
                :options="timezoneOptions"
                :disabled="saving"
              />
            </div>
          </div>

          <div>
            <h4 class="text-md font-medium text-gray-900 dark:text-white mb-3">Global Features</h4>
            <div class="space-y-3">
              <UiCheckbox
                v-model="generalSettings.enableLogging"
                label="Enable Detailed Logging"
                description="Log all notification activities"
                :disabled="saving"
              />
              <UiCheckbox
                v-model="generalSettings.enableAnalytics"
                label="Enable Analytics"
                description="Track notification performance metrics"
                :disabled="saving"
              />
              <UiCheckbox
                v-model="generalSettings.enableQuietHours"
                label="Enable Quiet Hours"
                description="Respect user quiet hours preferences"
                :disabled="saving"
              />
              <UiCheckbox
                v-model="generalSettings.enableUnsubscribe"
                label="Enable Unsubscribe Links"
                description="Include unsubscribe links in notifications"
                :disabled="saving"
              />
            </div>
          </div>
        </div>

        <!-- Save Actions -->
        <div class="flex items-center justify-between pt-6 border-t border-gray-200 dark:border-gray-700">
          <div class="flex items-center space-x-4">
            <UiButton @click="exportSettings" variant="outline" :disabled="saving">
              <Icon name="material-symbols:download" class="h-4 w-4 mr-2" />
              Export Settings
            </UiButton>
            <UiButton @click="importSettings" variant="outline" :disabled="saving">
              <Icon name="material-symbols:upload" class="h-4 w-4 mr-2" />
              Import Settings
            </UiButton>
          </div>
          <div class="flex items-center space-x-4">
            <span v-if="lastSaved" class="text-sm text-gray-500 dark:text-gray-400">
              Last saved: {{ formatTime(lastSaved) }}
            </span>
            <UiButton @click="saveSettings" :loading="saving">
              <Icon name="material-symbols:save" class="h-4 w-4 mr-2" />
              Save Settings
            </UiButton>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { PlatformRoles } from '~/app/features/auth/constants/roles'

// Page meta configuration
definePageMeta({
  layout: 'dashboard',
  title: 'Notification Settings',
  description: 'Configure notification preferences, channels, and delivery settings',
  pageHeaderIcon: 'material-symbols:settings',
  pageHeaderStats: [
    { key: 'channels', label: 'Active Channels', value: '3', color: 'purple' },
    { key: 'templates', label: 'Templates', value: '28', color: 'blue' },
    { key: 'delivery', label: 'Delivery Rate', value: '98.5%', color: 'green' },
    { key: 'settings', label: 'Configured', value: '15', color: 'yellow' }
  ],
  showRealTimeStatus: false,
  autoRefreshEnabled: false,
  middleware: ['rbac'],
  roles: [PlatformRoles.SUPER_ADMIN],
  breadcrumb: [
    { label: 'Dashboard', href: '/dashboard' },
    { label: 'Settings', href: '/dashboard/settings' },
    { label: 'Notifications', href: '/dashboard/settings/notifications' },
    { label: 'Settings' },
  ],
})

// Reactive state
const activeTab = ref('email')
const saving = ref(false)
const testingEmail = ref(false)
const sendingTestEmail = ref(false)
const testingSms = ref(false)
const sendingTestSms = ref(false)
const testingPush = ref(false)
const sendingTestPush = ref(false)
const lastSaved = ref<Date | null>(null)

// Settings tabs
const settingsTabs = [
  { id: 'email', label: 'Email', icon: 'material-symbols:email' },
  { id: 'sms', label: 'SMS', icon: 'material-symbols:sms' },
  { id: 'push', label: 'Push', icon: 'material-symbols:notifications' },
  { id: 'general', label: 'General', icon: 'material-symbols:settings' }
]

// Email settings
const emailSettings = reactive({
  smtpHost: 'smtp.example.com',
  smtpPort: 587,
  smtpUsername: '',
  smtpPassword: '',
  fromEmail: '<EMAIL>',
  fromName: 'Legal SaaS Platform',
  enableSsl: true,
  enableTracking: false,
  enableQueue: true,
  enableRetry: true
})

// SMS settings
const smsSettings = reactive({
  provider: 'twilio',
  apiKey: '',
  apiSecret: '',
  fromNumber: '',
  webhookUrl: '',
  region: 'us-east-1',
  enableDeliveryReports: true,
  enableUnicode: true,
  enableShortUrls: false,
  enableOptOut: true
})

// Push notification settings
const pushSettings = reactive({
  fcmServerKey: '',
  fcmSenderId: '',
  apnsKeyId: '',
  apnsTeamId: '',
  apnsBundleId: '',
  environment: 'production',
  enableBadgeCount: true,
  enableSound: true,
  enableVibration: true,
  enableRichMedia: false
})

// General settings
const generalSettings = reactive({
  maxRetries: 3,
  retryDelay: 5,
  batchSize: 100,
  rateLimit: 1000,
  defaultChannel: 'email',
  timezone: 'UTC',
  enableLogging: true,
  enableAnalytics: true,
  enableQuietHours: true,
  enableUnsubscribe: true
})

// Options for select fields
const smsProviders = [
  { value: 'twilio', label: 'Twilio' },
  { value: 'aws-sns', label: 'AWS SNS' },
  { value: 'nexmo', label: 'Nexmo' },
  { value: 'messagebird', label: 'MessageBird' }
]

const regionOptions = [
  { value: 'us-east-1', label: 'US East (N. Virginia)' },
  { value: 'us-west-2', label: 'US West (Oregon)' },
  { value: 'eu-west-1', label: 'Europe (Ireland)' },
  { value: 'ap-southeast-1', label: 'Asia Pacific (Singapore)' }
]

const environmentOptions = [
  { value: 'development', label: 'Development' },
  { value: 'staging', label: 'Staging' },
  { value: 'production', label: 'Production' }
]

const channelOptions = [
  { value: 'email', label: 'Email' },
  { value: 'sms', label: 'SMS' },
  { value: 'push', label: 'Push Notification' }
]

const timezoneOptions = [
  { value: 'UTC', label: 'UTC' },
  { value: 'America/New_York', label: 'Eastern Time' },
  { value: 'America/Chicago', label: 'Central Time' },
  { value: 'America/Denver', label: 'Mountain Time' },
  { value: 'America/Los_Angeles', label: 'Pacific Time' },
  { value: 'Europe/London', label: 'London' },
  { value: 'Europe/Paris', label: 'Paris' },
  { value: 'Asia/Tokyo', label: 'Tokyo' }
]

// Utility functions
const formatTime = (date: Date) => {
  return date.toLocaleString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// Methods
const saveSettings = async () => {
  try {
    saving.value = true

    // Simulate API call to save settings
    await new Promise(resolve => setTimeout(resolve, 2000))

    // In a real app, you would send the settings to the API
    // await $api.put('/notifications/settings', {
    //   email: emailSettings,
    //   sms: smsSettings,
    //   push: pushSettings,
    //   general: generalSettings
    // })

    lastSaved.value = new Date()
    console.log('Notification settings saved successfully')

    // Show success notification
    // useToast().success('Settings saved successfully')

  } catch (error) {
    console.error('Error saving settings:', error)
    // useToast().error('Failed to save settings')
  } finally {
    saving.value = false
  }
}

const testEmailConnection = async () => {
  try {
    testingEmail.value = true

    // Simulate testing email connection
    await new Promise(resolve => setTimeout(resolve, 2000))

    // In a real app, you would test the SMTP connection
    // await $api.post('/notifications/email/test-connection', emailSettings)

    console.log('Email connection test successful')
    alert('Email connection test successful!')

  } catch (error) {
    console.error('Email connection test failed:', error)
    alert('Email connection test failed!')
  } finally {
    testingEmail.value = false
  }
}

const sendTestEmail = async () => {
  try {
    sendingTestEmail.value = true

    const recipient = prompt('Enter email address for test:')
    if (!recipient) return

    // Simulate sending test email
    await new Promise(resolve => setTimeout(resolve, 2000))

    // In a real app, you would send a test email
    // await $api.post('/notifications/email/send-test', { recipient, settings: emailSettings })

    console.log('Test email sent successfully')
    alert(`Test email sent successfully to ${recipient}!`)

  } catch (error) {
    console.error('Failed to send test email:', error)
    alert('Failed to send test email!')
  } finally {
    sendingTestEmail.value = false
  }
}

const testSmsConnection = async () => {
  try {
    testingSms.value = true

    // Simulate testing SMS connection
    await new Promise(resolve => setTimeout(resolve, 2000))

    // In a real app, you would test the SMS provider connection
    // await $api.post('/notifications/sms/test-connection', smsSettings)

    console.log('SMS connection test successful')
    alert('SMS connection test successful!')

  } catch (error) {
    console.error('SMS connection test failed:', error)
    alert('SMS connection test failed!')
  } finally {
    testingSms.value = false
  }
}

const sendTestSms = async () => {
  try {
    sendingTestSms.value = true

    const recipient = prompt('Enter phone number for test (with country code):')
    if (!recipient) return

    // Simulate sending test SMS
    await new Promise(resolve => setTimeout(resolve, 2000))

    // In a real app, you would send a test SMS
    // await $api.post('/notifications/sms/send-test', { recipient, settings: smsSettings })

    console.log('Test SMS sent successfully')
    alert(`Test SMS sent successfully to ${recipient}!`)

  } catch (error) {
    console.error('Failed to send test SMS:', error)
    alert('Failed to send test SMS!')
  } finally {
    sendingTestSms.value = false
  }
}

const testPushConnection = async () => {
  try {
    testingPush.value = true

    // Simulate testing push notification connection
    await new Promise(resolve => setTimeout(resolve, 2000))

    // In a real app, you would test the push notification service
    // await $api.post('/notifications/push/test-connection', pushSettings)

    console.log('Push notification connection test successful')
    alert('Push notification connection test successful!')

  } catch (error) {
    console.error('Push notification connection test failed:', error)
    alert('Push notification connection test failed!')
  } finally {
    testingPush.value = false
  }
}

const sendTestPush = async () => {
  try {
    sendingTestPush.value = true

    const deviceToken = prompt('Enter device token for test:')
    if (!deviceToken) return

    // Simulate sending test push notification
    await new Promise(resolve => setTimeout(resolve, 2000))

    // In a real app, you would send a test push notification
    // await $api.post('/notifications/push/send-test', { deviceToken, settings: pushSettings })

    console.log('Test push notification sent successfully')
    alert(`Test push notification sent successfully to device!`)

  } catch (error) {
    console.error('Failed to send test push notification:', error)
    alert('Failed to send test push notification!')
  } finally {
    sendingTestPush.value = false
  }
}

const testNotifications = async () => {
  try {
    console.log('Testing all notification channels...')

    const email = prompt('Enter email for testing:')
    const phone = prompt('Enter phone number for testing:')

    if (email || phone) {
      // In a real app, you would test all configured channels
      // await $api.post('/notifications/test-all', { email, phone })

      alert('Test notifications sent to all configured channels!')
    }

  } catch (error) {
    console.error('Error testing notifications:', error)
    alert('Failed to test notifications!')
  }
}

const resetToDefaults = async () => {
  const confirmed = confirm('Are you sure you want to reset all settings to defaults? This action cannot be undone.')
  if (!confirmed) return

  try {
    // Reset all settings to defaults
    Object.assign(emailSettings, {
      smtpHost: 'smtp.example.com',
      smtpPort: 587,
      smtpUsername: '',
      smtpPassword: '',
      fromEmail: '<EMAIL>',
      fromName: 'Legal SaaS Platform',
      enableSsl: true,
      enableTracking: false,
      enableQueue: true,
      enableRetry: true
    })

    Object.assign(smsSettings, {
      provider: 'twilio',
      apiKey: '',
      apiSecret: '',
      fromNumber: '',
      webhookUrl: '',
      region: 'us-east-1',
      enableDeliveryReports: true,
      enableUnicode: true,
      enableShortUrls: false,
      enableOptOut: true
    })

    Object.assign(pushSettings, {
      fcmServerKey: '',
      fcmSenderId: '',
      apnsKeyId: '',
      apnsTeamId: '',
      apnsBundleId: '',
      environment: 'production',
      enableBadgeCount: true,
      enableSound: true,
      enableVibration: true,
      enableRichMedia: false
    })

    Object.assign(generalSettings, {
      maxRetries: 3,
      retryDelay: 5,
      batchSize: 100,
      rateLimit: 1000,
      defaultChannel: 'email',
      timezone: 'UTC',
      enableLogging: true,
      enableAnalytics: true,
      enableQuietHours: true,
      enableUnsubscribe: true
    })

    console.log('Settings reset to defaults')
    alert('Settings have been reset to defaults')

  } catch (error) {
    console.error('Error resetting settings:', error)
    alert('Failed to reset settings')
  }
}

const exportSettings = () => {
  try {
    const settings = {
      email: emailSettings,
      sms: smsSettings,
      push: pushSettings,
      general: generalSettings,
      exportedAt: new Date().toISOString()
    }

    const jsonContent = JSON.stringify(settings, null, 2)
    const blob = new Blob([jsonContent], { type: 'application/json' })
    const url = URL.createObjectURL(blob)

    const link = document.createElement('a')
    link.href = url
    link.download = `notification-settings-${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    URL.revokeObjectURL(url)
    console.log('Settings exported successfully')

  } catch (error) {
    console.error('Error exporting settings:', error)
    alert('Failed to export settings')
  }
}

const importSettings = () => {
  try {
    const input = document.createElement('input')
    input.type = 'file'
    input.accept = '.json'

    input.onchange = async (event: any) => {
      const file = event.target.files[0]
      if (!file) return

      try {
        const text = await file.text()
        const settings = JSON.parse(text)

        if (settings.email) Object.assign(emailSettings, settings.email)
        if (settings.sms) Object.assign(smsSettings, settings.sms)
        if (settings.push) Object.assign(pushSettings, settings.push)
        if (settings.general) Object.assign(generalSettings, settings.general)

        console.log('Settings imported successfully')
        alert('Settings imported successfully!')

      } catch (error) {
        console.error('Error importing settings:', error)
        alert('Failed to import settings. Please check the file format.')
      }
    }

    input.click()

  } catch (error) {
    console.error('Error importing settings:', error)
    alert('Failed to import settings')
  }
}

// Lifecycle hooks
onMounted(async () => {
  // Load current settings from API
  // const currentSettings = await $api.get('/notifications/settings')
  // Object.assign(emailSettings, currentSettings.email)
  // Object.assign(smsSettings, currentSettings.sms)
  // Object.assign(pushSettings, currentSettings.push)
  // Object.assign(generalSettings, currentSettings.general)

  console.log('Notification settings loaded')
})
</script>

<style scoped>
/* Tab navigation styles */
.border-purple-500 {
  border-color: rgb(168 85 247);
}

.text-purple-600 {
  color: rgb(147 51 234);
}

/* Form field spacing */
.space-y-6 > * + * {
  margin-top: 1.5rem;
}

.space-y-3 > * + * {
  margin-top: 0.75rem;
}

/* Grid responsive adjustments */
@media (max-width: 768px) {
  .grid-cols-1.md\:grid-cols-2 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
}

/* Transition effects */
.transition-colors {
  transition-property: color, background-color, border-color;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 200ms;
}

/* Focus states for accessibility */
button:focus,
input:focus,
select:focus {
  outline: 2px solid rgb(168 85 247);
  outline-offset: 2px;
}

/* Loading state styles */
.opacity-50 {
  opacity: 0.5;
}

/* Hover effects */
button:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
  .border-gray-200 {
    border-color: rgb(55 65 81);
  }

  .text-gray-500 {
    color: rgb(156 163 175);
  }

  .bg-gray-50 {
    background-color: rgb(55 65 81);
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
</style>
