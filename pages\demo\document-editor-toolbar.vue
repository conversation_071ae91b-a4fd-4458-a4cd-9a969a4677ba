<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <div class="container mx-auto py-8">
      <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">
          {{ t('documents.documentEditor') }} - {{ t('documents.editor.toolbar.toolbar') }}
        </h1>
        <p class="text-gray-600 dark:text-gray-400">
          Demo of the enhanced document editor toolbar with translation support
        </p>
      </div>

      <!-- Language Switcher -->
      <div class="mb-6">
        <div class="flex items-center gap-4">
          <span class="text-sm font-medium text-gray-700 dark:text-gray-300">
            {{ t('common.language') }}:
          </span>
          <UiButton
            v-for="locale in availableLocales"
            :key="locale.code"
            @click="setLocale(locale.code)"
            :variant="currentLocale === locale.code ? 'contained' : 'outline'"
            size="sm"
          >
            {{ locale.flag }} {{ locale.nativeName }}
          </UiButton>
        </div>
      </div>

      <!-- Complete Document Editor -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden h-[800px]">
        <DocumentEditor
          v-model="documentContent"
          :editable="true"
          :document-title="documentTitle"
          :document-subtitle="documentSubtitle"
          :show-header="true"
          :show-footer="true"
          :show-zoom-controls="true"
          :show-scroll-indicators="true"
          :initial-zoom="zoomLevel"
          @zoom-change="handleZoomChange"
          @view-change="handleViewChange"
          @language-change="handleLanguageChange"
        />
      </div>

      <!-- Demo Content -->
      <div class="mt-8 grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            {{ t('documents.editor.toolbar.tabs.home') }} Tab Features
          </h3>
          <ul class="space-y-2 text-sm text-gray-600 dark:text-gray-400">
            <li>• {{ t('documents.editor.toolbar.groups.clipboard') }}: {{ t('documents.editor.toolbar.paste') }}, {{ t('documents.editor.toolbar.cut') }}, {{ t('documents.editor.toolbar.copy') }}</li>
            <li>• {{ t('documents.editor.toolbar.groups.font') }}: Font family, size, {{ t('documents.editor.toolbar.bold') }}, {{ t('documents.editor.toolbar.italic') }}, {{ t('documents.editor.toolbar.underline') }}</li>
            <li>• {{ t('documents.editor.toolbar.groups.paragraph') }}: Text alignment, lists</li>
            <li>• {{ t('documents.editor.toolbar.groups.styles') }}: Heading styles</li>
          </ul>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Other Tabs
          </h3>
          <ul class="space-y-2 text-sm text-gray-600 dark:text-gray-400">
            <li>• <strong>{{ t('documents.editor.toolbar.tabs.insert') }}:</strong> {{ t('documents.editor.toolbar.link') }}, {{ t('documents.editor.toolbar.image') }}, {{ t('documents.editor.toolbar.video') }}, {{ t('documents.editor.toolbar.audio') }}</li>
            <li>• <strong>{{ t('documents.editor.toolbar.tabs.table') }}:</strong> {{ t('documents.editor.toolbar.insertTable') }}, row operations</li>
            <li>• <strong>{{ t('documents.editor.toolbar.tabs.tools') }}:</strong> {{ t('documents.editor.toolbar.search') }}, {{ t('documents.editor.toolbar.translate') }}</li>
            <li>• <strong>{{ t('documents.editor.toolbar.tabs.page') }}:</strong> {{ t('documents.editor.toolbar.pageBreak') }}, {{ t('documents.editor.toolbar.lineBreak') }}</li>
            <li>• <strong>{{ t('documents.editor.toolbar.tabs.export') }}:</strong> {{ t('documents.editor.toolbar.exportPdf') }}, {{ t('documents.editor.toolbar.exportWord') }}, {{ t('documents.editor.toolbar.share') }}</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Editor } from '@tiptap/vue-3'
import StarterKit from '@tiptap/starter-kit'
import TextAlign from '@tiptap/extension-text-align'
import {TextStyle} from '@tiptap/extension-text-style'
import Color from '@tiptap/extension-color'
import FontFamily from '@tiptap/extension-font-family'
import Underline from '@tiptap/extension-underline'
import DocumentEditor from '~/components/editors/document-editor/DocumentEditor.vue'

definePageMeta({
  layout: 'canvas'
})
// Composables
const { t, locale, setLocale } = useI18n()

// Available locales
const availableLocales = [
  { code: 'en', flag: '🇺🇸', nativeName: 'English' },
  { code: 'he', flag: '🇮🇱', nativeName: 'עברית' },
  { code: 'ar', flag: '🇸🇦', nativeName: 'العربية' }
]

// RTL support
const rtlLanguages = ['he', 'ar']
const isRTL = computed(() => rtlLanguages.includes(locale.value))
const currentLocale = computed(() => locale.value)

// Document state
const documentContent = ref(`
  <h1>Welcome to the Enhanced Document Editor</h1>
  <p>This is a demonstration of the new document editor with zoom functionality and clean design. The editor includes:</p>
  <ul>
    <li><strong>Professional Layout:</strong> Clean document interface with proper margins and typography</li>
    <li><strong>Zoom Controls:</strong> Built-in zoom in/out functionality with visual controls</li>
    <li><strong>Multi-language Support:</strong> Full RTL support for Hebrew and Arabic</li>
    <li><strong>Responsive Design:</strong> Adapts to different screen sizes</li>
    <li><strong>Print-ready:</strong> Optimized for both screen and print viewing</li>
  </ul>
  <p>Try switching languages to see the interface adapt in real-time!</p>
  <p>Use the zoom controls to adjust the document size for comfortable viewing.</p>
`)
const documentTitle = ref('Legal Document Editor Demo')
const documentSubtitle = ref('Professional Document Editing Experience')
const zoomLevel = ref(100)

// Event handlers
const handleZoomChange = (zoom: number) => {
  console.log('Zoom changed to:', zoom)
  zoomLevel.value = zoom
}

const handleViewChange = (view: string) => {
  console.log('View changed to:', view)
}

const handleLanguageChange = (language: string) => {
  console.log('Language changed to:', language)
  setLocale(language)
}

 
</script>

<style scoped>
@reference '~/assets/css/tailwind.css';
.prose {
  @apply text-gray-900 dark:text-gray-100;
}

.prose h1,
.prose h2,
.prose h3,
.prose h4,
.prose h5,
.prose h6 {
  @apply text-gray-900 dark:text-white;
}

.prose strong {
  @apply text-gray-900 dark:text-white;
}

.prose ul li,
.prose ol li {
  @apply text-gray-700 dark:text-gray-300;
}
</style>
