<template>
  <div class="log-channel-editor space-y-6">
    <!-- Log Configuration -->
    <div class="space-y-4">
      <div class="flex items-center justify-between">
        <h3 class="text-lg font-medium text-gray-900 dark:text-white">Log Template</h3>
        <div class="flex items-center space-x-2">
          <UiButton
            @click="showTemplateLibrary = true"
            variant="outline"
            size="sm"
          >
            <Icon name="heroicons:book-open" class="w-4 h-4 mr-2" />
            Templates
          </UiButton>
          <UiButton
            @click="showVariableModal = true"
            variant="outline"
            size="sm"
          >
            <Icon name="heroicons:variable" class="w-4 h-4 mr-2" />
            Variables
          </UiButton>
        </div>
      </div>

      <!-- Log Level -->
      <div class="space-y-2">
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
          Log Level
        </label>
        <select
          v-model="localContent.level"
          @change="handleContentChange"
          class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-gray-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm"
        >
          <option value="debug">Debug</option>
          <option value="info">Info</option>
          <option value="warn">Warning</option>
          <option value="error">Error</option>
          <option value="fatal">Fatal</option>
        </select>
      </div>

      <!-- Log Message -->
      <div class="space-y-2">
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
          Log Message
        </label>
        <UiTextarea
          v-model="localContent.message"
          placeholder="Log message template..."
          :rows="4"
          @input="handleContentChange"
        />
      </div>

      <!-- Log Format -->
      <div class="space-y-2">
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
          Log Format
        </label>
        <select
          v-model="localContent.format"
          @change="handleContentChange"
          class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-gray-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm"
        >
          <option value="json">JSON</option>
          <option value="text">Plain Text</option>
          <option value="structured">Structured</option>
          <option value="syslog">Syslog</option>
        </select>
      </div>

      <!-- Data Fields -->
      <div class="space-y-4">
        <div class="flex items-center justify-between">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Data Fields
          </label>
          <UiButton
            @click="addDataField"
            variant="outline"
            size="sm"
          >
            <Icon name="heroicons:plus" class="w-4 h-4 mr-1" />
            Add Field
          </UiButton>
        </div>
        
        <div v-if="localContent.dataFields.length > 0" class="space-y-3">
          <div
            v-for="(field, index) in localContent.dataFields"
            :key="index"
            class="flex items-center space-x-3 p-3 border border-gray-200 dark:border-gray-700 rounded-lg"
          >
            <div class="flex-1 grid grid-cols-1 md:grid-cols-3 gap-3">
              <UiInput
                v-model="field.key"
                placeholder="Field name"
                @input="handleContentChange"
              />
              <select
                v-model="field.type"
                @change="handleContentChange"
                class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-gray-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm"
              >
                <option value="string">String</option>
                <option value="number">Number</option>
                <option value="boolean">Boolean</option>
                <option value="object">Object</option>
                <option value="array">Array</option>
              </select>
              <UiInput
                v-model="field.value"
                placeholder="Field value or variable"
                @input="handleContentChange"
              />
            </div>
            <UiButton
              @click="removeDataField(index)"
              variant="ghost"
              size="sm"
              class="text-red-600 hover:text-red-700"
            >
              <Icon name="heroicons:trash" class="w-4 h-4" />
            </UiButton>
          </div>
        </div>
        
        <div v-else class="text-sm text-gray-500 dark:text-gray-400 text-center py-4 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg">
          No data fields added. Click "Add Field" to include structured data in your logs.
        </div>
      </div>
    </div>

    <!-- Log Preview -->
    <UiCard class="p-4">
      <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-4">Log Entry Preview</h4>
      
      <!-- Format Tabs -->
      <div class="flex space-x-1 mb-4">
        <button
          v-for="format in ['json', 'text', 'structured', 'syslog']"
          :key="format"
          @click="previewFormat = format"
          :class="[
            'px-3 py-1 text-xs font-medium rounded-md transition-colors',
            previewFormat === format
              ? 'bg-gray-600 text-white'
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600'
          ]"
        >
          {{ format.toUpperCase() }}
        </button>
      </div>

      <!-- Log Entry Display -->
      <div class="bg-gray-900 rounded-lg p-4 font-mono text-sm overflow-x-auto">
        <div v-if="previewFormat === 'json'" class="text-green-400">
          <pre>{{ formattedLogEntry.json }}</pre>
        </div>
        <div v-else-if="previewFormat === 'text'" class="text-gray-300">
          <pre>{{ formattedLogEntry.text }}</pre>
        </div>
        <div v-else-if="previewFormat === 'structured'" class="text-blue-400">
          <pre>{{ formattedLogEntry.structured }}</pre>
        </div>
        <div v-else-if="previewFormat === 'syslog'" class="text-yellow-400">
          <pre>{{ formattedLogEntry.syslog }}</pre>
        </div>
      </div>
    </UiCard>

    <!-- Log Settings -->
    <UiCard class="p-4">
      <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-4">Log Settings</h4>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Log Category
          </label>
          <UiInput
            v-model="localContent.settings.category"
            placeholder="e.g., user-actions, system-events"
            @input="handleContentChange"
          />
        </div>
        
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Source
          </label>
          <UiInput
            v-model="localContent.settings.source"
            placeholder="e.g., web-app, api, background-job"
            @input="handleContentChange"
          />
        </div>
      </div>
      
      <div class="mt-4 space-y-3">
        <label class="flex items-center">
          <input
            type="checkbox"
            v-model="localContent.settings.includeTimestamp"
            @change="handleContentChange"
            class="rounded border-gray-300 text-gray-600 focus:ring-gray-500"
          />
          <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Include timestamp</span>
        </label>
        
        <label class="flex items-center">
          <input
            type="checkbox"
            v-model="localContent.settings.includeUserId"
            @change="handleContentChange"
            class="rounded border-gray-300 text-gray-600 focus:ring-gray-500"
          />
          <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Include user ID</span>
        </label>
        
        <label class="flex items-center">
          <input
            type="checkbox"
            v-model="localContent.settings.includeRequestId"
            @change="handleContentChange"
            class="rounded border-gray-300 text-gray-600 focus:ring-gray-500"
          />
          <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Include request ID</span>
        </label>
        
        <label class="flex items-center">
          <input
            type="checkbox"
            v-model="localContent.settings.enableStackTrace"
            @change="handleContentChange"
            class="rounded border-gray-300 text-gray-600 focus:ring-gray-500"
          />
          <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Enable stack trace (for errors)</span>
        </label>
      </div>
    </UiCard>

    <!-- Log Best Practices -->
    <UiCard class="p-4">
      <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-3">Logging Best Practices</h4>
      <div class="space-y-2 text-sm text-gray-600 dark:text-gray-400">
        <div class="flex items-start space-x-2">
          <Icon name="heroicons:check-circle" class="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
          <span>Use appropriate log levels (debug for development, info for general events)</span>
        </div>
        <div class="flex items-start space-x-2">
          <Icon name="heroicons:check-circle" class="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
          <span>Include relevant context data for debugging</span>
        </div>
        <div class="flex items-start space-x-2">
          <Icon name="heroicons:check-circle" class="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
          <span>Avoid logging sensitive information (passwords, tokens)</span>
        </div>
        <div class="flex items-start space-x-2">
          <Icon name="heroicons:check-circle" class="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
          <span>Use structured logging for better searchability</span>
        </div>
      </div>
    </UiCard>

    <!-- Template Library Modal -->
    <LogTemplateLibraryModal
      v-if="showTemplateLibrary"
      @close="showTemplateLibrary = false"
      @select="insertTemplate"
    />

    <!-- Variable Insert Modal -->
    <VariableInsertModal
      v-if="showVariableModal"
      @close="showVariableModal = false"
      @insert="insertVariable"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, defineAsyncComponent } from 'vue'

// Lazy load modals
const LogTemplateLibraryModal = defineAsyncComponent(() => import('../../template-modals/LogTemplateLibraryModal.vue'))
const VariableInsertModal = defineAsyncComponent(() => import('../../template-modals/VariableInsertModal.vue'))

// Props
interface Props {
  content: any
  variables: string[]
  templateData: any
  category?: any
}

const props = withDefaults(defineProps<Props>(), {
  content: () => ({}),
  variables: () => [],
})

// Emits
const emit = defineEmits<{
  'update:content': [content: any]
  'update:variables': [variables: string[]]
  'content-change': [content: any, variables: string[]]
}>()

// State
const showTemplateLibrary = ref(false)
const showVariableModal = ref(false)
const previewFormat = ref('json')

// Local content with defaults
const localContent = ref({
  level: 'info',
  message: '',
  format: 'json',
  dataFields: [],
  settings: {
    category: '',
    source: '',
    includeTimestamp: true,
    includeUserId: true,
    includeRequestId: true,
    enableStackTrace: false,
  },
  ...props.content
})

const localVariables = ref([...props.variables])

// Computed
const renderedMessage = computed(() => {
  let message = localContent.value.message || ''
  localVariables.value.forEach(variable => {
    const sampleValue = getSampleValue(variable)
    message = message.replace(new RegExp(`{${variable}}`, 'g'), sampleValue)
  })
  return message
})

const formattedLogEntry = computed(() => {
  const timestamp = new Date().toISOString()
  const baseData = {
    timestamp: localContent.value.settings.includeTimestamp ? timestamp : undefined,
    level: localContent.value.level,
    message: renderedMessage.value || 'Log message will appear here',
    category: localContent.value.settings.category || 'general',
    source: localContent.value.settings.source || 'application',
    userId: localContent.value.settings.includeUserId ? 'user-123' : undefined,
    requestId: localContent.value.settings.includeRequestId ? 'req-abc-123' : undefined,
  }

  // Add custom data fields
  const customData: Record<string, any> = {}
  localContent.value.dataFields.forEach((field: any) => {
    if (field.key && field.value) {
      let value = field.value
      // Replace variables in field values
      localVariables.value.forEach(variable => {
        const sampleValue = getSampleValue(variable)
        value = value.replace(new RegExp(`{${variable}}`, 'g'), sampleValue)
      })
      
      // Convert value based on type
      switch (field.type) {
        case 'number':
          customData[field.key] = parseFloat(value) || 0
          break
        case 'boolean':
          customData[field.key] = value.toLowerCase() === 'true'
          break
        case 'object':
          try {
            customData[field.key] = JSON.parse(value)
          } catch {
            customData[field.key] = value
          }
          break
        case 'array':
          customData[field.key] = value.split(',').map((v: string) => v.trim())
          break
        default:
          customData[field.key] = value
      }
    }
  })

  const logData = { ...baseData, ...customData }

  return {
    json: JSON.stringify(logData, null, 2),
    text: `[${timestamp}] ${localContent.value.level.toUpperCase()}: ${renderedMessage.value}`,
    structured: Object.entries(logData)
      .map(([key, value]) => `${key}=${JSON.stringify(value)}`)
      .join(' '),
    syslog: `<${getSyslogPriority(localContent.value.level)}>${timestamp} ${localContent.value.settings.source || 'app'}: ${renderedMessage.value}`
  }
})

// Watchers
watch(() => props.content, (newContent) => {
  localContent.value = {
    level: 'info',
    message: '',
    format: 'json',
    dataFields: [],
    settings: {
      category: '',
      source: '',
      includeTimestamp: true,
      includeUserId: true,
      includeRequestId: true,
      enableStackTrace: false,
    },
    ...newContent
  }
}, { deep: true, immediate: true })

watch(() => props.variables, (newVariables) => {
  localVariables.value = [...newVariables]
}, { immediate: true })

// Methods
const handleContentChange = () => {
  emit('update:content', localContent.value)
  emit('content-change', localContent.value, localVariables.value)
}

const addDataField = () => {
  localContent.value.dataFields.push({
    key: '',
    type: 'string',
    value: ''
  })
  handleContentChange()
}

const removeDataField = (index: number) => {
  localContent.value.dataFields.splice(index, 1)
  handleContentChange()
}

const getSyslogPriority = (level: string) => {
  const priorities = {
    debug: 7,
    info: 6,
    warn: 4,
    error: 3,
    fatal: 0
  }
  return priorities[level as keyof typeof priorities] || 6
}

const insertTemplate = (template: any) => {
  localContent.value.level = template.level || localContent.value.level
  localContent.value.message = template.message || localContent.value.message
  localContent.value.format = template.format || localContent.value.format
  
  if (template.dataFields) {
    localContent.value.dataFields = [...template.dataFields]
  }
  
  // Merge variables
  if (template.variables) {
    const newVariables = [...new Set([...localVariables.value, ...template.variables])]
    localVariables.value = newVariables
    emit('update:variables', newVariables)
  }
  
  showTemplateLibrary.value = false
  handleContentChange()
}

const insertVariable = (variable: string) => {
  const variableText = `{${variable}}`
  localContent.value.message += variableText
  
  // Add variable to list if not already present
  if (!localVariables.value.includes(variable)) {
    localVariables.value.push(variable)
    emit('update:variables', localVariables.value)
  }
  
  showVariableModal.value = false
  handleContentChange()
}

const getSampleValue = (variable: string) => {
  const sampleValues: Record<string, string> = {
    userId: 'user-123',
    userName: 'John Doe',
    action: 'login',
    resource: 'dashboard',
    ipAddress: '*************',
    userAgent: 'Mozilla/5.0...',
    sessionId: 'sess-abc-123',
    tenantId: 'tenant-456',
    caseId: 'case-789',
    documentId: 'doc-101',
  }
  
  return sampleValues[variable] || `[${variable.toUpperCase()}]`
}
</script>
