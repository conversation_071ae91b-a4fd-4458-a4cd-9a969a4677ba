<template>
  <UiModal @close="$emit('close')" size="lg">
    <div class="p-6">
      <!-- Header -->
      <div class="flex items-center space-x-3 mb-6">
        <div class="w-10 h-10 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center">
          <Icon name="heroicons:bell" class="w-5 h-5 text-purple-600 dark:text-purple-400" />
        </div>
        <div>
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Push Notification Template Library</h3>
          <p class="text-sm text-gray-600 dark:text-gray-400">Choose from pre-built push notification templates</p>
        </div>
      </div>

      <!-- Search and Filter -->
      <div class="flex items-center space-x-4 mb-6">
        <div class="flex-1">
          <UiInput
            v-model="searchQuery"
            placeholder="Search push templates..."
            class="w-full"
          >
            <template #prefix>
              <Icon name="heroicons:magnifying-glass" class="w-4 h-4 text-gray-400" />
            </template>
          </UiInput>
        </div>
        <div>
          <select
            v-model="selectedCategory"
            class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm"
          >
            <option value="">All Categories</option>
            <option value="reminders">Reminders</option>
            <option value="updates">Updates</option>
            <option value="alerts">Alerts</option>
            <option value="marketing">Marketing</option>
          </select>
        </div>
      </div>

      <!-- Template List -->
      <div class="space-y-4 max-h-96 overflow-y-auto">
        <div
          v-for="template in filteredTemplates"
          :key="template.id"
          class="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:border-purple-300 dark:hover:border-purple-600 cursor-pointer transition-colors"
          @click="selectTemplate(template)"
        >
          <div class="flex items-start justify-between mb-3">
            <div>
              <h4 class="text-sm font-medium text-gray-900 dark:text-white">{{ template.name }}</h4>
              <p class="text-xs text-gray-500 dark:text-gray-400">{{ template.category }}</p>
            </div>
            <div class="flex items-center space-x-2">
              <span
                :class="[
                  'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium',
                  getCategoryColor(template.category)
                ]"
              >
                {{ template.category }}
              </span>
              <span
                :class="[
                  'text-xs px-2 py-1 rounded',
                  getPriorityColor(template.priority)
                ]"
              >
                {{ template.priority }}
              </span>
            </div>
          </div>
          
          <p class="text-sm text-gray-600 dark:text-gray-300 mb-3">{{ template.description }}</p>
          
          <!-- Push Notification Preview -->
          <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-3 mb-3">
            <div class="flex items-start space-x-3">
              <div class="w-8 h-8 bg-purple-100 dark:bg-purple-900 rounded flex items-center justify-center flex-shrink-0">
                <Icon name="heroicons:bell" class="w-4 h-4 text-purple-600 dark:text-purple-400" />
              </div>
              <div class="flex-1 min-w-0">
                <div class="text-sm font-medium text-gray-900 dark:text-white">
                  {{ template.title }}
                </div>
                <div class="text-sm text-gray-600 dark:text-gray-300 mt-1">
                  {{ template.content }}
                </div>
                <div v-if="template.actions.length > 0" class="flex items-center space-x-2 mt-2">
                  <button
                    v-for="action in template.actions"
                    :key="action.title"
                    class="px-2 py-1 text-xs font-medium text-purple-600 dark:text-purple-400 bg-purple-50 dark:bg-purple-900/20 rounded"
                  >
                    {{ action.title }}
                  </button>
                </div>
              </div>
              <span class="text-xs text-gray-500 dark:text-gray-400">now</span>
            </div>
          </div>
          
          <div v-if="template.variables.length > 0" class="pt-3 border-t border-gray-200 dark:border-gray-700">
            <div class="text-xs text-gray-500 dark:text-gray-400 mb-1">Variables:</div>
            <div class="flex flex-wrap gap-1">
              <span
                v-for="variable in template.variables"
                :key="variable"
                class="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-mono bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200"
              >
                {{ variable }}
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- Actions -->
      <div class="flex items-center justify-end space-x-3 mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
        <UiButton @click="$emit('close')" variant="outline">
          Cancel
        </UiButton>
      </div>
    </div>
  </UiModal>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

// Emits
const emit = defineEmits<{
  close: []
  select: [template: any]
}>()

// State
const searchQuery = ref('')
const selectedCategory = ref('')

// Push notification templates library
const pushTemplates = [
  {
    id: 'appointment-reminder',
    name: 'Appointment Reminder',
    category: 'reminders',
    description: 'Remind clients about upcoming appointments',
    title: 'Appointment Tomorrow',
    content: 'You have an appointment with {attorneyName} tomorrow at {appointmentTime}.',
    priority: 'high',
    actions: [
      { title: 'View Details', url: '/appointments/{appointmentId}' },
      { title: 'Reschedule', url: '/appointments/{appointmentId}/reschedule' }
    ],
    variables: ['attorneyName', 'appointmentTime', 'appointmentId']
  },
  {
    id: 'case-update',
    name: 'Case Update',
    category: 'updates',
    description: 'Notify about important case developments',
    title: 'Case Update Available',
    content: 'New update available for {caseTitle}. Tap to view details.',
    priority: 'high',
    actions: [
      { title: 'View Update', url: '/cases/{caseId}' }
    ],
    variables: ['caseTitle', 'caseId']
  },
  {
    id: 'document-ready',
    name: 'Document Ready',
    category: 'updates',
    description: 'Notify when documents are ready for review',
    title: 'Document Ready for Review',
    content: '{documentTitle} is ready for your review and signature.',
    priority: 'normal',
    actions: [
      { title: 'Review', url: '/documents/{documentId}' },
      { title: 'Download', url: '/documents/{documentId}/download' }
    ],
    variables: ['documentTitle', 'documentId']
  },
  {
    id: 'payment-due',
    name: 'Payment Due Reminder',
    category: 'reminders',
    description: 'Remind about upcoming payment due dates',
    title: 'Payment Due Soon',
    content: 'Invoice #{invoiceNumber} for ${amount} is due in 3 days.',
    priority: 'normal',
    actions: [
      { title: 'Pay Now', url: '/invoices/{invoiceId}/pay' },
      { title: 'View Invoice', url: '/invoices/{invoiceId}' }
    ],
    variables: ['invoiceNumber', 'amount', 'invoiceId']
  },
  {
    id: 'court-date-alert',
    name: 'Court Date Alert',
    category: 'alerts',
    description: 'Alert about upcoming court appearances',
    title: 'Court Appearance Tomorrow',
    content: 'IMPORTANT: Court appearance for {caseTitle} at {courtTime}.',
    priority: 'urgent',
    actions: [
      { title: 'View Details', url: '/cases/{caseId}/court' },
      { title: 'Get Directions', url: '/courts/{courtId}/directions' }
    ],
    variables: ['caseTitle', 'courtTime', 'caseId', 'courtId']
  },
  {
    id: 'message-received',
    name: 'New Message',
    category: 'updates',
    description: 'Notify about new messages from attorney',
    title: 'New Message from {attorneyName}',
    content: 'You have a new message regarding {caseTitle}.',
    priority: 'normal',
    actions: [
      { title: 'Read Message', url: '/messages/{messageId}' },
      { title: 'Reply', url: '/messages/{messageId}/reply' }
    ],
    variables: ['attorneyName', 'caseTitle', 'messageId']
  },
  {
    id: 'welcome-notification',
    name: 'Welcome Notification',
    category: 'marketing',
    description: 'Welcome new clients to the platform',
    title: 'Welcome to {companyName}',
    content: 'Your legal portal is ready. Tap to explore your dashboard.',
    priority: 'normal',
    actions: [
      { title: 'Get Started', url: '/dashboard' },
      { title: 'Take Tour', url: '/onboarding' }
    ],
    variables: ['companyName']
  },
  {
    id: 'deadline-alert',
    name: 'Deadline Alert',
    category: 'alerts',
    description: 'Alert about approaching deadlines',
    title: 'Deadline Approaching',
    content: '{documentTitle} deadline is in 24 hours. Action required.',
    priority: 'urgent',
    actions: [
      { title: 'Take Action', url: '/documents/{documentId}' }
    ],
    variables: ['documentTitle', 'documentId']
  },
  {
    id: 'consultation-reminder',
    name: 'Consultation Reminder',
    category: 'reminders',
    description: 'Remind about upcoming consultations',
    title: 'Consultation in 1 Hour',
    content: 'Your consultation with {attorneyName} starts at {consultationTime}.',
    priority: 'high',
    actions: [
      { title: 'Join Call', url: '/consultations/{consultationId}/join' },
      { title: 'Reschedule', url: '/consultations/{consultationId}/reschedule' }
    ],
    variables: ['attorneyName', 'consultationTime', 'consultationId']
  },
  {
    id: 'payment-received',
    name: 'Payment Confirmation',
    category: 'updates',
    description: 'Confirm successful payments',
    title: 'Payment Received',
    content: 'Thank you! Your payment of ${amount} has been processed.',
    priority: 'normal',
    actions: [
      { title: 'View Receipt', url: '/payments/{paymentId}/receipt' }
    ],
    variables: ['amount', 'paymentId']
  }
]

// Computed
const filteredTemplates = computed(() => {
  let templates = pushTemplates
  
  // Filter by category
  if (selectedCategory.value) {
    templates = templates.filter(template => template.category === selectedCategory.value)
  }
  
  // Filter by search query
  if (searchQuery.value.trim()) {
    const query = searchQuery.value.toLowerCase()
    templates = templates.filter(template =>
      template.name.toLowerCase().includes(query) ||
      template.description.toLowerCase().includes(query) ||
      template.title.toLowerCase().includes(query) ||
      template.content.toLowerCase().includes(query)
    )
  }
  
  return templates
})

// Methods
const selectTemplate = (template: any) => {
  emit('select', template)
}

const getCategoryColor = (category: string) => {
  const colors = {
    'reminders': 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
    'updates': 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
    'alerts': 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
    'marketing': 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200',
  }
  return colors[category as keyof typeof colors] || 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
}

const getPriorityColor = (priority: string) => {
  const colors = {
    'urgent': 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
    'high': 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200',
    'normal': 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
    'low': 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200',
  }
  return colors[priority as keyof typeof colors] || 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
}
</script>
