/**
 * Core Type System
 * 
 * Foundational types used across the entire application
 * These types provide the building blocks for all domain models
 */

// ============================================================================
// UTILITY TYPES
// ============================================================================

/**
 * Make specific properties optional while keeping others required
 */
export type PartialBy<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>

/**
 * Make specific properties required while keeping others optional
 */
export type RequiredBy<T, K extends keyof T> = Omit<T, K> & Required<Pick<T, K>>

/**
 * Deep partial type for nested objects
 */
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P]
}

/**
 * Deep readonly type for immutable nested objects
 */
export type DeepReadonly<T> = {
  readonly [P in keyof T]: T[P] extends object ? DeepReadonly<T[P]> : T[P]
}

/**
 * Extract keys of a specific type from an object
 */
export type KeysOfType<T, U> = {
  [K in keyof T]: T[K] extends U ? K : never
}[keyof T]

/**
 * Branded types for type safety
 */
export type Brand<T, B> = T & { __brand: B }

// ============================================================================
// COMMON DOMAIN TYPES
// ============================================================================

/**
 * Unique identifier types with branding for type safety
 */
export type UUID = Brand<string, 'UUID'>
export type UserId = Brand<string, 'UserId'>
export type TenantId = Brand<string, 'TenantId'>
export type CaseId = Brand<string, 'CaseId'>
export type DocumentId = Brand<string, 'DocumentId'>
export type TemplateId = Brand<string, 'TemplateId'>

/**
 * Timestamp types
 */
export type ISODateString = Brand<string, 'ISODateString'>
export type UnixTimestamp = Brand<number, 'UnixTimestamp'>

/**
 * Common status enums
 */
export enum EntityStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  PENDING = 'pending',
  SUSPENDED = 'suspended',
  DELETED = 'deleted'
}

export enum ProcessingStatus {
  IDLE = 'idle',
  PROCESSING = 'processing',
  SUCCESS = 'success',
  ERROR = 'error',
  CANCELLED = 'cancelled'
}

// ============================================================================
// AUDIT AND METADATA TYPES
// ============================================================================

/**
 * Audit trail information
 */
export interface AuditInfo {
  createdAt: ISODateString
  createdBy: UserId
  updatedAt: ISODateString
  updatedBy: UserId
  version: number
}

/**
 * Soft delete information
 */
export interface SoftDeleteInfo {
  isDeleted: boolean
  deletedAt?: ISODateString
  deletedBy?: UserId
  deletionReason?: string
}

/**
 * Base entity interface that all domain entities should extend
 */
export interface BaseEntity {
  id: UUID
  tenantId: TenantId
  audit: AuditInfo
  softDelete?: SoftDeleteInfo
}

// ============================================================================
// PAGINATION AND FILTERING TYPES
// ============================================================================

/**
 * Pagination parameters
 */
export interface PaginationParams {
  page: number
  limit: number
  offset?: number
}

/**
 * Pagination metadata
 */
export interface PaginationMeta {
  page: number
  limit: number
  total: number
  totalPages: number
  hasNext: boolean
  hasPrev: boolean
}

/**
 * Sort parameters
 */
export interface SortParams<T = string> {
  field: T
  direction: 'asc' | 'desc'
}

/**
 * Filter operators
 */
export enum FilterOperator {
  EQUALS = 'eq',
  NOT_EQUALS = 'ne',
  GREATER_THAN = 'gt',
  GREATER_THAN_OR_EQUAL = 'gte',
  LESS_THAN = 'lt',
  LESS_THAN_OR_EQUAL = 'lte',
  IN = 'in',
  NOT_IN = 'nin',
  CONTAINS = 'contains',
  STARTS_WITH = 'starts_with',
  ENDS_WITH = 'ends_with',
  IS_NULL = 'is_null',
  IS_NOT_NULL = 'is_not_null'
}

/**
 * Generic filter condition
 */
export interface FilterCondition<T = any> {
  field: string
  operator: FilterOperator
  value: T
}

/**
 * Complex filter with logical operators
 */
export interface FilterGroup {
  operator: 'AND' | 'OR'
  conditions: (FilterCondition | FilterGroup)[]
}

// ============================================================================
// API RESPONSE TYPES
// ============================================================================

/**
 * Standard API response wrapper
 */
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  errors?: ApiError[]
  meta?: ResponseMeta
}

/**
 * Paginated API response
 */
export interface PaginatedResponse<T = any> extends ApiResponse<T[]> {
  pagination: PaginationMeta
}

/**
 * API error structure
 */
export interface ApiError {
  code: string
  message: string
  field?: string
  details?: Record<string, any>
}

/**
 * Response metadata
 */
export interface ResponseMeta {
  timestamp: ISODateString
  requestId: UUID
  version: string
  processingTime?: number
  warnings?: string[]
}

// ============================================================================
// VALIDATION TYPES
// ============================================================================

/**
 * Validation result
 */
export interface ValidationResult {
  isValid: boolean
  errors: ValidationError[]
  warnings?: ValidationWarning[]
}

/**
 * Validation error
 */
export interface ValidationError {
  field: string
  code: string
  message: string
  value?: any
}

/**
 * Validation warning
 */
export interface ValidationWarning {
  field: string
  code: string
  message: string
  value?: any
}

// ============================================================================
// PERMISSION AND SECURITY TYPES
// ============================================================================

/**
 * Permission context
 */
export interface PermissionContext {
  userId: UserId
  tenantId: TenantId
  roles: string[]
  permissions: string[]
  resourceId?: UUID
  resourceType?: string
}

/**
 * Security classification
 */
export enum SecurityClassification {
  PUBLIC = 'public',
  INTERNAL = 'internal',
  CONFIDENTIAL = 'confidential',
  RESTRICTED = 'restricted'
}

// ============================================================================
// FILE AND MEDIA TYPES
// ============================================================================

/**
 * File information
 */
export interface FileInfo {
  id: UUID
  name: string
  originalName: string
  mimeType: string
  size: number
  url: string
  thumbnailUrl?: string
  checksum: string
  uploadedAt: ISODateString
  uploadedBy: UserId
  classification: SecurityClassification
}

/**
 * File upload parameters
 */
export interface FileUploadParams {
  file: File
  classification?: SecurityClassification
  tags?: string[]
  metadata?: Record<string, any>
}

// ============================================================================
// NOTIFICATION TYPES
// ============================================================================

/**
 * Notification priority
 */
export enum NotificationPriority {
  LOW = 'low',
  NORMAL = 'normal',
  HIGH = 'high',
  URGENT = 'urgent'
}

/**
 * Notification channel
 */
export enum NotificationChannel {
  EMAIL = 'email',
  SMS = 'sms',
  PUSH = 'push',
  IN_APP = 'in_app',
  WEBHOOK = 'webhook'
}

// ============================================================================
// SEARCH TYPES
// ============================================================================

/**
 * Search entity types
 */
export type SearchEntityType = 'cases' | 'documents' | 'users' | 'clients' | 'templates' | 'all'

/**
 * Search parameters
 */
export interface SearchParams {
  query: string
  types?: SearchEntityType[]
  filters?: FilterGroup
  sort?: SortParams[]
  pagination?: PaginationParams
  facets?: string[]
  highlight?: boolean
}

/**
 * Search result
 */
export interface SearchResult<T = any> {
  item: T
  score: number
  highlights?: Record<string, string[]>
  type: SearchEntityType
  matchedFields?: string[]
}

/**
 * Search response
 */
export interface SearchResponse<T = any> {
  results: SearchResult<T>[]
  total: number
  facets?: Record<string, SearchFacet>
  suggestions?: string[]
  processingTime: number
  totalByType?: Record<SearchEntityType, number>
}

/**
 * Global search result item
 */
export interface GlobalSearchResultItem {
  id: string
  title: string
  description?: string
  type: SearchEntityType
  url: string
  metadata?: Record<string, any>
  createdAt?: string
  updatedAt?: string
  status?: string
  tags?: string[]
  thumbnail?: string
  icon?: string
}

/**
 * Search filter configuration for global search
 */
export interface GlobalSearchFilterConfig {
  key: string
  label: string
  type: 'select' | 'multiselect' | 'dateRange' | 'numberRange' | 'boolean'
  options?: Array<{ label: string; value: any }>
  placeholder?: string
  entityTypes?: SearchEntityType[]
}

/**
 * Search view mode
 */
export type SearchViewMode = 'list' | 'grid' | 'table'

/**
 * Search sort options
 */
export interface SearchSortOption {
  key: string
  label: string
  field: string
  direction: 'asc' | 'desc'
}

/**
 * Global search state
 */
export interface GlobalSearchState {
  query: string
  selectedTypes: SearchEntityType[]
  filters: Record<string, any>
  sortBy: string
  viewMode: SearchViewMode
  isLoading: boolean
  results: GlobalSearchResultItem[]
  total: number
  totalByType: Record<SearchEntityType, number>
  suggestions: string[]
  error: string | null
  hasSearched: boolean
}

/**
 * Search facet
 */
export interface SearchFacet {
  name: string
  values: SearchFacetValue[]
}

/**
 * Search facet value
 */
export interface SearchFacetValue {
  value: string
  count: number
  selected?: boolean
}

// ============================================================================
// TYPE GUARDS
// ============================================================================

/**
 * Type guard for checking if a value is a valid UUID
 */
export function isUUID(value: any): value is UUID {
  return typeof value === 'string' && /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(value)
}

/**
 * Type guard for checking if a value is a valid ISO date string
 */
export function isISODateString(value: any): value is ISODateString {
  return typeof value === 'string' && !isNaN(Date.parse(value))
}

/**
 * Type guard for API response
 */
export function isApiResponse<T>(value: any): value is ApiResponse<T> {
  return typeof value === 'object' && value !== null && typeof value.success === 'boolean'
}

/**
 * Type guard for base entity
 */
export function isBaseEntity(value: any): value is BaseEntity {
  return (
    typeof value === 'object' &&
    value !== null &&
    isUUID(value.id) &&
    typeof value.tenantId === 'string' &&
    typeof value.audit === 'object'
  )
}
