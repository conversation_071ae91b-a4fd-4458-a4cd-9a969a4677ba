import { describe, it, expect, beforeEach, vi } from 'vitest'
import { ref } from 'vue'
import { useDynamicFormValidation } from '~/composables/useDynamicFormValidation'
import type { DynamicFormConfig, DynamicFormField } from '~/app/shared/types'
import { DynamicFormFieldType } from '~/app/shared/types'

// Mock zod
vi.mock('zod', () => ({
  z: {
    any: () => ({
      optional: () => ({}),
      refine: () => ({})
    }),
    string: () => ({
      min: () => ({}),
      max: () => ({}),
      email: () => ({}),
      url: () => ({}),
      regex: () => ({}),
      optional: () => ({}),
      refine: () => ({})
    }),
    number: () => ({
      int: () => ({}),
      min: () => ({}),
      max: () => ({}),
      positive: () => ({}),
      optional: () => ({}),
      refine: () => ({})
    }),
    boolean: () => ({
      refine: () => ({})
    }),
    array: () => ({
      min: () => ({})
    }),
    instanceof: () => ({}),
    date: () => ({})
  }
}))

describe('useDynamicFormValidation', () => {
  let config: DynamicFormConfig
  let formData: Record<string, any>
  let validation: ReturnType<typeof useDynamicFormValidation>

  beforeEach(() => {
    config = {
      id: 'test-form',
      fields: [
        {
          id: 'firstName',
          name: 'firstName',
          label: 'First Name',
          type: DynamicFormFieldType.TEXT,
          required: true,
          validation: {
            required: true,
            minLength: 2,
            maxLength: 50
          }
        },
        {
          id: 'email',
          name: 'email',
          label: 'Email',
          type: DynamicFormFieldType.EMAIL,
          required: true,
          validation: {
            required: true,
            email: true
          }
        },
        {
          id: 'age',
          name: 'age',
          label: 'Age',
          type: DynamicFormFieldType.NUMBER,
          validation: {
            min: 18,
            max: 120,
            integer: true
          }
        },
        {
          id: 'newsletter',
          name: 'newsletter',
          label: 'Newsletter',
          type: DynamicFormFieldType.CHECKBOX,
          validation: {
            required: true
          }
        }
      ]
    }

    formData = {}
    validation = useDynamicFormValidation(config, formData)
  })

  describe('Validation State', () => {
    it('initializes with correct default state', () => {
      expect(validation.validationState.isValid).toBe(false)
      expect(validation.validationState.isValidating).toBe(false)
      expect(validation.validationState.errors).toEqual({})
      expect(validation.validationState.touched).toEqual({})
      expect(validation.validationState.validationCount).toBe(0)
    })

    it('tracks validation count', async () => {
      const initialCount = validation.validationState.validationCount
      await validation.validateAll()
      expect(validation.validationState.validationCount).toBe(initialCount + 1)
    })
  })

  describe('Field Validation', () => {
    it('validates required fields correctly', async () => {
      const field = config.fields[0] // firstName field
      
      // Test empty value
      let errors = await validation.validateField(field, '')
      expect(errors.length).toBeGreaterThan(0)
      
      // Test valid value
      errors = await validation.validateField(field, 'John')
      expect(errors.length).toBe(0)
    })

    it('validates email fields correctly', async () => {
      const field = config.fields[1] // email field
      
      // Test invalid email
      let errors = await validation.validateField(field, 'invalid-email')
      expect(errors.length).toBeGreaterThan(0)
      
      // Test valid email
      errors = await validation.validateField(field, '<EMAIL>')
      expect(errors.length).toBe(0)
    })

    it('validates number fields correctly', async () => {
      const field = config.fields[2] // age field
      
      // Test value below minimum
      let errors = await validation.validateField(field, 15)
      expect(errors.length).toBeGreaterThan(0)
      
      // Test value above maximum
      errors = await validation.validateField(field, 150)
      expect(errors.length).toBeGreaterThan(0)
      
      // Test valid value
      errors = await validation.validateField(field, 25)
      expect(errors.length).toBe(0)
    })

    it('validates checkbox fields correctly', async () => {
      const field = config.fields[3] // newsletter field
      
      // Test unchecked required checkbox
      let errors = await validation.validateField(field, false)
      expect(errors.length).toBeGreaterThan(0)
      
      // Test checked checkbox
      errors = await validation.validateField(field, true)
      expect(errors.length).toBe(0)
    })
  })

  describe('Custom Validation', () => {
    it('executes custom validation functions', async () => {
      const customField: DynamicFormField = {
        id: 'password',
        name: 'password',
        label: 'Password',
        type: DynamicFormFieldType.PASSWORD,
        validation: {
          custom: (value: string) => {
            if (value && value.length < 8) {
              return 'Password must be at least 8 characters'
            }
            return null
          }
        }
      }

      // Test invalid password
      let errors = await validation.validateField(customField, 'short')
      expect(errors).toContain('Password must be at least 8 characters')

      // Test valid password
      errors = await validation.validateField(customField, 'validpassword123')
      expect(errors.length).toBe(0)
    })

    it('executes async custom validation functions', async () => {
      const asyncField: DynamicFormField = {
        id: 'username',
        name: 'username',
        label: 'Username',
        type: DynamicFormFieldType.TEXT,
        validation: {
          customAsync: async (value: string) => {
            // Simulate async validation (e.g., checking username availability)
            await new Promise(resolve => setTimeout(resolve, 10))
            if (value === 'taken') {
              return 'Username is already taken'
            }
            return null
          }
        }
      }

      // Test taken username
      let errors = await validation.validateField(asyncField, 'taken')
      expect(errors).toContain('Username is already taken')

      // Test available username
      errors = await validation.validateField(asyncField, 'available')
      expect(errors.length).toBe(0)
    })
  })

  describe('Conditional Validation', () => {
    it('skips validation for hidden fields', async () => {
      const conditionalField: DynamicFormField = {
        id: 'companyName',
        name: 'companyName',
        label: 'Company Name',
        type: DynamicFormFieldType.TEXT,
        required: true,
        conditional: {
          show: [
            { field: 'accountType', operator: 'equals', value: 'business' }
          ]
        },
        validation: {
          required: true
        }
      }

      // Set form data so field is hidden
      formData.accountType = 'personal'

      const errors = await validation.validateField(conditionalField, '')
      expect(errors.length).toBe(0) // Should not validate hidden field
    })

    it('validates conditionally required fields', async () => {
      const conditionalField: DynamicFormField = {
        id: 'phoneNumber',
        name: 'phoneNumber',
        label: 'Phone Number',
        type: DynamicFormFieldType.TEXT,
        conditional: {
          require: [
            { field: 'contactMethod', operator: 'equals', value: 'phone' }
          ]
        },
        validation: {
          required: true
        }
      }

      // Set form data so field is required
      formData.contactMethod = 'phone'

      let errors = await validation.validateField(conditionalField, '')
      expect(errors.length).toBeGreaterThan(0)

      // Set form data so field is not required
      formData.contactMethod = 'email'

      errors = await validation.validateField(conditionalField, '')
      expect(errors.length).toBe(0)
    })
  })

  describe('Validation All Fields', () => {
    it('validates all fields and returns overall validity', async () => {
      // Set invalid data
      formData.firstName = ''
      formData.email = 'invalid'
      formData.age = 15

      const isValid = await validation.validateAll()
      expect(isValid).toBe(false)
      expect(validation.hasErrors.value).toBe(true)
      expect(Object.keys(validation.validationState.errors).length).toBeGreaterThan(0)
    })

    it('returns true when all fields are valid', async () => {
      // Set valid data
      formData.firstName = 'John'
      formData.email = '<EMAIL>'
      formData.age = 25
      formData.newsletter = true

      const isValid = await validation.validateAll()
      expect(isValid).toBe(true)
      expect(validation.hasErrors.value).toBe(false)
      expect(Object.keys(validation.validationState.errors).length).toBe(0)
    })
  })

  describe('Field-specific Validation', () => {
    it('validates single field by name', async () => {
      formData.firstName = ''

      const isValid = await validation.validateFieldByName('firstName')
      expect(isValid).toBe(false)
      expect(validation.validationState.errors.firstName).toBeDefined()
    })

    it('clears errors for valid fields', async () => {
      // Set initial error
      validation.validationState.errors.firstName = ['Required']

      // Validate with valid value
      formData.firstName = 'John'
      const isValid = await validation.validateFieldByName('firstName')
      
      expect(isValid).toBe(true)
      expect(validation.validationState.errors.firstName).toBeUndefined()
    })
  })

  describe('Error Management', () => {
    it('gets field error correctly', () => {
      validation.validationState.errors.firstName = ['First name is required']
      
      const error = validation.getFieldError('firstName')
      expect(error).toBe('First name is required')
    })

    it('returns null for fields without errors', () => {
      const error = validation.getFieldError('nonexistent')
      expect(error).toBeNull()
    })

    it('checks if field has error', () => {
      validation.validationState.errors.firstName = ['Error']
      
      expect(validation.hasFieldError('firstName')).toBe(true)
      expect(validation.hasFieldError('email')).toBe(false)
    })

    it('clears field errors', () => {
      validation.validationState.errors.firstName = ['Error']
      validation.clearFieldError('firstName')
      
      expect(validation.validationState.errors.firstName).toBeUndefined()
    })

    it('clears all errors', () => {
      validation.validationState.errors = {
        firstName: ['Error 1'],
        email: ['Error 2']
      }
      
      validation.clearAllErrors()
      expect(validation.validationState.errors).toEqual({})
      expect(validation.validationState.isValid).toBe(true)
    })
  })

  describe('Touch Management', () => {
    it('tracks touched fields', () => {
      validation.touchField('firstName')
      expect(validation.isFieldTouched('firstName')).toBe(true)
      expect(validation.isFieldTouched('email')).toBe(false)
    })

    it('touches all fields', () => {
      validation.touchAll()
      
      config.fields.forEach(field => {
        expect(validation.isFieldTouched(field.name)).toBe(true)
      })
    })
  })

  describe('Computed Properties', () => {
    it('calculates error count correctly', () => {
      validation.validationState.errors = {
        firstName: ['Error 1', 'Error 2'],
        email: ['Error 3']
      }
      
      expect(validation.errorCount.value).toBe(3)
    })

    it('tracks touched field count', () => {
      validation.touchField('firstName')
      validation.touchField('email')
      
      expect(validation.touchedFields.value).toEqual(['firstName', 'email'])
    })
  })

  describe('Reset Functionality', () => {
    it('resets validation state', () => {
      // Set some state
      validation.validationState.errors = { firstName: ['Error'] }
      validation.validationState.touched = { firstName: true }
      validation.validationState.validationCount = 5

      validation.resetValidation()

      expect(validation.validationState.errors).toEqual({})
      expect(validation.validationState.touched).toEqual({})
      expect(validation.validationState.isValid).toBe(false)
      expect(validation.validationState.validationCount).toBe(0)
    })
  })
})
