import axios, { type AxiosInstance, type InternalAxiosRequestConfig } from 'axios';
import { type H3Event, getCookie, deleteCookie, getHeader } from 'h3';
import { ACCESS_TOKEN_COOKIE, REFRESH_TOKEN_COOKIE } from '~/server/constants';
import getRefreshTokenUtil from '~/server/utils/getRefreshToken';

// Unique string key for storing the refresh promise in event.context
const REFRESH_PROMISE_KEY = 'axiosInstanceRefreshTokenPromise';

// Define an interface for the authenticated Axios instance if not already globally available
interface AuthenticatedAxiosInstance extends AxiosInstance {
  // If you have custom methods like setAccessToken, define them here
  // For this refactor, we'll assume standard AxiosInstance usage primarily
}

import { useRuntimeConfig } from '#imports';

export function createApiInstance(event?: H3Event): AuthenticatedAxiosInstance {
  const config = useRuntimeConfig(); // Nuxt 3 way to access runtime config

  const apiInstance: AuthenticatedAxiosInstance = axios.create({
    baseURL: config.public.apiBase as string,
    headers: {
      'Content-Type': 'multipart/form-data',
      // Accept-Language can be more dynamic if needed, potentially passed in or derived differently
      'Accept-Language': event ? getHeader(event, 'accept-language') || 'en' : 'en',
    },
    withCredentials: true, // Important for sending cookies
  }) as AuthenticatedAxiosInstance;

  // Request Interceptor
  apiInstance.interceptors.request.use(
    async (requestConfig: InternalAxiosRequestConfig & { _retry?: boolean }) => {
      // If this is a retry attempt (marked by _retry === true),
      // the Authorization header should have already been set by the response interceptor
      // with the new token. We should not overwrite it by reading the cookie again,
      // as the cookie might not be updated yet in this immediate context.
      if (requestConfig._retry) {
        console.log('[apiInstance Request Interceptor] Retry request: Skipping cookie check for Authorization header.');
        return requestConfig;
      }

      // add tenant id to headers
      if (event) {
        const tenantId = getHeader(event, 'x-tenant-id');
        if (tenantId) {
          requestConfig.headers['x-tenant-id'] = tenantId;
        }
      }

      if (event) { // Only try to access cookies if H3Event is available (server-side context)
        const accessToken = getCookie(event, ACCESS_TOKEN_COOKIE);
        if (accessToken) {
          console.log('[apiInstance Request Interceptor] Initial request: Setting Authorization header from cookie.');
          requestConfig.headers.Authorization = `Bearer ${accessToken}`;
        } else {
          console.log('[apiInstance Request Interceptor] Initial request: No access token cookie found.');
        }
      }
      return requestConfig;
    },
    (error) => Promise.reject(error)
  );

  // Response Interceptor for token refresh
  apiInstance.interceptors.response.use(
    (response) => response,
    async (error) => {
      const originalRequest = error.config as InternalAxiosRequestConfig & { _retry?: boolean };
 
      
      // Check for 401 Unauthorized, ensure it's not a retry, and H3Event is available
      if (error.response?.status === 401 && !originalRequest._retry && event) {
        originalRequest._retry = true; // Mark that a retry attempt will be made

        let oldAccessToken: string | undefined;
        const authHeader = originalRequest.headers?.Authorization;
        if (typeof authHeader === 'string' && authHeader.startsWith('Bearer ')) {
          oldAccessToken = authHeader.split(' ')[1];
        }
        console.log('[apiInstance Interceptor] Old Access Token that caused 401:', oldAccessToken ? `****** (ends with ${oldAccessToken.slice(-6)})` : 'Not present');

        // CRUCIAL CHECK: Only attempt refresh if a refresh token cookie actually exists
        const refreshTokenFromCookie = getCookie(event, REFRESH_TOKEN_COOKIE);
        if (!refreshTokenFromCookie) {
          console.log('[apiInstance Interceptor] No refresh token cookie found. Aborting refresh attempt.');
          deleteCookie(event, ACCESS_TOKEN_COOKIE, { httpOnly: true, path: '/', sameSite: process.env.NODE_ENV === 'production' ? 'strict' as const : 'lax' as const });
          return Promise.reject(error); // Reject with the original 401 error
        }

        // Ensure event.context exists (it should in H3)
        event.context = event.context || {};

        let refreshPromise = event.context[REFRESH_PROMISE_KEY] as Promise<{ accessToken?: string } | null> | undefined;

        if (!refreshPromise) {
          console.log('[apiInstance Interceptor] No refresh in progress for this event. Initiating new token refresh.');
          refreshPromise = getRefreshTokenUtil(event)
            .catch((refreshErr: any) => {
              console.error('[apiInstance Interceptor] getRefreshTokenUtil threw an error during refresh attempt:', refreshErr.message || refreshErr);
              const cookieOptions = { httpOnly: true, path: '/', sameSite: process.env.NODE_ENV === 'production' ? 'strict' as const : 'lax' as const };
              deleteCookie(event, REFRESH_TOKEN_COOKIE, { ...cookieOptions, path: '/api/' });
              deleteCookie(event, ACCESS_TOKEN_COOKIE, cookieOptions);
              return null; // Indicate failure
            })
            .finally(() => {
              event.context[REFRESH_PROMISE_KEY] = undefined; // Clear the promise once settled
            });
          event.context[REFRESH_PROMISE_KEY] = refreshPromise;
        } else {
          console.log('[apiInstance Interceptor] Refresh already in progress for this event. Awaiting existing refresh promise.');
        }

        try {
          const refreshResponseData = await refreshPromise;

          if (refreshResponseData && refreshResponseData.accessToken) {
            const { accessToken: newAccessToken } = refreshResponseData;

            console.log('[apiInstance Interceptor] New Access Token received:', `****** (ends with ${newAccessToken.slice(-6)})`);
            if (oldAccessToken && oldAccessToken === newAccessToken) {
              console.warn('[apiInstance Interceptor] WARNING: Old and new access tokens are identical. This might indicate an issue with the refresh process if a new token was expected.');
            } else if (oldAccessToken) {
              console.log('[apiInstance Interceptor] Old and new access tokens are different, as expected.');
            }

            console.log('[apiInstance Interceptor] Token refresh successful. Retrying original request.');
            // originalRequest is InternalAxiosRequestConfig, so headers is guaranteed to be defined.
            originalRequest.headers.Authorization = `Bearer ${newAccessToken}`;
            
            console.log('[apiInstance Interceptor] Retrying original request with new Authorization header:', `Bearer ****** (ends with ${newAccessToken.slice(-6)})`);
            return apiInstance(originalRequest); // Retry the original request
          } else {
            console.error('[apiInstance Interceptor] Token refresh did not yield a new access token or refreshPromise resolved to null/failed.');
            return Promise.reject(error); // Propagate the original 401 error
          }
        } catch (awaitError: any) {
          console.error('[apiInstance Interceptor] Error awaiting the refresh promise:', awaitError.message || awaitError);
          return Promise.reject(error); // Propagate the original 401 error
        }
      }
      return Promise.reject(error);
    }
  );

  return apiInstance;
}