// stores/notificationTrigger.ts
import { defineStore } from 'pinia';
import { useApi } from '../composables/useApi.js';
import { useToast } from '../composables/useToast.js';
import type { NotificationTrigger, NotificationTriggerPayload } from '../types/notification.ts';

// Interface for query parameters when fetching notification triggers
export interface GetNotificationTriggersQuery {
  isActive?: boolean;
  channel?: 'email' | 'sms' | 'in_app';
  event?: string;
  sort?: string; // e.g., "name:ASC", "createdAt:DESC"
  search?: string;
  limit?: number;
  page?: number;
}

// Interface for the API response when fetching multiple triggers
interface NotificationTriggersResponse {
  data: NotificationTrigger[];
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}
interface NotificationTriggerState {
  triggers: NotificationTrigger[];
  selectedTrigger: NotificationTrigger | null;
  meta: NotificationTriggersResponse['meta'] | null; // For pagination metadata
  isLoading: boolean;
  error: string | null;
}

export const useNotificationTriggerStore = defineStore('notificationTrigger', {
  state: (): NotificationTriggerState => ({
    triggers: [],
    selectedTrigger: null,
    meta: null,
    isLoading: false,
    error: null,
  }),

  getters: {
    allTriggersWithMeta: (state) => ({ triggers: state.triggers, meta: state.meta }), // Getter for data + meta
    allTriggers: (state) => state.triggers,
    getTriggerById: (state) => (id: string) => state.triggers.find((t: NotificationTrigger) => t.id === id),
    getIsLoading: (state) => state.isLoading,
    getError: (state) => state.error,
  },

  actions: {
    clearError() {
      this.error = null;
    },

    setSelectedTrigger(trigger: NotificationTrigger | null) {
      this.selectedTrigger = trigger;
    },

    async fetchNotificationTriggers(query?: GetNotificationTriggersQuery) {
      this.isLoading = true;
      this.clearError();
      const { get } = useApi();
      try {
        // Backend: GET /notifications/triggers
        const response = await get<NotificationTriggersResponse>('/notifications/triggers', { params: query });
        this.triggers = response.data || []; // Ensure triggers is always an array
        this.meta = response.meta || null;   // Ensure meta is null if not present
      } catch (err: any) {
        this.error = this.handleApiError(err, 'Failed to fetch notification triggers.');
        this.triggers = []; // Clear data on error
        this.meta = null; // Clear meta on error
        const { showToast } = useToast();
        showToast({
          type: 'error',
          title: 'Fetch Error',
          message: this.error!,
        });
        // No re-throw for fetch operations usually, components rely on isLoading/error state
      } finally {
        this.isLoading = false;
      }
    },

    async createNotificationTrigger(payload: NotificationTriggerPayload) {
      this.isLoading = true;
      this.clearError();
      const { post } = useApi();
      try {
        // Backend: POST /notifications/triggers
        const response = await post<NotificationTrigger>('/notifications/triggers', payload);
        this.triggers.push(response); // Add to local state
        const { showToast } = useToast();
        showToast({
          type: 'success',
          title: 'Success',
          message: `Notification trigger '${response.name}' created successfully.`,
        });
        // return true; // Removed
      } catch (err: any) {
        this.error = this.handleApiError(err, 'Failed to create notification trigger.');
        const { showToast } = useToast();
        showToast({
          type: 'error',
          title: 'Creation Failed',
          message: this.error!,
        });
        throw err; // Re-throw error
      } finally {
        this.isLoading = false;
      }
    },

    async updateNotificationTrigger(id: string, payload: Partial<NotificationTriggerPayload>) {
      this.isLoading = true;
      this.clearError();
      const { patch } = useApi();
      try {
        // Backend: PATCH /notifications/triggers/:id
        const response = await patch<NotificationTrigger>(`/notifications/triggers/${id}`, payload);
        const index = this.triggers.findIndex((t: NotificationTrigger) => t.id === id);
        if (index !== -1) {
          this.triggers[index] = response; // Update local state
        }
        const { showToast } = useToast();
        showToast({
          type: 'success',
          title: 'Success',
          message: `Notification trigger '${response.name}' updated successfully.`,
        });
        // return true; // Removed
      } catch (err: any) {
        this.error = this.handleApiError(err, 'Failed to update notification trigger.');
        const { showToast } = useToast();
        showToast({
          type: 'error',
          title: 'Update Failed',
          message: this.error!,
        });
        throw err; // Re-throw error
      } finally {
        this.isLoading = false;
      }
    },

    async deleteNotificationTrigger(id: string) {
      this.isLoading = true;
      this.clearError();
      const { delete: del } = useApi(); // Alias delete to avoid keyword conflict
      try {
        // Backend: DELETE /notifications/triggers/:id (soft-delete)
        await del(`/notifications/triggers/${id}`);
        this.triggers = this.triggers.filter((t: NotificationTrigger) => t.id !== id); // Remove from local state
        const { showToast } = useToast();
        showToast({
          type: 'success',
          title: 'Success',
          message: 'Notification trigger deleted successfully.',
        });
        // return true; // Removed
      } catch (err: any) {
        this.error = this.handleApiError(err, 'Failed to delete notification trigger.');
        const { showToast } = useToast();
        showToast({
          type: 'error',
          title: 'Deletion Failed',
          message: this.error!,
        });
        throw err; // Re-throw error
      } finally {
        this.isLoading = false;
      }
    },

    async previewNotificationTrigger(id: string) {
      this.isLoading = true;
      this.clearError();
      const { post } = useApi();
      try {
        // Backend: POST /notifications/triggers/:id/preview
        const response = await post<any>(`/notifications/triggers/${id}/preview`);
        const { showToast } = useToast();
        showToast({
          type: 'info',
          title: 'Preview Initiated',
          message: 'Check your email/in-app notifications for the preview.',
          duration: 5000,
        });
        return response; // Return preview data if needed by UI, or void if not
      } catch (err: any) {
        this.error = this.handleApiError(err, 'Failed to preview notification trigger.');
        const { showToast } = useToast();
        showToast({
          type: 'error',
          title: 'Preview Failed',
          message: this.error!,
        });
        throw err; // Re-throw error
      } finally {
        this.isLoading = false;
      }
    },

    /**
     * Helper to handle API errors consistently.
     */
    handleApiError(err: any, defaultMessage: string): string {
      if (err.response && err.response.data && err.response.data.message) {
        if (Array.isArray(err.response.data.message)) {
          return err.response.data.message.join(', ');
        }
        return err.response.data.message;
      }
      return err.message || defaultMessage;
    },
  },
});