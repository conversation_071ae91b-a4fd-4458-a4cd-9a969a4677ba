<template>
  <section 
    :class="[
      'relative overflow-hidden',
      backgroundClass,
      paddingClass,
      textColorClass
    ]"
  >
    <!-- Background Elements -->
    <div v-if="backgroundImage" class="absolute inset-0 z-0">
      <img 
        :src="backgroundImage" 
        :alt="backgroundImageAlt || 'Background'" 
        class="w-full h-full object-cover"
        :class="backgroundImageOpacity"
        loading="eager"
      />
    </div>
    
    <!-- Background Pattern/Gradient Overlay -->
    <div v-if="showOverlay" :class="overlayClass" />
    
    <!-- Floating Elements -->
    <div v-if="showFloatingElements" class="absolute inset-0 overflow-hidden pointer-events-none">
      <div class="absolute top-20 left-10 w-20 h-20 bg-white/10 rounded-full blur-xl animate-float" />
      <div class="absolute top-40 right-20 w-32 h-32 bg-white/5 rounded-full blur-2xl animate-float-delayed" />
      <div class="absolute bottom-20 left-1/4 w-16 h-16 bg-white/10 rounded-full blur-lg animate-float-slow" />
    </div>

    <!-- Content Container -->
    <div class="container mx-auto px-6 relative z-10">
      <div :class="contentAlignmentClass">
        <!-- Badge/Announcement -->
        <div v-if="badge" class="mb-6 animate-fade-in-up">
          <UiBadge 
            :variant="badge.variant || 'primary'" 
            :size="badge.size || 'lg'"
            class="inline-flex items-center gap-2"
          >
            <Icon v-if="badge.icon" :name="badge.icon" class="w-4 h-4" />
            {{ badge.text }}
          </UiBadge>
        </div>

        <!-- Main Heading -->
        <h1 :class="headingClass" class="animate-fade-in-up">
          <span v-if="headingHighlight" class="block">
            {{ headingHighlight }}
          </span>
          {{ title }}
        </h1>

        <!-- Subtitle/Description -->
        <p v-if="subtitle" :class="subtitleClass" class="animate-fade-in-up delay-200">
          {{ subtitle }}
        </p>

        <!-- Action Buttons -->
        <div v-if="actions.length > 0" :class="actionsClass" class="animate-fade-in-up delay-400">
          <UiButton
            v-for="(action, index) in actions"
            :key="index"
            :variant="action.variant || 'primary'"
            :size="action.size || 'lg'"
            :to="action.to"
            :href="action.href"
            :target="action.external ? '_blank' : undefined"
            :class="[
              'transform hover:scale-105 transition-all duration-300 ease-in-out',
              action.class
            ]"
            @click="action.onClick"
          >
            <Icon v-if="action.icon" :name="action.icon" class="w-5 h-5 mr-2" />
            {{ action.text }}
          </UiButton>
        </div>

        <!-- Additional Content Slot -->
        <div v-if="$slots.additional" class="mt-8 animate-fade-in-up delay-500">
          <slot name="additional" />
        </div>
      </div>
    </div>

    <!-- Bottom Wave/Divider -->
    <div v-if="showBottomWave" class="absolute bottom-0 left-0 w-full">
      <svg class="w-full h-12 fill-current text-white" viewBox="0 0 1200 120" preserveAspectRatio="none">
        <path d="M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z" opacity=".25"></path>
        <path d="M0,0V15.81C13,36.92,27.64,56.86,47.69,72.05,99.41,111.27,165,111,224.58,91.58c31.15-10.15,60.09-26.07,89.67-39.8,40.92-19,84.73-46,130.83-49.67,36.26-2.85,70.9,9.42,98.6,31.56,31.77,25.39,62.32,62,103.63,73,40.44,10.79,81.35-6.69,119.13-24.28s75.16-39,116.92-43.05c59.73-5.85,113.28,22.88,168.9,38.84,30.2,8.66,59,6.17,87.09-7.5,22.43-10.89,48-26.93,60.65-49.24V0Z" opacity=".5"></path>
        <path d="M0,0V5.63C149.93,59,314.09,71.32,475.83,42.57c43-7.64,84.23-20.12,127.61-26.46,59-8.63,112.48,12.24,165.56,35.4C827.93,77.22,886,95.24,951.2,90c86.53-7,172.46-45.71,248.8-84.81V0Z"></path>
      </svg>
    </div>
  </section>
</template>

<script setup lang="ts">
interface Badge {
  text: string
  icon?: string
  variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'danger'
  size?: 'sm' | 'md' | 'lg'
}

interface Action {
  text: string
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost'
  size?: 'sm' | 'md' | 'lg' | 'xl'
  to?: string
  href?: string
  external?: boolean
  icon?: string
  class?: string
  onClick?: () => void
}

interface Props {
  title: string
  subtitle?: string
  headingHighlight?: string
  badge?: Badge
  actions?: Action[]
  variant?: 'primary' | 'secondary' | 'dark' | 'light' | 'gradient'
  size?: 'sm' | 'md' | 'lg' | 'xl'
  textAlign?: 'left' | 'center' | 'right'
  backgroundImage?: string
  backgroundImageAlt?: string
  backgroundImageOpacity?: string
  showOverlay?: boolean
  showBottomWave?: boolean
  showFloatingElements?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  actions: () => [],
  variant: 'primary',
  size: 'lg',
  textAlign: 'center',
  backgroundImageOpacity: 'opacity-30',
  showOverlay: true,
  showBottomWave: false,
  showFloatingElements: true
})

// Computed classes
const backgroundClass = computed(() => {
  const variants = {
    primary: 'bg-gradient-to-r from-brandPrimary-700 to-brandPrimary-900',
    secondary: 'bg-gradient-to-r from-purple-600 to-brandPrimary-700',
    dark: 'bg-gradient-to-r from-gray-800 to-gray-900',
    light: 'bg-gradient-to-r from-gray-50 to-white',
    gradient: 'bg-gradient-to-r from-blue-600 via-purple-600 to-brandPrimary-700'
  }
  return variants[props.variant]
})

const textColorClass = computed(() => {
  return props.variant === 'light' ? 'text-gray-900' : 'text-white'
})

const paddingClass = computed(() => {
  const sizes = {
    sm: 'py-16 md:py-20',
    md: 'py-20 md:py-24',
    lg: 'py-24 md:py-32',
    xl: 'py-32 md:py-40'
  }
  return sizes[props.size]
})

const contentAlignmentClass = computed(() => {
  const alignments = {
    left: 'text-left max-w-4xl',
    center: 'text-center max-w-4xl mx-auto',
    right: 'text-right max-w-4xl ml-auto'
  }
  return alignments[props.textAlign]
})

const headingClass = computed(() => {
  const baseClass = 'font-extrabold leading-tight mb-6'
  const sizeClass = props.size === 'xl' ? 'text-5xl md:text-7xl' : 
                   props.size === 'lg' ? 'text-4xl md:text-6xl' :
                   props.size === 'md' ? 'text-3xl md:text-5xl' : 
                   'text-2xl md:text-4xl'
  return `${baseClass} ${sizeClass}`
})

const subtitleClass = computed(() => {
  const baseClass = 'opacity-90 mb-10 max-w-3xl'
  const sizeClass = props.size === 'xl' ? 'text-2xl md:text-3xl' :
                   props.size === 'lg' ? 'text-xl md:text-2xl' :
                   'text-lg md:text-xl'
  const alignClass = props.textAlign === 'center' ? 'mx-auto' :
                    props.textAlign === 'right' ? 'ml-auto' : ''
  return `${baseClass} ${sizeClass} ${alignClass}`
})

const actionsClass = computed(() => {
  const baseClass = 'flex gap-4'
  const alignClass = props.textAlign === 'center' ? 'justify-center' :
                    props.textAlign === 'right' ? 'justify-end' : 'justify-start'
  const responsiveClass = 'flex-col sm:flex-row'
  return `${baseClass} ${alignClass} ${responsiveClass}`
})

const overlayClass = computed(() => {
  if (!props.showOverlay) return ''
  return 'absolute inset-0 bg-black/20 z-0'
})
</script>

<style scoped>
@keyframes fadeInFromBottom {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

@keyframes float-delayed {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-30px) rotate(-180deg); }
}

@keyframes float-slow {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-15px); }
}

.animate-fade-in-up {
  animation: fadeInFromBottom 0.8s ease-out forwards;
  opacity: 0;
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-float-delayed {
  animation: float-delayed 8s ease-in-out infinite;
}

.animate-float-slow {
  animation: float-slow 10s ease-in-out infinite;
}

.delay-200 { animation-delay: 0.2s; }
.delay-400 { animation-delay: 0.4s; }
.delay-500 { animation-delay: 0.5s; }
</style>
