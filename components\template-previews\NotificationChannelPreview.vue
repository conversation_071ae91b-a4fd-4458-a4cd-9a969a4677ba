<template>
  <div class="space-y-4">
    <!-- Channel-specific Preview -->
    <component
      :is="currentPreviewComponent"
      :content="content"
      :variables="variables"
      :template-data="templateData"
    />
  </div>
</template>

<script setup lang="ts">
import { computed, defineAsyncComponent } from 'vue'

// Props
interface Props {
  channel: string
  content: any
  variables: string[]
  templateData: any
}

const props = defineProps<Props>()

// Dynamic preview components
const previewComponents = {
  email: defineAsyncComponent(() => import('./channels/EmailChannelPreview.vue')),
  sms: defineAsyncComponent(() => import('./channels/SmsChannelPreview.vue')),
  push: defineAsyncComponent(() => import('./channels/PushChannelPreview.vue')),
  in_app: defineAsyncComponent(() => import('./channels/InAppChannelPreview.vue')),
  log: defineAsyncComponent(() => import('./channels/LogChannelPreview.vue')),
}

// Computed
const currentPreviewComponent = computed(() => {
  return previewComponents[props.channel as keyof typeof previewComponents] || null
})
</script>
