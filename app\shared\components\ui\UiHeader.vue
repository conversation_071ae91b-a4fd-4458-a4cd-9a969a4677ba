<template>
  <component
    :is="tag"
    :class="[
      'font-medium transition-colors duration-200',
      sizeClasses,
      colorClasses,
      alignmentClasses,
      spacingClasses,
      props.uppercase ? 'uppercase' : '',
      props.truncate ? 'truncate' : '',
      props.noWrap ? 'whitespace-nowrap' : '',
      props.class
    ]"
    :style="props.style"
  >
    <!-- Leading Icon -->
    <Icon
      v-if="props.leadingIcon"
      :name="props.leadingIcon"
      :class="[
        'inline-flex items-center',
        iconSizeClasses,
        $slots.default ? (isRtl ? 'ml-2' : 'mr-2') : ''
      ]"
    />

    <!-- Header Content -->
    <span v-if="$slots.default" class="inline-flex items-center px-1.5">
      <slot />
    </span>
    <span v-else-if="props.text" class="inline-flex items-center px-1.5">
      {{ props.text }}
    </span>

    <!-- Trailing Icon -->
    <Icon
      v-if="props.trailingIcon"
      :name="props.trailingIcon"
      :class="[
        'inline-flex items-center',
        iconSizeClasses,
        $slots.default || props.text ? (isRtl ? 'mr-2' : 'ml-2') : ''
      ]"
    />

    <!-- Badge/Indicator -->
    <span
      v-if="props.badge"
      :class="[
        'inline-flex items-center justify-center rounded-full text-xs font-medium',
        badgeClasses,
        $slots.default || props.text ? (isRtl ? 'mr-2' : 'ml-2') : ''
      ]"
    >
      {{ typeof props.badge === 'string' || typeof props.badge === 'number' ? props.badge : '' }}
    </span>

    <!-- Required Indicator -->
    <span
      v-if="props.required"
      class="text-brandDanger ml-1"
      aria-label="Required"
    >
      *
    </span>
  </component>
</template>

<script setup lang="ts">
import { computed, withDefaults } from 'vue';

interface Props {
  /** The HTML tag to render */
  tag?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'p' | 'span' | 'div' | 'label';
  /** Header text content */
  text?: string;
  /** Header size variant */
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl' | '4xl';
  /** Color variant */
  color?: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' | 'muted' | 'white';
  /** Text alignment */
  align?: 'left' | 'center' | 'right';
  /** Spacing variant */
  spacing?: 'none' | 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  /** Leading icon */
  leadingIcon?: string;
  /** Trailing icon */
  trailingIcon?: string;
  /** Badge content */
  badge?: string | number | boolean;
  /** Badge color variant */
  badgeColor?: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger';
  /** Transform text to uppercase */
  uppercase?: boolean;
  /** Truncate long text */
  truncate?: boolean;
  /** Prevent text wrapping */
  noWrap?: boolean;
  /** Show required indicator */
  required?: boolean;
  /** Custom CSS classes */
  class?: string;
  /** Custom inline styles */
  style?: Record<string, string>;
}

const props = withDefaults(defineProps<Props>(), {
  tag: 'h2',
  size: 'md',
  color: 'default',
  align: 'left',
  spacing: 'md',
  badgeColor: 'primary',
  uppercase: false,
  truncate: false,
  noWrap: false,
  required: false,
});

// RTL support
const isRtl = computed(() => {
  if (typeof document !== 'undefined') {
    return document.documentElement.dir === 'rtl' || document.documentElement.getAttribute('dir') === 'rtl';
  }
  return false;
});

// Size classes
const sizeClasses = computed(() => {
  const sizeMap = {
    'xs': 'text-xs',
    'sm': 'text-sm',
    'md': 'text-base',
    'lg': 'text-lg',
    'xl': 'text-xl',
    '2xl': 'text-2xl',
    '3xl': 'text-3xl',
    '4xl': 'text-4xl',
  };
  return sizeMap[props.size];
});

// Icon size classes based on header size
const iconSizeClasses = computed(() => {
  const iconSizeMap = {
    'xs': 'h-3 w-3',
    'sm': 'h-4 w-4',
    'md': 'h-5 w-5',
    'lg': 'h-6 w-6',
    'xl': 'h-7 w-7',
    '2xl': 'h-8 w-8',
    '3xl': 'h-9 w-9',
    '4xl': 'h-10 w-10',
  };
  return iconSizeMap[props.size];
});

// Color classes
const colorClasses = computed(() => {
  const colorMap = {
    'default': 'text-gray-900 dark:text-gray-100',
    'primary': 'text-brandPrimary-700 dark:text-brandPrimary-400',
    'secondary': 'text-brandSecondary-700 dark:text-brandSecondary-400',
    'success': 'text-brandSuccess-700 dark:text-brandSuccess-400',
    'warning': 'text-brandWarning-700 dark:text-brandWarning-400',
    'danger': 'text-brandDanger-700 dark:text-brandDanger-400',
    'muted': 'text-gray-600 dark:text-gray-400',
    'white': 'text-white',
  };
  return colorMap[props.color];
});

// Alignment classes
const alignmentClasses = computed(() => {
  const alignMap = {
    'left': isRtl.value ? 'text-right' : 'text-left',
    'center': 'text-center',
    'right': isRtl.value ? 'text-left' : 'text-right',
  };
  return alignMap[props.align];
});

// Spacing classes
const spacingClasses = computed(() => {
  const spacingMap = {
    'none': '',
    'xs': 'mb-1',
    'sm': 'mb-2',
    'md': 'mb-3',
    'lg': 'mb-4',
    'xl': 'mb-6',
  };
  return spacingMap[props.spacing];
});

// Badge classes
const badgeClasses = computed(() => {
  const badgeColorMap = {
    'default': 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300',
    'primary': 'bg-brandPrimary-100 text-brandPrimary-800 dark:bg-brandPrimary-900 dark:text-brandPrimary-200',
    'secondary': 'bg-brandSecondary-100 text-brandSecondary-800 dark:bg-brandSecondary-900 dark:text-brandSecondary-200',
    'success': 'bg-brandSuccess-100 text-brandSuccess-800 dark:bg-brandSuccess-900 dark:text-brandSuccess-200',
    'warning': 'bg-brandWarning-100 text-brandWarning-800 dark:bg-brandWarning-900 dark:text-brandWarning-200',
    'danger': 'bg-brandDanger-100 text-brandDanger-800 dark:bg-brandDanger-900 dark:text-brandDanger-200',
  };
  
  const sizeMap = {
    'xs': 'h-4 min-w-4 px-1.5',
    'sm': 'h-5 min-w-5 px-2',
    'md': 'h-6 min-w-6 px-2',
    'lg': 'h-6 min-w-6 px-2.5',
    'xl': 'h-7 min-w-7 px-3',
    '2xl': 'h-8 min-w-8 px-3',
    '3xl': 'h-9 min-w-9 px-3.5',
    '4xl': 'h-10 min-w-10 px-4',
  };
  
  return `${badgeColorMap[props.badgeColor]} ${sizeMap[props.size]}`;
});
</script>

<style scoped>
/* Custom styles for enhanced header appearance */
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
