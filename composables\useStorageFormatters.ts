/**
 * Storage size formatting composable
 * Provides utilities for formatting bytes into human-readable storage sizes
 */

export interface StorageUnit {
  name: string
  symbol: string
  bytes: number
}

export interface StorageFormatOptions {
  decimals?: number
  binary?: boolean
  longForm?: boolean
  separator?: string
}

export interface StorageFormatResult {
  value: number
  unit: string
  formatted: string
  bytes: number
}

/**
 * Storage size formatting composable
 */
export function useStorageFormatters() {
  // Storage units (decimal - base 1000)
  const DECIMAL_UNITS: StorageUnit[] = [
    { name: 'byte', symbol: 'B', bytes: 1 },
    { name: 'kilobyte', symbol: 'KB', bytes: 1000 },
    { name: 'megabyte', symbol: 'MB', bytes: 1000 ** 2 },
    { name: 'gigabyte', symbol: 'GB', bytes: 1000 ** 3 },
    { name: 'terabyte', symbol: 'TB', bytes: 1000 ** 4 },
    { name: 'petabyte', symbol: 'PB', bytes: 1000 ** 5 },
    { name: 'exabyte', symbol: 'EB', bytes: 1000 ** 6 }
  ]

  // Storage units (binary - base 1024)
  const BINARY_UNITS: StorageUnit[] = [
    { name: 'byte', symbol: 'B', bytes: 1 },
    { name: 'kibibyte', symbol: 'KiB', bytes: 1024 },
    { name: 'mebibyte', symbol: 'MiB', bytes: 1024 ** 2 },
    { name: 'gibibyte', symbol: 'GiB', bytes: 1024 ** 3 },
    { name: 'tebibyte', symbol: 'TiB', bytes: 1024 ** 4 },
    { name: 'pebibyte', symbol: 'PiB', bytes: 1024 ** 5 },
    { name: 'exbibyte', symbol: 'EiB', bytes: 1024 ** 6 }
  ]

  /**
   * Format bytes into human-readable storage size
   */
  const formatBytes = (
    bytes: number,
    options: StorageFormatOptions = {}
  ): string => {
    const {
      decimals = 2,
      binary = false,
      longForm = false,
      separator = ' '
    } = options

    if (bytes === 0) return `0${separator}${longForm ? 'bytes' : 'B'}`
    if (bytes < 0) return `0${separator}${longForm ? 'bytes' : 'B'}`

    const units = binary ? BINARY_UNITS : DECIMAL_UNITS
    const base = binary ? 1024 : 1000

    const unitIndex = Math.floor(Math.log(bytes) / Math.log(base))
    const clampedIndex = Math.min(unitIndex, units.length - 1)
    const unit = units[clampedIndex]

    const value = bytes / unit.bytes
    const roundedValue = Math.round(value * Math.pow(10, decimals)) / Math.pow(10, decimals)

    const unitLabel = longForm ? 
      (roundedValue === 1 ? unit.name : `${unit.name}s`) : 
      unit.symbol

    return `${roundedValue}${separator}${unitLabel}`
  }

  /**
   * Format bytes with detailed information
   */
  const formatBytesDetailed = (
    bytes: number,
    options: StorageFormatOptions = {}
  ): StorageFormatResult => {
    const {
      decimals = 2,
      binary = false,
      longForm = false,
      separator = ' '
    } = options

    if (bytes === 0 || bytes < 0) {
      return {
        value: 0,
        unit: longForm ? 'bytes' : 'B',
        formatted: `0${separator}${longForm ? 'bytes' : 'B'}`,
        bytes: Math.max(0, bytes)
      }
    }

    const units = binary ? BINARY_UNITS : DECIMAL_UNITS
    const base = binary ? 1024 : 1000

    const unitIndex = Math.floor(Math.log(bytes) / Math.log(base))
    const clampedIndex = Math.min(unitIndex, units.length - 1)
    const unit = units[clampedIndex]

    const value = bytes / unit.bytes
    const roundedValue = Math.round(value * Math.pow(10, decimals)) / Math.pow(10, decimals)

    const unitLabel = longForm ? 
      (roundedValue === 1 ? unit.name : `${unit.name}s`) : 
      unit.symbol

    return {
      value: roundedValue,
      unit: unitLabel,
      formatted: `${roundedValue}${separator}${unitLabel}`,
      bytes
    }
  }

  /**
   * Parse storage size string back to bytes
   */
  const parseStorageSize = (sizeString: string): number => {
    if (!sizeString || typeof sizeString !== 'string') return 0

    const cleanString = sizeString.trim().toLowerCase()
    const match = cleanString.match(/^(\d+(?:\.\d+)?)\s*([a-z]+)?$/)
    
    if (!match) return 0

    const value = parseFloat(match[1])
    const unitStr = match[2] || 'b'

    // Find matching unit
    const allUnits = [...DECIMAL_UNITS, ...BINARY_UNITS]
    const unit = allUnits.find(u => 
      u.symbol.toLowerCase() === unitStr ||
      u.name.toLowerCase() === unitStr ||
      u.name.toLowerCase() + 's' === unitStr
    )

    return unit ? Math.round(value * unit.bytes) : value
  }

  /**
   * Convert between storage units
   */
  const convertStorageUnit = (
    value: number,
    fromUnit: string,
    toUnit: string,
    binary = false
  ): number => {
    const units = binary ? BINARY_UNITS : DECIMAL_UNITS
    
    const from = units.find(u => 
      u.symbol.toLowerCase() === fromUnit.toLowerCase() ||
      u.name.toLowerCase() === fromUnit.toLowerCase()
    )
    
    const to = units.find(u => 
      u.symbol.toLowerCase() === toUnit.toLowerCase() ||
      u.name.toLowerCase() === toUnit.toLowerCase()
    )

    if (!from || !to) return 0

    const bytes = value * from.bytes
    return bytes / to.bytes
  }

  /**
   * Get storage usage percentage
   */
  const getStorageUsagePercentage = (used: number, total: number): number => {
    if (total <= 0) return 0
    return Math.min(100, Math.max(0, (used / total) * 100))
  }

  /**
   * Format storage usage with percentage
   */
  const formatStorageUsage = (
    used: number,
    total: number,
    options: StorageFormatOptions = {}
  ): string => {
    const usedFormatted = formatBytes(used, options)
    const totalFormatted = formatBytes(total, options)
    const percentage = getStorageUsagePercentage(used, total)
    
    return `${usedFormatted} / ${totalFormatted} (${percentage.toFixed(1)}%)`
  }

  /**
   * Get remaining storage
   */
  const getRemainingStorage = (used: number, total: number): number => {
    return Math.max(0, total - used)
  }

  /**
   * Format remaining storage
   */
  const formatRemainingStorage = (
    used: number,
    total: number,
    options: StorageFormatOptions = {}
  ): string => {
    const remaining = getRemainingStorage(used, total)
    return formatBytes(remaining, options)
  }

  /**
   * Check if storage is near capacity
   */
  const isStorageNearCapacity = (
    used: number,
    total: number,
    threshold = 0.9
  ): boolean => {
    if (total <= 0) return false
    return (used / total) >= threshold
  }

  /**
   * Get storage status
   */
  const getStorageStatus = (
    used: number,
    total: number
  ): 'low' | 'medium' | 'high' | 'critical' => {
    const percentage = getStorageUsagePercentage(used, total)
    
    if (percentage >= 95) return 'critical'
    if (percentage >= 85) return 'high'
    if (percentage >= 70) return 'medium'
    return 'low'
  }

  /**
   * Reactive storage formatting
   */
  const formatBytesReactive = (
    bytes: Ref<number>,
    options: StorageFormatOptions = {}
  ): Ref<string> => {
    return computed(() => formatBytes(bytes.value, options))
  }

  return {
    // Core formatting functions
    formatBytes,
    formatBytesDetailed,
    parseStorageSize,
    convertStorageUnit,
    
    // Usage utilities
    getStorageUsagePercentage,
    formatStorageUsage,
    getRemainingStorage,
    formatRemainingStorage,
    isStorageNearCapacity,
    getStorageStatus,
    
    // Reactive formatting
    formatBytesReactive,
    
    // Constants
    DECIMAL_UNITS,
    BINARY_UNITS
  }
}

/**
 * Quick format bytes function for simple use cases
 */
export const formatBytes = (bytes: number, decimals = 2): string => {
  const { formatBytes: format } = useStorageFormatters()
  return format(bytes, { decimals })
}

/**
 * Quick format file size function (legacy compatibility)
 */
export const formatFileSize = formatBytes

export default useStorageFormatters
