/**
 * Debounce Composable
 * 
 * Provides debouncing functionality for functions and reactive values
 */

import { ref, watch, type Ref } from 'vue'

export interface DebounceOptions {
  delay: number
  immediate?: boolean
  maxWait?: number
}

/**
 * Debounce a function
 */
export function useDebounce<T extends (...args: any[]) => any>(
  fn: T,
  delay: number = 300,
  options: Omit<DebounceOptions, 'delay'> = {}
): T {
  let timeoutId: NodeJS.Timeout | null = null
  let maxTimeoutId: NodeJS.Timeout | null = null
  let lastCallTime = 0
  
  const debouncedFn = ((...args: Parameters<T>) => {
    const now = Date.now()
    
    // Clear existing timeouts
    if (timeoutId) {
      clearTimeout(timeoutId)
    }
    
    // Handle immediate execution
    if (options.immediate && now - lastCallTime > delay) {
      lastCallTime = now
      return fn(...args)
    }
    
    // Set up max wait timeout if specified
    if (options.maxWait && !maxTimeoutId) {
      maxTimeoutId = setTimeout(() => {
        if (timeoutId) {
          clearTimeout(timeoutId)
          timeoutId = null
        }
        maxTimeoutId = null
        lastCallTime = Date.now()
        fn(...args)
      }, options.maxWait)
    }
    
    // Set up regular debounce timeout
    timeoutId = setTimeout(() => {
      if (maxTimeoutId) {
        clearTimeout(maxTimeoutId)
        maxTimeoutId = null
      }
      timeoutId = null
      lastCallTime = Date.now()
      fn(...args)
    }, delay)
  }) as T
  
  return debouncedFn
}

/**
 * Debounce a reactive value
 */
export function useDebouncedRef<T>(
  value: Ref<T>,
  delay: number = 300,
  options: Omit<DebounceOptions, 'delay'> = {}
): Ref<T> {
  const debouncedValue = ref(value.value) as Ref<T>
  
  const updateDebouncedValue = useDebounce((newValue: T) => {
    debouncedValue.value = newValue
  }, delay, options)
  
  watch(value, updateDebouncedValue, { immediate: options.immediate })
  
  return debouncedValue
}

/**
 * Create a debounced watcher
 */
export function useDebouncedWatch<T>(
  source: Ref<T>,
  callback: (value: T, oldValue: T) => void,
  delay: number = 300,
  options: Omit<DebounceOptions, 'delay'> = {}
): void {
  const debouncedCallback = useDebounce(callback, delay, options)
  
  watch(source, debouncedCallback)
}
