/**
 * Case Management Domain Models
 *
 * Enhanced domain models for the case management feature
 * with comprehensive type safety and business logic
 */

// Unused import block removed

// ============================================================================
// CASE STATUS AND PRIORITY ENUMS
// ============================================================================

export enum CaseStatus {
  DRAFT = 'draft',
  OPEN = 'open',
  IN_PROGRESS = 'in_progress',
  ON_HOLD = 'on_hold',
  UNDER_REVIEW = 'under_review',
  AWAITING_CLIENT = 'awaiting_client',
  AWAITING_COURT = 'awaiting_court',
  CLOSED = 'closed',
  ARCHIVED = 'archived',
  CANCELLED = 'cancelled'
}

export enum CasePriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent',
  CRITICAL = 'critical'
}

export enum CaseType {
  CIVIL = 'civil',
  CRIMINAL = 'criminal',
  CORPORATE = 'corporate',
  FAMILY = 'family',
  IMMIGRATION = 'immigration',
  INTELLECTUAL_PROPERTY = 'intellectual_property',
  LABOR = 'labor',
  REAL_ESTATE = 'real_estate',
  TAX = 'tax',
  BANKRUPTCY = 'bankruptcy',
  PERSONAL_INJURY = 'personal_injury',
  CONTRACT = 'contract',
  LITIGATION = 'litigation',
  COMPLIANCE = 'compliance',
  OTHER = 'other'
}

// ============================================================================
// CASE PARTICIPANT TYPES
// ============================================================================

export enum ParticipantRole {
  ATTORNEY = 'attorney',
  PARALEGAL = 'paralegal',
  CLIENT = 'client',
  OPPOSING_COUNSEL = 'opposing_counsel',
  OPPOSING_PARTY = 'opposing_party',
  WITNESS = 'witness',
  EXPERT_WITNESS = 'expert_witness',
  COURT_REPORTER = 'court_reporter',
  MEDIATOR = 'mediator',
  ARBITRATOR = 'arbitrator',
  JUDGE = 'judge',
  CLERK = 'clerk',
  OTHER = 'other'
}

export enum ParticipantStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  PENDING = 'pending',
  REMOVED = 'removed'
}

export interface CaseParticipant {
  id: string
  userId: string
  role: 'attorney' | 'paralegal' | 'client' | 'witness' | 'expert' | 'other'
  name: string
  email: string
  phone?: string
  address?: string
  notes?: string
  isPrimary: boolean
  addedAt: string
  addedBy: string
}

export interface CaseDocument {
  id: string
  name: string
  type: string
  size: number
  url: string
  uploadedAt: string
  uploadedBy: string
  tags: string[]
  isConfidential: boolean
  version: number
  checksum: string
}

export interface CaseNote {
  id: string
  content: string
  type: 'general' | 'meeting' | 'call' | 'email' | 'task' | 'milestone'
  isPrivate: boolean
  createdAt: string
  createdBy: string
  updatedAt?: string
  updatedBy?: string
  attachments: string[]
  tags: string[]
}

export interface CaseTask {
  id: string
  title: string
  description?: string
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled'
  priority: CasePriority
  assignedTo: string[]
  dueDate?: string
  completedAt?: string
  createdAt: string
  createdBy: string
  dependencies: string[]
  estimatedHours?: number
  actualHours?: number
}

export interface CaseTimeline {
  id: string
  type: 'created' | 'status_changed' | 'participant_added' | 'document_uploaded' | 'note_added' | 'task_created' | 'task_completed' | 'custom'
  title: string
  description?: string
  timestamp: string
  userId: string
  userName: string
  metadata?: Record<string, any>
}

export interface CaseBilling {
  id: string
  totalHours: number
  totalAmount: number
  hourlyRate: number
  currency: string
  billingStatus: 'draft' | 'sent' | 'paid' | 'overdue'
  lastBilledAt?: string
  nextBillingDate?: string
  timeEntries: TimeEntry[]
}

export interface TimeEntry {
  id: string
  userId: string
  userName: string
  description: string
  hours: number
  rate: number
  date: string
  isBillable: boolean
  isBilled: boolean
  createdAt: string
}

export interface Case {
  id: string
  caseNumber: string
  title: string
  description?: string
  status: CaseStatus
  priority: CasePriority
  type: CaseType
  
  // Participants
  participants: CaseParticipant[]
  primaryAttorney?: string
  primaryClient?: string
  
  // Dates
  createdAt: string
  updatedAt: string
  openedAt?: string
  closedAt?: string
  dueDate?: string
  
  // Organization
  tags: string[]
  category?: string
  jurisdiction?: string
  courtName?: string
  judgeAssigned?: string
  
  // Documents and notes
  documents: CaseDocument[]
  notes: CaseNote[]
  tasks: CaseTask[]
  timeline: CaseTimeline[]
  
  // Billing
  billing?: CaseBilling
  
  // Metadata
  customFields: Record<string, any>
  isConfidential: boolean
  isArchived: boolean
  
  // Tenant info
  tenantId: string
  
  // Audit
  createdBy: string
  updatedBy: string
}

export interface CaseCreateData {
  title: string
  description?: string
  type: CaseType
  priority: CasePriority
  primaryClient?: string
  primaryAttorney?: string
  dueDate?: string
  tags?: string[]
  category?: string
  jurisdiction?: string
  courtName?: string
  customFields?: Record<string, any>
  isConfidential?: boolean
}

export interface CaseUpdateData {
  title?: string
  description?: string
  status?: CaseStatus
  priority?: CasePriority
  type?: CaseType
  dueDate?: string
  tags?: string[]
  category?: string
  jurisdiction?: string
  courtName?: string
  judgeAssigned?: string
  customFields?: Record<string, any>
  isConfidential?: boolean
}

export interface CaseFilters {
  status?: CaseStatus[]
  priority?: CasePriority[]
  type?: CaseType[]
  assignedTo?: string[]
  createdBy?: string[]
  tags?: string[]
  dateRange?: {
    start: string
    end: string
    field: 'createdAt' | 'updatedAt' | 'dueDate' | 'closedAt'
  }
  search?: string
  isArchived?: boolean
  isConfidential?: boolean
}

export interface CaseSortOptions {
  field: 'title' | 'status' | 'priority' | 'type' | 'createdAt' | 'updatedAt' | 'dueDate'
  direction: 'asc' | 'desc'
}

export interface CaseListOptions {
  page?: number
  limit?: number
  filters?: CaseFilters
  sort?: CaseSortOptions
  include?: ('participants' | 'documents' | 'notes' | 'tasks' | 'timeline' | 'billing')[]
}
