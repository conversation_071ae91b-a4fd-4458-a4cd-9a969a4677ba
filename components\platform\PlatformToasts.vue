<template>
  <div aria-live="assertive" class="fixed inset-0 flex items-end px-4 py-6 pointer-events-none sm:p-6 sm:items-start">
    <div class="w-full flex flex-col items-center space-y-4 sm:items-end">
      <transition-group
        name="toast"
        tag="div"
        enter-active-class="transform ease-out duration-300 transition"
        enter-from-class="translate-y-2 opacity-0 sm:translate-y-0 sm:translate-x-2"
        enter-to-class="translate-y-0 opacity-100 sm:translate-x-0"
        leave-active-class="transition ease-in duration-75"
        leave-from-class="opacity-100"
        leave-to-class="opacity-0"
      >
        <div
          v-for="toast in activeToasts"
          :key="toast.id"
          class="max-w-sm w-full shadow-sm rounded-lg pointer-events-auto border overflow-hidden"
          style="min-width: 24rem;"
          :class="getToastClasses(toast.type)"
        >
          <div class="p-4">
            <div class="flex items-center">
              <div class="flex-shrink-0 mr-3">
                <Icon
                  :name="getToastIcon(toast.type)"
                  :class="[ getIconColor(toast.type)]"
                  size="calc(var(--spacing) * 6)"
                
                />
              </div>
              <div class="flex-1 min-w-0">
                <div class="flex items-center">
                  <span class="text-sm font-medium mr-2" :class="getTextColor(toast.type)">
                    {{ toast.title || getDefaultTitle(toast.type) }}
                  </span>
                  <span class="text-xs text-gray-500">
                    {{ formatTimestamp(toast.createdAt) }}
                  </span>
                </div>
                <p v-if="toast.message" class="text-sm mt-1" :class="getTextColor(toast.type)">
                  {{ toast.message }}
                </p>
              </div>
              <div class="flex-shrink-0 ml-3">
                <button
                  @click="removeToast(toast.id)"
                  class="p-1 rounded-md hover:bg-black/10 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition-colors duration-200"
                  :aria-label="`Dismiss ${toast.type} notification`"
                >
                  <Icon name="heroicons:x-mark" size="calc(var(--spacing) * 4)" :class="getTextColor(toast.type)" />
                </button>
              </div>
            </div>
          </div>

          <!-- Progress bar for auto-dismiss -->
          <div
            v-if="toast.duration && toast.duration > 0"
            class="absolute bottom-0 left-0 h-1 bg-current opacity-30 transition-all duration-100 ease-linear"
            :style="{ width: `${getProgressWidth(toast)}%` }"
          ></div>
        </div>
      </transition-group>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted } from 'vue';
import { useToastStore } from '~/stores/toast';
import { storeToRefs } from 'pinia';

const toastStore = useToastStore();
// Use storeToRefs to destructure reactive properties from the store
// This maintains reactivity for 'activeToasts'
const { activeToasts } = storeToRefs(toastStore);

// Expose removeToast directly from the store action
const removeToast = toastStore.removeToast;

// Auto-dismiss functionality
const timeouts = ref<Map<string, NodeJS.Timeout>>(new Map());
const progressUpdateInterval = ref<NodeJS.Timeout | null>(null);

// Function to start auto-removal for a toast
const startAutoRemove = (toast: any) => {
  // Skip if duration is 0 (no auto-dismiss) or already has a timeout
  if (!toast.duration || toast.duration <= 0 || timeouts.value.has(toast.id)) {
    return;
  }

  // Set start time for progress calculation
  toast.startTime = Date.now();

  const timeout = setTimeout(() => {
    removeToast(toast.id);
    timeouts.value.delete(toast.id);
  }, toast.duration);

  timeouts.value.set(toast.id, timeout);
};

// Watch for new toasts and set up auto-removal
const setupToastWatcher = () => {
  let previousToastIds = new Set(activeToasts.value.map(t => t.id));

  const checkForNewToasts = () => {
    const currentToastIds = new Set(activeToasts.value.map(t => t.id));

    // Find new toasts and start auto-removal
    for (const toast of activeToasts.value) {
      if (!previousToastIds.has(toast.id)) {
        startAutoRemove(toast);
      }
    }

    // Clean up removed toasts
    for (const id of previousToastIds) {
      if (!currentToastIds.has(id)) {
        const timeout = timeouts.value.get(id);
        if (timeout) {
          clearTimeout(timeout);
          timeouts.value.delete(id);
        }
      }
    }

    previousToastIds = currentToastIds;
  };

  // Check every 100ms for new toasts
  const watchInterval = setInterval(checkForNewToasts, 100);

  return () => clearInterval(watchInterval);
};

// Lifecycle management
let cleanupWatcher: (() => void) | null = null;

onMounted(() => {
  cleanupWatcher = setupToastWatcher();

  // Set up auto-removal for existing toasts
  activeToasts.value.forEach(toast => {
    startAutoRemove(toast);
  });

  // Start progress update interval for smooth progress bars
  progressUpdateInterval.value = setInterval(() => {
    // Force reactivity update for progress bars
    // This ensures the progress bars update smoothly
  }, 100);
});

onUnmounted(() => {
  if (cleanupWatcher) {
    cleanupWatcher();
  }

  if (progressUpdateInterval.value) {
    clearInterval(progressUpdateInterval.value);
  }

  // Clear all timeouts
  for (const timeout of timeouts.value.values()) {
    clearTimeout(timeout);
  }
  timeouts.value.clear();
});

// Toast styling functions
const getToastClasses = (type: string) => {
  const classes = {
    success: 'bg-brandSuccess-50 border-brandSuccess-200',
    error: 'bg-brandDanger-50 border-brandDanger-200',
    warning: 'bg-brandWarning-50 border-brandWarning-200',
    info: 'bg-brandInfo-50 border-brandInfo-200'
  }
  return classes[type as keyof typeof classes] || classes.info
}

const getToastIcon = (type: string) => {
  const icons = {
    success: 'heroicons:check-circle',
    error: 'heroicons:x-circle',
    warning: 'heroicons:exclamation-triangle',
    info: 'heroicons:information-circle'
  }
  return icons[type as keyof typeof icons] || icons.info
}

const getIconColor = (type: string) => {
  const colors = {
    success: 'text-green-600',
    error: 'text-red-600',
    warning: 'text-yellow-600',
    info: 'text-blue-600'
  }
  return colors[type as keyof typeof colors] || colors.info
}

const getTextColor = (type: string) => {
  const colors = {
    success: 'text-green-800',
    error: 'text-red-800',
    warning: 'text-yellow-800',
    info: 'text-blue-800'
  }
  return colors[type as keyof typeof colors] || colors.info
}

const getDefaultTitle = (type: string) => {
  const titles = {
    success: 'Success',
    error: 'Error',
    warning: 'Warning',
    info: 'Information'
  }
  return titles[type as keyof typeof titles] || titles.info
}

const formatTimestamp = (timestamp: number) => {
  const now = Date.now()
  const diff = now - timestamp
  const minutes = Math.floor(diff / 60000)

  if (minutes < 1) {
    return 'just now'
  } else if (minutes === 1) {
    return '1 min ago'
  } else if (minutes < 60) {
    return `${minutes} mins ago`
  } else {
    const hours = Math.floor(minutes / 60)
    return hours === 1 ? '1 hour ago' : `${hours} hours ago`
  }
}

// Progress calculation for auto-dismiss
const getProgressWidth = (toast: any) => {
  if (!toast.duration || toast.duration <= 0 || !toast.startTime) {
    return 0;
  }

  const elapsed = Date.now() - toast.startTime;
  const progress = Math.max(0, Math.min(100, (elapsed / toast.duration) * 100));
  return progress;
}
</script>

<style scoped>
/* Transition styles for toasts - crucial for smooth entry/exit */
.toast-enter-active,
.toast-leave-active {
  transition: all 0.3s ease;
}
.toast-enter-from,
.toast-leave-to {
  opacity: 0;
  transform: translateX(100%);
}
/* Ensure toasts stack correctly */
.toast-move {
  transition: transform 0.3s ease;
}
</style>