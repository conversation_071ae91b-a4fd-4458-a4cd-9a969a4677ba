<template>
  <div class="space-y-6 py-6">
    <!-- Usage Overview Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <UiCard
        v-for="metric in usageMetrics"
        :key="metric.key"
        class="hover:shadow-lg transition-shadow duration-200"
      >
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">
              {{ metric.label }}
            </p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white mt-1">
              {{ metric.value }}
            </p>
            <p v-if="metric.limit" class="text-xs text-gray-500 dark:text-gray-400 mt-1">
              of {{ metric.limit }} {{ metric.unit }}
            </p>
          </div>
          <div
            :class="[
              'w-12 h-12 rounded-xl flex items-center justify-center',
              metric.iconBg
            ]"
          >
            <Icon :name="metric.icon" :class="['h-6 w-6', metric.iconColor]" />
          </div>
        </div>
        <div v-if="metric.percentage !== undefined" class="mt-4">
          <div class="flex items-center justify-between text-xs mb-1">
            <span class="text-gray-500 dark:text-gray-400">Usage</span>
            <span :class="getUsageColor(metric.percentage)">{{ metric.percentage }}%</span>
          </div>
          <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div
              class="h-2 rounded-full transition-all duration-300"
              :class="getUsageBarColor(metric.percentage)"
              :style="{ width: `${Math.min(metric.percentage, 100)}%` }"
            ></div>
          </div>
        </div>
      </UiCard>
    </div>

    <!-- Usage Charts -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Storage Usage Over Time -->
      <UiCard>
        <template #header>
          <div>
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Storage Usage Trend</h3>
            <p class="text-sm text-gray-500 dark:text-gray-400">Storage consumption over time</p>
          </div>
        </template>
        <div class="h-64">
          <AreaChart
            :data="storageChartData"
            :categories="storageChartCategories"
            :key="colorMode.value"
            x-formatter="date"
            y-formatter="storage"
            :grid="true"
            curve="smooth"
            :legend="{ position: 'top' }"
          />
        </div>
      </UiCard>

      <!-- Activity Distribution -->
      <UiCard>
        <template #header>
          <div>
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Activity Distribution</h3>
            <p class="text-sm text-gray-500 dark:text-gray-400">User activity breakdown</p>
          </div>
        </template>
        <div class="h-64">
          <DonutChart
            :data="activityDistributionData"
            :labels="activityDistributionLabels"
            :hide-legend="true"
            :radius="0"
            :key="colorMode.value"
          />
          <div class="absolute inset-0 flex items-center justify-center">
            <div class="text-center">
              <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ totalActivities }}</p>
              <p class="text-sm text-gray-500 dark:text-gray-400">Total Actions</p>
            </div>
          </div>
        </div>
      </UiCard>
    </div>

    <!-- Detailed Usage Breakdown -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Resource Usage -->
      <UiCard>
        <template #header>
          <div>
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Resource Usage</h3>
            <p class="text-sm text-gray-500 dark:text-gray-400">Current resource consumption</p>
          </div>
        </template>
        <div class="space-y-4">
          <div v-for="resource in resourceUsage" :key="resource.name" class="space-y-2">
            <div class="flex items-center justify-between">
              <div class="flex items-center gap-2">
                <Icon :name="resource.icon" :class="['h-4 w-4', resource.iconColor]" />
                <span class="text-sm font-medium text-gray-700 dark:text-gray-300">{{ resource.name }}</span>
              </div>
              <div class="text-right">
                <span class="text-sm font-medium text-gray-900 dark:text-white">
                  {{ resource.used }} / {{ resource.limit }}
                </span>
                <span class="text-xs text-gray-500 dark:text-gray-400 ml-1">{{ resource.unit }}</span>
              </div>
            </div>
            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div
                class="h-2 rounded-full transition-all duration-300"
                :class="getUsageBarColor(resource.percentage)"
                :style="{ width: `${Math.min(resource.percentage, 100)}%` }"
              ></div>
            </div>
            <div class="flex items-center justify-between text-xs">
              <span class="text-gray-500 dark:text-gray-400">{{ resource.percentage }}% used</span>
              <span :class="getUsageColor(resource.percentage)">
                {{ resource.remaining }} {{ resource.unit }} remaining
              </span>
            </div>
          </div>
        </div>
      </UiCard>

      <!-- Usage History -->
      <UiCard>
        <template #header>
          <div>
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Usage History</h3>
            <p class="text-sm text-gray-500 dark:text-gray-400">Recent usage activities</p>
          </div>
        </template>
        <div class="space-y-3">
          <div
            v-for="entry in usageHistory"
            :key="entry.id"
            class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg"
          >
            <div class="flex items-center gap-3">
              <div :class="['w-8 h-8 rounded-full flex items-center justify-center', entry.iconBg]">
                <Icon :name="entry.icon" :class="['h-4 w-4', entry.iconColor]" />
              </div>
              <div>
                <p class="text-sm font-medium text-gray-900 dark:text-white">{{ entry.action }}</p>
                <p class="text-xs text-gray-500 dark:text-gray-400">{{ entry.resource }}</p>
              </div>
            </div>
            <div class="text-right">
              <p class="text-sm font-medium text-gray-900 dark:text-white">{{ entry.amount }}</p>
              <p class="text-xs text-gray-500 dark:text-gray-400">{{ fromNow(entry.timestamp) }}</p>
            </div>
          </div>
        </div>
      </UiCard>
    </div>

    <!-- Usage Limits and Quotas -->
    <UiCard>
      <template #header>
        <div class="flex items-center justify-between">
          <div>
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Usage Limits & Quotas</h3>
            <p class="text-sm text-gray-500 dark:text-gray-400">Current limits and quota usage</p>
          </div>
          <UiButton variant="outline" size="sm" @click="requestQuotaIncrease">
            Request Increase
          </UiButton>
        </div>
      </template>
      <div class="overflow-x-auto">
        <table class="w-full">
          <thead>
            <tr class="border-b border-gray-200 dark:border-gray-700">
              <th class="text-left py-3 px-4 font-medium text-gray-700 dark:text-gray-300">Resource</th>
              <th class="text-left py-3 px-4 font-medium text-gray-700 dark:text-gray-300">Current Usage</th>
              <th class="text-left py-3 px-4 font-medium text-gray-700 dark:text-gray-300">Limit</th>
              <th class="text-left py-3 px-4 font-medium text-gray-700 dark:text-gray-300">Usage %</th>
              <th class="text-left py-3 px-4 font-medium text-gray-700 dark:text-gray-300">Status</th>
            </tr>
          </thead>
          <tbody>
            <tr
              v-for="quota in quotaLimits"
              :key="quota.resource"
              class="border-b border-gray-100 dark:border-gray-800"
            >
              <td class="py-3 px-4">
                <div class="flex items-center gap-2">
                  <Icon :name="quota.icon" class="h-4 w-4 text-gray-500" />
                  <span class="text-sm font-medium text-gray-900 dark:text-white">{{ quota.resource }}</span>
                </div>
              </td>
              <td class="py-3 px-4">
                <span class="text-sm text-gray-700 dark:text-gray-300">{{ quota.used }} {{ quota.unit }}</span>
              </td>
              <td class="py-3 px-4">
                <span class="text-sm text-gray-700 dark:text-gray-300">
                  {{ quota.limit === -1 ? 'Unlimited' : `${quota.limit} ${quota.unit}` }}
                </span>
              </td>
              <td class="py-3 px-4">
                <div class="flex items-center gap-2">
                  <div class="w-16 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div
                      class="h-2 rounded-full transition-all duration-300"
                      :class="getUsageBarColor(quota.percentage)"
                      :style="{ width: `${Math.min(quota.percentage, 100)}%` }"
                    ></div>
                  </div>
                  <span class="text-xs text-gray-500 dark:text-gray-400">{{ quota.percentage }}%</span>
                </div>
              </td>
              <td class="py-3 px-4">
                <UiBadge :color="getQuotaStatusColor(quota.percentage)" size="sm">
                  {{ getQuotaStatus(quota.percentage) }}
                </UiBadge>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </UiCard>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { useUserStore } from '~/stores/user'
import { useColorMode } from '@vueuse/core'
import { PlatformRoles } from "~/app/features/auth/constants/roles";
import type { fromNow } from "utils/dateFormatters";

const route = useRoute()
const userStore = useUserStore()
const colorMode = useColorMode()

const userId = computed(() => route.params.id as string)
const user = computed(() => userStore.users.find(u => u.id === userId.value))

definePageMeta({
  layout: "dashboard",
  title: () => {
    const userStore = useUserStore();
    const userId = useRoute().params.id as string;
    const user = userStore.users.find(u => u.id === userId);
    return user ? `${user.name} - Usage` : "User Usage";
  },
  meta: () => ({ title: "User Usage" }),
  showPageHeader: true,
  showPageHeaderTitle: true,
  middleware: ["rbac"],
  roles: [PlatformRoles.SUPER_ADMIN],
  breadcrumbs: () => {
    const userStore = useUserStore();
    const userId = useRoute().params.id as string;
    const user = userStore.users.find(u => u.id === userId);
    if (!user) return [];
    return [
      { label: "Dashboard", href: "/dashboard" },
      { label: "Users", href: "/dashboard/users" },
      { label: user.name, href: `/dashboard/users/${userId}` },
      { label: "Usage", href: "" }
    ];
  }
});

// Mock data - replace with real API calls
const usageData = ref({
  storage: { used: 2.5, limit: 10, unit: 'GB' },
  cases: { used: 15, limit: 50, unit: 'cases' },
  documents: { used: 120, limit: 500, unit: 'files' },
  apiCalls: { used: 1250, limit: 5000, unit: 'calls' }
})

// Computed properties
const usageMetrics = computed(() => [
  {
    key: 'storage',
    label: 'Storage Used',
    value: `${usageData.value.storage.used} GB`,
    limit: usageData.value.storage.limit,
    unit: 'GB',
    percentage: (usageData.value.storage.used / usageData.value.storage.limit) * 100,
    icon: 'material-symbols:storage',
    iconBg: 'bg-blue-100 dark:bg-blue-900/20',
    iconColor: 'text-blue-600 dark:text-blue-400'
  },
  {
    key: 'cases',
    label: 'Cases Created',
    value: usageData.value.cases.used,
    limit: usageData.value.cases.limit,
    unit: 'cases',
    percentage: (usageData.value.cases.used / usageData.value.cases.limit) * 100,
    icon: 'material-symbols:folder',
    iconBg: 'bg-green-100 dark:bg-green-900/20',
    iconColor: 'text-green-600 dark:text-green-400'
  },
  {
    key: 'documents',
    label: 'Documents',
    value: usageData.value.documents.used,
    limit: usageData.value.documents.limit,
    unit: 'files',
    percentage: (usageData.value.documents.used / usageData.value.documents.limit) * 100,
    icon: 'material-symbols:description',
    iconBg: 'bg-purple-100 dark:bg-purple-900/20',
    iconColor: 'text-purple-600 dark:text-purple-400'
  },
  {
    key: 'api_calls',
    label: 'API Calls',
    value: usageData.value.apiCalls.used,
    limit: usageData.value.apiCalls.limit,
    unit: 'calls',
    percentage: (usageData.value.apiCalls.used / usageData.value.apiCalls.limit) * 100,
    icon: 'material-symbols:api',
    iconBg: 'bg-orange-100 dark:bg-orange-900/20',
    iconColor: 'text-orange-600 dark:text-orange-400'
  }
])

const resourceUsage = computed(() => [
  {
    name: 'Document Storage',
    used: usageData.value.storage.used,
    limit: usageData.value.storage.limit,
    unit: 'GB',
    percentage: (usageData.value.storage.used / usageData.value.storage.limit) * 100,
    remaining: usageData.value.storage.limit - usageData.value.storage.used,
    icon: 'material-symbols:storage',
    iconColor: 'text-blue-600 dark:text-blue-400'
  },
  {
    name: 'Active Cases',
    used: usageData.value.cases.used,
    limit: usageData.value.cases.limit,
    unit: 'cases',
    percentage: (usageData.value.cases.used / usageData.value.cases.limit) * 100,
    remaining: usageData.value.cases.limit - usageData.value.cases.used,
    icon: 'material-symbols:folder',
    iconColor: 'text-green-600 dark:text-green-400'
  },
  {
    name: 'Monthly API Calls',
    used: usageData.value.apiCalls.used,
    limit: usageData.value.apiCalls.limit,
    unit: 'calls',
    percentage: (usageData.value.apiCalls.used / usageData.value.apiCalls.limit) * 100,
    remaining: usageData.value.apiCalls.limit - usageData.value.apiCalls.used,
    icon: 'material-symbols:api',
    iconColor: 'text-orange-600 dark:text-orange-400'
  }
])

const quotaLimits = computed(() => [
  {
    resource: 'Storage',
    used: usageData.value.storage.used,
    limit: usageData.value.storage.limit,
    unit: 'GB',
    percentage: (usageData.value.storage.used / usageData.value.storage.limit) * 100,
    icon: 'material-symbols:storage'
  },
  {
    resource: 'Cases',
    used: usageData.value.cases.used,
    limit: usageData.value.cases.limit,
    unit: 'cases',
    percentage: (usageData.value.cases.used / usageData.value.cases.limit) * 100,
    icon: 'material-symbols:folder'
  },
  {
    resource: 'Documents',
    used: usageData.value.documents.used,
    limit: usageData.value.documents.limit,
    unit: 'files',
    percentage: (usageData.value.documents.used / usageData.value.documents.limit) * 100,
    icon: 'material-symbols:description'
  },
  {
    resource: 'API Calls',
    used: usageData.value.apiCalls.used,
    limit: usageData.value.apiCalls.limit,
    unit: 'calls/month',
    percentage: (usageData.value.apiCalls.used / usageData.value.apiCalls.limit) * 100,
    icon: 'material-symbols:api'
  }
])

const usageHistory = computed(() => [
  {
    id: '1',
    action: 'Document Upload',
    resource: 'case-documents.pdf',
    amount: '+2.1 MB',
    timestamp: new Date().toISOString(),
    icon: 'material-symbols:upload',
    iconBg: 'bg-green-100 dark:bg-green-900/20',
    iconColor: 'text-green-600 dark:text-green-400'
  },
  {
    id: '2',
    action: 'Case Created',
    resource: 'Smith vs. Johnson',
    amount: '+1 case',
    timestamp: new Date(Date.now() - 3600000).toISOString(),
    icon: 'material-symbols:add',
    iconBg: 'bg-blue-100 dark:bg-blue-900/20',
    iconColor: 'text-blue-600 dark:text-blue-400'
  }
])

const storageChartData = computed(() => {
  return Array.from({ length: 30 }, (_, i) => ({
    date: new Date(Date.now() - (29 - i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    value: Math.random() * 2 + 1
  }))
})

const storageChartCategories = computed(() => [
  { name: 'Storage (GB)', color: '#3B82F6' }
])

const activityDistributionData = computed(() => [40, 30, 20, 10])
const activityDistributionLabels = computed(() => ({
  labels: ['Documents', 'Cases', 'API Calls', 'Other'],
  timestamp: new Date().toISOString()
}))

const totalActivities = computed(() => 156)

// Methods
const getUsageColor = (percentage: number) => {
  if (percentage >= 90) return 'text-red-600 dark:text-red-400'
  if (percentage >= 75) return 'text-orange-600 dark:text-orange-400'
  return 'text-green-600 dark:text-green-400'
}

const getUsageBarColor = (percentage: number) => {
  if (percentage >= 90) return 'bg-red-500'
  if (percentage >= 75) return 'bg-orange-500'
  return 'bg-green-500'
}

const getQuotaStatus = (percentage: number) => {
  if (percentage >= 90) return 'Critical'
  if (percentage >= 75) return 'Warning'
  return 'Normal'
}

const getQuotaStatusColor = (percentage: number) => {
  if (percentage >= 90) return 'error'
  if (percentage >= 75) return 'warning'
  return 'success'
}

const requestQuotaIncrease = () => {
  // Handle quota increase request
  console.log('Requesting quota increase for user:', userId.value)
}

onMounted(async () => {
  // Fetch usage data
  console.log('Fetching usage data for user:', userId.value)
})
</script>
