import common from './common.json'
import time from './time.json'
import tenantList from './tenantList.json'
import tenantDetail from './tenantDetail.json'
import tenantCreate from './tenantCreate.json'
import userManagement from './userManagement.json'
import navigation from './navigation.json'
import caseManagement from './caseManagement.json'
import authPages from './authPages.json'
import ui from './ui.json'
import dashboard from './dashboard.json'
import documents from './documents.json'
import templates from './templates.json'
import notifications from './notifications.json'
import errors from './errors.json'

export default {
  common,
  time,
  tenantList,
  tenantDetail,
  tenantCreate,
  userManagement,
  navigation,
  caseManagement,
  authPages,
  ui,
  dashboard,
  documents,
  templates,
  notifications,
  errors
}
