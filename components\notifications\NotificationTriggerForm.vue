<template>
  <form @submit.prevent="handleSubmit" class="space-y-6">
    <div>
      <UiInput
        id="triggerName"
          name="triggerName"
          label="Trigger Name"
          v-model="formData.name"
          placeholder="e.g., Case Created Notification"
          required
          :error="validationErrors.name ? validationErrors.name : undefined"
        />
      </div>

      <div>
        <label for="triggerDescription" class="block text-sm font-medium text-gray-700 mb-1">
          Description (Optional)
        </label>
        <UiTextarea
          id="triggerDescription"
          v-model="formData.description"
          placeholder="Briefly describe what this trigger does"
          :rows="3"
          :error="validationErrors.description ? validationErrors.description : undefined"
        />
      </div>

      <div>
        <label for="event" class="block text-sm font-medium text-gray-700 mb-1">Event Type</label>
        <UiAutocomplete
          id="event"
          v-model="formData.event"
          api-url="/notifications/triggers/event-types"
          options-key="name"
          value-key="id"
          query-param="search"
          page-query-param="page"
          limit-query-param="limit"
          placeholder="Select or type an event type"
          :error="validationErrors.event ? validationErrors.event : undefined"
          help-text="The event that will cause this notification to fire."
        />
        <!-- TODO: Ensure UiAutocomplete handles 'required' validation display if needed, or add manual error display -->
      </div>

      <div>
        <UiSelect
          id="channel"
          label="Channel"
          v-model="formData.channel"
          :options="channelOptions"
          placeholder="Select a channel"
          required
          :error="validationErrors.channel ? validationErrors.channel : undefined"
          help-text="The delivery channel for the notification."
        />
      </div>
      <!-- <div>
        <MultiRecipientSelect label="Recipients" v-model="formData.recipients" :options="recipientOptions" :error="validationErrors.recipients ? validationErrors.recipients : undefined" />
      </div>
        -->
      
     

      <div>
      <UiAutocomplete
        lable="template"
          v-model="formData.templateId"
           api-url="/templates"
          query-param="search"
          page-query-param="page"
          limit-query-param="limit"
          :items-per-page="10"
          display-key="name"
          value-key="id"
          response-items-path="data"
          response-meta-path="meta"
          placeholder="Search for a template..."
    
        >
          <template #suggestion="{ suggestion }">
            <div class="px-3 py-2">
              <div class="font-semibold">{{ suggestion.name }} <span class="text-xs text-gray-500">({{suggestion.channel}})</span></div>
              <div class="text-sm text-gray-600">{{ suggestion.description }}</div>
              <div class="text-xs text-gray-400">ID: {{ suggestion.id }}</div>
            </div>
          </template>
        </UiAutocomplete>
      </div>
     

      <div>
        <label for="conditions" class="block text-sm font-medium text-gray-700 mb-1">
          Conditions (JSON)
        </label>
        <UiTextarea
          id="conditions"
          v-model="formData.conditionsJson"
          placeholder='e.g., { "casePriority": "High", "documentType": "Contract" }'
          :rows="5"
          :error="validationErrors.conditionsJson ? validationErrors.conditionsJson : undefined"
          help-text="Optional. Define advanced conditions in JSON format. E.g., for 'Case Created' event, a condition could be `{ 'caseType': 'Litigation' }`."
        />
      </div>

      <div>
        <UiCheckbox
          id="isActive"
          label="Active"
          v-model="formData.isActive"
          help-text="Enable or disable this notification trigger."
        />
      </div>
      
      <UiAlert v-if="notificationTriggerStore.getError" type="error" title="Form Error">
        {{ notificationTriggerStore.getError }}
      </UiAlert>

      <div class="flex justify-end space-x-3 pt-4 border-t mt-6">
        <UiButton type="button" variant="secondary" @click="handleCancel">
          Cancel
        </UiButton>
        <UiButton type="submit" :loading="isLoading">
          {{ isEditMode ? 'Save Changes' : 'Create Trigger' }}
        </UiButton>
      </div>
    </form>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue';
// UiModal import removed
import UiInput from '~/components/ui/UiInput.vue';
import UiSelect from '~/components/ui/UiSelect.vue';
import UiTextarea from '~/components/ui/UiTextarea.vue';
import UiCheckbox from '~/components/ui/UiCheckbox.vue';
import UiAlert from '~/components/ui/UiAlert.vue';
import UiButton from '~/components/ui/UiButton.vue'; // Added UiButton
import UiAutocomplete from '~/components/ui/UiAutocomplete.vue'; // Added UiAutocomplete
// import MultiRecipientSelect from './MultiRecipientSelect.vue'; // Added MultiRecipientSelect
import { useNotificationTriggerStore } from '~/stores/notificationTrigger';
import type { NotificationTrigger, NotificationTriggerPayload } from '~/types/notification';
import type { Recipient } from '~/types/recipient';
// Props
const props = defineProps<{
  // isVisible prop removed
  tenantId: string; // The ID of the current tenant (important for multi-tenancy)
  triggerToEdit?: NotificationTrigger | null; // Optional: Pass a trigger object for editing
}>();

// Emits
const emit = defineEmits(['cancel', 'save']); // Updated emits

// Store
const notificationTriggerStore = useNotificationTriggerStore();
const isLoading = computed(() => notificationTriggerStore.getIsLoading);

// Form Data
const defaultFormData = (): NotificationTriggerPayload & { conditionsJson: string } => ({
  name: '',
  description: '', // Added description
  event: '',
  channel: '', // Added channel
  recipients: [] as Recipient[],
  templateId: '',
  conditions: {},
  conditionsJson: '',
  isActive: true,
});

const formData = reactive<NotificationTriggerPayload & { conditionsJson: string }>(defaultFormData());
const validationErrors = reactive<Record<string, string | null>>({
  name: null,
  description: null, // Added description
  event: null,
  channel: null, // Added channel
  recipients: null,
  templateId: null,
  conditionsJson: null,
});

const isEditMode = computed(() => !!props.triggerToEdit);

// Options for select inputs
// eventOptions removed, will use UiAutocomplete

const channelOptions = ref([
  { value: 'email', label: 'Email' },
  { value: 'sms', label: 'SMS' },
  { value: 'in_app', label: 'In-App Notification' },
]);

const recipientOptions = ref([
  { value: 'TENANT_OWNER', label: 'Tenant Owner' },
  { value: 'ADMIN', label: 'Admin' },
  { value: 'CASE_ASSIGNEE', label: 'Case Assignee (if applicable)' },
  { value: 'DOCUMENT_OWNER', label: 'Document Owner (if applicable)' },
  { value: 'CUSTOM_EMAIL', label: 'Custom Email Address(es)' }, // Requires additional input for email addresses
  // ... more recipient types/roles from backend
]);

// This should ideally come from an API call, e.g., GET /v1/templates
const templateOptions = ref([
  { value: 'template-case-creation-id', label: 'Default Case Creation Email' },
  { value: 'template-document-ready-id', label: 'Document Ready for Review' },
  // ... actual templates from backend /v1/templates
]);

// Watch for changes in `triggerToEdit` prop to populate form for editing
watch(() => props.triggerToEdit, (newVal) => {
  if (newVal) {
    formData.name = newVal.name;
    formData.description = newVal.description || '';
    formData.event = newVal.event;
    formData.channel = newVal.channel;
    formData.recipients = newVal.recipients;
    formData.templateId = newVal.templateId;
    formData.conditions = newVal.conditions;
    formData.conditionsJson = JSON.stringify(newVal.conditions, null, 2);
    formData.isActive = newVal.isActive;
  } else {
    Object.assign(formData, defaultFormData());
  }
}, { immediate: true });

// Input validation
const validateForm = () => {
  let isValid = true;
  Object.keys(validationErrors).forEach(key => (validationErrors[key] = null)); // Clear previous errors

  if (!formData.name.trim()) {
    validationErrors.name = 'Trigger Name is required.';
    isValid = false;
  }
  if (!formData.event) {
    validationErrors.event = 'Event Type is required.';
    isValid = false;
  }
  if (!formData.channel) {
    validationErrors.channel = 'Channel is required.';
    isValid = false;
  }
  // if (formData.recipients.length === 0) {
  //   validationErrors.recipients = 'At least one recipient type is required.';
  //   isValid = false;
  // }
  if (!formData.templateId) {
    validationErrors.templateId = 'Notification Template is required.';
    isValid = false;
  }

  try {
    if (formData.conditionsJson.trim()) {
      formData.conditions = JSON.parse(formData.conditionsJson);
    } else {
      formData.conditions = {};
    }
  } catch (e) {
    validationErrors.conditionsJson = 'Invalid JSON format for Conditions.';
    isValid = false;
  }

  return isValid;
};

// Handle form submission
const handleSubmit = async () => {
  if (!validateForm()) {
    return;
  }

  const payload: NotificationTriggerPayload = {
    name: formData.name,
    description: formData.description || undefined, // Ensure optional field is handled
    event: formData.event,
    channel: formData.channel,
    recipients: formData.recipients,
    templateId: formData.templateId,
    conditions: formData.conditions,
    isActive: formData.isActive,
  };

  try {
    if (isEditMode.value && props.triggerToEdit?.id) {
      await notificationTriggerStore.updateNotificationTrigger(props.triggerToEdit.id, payload);
    } else {
      await notificationTriggerStore.createNotificationTrigger(payload);
    }
    emit('save', payload); // Emit save with payload
    // Toast notifications are handled by the store actions
  } catch (error) {
    // Errors are caught and displayed by the store/toast as well.
    // Additional component-specific error handling can go here if needed.
    console.error('Form submission error:', error);
  }
};

const handleCancel = () => { // Renamed from closeModal
  emit('cancel');
  Object.assign(formData, defaultFormData());
  Object.keys(validationErrors).forEach(key => (validationErrors[key] = null));
};
</script>

<style scoped>
/* Add component-specific styles here if needed, or rely on Tailwind CSS utilities */
</style>