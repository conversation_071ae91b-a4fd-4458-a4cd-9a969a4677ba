<template>
  <div class="space-y-6">
    <!-- <PERSON> Header -->
    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
      <div class="flex items-center gap-4">
        <!-- User Avatar -->
        <div class="flex-shrink-0">
          <div v-if="selectedUser?.avatarUrl" class="w-12 h-12 rounded-full overflow-hidden">
            <img
              :src="selectedUser.avatarUrl"
              :alt="`${selectedUser.name} avatar`"
              class="w-full h-full object-cover"
            />
          </div>
          <div v-else class="w-12 h-12 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center">
            <Icon name="material-symbols:person" class="h-6 w-6 text-blue-600 dark:text-blue-400" />
          </div>
        </div>
        <div>
          <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
            Edit {{ selectedUser?.name || 'User' }}
          </h1>
          <p class="text-gray-600 dark:text-gray-400 mt-1">
            Update user details, roles, and settings
          </p>
        </div>
      </div>
      <div class="flex items-center gap-3">
        <UiButton
          variant="ghost"
          size="sm"
          leading-icon="material-symbols:arrow-back"
          @click="goBackToUser"
        >
          Back to User
        </UiButton>
        <UiButton
          variant="secondary"
          size="sm"
          leading-icon="material-symbols:preview"
          @click="previewChanges"
          :disabled="!hasChanges"
        >
          Preview
        </UiButton>
      </div>
    </div>

    <!-- Form Container -->
    <form @submit.prevent="handleSubmit" class="space-y-6">
      <!-- Main Content Grid -->
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Form -->
        <div class="lg:col-span-2 space-y-6">
          <!-- Basic Information -->
          <UiCard>
            <template #header>
              <div class="flex items-center gap-3">
                <div class="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
                  <Icon name="material-symbols:person" class="h-5 w-5 text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                  <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Basic Information</h3>
                  <p class="text-sm text-gray-500 dark:text-gray-400">Update user name, email, and basic details</p>
                </div>
              </div>
            </template>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <UiInput
                  id="userName"
                  name="userName"
                  v-model="formData.name"
                  label="Full Name"
                  placeholder="Enter full name"
                  required
                  :error="errors.name"
                  leading-icon="material-symbols:person"
                />
              </div>
              <div>
                <UiInput
                  id="userEmail"
                  name="userEmail"
                  type="email"
                  v-model="formData.email"
                  label="Email Address"
                  placeholder="Enter email address"
                  required
                  :error="errors.email"
                  leading-icon="material-symbols:email"
                />
              </div>
              <div>
                <UiInput
                  id="userTitle"
                  name="userTitle"
                  v-model="formData.title"
                  label="Job Title"
                  placeholder="Enter job title"
                  :error="errors.title"
                  leading-icon="material-symbols:work"
                />
              </div>
            </div>
          </UiCard>

          <!-- Avatar Upload -->
          <UiCard>
            <template #header>
              <div class="flex items-center gap-3">
                <div class="p-2 bg-purple-100 dark:bg-purple-900/20 rounded-lg">
                  <Icon name="material-symbols:account-circle" class="h-5 w-5 text-purple-600 dark:text-purple-400" />
                </div>
                <div>
                  <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Profile Avatar</h3>
                  <p class="text-sm text-gray-500 dark:text-gray-400">Upload or update the user avatar</p>
                </div>
              </div>
            </template>
            <div class="space-y-4">
              <!-- Current Avatar Preview -->
              <div v-if="selectedUser?.avatarUrl || formData.avatarPreview" class="flex items-center gap-4">
                <div class="w-16 h-16 rounded-full overflow-hidden bg-gray-100 dark:bg-gray-700">
                  <img
                    :src="formData.avatarPreview || selectedUser?.avatarUrl"
                    :alt="formData.name || 'User avatar'"
                    class="w-full h-full object-cover"
                  />
                </div>
                <div>
                  <p class="text-sm font-medium text-gray-900 dark:text-white">Current Avatar</p>
                  <p class="text-xs text-gray-500 dark:text-gray-400">
                    {{ formData.avatarFile?.name || 'Uploaded avatar' }}
                  </p>
                </div>
                <UiButton
                  type="button"
                  variant="ghost"
                  size="sm"
                  @click="removeAvatar"
                >
                  Remove
                </UiButton>
              </div>

              <!-- Avatar Upload -->
              <UiLogoUpload
                v-model="formData.avatarFile"
                label="Upload New Avatar"
                :max-size="2 * 1024 * 1024"
                help-text="Upload an avatar for the user. Recommended size: 200x200px, max 2MB"
                @change="handleAvatarChange"
                @error="handleAvatarError"
              />
            </div>
          </UiCard>

          <!-- Roles & Permissions -->
          <UiCard>
            <template #header>
              <div class="flex items-center gap-3">
                <div class="p-2 bg-green-100 dark:bg-green-900/20 rounded-lg">
                  <Icon name="material-symbols:admin-panel-settings" class="h-5 w-5 text-green-600 dark:text-green-400" />
                </div>
                <div>
                  <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Roles & Permissions</h3>
                  <p class="text-sm text-gray-500 dark:text-gray-400">Configure user roles and access permissions</p>
                </div>
              </div>
            </template>
            <div class="space-y-6">
              <div>
                <UiSelect
                  id="roles"
                  v-model="formData.roles"
                  label="User Roles"
                  :options="roleOptions"
                  multiple
                  required
                  :error="errors.roles"
                  leading-icon="material-symbols:group"
                  help-text="Select one or more roles for this user"
                />
              </div>

              <!-- Tenant Assignment -->
              <div>
                <UiSelect
                  id="tenantId"
                  v-model="formData.tenantId"
                  label="Tenant Assignment"
                  :options="tenantOptions"
                  :error="errors.tenantId"
                  leading-icon="material-symbols:business"
                  help-text="Assign user to a specific tenant (optional for platform users)"
                />
              </div>

              <!-- Role Preview -->
              <div v-if="selectedRolePermissions.length > 0" class="p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-3">Role Permissions</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                  <div
                    v-for="permission in selectedRolePermissions"
                    :key="permission.name"
                    class="flex items-center gap-2 text-sm"
                  >
                    <Icon
                      name="material-symbols:check-circle"
                      class="h-4 w-4 text-green-500"
                    />
                    <span class="text-gray-900 dark:text-white">
                      {{ permission.name }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </UiCard>

          <!-- Security Settings -->
          <UiCard>
            <template #header>
              <div class="flex items-center gap-3">
                <div class="p-2 bg-orange-100 dark:bg-orange-900/20 rounded-lg">
                  <Icon name="material-symbols:security" class="h-5 w-5 text-orange-600 dark:text-orange-400" />
                </div>
                <div>
                  <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Security Settings</h3>
                  <p class="text-sm text-gray-500 dark:text-gray-400">Configure user security and authentication settings</p>
                </div>
              </div>
            </template>
            <div class="space-y-6">
              <!-- User Status -->
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                  User Status
                </label>
                <div class="space-y-3">
                  <div class="flex items-center gap-3">
                    <UiCheckbox
                      id="isActive"
                      v-model="formData.isActive"
                      label="Active"
                    />
                    <span class="text-sm text-gray-500 dark:text-gray-400">
                      Allow user to access the platform
                    </span>
                  </div>
                  <div v-if="!formData.isActive" class="p-3 bg-yellow-50 dark:bg-yellow-900/10 rounded-lg">
                    <div class="flex items-center gap-2">
                      <Icon name="material-symbols:warning" class="h-4 w-4 text-yellow-600 dark:text-yellow-400" />
                      <p class="text-sm text-yellow-800 dark:text-yellow-200">
                        Deactivating this user will prevent them from accessing the platform.
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Security Options -->
              <div class="space-y-4">
                <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300">Security Options</h4>
                <div class="space-y-3">
                  <UiCheckbox
                    id="otpEnabled"
                    v-model="formData.otpEnabled"
                    label="Enable Two-Factor Authentication"
                  />
                  <UiCheckbox
                    id="forcePasswordReset"
                    v-model="formData.forcePasswordReset"
                    label="Force Password Reset on Next Login"
                  />
                  <UiCheckbox
                    id="emailNotifications"
                    v-model="formData.emailNotifications"
                    label="Enable Email Notifications"
                  />
                </div>
              </div>
            </div>
          </UiCard>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
          <!-- Save Actions -->
          <UiCard>
            <template #header>
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Actions</h3>
            </template>
            <div class="space-y-3">
              <UiButton
                type="submit"
                :loading="isSubmitting"
                :disabled="!hasChanges"
                class="w-full"
              >
                Save Changes
              </UiButton>
              <UiButton
                type="button"
                variant="secondary"
                @click="resetForm"
                :disabled="isSubmitting"
                class="w-full"
              >
                Reset Form
              </UiButton>
              <UiButton
                type="button"
                variant="ghost"
                @click="goBackToUser"
                :disabled="isSubmitting"
                class="w-full"
              >
                Cancel
              </UiButton>
            </div>
          </UiCard>

          <!-- Change Summary -->
          <UiCard v-if="hasChanges">
            <template #header>
              <div class="flex items-center gap-2">
                <Icon name="material-symbols:edit-note" class="h-5 w-5 text-blue-600 dark:text-blue-400" />
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Pending Changes</h3>
              </div>
            </template>
            <div class="space-y-3">
              <div
                v-for="change in pendingChanges"
                :key="change.field"
                class="flex items-center justify-between text-sm"
              >
                <span class="text-gray-600 dark:text-gray-400">{{ change.label }}</span>
                <div class="text-right">
                  <p class="text-gray-900 dark:text-white font-medium">{{ change.newValue }}</p>
                  <p class="text-gray-500 dark:text-gray-400 text-xs line-through">{{ change.oldValue }}</p>
                </div>
              </div>
            </div>
          </UiCard>

          <!-- Current Information -->
          <UiCard>
            <template #header>
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Current Information</h3>
            </template>
            <div class="space-y-4">
              <div>
                <label class="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">User ID</label>
                <p class="text-sm text-gray-900 dark:text-white mt-1 font-mono">{{ selectedUser?.id }}</p>
              </div>
              <div>
                <label class="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">Created</label>
                <p class="text-sm text-gray-900 dark:text-white mt-1">{{ formatDate(selectedUser?.createdAt) }}</p>
              </div>
              <div>
                <label class="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">Last Updated</label>
                <p class="text-sm text-gray-900 dark:text-white mt-1">{{ formatDate(selectedUser?.updatedAt) }}</p>
              </div>
              <div>
                <label class="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">Last Login</label>
                <p class="text-sm text-gray-900 dark:text-white mt-1">{{ formatDate(selectedUser?.lastLoginAt) || 'Never' }}</p>
              </div>
              <div>
                <label class="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">Current Status</label>
                <UiBadge :variant="selectedUser?.isActive ? 'success' : 'warning'" class="mt-1">
                  {{ selectedUser?.isActive ? 'Active' : 'Inactive' }}
                </UiBadge>
              </div>
            </div>
          </UiCard>

          <!-- Help & Documentation -->
          <UiCard>
            <template #header>
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Need Help?</h3>
            </template>
            <div class="space-y-3">
              <UiButton
                variant="outline"
                size="sm"
                leading-icon="material-symbols:help"
                @click="openDocumentation"
                class="w-full"
              >
                View Documentation
              </UiButton>
              <UiButton
                variant="outline"
                size="sm"
                leading-icon="material-symbols:support"
                @click="contactSupport"
                class="w-full"
              >
                Contact Support
              </UiButton>
            </div>
          </UiCard>
        </div>
      </div>

      <!-- Error Display -->
      <div v-if="submitError" class="mt-6">
        <UiAlert type="error" :title="submitError">
          Please review the form and try again.
        </UiAlert>
      </div>

      <!-- Success Display -->
      <div v-if="successMessage" class="mt-6">
        <UiAlert type="success" :title="successMessage">
          User has been updated successfully.
        </UiAlert>
      </div>
    </form>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useUserStore, type UpdateUserPayload, type User } from '~/stores/user'
import { PlatformRoles, TenantRoles } from "~/app/features/auth/constants/roles";

// Page meta
definePageMeta({
  layout: 'dashboard',
  title: 'Edit User',
  description: 'Edit user details and settings',
  pageHeaderIcon: 'material-symbols:person',
  showRealTimeStatus: false,
  autoRefreshEnabled: false,
  refreshInterval: 0,
  isLoading: false,
  showActionsMenu: true,
  middleware: ['rbac'],
  roles: [PlatformRoles.SUPER_ADMIN],
  breadcrumb: [
    { label: 'Dashboard', href: '/dashboard' },
    { label: 'Users', href: '/dashboard/users' },
    { label: 'Edit' },
  ],
})

// Composables
const route = useRoute()
const router = useRouter()
const userStore = useUserStore()

// Get user ID from route
const userId = computed(() => route.params.id as string)

// Form data interface
interface UserFormData {
  name: string
  email: string
  title: string
  roles: string[]
  tenantId: string | null
  avatarFile: File | null
  avatarPreview: string | null
  isActive: boolean
  otpEnabled: boolean
  forcePasswordReset: boolean
  emailNotifications: boolean
}

// Reactive state
const formData = ref<UserFormData>({
  name: '',
  email: '',
  title: '',
  roles: [],
  tenantId: null,
  avatarFile: null,
  avatarPreview: null,
  isActive: true,
  otpEnabled: false,
  forcePasswordReset: false,
  emailNotifications: true,
})

const originalData = ref<UserFormData | null>(null)
const isSubmitting = ref(false)
const submitError = ref('')
const successMessage = ref('')
const errors = ref<Record<string, string>>({})

// Options
const roleOptions = [
  { value: PlatformRoles.SUPER_ADMIN, label: 'Super Admin' },
  { value: TenantRoles.ADMIN, label: 'Admin' },
  { value: TenantRoles.LAWYER, label: 'Lawyer' },
  { value: TenantRoles.PARALEGAL, label: 'Paralegal' },
  { value: TenantRoles.CLIENT, label: 'Client' },
]

const tenantOptions = [
  { value: null, label: 'No Tenant (Platform User)' },
  // This would be populated from tenant store
]

// Mock role permissions (replace with real data)
const rolePermissions = {
  [PlatformRoles.SUPER_ADMIN]: [
    { name: 'Full platform access' },
    { name: 'Manage all tenants' },
    { name: 'System administration' },
  ],
  [TenantRoles.ADMIN]: [
    { name: 'Tenant administration' },
    { name: 'User management' },
    { name: 'Case management' },
  ],
  [TenantRoles.LAWYER]: [
    { name: 'Case management' },
    { name: 'Document access' },
    { name: 'Client communication' },
  ],
  [TenantRoles.PARALEGAL]: [
    { name: 'Case assistance' },
    { name: 'Document preparation' },
    { name: 'Research tasks' },
  ],
  [TenantRoles.CLIENT]: [
    { name: 'View own cases' },
    { name: 'Upload documents' },
    { name: 'Communication access' },
  ],
}

// Computed properties
const selectedUser = computed(() => userStore.users.find(u => u.id === userId.value))

const selectedRolePermissions = computed(() => {
  const permissions: Array<{ name: string }> = []
  formData.value.roles.forEach(role => {
    const rolePerms = rolePermissions[role as keyof typeof rolePermissions] || []
    permissions.push(...rolePerms)
  })
  return permissions
})

const hasChanges = computed(() => {
  if (!originalData.value) return false

  return Object.keys(formData.value).some(key => {
    const formKey = key as keyof UserFormData
    if (formKey === 'avatarFile' || formKey === 'avatarPreview') return false
    if (formKey === 'roles') {
      return JSON.stringify(formData.value[formKey]) !== JSON.stringify(originalData.value![formKey])
    }
    return formData.value[formKey] !== originalData.value![formKey]
  }) || formData.value.avatarFile !== null
})

const pendingChanges = computed(() => {
  if (!originalData.value || !hasChanges.value) return []

  const changes: Array<{ field: string; label: string; oldValue: string; newValue: string }> = []

  const fieldLabels: Record<string, string> = {
    name: 'Full Name',
    email: 'Email',
    title: 'Title',
    roles: 'Roles',
    tenantId: 'Tenant',
    isActive: 'Status',
    otpEnabled: 'Two-Factor Auth',
    forcePasswordReset: 'Force Password Reset',
    emailNotifications: 'Email Notifications',
  }

  Object.keys(fieldLabels).forEach(field => {
    const formKey = field as keyof UserFormData
    let oldValue = originalData.value![formKey]
    let newValue = formData.value[formKey]

    if (field === 'roles') {
      oldValue = (oldValue as string[]).join(', ')
      newValue = (newValue as string[]).join(', ')
    }

    if (oldValue !== newValue) {
      changes.push({
        field,
        label: fieldLabels[field],
        oldValue: String(oldValue),
        newValue: String(newValue),
      })
    }
  })

  if (formData.value.avatarFile) {
    changes.push({
      field: 'avatar',
      label: 'Avatar',
      oldValue: 'Current avatar',
      newValue: 'New avatar',
    })
  }

  return changes
})

// Methods
const loadUserData = () => {
  if (!selectedUser.value) return

  const user = selectedUser.value
  const data: UserFormData = {
    name: user.name,
    email: user.email,
    title: user.title || '',
    roles: user.roles,
    tenantId: user.tenantId,
    avatarFile: null,
    avatarPreview: null,
    isActive: user.isActive,
    otpEnabled: user.otpEnabled,
    forcePasswordReset: false,
    emailNotifications: true,
  }

  formData.value = { ...data }
  originalData.value = { ...data }
}

const validateForm = () => {
  errors.value = {}

  if (!formData.value.name.trim()) {
    errors.value.name = 'Full name is required'
  }

  if (!formData.value.email.trim()) {
    errors.value.email = 'Email address is required'
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.value.email)) {
    errors.value.email = 'Please enter a valid email address'
  }

  if (formData.value.roles.length === 0) {
    errors.value.roles = 'At least one role is required'
  }

  return Object.keys(errors.value).length === 0
}

const handleSubmit = async () => {
  submitError.value = ''
  successMessage.value = ''

  if (!validateForm()) {
    submitError.value = 'Please fix the validation errors'
    return
  }

  isSubmitting.value = true

  try {
    const payload: UpdateUserPayload = {
      name: formData.value.name,
      email: formData.value.email,
      title: formData.value.title,
      roles: formData.value.roles,
      isActive: formData.value.isActive,
      otpEnabled: formData.value.otpEnabled,
    }

    // Handle avatar upload if there's a new file
    if (formData.value.avatarFile) {
      // This would typically use a separate avatar upload method
      console.log('Uploading new avatar:', formData.value.avatarFile.name)
    }

    await userStore.updateUser(userId.value, payload)

    successMessage.value = 'User updated successfully'

    // Update original data to reflect saved state
    originalData.value = { ...formData.value, avatarFile: null }

    // Redirect after a delay
    setTimeout(() => {
      router.push(`/dashboard/users/${userId.value}`)
    }, 2000)

  } catch (error: any) {
    submitError.value = error.message || 'Failed to update user'
  } finally {
    isSubmitting.value = false
  }
}

const resetForm = () => {
  if (originalData.value) {
    formData.value = { ...originalData.value }
  }
  errors.value = {}
  submitError.value = ''
  successMessage.value = ''
}

const handleAvatarChange = (file: File | null) => {
  if (file) {
    const reader = new FileReader()
    reader.onload = (e) => {
      formData.value.avatarPreview = e.target?.result as string
    }
    reader.readAsDataURL(file)
  }
}

const handleAvatarError = (error: string) => {
  errors.value.avatar = error
}

const removeAvatar = () => {
  formData.value.avatarFile = null
  formData.value.avatarPreview = null
}

const previewChanges = () => {
  console.log('Previewing changes:', pendingChanges.value)
}

const goBackToUser = () => {
  if (hasChanges.value) {
    if (confirm('You have unsaved changes. Are you sure you want to leave?')) {
      router.push(`/dashboard/users/${userId.value}`)
    }
  } else {
    router.push(`/dashboard/users/${userId.value}`)
  }
}

const openDocumentation = () => {
  window.open('/docs/user-management', '_blank')
}

const contactSupport = () => {
  router.push('/dashboard/support')
}

const formatDate = (dateString: string | undefined) => {
  if (!dateString) return 'N/A'
  return new Date(dateString).toLocaleDateString()
}

// Lifecycle
onMounted(async () => {
  // Fetch user details if not already loaded
  if (!selectedUser.value) {
    await userStore.fetchAllUsers()
  }

  // Load user data into form
  loadUserData()
})

// Watch for user changes
watch(selectedUser, () => {
  if (selectedUser.value) {
    loadUserData()
  }
})

// Watch for user store errors
watch(
  () => userStore.error,
  (newError) => {
    if (newError) {
      submitError.value = newError
    }
  }
)
</script>