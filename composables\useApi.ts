// composables/useApi.ts
import type { AxiosInstance, AxiosResponse } from 'axios';
import { useNuxtApp } from '#app';

/**
 * Custom composable for making authenticated API requests using the pre-configured Axios instance.
 *
 * This composable provides a convenient way to access the Axios instance created and configured
 * in `plugins/api.client.ts`, which includes:
 * - Proxy requests through Nuxt server (/api/proxy) to avoid CORS issues
 * - Automatic handling of HttpOnly cookies for authentication
 * - Centralized handling of 401 Unauthorized errors
 * - Network error handling and toast notifications
 * - `Accept-Language` header for internationalization
 */
export const useApi = () => {
  // Access the Axios instance provided by the api.client.ts plugin
  const { $api } = useNuxtApp();

  // Return an object with simplified methods for common HTTP verbs.
  // These methods directly use the configured Axios instance.
  // We explicitly type the response to ensure type safety.
  return {
    /**
     * Performs a GET request.
     * @param url The API endpoint URL (relative to base URL).
     * @param config Optional Axios request configuration.
     * @returns A promise that resolves to the data from the API response.
     */
    get: async <T>(url: string, config?: Parameters<AxiosInstance['get']>[1]): Promise<T> => {
      const response: AxiosResponse<T> = await $api.get<T>(url, config);
      return response.data;
    },

    /**
     * Performs a POST request.
     * @param url The API endpoint URL (relative to base URL).
     * @param data The data to send in the request body.
     * @param config Optional Axios request configuration.
     * @returns A promise that resolves to the data from the API response.
     */
    post: async <T>(url: string, data?: any, config?: Parameters<AxiosInstance['post']>[2]): Promise<T> => {
      const response: AxiosResponse<T> = await $api.post<T>(url, data, config);
      return response.data;
    },

    /**
     * Performs a PUT request.
     * @param url The API endpoint URL (relative to base URL).
     * @param data The data to send in the request body.
     * @param config Optional Axios request configuration.
     * @returns A promise that resolves to the data from the API response.
     */
    put: async <T>(url: string, data?: any, config?: Parameters<AxiosInstance['put']>[2]): Promise<T> => {
      const response: AxiosResponse<T> = await $api.put<T>(url, data, config);
      return response.data;
    },

    /**
     * Performs a PATCH request.
     * @param url The API endpoint URL (relative to base URL).
     * @param data The data to send in the request body.
     * @param config Optional Axios request configuration.
     * @returns A promise that resolves to the data from the API response.
     */
    patch: async <T>(url: string, data?: any, config?: Parameters<AxiosInstance['patch']>[2]): Promise<T> => {
      const response: AxiosResponse<T> = await $api.patch<T>(url, data, config);
      return response.data;
    },

    /**
     * Performs a DELETE request.
     * @param url The API endpoint URL (relative to base URL).
     * @param config Optional Axios request configuration.
     * @returns A promise that resolves to the data from the API response.
     */
    delete: async <T>(url: string, config?: Parameters<AxiosInstance['delete']>[1]): Promise<T> => {
      const response: AxiosResponse<T> = await $api.delete<T>(url, config);
      return response.data;
    },

    /**
     * Provides direct access to the configured Axios instance.
     * Useful for more complex requests or when you need full Axios control.
     */
    axiosInstance: $api,
  };
};