/**
 * Chart Components Type Definitions
 * 
 * Comprehensive TypeScript interfaces for all chart components
 */

// Common Chart Types
export type ChartTheme = 'light' | 'dark' | 'auto'
export type LegendPosition = 'top' | 'bottom' | 'left' | 'right'
export type ChartSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl' | string

// Color Palette
export interface ColorPalette {
  primary: string[]
  secondary: string[]
  success: string[]
  warning: string[]
  danger: string[]
  info: string[]
}

// Base Chart Props
export interface BaseChartProps {
  title?: string
  subtitle?: string
  height?: string
  theme?: ChartTheme
  responsive?: boolean
  maintainAspectRatio?: boolean
  clickable?: boolean
  hoverable?: boolean
  showLegend?: boolean
  legendPosition?: LegendPosition
  showGrid?: boolean
  showXAxis?: boolean
  showYAxis?: boolean
  animated?: boolean
  animationDuration?: number
  loading?: boolean
  error?: string
  containerClass?: string
}

// NuxtCharts Common Types
export interface BulletLegendItemInterface {
  name: string
  color: string
}

// Chart-specific Props (NuxtCharts-based)
export interface BarChartProps {
  data: any[]
  height?: number
  categories: Record<string, BulletLegendItemInterface>
  yAxis?: string[]
  xNumTicks?: number
  radius?: number
  yGridLine?: boolean
  xFormatter?: (i: number) => string
  yFormatter?: (i: number) => number
  legendPosition?: 'top' | 'bottom' | 'left' | 'right'
  hideLegend?: boolean
  colorModeKey?: string
}

export interface LineChartProps {
  data: any[]
  height?: number
  yLabel?: string
  xNumTicks?: number
  yNumTicks?: number
  categories: Record<string, BulletLegendItemInterface>
  xFormatter?: (i: number) => string | number
  yGridLine?: boolean
  curveType?: 'linear' | 'monotoneX' | 'natural' | 'step'
  legendPosition?: 'top' | 'bottom' | 'left' | 'right'
  hideLegend?: boolean
}

export interface AreaChartProps {
  data: any[]
  height?: number
  categories: Record<string, BulletLegendItemInterface>
  yGridLine?: boolean
  xFormatter?: (i: number) => string | number
  curveType?: 'linear' | 'monotoneX' | 'natural' | 'step'
  legendPosition?: 'top' | 'bottom' | 'left' | 'right'
  hideLegend?: boolean
  colorModeKey?: string
}

// Donut Chart Types
export interface DonutDataItem {
  color: string
  name: string
  value: number
}

export interface DonutChartProps {
  data: DonutDataItem[]
  height?: number
  hideLegend?: boolean
  radius?: number
  showLegend?: boolean
  centerValue?: number | string
  centerLabel?: string
  centerValueFormatter?: (value: number | string) => string
  valueFormatter?: (value: number) => string
}

// Progress Circle Types
export interface GradientConfig {
  from: string
  to: string
}

export interface ProgressCircleProps {
  value: number
  size?: number
  strokeWidth?: number
  showLabel?: boolean
}

// Progress Linear Types
export interface ProgressLinearProps {
  value: number
  bufferValue?: number
  height?: number
  label?: string
  minLabel?: string
  maxLabel?: string
  showLabel?: boolean
  showValue?: boolean
  showPercentage?: boolean
  showDetails?: boolean
  animated?: boolean
  indeterminate?: boolean
  gradient?: GradientConfig
}

// Tracker Types
export interface TrackerSubItem {
  id?: string | number
  title: string
  completed: boolean
}

export interface TrackerItem {
  id?: string | number
  title: string
  description?: string
  status: 'pending' | 'in-progress' | 'completed' | 'blocked'
  priority?: 'low' | 'medium' | 'high' | 'urgent'
  dueDate?: Date | string
  subItems?: TrackerSubItem[]
}

export interface TrackerProps {
  items: TrackerItem[]
  title?: string
  subtitle?: string
  showSummary?: boolean
  showProgressBar?: boolean
  showActions?: boolean
  showConnectors?: boolean
  clickable?: boolean
  loading?: boolean
  containerClass?: string
}

// Event Types
export interface ChartClickEvent {
  event: MouseEvent
  elements: any[]
  chart: any
}

export interface ChartHoverEvent {
  event: MouseEvent
  elements: any[]
  chart: any
}

export interface TrackerItemEvent {
  item: TrackerItem
  index: number
}

export interface TrackerSubItemEvent {
  item: TrackerItem
  subIndex: number
}

// Chart Configuration Types
export interface ChartConfiguration {
  defaultColors: string[]
  themes: {
    light: {
      textColor: string
      gridColor: string
      backgroundColor: string
    }
    dark: {
      textColor: string
      gridColor: string
      backgroundColor: string
    }
  }
  animation: {
    duration: number
    easing: string
  }
  responsive: {
    breakpoints: {
      sm: number
      md: number
      lg: number
      xl: number
    }
  }
}

// Utility Types
export type ChartDataPoint = {
  x: number | string
  y: number
  label?: string
}

export type ChartAxis = {
  type: 'linear' | 'logarithmic' | 'category' | 'time'
  position: 'top' | 'bottom' | 'left' | 'right'
  title?: string
  min?: number
  max?: number
  stepSize?: number
}

export type ChartTooltip = {
  enabled: boolean
  backgroundColor: string
  titleColor: string
  bodyColor: string
  borderColor: string
  borderWidth: number
  cornerRadius: number
  displayColors: boolean
}

// All chart component props are exported individually above

// Default configurations
export const DEFAULT_COLORS: string[] = [
  '#3b82f6', // blue-500
  '#10b981', // emerald-500
  '#f59e0b', // amber-500
  '#ef4444', // red-500
  '#8b5cf6', // violet-500
  '#06b6d4', // cyan-500
  '#84cc16', // lime-500
  '#f97316', // orange-500
  '#ec4899', // pink-500
  '#6b7280'  // gray-500
]

export const CHART_THEMES: ChartConfiguration['themes'] = {
  light: {
    textColor: '#374151',
    gridColor: '#e5e7eb',
    backgroundColor: '#ffffff'
  },
  dark: {
    textColor: '#d1d5db',
    gridColor: '#374151',
    backgroundColor: '#1f2937'
  }
}

export const DEFAULT_ANIMATION = {
  duration: 750,
  easing: 'easeInOutQuart'
}

// Helper type guards for NuxtCharts data
export const isChartDataArray = (data: any): data is any[] => {
  return Array.isArray(data) && data.length > 0
}

export const isBulletLegendItem = (item: any): item is BulletLegendItemInterface => {
  return item && typeof item.name === 'string' && typeof item.color === 'string'
}

export const isDonutDataItem = (item: any): item is DonutDataItem => {
  return item && typeof item.label === 'string' && typeof item.value === 'number'
}

export const isTrackerItem = (item: any): item is TrackerItem => {
  return item && typeof item.title === 'string' && typeof item.status === 'string'
}
