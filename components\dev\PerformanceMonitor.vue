<!--
  Performance Monitor Component
  
  Development-only component for monitoring application performance,
  memory usage, and rendering metrics
-->

<template>
  <div
    v-if="isDevelopment && isVisible"
    class="fixed bottom-4 left-4 bg-black bg-opacity-90 text-white text-xs rounded-lg p-3 font-mono z-50 max-w-xs"
    @click="toggleExpanded"
  >
    <!-- Header -->
    <div class="flex items-center justify-between mb-2">
      <span class="font-semibold text-green-400">Performance Monitor</span>
      <div class="flex items-center space-x-2">
        <button
          type="button"
          class="text-gray-400 hover:text-white transition-colors duration-200"
          @click.stop="toggleExpanded"
          :aria-label="isExpanded ? 'Collapse monitor' : 'Expand monitor'"
        >
          <Icon
            :name="isExpanded ? 'heroicons:chevron-down' : 'heroicons:chevron-up'"
            class="h-3 w-3"
          />
        </button>
        <button
          type="button"
          class="text-gray-400 hover:text-red-400 transition-colors duration-200"
          @click.stop="hide"
          aria-label="Hide monitor"
        >
          <Icon name="heroicons:x-mark" class="h-3 w-3" />
        </button>
      </div>
    </div>

    <!-- Compact view -->
    <div v-if="!isExpanded" class="space-y-1">
      <div class="flex justify-between">
        <span>FPS:</span>
        <span :class="getFpsColor(currentFps)">{{ currentFps }}</span>
      </div>
      <div class="flex justify-between">
        <span>Memory:</span>
        <span>{{ formatMemory(memoryUsage.usedJSHeapSize) }}</span>
      </div>
    </div>

    <!-- Expanded view -->
    <div v-else class="space-y-2">
      <!-- FPS -->
      <div class="border-b border-gray-700 pb-2">
        <div class="flex justify-between mb-1">
          <span>FPS:</span>
          <span :class="getFpsColor(currentFps)">{{ currentFps }}</span>
        </div>
        <div class="w-full bg-gray-700 rounded-full h-1">
          <div
            class="h-1 rounded-full transition-all duration-300"
            :class="getFpsBarColor(currentFps)"
            :style="{ width: `${Math.min(currentFps / 60 * 100, 100)}%` }"
          ></div>
        </div>
      </div>

      <!-- Memory Usage -->
      <div class="border-b border-gray-700 pb-2">
        <div class="text-yellow-400 mb-1">Memory Usage</div>
        <div class="space-y-1 text-xs">
          <div class="flex justify-between">
            <span>Used:</span>
            <span>{{ formatMemory(memoryUsage.usedJSHeapSize) }}</span>
          </div>
          <div class="flex justify-between">
            <span>Total:</span>
            <span>{{ formatMemory(memoryUsage.totalJSHeapSize) }}</span>
          </div>
          <div class="flex justify-between">
            <span>Limit:</span>
            <span>{{ formatMemory(memoryUsage.jsHeapSizeLimit) }}</span>
          </div>
        </div>
        <div class="w-full bg-gray-700 rounded-full h-1 mt-1">
          <div
            class="bg-yellow-500 h-1 rounded-full transition-all duration-300"
            :style="{ width: `${getMemoryPercentage()}%` }"
          ></div>
        </div>
      </div>

      <!-- Render Performance -->
      <div class="border-b border-gray-700 pb-2">
        <div class="text-blue-400 mb-1">Render Performance</div>
        <div class="space-y-1 text-xs">
          <div class="flex justify-between">
            <span>Components:</span>
            <span>{{ componentCount }}</span>
          </div>
          <div class="flex justify-between">
            <span>Avg Render:</span>
            <span>{{ averageRenderTime.toFixed(2) }}ms</span>
          </div>
          <div class="flex justify-between">
            <span>Last Render:</span>
            <span>{{ lastRenderTime.toFixed(2) }}ms</span>
          </div>
        </div>
      </div>

      <!-- Network -->
      <div class="border-b border-gray-700 pb-2">
        <div class="text-purple-400 mb-1">Network</div>
        <div class="space-y-1 text-xs">
          <div class="flex justify-between">
            <span>Requests:</span>
            <span>{{ networkStats.requests }}</span>
          </div>
          <div class="flex justify-between">
            <span>Errors:</span>
            <span class="text-red-400">{{ networkStats.errors }}</span>
          </div>
          <div class="flex justify-between">
            <span>Avg Time:</span>
            <span>{{ networkStats.averageTime.toFixed(0) }}ms</span>
          </div>
        </div>
      </div>

      <!-- Actions -->
      <div class="flex space-x-2 pt-1">
        <button
          type="button"
          class="px-2 py-1 bg-blue-600 hover:bg-blue-700 rounded text-xs transition-colors duration-200"
          @click="collectGarbage"
        >
          GC
        </button>
        <button
          type="button"
          class="px-2 py-1 bg-green-600 hover:bg-green-700 rounded text-xs transition-colors duration-200"
          @click="exportData"
        >
          Export
        </button>
        <button
          type="button"
          class="px-2 py-1 bg-red-600 hover:bg-red-700 rounded text-xs transition-colors duration-200"
          @click="clearStats"
        >
          Clear
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, computed } from 'vue'

// Props
interface Props {
  updateInterval?: number
  maxSamples?: number
}

const props = withDefaults(defineProps<Props>(), {
  updateInterval: 1000,
  maxSamples: 60
})
console.log(process.env.NODE_ENV);

// State
const isDevelopment = process.env.NODE_ENV === 'development'
const isVisible = ref(true)
const isExpanded = ref(false)

// Performance metrics
const currentFps = ref(0)
const fpsHistory = ref<number[]>([])
const lastFrameTime = ref(performance.now())
const frameCount = ref(0)

// Memory usage
const memoryUsage = reactive({
  usedJSHeapSize: 0,
  totalJSHeapSize: 0,
  jsHeapSizeLimit: 0
})

// Render performance
const componentCount = ref(0)
const renderTimes = ref<number[]>([])
const lastRenderTime = ref(0)

// Network stats
const networkStats = reactive({
  requests: 0,
  errors: 0,
  totalTime: 0,
  averageTime: 0
})

// Computed
const averageRenderTime = computed(() => {
  if (renderTimes.value.length === 0) return 0
  return renderTimes.value.reduce((a, b) => a + b, 0) / renderTimes.value.length
})

// Methods
const updateFps = () => {
  const now = performance.now()
  const delta = now - lastFrameTime.value
  
  if (delta >= 1000) {
    currentFps.value = Math.round((frameCount.value * 1000) / delta)
    fpsHistory.value.push(currentFps.value)
    
    if (fpsHistory.value.length > props.maxSamples) {
      fpsHistory.value.shift()
    }
    
    frameCount.value = 0
    lastFrameTime.value = now
  }
  
  frameCount.value++
}

const updateMemoryUsage = () => {
  if ('memory' in performance) {
    const memory = (performance as any).memory
    memoryUsage.usedJSHeapSize = memory.usedJSHeapSize
    memoryUsage.totalJSHeapSize = memory.totalJSHeapSize
    memoryUsage.jsHeapSizeLimit = memory.jsHeapSizeLimit
  }
}

const updateComponentCount = () => {
  // Estimate component count by counting Vue instances
  const vueInstances = document.querySelectorAll('[data-v-]')
  componentCount.value = vueInstances.length
}

const measureRenderTime = () => {
  const start = performance.now()
  
  requestAnimationFrame(() => {
    const end = performance.now()
    const renderTime = end - start
    
    lastRenderTime.value = renderTime
    renderTimes.value.push(renderTime)
    
    if (renderTimes.value.length > props.maxSamples) {
      renderTimes.value.shift()
    }
  })
}

const monitorNetworkRequests = () => {
  // Override fetch to monitor network requests
  const originalFetch = window.fetch
  
  window.fetch = async (...args) => {
    const start = performance.now()
    networkStats.requests++
    
    try {
      const response = await originalFetch(...args)
      const end = performance.now()
      const duration = end - start
      
      networkStats.totalTime += duration
      networkStats.averageTime = networkStats.totalTime / networkStats.requests
      
      return response
    } catch (error) {
      networkStats.errors++
      throw error
    }
  }
  
  return () => {
    window.fetch = originalFetch
  }
}

// Utility methods
const formatMemory = (bytes: number): string => {
  const mb = bytes / (1024 * 1024)
  return `${mb.toFixed(1)}MB`
}

const getMemoryPercentage = (): number => {
  if (memoryUsage.jsHeapSizeLimit === 0) return 0
  return (memoryUsage.usedJSHeapSize / memoryUsage.jsHeapSizeLimit) * 100
}

const getFpsColor = (fps: number): string => {
  if (fps >= 55) return 'text-green-400'
  if (fps >= 30) return 'text-yellow-400'
  return 'text-red-400'
}

const getFpsBarColor = (fps: number): string => {
  if (fps >= 55) return 'bg-green-500'
  if (fps >= 30) return 'bg-yellow-500'
  return 'bg-red-500'
}

// Actions
const toggleExpanded = () => {
  isExpanded.value = !isExpanded.value
}

const hide = () => {
  isVisible.value = false
}

const collectGarbage = () => {
  if ('gc' in window) {
    (window as any).gc()
  } else {
    console.warn('Garbage collection not available. Run Chrome with --js-flags="--expose-gc"')
  }
}

const exportData = () => {
  const data = {
    timestamp: new Date().toISOString(),
    fps: {
      current: currentFps.value,
      history: fpsHistory.value
    },
    memory: memoryUsage,
    render: {
      componentCount: componentCount.value,
      averageTime: averageRenderTime.value,
      history: renderTimes.value
    },
    network: networkStats
  }
  
  const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `performance-${Date.now()}.json`
  a.click()
  URL.revokeObjectURL(url)
}

const clearStats = () => {
  fpsHistory.value = []
  renderTimes.value = []
  networkStats.requests = 0
  networkStats.errors = 0
  networkStats.totalTime = 0
  networkStats.averageTime = 0
}

// Lifecycle
let animationFrame: number
let updateInterval: NodeJS.Timeout
let restoreFetch: (() => void) | null = null

onMounted(() => {
  if (!isDevelopment) return
  
  // Start monitoring
  const animate = () => {
    updateFps()
    measureRenderTime()
    animationFrame = requestAnimationFrame(animate)
  }
  animate()
  
  // Update other metrics periodically
  updateInterval = setInterval(() => {
    updateMemoryUsage()
    updateComponentCount()
  }, props.updateInterval)
  
  // Monitor network requests
  restoreFetch = monitorNetworkRequests()
  
  // Keyboard shortcut to toggle visibility
  const handleKeydown = (event: KeyboardEvent) => {
    if (event.ctrlKey && event.shiftKey && event.key === 'P') {
      event.preventDefault()
      isVisible.value = !isVisible.value
    }
  }
  
  document.addEventListener('keydown', handleKeydown)
  
  onUnmounted(() => {
    document.removeEventListener('keydown', handleKeydown)
  })
})

onUnmounted(() => {
  if (animationFrame) {
    cancelAnimationFrame(animationFrame)
  }
  
  if (updateInterval) {
    clearInterval(updateInterval)
  }
  
  if (restoreFetch) {
    restoreFetch()
  }
})
</script>

<style scoped>
/* Ensure the monitor stays on top and is easily readable */
.performance-monitor {
  backdrop-filter: blur(4px);
  user-select: none;
}

/* Smooth transitions for expand/collapse */
.monitor-content {
  transition: all 0.2s ease-in-out;
}

/* Ensure proper contrast in all themes */
.performance-monitor {
  color: #ffffff;
  background-color: rgba(0, 0, 0, 0.9);
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .performance-monitor {
    bottom: 1rem;
    left: 1rem;
    right: 1rem;
    max-width: none;
  }
}
</style>
