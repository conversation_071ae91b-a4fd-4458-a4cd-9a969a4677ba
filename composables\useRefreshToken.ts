// composables/useRefreshToken.ts (Conceptual - cannot write this in Ask mode)
import { useAuthStore } from '~/stores/auth'; // Assuming you have a Pinia store
import type { User } from '~/app/features/auth/types/models'; // Import User type
import { navigateTo, useNuxtApp } from '#app'; // Explicitly import Nuxt composables
 
export const useRefreshToken = () => {
  const authStore = useAuthStore();
 
  let isRefreshing = false; // Simple flag to prevent concurrent refreshes

  const refreshToken = async () => {
    // if (!authStore.isAuthenticated) {
    //   console.warn('[useRefreshToken] User not authenticated. Skipping token refresh.');
    //   // Optionally, throw an error or return a specific value indicating failure
    //   // For now, let's throw an error similar to other failures.
    //   throw new Error('User not authenticated; refresh token call aborted.');
    // }

    if (isRefreshing) {
      // Wait for the ongoing refresh to complete
      // This might involve a promise or an event emitter
      return; // Or return the promise of the ongoing refresh
    }

    isRefreshing = true;

    try {
      // Get API client from Nuxt app
      const { $api } = useNuxtApp();
      if (!$api) {
        throw new Error('API client not available');
      }

      // The browser automatically sends the HttpOnly refresh token cookie
      const response = await $api.post<{ user: User }>('/auth/refresh-token');
 

      if (response.data) {
        // authStore.setAccessToken(response.accessToken); // Update your auth store
        // If your backend also sends a new refresh token to be set via headers, handle that here
        // (though typically HttpOnly cookies are set by the server directly)


        return response.data
      } else {
        // Handle cases where accessToken is not in the response
        throw new Error('Failed to refresh token: No access token received');
      }
    } catch (error) {
      console.error('Token refresh failed:', error);
      authStore.logout(); // Or clear user session
      // Potentially redirect to login
      navigateTo('/auth/login');
      throw error; // Re-throw to be caught by the caller
    } finally {
      isRefreshing = false;
    }
  };

  return {
    refreshToken,
    // isRefreshing (if you want to expose it)
  };
};
