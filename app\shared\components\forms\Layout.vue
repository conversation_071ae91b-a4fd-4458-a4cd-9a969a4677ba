<template>
  <div :class="layoutClass">
    <div v-if="layout === '1-col'" class="flex flex-col space-y-5">
      <slot />
    </div>
    <template v-if="layout === '2-col'">
      <div class="col-span-3 flex flex-col space-y-5">
        <slot />
      </div>
      <aside :class="['col-span-1', { 'sticky top-4': stickySidebar }]">
        <div class="flex flex-col space-y-5">
          <slot name="sidebar" />
        </div>
      </aside>
    </template>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";

const props = defineProps({
  layout: {
    type: String,
    default: '1-col',
    validator: (value: string) => ['1-col', '2-col'].includes(value),
  },
  stickySidebar: {
    type: Boolean,
    default: false,
  },
})

const layoutClass = computed(() => {
  if (props.layout === '2-col') {
    return 'grid grid-cols-1 md:grid-cols-4 gap-5'
  }
  return ''
})
</script>