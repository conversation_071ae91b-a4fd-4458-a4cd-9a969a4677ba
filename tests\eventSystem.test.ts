/**
 * Event System Tests
 * Tests for the event bus, listeners, and emitters
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { createEventBus } from '../utils/eventBus'
import type { EventMap } from '../types/events'

describe('Event System', () => {
  let eventBus: ReturnType<typeof createEventBus>

  beforeEach(() => {
    eventBus = createEventBus(false) // Disable debug mode for tests
  })

  afterEach(() => {
    eventBus.clear()
  })

  describe('Basic Event Handling', () => {
    it('should emit and receive events', () => {
      const handler = vi.fn()
      
      eventBus.on('case:created', handler)
      eventBus.emit('case:created', {
        caseId: 'test-case',
        caseTitle: 'Test Case',
        caseType: 'litigation',
        clientId: 'client-123',
        assignedTo: ['user-1'],
        timestamp: new Date().toISOString()
      })

      expect(handler).toHaveBeenCalledWith(
        expect.objectContaining({
          caseId: 'test-case',
          caseTitle: 'Test Case',
          caseType: 'litigation'
        })
      )
    })

    it('should handle multiple listeners for the same event', () => {
      const handler1 = vi.fn()
      const handler2 = vi.fn()
      
      eventBus.on('case:updated', handler1)
      eventBus.on('case:updated', handler2)
      
      const eventData = {
        caseId: 'case-123',
        changes: { status: 'active' },
        updatedBy: 'user-1',
        timestamp: new Date().toISOString()
      }
      
      eventBus.emit('case:updated', eventData)

      expect(handler1).toHaveBeenCalledWith(eventData)
      expect(handler2).toHaveBeenCalledWith(eventData)
    })

    it('should remove specific listeners', () => {
      const handler1 = vi.fn()
      const handler2 = vi.fn()
      
      eventBus.on('document:uploaded', handler1)
      eventBus.on('document:uploaded', handler2)
      eventBus.off('document:uploaded', handler1)
      
      eventBus.emit('document:uploaded', {
        documentId: 'doc-123',
        documentName: 'test.pdf',
        fileSize: 1024,
        mimeType: 'application/pdf',
        uploadedBy: 'user-1',
        timestamp: new Date().toISOString()
      })

      expect(handler1).not.toHaveBeenCalled()
      expect(handler2).toHaveBeenCalled()
    })

    it('should handle once listeners', () => {
      const handler = vi.fn()
      
      eventBus.once('template:created', handler)
      
      const eventData = {
        templateId: 'template-123',
        templateName: 'Test Template',
        createdBy: 'user-1',
        isPublic: true,
        timestamp: new Date().toISOString()
      }
      
      // Emit twice
      eventBus.emit('template:created', eventData)
      eventBus.emit('template:created', eventData)

      expect(handler).toHaveBeenCalledTimes(1)
    })
  })

  describe('Event Listener Management', () => {
    it('should track listener count correctly', () => {
      expect(eventBus.listenerCount()).toBe(0)
      
      const unsubscribe1 = eventBus.on('case:created', vi.fn())
      expect(eventBus.listenerCount()).toBe(1)
      
      const unsubscribe2 = eventBus.on('case:updated', vi.fn())
      expect(eventBus.listenerCount()).toBe(2)
      
      unsubscribe1()
      expect(eventBus.listenerCount()).toBe(1)
      
      unsubscribe2()
      expect(eventBus.listenerCount()).toBe(0)
    })

    it('should clear all listeners', () => {
      eventBus.on('case:created', vi.fn())
      eventBus.on('document:uploaded', vi.fn())
      eventBus.on('user:login', vi.fn())
      
      expect(eventBus.listenerCount()).toBe(3)
      
      eventBus.clear()
      
      expect(eventBus.listenerCount()).toBe(0)
    })

    it('should return unsubscribe function', () => {
      const handler = vi.fn()
      const unsubscribe = eventBus.on('case:deleted', handler)
      
      expect(typeof unsubscribe).toBe('function')
      
      eventBus.emit('case:deleted', {
        caseId: 'case-123',
        timestamp: new Date().toISOString()
      })
      
      expect(handler).toHaveBeenCalledTimes(1)
      
      unsubscribe()
      
      eventBus.emit('case:deleted', {
        caseId: 'case-456',
        timestamp: new Date().toISOString()
      })
      
      expect(handler).toHaveBeenCalledTimes(1) // Should not be called again
    })
  })

  describe('Event Data Validation', () => {
    it('should automatically add timestamp if missing', () => {
      const handler = vi.fn()
      eventBus.on('case:created', handler)
      
      const eventData = {
        caseId: 'case-123',
        caseTitle: 'Test Case',
        caseType: 'litigation',
        clientId: 'client-456',
        assignedTo: ['user-1']
        // Note: no timestamp provided
      }
      
      eventBus.emit('case:created', eventData as any)
      
      expect(handler).toHaveBeenCalledWith(
        expect.objectContaining({
          ...eventData,
          timestamp: expect.any(String)
        })
      )
    })

    it('should preserve existing timestamp', () => {
      const handler = vi.fn()
      eventBus.on('document:shared', handler)
      
      const customTimestamp = '2023-01-01T00:00:00.000Z'
      const eventData = {
        documentId: 'doc-123',
        sharedWith: ['user-1', 'user-2'],
        permissions: ['read'],
        sharedBy: 'user-admin',
        timestamp: customTimestamp
      }
      
      eventBus.emit('document:shared', eventData)
      
      expect(handler).toHaveBeenCalledWith(
        expect.objectContaining({
          timestamp: customTimestamp
        })
      )
    })
  })

  describe('UI Events', () => {
    it('should handle search events', () => {
      const handler = vi.fn()
      eventBus.on('ui:search', handler)
      
      eventBus.emit('ui:search', {
        component: 'CaseList',
        action: 'search',
        query: 'contract litigation',
        filters: { status: 'active', type: 'contract' },
        resultsCount: 15,
        timestamp: new Date().toISOString()
      })
      
      expect(handler).toHaveBeenCalledWith(
        expect.objectContaining({
          query: 'contract litigation',
          resultsCount: 15
        })
      )
    })

    it('should handle bulk action events', () => {
      const handler = vi.fn()
      eventBus.on('ui:bulk-action', handler)
      
      eventBus.emit('ui:bulk-action', {
        component: 'DocumentList',
        action: 'bulk-delete',
        itemCount: 5,
        itemIds: ['doc-1', 'doc-2', 'doc-3', 'doc-4', 'doc-5'],
        entityType: 'documents',
        timestamp: new Date().toISOString()
      })
      
      expect(handler).toHaveBeenCalledWith(
        expect.objectContaining({
          action: 'bulk-delete',
          itemCount: 5,
          entityType: 'documents'
        })
      )
    })
  })

  describe('System Events', () => {
    it('should handle error events', () => {
      const handler = vi.fn()
      eventBus.on('system:error', handler)
      
      const error = new Error('Test error')
      eventBus.emit('system:error', {
        level: 'error',
        category: 'api',
        message: 'API request failed',
        error,
        severity: 'medium',
        context: { endpoint: '/api/cases', method: 'POST' },
        timestamp: new Date().toISOString()
      })
      
      expect(handler).toHaveBeenCalledWith(
        expect.objectContaining({
          error,
          severity: 'medium',
          category: 'api'
        })
      )
    })

    it('should handle performance events', () => {
      const handler = vi.fn()
      eventBus.on('system:performance', handler)
      
      eventBus.emit('system:performance', {
        level: 'info',
        category: 'performance',
        message: 'Page load time recorded',
        metric: 'page_load_time',
        value: 1250,
        unit: 'ms',
        threshold: 2000,
        timestamp: new Date().toISOString()
      })
      
      expect(handler).toHaveBeenCalledWith(
        expect.objectContaining({
          metric: 'page_load_time',
          value: 1250,
          unit: 'ms'
        })
      )
    })
  })

  describe('Event Types', () => {
    it('should handle all defined event types', () => {
      const events: Array<keyof EventMap> = [
        'case:created',
        'case:updated',
        'case:status-changed',
        'case:deleted',
        'document:uploaded',
        'document:shared',
        'template:created',
        'user:invited',
        'ui:search',
        'ui:filter',
        'system:error',
        'app:ready'
      ]
      
      events.forEach(eventType => {
        const handler = vi.fn()
        eventBus.on(eventType, handler)
        expect(eventBus.listenerCount(eventType)).toBe(1)
      })
      
      expect(eventBus.listenerCount()).toBe(events.length)
    })
  })
})
