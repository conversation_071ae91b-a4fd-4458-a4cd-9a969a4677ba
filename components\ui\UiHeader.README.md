# UiHeader Component

A versatile and accessible header/label component for the Legal SaaS Frontend. This component provides consistent typography, styling, and functionality for headers, labels, and text elements throughout the application.

## Features

- **Flexible HTML Tags**: Supports h1-h6, p, span, div, and label elements
- **Multiple Size Variants**: From xs to 4xl sizing options
- **Color Variants**: Brand colors with dark mode support
- **Icon Support**: Leading and trailing icons with proper sizing
- **Badge/Indicator Support**: Optional badges with color variants
- **RTL Support**: Full right-to-left language support
- **Accessibility**: Proper ARIA attributes and semantic HTML
- **Required Field Indicator**: Built-in asterisk for required fields
- **Text Utilities**: Truncation, uppercase, and no-wrap options
- **Responsive Design**: Works across all screen sizes
- **Dark Mode**: Full dark mode compatibility

## Basic Usage

```vue
<template>
  <!-- Simple header -->
  <UiHeader text="Page Title" />
  
  <!-- With custom tag and size -->
  <UiHeader 
    tag="h1" 
    text="Main Heading" 
    size="2xl" 
    color="primary" 
  />
  
  <!-- Using slot content -->
  <UiHeader tag="h3" size="lg">
    Custom Header Content
  </UiHeader>
  
  <!-- As a label with required indicator -->
  <UiHeader 
    tag="label" 
    text="Email Address" 
    size="sm" 
    :required="true" 
  />
</template>
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `tag` | `string` | `'h2'` | HTML tag to render (h1-h6, p, span, div, label) |
| `text` | `string` | `undefined` | Header text content |
| `size` | `string` | `'md'` | Size variant (xs, sm, md, lg, xl, 2xl, 3xl, 4xl) |
| `color` | `string` | `'default'` | Color variant (default, primary, secondary, success, warning, danger, muted, white) |
| `align` | `string` | `'left'` | Text alignment (left, center, right) |
| `spacing` | `string` | `'md'` | Bottom margin spacing (none, xs, sm, md, lg, xl) |
| `leadingIcon` | `string` | `undefined` | Leading icon name |
| `trailingIcon` | `string` | `undefined` | Trailing icon name |
| `badge` | `string\|number\|boolean` | `undefined` | Badge content |
| `badgeColor` | `string` | `'primary'` | Badge color variant |
| `uppercase` | `boolean` | `false` | Transform text to uppercase |
| `truncate` | `boolean` | `false` | Truncate long text with ellipsis |
| `noWrap` | `boolean` | `false` | Prevent text wrapping |
| `required` | `boolean` | `false` | Show required indicator (*) |
| `class` | `string` | `undefined` | Custom CSS classes |
| `style` | `object` | `undefined` | Custom inline styles |

## Examples

### Size Variants

```vue
<template>
  <div class="space-y-4">
    <UiHeader text="Extra Small Header" size="xs" />
    <UiHeader text="Small Header" size="sm" />
    <UiHeader text="Medium Header" size="md" />
    <UiHeader text="Large Header" size="lg" />
    <UiHeader text="Extra Large Header" size="xl" />
    <UiHeader text="2XL Header" size="2xl" />
    <UiHeader text="3XL Header" size="3xl" />
    <UiHeader text="4XL Header" size="4xl" />
  </div>
</template>
```

### Color Variants

```vue
<template>
  <div class="space-y-4">
    <UiHeader text="Default Color" color="default" />
    <UiHeader text="Primary Color" color="primary" />
    <UiHeader text="Secondary Color" color="secondary" />
    <UiHeader text="Success Color" color="success" />
    <UiHeader text="Warning Color" color="warning" />
    <UiHeader text="Danger Color" color="danger" />
    <UiHeader text="Muted Color" color="muted" />
  </div>
</template>
```

### With Icons

```vue
<template>
  <div class="space-y-4">
    <!-- Leading icon -->
    <UiHeader 
      text="Settings" 
      leading-icon="heroicons:cog-6-tooth" 
      size="lg" 
    />
    
    <!-- Trailing icon -->
    <UiHeader 
      text="External Link" 
      trailing-icon="heroicons:arrow-top-right-on-square" 
    />
    
    <!-- Both icons -->
    <UiHeader 
      text="User Profile" 
      leading-icon="heroicons:user" 
      trailing-icon="heroicons:chevron-right" 
    />
  </div>
</template>
```

### With Badges

```vue
<template>
  <div class="space-y-4">
    <!-- Numeric badge -->
    <UiHeader 
      text="Notifications" 
      :badge="5" 
      badge-color="danger" 
    />
    
    <!-- Text badge -->
    <UiHeader 
      text="New Feature" 
      badge="NEW" 
      badge-color="success" 
    />
    
    <!-- Boolean badge (shows empty badge) -->
    <UiHeader 
      text="Status" 
      :badge="true" 
      badge-color="warning" 
    />
  </div>
</template>
```

### Form Labels

```vue
<template>
  <div class="space-y-4">
    <!-- Required field label -->
    <UiHeader 
      tag="label" 
      text="Email Address" 
      size="sm" 
      :required="true" 
      for="email-input" 
    />
    
    <!-- Optional field label -->
    <UiHeader 
      tag="label" 
      text="Phone Number" 
      size="sm" 
      color="muted" 
      for="phone-input" 
    />
  </div>
</template>
```

### Text Utilities

```vue
<template>
  <div class="space-y-4">
    <!-- Uppercase text -->
    <UiHeader 
      text="Section Title" 
      :uppercase="true" 
      size="sm" 
      color="muted" 
    />
    
    <!-- Truncated text -->
    <UiHeader 
      text="This is a very long header that will be truncated with ellipsis" 
      :truncate="true" 
      class="max-w-xs" 
    />
    
    <!-- No wrap text -->
    <UiHeader 
      text="This text will not wrap to multiple lines" 
      :no-wrap="true" 
    />
  </div>
</template>
```

### Alignment Options

```vue
<template>
  <div class="space-y-4">
    <UiHeader text="Left Aligned" align="left" />
    <UiHeader text="Center Aligned" align="center" />
    <UiHeader text="Right Aligned" align="right" />
  </div>
</template>
```

### Spacing Control

```vue
<template>
  <div>
    <UiHeader text="No Spacing" spacing="none" />
    <UiHeader text="Extra Small Spacing" spacing="xs" />
    <UiHeader text="Small Spacing" spacing="sm" />
    <UiHeader text="Medium Spacing" spacing="md" />
    <UiHeader text="Large Spacing" spacing="lg" />
    <UiHeader text="Extra Large Spacing" spacing="xl" />
  </div>
</template>
```

## Accessibility

The component follows accessibility best practices:

- Uses semantic HTML elements (h1-h6, label, etc.)
- Supports proper ARIA attributes
- Provides screen reader friendly required indicators
- Maintains proper color contrast ratios
- Supports keyboard navigation when used as labels

## RTL Support

The component automatically adapts to right-to-left languages:

- Icon positioning adjusts based on text direction
- Text alignment respects RTL layout
- Spacing and margins flip appropriately

## Styling

The component uses Tailwind CSS classes and follows the application's design system:

- Consistent with brand color palette
- Supports dark mode theming
- Responsive design patterns
- Smooth transitions and animations

## Integration

The component integrates seamlessly with other UI components and can be used in:

- Page headers and sections
- Form labels and field descriptions
- Card titles and content headers
- Navigation and menu items
- Dashboard widgets and panels
