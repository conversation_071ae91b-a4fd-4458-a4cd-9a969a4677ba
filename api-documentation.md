# API Documentation

## Table of Contents
1. [API Routes](#api-routes)
2. [Settings Variables](#settings-variables)
3. [Roles and Permissions](#roles-and-permissions)
4. [Real-time Monitoring](#real-time-monitoring)

## API Routes

### Authentication
- `POST /auth/login`
  - Request:
    ```json
    {
      "email": "<EMAIL>",
      "password": "password123"
    }
    ```
  - Response:
    ```json
    {
      "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "expires_in": 3600
    }
    ```

- `POST /auth/refresh`
  - Request:
    ```json
    {
      "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
    }
    ```
  - Response:
    ```json
    {
      "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "expires_in": 3600
    }
    ```

- `POST /auth/logout`
  - Request: None (Uses JWT from Authorization header)
  - Response:
    ```json
    {
      "message": "Successfully logged out"
    }
    ```

### Users
- `GET /users`
  - Parameters: 
    - `page`: Page number
    - `limit`: Items per page
    - `search`: Search term
    - `sort`: Sort field and direction (e.g., "createdAt:DESC")
  - Response:
    ```json
    {
      "data": [
        {
          "id": "user-uuid-1",
          "email": "<EMAIL>",
          "firstName": "John",
          "lastName": "Doe",
          "roles": ["LAWYER"],
          "tenantId": "tenant-uuid-1",
          "isActive": true,
          "createdAt": "2023-12-07T10:00:00Z",
          "updatedAt": "2023-12-07T10:00:00Z"
        }
      ],
      "meta": {
        "totalItems": 1,
        "itemCount": 1,
        "itemsPerPage": 10,
        "totalPages": 1,
        "currentPage": 1
      }
    }
    ```

- `GET /users/:id`
  - Parameters: 
    - `id`: User ID
  - Response:
    ```json
    {
      "id": "user-uuid-1",
      "email": "<EMAIL>",
      "firstName": "John",
      "lastName": "Doe",
      "roles": ["LAWYER"],
      "tenantId": "tenant-uuid-1",
      "isActive": true,
      "createdAt": "2023-12-07T10:00:00Z",
      "updatedAt": "2023-12-07T10:00:00Z"
    }
    ```

- `POST /users`
  - Request:
    ```json
    {
      "email": "<EMAIL>",
      "password": "Password123!",
      "firstName": "Jane",
      "lastName": "Smith",
      "roles": ["CLIENT"],
      "tenantId": "tenant-uuid-1"
    }
    ```
  - Response:
    ```json
    {
      "id": "user-uuid-2",
      "email": "<EMAIL>",
      "firstName": "Jane",
      "lastName": "Smith",
      "roles": ["CLIENT"],
      "tenantId": "tenant-uuid-1",
      "isActive": true,
      "createdAt": "2023-12-07T10:00:00Z",
      "updatedAt": "2023-12-07T10:00:00Z"
    }
    ```

- `PATCH /users/:id`
  - Parameters: 
    - `id`: User ID
  - Request:
    ```json
    {
      "firstName": "Jane",
      "lastName": "Johnson",
      "isActive": false
    }
    ```
  - Response:
    ```json
    {
      "id": "user-uuid-1",
      "email": "<EMAIL>",
      "firstName": "Jane",
      "lastName": "Johnson",
      "roles": ["LAWYER"],
      "tenantId": "tenant-uuid-1",
      "isActive": false,
      "createdAt": "2023-12-07T10:00:00Z",
      "updatedAt": "2023-12-08T15:30:00Z"
    }
    ```

- `DELETE /users/:id`
  - Parameters: 
    - `id`: User ID
  - Response:
    ```json
    {
      "message": "User successfully deleted"
    }
    ```

### Cases
- `GET /cases`
  - Parameters: 
    - `page`: Page number
    - `limit`: Items per page
    - `status`: Filter by status
    - `assignedTo`: Filter by assigned user
    - `sort`: Sort field and direction
  - Response:
    ```json
    {
      "data": [
        {
          "id": "case-uuid-1",
          "title": "Contract Dispute Case",
          "description": "Client contract dispute requiring legal review",
          "status": "open",
          "priority": "high",
          "assignedUserIds": ["user-uuid-1"],
          "deadline": "2024-06-20T17:00:00.000Z",
          "relatedDocumentIds": ["doc-uuid-1"],
          "tenantId": "tenant-uuid-1",
          "createdAt": "2023-12-07T10:00:00Z",
          "updatedAt": "2023-12-07T10:00:00Z",
          "statusHistory": [
            {
              "status": "open",
              "changedAt": "2023-12-07T10:00:00Z",
              "actorId": "user-uuid-1"
            }
          ]
        }
      ],
      "meta": {
        "totalItems": 1,
        "itemCount": 1,
        "itemsPerPage": 10,
        "totalPages": 1,
        "currentPage": 1
      }
    }
    ```

- `GET /cases/:id`
  - Parameters: 
    - `id`: Case ID
  - Response:
    ```json
    {
      "id": "case-uuid-1",
      "title": "Contract Dispute Case",
      "description": "Client contract dispute requiring legal review",
      "status": "open",
      "priority": "high",
      "assignedUserIds": ["user-uuid-1"],
      "deadline": "2024-06-20T17:00:00.000Z",
      "relatedDocumentIds": ["doc-uuid-1"],
      "tenantId": "tenant-uuid-1",
      "createdAt": "2023-12-07T10:00:00Z",
      "updatedAt": "2023-12-07T10:00:00Z",
      "statusHistory": [
        {
          "status": "open",
          "changedAt": "2023-12-07T10:00:00Z",
          "actorId": "user-uuid-1"
        }
      ]
    }
    ```

- `POST /cases`
  - Request:
    ```json
    {
      "title": "Contract Dispute Case",
      "description": "Client contract dispute requiring legal review",
      "priority": "high",
      "assignedUserIds": ["user-uuid-1"],
      "deadline": "2024-06-20T17:00:00.000Z",
      "relatedDocumentIds": ["doc-uuid-1"]
    }
    ```
  - Response:
    ```json
    {
      "id": "case-uuid-1",
      "title": "Contract Dispute Case",
      "description": "Client contract dispute requiring legal review",
      "status": "open",
      "priority": "high",
      "assignedUserIds": ["user-uuid-1"],
      "deadline": "2024-06-20T17:00:00.000Z",
      "relatedDocumentIds": ["doc-uuid-1"],
      "tenantId": "tenant-uuid-1",
      "createdAt": "2023-12-07T10:00:00Z",
      "updatedAt": "2023-12-07T10:00:00Z",
      "statusHistory": [
        {
          "status": "open",
          "changedAt": "2023-12-07T10:00:00Z",
          "actorId": "user-uuid-1"
        }
      ]
    }
    ```

- `PATCH /cases/:id`
  - Parameters: 
    - `id`: Case ID
  - Request:
    ```json
    {
      "status": "in_progress",
      "priority": "medium",
      "assignedUserIds": ["user-uuid-1", "user-uuid-2"]
    }
    ```
  - Response:
    ```json
    {
      "id": "case-uuid-1",
      "title": "Contract Dispute Case",
      "description": "Client contract dispute requiring legal review",
      "status": "in_progress",
      "priority": "medium",
      "assignedUserIds": ["user-uuid-1", "user-uuid-2"],
      "deadline": "2024-06-20T17:00:00.000Z",
      "relatedDocumentIds": ["doc-uuid-1"],
      "tenantId": "tenant-uuid-1",
      "createdAt": "2023-12-07T10:00:00Z",
      "updatedAt": "2023-12-08T15:30:00Z",
      "statusHistory": [
        {
          "status": "open",
          "changedAt": "2023-12-07T10:00:00Z",
          "actorId": "user-uuid-1"
        },
        {
          "status": "in_progress",
          "changedAt": "2023-12-08T15:30:00Z",
          "actorId": "user-uuid-1"
        }
      ]
    }
    ```

### Case Comments
- `GET /cases/:caseId/comments`
  - Parameters:
    - `caseId`: Case ID
    - `page`: Page number
    - `limit`: Items per page
  - Response:
    ```json
    {
      "data": [
        {
          "id": "comment-uuid-1",
          "caseId": "case-uuid-1",
          "authorId": "user-uuid-1",
          "body": "This is the first comment on the case.",
          "mentions": ["user-uuid-2"],
          "createdAt": "2024-06-13T11:00:00.000Z",
          "updatedAt": "2024-06-13T11:00:00.000Z",
          "tenantId": "tenant-uuid-1",
          "author": {
            "id": "user-uuid-1",
            "email": "<EMAIL>",
            "firstName": "John",
            "lastName": "Doe"
          }
        }
      ],
      "meta": {
        "totalItems": 1,
        "itemCount": 1,
        "itemsPerPage": 10,
        "totalPages": 1,
        "currentPage": 1
      }
    }
    ```

- `POST /cases/:caseId/comments`
  - Parameters:
    - `caseId`: Case ID
  - Request:
    ```json
    {
      "body": "This is a new comment on the case.",
      "mentions": ["user-uuid-2"]
    }
    ```
  - Response:
    ```json
    {
      "id": "comment-uuid-2",
      "caseId": "case-uuid-1",
      "authorId": "user-uuid-1",
      "body": "This is a new comment on the case.",
      "mentions": ["user-uuid-2"],
      "createdAt": "2024-06-14T11:00:00.000Z",
      "updatedAt": "2024-06-14T11:00:00.000Z",
      "tenantId": "tenant-uuid-1"
    }
    ```

### Documents
- `POST /documents/upload`
  - Request: Multipart form data with file
  - Response:
    ```json
    {
      "id": "doc-uuid-1",
      "name": "Contract.pdf",
      "contentType": "application/pdf",
      "size": 1024567,
      "path": "uploads/tenant-uuid-1/2023/12/07/contract.pdf",
      "uploadedById": "user-uuid-1",
      "tenantId": "tenant-uuid-1",
      "createdAt": "2023-12-07T10:00:00Z",
      "updatedAt": "2023-12-07T10:00:00Z"
    }
    ```

- `GET /documents/:id`
  - Parameters: 
    - `id`: Document ID
  - Response:
    ```json
    {
      "id": "doc-uuid-1",
      "name": "Contract.pdf",
      "contentType": "application/pdf",
      "size": 1024567,
      "path": "uploads/tenant-uuid-1/2023/12/07/contract.pdf",
      "uploadedById": "user-uuid-1",
      "tenantId": "tenant-uuid-1",
      "createdAt": "2023-12-07T10:00:00Z",
      "updatedAt": "2023-12-07T10:00:00Z",
      "metadata": {
        "pageCount": 12,
        "author": "John Smith",
        "createdDate": "2023-11-30T09:00:00Z"
      }
    }
    ```

- `POST /documents/from-template`
  - Request:
    ```json
    {
      "templateId": "tplt-uuid-1",
      "documentName": "Generated Contract from Template X.docx",
      "variables": {
        "clientName": "Acme Corp",
        "projectScope": "New Website Development"
      },
      "caseId": "case-uuid-1"
    }
    ```
  - Response:
    ```json
    {
      "id": "doc-uuid-2",
      "name": "Generated Contract from Template X.docx",
      "contentType": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      "size": 245678,
      "path": "uploads/tenant-uuid-1/2023/12/07/generated-contract.docx",
      "uploadedById": "user-uuid-1",
      "tenantId": "tenant-uuid-1",
      "createdAt": "2023-12-07T10:00:00Z",
      "updatedAt": "2023-12-07T10:00:00Z",
      "templateId": "tplt-uuid-1",
      "caseId": "case-uuid-1"
    }
    ```

- `POST /documents/bulk-upload`
  - Request: Multipart form data with multiple files and metadata
    ```json
    {
      "name": "Project Alpha - Evidence Batch 1",
      "caseId": "case-uuid-1"
    }
    ```
  - Response:
    ```json
    {
      "uploadedCount": 3,
      "failedCount": 0,
      "documents": [
        {
          "id": "doc-uuid-3",
          "name": "Evidence1.pdf",
          "contentType": "application/pdf",
          "size": 524288,
          "path": "uploads/tenant-uuid-1/2023/12/07/evidence1.pdf",
          "uploadedById": "user-uuid-1",
          "tenantId": "tenant-uuid-1",
          "createdAt": "2023-12-07T10:00:00Z",
          "updatedAt": "2023-12-07T10:00:00Z",
          "caseId": "case-uuid-1"
        },
        {
          "id": "doc-uuid-4",
          "name": "Evidence2.jpg",
          "contentType": "image/jpeg",
          "size": 1048576,
          "path": "uploads/tenant-uuid-1/2023/12/07/evidence2.jpg",
          "uploadedById": "user-uuid-1",
          "tenantId": "tenant-uuid-1",
          "createdAt": "2023-12-07T10:00:00Z",
          "updatedAt": "2023-12-07T10:00:00Z",
          "caseId": "case-uuid-1"
        }
      ]
    }
    ```

### Templates
- `POST /templates`
  - Request:
    ```json
    {
      "name": "Standard NDA Template",
      "description": "Standard NDA for new contractors",
      "content": "<h1>Non-Disclosure Agreement</h1><p>This agreement is between {{company_name}} and {{contractor_name}}...</p>",
      "variables": ["company_name", "contractor_name", "effective_date"]
    }
    ```
  - Response:
    ```json
    {
      "id": "tplt-uuid-1",
      "name": "Standard NDA Template",
      "description": "Standard NDA for new contractors",
      "content": "<h1>Non-Disclosure Agreement</h1><p>This agreement is between {{company_name}} and {{contractor_name}}...</p>",
      "variables": ["company_name", "contractor_name", "effective_date"],
      "isActive": true,
      "tenantId": "tenant-uuid-1",
      "createdById": "user-uuid-1",
      "createdAt": "2023-12-07T10:00:00Z",
      "updatedAt": "2023-12-07T10:00:00Z"
    }
    ```

- `GET /templates`
  - Parameters: Optional filters
  - Response:
    ```json
    {
      "data": [
        {
          "id": "tplt-uuid-1",
          "name": "Standard NDA Template",
          "description": "Standard NDA for new contractors",
          "variables": ["company_name", "contractor_name", "effective_date"],
          "isActive": true,
          "tenantId": "tenant-uuid-1",
          "createdById": "user-uuid-1",
          "createdAt": "2023-12-07T10:00:00Z",
          "updatedAt": "2023-12-07T10:00:00Z"
        }
      ],
      "meta": {
        "totalItems": 1,
        "itemCount": 1,
        "itemsPerPage": 10,
        "totalPages": 1,
        "currentPage": 1
      }
    }
    ```

- `POST /templates/preview`
  - Request:
    ```json
    {
      "content": "<h1>Hello {{name}}</h1>",
      "variables": {
        "name": "John Doe",
        "company_name": "Acme Corp"
      }
    }
    ```
  - Response:
    ```json
    {
      "renderedContent": "<h1>Hello John Doe</h1>",
      "detectedVariables": ["name", "company_name"],
      "usedVariables": ["name"],
      "unusedVariables": ["company_name"]
    }
    ```

### Notifications
- `POST /notifications/triggers/preview`
  - Request:
    ```json
    {
      "contextData": {
        "user": {
          "name": "John Doe",
          "email": "<EMAIL>"
        },
        "case": {
          "id": "123",
          "title": "Sample Case"
        }
      }
    }
    ```
  - Response:
    ```json
    {
      "renderedTemplate": "Hello John Doe, a new case 'Sample Case' has been assigned to you.",
      "evaluatedConditions": {
        "case.exists": true,
        "user.email.exists": true
      },
      "wouldTrigger": true,
      "channels": ["email"],
      "recipients": ["<EMAIL>"]
    }
    ```

- `POST /events/trigger`
  - Request:
    ```json
    {
      "event": "user.created",
      "payload": {
        "userId": "123",
        "tenantId": "abc",
        "email": "<EMAIL>"
      }
    }
    ```
  - Response:
    ```json
    {
      "success": true,
      "triggeredHandlers": 2,
      "event": "user.created",
      "timestamp": "2023-12-07T10:00:00Z"
    }
    ```

### Settings
- `GET /settings`
  - Response:
    ```json
    {
      "data": [
        {
          "id": "setting-uuid-1",
          "key": "site_name",
          "value": "My Awesome App",
          "type": "STRING",
          "isEncrypted": false,
          "description": "The public name of the application.",
          "scope": "PLATFORM",
          "tenantId": null,
          "createdAt": "2023-12-07T10:00:00Z",
          "updatedAt": "2023-12-07T10:00:00Z"
        }
      ],
      "meta": {
        "totalItems": 1,
        "itemCount": 1,
        "itemsPerPage": 10,
        "totalPages": 1,
        "currentPage": 1
      }
    }
    ```

- `PATCH /settings/:key`
  - Parameters:
    - `key`: Setting key
  - Request:
    ```json
    {
      "value": "New App Name"
    }
    ```
  - Response:
    ```json
    {
      "id": "setting-uuid-1",
      "key": "site_name",
      "value": "New App Name",
      "type": "STRING",
      "isEncrypted": false,
      "description": "The public name of the application.",
      "scope": "PLATFORM",
      "tenantId": null,
      "createdAt": "2023-12-07T10:00:00Z",
      "updatedAt": "2023-12-08T15:30:00Z"
    }
    ```

### API Documentation
- `GET /api-docs`
  - Response: Swagger UI HTML

- `GET /api-docs-json`
  - Response: OpenAPI specification in JSON format

- `GET /api-docs-yaml`
  - Response: OpenAPI specification in YAML format

## Settings Variables

### Application Core Settings
| Key | Type | Scope | Default | Description |
|-----|------|-------|---------|-------------|
| `appName` | STRING | PLATFORM | "Legal SaaS" | Application name |
| `appVersion` | STRING | PLATFORM | "1.0.0" | Application version |
| `appEnvironment` | STRING | PLATFORM | "production" | Application environment |
| `appUrl` | STRING | PLATFORM | "http://localhost:3000" | Application URL |
| `appLogo` | STRING | PLATFORM | "/assets/logo.png" | Application logo path |
| `appFavicon` | STRING | PLATFORM | "/assets/favicon.ico" | Application favicon path |
| `appTheme` | STRING | PLATFORM | "light" | Application theme |
| `appLanguage` | STRING | PLATFORM | "en" | Application language |
| `appTimeZone` | STRING | PLATFORM | "UTC" | Application timezone |
| `appDateFormat` | STRING | PLATFORM | "YYYY-MM-DD" | Application date format |
| `appTimeFormat` | STRING | PLATFORM | "HH:mm:ss" | Application time format |

### Security and Encryption
| Key | Type | Scope | Default | Description |
|-----|------|-------|---------|-------------|
| `securityEnabled` | BOOLEAN | PLATFORM | true | Enable security features |
| `securityEncryptionEnabled` | BOOLEAN | PLATFORM | true | Enable encryption |
| `securityEncryptionAlgorithm` | STRING | PLATFORM | "aes-256-gcm" | Encryption algorithm |
| `securityPasswordMinLength` | NUMBER | PLATFORM | 8 | Minimum password length |
| `securityPasswordRequireUppercase` | BOOLEAN | PLATFORM | true | Require uppercase in passwords |
| `securityPasswordRequireLowercase` | BOOLEAN | PLATFORM | true | Require lowercase in passwords |
| `securityPasswordRequireNumbers` | BOOLEAN | PLATFORM | true | Require numbers in passwords |
| `securityPasswordRequireSymbols` | BOOLEAN | PLATFORM | true | Require symbols in passwords |
| `securitySessionTimeout` | NUMBER | PLATFORM | 3600 | Session timeout in seconds |
| `securityJwtSecret` | STRING | PLATFORM | "" | JWT secret key |
| `securityJwtExpiresIn` | STRING | PLATFORM | "1d" | JWT expiration time |
| `securityJwtRefreshExpiresIn` | STRING | PLATFORM | "7d" | JWT refresh token expiration |

### API Documentation Settings
| Key | Type | Scope | Default | Description |
|-----|------|-------|---------|-------------|
| `swaggerEnabled` | BOOLEAN | PLATFORM | true | Enable Swagger API documentation |
| `swaggerPath` | STRING | PLATFORM | "/api-docs" | Documentation endpoint path |
| `swaggerTitle` | STRING | PLATFORM | "Legal SaaS API" | Documentation title |
| `swaggerDescription` | STRING | PLATFORM | "Legal SaaS Platform API Documentation" | Documentation description |
| `swaggerVersion` | STRING | PLATFORM | "1.0.0" | API version |
| `swaggerJsonPath` | STRING | PLATFORM | "/api-docs-json" | JSON export path |
| `swaggerYamlPath` | STRING | PLATFORM | "/api-docs-yaml" | YAML export path |
| `swaggerDeepLinking` | BOOLEAN | PLATFORM | true | Enable deep linking |
| `swaggerDefaultModelsExpandDepth` | NUMBER | PLATFORM | 1 | Default models expand depth |
| `swaggerDefaultModelExpandDepth` | NUMBER | PLATFORM | 1 | Default model expand depth |
| `swaggerDefaultModelRendering` | STRING | PLATFORM | "example" | Default model rendering |
| `swaggerDisplayRequestDuration` | BOOLEAN | PLATFORM | true | Display request duration |

### Email and Notifications
| Key | Type | Scope | Default | Description |
|-----|------|-------|---------|-------------|
| `smtpConfig` | OBJECT | PLATFORM | See below | SMTP configuration |
| `emailDefaultFromAddress` | STRING | PLATFORM | "<EMAIL>" | Default from address |
| `emailDefaultReplyToAddress` | STRING | PLATFORM | "<EMAIL>" | Default reply-to address |
| `emailTemplatesPath` | STRING | PLATFORM | "./templates/email" | Email templates path |
| `notificationsEnabled` | BOOLEAN | PLATFORM | true | Enable notifications |
| `notificationsDefaultChannel` | STRING | PLATFORM | "email" | Default notification channel |

Default SMTP Config:
```json
{
  "host": "localhost",
  "port": 1025,
  "secure": false,
  "auth": {
    "user": "",
    "pass": ""
  },
  "defaultFromAddress": "<EMAIL>"
}
```

### File Upload Settings
| Key | Type | Scope | Default | Description |
|-----|------|-------|---------|-------------|
| `uploadDirectory` | STRING | PLATFORM | "./uploads" | Directory for storing uploaded files |
| `maxFileSize` | NUMBER | PLATFORM | 10485760 | Maximum file upload size in bytes (10MB) |
| `allowedFileTypes` | JSON | PLATFORM | See below | Allowed file extensions for uploads |

Allowed File Types:
```json
[
  "pdf", "doc", "docx", "txt", "rtf", "odt",
  "jpg", "jpeg", "png", "gif", "bmp",
  "xls", "xlsx", "csv", "ppt", "pptx"
]
```

### Tenant Usage Limits
| Key | Type | Scope | Default | Description |
|-----|------|-------|---------|-------------|
| `planLimits` | JSON | PLATFORM | See below | Usage limits for different subscription plans |

Plan Limits:
```json
{
  "basic": { 
    "documents": 100, 
    "storageMB": 100, 
    "users": 5, 
    "cases": 10 
  },
  "pro": { 
    "documents": 1000, 
    "storageMB": 1000, 
    "users": 20, 
    "cases": 100 
  },
  "enterprise": { 
    "documents": -1, 
    "storageMB": -1, 
    "users": -1, 
    "cases": -1 
  }
}
```

### Redis & Caching Settings
| Key | Type | Scope | Default | Description |
|-----|------|-------|---------|-------------|
| `redisConfig` | JSON | PLATFORM | See below | Redis configuration for caching and sessions |
| `cacheDefaultTtl` | NUMBER | PLATFORM | 300 | Default cache TTL in seconds |
| `cacheMaxItems` | NUMBER | PLATFORM | 1000 | Maximum number of items in cache |

Redis Config:
```json
{
  "host": "localhost",
  "port": 6379,
  "password": "",
  "db": 0,
  "keyPrefix": "legal-saas:",
  "ttl": 300,
  "max": 1000
}
```

## Roles and Permissions

### Default Roles
1. **SuperAdmin**
   - Full system access
   - Can manage all tenants, users, and settings
   - Example permissions:
     ```json
     {
       "users": ["create", "read", "update", "delete"],
       "tenants": ["create", "read", "update", "delete"],
       "settings": ["read", "update"],
       "cases": ["create", "read", "update", "delete"],
       "documents": ["create", "read", "update", "delete"],
       "templates": ["create", "read", "update", "delete"],
       "audit": ["read"],
       "monitoring": ["read"]
     }
     ```

2. **TenantOwner**
   - Can manage their tenant settings and users
   - Full access to all tenant data
   - Example permissions:
     ```json
     {
       "users": ["create", "read", "update", "delete"],
       "tenants": ["read", "update"],
       "settings": ["read", "update"],
       "cases": ["create", "read", "update", "delete"],
       "documents": ["create", "read", "update", "delete"],
       "templates": ["create", "read", "update", "delete"],
       "audit": ["read"]
     }
     ```

3. **Admin**
   - Administrative access within a tenant
   - Can manage users, cases, and documents
   - Example permissions:
     ```json
     {
       "users": ["create", "read", "update"],
       "cases": ["create", "read", "update", "delete"],
       "documents": ["create", "read",