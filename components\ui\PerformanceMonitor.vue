<template>
  <div v-if="showMonitor" class="fixed bottom-4 right-4 z-50">
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-4 w-80">
      <!-- Header -->
      <div class="flex items-center justify-between mb-3">
        <h3 class="text-sm font-semibold text-gray-900 dark:text-white">Performance Monitor</h3>
        <div class="flex items-center gap-2">
          <UiButton @click="toggleAutoUpdate" size="sm" variant="ghost" class="p-1">
            <Icon 
              :name="autoUpdate ? 'material-symbols:pause' : 'material-symbols:play-arrow'" 
              class="h-4 w-4" 
            />
          </UiButton>
          <UiButton @click="showMonitor = false" size="sm" variant="ghost" class="p-1">
            <Icon name="material-symbols:close" class="h-4 w-4" />
          </UiButton>
        </div>
      </div>

      <!-- Metrics Grid -->
      <div class="grid grid-cols-2 gap-3 mb-4">
        <!-- FPS -->
        <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
          <div class="flex items-center justify-between">
            <span class="text-xs text-gray-500 dark:text-gray-400">FPS</span>
            <div 
              :class="[
                'w-2 h-2 rounded-full',
                fps >= 55 ? 'bg-green-500' : fps >= 30 ? 'bg-yellow-500' : 'bg-red-500'
              ]"
            ></div>
          </div>
          <div class="text-lg font-semibold text-gray-900 dark:text-white">{{ fps }}</div>
        </div>

        <!-- Memory Usage -->
        <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
          <div class="flex items-center justify-between">
            <span class="text-xs text-gray-500 dark:text-gray-400">Memory</span>
            <div 
              :class="[
                'w-2 h-2 rounded-full',
                memoryUsage < 50 ? 'bg-green-500' : memoryUsage < 80 ? 'bg-yellow-500' : 'bg-red-500'
              ]"
            ></div>
          </div>
          <div class="text-lg font-semibold text-gray-900 dark:text-white">{{ memoryUsage }}MB</div>
        </div>

        <!-- Load Time -->
        <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
          <div class="flex items-center justify-between">
            <span class="text-xs text-gray-500 dark:text-gray-400">Load Time</span>
            <div 
              :class="[
                'w-2 h-2 rounded-full',
                loadTime < 1000 ? 'bg-green-500' : loadTime < 3000 ? 'bg-yellow-500' : 'bg-red-500'
              ]"
            ></div>
          </div>
          <div class="text-lg font-semibold text-gray-900 dark:text-white">{{ loadTime }}ms</div>
        </div>

        <!-- Bundle Size -->
        <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
          <div class="flex items-center justify-between">
            <span class="text-xs text-gray-500 dark:text-gray-400">Bundle</span>
            <div 
              :class="[
                'w-2 h-2 rounded-full',
                bundleSize < 500 ? 'bg-green-500' : bundleSize < 1000 ? 'bg-yellow-500' : 'bg-red-500'
              ]"
            ></div>
          </div>
          <div class="text-lg font-semibold text-gray-900 dark:text-white">{{ bundleSize }}KB</div>
        </div>
      </div>

      <!-- Performance Chart -->
      <div class="mb-4">
        <div class="flex items-center justify-between mb-2">
          <span class="text-xs text-gray-500 dark:text-gray-400">FPS History</span>
          <span class="text-xs text-gray-500 dark:text-gray-400">{{ fpsHistory.length }} samples</span>
        </div>
        <div class="h-16 bg-gray-100 dark:bg-gray-700 rounded relative overflow-hidden">
          <svg class="w-full h-full" viewBox="0 0 100 60">
            <polyline
              :points="fpsChartPoints"
              fill="none"
              stroke="currentColor"
              stroke-width="1"
              class="text-blue-500"
            />
          </svg>
        </div>
      </div>

      <!-- Core Web Vitals -->
      <div class="space-y-2">
        <div class="text-xs font-medium text-gray-700 dark:text-gray-300">Core Web Vitals</div>
        
        <!-- LCP -->
        <div class="flex items-center justify-between">
          <span class="text-xs text-gray-500 dark:text-gray-400">LCP</span>
          <div class="flex items-center gap-2">
            <div class="w-16 bg-gray-200 dark:bg-gray-600 rounded-full h-1">
              <div 
                class="h-1 rounded-full transition-all duration-300"
                :class="lcp <= 2500 ? 'bg-green-500' : lcp <= 4000 ? 'bg-yellow-500' : 'bg-red-500'"
                :style="{ width: Math.min(100, (lcp / 5000) * 100) + '%' }"
              ></div>
            </div>
            <span class="text-xs text-gray-900 dark:text-white w-12 text-right">{{ lcp }}ms</span>
          </div>
        </div>

        <!-- FID -->
        <div class="flex items-center justify-between">
          <span class="text-xs text-gray-500 dark:text-gray-400">FID</span>
          <div class="flex items-center gap-2">
            <div class="w-16 bg-gray-200 dark:bg-gray-600 rounded-full h-1">
              <div 
                class="h-1 rounded-full transition-all duration-300"
                :class="fid <= 100 ? 'bg-green-500' : fid <= 300 ? 'bg-yellow-500' : 'bg-red-500'"
                :style="{ width: Math.min(100, (fid / 500) * 100) + '%' }"
              ></div>
            </div>
            <span class="text-xs text-gray-900 dark:text-white w-12 text-right">{{ fid }}ms</span>
          </div>
        </div>

        <!-- CLS -->
        <div class="flex items-center justify-between">
          <span class="text-xs text-gray-500 dark:text-gray-400">CLS</span>
          <div class="flex items-center gap-2">
            <div class="w-16 bg-gray-200 dark:bg-gray-600 rounded-full h-1">
              <div 
                class="h-1 rounded-full transition-all duration-300"
                :class="cls <= 0.1 ? 'bg-green-500' : cls <= 0.25 ? 'bg-yellow-500' : 'bg-red-500'"
                :style="{ width: Math.min(100, (cls / 0.5) * 100) + '%' }"
              ></div>
            </div>
            <span class="text-xs text-gray-900 dark:text-white w-12 text-right">{{ cls.toFixed(3) }}</span>
          </div>
        </div>
      </div>

      <!-- Actions -->
      <div class="mt-4 pt-3 border-t border-gray-200 dark:border-gray-700">
        <div class="flex gap-2">
          <UiButton @click="clearMetrics" size="sm" variant="outline" class="flex-1">
            Clear
          </UiButton>
          <UiButton @click="exportMetrics" size="sm" variant="outline" class="flex-1">
            Export
          </UiButton>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, onUnmounted, ref } from 'vue'

// State
const showMonitor = ref(false)
const autoUpdate = ref(true)
const fps = ref(60)
const memoryUsage = ref(0)
const loadTime = ref(0)
const bundleSize = ref(0)
const lcp = ref(0) // Largest Contentful Paint
const fid = ref(0) // First Input Delay
const cls = ref(0) // Cumulative Layout Shift
const fpsHistory = ref<number[]>([])

// Performance monitoring
let animationId: number | null = null
let lastTime = 0
let frameCount = 0

// FPS Chart
const fpsChartPoints = computed(() => {
  const points = fpsHistory.value.slice(-50) // Last 50 samples
  return points.map((fps, index) => {
    const x = (index / (points.length - 1)) * 100
    const y = 60 - (fps / 60) * 60 // Invert Y axis
    return `${x},${y}`
  }).join(' ')
})

// Methods
const measureFPS = (currentTime: number) => {
  frameCount++
  
  if (currentTime - lastTime >= 1000) {
    fps.value = Math.round((frameCount * 1000) / (currentTime - lastTime))
    fpsHistory.value.push(fps.value)
    
    // Keep only last 100 samples
    if (fpsHistory.value.length > 100) {
      fpsHistory.value.shift()
    }
    
    frameCount = 0
    lastTime = currentTime
  }
  
  if (autoUpdate.value) {
    animationId = requestAnimationFrame(measureFPS)
  }
}

const measureMemoryUsage = () => {
  if ('memory' in performance) {
    const memory = (performance as any).memory
    memoryUsage.value = Math.round(memory.usedJSHeapSize / 1024 / 1024)
  }
}

const measureLoadTime = () => {
  if (performance.timing) {
    loadTime.value = performance.timing.loadEventEnd - performance.timing.navigationStart
  }
}

const measureBundleSize = () => {
  // Estimate bundle size from loaded resources
  const resources = performance.getEntriesByType('resource')
  let totalSize = 0
  
  resources.forEach((resource: any) => {
    if (resource.transferSize) {
      totalSize += resource.transferSize
    }
  })
  
  bundleSize.value = Math.round(totalSize / 1024)
}

const measureCoreWebVitals = () => {
  // LCP
  const lcpObserver = new PerformanceObserver((list) => {
    const entries = list.getEntries()
    const lastEntry = entries[entries.length - 1]
    lcp.value = Math.round(lastEntry.startTime)
  })
  
  try {
    lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] })
  } catch (e) {
    // LCP not supported
  }

  // FID
  const fidObserver = new PerformanceObserver((list) => {
    const entries = list.getEntries()
    entries.forEach((entry: any) => {
      fid.value = Math.round(entry.processingStart - entry.startTime)
    })
  })
  
  try {
    fidObserver.observe({ entryTypes: ['first-input'] })
  } catch (e) {
    // FID not supported
  }

  // CLS
  let clsValue = 0
  const clsObserver = new PerformanceObserver((list) => {
    const entries = list.getEntries()
    entries.forEach((entry: any) => {
      if (!entry.hadRecentInput) {
        clsValue += entry.value
        cls.value = clsValue
      }
    })
  })
  
  try {
    clsObserver.observe({ entryTypes: ['layout-shift'] })
  } catch (e) {
    // CLS not supported
  }
}

const toggleAutoUpdate = () => {
  autoUpdate.value = !autoUpdate.value
  
  if (autoUpdate.value) {
    startMonitoring()
  } else {
    stopMonitoring()
  }
}

const startMonitoring = () => {
  if (!animationId) {
    animationId = requestAnimationFrame(measureFPS)
  }
  
  // Update other metrics periodically
  const interval = setInterval(() => {
    if (autoUpdate.value) {
      measureMemoryUsage()
      measureLoadTime()
      measureBundleSize()
    } else {
      clearInterval(interval)
    }
  }, 2000)
}

const stopMonitoring = () => {
  if (animationId) {
    cancelAnimationFrame(animationId)
    animationId = null
  }
}

const clearMetrics = () => {
  fpsHistory.value = []
  fps.value = 60
  memoryUsage.value = 0
  loadTime.value = 0
  bundleSize.value = 0
  lcp.value = 0
  fid.value = 0
  cls.value = 0
}

const exportMetrics = () => {
  const metrics = {
    timestamp: new Date().toISOString(),
    fps: fps.value,
    fpsHistory: fpsHistory.value,
    memoryUsage: memoryUsage.value,
    loadTime: loadTime.value,
    bundleSize: bundleSize.value,
    coreWebVitals: {
      lcp: lcp.value,
      fid: fid.value,
      cls: cls.value
    }
  }
  
  const blob = new Blob([JSON.stringify(metrics, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `performance-metrics-${Date.now()}.json`
  a.click()
  URL.revokeObjectURL(url)
}

// Global toggle function
const toggleMonitor = () => {
  showMonitor.value = !showMonitor.value
}

// Lifecycle
onMounted(() => {
  // Initialize measurements
  measureMemoryUsage()
  measureLoadTime()
  measureBundleSize()
  measureCoreWebVitals()
  
  if (autoUpdate.value) {
    startMonitoring()
  }
  
  // Add global keyboard shortcut
  const handleKeydown = (event: KeyboardEvent) => {
    if (event.ctrlKey && event.shiftKey && event.key === 'P') {
      event.preventDefault()
      toggleMonitor()
    }
  }
  
  document.addEventListener('keydown', handleKeydown)
  
  // Cleanup function
  onUnmounted(() => {
    stopMonitoring()
    document.removeEventListener('keydown', handleKeydown)
  })
})

onUnmounted(() => {
  stopMonitoring()
})

// Expose toggle function
defineExpose({
  toggleMonitor,
  showMonitor
})
</script>
