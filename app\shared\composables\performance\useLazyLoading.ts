/**
 * Enhanced Lazy Loading Composable
 * 
 * Comprehensive lazy loading system with intersection observer,
 * preloading strategies, and performance optimizations
 */

import { ref, computed, onMounted, onUnmounted, readonly } from 'vue' // Removed watch, Ref, Added readonly
import { useLogger } from '../core/useLogger.js' // Added .js
import { usePerformance } from './usePerformance.js' // Added .js

// ============================================================================
// LAZY LOADING TYPES
// ============================================================================

export interface LazyLoadConfig {
  rootMargin?: string
  threshold?: number | number[]
  preloadDistance?: number
  retryAttempts?: number
  retryDelay?: number
  placeholder?: string
  errorImage?: string
  fadeInDuration?: number
  enablePreload?: boolean
  preloadStrategy?: 'eager' | 'lazy' | 'viewport' | 'interaction'
}

export interface LazyLoadState {
  isLoading: boolean
  isLoaded: boolean
  hasError: boolean
  error?: Error | undefined // Ensure undefined is allowed
  retryCount: number
  loadTime?: number
}

export interface LazyLoadItem {
  id: string
  element: HTMLElement
  src: string
  state: LazyLoadState
  config: LazyLoadConfig
}

// ============================================================================
// GLOBAL LAZY LOADING STATE
// ============================================================================

const lazyItems = ref(new Map<string, LazyLoadItem>())
const observers = ref(new Map<string, IntersectionObserver>())
const loadQueue = ref<string[]>([])
const isProcessingQueue = ref(false)

// ============================================================================
// LAZY LOADING COMPOSABLE
// ============================================================================

export function useLazyLoading(config: LazyLoadConfig = {}) {
  // ============================================================================
  // CONFIGURATION
  // ============================================================================
  
  const defaultConfig: Required<LazyLoadConfig> = {
    rootMargin: '50px',
    threshold: 0.1,
    preloadDistance: 200,
    retryAttempts: 3,
    retryDelay: 1000,
    placeholder: '',
    errorImage: '',
    fadeInDuration: 300,
    enablePreload: true,
    preloadStrategy: 'viewport'
  }
  
  const lazyConfig = { ...defaultConfig, ...config }
  
  // ============================================================================
  // DEPENDENCIES
  // ============================================================================
  
  const logger = useLogger('LazyLoading')
  const performance = usePerformance()
  
  // ============================================================================
  // STATE
  // ============================================================================
  
  const isSupported = ref(typeof IntersectionObserver !== 'undefined')
  const loadedCount = ref(0)
  const errorCount = ref(0)
  const totalLoadTime = ref(0)
  
  // ============================================================================
  // COMPUTED
  // ============================================================================
  
  const averageLoadTime = computed(() => {
    return loadedCount.value > 0 ? totalLoadTime.value / loadedCount.value : 0
  })
  
  const successRate = computed(() => {
    const total = loadedCount.value + errorCount.value
    return total > 0 ? (loadedCount.value / total) * 100 : 0
  })
  
  // ============================================================================
  // INTERSECTION OBSERVER SETUP
  // ============================================================================
  
  const createObserver = (config: LazyLoadConfig): IntersectionObserver => {
    return new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const itemId = entry.target.getAttribute('data-lazy-id')
            if (itemId) {
              loadItem(itemId)
            }
          }
        })
      },
      (() => {
        const observerOptions: IntersectionObserverInit = {};
        if (config.rootMargin !== undefined) {
          observerOptions.rootMargin = config.rootMargin;
        }
        if (config.threshold !== undefined) {
          observerOptions.threshold = config.threshold;
        }
        return observerOptions;
      })()
    )
  }
  
  // ============================================================================
  // LAZY LOADING METHODS
  // ============================================================================
  
  const observe = (element: HTMLElement, src: string, itemConfig: Partial<LazyLoadConfig> = {}): string => {
    if (!isSupported.value) {
      // Fallback for browsers without IntersectionObserver
      loadImageDirectly(element, src)
      return ''
    }
    
    const itemId = generateItemId()
    const mergedConfig = { ...lazyConfig, ...itemConfig }
    
    // Create lazy load item
    const item: LazyLoadItem = {
      id: itemId,
      element,
      src,
      state: {
        isLoading: false,
        isLoaded: false,
        hasError: false,
        retryCount: 0
      },
      config: mergedConfig
    }
    
    // Store item
    lazyItems.value.set(itemId, item)
    
    // Set up element
    element.setAttribute('data-lazy-id', itemId)
    element.setAttribute('data-src', src)
    
    // Set placeholder
    if (mergedConfig.placeholder) {
      if (element.tagName === 'IMG') {
        (element as HTMLImageElement).src = mergedConfig.placeholder
      } else {
        element.style.backgroundImage = `url(${mergedConfig.placeholder})`
      }
    }
    
    // Get or create observer
    const observerKey = `${mergedConfig.rootMargin}_${mergedConfig.threshold}`
    let observer = observers.value.get(observerKey)
    
    if (!observer) {
      observer = createObserver(mergedConfig)
      observers.value.set(observerKey, observer)
    }
    
    // Start observing
    observer.observe(element)
    
    // Preload if strategy requires it
    if (mergedConfig.enablePreload) {
      handlePreloading(item)
    }
    
    logger.debug('Element added to lazy loading', { itemId, src })
    return itemId
  }
  
  const loadItem = async (itemId: string): Promise<void> => {
    const item = lazyItems.value.get(itemId)
    if (!item || item.state.isLoading || item.state.isLoaded) return
    
    logger.debug('Loading lazy item', { itemId, src: item.src })
    
    const startTime = performance.now()
    item.state.isLoading = true
    
    try {
      await loadWithRetry(item)
      
      item.state.isLoaded = true
      item.state.loadTime = performance.now() - startTime
      
      // Update statistics
      loadedCount.value++
      totalLoadTime.value += item.state.loadTime
      
      // Apply fade-in animation
      if (item.config.fadeInDuration && item.config.fadeInDuration > 0) {
        applyFadeIn(item.element, item.config.fadeInDuration)
      }
      
      // Stop observing
      unobserve(itemId)
      
      logger.debug('Lazy item loaded successfully', { 
        itemId, 
        loadTime: item.state.loadTime 
      })
      
    } catch (error) {
      item.state.hasError = true
      item.state.error = error instanceof Error ? error : new Error('Load failed')
      
      errorCount.value++
      
      // Set error image
      if (item.config.errorImage) {
        setElementSource(item.element, item.config.errorImage)
      }
      
      logger.error('Lazy item load failed', { itemId, error })
      
    } finally {
      item.state.isLoading = false
    }
  }
  
  const loadWithRetry = async (item: LazyLoadItem): Promise<void> => {
    const maxAttempts = (item.config.retryAttempts ?? defaultConfig.retryAttempts) + 1;
    
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        await loadImagePromise(item.src)
        setElementSource(item.element, item.src)
        return
      } catch (error) {
        item.state.retryCount = attempt
        
        if (attempt === maxAttempts) {
          throw error
        }
        
        // Wait before retry
        await new Promise(resolve =>
          setTimeout(resolve, (item.config.retryDelay ?? defaultConfig.retryDelay) * attempt)
        )
        
        logger.debug('Retrying lazy load', { 
          itemId: item.id, 
          attempt, 
          maxAttempts 
        })
      }
    }
  }
  
  const loadImagePromise = (src: string): Promise<void> => {
    return new Promise((resolve, reject) => {
      const img = new Image()
      
      img.onload = () => resolve()
      img.onerror = () => reject(new Error(`Failed to load image: ${src}`))
      
      img.src = src
    })
  }
  
  const setElementSource = (element: HTMLElement, src: string) => {
    if (element.tagName === 'IMG') {
      (element as HTMLImageElement).src = src
    } else {
      element.style.backgroundImage = `url(${src})`
    }
  }
  
  const loadImageDirectly = (element: HTMLElement, src: string) => {
    setElementSource(element, src)
  }
  
  // ============================================================================
  // PRELOADING STRATEGIES
  // ============================================================================
  
  const handlePreloading = (item: LazyLoadItem) => {
    switch (item.config.preloadStrategy) {
      case 'eager':
        // Load immediately
        loadItem(item.id)
        break
        
      case 'viewport':
        // Already handled by intersection observer
        break
        
      case 'interaction':
        // Load on user interaction
        setupInteractionPreload(item)
        break
        
      case 'lazy':
      default:
        // No preloading
        break
    }
  }
  
  const setupInteractionPreload = (item: LazyLoadItem) => {
    const events = ['mouseenter', 'touchstart', 'focus']
    
    const handleInteraction = () => {
      loadItem(item.id)
      
      // Remove event listeners
      events.forEach(event => {
        item.element.removeEventListener(event, handleInteraction)
      })
    }
    
    events.forEach(event => {
      item.element.addEventListener(event, handleInteraction, { passive: true })
    })
  }
  
  const preloadNext = (count: number = 3) => {
    const unloadedItems = Array.from(lazyItems.value.values())
      .filter(item => !item.state.isLoaded && !item.state.isLoading)
      .slice(0, count)
    
    unloadedItems.forEach(item => {
      if (isInViewport(item.element, item.config.preloadDistance)) {
        loadItem(item.id)
      }
    })
  }
  
  // ============================================================================
  // UTILITY FUNCTIONS
  // ============================================================================
  
  const generateItemId = (): string => {
    return `lazy_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }
  
  const isInViewport = (element: HTMLElement, distance: number = 0): boolean => {
    const rect = element.getBoundingClientRect()
    const windowHeight = window.innerHeight || document.documentElement.clientHeight
    const windowWidth = window.innerWidth || document.documentElement.clientWidth
    
    return (
      rect.top >= -distance &&
      rect.left >= -distance &&
      rect.bottom <= windowHeight + distance &&
      rect.right <= windowWidth + distance
    )
  }
  
  const applyFadeIn = (element: HTMLElement, duration: number) => {
    element.style.opacity = '0'
    element.style.transition = `opacity ${duration}ms ease-in-out`
    
    requestAnimationFrame(() => {
      element.style.opacity = '1'
    })
    
    // Clean up transition after animation
    setTimeout(() => {
      element.style.transition = ''
    }, duration)
  }
  
  // ============================================================================
  // MANAGEMENT METHODS
  // ============================================================================
  
  const unobserve = (itemId: string) => {
    const item = lazyItems.value.get(itemId)
    if (!item) return
    
    // Find and stop observing
    for (const observer of observers.value.values()) {
      observer.unobserve(item.element)
    }
    
    // Remove from items
    lazyItems.value.delete(itemId)
    
    logger.debug('Stopped observing lazy item', { itemId })
  }
  
  const unobserveAll = () => {
    // Disconnect all observers
    for (const observer of observers.value.values()) {
      observer.disconnect()
    }
    
    // Clear all data
    observers.value.clear()
    lazyItems.value.clear()
    
    logger.info('All lazy loading observers disconnected')
  }
  
  const retry = (itemId: string) => {
    const item = lazyItems.value.get(itemId)
    if (!item) return
    
    // Reset state
    item.state.hasError = false
    item.state.error = undefined
    item.state.retryCount = 0
    
    // Retry loading
    loadItem(itemId)
  }
  
  const retryAll = () => {
    const failedItems = Array.from(lazyItems.value.values())
      .filter(item => item.state.hasError)
    
    failedItems.forEach(item => retry(item.id))
    
    logger.info('Retrying all failed lazy loads', { count: failedItems.length })
  }
  
  // ============================================================================
  // QUEUE PROCESSING
  // ============================================================================
  
  const processLoadQueue = async () => {
    if (isProcessingQueue.value || loadQueue.value.length === 0) return
    
    isProcessingQueue.value = true
    
    try {
      // Process items in batches to avoid overwhelming the browser
      const batchSize = 3
      
      while (loadQueue.value.length > 0) {
        const batch = loadQueue.value.splice(0, batchSize)
        
        await Promise.all(
          batch.map(itemId => loadItem(itemId))
        )
        
        // Small delay between batches
        if (loadQueue.value.length > 0) {
          await new Promise(resolve => setTimeout(resolve, 100))
        }
      }
    } finally {
      isProcessingQueue.value = false
    }
  }
  
  // ============================================================================
  // METRICS AND MONITORING
  // ============================================================================
  
  const getMetrics = () => {
    return {
      totalItems: lazyItems.value.size,
      loadedCount: loadedCount.value,
      errorCount: errorCount.value,
      averageLoadTime: averageLoadTime.value,
      successRate: successRate.value,
      isSupported: isSupported.value,
      activeObservers: observers.value.size
    }
  }
  
  const getItemState = (itemId: string) => {
    const item = lazyItems.value.get(itemId)
    return item ? { ...item.state } : null
  }
  
  // ============================================================================
  // LIFECYCLE
  // ============================================================================
  
  onMounted(() => {
    // Start queue processing
    processLoadQueue()
    
    // Set up scroll-based preloading
    if (lazyConfig.enablePreload) {
      const handleScroll = throttle(() => {
        preloadNext()
      }, 100)
      
      window.addEventListener('scroll', handleScroll, { passive: true })
      
      onUnmounted(() => {
        window.removeEventListener('scroll', handleScroll)
      })
    }
  })
  
  onUnmounted(() => {
    unobserveAll()
  })
  
  // ============================================================================
  // RETURN INTERFACE
  // ============================================================================
  
  return {
    // State
    isSupported,
    loadedCount: readonly(loadedCount),
    errorCount: readonly(errorCount),
    
    // Computed
    averageLoadTime,
    successRate,
    
    // Methods
    observe,
    loadItem,
    unobserve,
    unobserveAll,
    retry,
    retryAll,
    preloadNext,
    
    // Utilities
    getMetrics,
    getItemState
  }
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

function throttle<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let timeoutId: NodeJS.Timeout | null = null
  let lastExecTime = 0
  
  return (...args: Parameters<T>) => {
    const currentTime = Date.now()
    
    if (currentTime - lastExecTime > delay) {
      func(...args)
      lastExecTime = currentTime
    } else {
      if (timeoutId) {
        clearTimeout(timeoutId)
      }
      
      timeoutId = setTimeout(() => {
        func(...args)
        lastExecTime = Date.now()
      }, delay - (currentTime - lastExecTime))
    }
  }
}
