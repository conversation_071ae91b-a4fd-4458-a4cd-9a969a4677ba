# Modal Component Access System

This document explains how to access and interact with dynamic components inside global modals in the Legal SaaS Frontend application.

## Overview

The enhanced modal system provides several ways to access and interact with components rendered inside modals:

1. **useModalComponentAccess** - Core composable for accessing modal components
2. **createModalWithComponentAccess** - Helper function for creating modals with enhanced access
3. **Enhanced useGlobalModal** - Updated global modal composable with component access methods

## Basic Usage

### Method 1: Using createModalWithComponentAccess (Recommended)

```typescript
import { createModalWithComponentAccess } from '~/composables/useModalComponentAccess'
import MyComponent from '~/components/MyComponent.vue'

const modal = createModalWithComponentAccess({
  title: 'My Modal',
  component: MyComponent,
  size: ComponentSize.LG,
  props: {
    initialData: { name: '<PERSON>' }
  },
  onOk: () => {
    // Access component when ready
    modal.whenReady((component) => {
      // Get component data
      const data = modal.getComponentData()
      console.log('Component data:', data)
      
      // Call component methods
      const result = modal.callMethod('submitForm')
      console.log('Submit result:', result)
      
      // Get/set properties
      const isValid = modal.getProperty('isValid')
      modal.setProperty('status', 'processing')
    })
  }
})
```

### Method 2: Using useModalComponentAccess

```typescript
import { useGlobalModal } from '~/composables/useGlobalModal'
import { useModalComponentAccess } from '~/composables/useModalComponentAccess'

const { openModal } = useGlobalModal()
const { whenModalComponentReady, callModalComponentMethod } = useModalComponentAccess()

const modal = openModal({
  title: 'My Modal',
  component: MyComponent,
  onOk: () => {
    whenModalComponentReady(modal.id, (component) => {
      const result = callModalComponentMethod(modal.id, 'getFormData')
      console.log('Form data:', result)
    })
  }
})
```

## Component Requirements

For components to be accessible from modals, they should use `defineExpose` to expose their data and methods:

```vue
<script setup lang="ts">
import { ref, reactive } from 'vue'

const formData = reactive({
  name: '',
  email: ''
})

const isValid = ref(false)

const validateForm = () => {
  isValid.value = formData.name && formData.email.includes('@')
  return isValid.value
}

const getFormData = () => {
  return { ...formData }
}

const resetForm = () => {
  formData.name = ''
  formData.email = ''
  isValid.value = false
}

// Expose data and methods to parent components/modals
defineExpose({
  // Data
  formData,
  isValid,
  
  // Methods
  validateForm,
  getFormData,
  resetForm
})
</script>
```

## Available Methods

### Component Access Methods

- `getComponent()` - Get the component instance
- `getComponentData()` - Get all component data
- `callMethod(methodName, ...args)` - Call a component method
- `getProperty(propertyName)` - Get a component property
- `setProperty(propertyName, value)` - Set a component property
- `whenReady(callback)` - Execute callback when component is ready

### Modal Management Methods

- `open()` - Open the modal
- `close(reason?)` - Close the modal
- `isOpen` - Reactive reference to modal open state
- `isClosing` - Reactive reference to modal closing state

## Real-World Examples

### Example 1: Form Validation Modal

```typescript
const openFormModal = () => {
  const modal = createModalWithComponentAccess({
    title: 'User Registration',
    component: UserRegistrationForm,
    size: ComponentSize.LG,
    onOk: () => {
      modal.whenReady((component) => {
        // Validate form before closing
        const isValid = modal.callMethod('validateForm')
        if (isValid) {
          const formData = modal.callMethod('getFormData')
          // Process form data
          submitUserRegistration(formData)
          modal.close()
        } else {
          // Show validation errors
          modal.setProperty('showErrors', true)
        }
      })
    }
  })
}
```

### Example 2: Data Import Modal

```typescript
const openImportModal = () => {
  const modal = createModalWithComponentAccess({
    title: 'Import Data',
    component: DataImportComponent,
    size: ComponentSize.XL,
    onOk: () => {
      modal.whenReady((component) => {
        // Get imported data
        const importedData = modal.callMethod('getImportedData')
        const validationResult = modal.callMethod('validateData')
        
        if (validationResult.isValid) {
          // Process imported data
          processImportedData(importedData)
          modal.close()
        } else {
          // Show validation errors
          modal.setProperty('errors', validationResult.errors)
        }
      })
    }
  })
}
```

### Example 3: Settings Configuration Modal

```typescript
const openSettingsModal = (currentSettings) => {
  const modal = createModalWithComponentAccess({
    title: 'Configure Settings',
    component: SettingsComponent,
    props: {
      initialSettings: currentSettings
    },
    onOk: () => {
      modal.whenReady((component) => {
        // Get updated settings
        const newSettings = modal.callMethod('getSettings')
        const hasChanges = modal.callMethod('hasUnsavedChanges')
        
        if (hasChanges) {
          // Save settings
          saveSettings(newSettings)
          modal.close()
        } else {
          modal.close()
        }
      })
    },
    onCancel: () => {
      modal.whenReady((component) => {
        const hasChanges = modal.callMethod('hasUnsavedChanges')
        if (hasChanges) {
          // Show confirmation dialog
          confirmDiscardChanges().then((confirmed) => {
            if (confirmed) {
              modal.close()
            }
          })
        } else {
          modal.close()
        }
      })
    }
  })
}
```

## Best Practices

1. **Always use `whenReady()`** - Component access should always be wrapped in `whenReady()` to ensure the component is mounted
2. **Use `defineExpose`** - Components should explicitly expose their public API
3. **Handle errors gracefully** - Check if methods exist before calling them
4. **Keep component API simple** - Expose only what's necessary for parent interaction
5. **Use TypeScript** - Define interfaces for exposed component APIs

## Troubleshooting

### Component not found
- Ensure the modal is open and the component is mounted
- Use `whenReady()` to wait for component initialization

### Method not found
- Check that the method is exposed using `defineExpose`
- Verify the method name spelling

### Property access issues
- Ensure properties are reactive (ref/reactive)
- Check that properties are exposed in `defineExpose`

## Migration from Old System

If you're migrating from the old modal system:

```typescript
// Old way
const modal = openModal({
  component: MyComponent,
  onOk: () => {
    console.log(modal.componentRef) // May be undefined
  }
})

// New way
const modal = createModalWithComponentAccess({
  component: MyComponent,
  onOk: () => {
    modal.whenReady((component) => {
      const data = modal.getComponentData()
      console.log('Component data:', data)
    })
  }
})
```
