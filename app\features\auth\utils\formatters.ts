/**
 * Authentication Formatters
 * 
 * Utility functions for formatting authentication-related data
 * for display in the user interface
 */

import type { User } from '../types/models.js' // Added .js
import type { PasswordStrength } from '../types/ui.js' // Added .js

// Format user display name
export const formatUserDisplayName = (user: User): string => {
  if (user.firstName && user.lastName) {
    return `${user.firstName} ${user.lastName}`
  }
  
  if (user.firstName) {
    return user.firstName
  }
  
  if (user.lastName) {
    return user.lastName
  }
  
  return user.email.split('@')[0]
}

// Format user initials for avatars
export const formatUserInitials = (user: User): string => {
  if (user.firstName && user.lastName) {
    return `${user.firstName.charAt(0)}${user.lastName.charAt(0)}`.toUpperCase()
  }
  
  if (user.firstName) {
    return user.firstName.charAt(0).toUpperCase()
  }
  
  if (user.lastName) {
    return user.lastName.charAt(0).toUpperCase()
  }
  
  return user.email.charAt(0).toUpperCase()
}

// Format phone number for display
export const formatPhoneNumber = (phone: string): string => {
  if (!phone) return ''
  
  // Remove all non-digit characters
  const digits = phone.replace(/\D/g, '')
  
  // Format US phone numbers
  if (digits.length === 10) {
    return `(${digits.slice(0, 3)}) ${digits.slice(3, 6)}-${digits.slice(6)}`
  }
  
  // Format international numbers with country code
  if (digits.length === 11 && digits.startsWith('1')) {
    return `+1 (${digits.slice(1, 4)}) ${digits.slice(4, 7)}-${digits.slice(7)}`
  }
  
  // Return original if can't format
  return phone
}

// Format date for display
export const formatDate = (date: string | Date, options?: Intl.DateTimeFormatOptions): string => {
  if (!date) return ''
  
  const dateObj = typeof date === 'string' ? new Date(date) : date
  
  if (isNaN(dateObj.getTime())) return ''
  
  const defaultOptions: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  }
  
  return dateObj.toLocaleDateString(undefined, { ...defaultOptions, ...options })
}

// Format relative time (e.g., "2 hours ago")
export const formatRelativeTime = (date: string | Date): string => {
  if (!date) return ''
  
  const dateObj = typeof date === 'string' ? new Date(date) : date
  const now = new Date()
  const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000)
  
  if (diffInSeconds < 60) {
    return 'Just now'
  }
  
  const diffInMinutes = Math.floor(diffInSeconds / 60)
  if (diffInMinutes < 60) {
    return `${diffInMinutes} minute${diffInMinutes === 1 ? '' : 's'} ago`
  }
  
  const diffInHours = Math.floor(diffInMinutes / 60)
  if (diffInHours < 24) {
    return `${diffInHours} hour${diffInHours === 1 ? '' : 's'} ago`
  }
  
  const diffInDays = Math.floor(diffInHours / 24)
  if (diffInDays < 7) {
    return `${diffInDays} day${diffInDays === 1 ? '' : 's'} ago`
  }
  
  const diffInWeeks = Math.floor(diffInDays / 7)
  if (diffInWeeks < 4) {
    return `${diffInWeeks} week${diffInWeeks === 1 ? '' : 's'} ago`
  }
  
  const diffInMonths = Math.floor(diffInDays / 30)
  if (diffInMonths < 12) {
    return `${diffInMonths} month${diffInMonths === 1 ? '' : 's'} ago`
  }
  
  const diffInYears = Math.floor(diffInDays / 365)
  return `${diffInYears} year${diffInYears === 1 ? '' : 's'} ago`
}

// Format password strength for display
export const formatPasswordStrength = (strength: PasswordStrength): {
  label: string
  color: string
  percentage: number
} => {
  const strengthMap: Record<PasswordStrength['level'], { label: string; color: string; percentage: number }> = {
    weak: { label: 'Weak', color: 'red', percentage: 25 },
    fair: { label: 'Fair', color: 'orange', percentage: 50 },
    good: { label: 'Good', color: 'yellow', percentage: 75 },
    strong: { label: 'Strong', color: 'green', percentage: 100 }
  };
  
  return strengthMap[strength.level];
}

// Format role for display
export const formatRole = (role: string): string => {
  return role
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(' ')
}

// Format permissions for display
export const formatPermissions = (permissions: string[]): string[] => {
  return permissions.map(permission => 
    permission
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ')
  )
}

// Format session duration
export const formatSessionDuration = (startTime: string | Date, endTime?: string | Date): string => {
  const start = typeof startTime === 'string' ? new Date(startTime) : startTime
  const end = endTime ? (typeof endTime === 'string' ? new Date(endTime) : endTime) : new Date()
  
  const durationMs = end.getTime() - start.getTime()
  const durationMinutes = Math.floor(durationMs / (1000 * 60))
  
  if (durationMinutes < 60) {
    return `${durationMinutes} minute${durationMinutes === 1 ? '' : 's'}`
  }
  
  const durationHours = Math.floor(durationMinutes / 60)
  const remainingMinutes = durationMinutes % 60
  
  if (durationHours < 24) {
    return remainingMinutes > 0 
      ? `${durationHours}h ${remainingMinutes}m`
      : `${durationHours} hour${durationHours === 1 ? '' : 's'}`
  }
  
  const durationDays = Math.floor(durationHours / 24)
  const remainingHours = durationHours % 24
  
  return remainingHours > 0
    ? `${durationDays}d ${remainingHours}h`
    : `${durationDays} day${durationDays === 1 ? '' : 's'}`
}

// Format file size
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// Format backup codes for display
export const formatBackupCodes = (codes: string[]): string[] => {
  return codes.map(code => {
    // Insert a space every 4 characters for readability
    return code.replace(/(.{4})/g, '$1 ').trim()
  })
}

// Format 2FA token for display (with spacing)
export const format2FAToken = (token: string): string => {
  if (token.length !== 6) return token
  return `${token.slice(0, 3)} ${token.slice(3)}`
}

// Format email for display (mask for privacy)
export const formatEmailForDisplay = (email: string, maskLevel: 'none' | 'partial' | 'full' = 'none'): string => {
  if (maskLevel === 'none') return email
  
  const [localPart, domain] = email.split('@')
  
  if (maskLevel === 'full') {
    return `${'*'.repeat(localPart.length)}@${domain}`
  }
  
  // Partial masking
  if (localPart.length <= 2) {
    return `${localPart.charAt(0)}*@${domain}`
  }
  
  const visibleChars = Math.max(1, Math.floor(localPart.length / 3))
  const maskedChars = localPart.length - (visibleChars * 2)
  
  return `${localPart.slice(0, visibleChars)}${'*'.repeat(maskedChars)}${localPart.slice(-visibleChars)}@${domain}`
}

// Format account status for display
export const formatAccountStatus = (status: string): {
  label: string
  color: string
  icon: string
} => {
  const statusMap: Record<string, { label: string; color: string; icon: string }> = {
    active: { label: 'Active', color: 'green', icon: 'check-circle' },
    inactive: { label: 'Inactive', color: 'gray', icon: 'minus-circle' },
    suspended: { label: 'Suspended', color: 'red', icon: 'x-circle' },
    pending_verification: { label: 'Pending Verification', color: 'yellow', icon: 'clock' },
    locked: { label: 'Locked', color: 'red', icon: 'lock-closed' },
    deleted: { label: 'Deleted', color: 'gray', icon: 'trash' }
  }
  
  return statusMap[status] || { label: status, color: 'gray', icon: 'question-mark-circle' }
}

// Format timezone for display
export const formatTimezone = (timezone: string): string => {
  try {
    const formatter = new Intl.DateTimeFormat('en', {
      timeZone: timezone,
      timeZoneName: 'long'
    })
    
    const parts = formatter.formatToParts(new Date())
    const timeZoneName = parts.find(part => part.type === 'timeZoneName')?.value
    
    return timeZoneName || timezone
  } catch {
    return timezone
  }
}

// Format currency (for subscription/billing)
export const formatCurrency = (amount: number, currency = 'USD'): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency
  }).format(amount)
}

// Format percentage
export const formatPercentage = (value: number, decimals = 0): string => {
  return `${(value * 100).toFixed(decimals)}%`
}

// Truncate text with ellipsis
export const truncateText = (text: string, maxLength: number): string => {
  if (text.length <= maxLength) return text
  return text.slice(0, maxLength - 3) + '...'
}

// Format API error messages for user display
export const formatApiError = (error: any): string => {
  if (typeof error === 'string') return error
  
  if (error?.message) return error.message
  if (error?.data?.message) return error.data.message
  if (error?.response?.data?.message) return error.response.data.message
  
  return 'An unexpected error occurred'
}
