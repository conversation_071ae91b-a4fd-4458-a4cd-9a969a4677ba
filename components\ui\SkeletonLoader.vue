<template>
  <div class="animate-pulse">
    <!-- Table Skeleton -->
    <div v-if="type === 'table'" class="space-y-4">
      <!-- Table Header -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex items-center justify-between mb-4">
          <div class="h-6 bg-gray-200 dark:bg-gray-700 rounded w-48"></div>
          <div class="flex space-x-2">
            <div class="h-8 bg-gray-200 dark:bg-gray-700 rounded w-20"></div>
            <div class="h-8 bg-gray-200 dark:bg-gray-700 rounded w-24"></div>
          </div>
        </div>
        <div class="h-10 bg-gray-200 dark:bg-gray-700 rounded w-full"></div>
      </div>
      
      <!-- Table Content -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
        <!-- Table Header Row -->
        <div class="bg-gray-50 dark:bg-gray-800/50 px-6 py-3 border-b border-gray-200 dark:border-gray-700">
          <div class="grid grid-cols-6 gap-4">
            <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
            <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
            <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
            <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
            <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
            <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
          </div>
        </div>
        
        <!-- Table Rows -->
        <div v-for="i in rows" :key="i" class="px-6 py-4 border-b border-gray-200 dark:border-gray-700 last:border-b-0">
          <div class="grid grid-cols-6 gap-4 items-center">
            <!-- Tenant Info -->
            <div class="flex items-center space-x-3">
              <div class="h-10 w-10 bg-gray-200 dark:bg-gray-700 rounded-full"></div>
              <div class="space-y-2">
                <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-24"></div>
                <div class="h-3 bg-gray-200 dark:bg-gray-700 rounded w-16"></div>
              </div>
            </div>
            
            <!-- Plan -->
            <div class="h-6 bg-gray-200 dark:bg-gray-700 rounded w-16"></div>
            
            <!-- Status -->
            <div class="h-6 bg-gray-200 dark:bg-gray-700 rounded w-20"></div>
            
            <!-- Users -->
            <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-8"></div>
            
            <!-- Created -->
            <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-20"></div>
            
            <!-- Actions -->
            <div class="flex space-x-2 justify-end">
              <div class="h-8 bg-gray-200 dark:bg-gray-700 rounded w-8"></div>
              <div class="h-8 bg-gray-200 dark:bg-gray-700 rounded w-8"></div>
              <div class="h-8 bg-gray-200 dark:bg-gray-700 rounded w-8"></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Cards Skeleton -->
    <div v-else-if="type === 'cards'" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
      <div v-for="i in rows" :key="i" class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <!-- Card Header -->
        <div class="flex items-center space-x-3 mb-4">
          <div class="h-12 w-12 bg-gray-200 dark:bg-gray-700 rounded-full"></div>
          <div class="flex-1 space-y-2">
            <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
            <div class="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
          </div>
        </div>
        
        <!-- Plan Badge -->
        <div class="h-6 bg-gray-200 dark:bg-gray-700 rounded w-16 mb-4"></div>
        
        <!-- Stats -->
        <div class="grid grid-cols-2 gap-4 mb-4">
          <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-3">
            <div class="h-6 bg-gray-200 dark:bg-gray-700 rounded w-8 mb-1"></div>
            <div class="h-3 bg-gray-200 dark:bg-gray-700 rounded w-12"></div>
          </div>
          <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-3">
            <div class="h-6 bg-gray-200 dark:bg-gray-700 rounded w-12 mb-1"></div>
            <div class="h-3 bg-gray-200 dark:bg-gray-700 rounded w-16"></div>
          </div>
        </div>
        
        <!-- Footer -->
        <div class="flex items-center justify-between">
          <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-16"></div>
          <div class="h-8 bg-gray-200 dark:bg-gray-700 rounded w-8"></div>
        </div>
      </div>
    </div>

    <!-- Dashboard Skeleton -->
    <div v-else-if="type === 'dashboard'" class="space-y-6">
      <!-- Metrics Cards -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div v-for="i in 4" :key="i" class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div class="flex items-center justify-between">
            <div class="space-y-2">
              <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-20"></div>
              <div class="h-8 bg-gray-200 dark:bg-gray-700 rounded w-16"></div>
              <div class="h-3 bg-gray-200 dark:bg-gray-700 rounded w-24"></div>
            </div>
            <div class="h-12 w-12 bg-gray-200 dark:bg-gray-700 rounded-lg"></div>
          </div>
        </div>
      </div>
      
      <!-- Charts -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div class="h-6 bg-gray-200 dark:bg-gray-700 rounded w-32 mb-6"></div>
          <div class="h-48 bg-gray-200 dark:bg-gray-700 rounded"></div>
        </div>
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div class="h-6 bg-gray-200 dark:bg-gray-700 rounded w-24 mb-6"></div>
          <div class="h-48 bg-gray-200 dark:bg-gray-700 rounded"></div>
        </div>
      </div>
    </div>

    <!-- List Skeleton -->
    <div v-else-if="type === 'list'" class="space-y-3">
      <div v-for="i in rows" :key="i" class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
        <div class="flex items-center space-x-4">
          <div class="h-10 w-10 bg-gray-200 dark:bg-gray-700 rounded-full"></div>
          <div class="flex-1 space-y-2">
            <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
            <div class="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
          </div>
          <div class="h-8 bg-gray-200 dark:bg-gray-700 rounded w-20"></div>
        </div>
      </div>
    </div>

    <!-- Form Skeleton -->
    <div v-else-if="type === 'form'" class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
      <div class="space-y-6">
        <div class="h-8 bg-gray-200 dark:bg-gray-700 rounded w-48"></div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div class="space-y-2">
            <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-20"></div>
            <div class="h-10 bg-gray-200 dark:bg-gray-700 rounded w-full"></div>
          </div>
          <div class="space-y-2">
            <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-16"></div>
            <div class="h-10 bg-gray-200 dark:bg-gray-700 rounded w-full"></div>
          </div>
        </div>
        
        <div class="space-y-2">
          <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-24"></div>
          <div class="h-24 bg-gray-200 dark:bg-gray-700 rounded w-full"></div>
        </div>
        
        <div class="flex space-x-4">
          <div class="h-10 bg-gray-200 dark:bg-gray-700 rounded w-24"></div>
          <div class="h-10 bg-gray-200 dark:bg-gray-700 rounded w-20"></div>
        </div>
      </div>
    </div>

    <!-- Text Skeleton -->
    <div v-else-if="type === 'text'" class="space-y-3">
      <div v-for="i in rows" :key="i" class="space-y-2">
        <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-full"></div>
        <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-5/6"></div>
        <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-4/6"></div>
      </div>
    </div>

    <!-- Custom Skeleton -->
    <div v-else class="space-y-4">
      <div v-for="i in rows" :key="i" class="h-16 bg-gray-200 dark:bg-gray-700 rounded"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  type?: 'table' | 'cards' | 'dashboard' | 'list' | 'form' | 'text' | 'custom'
  rows?: number
  animated?: boolean
}

withDefaults(defineProps<Props>(), {
  type: 'custom',
  rows: 5,
  animated: true
})
</script>

<style scoped>
@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Enhanced shimmer effect */
.animate-pulse > * {
  background: linear-gradient(90deg, 
    var(--color-gray-200) 25%, 
    var(--color-gray-300) 50%, 
    var(--color-gray-200) 75%
  );
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

/* Dark mode shimmer */
@media (prefers-color-scheme: dark) {
  .animate-pulse > * {
    background: linear-gradient(90deg, 
      var(--color-gray-700) 25%, 
      var(--color-gray-600) 50%, 
      var(--color-gray-700) 75%
    );
    background-size: 200px 100%;
  }
}

/* Reduce motion for accessibility */
@media (prefers-reduced-motion: reduce) {
  .animate-pulse {
    animation: none;
  }
  
  .animate-pulse > * {
    animation: none;
    background: var(--color-gray-200);
  }
  
  @media (prefers-color-scheme: dark) {
    .animate-pulse > * {
      background: var(--color-gray-700);
    }
  }
}
</style>
