<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900">


    <!-- Main Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
        <!-- Main Form -->
        <div class="lg:col-span-3 space-y-6">
          <form @submit.prevent="handleSubmit">
            <!-- Template Information Card -->
            <UiCard class="p-6">
              <div class="flex items-center space-x-3 mb-6">
                <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                  <Icon name="heroicons:information-circle" class="w-5 h-5 text-blue-600 dark:text-blue-400" />
                </div>
                <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Template Information</h2>
              </div>

              <div class="space-y-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div class="space-y-2">
                    <UiInput
                      id="templateName"
                      v-model="formData.name"
                      label="Template Name"
                      placeholder="e.g., Service Agreement Template"
                      required
                      :error="errors.name"
                    />
                    <p class="text-xs text-gray-500 dark:text-gray-400">
                      Choose a descriptive name for easy identification
                    </p>
                  </div>

                  <div class="space-y-2">
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      Document Category
                    </label>
                    <div class="relative">
                      <select
                        v-model="formData.category"
                        class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                        required
                      >
                        <option value="">Select a category</option>
                        <option
                          v-for="category in documentCategories"
                          :key="category.id"
                          :value="category.id"
                        >
                          {{ category.name }}
                        </option>
                      </select>
                      <Icon name="heroicons:chevron-down" class="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" />
                    </div>
                    <p class="text-xs text-gray-500 dark:text-gray-400">
                      Categorize your template for better organization
                    </p>
                  </div>
                </div>

                <div class="space-y-2">
                  <UiTextarea
                    id="templateDescription"
                    v-model="formData.description"
                    label="Description"
                    placeholder="Describe the purpose and usage of this template..."
                    :rows="3"
                  />
                  <p class="text-xs text-gray-500 dark:text-gray-400">
                    Help others understand when and how to use this template
                  </p>
                </div>

                <!-- Category Details -->
                <div v-if="selectedCategory" class="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                  <div class="flex items-start space-x-3">
                    <Icon :name="selectedCategory.icon" class="w-5 h-5 text-blue-600 dark:text-blue-400 mt-0.5" />
                    <div>
                      <h3 class="text-sm font-medium text-blue-900 dark:text-blue-100">{{ selectedCategory.name }}</h3>
                      <p class="text-sm text-blue-700 dark:text-blue-300 mt-1">{{ selectedCategory.description }}</p>
                      <div v-if="selectedCategory.metadata" class="flex flex-wrap gap-2 mt-2">
                        <span
                          v-if="selectedCategory.metadata.requiresSignature"
                          class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
                        >
                          <Icon name="heroicons:pencil" class="w-3 h-3 mr-1" />
                          Signature Required
                        </span>
                        <span
                          v-if="selectedCategory.metadata.hasVariables"
                          class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200"
                        >
                          <Icon name="heroicons:variable" class="w-3 h-3 mr-1" />
                          Dynamic Variables
                        </span>
                        <span
                          v-if="selectedCategory.metadata.notarizationRequired"
                          class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-200"
                        >
                          <Icon name="heroicons:shield-check" class="w-3 h-3 mr-1" />
                          Notarization Required
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </UiCard>

            <!-- Document Content Editor -->
            <UiCard class="p-6">
              <div class="flex items-center justify-between mb-6">
                <div class="flex items-center space-x-3">
                  <div class="w-8 h-8 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center">
                    <Icon name="heroicons:document-text" class="w-5 h-5 text-green-600 dark:text-green-400" />
                  </div>
                  <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Document Content</h2>
                </div>
                
                <div class="flex items-center space-x-2">
                  <UiButton @click="insertVariable" variant="outline" size="sm">
                    <Icon name="heroicons:plus" class="w-4 h-4 mr-1" />
                    Variable
                  </UiButton>
                  <UiButton @click="showTemplateLibrary = true" variant="outline" size="sm">
                    <Icon name="heroicons:book-open" class="w-4 h-4 mr-1" />
                    Library
                  </UiButton>
                </div>
              </div>

              <DocumentTemplateEditor
                v-model:content="formData.content"
                v-model:variables="formData.variables"
                :template-data="formData"
                :category="selectedCategory"
                @content-change="handleContentChange"
              />
            </UiCard>

            <!-- Form Actions -->
            <div class="flex items-center justify-between">
              <UiButton @click="$router.back()" variant="outline" type="button">
                <Icon name="heroicons:arrow-left" class="w-4 h-4 mr-2" />
                Cancel
              </UiButton>
              
              <div class="flex items-center space-x-3">
                <UiButton @click="saveDraft" variant="outline" type="button" :disabled="isSaving || !formData.name.trim()">
                  <Icon name="heroicons:document-duplicate" class="w-4 h-4 mr-2" />
                  Save as Draft
                </UiButton>
                <UiButton type="submit" :disabled="isSaving || !isFormValid" class="bg-blue-600 hover:bg-blue-700">
                  <Icon name="heroicons:check" class="w-4 h-4 mr-2" />
                  Create Template
                </UiButton>
              </div>
            </div>
          </form>
        </div>

        <!-- Sidebar -->
        <div class="lg:col-span-1 space-y-6">
          <!-- Quick Stats -->
          <UiCard class="p-4">
            <h3 class="text-sm font-medium text-gray-900 dark:text-white mb-3">Template Stats</h3>
            <div class="space-y-3">
              <div class="flex items-center justify-between text-sm">
                <span class="text-gray-600 dark:text-gray-400">Word Count</span>
                <span class="font-medium text-gray-900 dark:text-white">{{ wordCount }}</span>
              </div>
              <div class="flex items-center justify-between text-sm">
                <span class="text-gray-600 dark:text-gray-400">Variables</span>
                <span class="font-medium text-gray-900 dark:text-white">{{ formData.variables.length }}</span>
              </div>
              <div class="flex items-center justify-between text-sm">
                <span class="text-gray-600 dark:text-gray-400">Completion</span>
                <span class="font-medium text-gray-900 dark:text-white">{{ completionPercentage }}%</span>
              </div>
            </div>
          </UiCard>

          <!-- Variables Panel -->
          <UiCard class="p-4" v-if="formData.variables.length > 0">
            <h3 class="text-sm font-medium text-gray-900 dark:text-white mb-3">Template Variables</h3>
            <div class="space-y-2">
              <div
                v-for="variable in formData.variables"
                :key="variable"
                class="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-700 rounded-lg"
              >
                <span class="text-sm font-mono text-gray-700 dark:text-gray-300">{{ variable }}</span>
                <UiButton @click="removeVariable(variable)" variant="ghost" size="sm">
                  <Icon name="heroicons:x-mark" class="w-3 h-3" />
                </UiButton>
              </div>
            </div>
          </UiCard>

          <!-- Help & Tips -->
          <UiCard class="p-4">
            <h3 class="text-sm font-medium text-gray-900 dark:text-white mb-3">Tips & Guidelines</h3>
            <div class="space-y-3 text-sm text-gray-600 dark:text-gray-400">
              <div class="flex items-start space-x-2">
                <Icon name="heroicons:light-bulb" class="w-4 h-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                <p>Use variables like {clientName} for dynamic content</p>
              </div>
              <div class="flex items-start space-x-2">
                <Icon name="heroicons:shield-check" class="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                <p>Review legal compliance before publishing</p>
              </div>
              <div class="flex items-start space-x-2">
                <Icon name="heroicons:document-duplicate" class="w-4 h-4 text-blue-500 mt-0.5 flex-shrink-0" />
                <p>Save drafts frequently to avoid losing work</p>
              </div>
            </div>
          </UiCard>
        </div>
      </div>
    </div>

    <!-- Template Library Modal -->
    <DocumentTemplateLibraryModal
      v-if="showTemplateLibrary"
      @close="showTemplateLibrary = false"
      @select="insertTemplate"
    />

    <!-- Variable Insert Modal -->
    <VariableInsertModal
      v-if="showVariableModal"
      @close="showVariableModal = false"
      @insert="handleVariableInsert"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive, watch, defineAsyncComponent } from 'vue'
import { useRouter } from 'vue-router'
import { useTemplateStore } from '~/stores/template'
import { getTemplateCategoriesByType, getTemplateCategoryById } from '~/utils/templateCategories'
import { PlatformRoles, TenantRoles } from '~/app/features/auth/constants/roles'

// Lazy load components
const DocumentTemplateEditor = defineAsyncComponent(() => import('~/components/template-editors/DocumentTemplateEditor.vue'))
const DocumentTemplateLibraryModal = defineAsyncComponent(() => import('~/components/template-modals/DocumentTemplateLibraryModal.vue'))
const VariableInsertModal = defineAsyncComponent(() => import('~/components/template-modals/VariableInsertModal.vue'))

// Page meta
definePageMeta({
  layout: 'dashboard',
  title: 'Create Document Template',
  description: 'Create a new legal document template with dynamic content and variables',
  pageHeaderIcon: 'heroicons:document-text',
  showPageHeader: true,
  showPageHeaderTitle: true,
  pageHeaderActions: () => {
    return [
      {
        label: 'Save as Draft',
        icon: 'i-heroicons:document-duplicate',
        color: 'gray',
        variant: 'outline',
        click: () => 'saveDraft()'
      },
      {
        label: 'Preview',
        icon: 'i-heroicons:eye',
        color: 'info',
        variant: 'outline',
        click: () => 'previewTemplate()'
      }
    ].reverse()
  },
  middleware: ['rbac'],
  roles: [PlatformRoles.SUPER_ADMIN, TenantRoles.TENANT_OWNER, TenantRoles.ADMIN, TenantRoles.LAWYER],
  breadcrumb: [
    { label: 'Dashboard', href: '/dashboard' },
    { label: 'Templates', href: '/dashboard/templates' },
    { label: 'Document Templates', href: '/dashboard/templates/document' },
    { label: 'Create' },
  ],
})

// Composables
const router = useRouter()
const templateStore = useTemplateStore()

// State
const isSaving = ref(false)
const showTemplateLibrary = ref(false)
const showVariableModal = ref(false)
const errors = ref<Record<string, string>>({})

const formData = reactive({
  name: '',
  description: '',
  category: '',
  content: '',
  variables: [] as string[],
  type: 'document',
  isDraft: false,
  metadata: {},
})

// Computed
const documentCategories = computed(() => getTemplateCategoriesByType('document'))

const selectedCategory = computed(() =>
  formData.category ? getTemplateCategoryById(formData.category) : null
)

const wordCount = computed(() => {
  const text = formData.content.replace(/<[^>]*>/g, '').trim()
  return text ? text.split(/\s+/).length : 0
})

const completionPercentage = computed(() => {
  let score = 0
  if (formData.name.trim()) score += 25
  if (formData.category) score += 25
  if (formData.description.trim()) score += 25
  if (formData.content.trim()) score += 25
  return score
})

const isFormValid = computed(() =>
  formData.name.trim() &&
  formData.category &&
  formData.content.trim() &&
  !Object.keys(errors.value).length
)

const canPreview = computed(() =>
  formData.content.trim() && formData.name.trim()
)

// Watchers
watch(() => formData.name, (newName) => {
  if (newName.trim() && errors.value.name) {
    delete errors.value.name
  }
})

watch(() => formData.category, (newCategory) => {
  if (newCategory && selectedCategory.value?.metadata) {
    formData.metadata = { ...selectedCategory.value.metadata }
  }
})

// Methods
const validateForm = () => {
  errors.value = {}

  if (!formData.name.trim()) {
    errors.value.name = 'Template name is required'
  } else if (formData.name.length < 3) {
    errors.value.name = 'Template name must be at least 3 characters'
  }

  if (!formData.category) {
    errors.value.category = 'Please select a category'
  }

  if (!formData.content.trim()) {
    errors.value.content = 'Template content is required'
  }

  return Object.keys(errors.value).length === 0
}

const handleSubmit = async () => {
  if (!validateForm()) return

  isSaving.value = true
  try {
    const template = await templateStore.createTemplate({
      ...formData,
      isDraft: false,
    })

    if (template) {
      await router.push(`/dashboard/templates/document/${template.id}`)
    }
  } catch (error) {
    console.error('Error creating template:', error)
    // Handle error notification
  } finally {
    isSaving.value = false
  }
}

const saveDraft = async () => {
  if (!formData.name.trim()) {
    errors.value.name = 'Template name is required to save draft'
    return
  }

  isSaving.value = true
  try {
    const template = await templateStore.createTemplate({
      ...formData,
      isDraft: true,
    })

    if (template) {
      await router.push(`/dashboard/templates/document/${template.id}/edit`)
    }
  } catch (error) {
    console.error('Error saving draft:', error)
  } finally {
    isSaving.value = false
  }
}

const previewTemplate = () => {
  if (!canPreview.value) return

  // Open preview in new tab or modal
  const previewData = {
    name: formData.name,
    content: formData.content,
    variables: formData.variables,
    category: selectedCategory.value
  }

  // Store preview data temporarily and navigate
  sessionStorage.setItem('templatePreview', JSON.stringify(previewData))
  window.open('/dashboard/templates/preview', '_blank')
}

const insertVariable = () => {
  showVariableModal.value = true
}

const handleVariableInsert = (variable: string) => {
  if (!formData.variables.includes(variable)) {
    formData.variables.push(variable)
  }
  showVariableModal.value = false
}

const removeVariable = (variable: string) => {
  const index = formData.variables.indexOf(variable)
  if (index > -1) {
    formData.variables.splice(index, 1)
  }
}

const insertTemplate = (template: any) => {
  formData.content = template.content
  formData.variables = [...formData.variables, ...template.variables]
  showTemplateLibrary.value = false
}

const handleContentChange = (content: string, variables: string[]) => {
  formData.content = content
  formData.variables = variables
}
</script>
