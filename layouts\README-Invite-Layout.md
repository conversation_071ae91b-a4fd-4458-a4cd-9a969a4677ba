# Invite Layout Documentation

## Overview

The `invite.vue` layout is specifically designed for invitation-related pages in the Legal SaaS frontend. It provides a professional, branded experience for users accepting invitations, checking invitation status, or completing invitation-related workflows.

## Features

### Visual Design
- **Split Layout**: Left side branding panel (desktop) with right side form container
- **Responsive Design**: Mobile-first approach with adaptive layouts
- **Brand Consistency**: Uses Legal SaaS branding colors and typography
- **Animated Elements**: Subtle animations and floating elements for engagement
- **Dark Mode Support**: Full dark mode compatibility

### Functionality
- **Notification System**: Built-in success, error, and warning message handling
- **Loading States**: Integrated loading overlay and progress indicators
- **Progress Tracking**: Optional step-by-step progress indicator
- **Help Integration**: Built-in help links and support contact information
- **Accessibility**: WCAG compliant with proper focus management and reduced motion support

## Usage

### Basic Implementation

```vue
<template>
  <div class="space-y-6">
    <!-- Your invite page content here -->
    <div class="text-center">
      <h2 class="text-3xl font-bold text-gray-900 dark:text-white">
        Accept Invitation
      </h2>
      <p class="text-gray-600 dark:text-gray-400">
        Complete your account setup
      </p>
    </div>
    
    <!-- Form content -->
    <forms-form :schema="inviteSchema" @submit="handleSubmit" />
  </div>
</template>

<script setup lang="ts">
definePageMeta({
  layout: 'invite',
  title: 'Accept Invitation',
  subtitle: 'Join the platform'
});
</script>
```

### Using Layout Notifications

The invite layout provides a notification system that can be injected into child components:

```vue
<script setup lang="ts">
import { inject } from 'vue';

// Inject notification methods from layout
const inviteNotifications = inject('inviteNotifications') as any;

const handleSubmit = async (data: any) => {
  try {
    inviteNotifications?.setLoading(true);
    
    // Process invitation
    await processInvitation(data);
    
    inviteNotifications?.setSuccess('Invitation accepted successfully!');
  } catch (error) {
    inviteNotifications?.setError('Failed to process invitation');
  } finally {
    inviteNotifications?.setLoading(false);
  }
};
</script>
```

### Available Notification Methods

```typescript
interface InviteNotifications {
  // Set messages
  setError(message: string, autoHide?: boolean): void;
  setSuccess(message: string, autoHide?: boolean): void;
  setWarning(message: string, autoHide?: boolean): void;
  
  // Clear messages
  clearError(): void;
  clearSuccess(): void;
  clearWarning(): void;
  
  // Loading and progress
  setLoading(loading: boolean): void;
  setProgress(show: boolean): void;
  
  // State
  isLoading: Ref<boolean>;
  showProgress: Ref<boolean>;
}
```

### Progress Indicator

Enable the progress indicator for multi-step invitation flows:

```vue
<script setup lang="ts">
const inviteNotifications = inject('inviteNotifications') as any;

onMounted(() => {
  // Show progress indicator
  inviteNotifications?.setProgress(true);
});
</script>
```

## Layout Structure

### Left Panel (Desktop Only)
- **Branding Section**: Logo and welcome message
- **Feature Highlights**: Key platform benefits
- **Trust Indicators**: Security and compliance badges
- **Animated Background**: Subtle floating elements

### Right Panel (All Devices)
- **Mobile Header**: Logo and title for mobile devices
- **Progress Indicator**: Optional step tracker
- **Notification Area**: Success, error, and warning messages
- **Content Slot**: Main page content
- **Help Section**: Support links and contact information
- **Footer**: Copyright and legal links

## Customization

### Page Metadata

Configure page-specific settings using `definePageMeta`:

```vue
<script setup lang="ts">
definePageMeta({
  layout: 'invite',
  title: 'Custom Invite Title',
  subtitle: 'Custom subtitle',
  meta: [
    {
      name: 'description',
      content: 'Custom page description for SEO'
    }
  ]
});
</script>
```

### Styling

The layout uses Tailwind CSS with custom animations. Key classes:

- `animate-fade-in`: Fade in animation
- `animate-fade-in-up`: Fade in with upward motion
- `animate-float`: Floating animation for background elements
- `animate-bounce-in`: Bounce effect for notifications

### Accessibility Features

- **Reduced Motion**: Respects `prefers-reduced-motion` setting
- **Focus Management**: Proper focus indicators and keyboard navigation
- **Screen Reader Support**: Semantic HTML and ARIA labels
- **Color Contrast**: WCAG AA compliant color combinations

## Examples

### Accept Invitation Page
```vue
<!-- pages/auth/accept-invite/[token].vue -->
<template>
  <div class="space-y-6">
    <div class="text-center">
      <h2 class="text-3xl font-bold text-gray-900 dark:text-white">
        Accept Invitation
      </h2>
    </div>
    
    <forms-form 
      :schema="acceptInviteSchema" 
      @submit="handleAcceptInvite" 
    />
  </div>
</template>

<script setup lang="ts">
definePageMeta({
  layout: 'invite'
});
</script>
```

### Invitation Portal
```vue
<!-- pages/invite/index.vue -->
<template>
  <div class="space-y-6">
    <div class="text-center">
      <h2 class="text-3xl font-bold text-gray-900 dark:text-white">
        Check Your Invitation
      </h2>
    </div>
    
    <!-- Email input and check button -->
  </div>
</template>

<script setup lang="ts">
definePageMeta({
  layout: 'invite'
});
</script>
```

## Best Practices

1. **Keep Content Focused**: The layout is optimized for invitation workflows
2. **Use Notifications**: Leverage the built-in notification system for user feedback
3. **Progressive Enhancement**: Start with basic functionality, add enhancements
4. **Mobile First**: Design for mobile devices first, then enhance for desktop
5. **Accessibility**: Always test with keyboard navigation and screen readers

## Related Files

- `layouts/invite.vue` - Main layout file
- `pages/auth/accept-invite/[token].vue` - Accept invitation implementation
- `pages/invite/index.vue` - Invitation portal example
- `layouts/auth.vue` - Alternative auth layout for comparison
