import { ref, computed, reactive } from 'vue';

export interface ValidationRule {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  email?: boolean;
  url?: boolean;
  custom?: (value: any) => string | null;
}

export interface SettingsValidationError {
  field: string;
  message: string;
}

export interface FieldValidation {
  rules: ValidationRule;
  error: string | null;
  touched: boolean;
}

export const useSettingsValidation = () => {
  const fields = reactive<Record<string, FieldValidation>>({});
  const isValidating = ref(false);

  // Add field validation
  const addField = (fieldName: string, rules: ValidationRule) => {
    fields[fieldName] = {
      rules,
      error: null,
      touched: false
    };
  };

  // Validate single field
  const validateField = (fieldName: string, value: any): string | null => {
    const field = fields[fieldName];
    if (!field) return null;

    const { rules } = field;
    
    // Required validation
    if (rules.required && (!value || (typeof value === 'string' && value.trim() === ''))) {
      return 'This field is required';
    }

    // Skip other validations if field is empty and not required
    if (!value || (typeof value === 'string' && value.trim() === '')) {
      return null;
    }

    // String validations
    if (typeof value === 'string') {
      // Min length validation
      if (rules.minLength && value.length < rules.minLength) {
        return `Must be at least ${rules.minLength} characters`;
      }

      // Max length validation
      if (rules.maxLength && value.length > rules.maxLength) {
        return `Must be no more than ${rules.maxLength} characters`;
      }

      // Email validation
      if (rules.email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(value)) {
          return 'Please enter a valid email address';
        }
      }

      // URL validation
      if (rules.url) {
        try {
          new URL(value);
        } catch {
          return 'Please enter a valid URL';
        }
      }

      // Pattern validation
      if (rules.pattern && !rules.pattern.test(value)) {
        return 'Please enter a valid format';
      }
    }

    // Custom validation
    if (rules.custom) {
      const customError = rules.custom(value);
      if (customError) {
        return customError;
      }
    }

    return null;
  };

  // Validate and update field
  const validate = (fieldName: string, value: any): boolean => {
    const field = fields[fieldName];
    if (!field) return true;

    field.touched = true;
    field.error = validateField(fieldName, value);
    
    return field.error === null;
  };

  // Validate all fields
  const validateAll = (formData: Record<string, any>): boolean => {
    isValidating.value = true;
    let isValid = true;

    Object.keys(fields).forEach(fieldName => {
      const fieldValid = validate(fieldName, formData[fieldName]);
      if (!fieldValid) {
        isValid = false;
      }
    });

    isValidating.value = false;
    return isValid;
  };

  // Clear field error
  const clearError = (fieldName: string) => {
    const field = fields[fieldName];
    if (field) {
      field.error = null;
    }
  };

  // Clear all errors
  const clearAllErrors = () => {
    Object.keys(fields).forEach(fieldName => {
      fields[fieldName].error = null;
      fields[fieldName].touched = false;
    });
  };

  // Get field error
  const getError = (fieldName: string): string | null => {
    const field = fields[fieldName];
    return field?.error || null;
  };

  // Check if field has error
  const hasError = (fieldName: string): boolean => {
    const field = fields[fieldName];
    return !!(field?.error && field?.touched);
  };

  // Check if form is valid
  const isValid = computed(() => {
    return Object.values(fields).every(field => field.error === null);
  });

  // Check if form has errors
  const hasErrors = computed(() => {
    return Object.values(fields).some(field => field.error !== null && field.touched);
  });

  // Get all errors
  const errors = computed((): SettingsValidationError[] => {
    return Object.entries(fields)
      .filter(([_, field]) => field.error && field.touched)
      .map(([fieldName, field]) => ({
        field: fieldName,
        message: field.error!
      }));
  });

  // Touch field (mark as interacted with)
  const touch = (fieldName: string) => {
    const field = fields[fieldName];
    if (field) {
      field.touched = true;
    }
  };

  // Touch all fields
  const touchAll = () => {
    Object.keys(fields).forEach(fieldName => {
      fields[fieldName].touched = true;
    });
  };

  return {
    // State
    fields,
    isValidating,
    isValid,
    hasErrors,
    errors,

    // Methods
    addField,
    validate,
    validateField,
    validateAll,
    clearError,
    clearAllErrors,
    getError,
    hasError,
    touch,
    touchAll
  };
};

// Common validation rules
export const validationRules = {
  required: { required: true },
  email: { email: true },
  url: { url: true },
  requiredEmail: { required: true, email: true },
  requiredUrl: { required: true, url: true },
  platformName: { required: true, minLength: 2, maxLength: 50 },
  supportPhone: { 
    pattern: /^[\+]?[1-9][\d]{0,15}$/,
    custom: (value: string) => {
      if (value && !/^[\+]?[1-9][\d]{0,15}$/.test(value)) {
        return 'Please enter a valid phone number';
      }
      return null;
    }
  }
};
