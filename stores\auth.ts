// stores/auth.ts
import { defineStore } from 'pinia';
import { useRequestHeaders, useNuxtApp } from '#app';
import { ACCESS_TOKEN_COOKIE } from '~/server/constants';
import { PlatformRoles, TenantRoles } from '~/app/features/auth/constants/roles';
import type { User as AuthUserTypeDefinition, TenantRoleAssignment } from '~/types/auth'; // Use 'type' for interfaces from .d.ts


// Store-specific User interface that allows nullable fields for initial state
interface StoreUser {
  id: string | null; // Keep nullability for initial state
  email: string | null;
  name: string | null;
  phone?: string | null;
  address?: string | null;
  roles: PlatformRoles[] | null;
  tenantAccess: TenantRoleAssignment[] | null;
  activeTenantId?: string | null;
  otpEnabled?: boolean;
  // Store-specific fields
  permissions?: string[];
  tenantId?: string;
  tenantName?: string;
  avatarUrl?: string;
  is2faEnabled?: boolean;
}

interface AuthState {
  user: StoreUser | null;
  [ACCESS_TOKEN_COOKIE]: string | null;
  loading: boolean;
  error: string | null;
}

export const useAuthStore = defineStore('auth', {
  state: (): AuthState => ({
    user: null,
    [ACCESS_TOKEN_COOKIE]: null,
    loading: false,
    error: null,
  }),
  getters: {
    isAuthenticated: (state): boolean => !!state.user && !!state[ACCESS_TOKEN_COOKIE],
    currentUser: (state): StoreUser | null => state.user,
    getToken: (state): string | null => state[ACCESS_TOKEN_COOKIE],
    isLoading: (state): boolean => state.loading,
    authError: (state): string | null => state.error,

    // New Scoped Role Getters
    platformUserRoles: (state): PlatformRoles[] => state.user?.roles || [],
    tenantUserAccess: (state): TenantRoleAssignment[] => state.user?.tenantAccess || [],
    activeTenantId: (state): string | null | undefined => state.user?.activeTenantId,
    
    activeTenantUserRoles: (state): TenantRoles[] => {
      if (!state.user || !state.user.activeTenantId || !state.user.tenantAccess) {
        return [];
      }
      const activeTenant = state.user.tenantAccess.find(
        (ta: TenantRoleAssignment) => ta.tenantId === state.user?.activeTenantId
      );
      return activeTenant?.roles || [];
    },

    // Combined roles for convenience if needed, but be mindful of context
    allUserRoles: (state): (PlatformRoles | TenantRoles)[] => {
        const roles: (PlatformRoles | TenantRoles)[] = [];
        if (state.user?.roles) {
            roles.push(...state.user.roles);
        }
        if (state.user?.tenantAccess) {
            state.user.tenantAccess.forEach((ta: TenantRoleAssignment) => roles.push(...ta.roles));
        }
        return [...new Set(roles)]; // Unique roles
    },

    isOtpEnabled: (state): boolean => !!state.user?.otpEnabled, // Using new field name
    // userPermissions: (state): string[] => state.user?.permissions || [], // If permissions are still used
  },
  actions: {
    // setToken now primarily for internal store consistency, not localStorage persistence.
    // The actual HTTPOnly cookie is managed by the Nuxt server endpoints.
    setToken(token: string | null) {
      this[ACCESS_TOKEN_COOKIE] = token;
    },
    setUser(userData: User | null) {
      this.user = userData;
    },
    setLoading(status: boolean) {
      this.loading = status;
    },
    setError(message: string | null) {
      this.error = message;
    },

    async login(credentials: { email: string; password: string }, options: any) {
      this.setLoading(true);
      this.setError(null);
      try {
        // Get API client from Nuxt app
        // Client calls Nuxt server's login endpoint
        const response = await $fetch<{ user: User; message: string }>('/api/auth/login', {
          method: 'POST',
          body: credentials,
          // withCredentials: true, // Ensures cookies set by Nuxt server are handled
          ...options,
        });
        console.log('Login response:', response);

        // Nuxt server returned user data in body, access_token as HttpOnly cookie.
        // We only set the user here. The access_token is now in an HttpOnly cookie
        // so it's managed by the browser and Nuxt server, not Pinia's direct state for rehydration.
        this.setUser(response.user);
        // We set a dummy token value to indicate 'authenticated' client-side
        // since the real access_token is HttpOnly and not readable here.
        this.setToken('true'); // Or any non-null string

      } catch (e: any) {
        const errorMessage = e.response?.data?.message || e.message || 'Login failed.';
        this.setError(errorMessage);
        this.clearAuthData();
        throw e;
      } finally {
        this.setLoading(false);
      }
    },

    async logout() {
      this.setLoading(true);
      try {
         await $fetch('/api/auth/logout', { method: 'POST' }); // This clears Nuxt's cookies and signals backend
      } catch (e: any) {
        const errorMessage = e.response?.data?.message || e.message;
        console.error('Logout API call failed:', errorMessage);
      } finally {
        this.clearAuthData();
        this.setLoading(false);
        // Navigate after logout will be handled by middleware/component
        // navigateTo('/auth/login');
      }
    },

    // Fetches user data using the HttpOnly access_token cookie set by the Nuxt server
    async fetchUser() {
       if (this.isLoading) return; // Don't run if already loading
      this.setLoading(true);
      this.setError(null);
      try {
        const headers: Record<string, string> = {};
        if (process.server) {
          const nuxtApp = useNuxtApp();
          // Only forward the cookie header on the server side
          // nuxtApp.ssrContext.event.node.req.headers.cookie
          const cookieHeader = useRequestHeaders(['cookie'])
          if (cookieHeader && cookieHeader.cookie) {
            headers.cookie = cookieHeader.cookie;
          }
        }

        // This request to /api/auth/me will automatically send the HttpOnly access_token cookie
        // when running on the client. On the server, we explicitly forward it.
        const response = await $fetch<{ user: User }>('/api/auth/me', { headers });
        this.setUser(response.user);
        this.setToken('true'); // Indicate authenticated state
      } catch (e: any) {
        if (e.response?.status === 401 || e.statusCode === 401) {
          // This is an expected case if the user is not logged in (no valid cookie)
          // console.info('Auth Store: No active session found during initAuth.');
          this.setError('No active session.'); // Set a less verbose error or null
        } else {
          // Log other unexpected errors
          console.error('Auth Store: Failed to fetch user data during hydration/refresh:', e.data?.message || e.message || e);
          this.setError(e.data?.statusMessage || e.data?.error || 'Failed to fetch user data.');
        }
        this.clearAuthData(); // Clear state if fetch fails
      } finally {
        this.setLoading(false);
      }
    },

    // This action runs very early in the Nuxt lifecycle
    async initAuth() {
      // The crucial part: On any page load (SSR or client-side),
      // we attempt to fetch the user. If the HttpOnly `access_token` cookie
      // is present and valid, the server-side `/api/auth/me` will return the user,
      // and Pinia will be hydrated.
      // If `this.user` is already populated (from a previous hydration or client-side `login` call)
      // or `isLoading` is true, we skip to avoid redundant fetches.
      try {
        if (!this.user && !this.loading) { // Only attempt if user not already loaded and not currently loading
          await this.fetchUser();
        }
      } catch (error) {
        console.warn('Auth Store: initAuth failed, but continuing gracefully:', error);
        // Don't throw the error to prevent breaking the app initialization
        this.clearAuthData();
      }
    },

    clearAuthData() {
      this.user = null;
      this[ACCESS_TOKEN_COOKIE] = null;
      this.error = null;
    },

    // Action to set the active tenant context
    setActiveTenant(tenantId: string | null) {
      if (this.user) {
        this.user.activeTenantId = tenantId;
        // Potentially persist this preference (e.g., localStorage or backend)
        // and re-fetch user data or specific tenant data if needed.
      }
    },




  },
});