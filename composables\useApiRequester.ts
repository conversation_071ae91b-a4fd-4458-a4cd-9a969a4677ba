import { useNuxtApp } from '#app';
import  { type AxiosInstance } from 'axios';

/**
 * Composable to get the pre-configured Axios instance.
 * This instance automatically handles JWT authentication, token refreshing,
 * and centralized error handling based on the `api-requester.server.ts` plugin.
 *
 * @returns {AxiosInstance} The configured Axios instance.
 */
export const useApiRequester = (): AxiosInstance => {
  const { $apiRequester } = useNuxtApp();
  if (!$apiRequester) {
    // This should ideally not happen if the plugin is correctly registered and run.
    // However, it's good practice for robustness, especially during development.
    throw new Error('Axios apiRequester not provided by Nuxt plugin.');
  }
  return $apiRequester as AxiosInstance;
};