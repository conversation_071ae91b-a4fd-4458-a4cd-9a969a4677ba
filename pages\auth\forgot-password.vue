<template>

    <form class="space-y-6" @submit.prevent="handleForgotPassword">
      <div>
        <label for="email" class="block text-sm font-medium text-gray-700">
          Email address
        </label>
        <div class="mt-1">
          <input
            id="email"
            name="email"
            type="email"
            autocomplete="email"
            required
            v-model="email"
            :disabled="loading || resetInitiated"
            class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
          />
        </div>
      </div>

      <div v-if="resetInitiated" class="text-center text-sm text-green-600 font-medium">
        <p>A password reset link has been sent to your email address.</p>
        <p class="mt-1">Please check your inbox (and spam folder).</p>
      </div>

      <div>
        <button
          type="submit"
          :disabled="loading || resetInitiated"
          class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          :class="{ 'opacity-50 cursor-not-allowed': loading || resetInitiated }"
        >
          <span v-if="loading" class="flex items-center">
            <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-50" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Sending link...
          </span>
          <span v-else>Reset Password</span>
        </button>
      </div>
    </form>

    <div class="mt-6">
      <div class="relative">
        <div class="absolute inset-0 flex items-center">
          <div class="w-full border-t border-gray-300" />
        </div>
        <div class="relative flex justify-center text-sm">
          <span class="px-2 bg-white text-gray-500">
            Or
          </span>
        </div>
      </div>

      <div class="mt-6 text-center text-sm">
        <NuxtLink to="/auth/login" class="font-medium text-indigo-600 hover:text-indigo-500">
          Remembered your password? Sign in
        </NuxtLink>
      </div>
    </div>

</template>

<script lang="ts" setup>
import { ref } from 'vue';
import axios from 'axios';
import { useToast } from '~/composables/useToast'; // For global toasts

definePageMeta({
  layout: 'auth',
  title: 'Forgot Password',
  subtitle: 'Reset your password',
  meta: [
    {
      name: 'description',
      content: 'Request a password reset link for your Legal SaaS Platform account.',
    },
  ],
});

const email = ref<string>('');
const loading = ref<boolean>(false);
const resetInitiated = ref<boolean>(false); // To show success message and disable form

const { showToast } = useToast();

const API_BASE_URL = 'http://localhost:3000/api/v1'; // As per backend integration guide

const handleForgotPassword = async () => {
  loading.value = true;
  resetInitiated.value = false; // Reset status on new attempt

  try {
    // In a real application, you would send this to a backend endpoint
    // like POST /v1/auth/forgot-password (this is a common convention,
    // confirm with backend if different).
    // The backend guide doesn't explicitly list a /forgot-password endpoint,
    // so we'll assume a standard one like `/auth/forgot-password`.
    console.log(`Requesting password reset for: ${email.value}`);
    await axios.post(`${API_BASE_URL}/auth/forgot-password`, {
      email: email.value,
    });

    resetInitiated.value = true; // Show success message
    showToast({
      type: 'success',
      title: 'Reset Link Sent',
      message: 'If an account exists, a password reset link has been sent to your email.',
      timeout: 8000, // Give user more time to read
    });

  } catch (error: any) {
    let errorMessage = 'Could not initiate password reset. Please try again.';
    if (axios.isAxiosError(error) && error.response) {
      // Backend provides consistent JSON error responses 
      errorMessage = Array.isArray(error.response.data.message) // 
        ? error.response.data.message.join(', ')
        : error.response.data.message || error.response.data.error; // 
    }
    showToast({
      type: 'error',
      title: 'Reset Failed',
      message: errorMessage,
    });
    console.error('Forgot password error:', error);
  } finally {
    loading.value = false;
  }
};

useHead({
  title: 'Forgot Password - Legal SaaS Platform',
  meta: [
    {
      name: 'description',
      content: 'Request a password reset link for your Legal SaaS Platform account.',
    },
  ],
});
</script>

<style scoped>
/* No specific styles needed, relying on Tailwind CSS */
</style>