/**
 * Authentication UI Types
 * 
 * Type definitions for authentication UI components,
 * forms, and user interface states
 */

import type { /* AuthState, */ LoginMethod, AccountStatus } from '../constants/auth.js' // AuthState unused, Added .js

// Form States
export interface FormState {
  isSubmitting: boolean
  isValid: boolean
  isDirty: boolean
  errors: Record<string, string[]>
  touched: Record<string, boolean>
}

// Login Form
export interface LoginFormData {
  email: string
  password: string
  rememberMe: boolean
  twoFactorToken?: string
}

export interface LoginFormState extends FormState {
  data: LoginFormData
  showPassword: boolean
  requiresTwoFactor: boolean
  loginMethod: LoginMethod
}

// Registration Form
export interface RegisterFormData {
  firstName: string
  lastName: string
  email: string
  password: string
  confirmPassword: string
  phone?: string
  acceptTerms: boolean
  acceptPrivacy: boolean
  marketingConsent?: boolean
}

export interface RegisterFormState extends FormState {
  data: RegisterFormData
  showPassword: boolean
  showConfirmPassword: boolean
  passwordStrength: PasswordStrength
}

// Password Forms
export interface ChangePasswordFormData {
  currentPassword: string
  newPassword: string
  confirmPassword: string
}

export interface ForgotPasswordFormData {
  email: string
}

export interface ResetPasswordFormData {
  token: string
  password: string
  confirmPassword: string
}

// Two-Factor Authentication
export interface TwoFactorFormData {
  token: string
  useBackupCode: boolean
  backupCode?: string
}

export interface TwoFactorSetupData {
  qrCode: string
  secret: string
  verificationToken: string
  backupCodes: string[]
}

// Password Strength
export interface PasswordStrength {
  score: number // 0-4
  level: 'weak' | 'fair' | 'good' | 'strong'
  feedback: string[]
  requirements: {
    minLength: boolean
    hasUppercase: boolean
    hasLowercase: boolean
    hasNumbers: boolean
    hasSpecialChars: boolean
  }
}

// User Profile Forms
export interface ProfileFormData {
  firstName: string
  lastName: string
  email: string
  phone?: string
  avatar?: string
  timezone?: string
  language?: string
  notifications: NotificationPreferences
}

export interface NotificationPreferences {
  email: {
    security: boolean
    updates: boolean
    marketing: boolean
    digest: boolean
  }
  push: {
    security: boolean
    updates: boolean
    mentions: boolean
  }
  sms: {
    security: boolean
    critical: boolean
  }
}

// UI Component Props
export interface AuthLayoutProps {
  title: string
  subtitle?: string
  showLogo?: boolean
  showFooter?: boolean
  backgroundImage?: string
  maxWidth?: 'sm' | 'md' | 'lg'
}

export interface AuthFormProps {
  loading?: boolean
  disabled?: boolean
  showSocialLogin?: boolean
  redirectTo?: string
  variant?: 'default' | 'modal' | 'inline'
}

export interface PasswordInputProps {
  modelValue: string
  placeholder?: string
  disabled?: boolean
  showStrength?: boolean
  showToggle?: boolean
  required?: boolean
  autocomplete?: string
}

export interface TwoFactorInputProps {
  modelValue: string
  disabled?: boolean
  autoFocus?: boolean
  placeholder?: string
  length?: number
}

// Modal States
export interface AuthModalState {
  isOpen: boolean
  type: 'login' | 'register' | 'forgot-password' | 'two-factor'
  data?: any
  onSuccess?: (result: any) => void
  onCancel?: () => void
}

// Toast/Alert Types
export interface AuthAlert {
  type: 'success' | 'error' | 'warning' | 'info'
  title: string
  message: string
  duration?: number
  actions?: AuthAlertAction[]
}

export interface AuthAlertAction {
  label: string
  action: () => void
  variant?: 'primary' | 'secondary' | 'danger'
}

// Loading States
export interface AuthLoadingState {
  login: boolean
  register: boolean
  logout: boolean
  refresh: boolean
  profile: boolean
  changePassword: boolean
  forgotPassword: boolean
  resetPassword: boolean
  verifyEmail: boolean
  enable2FA: boolean
  disable2FA: boolean
}

// Error States
export interface AuthErrorState {
  login: string | null
  register: string | null
  profile: string | null
  changePassword: string | null
  forgotPassword: string | null
  resetPassword: string | null
  verifyEmail: string | null
  twoFactor: string | null
  general: string | null
}

// Session UI State
export interface SessionUIState {
  showSessionExpiredModal: boolean
  showInactivityWarning: boolean
  inactivityCountdown: number
  lastActivity: Date | null
}

// Social Login
export interface SocialLoginProvider {
  id: string
  name: string
  icon: string
  color: string
  enabled: boolean
}

export interface SocialLoginState {
  providers: SocialLoginProvider[]
  isLoading: Record<string, boolean>
  errors: Record<string, string | null>
}

// Account Management
export interface AccountUIState {
  status: AccountStatus
  isVerified: boolean
  has2FA: boolean
  lastLogin: Date | null
  activeSessions: number
  securityScore: number
}

// Navigation
export interface AuthNavigation {
  currentStep: number
  totalSteps: number
  canGoBack: boolean
  canGoNext: boolean
  steps: AuthNavigationStep[]
}

export interface AuthNavigationStep {
  id: string
  title: string
  description?: string
  isComplete: boolean
  isActive: boolean
  isDisabled: boolean
}

// Responsive Design
export interface AuthResponsiveState {
  isMobile: boolean
  isTablet: boolean
  isDesktop: boolean
  orientation: 'portrait' | 'landscape'
  screenSize: 'xs' | 'sm' | 'md' | 'lg' | 'xl'
}

// Theme
export interface AuthTheme {
  mode: 'light' | 'dark' | 'auto'
  primaryColor: string
  accentColor: string
  borderRadius: 'none' | 'sm' | 'md' | 'lg' | 'full'
  fontFamily: string
}

// Accessibility
export interface AuthAccessibility {
  reducedMotion: boolean
  highContrast: boolean
  fontSize: 'sm' | 'md' | 'lg' | 'xl'
  screenReader: boolean
  keyboardNavigation: boolean
}

// Component Events
export interface AuthFormEvents {
  submit: (data: any) => void
  cancel: () => void
  change: (field: string, value: any) => void
  blur: (field: string) => void
  focus: (field: string) => void
  error: (error: string) => void
}

// Validation Events
export interface ValidationEvents {
  validate: (field: string, value: any) => void
  validateAll: () => void
  clearErrors: (field?: string) => void
  setError: (field: string, error: string) => void
}

// Router/Navigation Types
export interface AuthRedirect {
  from?: string
  to: string
  replace?: boolean
  query?: Record<string, string>
}

export interface AuthGuardState {
  isAuthenticated: boolean
  isLoading: boolean
  requiredRoles?: string[]
  requiredPermissions?: string[]
  redirectTo?: string
}
