<!--
  Dynamic Dashboard Router
  
  This component handles dynamic routing for dashboard features
  with lazy loading and code splitting for optimal performance
-->

<template>
  <div class="dashboard-dynamic-route">
    <Suspense>
      <component 
        :is="currentComponent" 
        v-bind="componentProps"
        @error="handleComponentError"
      />
      
      <template #fallback>
        <div class="flex items-center justify-center min-h-[400px]">
          <UiSpinner size="lg" />
          <span class="ml-3 text-gray-600">Loading {{ featureName }}...</span>
        </div>
      </template>
    </Suspense>
  </div>
</template>

<script setup lang="ts">
import { computed, defineAsyncComponent, onMounted, h } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuth } from '~/app/features/auth'
import { PlatformRoles, TenantRoles } from '~/app/features/auth/constants/roles'

// Route and authentication
const route = useRoute()
const router = useRouter()
const {
  isSuperAdmin,
  isSupport,
  isTenantOwner,
  isAdminInActiveTenant,
  isLawyerInActiveTenant,
  isParalegalInActiveTenant,
  hasPlatformRole,
  hasActiveTenantRole
} = useAuth()

// Extract feature and sub-route from slug
const slug = computed(() => route.params.slug as string[])
const featureName = computed(() => slug.value?.[0] || '')
const subRoute = computed(() => slug.value?.slice(1) || [])

// Dynamic component loading with error handling
const componentMap = {
  // Cases feature
  cases: {
    index: () => import('~/app/cases/components/views/CasesList.vue'),
    create: () => import('~/app/cases/components/forms/CaseCreateForm.vue'),
    detail: () => import('~/app/cases/components/views/CaseDetail.vue'),
    edit: () => import('~/app/cases/components/forms/CaseEditForm.vue')
  },
  
  // Documents feature
  documents: {
    index: () => import('~/app/documents/components/views/DocumentsList.vue'),
    upload: () => import('~/app/documents/components/forms/DocumentUpload.vue'),
    viewer: () => import('~/app/documents/components/views/DocumentViewer.vue')
  },
  
  // Templates feature
  templates: {
    index: () => import('~/app/templates/components/views/TemplatesList.vue'),
    create: () => import('~/app/templates/components/forms/TemplateCreateForm.vue'),
    editor: () => import('~/app/templates/components/editors/TemplateEditor.vue')
  },
  
  // Users feature
  users: {
    index: () => import('~/app/users/components/views/UsersList.vue'),
    profile: () => import('~/app/users/components/views/UserProfile.vue'),
    settings: () => import('~/app/users/components/forms/UserSettings.vue')
  },
  
  // Settings feature
  settings: {
    index: () => import('~/app/settings/components/views/SettingsOverview.vue'),
    platform: () => import('~/app/settings/components/views/PlatformSettings.vue'),
    security: () => import('~/app/settings/components/views/SecuritySettings.vue')
  }
}

// Role requirements for each feature
const featureRoles = {
  cases: [TenantRoles.TENANT_OWNER, TenantRoles.ADMIN, TenantRoles.LAWYER, TenantRoles.PARALEGAL],
  documents: [TenantRoles.TENANT_OWNER, TenantRoles.ADMIN, TenantRoles.LAWYER, TenantRoles.PARALEGAL],
  templates: [TenantRoles.TENANT_OWNER, TenantRoles.ADMIN, TenantRoles.LAWYER],
  users: [TenantRoles.TENANT_OWNER, TenantRoles.ADMIN],
  settings: [TenantRoles.TENANT_OWNER, TenantRoles.ADMIN]
}

// Helper function to check feature access
const hasFeatureAccess = (feature: string): boolean => {
  // SuperAdmin has access to everything
  if (isSuperAdmin.value) return true

  const requiredRoles = featureRoles[feature as keyof typeof featureRoles]
  if (!requiredRoles) return true // If no roles required, allow access

  // Check if user has any of the required tenant roles
  return requiredRoles.some(role => hasActiveTenantRole(role))
}

// Get current component based on route
const currentComponent = computed(() => {
  const feature = featureName.value
  const action = subRoute.value[0] || 'index'
  
  if (!feature || !componentMap[feature as keyof typeof componentMap]) {
    return defineAsyncComponent(() => import('~/components/errors/NotFound.vue'))
  }
  
  // Check role access
  const requiredRoles = featureRoles[feature as keyof typeof featureRoles]
  if (requiredRoles && !hasFeatureAccess(feature)) {
    return defineAsyncComponent(() => import('~/components/errors/Forbidden.vue'))
  }
  
  const featureComponents = componentMap[feature as keyof typeof componentMap]
  const componentLoader = featureComponents[action as keyof typeof featureComponents]
  
  if (!componentLoader) {
    return defineAsyncComponent(() => import('~/components/errors/NotFound.vue'))
  }
  
  return defineAsyncComponent({
    loader: componentLoader,
    loadingComponent: () => h('div', { class: 'animate-pulse bg-gray-200 h-32 rounded' }),
    errorComponent: () => import('~/components/errors/ComponentError.vue'),
    delay: 200,
    timeout: 10000
  })
})

// Component props based on route params
const componentProps = computed(() => {
  const props: Record<string, any> = {}
  
  // Pass route parameters as props
  if (route.params.id) {
    props.id = route.params.id
  }
  
  // Pass query parameters
  if (route.query) {
    props.query = route.query
  }
  
  // Feature-specific props
  switch (featureName.value) {
    case 'cases':
      if (subRoute.value[0] === 'detail' && subRoute.value[1]) {
        props.caseId = subRoute.value[1]
      }
      break
      
    case 'documents':
      if (subRoute.value[0] === 'viewer' && subRoute.value[1]) {
        props.documentId = subRoute.value[1]
      }
      break
      
    case 'templates':
      if (subRoute.value[0] === 'editor' && subRoute.value[1]) {
        props.templateId = subRoute.value[1]
      }
      break
  }
  
  return props
})

// Error handling
const handleComponentError = (error: Error) => {
  console.error('Component error:', error)
  
  // Navigate to error page or show error message
  router.push('/dashboard/error')
}

// Page meta configuration
definePageMeta({
  layout: 'dashboard',
  middleware: ['rbac'],
  keepalive: true
})

// Page title computation (for future use)
const pageTitle = computed(() => {
  const feature = featureName.value
  const action = subRoute.value[0] || 'index'
  
  const titles = {
    cases: {
      index: 'Cases',
      create: 'Create Case',
      detail: 'Case Details',
      edit: 'Edit Case'
    },
    documents: {
      index: 'Documents',
      upload: 'Upload Document',
      viewer: 'Document Viewer'
    },
    templates: {
      index: 'Templates',
      create: 'Create Template',
      editor: 'Template Editor'
    },
    users: {
      index: 'Users',
      profile: 'User Profile',
      settings: 'User Settings'
    },
    settings: {
      index: 'Settings',
      platform: 'Platform Settings',
      security: 'Security Settings'
    }
  }
  
  const featureTitles = titles[feature as keyof typeof titles]
  if (featureTitles && typeof featureTitles === 'object') {
    return (featureTitles as any)[action] || 'Dashboard'
  }
  return 'Dashboard'
})

// Preload related components for better UX
onMounted(() => {
  const feature = featureName.value
  if (feature && componentMap[feature as keyof typeof componentMap]) {
    // Preload common actions for the current feature
    const commonActions = ['index', 'create', 'detail']
    const featureComponents = componentMap[feature as keyof typeof componentMap]
    
    commonActions.forEach(action => {
      const loader = featureComponents[action as keyof typeof featureComponents]
      if (loader && action !== (subRoute.value[0] || 'index')) {
        // Preload with low priority
        setTimeout(() => loader(), 1000)
      }
    })
  }
})
</script>

<style scoped>
.dashboard-dynamic-route {
  @apply min-h-screen;
}

/* Loading animations */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
