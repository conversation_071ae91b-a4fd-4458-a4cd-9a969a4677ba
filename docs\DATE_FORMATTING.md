# Date Formatting System

This document describes the centralized date formatting system implemented in the Legal SaaS Frontend application using dayjs-nuxt.

## Overview

The application uses a comprehensive date formatting system built on top of Day.js through the dayjs-nuxt module. This provides consistent, timezone-aware, and localized date formatting across the entire application with better performance than Moment.js.

## Features

- **Consistent Formatting**: All dates are formatted using the same system
- **Timezone Support**: Built-in timezone conversion and display
- **Day.js Integration**: Leverages the fast 2kB Day.js library (alternative to Moment.js)
- **Better Performance**: Significantly smaller bundle size compared to Moment.js
- **Fallback Support**: Graceful fallback to native Date API if Day.js is unavailable
- **TypeScript Support**: Full TypeScript definitions and type safety
- **Reactive Formatting**: Vue 3 reactive date formatting for dynamic updates
- **Multiple Usage Patterns**: Plugin, composable, and utility function approaches

## Installation & Configuration

The system is already configured in the application:

### Dependencies
```json
{
  "dayjs-nuxt": "^2.1.11"
}
```

### Nuxt Configuration
```typescript
// nuxt.config.ts
export default defineNuxtConfig({
  modules: ['dayjs-nuxt'],
  dayjs: {
    locales: ['en'],
    plugins: ['relativeTime', 'utc', 'timezone', 'duration', 'customParseFormat'],
    defaultLocale: 'en',
    defaultTimezone: 'America/New_York'
  }
})
```

## Usage Patterns

### 1. Global Plugin (Recommended for Templates)

Use the globally available formatters in Vue templates:

```vue
<template>
  <div>
    <!-- Basic formatting -->
    <p>{{ $formatDateDisplay(user.createdAt) }}</p>
    <p>{{ $formatDateTime(document.updatedAt) }}</p>
    <p>{{ $formatRelativeTime(notification.timestamp) }}</p>
    
    <!-- Legal document formatting -->
    <p>{{ $formatLegalDate(contract.signedAt) }}</p>
    
    <!-- Custom formatting -->
    <p>{{ $formatDate(date, 'MMMM D, YYYY [at] h:mm A') }}</p>
    
    <!-- Timezone-aware formatting -->
    <p>{{ $formatDateWithTimezone(date, 'MMM D, YYYY h:mm A', 'Europe/London') }}</p>
  </div>
</template>
```

### 2. Composable (Recommended for Script Setup)

Use the composable in Vue components:

```vue
<script setup lang="ts">
import { useDateFormatters } from '~/composables/useDateFormatters'
import { useDayjs } from '#dayjs' // Direct Day.js access

const {
  formatDateDisplay,
  formatDateTime,
  formatRelativeTime,
  formatReactive
} = useDateFormatters()

// Static formatting
const formattedDate = formatDateDisplay(user.createdAt)

// Reactive formatting
const reactiveDate = ref(new Date())
const formattedReactiveDate = formatReactive(reactiveDate, 'MMMM D, YYYY')

// Direct Day.js usage
const dayjs = useDayjs()
const customFormat = dayjs().format('YYYY-MM-DD HH:mm:ss')
</script>
```

### 3. Utility Functions (Recommended for Business Logic)

Import and use the utility functions directly:

```typescript
import { dateFormatters, DATE_FORMATS } from '~/utils/dateFormatters'

// In composables, stores, or other utilities
const formatUserDate = (date: string) => {
  return dateFormatters.formatDateDisplay(date)
}

// Custom formatting
const formatCustom = (date: string) => {
  return dateFormatters.formatDate(date, DATE_FORMATS.LEGAL_DATETIME)
}
```

## Available Formatters

### Basic Date Formatters

| Function | Description | Example Output |
|----------|-------------|----------------|
| `formatDateDisplay` | Standard date display | "Jan 15, 2024" |
| `formatDateFull` | Full date display | "January 15, 2024" |
| `formatDateTime` | Date with time | "Jan 15, 2024 2:30 PM" |
| `formatDateTimeFull` | Full date with time | "January 15, 2024 2:30:45 PM" |

### Time Formatters

| Function | Description | Example Output |
|----------|-------------|----------------|
| `formatTime` | 12-hour time | "2:30 PM" |
| `formatTime24` | 24-hour time | "14:30" |

### Relative Formatters

| Function | Description | Example Output |
|----------|-------------|----------------|
| `formatRelativeTime` | Relative time | "2 hours ago" |
| `formatRelativeTimeLong` | Relative time (long) | "2 hours" |
| `formatDuration` | Duration between dates | "2h 30m" |
| `formatAge` | Age calculation | "34 years" |

### Legal Document Formatters

| Function | Description | Example Output |
|----------|-------------|----------------|
| `formatLegalDate` | Legal document date | "January 15, 2024" |
| `formatLegalDateTime` | Legal document datetime | "January 15, 2024 at 2:30 PM" |

### System Formatters

| Function | Description | Example Output |
|----------|-------------|----------------|
| `formatFileTimestamp` | File naming timestamp | "2024-01-15_14-30-00" |
| `formatLogTimestamp` | Log entry timestamp | "2024-01-15 14:30:00" |

## Date Format Constants

Use predefined format constants for consistency:

```typescript
import { DATE_FORMATS } from '~/utils/dateFormatters'

// Available formats
DATE_FORMATS.DATE_ONLY          // 'YYYY-MM-DD'
DATE_FORMATS.DATE_DISPLAY       // 'MMM D, YYYY'
DATE_FORMATS.DATE_FULL          // 'MMMM D, YYYY'
DATE_FORMATS.TIME_12H           // 'h:mm A'
DATE_FORMATS.TIME_24H           // 'HH:mm'
DATE_FORMATS.DATETIME_DISPLAY   // 'MMM D, YYYY h:mm A'
DATE_FORMATS.LEGAL_DATE         // 'MMMM D, YYYY'
DATE_FORMATS.LEGAL_DATETIME     // 'MMMM D, YYYY [at] h:mm A'
// ... and more
```

## Timezone Support

The system supports timezone-aware formatting:

```typescript
// Format with specific timezone
const nyTime = formatDateWithTimezone(date, 'MMM D, YYYY h:mm A', 'America/New_York')
const londonTime = formatDateWithTimezone(date, 'MMM D, YYYY h:mm A', 'Europe/London')
const tokyoTime = formatDateWithTimezone(date, 'MMM D, YYYY h:mm A', 'Asia/Tokyo')
```

## Reactive Formatting

For dynamic dates that update automatically:

```vue
<script setup lang="ts">
import { ref } from 'vue'
import { useDateFormatters } from '~/composables/useDateFormatters'

const { formatReactive, formatRelativeTimeReactive } = useDateFormatters()

const currentDate = ref(new Date())
const formattedDate = formatReactive(currentDate, 'MMMM D, YYYY [at] h:mm:ss A')
const relativeTime = formatRelativeTimeReactive(currentDate)

// Update every second
setInterval(() => {
  currentDate.value = new Date()
}, 1000)
</script>

<template>
  <div>
    <p>Current time: {{ formattedDate }}</p>
    <p>Started: {{ relativeTime }}</p>
  </div>
</template>
```

## Migration from Existing Code

To migrate existing date formatting code:

### Before (Old Pattern)
```typescript
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}
```

### After (New Pattern)
```typescript
import { dateFormatters } from '~/utils/dateFormatters'

const formatDate = (dateString: string) => {
  return dateFormatters.formatDateDisplay(dateString)
}

// Or in templates
{{ $formatDateDisplay(dateString) }}
```

## Best Practices

1. **Use Consistent Formatters**: Always use the centralized formatters instead of creating custom ones
2. **Choose the Right Formatter**: Use specific formatters for their intended purpose (legal, system, display)
3. **Handle Null/Undefined**: All formatters handle null/undefined gracefully
4. **Use Reactive Formatting**: For dynamic dates, use reactive formatters
5. **Timezone Awareness**: Consider timezone requirements for your use case
6. **Performance**: Use the composable pattern for components with many date operations

## Error Handling

The system includes comprehensive error handling:

- Graceful fallback to native Date API if Moment.js fails
- Returns '-' for null/undefined dates
- Logs errors while providing fallback formatting
- Type safety prevents common date formatting errors

## Examples

See `components/examples/DateFormatterExample.vue` for comprehensive usage examples.

## Testing

A comprehensive test page is available at `/test-dayjs` when running the development server. This page demonstrates all the date formatting features and can be used to verify that the Day.js integration is working correctly.

## Migration from Moment.js

This application has been migrated from Moment.js to Day.js for better performance and smaller bundle size. The API remains largely the same, but with these key differences:

### Benefits of Day.js over Moment.js
- **Smaller Bundle Size**: Day.js is only 2kB compared to Moment.js's 67kB
- **Better Performance**: Faster parsing and formatting operations
- **Modern API**: Similar API to Moment.js but with modern JavaScript features
- **Tree Shaking**: Better support for tree shaking to reduce bundle size further

### API Differences
- Day.js uses `.diff(date, 'year')` instead of `.diff(date, 'years')`
- Some plugins need to be explicitly loaded (already configured in this app)
- Timezone handling is slightly different but functionally equivalent

## Support

For questions or issues with date formatting, refer to:
- [Day.js Documentation](https://day.js.org/docs/en/installation/installation)
- [dayjs-nuxt Documentation](https://github.com/nuxt-modules/dayjs)
- This application's date formatting utilities in `utils/dateFormatters.ts`
- Test page at `/test-dayjs` for live examples
