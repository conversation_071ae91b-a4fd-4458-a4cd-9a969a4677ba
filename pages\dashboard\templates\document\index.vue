<template>
  <div class="space-y-6 py-6">

    <!-- Templates Overview -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <!-- Total Templates -->
      <UiCard
        icon="material-symbols:description"
        icon-color="blue"
        :loading="isLoading"
        hover-effect
      >
        <template #header>
          <div class="flex items-center justify-between w-full">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Total Templates</h3>
            <UiBadge variant="info">{{ documentTemplates.length }}</UiBadge>
          </div>
        </template>
        <div class="space-y-3">
          <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ documentTemplates.length }}</p>
          <p class="text-sm text-gray-500 dark:text-gray-400">
            {{ activeTemplates }} active, {{ inactiveTemplates }} inactive
          </p>
          <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div 
              class="bg-blue-600 h-2 rounded-full transition-all duration-300"
              :style="{ width: `${(activeTemplates / documentTemplates.length) * 100}%` }"
            ></div>
          </div>
        </div>
      </UiCard>

      <!-- Usage This Month -->
      <UiCard
        icon="material-symbols:trending-up"
        icon-color="green"
        :loading="isLoading"
        hover-effect
      >
        <template #header>
          <div class="flex items-center justify-between w-full">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Usage This Month</h3>
            <UiBadge variant="success">+{{ usageGrowth }}%</UiBadge>
          </div>
        </template>
        <div class="space-y-3">
          <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ monthlyUsage }}</p>
          <p class="text-sm text-gray-500 dark:text-gray-400">
            documents generated
          </p>
          <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div 
              class="bg-green-600 h-2 rounded-full transition-all duration-300"
              :style="{ width: `${Math.min((monthlyUsage / monthlyTarget) * 100, 100)}%` }"
            ></div>
          </div>
        </div>
      </UiCard>

      <!-- Popular Categories -->
      <UiCard
        icon="material-symbols:category"
        icon-color="purple"
        :loading="isLoading"
        hover-effect
      >
        <template #header>
          <div class="flex items-center justify-between w-full">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Categories</h3>
            <UiBadge variant="info">{{ templateCategories.length }}</UiBadge>
          </div>
        </template>
        <div class="space-y-3">
          <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ templateCategories.length }}</p>
          <p class="text-sm text-gray-500 dark:text-gray-400">
            template categories
          </p>
          <div class="space-y-1">
            <div class="flex justify-between text-sm">
              <span class="text-gray-500 dark:text-gray-400">Contracts</span>
              <span class="text-gray-900 dark:text-white">{{ getTemplatesByCategory('contracts').length }}</span>
            </div>
            <div class="flex justify-between text-sm">
              <span class="text-gray-500 dark:text-gray-400">Legal Forms</span>
              <span class="text-gray-900 dark:text-white">{{ getTemplatesByCategory('legal-forms').length }}</span>
            </div>
          </div>
        </div>
      </UiCard>

      <!-- Recent Activity -->
      <UiCard
        icon="material-symbols:history"
        icon-color="yellow"
        :loading="isLoading"
        hover-effect
      >
        <template #header>
          <div class="flex items-center justify-between w-full">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Recent Activity</h3>
            <UiBadge variant="warning">{{ recentActivity.length }}</UiBadge>
          </div>
        </template>
        <div class="space-y-3">
          <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ recentActivity.length }}</p>
          <p class="text-sm text-gray-500 dark:text-gray-400">
            actions this week
          </p>
          <div class="space-y-1">
            <div class="flex justify-between text-sm">
              <span class="text-gray-500 dark:text-gray-400">Created</span>
              <span class="text-gray-900 dark:text-white">{{ recentActivity.filter(a => a.type === 'created').length }}</span>
            </div>
            <div class="flex justify-between text-sm">
              <span class="text-gray-500 dark:text-gray-400">Modified</span>
              <span class="text-gray-900 dark:text-white">{{ recentActivity.filter(a => a.type === 'modified').length }}</span>
            </div>
          </div>
        </div>
      </UiCard>
    </div>

    <!-- Template Categories -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <div
        v-for="category in templateCategories"
        :key="category.id"
        class="space-y-4"
      >
        <UiCard>
          <template #header>
            <div class="flex items-center justify-between">
              <div class="flex items-center gap-3">
                <div :class="[
                  'w-10 h-10 rounded-full flex items-center justify-center text-white',
                  category.color
                ]">
                  <Icon :name="category.icon" class="h-5 w-5" />
                </div>
                <div>
                  <h3 class="text-lg font-semibold text-gray-900 dark:text-white">{{ category.name }}</h3>
                  <p class="text-sm text-gray-500 dark:text-gray-400">{{ category.description }}</p>
                </div>
              </div>
              <UiBadge variant="info">{{ getTemplatesByCategory(category.slug).length }}</UiBadge>
            </div>
          </template>
          <div class="space-y-3">
            <div
              v-for="template in getTemplatesByCategory(category.slug).slice(0, 3)"
              :key="template.id"
              class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors duration-200 cursor-pointer"
              @click="viewTemplate(template)"
            >
              <div class="flex items-center gap-3">
                <div class="w-8 h-8 bg-gray-200 dark:bg-gray-600 rounded flex items-center justify-center">
                  <Icon name="material-symbols:description" class="h-4 w-4 text-gray-500 dark:text-gray-400" />
                </div>
                <div>
                  <p class="text-sm font-medium text-gray-900 dark:text-white">{{ template.name }}</p>
                  <p class="text-xs text-gray-500 dark:text-gray-400">{{ template.usage }} uses</p>
                </div>
              </div>
              <div class="flex items-center gap-2">
                <UiBadge :variant="template.isActive ? 'success' : 'neutral'" size="sm">
                  {{ template.isActive ? 'Active' : 'Inactive' }}
                </UiBadge>
                <UiButton @click.stop="editTemplate(template)" variant="ghost" size="sm">
                  <Icon name="material-symbols:edit" class="h-4 w-4" />
                </UiButton>
              </div>
            </div>
            
            <UiButton 
              v-if="getTemplatesByCategory(category.slug).length > 3"
              @click="viewCategoryTemplates(category)"
              variant="outline" 
              size="sm" 
              class="w-full"
            >
              View All {{ getTemplatesByCategory(category.slug).length }} Templates
            </UiButton>
            
            <UiButton 
              @click="createTemplate(category)"
              variant="outline" 
              size="sm" 
              class="w-full"
            >
              <Icon name="material-symbols:add" class="h-4 w-4 mr-2" />
              Create {{ category.name }} Template
            </UiButton>
          </div>
        </UiCard>
      </div>
    </div>

    <!-- Popular Templates -->
    <UiCard>
      <template #header>
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Most Popular Templates</h3>
          <UiButton @click="viewAllTemplates" variant="ghost" size="sm">
            View All
          </UiButton>
        </div>
      </template>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <div
          v-for="template in popularTemplates"
          :key="template.id"
          class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors duration-200 cursor-pointer"
          @click="viewTemplate(template)"
        >
          <div class="flex items-center gap-3">
            <div :class="[
              'w-12 h-12 rounded-lg flex items-center justify-center text-white',
              template.category.color
            ]">
              <Icon :name="template.category.icon" class="h-6 w-6" />
            </div>
            <div>
              <p class="text-sm font-medium text-gray-900 dark:text-white">{{ template.name }}</p>
              <p class="text-xs text-gray-500 dark:text-gray-400">{{ template.category.name }}</p>
              <p class="text-xs text-gray-500 dark:text-gray-400">{{ template.usage }} uses this month</p>
            </div>
          </div>
          <div class="flex items-center gap-2">
            <UiBadge variant="success" size="sm">Popular</UiBadge>
            <UiButton @click.stop="useTemplate(template)" variant="outline" size="sm">
              <Icon name="material-symbols:play-arrow" class="h-4 w-4 mr-1" />
              Use
            </UiButton>
          </div>
        </div>
      </div>
    </UiCard>

    <!-- Recent Activity -->
    <UiCard>
      <template #header>
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Recent Template Activity</h3>
          <UiButton @click="viewActivityLog" variant="ghost" size="sm">
            View Full Log
          </UiButton>
        </div>
      </template>
      <div class="space-y-3">
        <div
          v-for="activity in recentActivity"
          :key="activity.id"
          class="flex items-start gap-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg"
        >
          <div class="flex-shrink-0">
            <div :class="[
              'w-8 h-8 rounded-full flex items-center justify-center',
              activity.type === 'created' ? 'bg-green-100 dark:bg-green-900/20' :
              activity.type === 'modified' ? 'bg-blue-100 dark:bg-blue-900/20' :
              activity.type === 'used' ? 'bg-purple-100 dark:bg-purple-900/20' :
              'bg-gray-100 dark:bg-gray-900/20'
            ]">
              <Icon :name="activity.icon" :class="[
                'h-4 w-4',
                activity.type === 'created' ? 'text-green-600 dark:text-green-400' :
                activity.type === 'modified' ? 'text-blue-600 dark:text-blue-400' :
                activity.type === 'used' ? 'text-purple-600 dark:text-purple-400' :
                'text-gray-600 dark:text-gray-400'
              ]" />
            </div>
          </div>
          <div class="flex-1 min-w-0">
            <p class="text-sm text-gray-900 dark:text-white">{{ activity.description }}</p>
            <div class="flex items-center gap-2 mt-1">
              <p class="text-xs text-gray-500 dark:text-gray-400">{{ activity.timestamp }}</p>
              <span class="text-xs text-gray-400">•</span>
              <p class="text-xs text-gray-500 dark:text-gray-400">by {{ activity.user }}</p>
            </div>
          </div>
        </div>

        <div v-if="recentActivity.length === 0" class="text-center py-6">
          <Icon name="material-symbols:history" class="h-8 w-8 text-gray-400 mx-auto mb-2" />
          <p class="text-sm text-gray-500 dark:text-gray-400">No recent activity</p>
        </div>
      </div>
    </UiCard>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { PlatformRoles } from '~/app/features/auth/constants/roles'

// Page meta configuration
definePageMeta({
  layout: 'dashboard',
  title: 'Document Templates',
  description: 'Create and manage legal document templates for automated generation',
  pageHeaderIcon: 'material-symbols:description',
  showPageHeader: true,
  showPageHeaderTitle: true,
  // pageHeaderStats: [
  //   { key: 'templates', label: 'Total Templates', value: '47', color: 'blue' },
  //   { key: 'usage', label: 'Monthly Usage', value: '1,234', color: 'green' },
  //   { key: 'categories', label: 'Categories', value: '8', color: 'purple' },
  //   { key: 'activity', label: 'Recent Activity', value: '23', color: 'yellow' }
  // ],
  pageHeaderActions: () => {
    return [
      {
        label: 'Create Template',
        icon: 'i-heroicons:document-text',
        color: 'success',
        variant: 'outline',
        click: () => navigateTo('/dashboard/templates/document/create')
      },
      {
        label: 'Manage Categories',
        icon: 'i-heroicons:tag',
        color: 'info',
        variant: 'outline',
        click: () => navigateTo('/dashboard/templates/document/categories')
      },
      {
        label: 'Import',
        icon: 'i-heroicons:arrow-up-tray',
        color: 'gray',
        variant: 'outline',
        click: () => navigateTo('/dashboard/templates/document/import')
      }
    ].reverse()
  },
  middleware: ['rbac'],
  roles: [PlatformRoles.SUPER_ADMIN],
  breadcrumbs: [
    { label: 'Templates', href: '/dashboard/templates' },
    { label: 'Document Templates' },
  ],
})

// Reactive state
const isLoading = ref(true)
const monthlyUsage = ref(1234)
const monthlyTarget = ref(1500)
const usageGrowth = ref(15.3)

// Template categories
const templateCategories = ref([
  {
    id: 1,
    name: 'Contracts',
    slug: 'contracts',
    description: 'Legal agreements and contracts',
    icon: 'material-symbols:handshake',
    color: 'bg-blue-500'
  },
  {
    id: 2,
    name: 'Legal Forms',
    slug: 'legal-forms',
    description: 'Standard legal forms and documents',
    icon: 'material-symbols:assignment',
    color: 'bg-green-500'
  },
  {
    id: 3,
    name: 'Court Documents',
    slug: 'court-documents',
    description: 'Court filings and legal pleadings',
    icon: 'material-symbols:gavel',
    color: 'bg-purple-500'
  },
  {
    id: 4,
    name: 'Corporate',
    slug: 'corporate',
    description: 'Corporate governance documents',
    icon: 'material-symbols:business',
    color: 'bg-yellow-500'
  },
  {
    id: 5,
    name: 'Real Estate',
    slug: 'real-estate',
    description: 'Property and real estate documents',
    icon: 'material-symbols:home',
    color: 'bg-red-500'
  },
  {
    id: 6,
    name: 'Employment',
    slug: 'employment',
    description: 'Employment and HR documents',
    icon: 'material-symbols:work',
    color: 'bg-indigo-500'
  },
  {
    id: 7,
    name: 'Intellectual Property',
    slug: 'intellectual-property',
    description: 'IP and patent documents',
    icon: 'material-symbols:lightbulb',
    color: 'bg-pink-500'
  },
  {
    id: 8,
    name: 'Family Law',
    slug: 'family-law',
    description: 'Family and domestic relations',
    icon: 'material-symbols:family-restroom',
    color: 'bg-teal-500'
  }
])

// Document templates data
const documentTemplates = ref([
  // Contracts
  { id: 1, name: 'Service Agreement', category: 'contracts', isActive: true, usage: 156, categoryObj: templateCategories.value[0] },
  { id: 2, name: 'Non-Disclosure Agreement', category: 'contracts', isActive: true, usage: 234, categoryObj: templateCategories.value[0] },
  { id: 3, name: 'Employment Contract', category: 'contracts', isActive: true, usage: 89, categoryObj: templateCategories.value[0] },
  { id: 4, name: 'Lease Agreement', category: 'contracts', isActive: true, usage: 167, categoryObj: templateCategories.value[0] },

  // Legal Forms
  { id: 5, name: 'Power of Attorney', category: 'legal-forms', isActive: true, usage: 78, categoryObj: templateCategories.value[1] },
  { id: 6, name: 'Affidavit Template', category: 'legal-forms', isActive: true, usage: 123, categoryObj: templateCategories.value[1] },
  { id: 7, name: 'Notarized Statement', category: 'legal-forms', isActive: true, usage: 45, categoryObj: templateCategories.value[1] },

  // Court Documents
  { id: 8, name: 'Motion to Dismiss', category: 'court-documents', isActive: true, usage: 67, categoryObj: templateCategories.value[2] },
  { id: 9, name: 'Complaint Filing', category: 'court-documents', isActive: true, usage: 89, categoryObj: templateCategories.value[2] },
  { id: 10, name: 'Discovery Request', category: 'court-documents', isActive: true, usage: 34, categoryObj: templateCategories.value[2] },

  // Corporate
  { id: 11, name: 'Board Resolution', category: 'corporate', isActive: true, usage: 56, categoryObj: templateCategories.value[3] },
  { id: 12, name: 'Shareholder Agreement', category: 'corporate', isActive: true, usage: 78, categoryObj: templateCategories.value[3] },

  // Real Estate
  { id: 13, name: 'Purchase Agreement', category: 'real-estate', isActive: true, usage: 145, categoryObj: templateCategories.value[4] },
  { id: 14, name: 'Property Deed', category: 'real-estate', isActive: true, usage: 98, categoryObj: templateCategories.value[4] },

  // Employment
  { id: 15, name: 'Employee Handbook', category: 'employment', isActive: true, usage: 67, categoryObj: templateCategories.value[5] },
  { id: 16, name: 'Termination Letter', category: 'employment', isActive: true, usage: 34, categoryObj: templateCategories.value[5] },

  // Intellectual Property
  { id: 17, name: 'Patent Application', category: 'intellectual-property', isActive: true, usage: 23, categoryObj: templateCategories.value[6] },
  { id: 18, name: 'Trademark Filing', category: 'intellectual-property', isActive: true, usage: 45, categoryObj: templateCategories.value[6] },

  // Family Law
  { id: 19, name: 'Divorce Petition', category: 'family-law', isActive: true, usage: 78, categoryObj: templateCategories.value[7] },
  { id: 20, name: 'Child Custody Agreement', category: 'family-law', isActive: true, usage: 56, categoryObj: templateCategories.value[7] }
])

// Recent activity data
const recentActivity = ref([
  {
    id: 1,
    type: 'created',
    icon: 'material-symbols:add',
    description: 'New "Software License Agreement" template created',
    timestamp: '2 hours ago',
    user: 'John Admin'
  },
  {
    id: 2,
    type: 'modified',
    icon: 'material-symbols:edit',
    description: 'Updated "Service Agreement" template with new clauses',
    timestamp: '5 hours ago',
    user: 'Sarah Legal'
  },
  {
    id: 3,
    type: 'used',
    icon: 'material-symbols:play-arrow',
    description: 'Generated 15 documents using "NDA Template"',
    timestamp: '1 day ago',
    user: 'Mike Lawyer'
  },
  {
    id: 4,
    type: 'created',
    icon: 'material-symbols:add',
    description: 'New "Privacy Policy" template added to legal forms',
    timestamp: '2 days ago',
    user: 'Lisa Admin'
  }
])

// Computed properties
const activeTemplates = computed(() => documentTemplates.value.filter(t => t.isActive).length)
const inactiveTemplates = computed(() => documentTemplates.value.filter(t => !t.isActive).length)

const popularTemplates = computed(() =>
  documentTemplates.value
    .sort((a, b) => b.usage - a.usage)
    .slice(0, 6)
    .map(template => ({
      ...template,
      category: templateCategories.value.find(cat => cat.slug === template.category) || templateCategories.value[0]
    }))
)

// Methods
const getTemplatesByCategory = (categorySlug: string) => {
  return documentTemplates.value.filter(template => template.category === categorySlug)
}

const viewTemplate = (template: any) => {
  navigateTo(`/dashboard/templates/${template.id}`)
}

const editTemplate = (template: any) => {
  navigateTo(`/dashboard/templates/${template.id}/edit`)
}

const useTemplate = (template: any) => {
  navigateTo(`/dashboard/documents/create?template=${template.id}`)
}

const createTemplate = (category: any) => {
  navigateTo(`/dashboard/templates/create/document?category=${category.slug}`)
}

const viewCategoryTemplates = (category: any) => {
  navigateTo(`/dashboard/templates?category=${category.slug}`)
}

const viewAllTemplates = () => {
  navigateTo('/dashboard/templates')
}

const viewActivityLog = () => {
  navigateTo('/dashboard/templates/activity')
}

const importTemplates = async () => {
  try {
    // In a real app, you would open a file picker and import templates
    console.log('Opening template import dialog...')

    // Simulate file import
    const input = document.createElement('input')
    input.type = 'file'
    input.accept = '.json,.docx,.pdf'
    input.multiple = true

    input.onchange = async (event: any) => {
      const files = event.target.files
      console.log('Importing templates:', files.length, 'files')

      // In a real app, you would process and upload the files
      // await $api.post('/templates/import', formData)
    }

    input.click()

  } catch (error) {
    console.error('Error importing templates:', error)
  }
}

// Lifecycle hooks
onMounted(async () => {
  // Initial data load
  isLoading.value = false
  console.log('Document templates loaded')
})
</script>
