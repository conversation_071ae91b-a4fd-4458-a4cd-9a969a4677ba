/**
 * Client-side performance optimizations plugin
 * Implements various performance enhancements for public pages
 */
import { defineNuxtPlugin } from '#app';
 
export default defineNuxtPlugin(() => {
  // Only run on client side
  if (typeof window === 'undefined') return

  /**
   * Lazy load images with intersection observer
   */
  const setupLazyLoading = () => {
    const imageObserver = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          const img = entry.target as HTMLImageElement
          const src = img.dataset.src
          
          if (src) {
            img.src = src
            img.removeAttribute('data-src')
            img.classList.remove('lazy-load')
            img.classList.add('lazy-loaded')
            imageObserver.unobserve(img)
          }
        }
      })
    }, {
      threshold: 0.1,
      rootMargin: '50px'
    })

    // Observe all images with data-src attribute
    const lazyImages = document.querySelectorAll('img[data-src]')
    lazyImages.forEach(img => imageObserver.observe(img))

    // Watch for dynamically added images
    const mutationObserver = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            const element = node as Element
            const lazyImages = element.querySelectorAll('img[data-src]')
            lazyImages.forEach(img => imageObserver.observe(img))
          }
        })
      })
    })

    mutationObserver.observe(document.body, {
      childList: true,
      subtree: true
    })
  }

  /**
   * Preload critical resources
   */
  const preloadCriticalResources = () => {
    const criticalResources = [
      // '/fonts/inter-var.woff2',
      // '/images/hero-bg.webp',
      // '/logos/legal-saas-logo-full.svg'
    ]

    criticalResources.forEach(resource => {
      const link = document.createElement('link')
      link.rel = 'preload'
      link.href = resource
      
      if (resource.includes('.woff2')) {
        link.as = 'font'
        link.type = 'font/woff2'
        link.crossOrigin = 'anonymous'
      } else if (resource.includes('.webp') || resource.includes('.jpg') || resource.includes('.png')) {
        link.as = 'image'
      }
      
      document.head.appendChild(link)
    })
  }

  /**
   * Optimize third-party scripts
   */
  const optimizeThirdPartyScripts = () => {
    // Defer non-critical scripts
    const deferredScripts = document.querySelectorAll('script[data-defer]')
    deferredScripts.forEach(script => {
      const newScript = document.createElement('script')
      newScript.src = script.getAttribute('src') || ''
      newScript.defer = true
      script.parentNode?.replaceChild(newScript, script)
    })

    // Load analytics after user interaction
    let analyticsLoaded = false
    const loadAnalytics = () => {
      if (analyticsLoaded) return
      analyticsLoaded = true
      
      // Load Google Analytics or other analytics here
      // Example: gtag('config', 'GA_MEASUREMENT_ID')
    }

    // Load analytics on first user interaction
    const interactionEvents = ['click', 'scroll', 'keydown', 'touchstart']
    interactionEvents.forEach(event => {
      document.addEventListener(event, loadAnalytics, { once: true, passive: true })
    })

    // Fallback: load after 5 seconds
    setTimeout(loadAnalytics, 5000)
  }

  /**
   * Implement resource hints
   */
  const addResourceHints = () => {
    const hints = [
      { rel: 'dns-prefetch', href: '//fonts.googleapis.com' },
      { rel: 'dns-prefetch', href: '//www.google-analytics.com' },
      { rel: 'preconnect', href: 'https://fonts.gstatic.com', crossorigin: true }
    ]

    hints.forEach(hint => {
      const link = document.createElement('link')
      link.rel = hint.rel
      link.href = hint.href
      if (hint.crossorigin) link.crossOrigin = 'anonymous'
      document.head.appendChild(link)
    })
  }

  /**
   * Optimize CSS delivery
   */
  const optimizeCSSDelivery = () => {
    // Load non-critical CSS asynchronously
    const nonCriticalCSS = [
      // '/css/animations.css',
      // '/css/print.css'
    ]

    nonCriticalCSS.forEach(href => {
      const link = document.createElement('link')
      link.rel = 'stylesheet'
      link.href = href
      link.media = 'print'
      link.onload = () => { link.media = 'all' }
      document.head.appendChild(link)
    })
  }

  /**
   * Implement service worker for caching
   */
  const registerServiceWorker = async () => {
    if ('serviceWorker' in navigator) {
      try {
        const registration = await navigator.serviceWorker.register('/sw.js')
        console.log('Service Worker registered:', registration)
      } catch (error) {
        console.log('Service Worker registration failed:', error)
      }
    }
  }

  /**
   * Monitor Core Web Vitals
   */
  const monitorCoreWebVitals = () => {
    // Monitor Largest Contentful Paint (LCP)
    const observeLCP = () => {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        const lastEntry = entries[entries.length - 1]
        console.log('LCP:', lastEntry.startTime)
      })
      observer.observe({ entryTypes: ['largest-contentful-paint'] })
    }

    // Monitor First Input Delay (FID)
    const observeFID = () => {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach(entry => {
          // Check if entry is PerformanceEventTiming
          if ('processingStart' in entry && 'startTime' in entry) {
            const firstInputEntry = entry as PerformanceEventTiming;
            console.log('FID:', firstInputEntry.processingStart - firstInputEntry.startTime);
          }
        });
      })
      observer.observe({ entryTypes: ['first-input'] })
    }

    // Monitor Cumulative Layout Shift (CLS)
    const observeCLS = () => {
      let clsValue = 0
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach(entry => {
          // Check if entry has properties of LayoutShift
          if (typeof (entry as any).value === 'number' && typeof (entry as any).hadRecentInput === 'boolean') {
            const layoutShiftEntry = entry as any; // Cast to any for property access
            if (!layoutShiftEntry.hadRecentInput) {
              clsValue += layoutShiftEntry.value;
            }
          }
        });
        console.log('CLS:', clsValue)
      })
      observer.observe({ entryTypes: ['layout-shift'] })
    }

    observeLCP()
    observeFID()
    observeCLS()
  }

  /**
   * Optimize scroll performance
   */
  const optimizeScrollPerformance = () => {
    let ticking = false

    const updateScrollPosition = () => {
      // Update scroll-dependent elements here
      ticking = false
    }

    const requestTick = () => {
      if (!ticking) {
        requestAnimationFrame(updateScrollPosition)
        ticking = true
      }
    }

    window.addEventListener('scroll', requestTick, { passive: true })
  }

  /**
   * Reduce layout thrashing
   */
  const reduceLayoutThrashing = () => {
    // Batch DOM reads and writes
    const batchedUpdates: (() => void)[] = []
    
    const flushUpdates = () => {
      batchedUpdates.forEach(update => update())
      batchedUpdates.length = 0
    }

    // Expose global function for batching updates
    ;(window as any).batchUpdate = (updateFn: () => void) => {
      batchedUpdates.push(updateFn)
      if (batchedUpdates.length === 1) {
        requestAnimationFrame(flushUpdates)
      }
    }
  }

  /**
   * Initialize all optimizations
   */
  const initializeOptimizations = () => {
    // Run immediately
    preloadCriticalResources()
    addResourceHints()
    optimizeCSSDelivery()
    reduceLayoutThrashing()

    // Run after DOM is ready
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => {
        setupLazyLoading()
        optimizeThirdPartyScripts()
        optimizeScrollPerformance()
      })
    } else {
      setupLazyLoading()
      optimizeThirdPartyScripts()
      optimizeScrollPerformance()
    }

    // Run after page load
    window.addEventListener('load', () => {
      registerServiceWorker()
      monitorCoreWebVitals()
    })
  }

  // Initialize optimizations
  initializeOptimizations()

  return {
    provide: {
      performance: {
        preloadResource: (url: string, type: string) => {
          const link = document.createElement('link')
          link.rel = 'preload'
          link.href = url
          link.as = type
          document.head.appendChild(link)
        },
        batchUpdate: (updateFn: () => void) => {
          ;(window as any).batchUpdate(updateFn)
        }
      }
    }
  }
})
