<template>
  <div class="relative">
    <!-- Search Input -->
    <div class="relative">
      <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
        <Icon name="heroicons:magnifying-glass" class="h-5 w-5 text-gray-400" />
      </div>
      <input
        ref="searchInput"
        v-model="searchQuery"
        type="text"
        :placeholder="placeholder"
        class="block w-full pl-10 pr-10 py-2.5 border border-gray-300 rounded-lg leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-brandPrimary focus:border-brandPrimary sm:text-sm transition-colors duration-200"
        @keydown.escape="clearSearch"
        @keydown.enter="handleEnter"
        @focus="showResults = true"
        @blur="handleBlur"
      />
      
      <!-- Clear Button -->
      <div v-if="searchQuery" class="absolute inset-y-0 right-0 pr-3 flex items-center">
        <button
          @click="clearSearch"
          class="text-gray-400 hover:text-gray-600 transition-colors duration-200 p-1 rounded-md hover:bg-gray-100"
          type="button"
        >
          <Icon name="heroicons:x-mark" class="h-4 w-4" />
        </button>
      </div>
    </div>

    <!-- Search Results Dropdown -->
    <div
      v-if="showResults && searchQuery && filteredResults.length > 0"
      class="absolute z-50 mt-2 w-full bg-white rounded-lg shadow-lg border border-gray-200 max-h-80 overflow-y-auto"
    >
      <div class="py-2">
        <div
          v-for="(result, index) in filteredResults"
          :key="result.value"
          @click="selectResult(result)"
          :class="[
            'px-4 py-3 cursor-pointer transition-colors duration-150',
            index === highlightedIndex
              ? 'bg-brandPrimary/10 text-brandPrimary'
              : 'hover:bg-gray-50 text-gray-900'
          ]"
        >
          <div class="flex items-center gap-3">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-brandPrimary/10 rounded-lg flex items-center justify-center">
                <Icon :name="result.icon" class="h-4 w-4 text-brandPrimary" />
              </div>
            </div>
            <div class="flex-1 min-w-0">
              <p class="text-sm font-medium truncate">{{ result.label }}</p>
              <p class="text-xs text-gray-500 truncate">{{ result.description }}</p>
            </div>
            <div class="flex-shrink-0">
              <Icon name="heroicons:arrow-right" class="h-4 w-4 text-gray-400" />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- No Results -->
    <div
      v-if="showResults && searchQuery && filteredResults.length === 0"
      class="absolute z-50 mt-2 w-full bg-white rounded-lg shadow-lg border border-gray-200 p-4"
    >
      <div class="text-center text-gray-500">
        <Icon name="heroicons:magnifying-glass" class="mx-auto h-8 w-8 text-gray-400 mb-2" />
        <p class="text-sm">No settings found for "{{ searchQuery }}"</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, onMounted, onUnmounted } from 'vue';

interface SearchResult {
  label: string;
  value: string;
  icon: string;
  description: string;
  category: string;
  keywords?: string[];
}

interface Props {
  items: SearchResult[];
  placeholder?: string;
  modelValue?: string;
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: 'Search settings...',
  modelValue: ''
});

const emit = defineEmits<{
  'update:modelValue': [value: string];
  'select': [result: SearchResult];
}>();

// Refs
const searchInput = ref<HTMLInputElement | null>(null);
const searchQuery = ref(props.modelValue);
const showResults = ref(false);
const highlightedIndex = ref(-1);

// Computed
const filteredResults = computed(() => {
  if (!searchQuery.value.trim()) return [];
  
  const query = searchQuery.value.toLowerCase().trim();
  return props.items.filter(item => {
    return (
      item.label.toLowerCase().includes(query) ||
      item.description.toLowerCase().includes(query) ||
      item.category.toLowerCase().includes(query) ||
      item.keywords?.some(keyword => keyword.toLowerCase().includes(query))
    );
  }).slice(0, 8); // Limit results
});

// Watch for external changes
watch(() => props.modelValue, (newValue) => {
  searchQuery.value = newValue;
});

// Watch for search query changes
watch(searchQuery, (newValue) => {
  emit('update:modelValue', newValue);
  highlightedIndex.value = -1;
});

// Methods
const clearSearch = () => {
  searchQuery.value = '';
  showResults.value = false;
  highlightedIndex.value = -1;
};

const selectResult = (result: SearchResult) => {
  emit('select', result);
  showResults.value = false;
  highlightedIndex.value = -1;
  searchInput.value?.blur();
};

const handleEnter = () => {
  if (highlightedIndex.value >= 0 && filteredResults.value[highlightedIndex.value]) {
    selectResult(filteredResults.value[highlightedIndex.value]);
  }
};

const handleBlur = () => {
  // Delay hiding results to allow for clicks
  setTimeout(() => {
    showResults.value = false;
    highlightedIndex.value = -1;
  }, 200);
};

// Keyboard navigation
const handleKeydown = (event: KeyboardEvent) => {
  if (!showResults.value || filteredResults.value.length === 0) return;
  
  switch (event.key) {
    case 'ArrowDown':
      event.preventDefault();
      highlightedIndex.value = Math.min(highlightedIndex.value + 1, filteredResults.value.length - 1);
      break;
    case 'ArrowUp':
      event.preventDefault();
      highlightedIndex.value = Math.max(highlightedIndex.value - 1, -1);
      break;
  }
};

// Focus method for external use
const focus = () => {
  nextTick(() => {
    searchInput.value?.focus();
  });
};

// Lifecycle
onMounted(() => {
  document.addEventListener('keydown', handleKeydown);
});

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown);
});

// Expose methods
defineExpose({
  focus,
  clearSearch
});
</script>
