<!--
  Platform Navigation Component

  Enhanced sidebar navigation with role-based menu items and multi-level support
-->

<template>
  <nav class="flex flex-col h-full bg-white border-r border-gray-200" role="navigation" :aria-label="$t('navigation.mainNavigation')">
    <!-- Logo Section -->
    <div class="flex items-center px-4 py-4 border-b border-gray-200">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <Icon name="heroicons:scale" size="calc(var(--spacing) * 9)" class="text-brandPrimary-600" />
        </div>
        <div v-if="!collapsed" class="ltr:ml-3 rtl:mr-3">
          <h1 class="text-lg font-semibold text-gray-900">{{ $t('navigation.legalSaas') }}</h1>
        </div>
      </div>
    </div>

    <!-- Navigation Menu -->
    <div class="flex-1 px-3 py-4 space-y-1 overflow-y-auto">
      <template v-for="item in filteredMenuItems" :key="item.id">
        <!-- Menu Item with Children (Expandable) -->
        <div v-if="item.children && item.children.length > 0" class="space-y-1">
          <!-- Parent Item -->
          <button @click="toggleSubmenu(item.id)" :class="[
              'group w-full flex items-center justify-between px-3 py-2 text-sm font-medium rounded-md transition-colors duration-200',
              hasActiveChildWrapper(item) || isActive(item.path, true)
                ? 'bg-brandPrimary-100 text-brandPrimary-700'
                : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'
            ]">
            <div class="flex items-center">
              <Icon :name="item.icon" size="calc(var(--spacing) * 5)" :class="[
                  'transition-colors duration-200',
                  collapsed ? 'ltr:mr-0 rtl:ml-0' : 'ltr:mr-3 rtl:ml-3',
                  hasActiveChildWrapper(item) || isActive(item.path, true)
                    ? 'text-brandPrimary-500'
                    : 'text-gray-400 group-hover:text-gray-500'
                ]" />
              <span v-if="!collapsed">{{ item.label }}</span>
            </div>
            <Icon v-if="!collapsed"
              :name="expandedItems.includes(item.id) ? 'heroicons:chevron-up' : 'heroicons:chevron-down'"
              class="h-4 w-4 text-gray-400 transition-transform duration-200" />
          </button>

          <!-- Submenu Items -->
          <Transition enter-active-class="transition-all duration-200 ease-out" enter-from-class="opacity-0 max-h-0"
            enter-to-class="opacity-100 max-h-96" leave-active-class="transition-all duration-200 ease-in"
            leave-from-class="opacity-100 max-h-96" leave-to-class="opacity-0 max-h-0">
            <div v-if="expandedItems.includes(item.id) && !collapsed" class="ltr:ml-6 rtl:mr-6 space-y-1 overflow-hidden">
              <template v-for="child in item.children" :key="child.id">
                <!-- Second Level Item -->
                <div v-if="child.children && child.children.length > 0" class="space-y-1">
                  <!-- Second Level Parent -->
                  <button @click="toggleSubmenu(child.id)" :class="[
                      'group w-full flex items-center justify-between px-3 py-2 text-sm rounded-md transition-colors duration-200',
                      hasActiveChildWrapper(child) || isActive(child.path, true)
                        ? 'bg-brandPrimary-50 text-brandPrimary-600'
                        : 'text-gray-600 hover:bg-gray-50 hover:text-gray-800'
                    ]">
                    <div class="flex items-center">
                      <Icon :name="child.icon" size="calc(var(--spacing) * 4)" :class="[
                          'ltr:mr-2 rtl:ml-2 transition-colors duration-200',
                          hasActiveChildWrapper(child) || isActive(child.path, true)
                            ? 'text-brandPrimary-500'
                            : 'text-gray-400 group-hover:text-gray-500'
                        ]" />
                      <span>{{ child.label }}</span>
                    </div>
                    <Icon :name="expandedItems.includes(child.id) ? 'heroicons:chevron-up' : 'heroicons:chevron-down'"
                      size="calc(var(--spacing) * 3)"
                      class=" text-gray-400 transition-transform duration-200" />
                  </button>

                  <!-- Third Level Items -->
                  <Transition enter-active-class="transition-all duration-200 ease-out"
                    enter-from-class="opacity-0 max-h-0" enter-to-class="opacity-100 max-h-96"
                    leave-active-class="transition-all duration-200 ease-in" leave-from-class="opacity-100 max-h-96"
                    leave-to-class="opacity-0 max-h-0">
                    <div v-if="expandedItems.includes(child.id)" class="ltr:ml-4 rtl:mr-4 space-y-1 overflow-hidden">
                      <NuxtLink v-for="grandchild in child.children" :key="grandchild.id" :to="grandchild.path" :class="[
                          'group flex items-center px-3 py-1.5 text-xs rounded-md transition-colors duration-200',
                          isActive(grandchild.path)
                            ? 'bg-brandPrimary-50 text-brandPrimary-600 '
                            : 'text-gray-500 hover:bg-gray-50 hover:text-gray-700'
                        ]">
                        <Icon :name="grandchild.icon" size="calc(var(--spacing) * 3)" :class="[
                            'ltr:mr-2 rtl:ml-2 transition-colors duration-200',
                            isActive(grandchild.path, true)
                              ? 'text-brandPrimary-500'
                              : 'text-gray-400 group-hover:text-gray-500'
                          ]" />
                        <span>{{ grandchild.label }}</span>
                        <span v-if="grandchild.badge" :class="[
                          'ltr:ml-auto rtl:mr-auto px-1.5 py-0.5 text-xs rounded-full',
                          grandchild.badge.type === 'success' ? 'bg-green-100 text-green-800' :
                          grandchild.badge.type === 'warning' ? 'bg-yellow-100 text-yellow-800' :
                          grandchild.badge.type === 'error' ? 'bg-red-100 text-red-800' :
                          'bg-gray-100 text-gray-800'
                        ]">
                          {{ grandchild.badge.text }}
                        </span>
                      </NuxtLink>
                    </div>
                  </Transition>
                </div>

                <!-- Second Level Leaf Item -->
                <NuxtLink v-else :to="child.path" :class="[
                    'group flex items-center px-3 py-2 text-sm rounded-md transition-colors duration-200',
                    isActive(child.path, true)
                      ? 'bg-brandPrimary-50 text-brandPrimary-600 '
                      : 'text-gray-600 hover:bg-gray-50 hover:text-gray-800'
                  ]">
                  <Icon :name="child.icon" size="calc(var(--spacing) * 4)" :class="[
                      'mr-2 transition-colors duration-200',
                      isActive(child.path, true)
                        ? 'text-brandPrimary-500'
                        : 'text-gray-400 group-hover:text-gray-500'
                    ]" />
                  <span>{{ child.label }}</span>
                  <span v-if="child.badge" :class="[
                    'ml-auto px-1.5 py-0.5 text-xs rounded-full',
                    child.badge.type === 'success' ? 'bg-green-100 text-green-800' :
                    child.badge.type === 'warning' ? 'bg-yellow-100 text-yellow-800' :
                    child.badge.type === 'error' ? 'bg-red-100 text-red-800' :
                    'bg-gray-100 text-gray-800'
                  ]">
                    {{ child.badge.text }}
                  </span>
                </NuxtLink>
              </template>
            </div>
          </Transition>
        </div>

        <!-- Single Menu Item (No Children) -->
        <NuxtLink v-else :to="item.path" :class="[
            'group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors duration-200',
            isActive(item.path, true)
              ? 'bg-brandPrimary-100 text-brandPrimary-700'
              : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'
          ]">
          <Icon :name="item.icon" size="calc(var(--spacing) * 5)" :class="[
              'transition-colors duration-200',
              collapsed ? 'mr-0' : 'mr-3',
              isActive(item.path, true)
                ? 'text-brandPrimary-500'
                : 'text-gray-400 group-hover:text-gray-500'
            ]" />
          <span v-if="!collapsed">{{ item.label }}</span>
          <span v-if="item.badge && !collapsed" :class="[
            'ml-auto px-1.5 py-0.5 text-xs rounded-full',
            item.badge.type === 'success' ? 'bg-green-100 text-green-800' :
            item.badge.type === 'warning' ? 'bg-yellow-100 text-yellow-800' :
            item.badge.type === 'error' ? 'bg-red-100 text-red-800' :
            'bg-gray-100 text-gray-800'
          ]">
            {{ item.badge.text }}
          </span>
        </NuxtLink>
      </template>
    </div>
    <!-- Collapse Toggle -->
    <div class="border-t border-gray-200 p-3">
      <button @click="$emit('toggle-collapse')"
        class="w-full flex items-center justify-center px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100 hover:text-gray-900 rounded-md transition-colors duration-200">
        <Icon :name="collapsed ? 'heroicons:chevron-right' : 'heroicons:chevron-left'"
          size="calc(var(--spacing) * 4)" />
        <span v-if="!collapsed" class="ml-2">Collapse</span>
      </button>
    </div>
  </nav>
</template>

<script setup lang="ts">
import { watch } from 'vue'
import { useRoute } from 'vue-router'
import { useNavigation, type MenuItem } from '~/composables/useNavigation'
import { PlatformRoles, TenantRoles } from '~/app/features/auth/constants/roles'
// Props
interface Props {
  collapsed?: boolean
  userRoles?: string[]
  tenantRoles?: string[]
  activeTenantId?: string | null
}

const props = withDefaults(defineProps<Props>(), {
  collapsed: false,
  userRoles: () => [PlatformRoles.SUPER_ADMIN], // Default to super_admin for demo
  tenantRoles: () => [TenantRoles.TENANT_OWNER],
  activeTenantId: null
})

// Emits
defineEmits<{
  'toggle-collapse': []
}>()

// Composables
const route = useRoute()
const { t: $t } = useI18n()
const navigation = useNavigation({
  userRoles: props.userRoles,
  tenantRoles: props.tenantRoles,
  activeTenantId: props.activeTenantId
})
// console.log(navigation);

// Destructure navigation composable
const {
  expandedItems,
  filteredMenuItems,
  isMenuItemActive,
  hasActiveChild,
  toggleSubmenu,
  expandToPath,
  collapseAll
} = navigation

// Methods
const isActive = (path?: string, exact = false): boolean => {
  if (!path) return false

  if (exact) {
    return route.path === path
  }
  return route.path.startsWith(path)
}

const hasActiveChildWrapper = (item: MenuItem): boolean => {
  return hasActiveChild(item, route.path)
}

// Auto-expand active menu items
watch(() => route.path, (newPath) => {
  expandToPath(newPath)
}, { immediate: true })

// Watch for collapsed state changes
watch(() => props.collapsed, (newCollapsed) => {
  if (newCollapsed) {
    // Clear expanded items when collapsed
    collapseAll()
  }
})

// Watch for role changes and update navigation
watch([
  () => props.userRoles,
  () => props.tenantRoles,
  () => props.activeTenantId
], () => {
  // Navigation will automatically update due to reactive props
}, { deep: true })
</script>
