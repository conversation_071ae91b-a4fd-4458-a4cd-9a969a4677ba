import { z } from 'zod'

function buildZodSchema(schema: any[]): z.ZodObject<any> {
  const shape: Record<string, z.ZodType<any, any>> = {}

  function traverse(schemaItems: any[]) {
    for (const item of schemaItems) {
      if (item.component && item.name && item.rules) {
        shape[item.name] = item.rules
      } else if (item.tabs && item.tabs.tabs) {
        traverse(item.tabs.tabs.flatMap((tab: any) => tab.content))
      } else if (item.accordions && item.accordions.accordions) {
        traverse(item.accordions.accordions.flatMap((accordion: any) => accordion.content))
      } else if (item.card && item.card.content) {
        traverse(item.card.content)
      }
    }
  }

  traverse(schema)
  return z.object(shape)
}

export function useZodSchema(schema: any) {
  if (Array.isArray(schema)) {
    return buildZodSchema(schema)
  }

  const contentSchema = buildZodSchema(schema.content || [])
  const sidebarSchema = buildZodSchema(schema.sidebar || [])

  return contentSchema.merge(sidebarSchema)
}