<template>
  <ui-card v-bind="cardProps" :title="cardTitle" :subtitle="cardSubtitle">
    <template #header v-if="props.props.header">
      <slot name="header" v-bind="cardProps" />
    </template>
    <slot />
  </ui-card>
</template>
<script setup lang="ts">
import { useFormsProps } from "~/app/shared/composables/useFormsProps";

defineOptions({
  inheritAttrs: false,
});

interface Props {
  subtitle?: string | Function;
  title?: string | Function;
  props?: any;
}

const props = withDefaults(defineProps<Props>(), {
  props: {},
});

const { props: cardProps, title: cardTitle, subtitle: cardSubtitle } = useFormsProps(
  props
);
 


</script>
