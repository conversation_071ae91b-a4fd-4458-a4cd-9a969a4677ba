<template>
  <div
    ref="containerRef"
    class="virtual-scroll-container"
    :style="{ height: containerHeight + 'px' }"
    @scroll="handleScroll"
  >
    <!-- Virtual spacer for items before visible range -->
    <div :style="{ height: offsetY + 'px' }"></div>
    
    <!-- Visible items -->
    <div
      v-for="(item, index) in visibleItems"
      :key="getItemKey(item, startIndex + index)"
      :style="{ height: itemHeight + 'px' }"
      class="virtual-scroll-item"
    >
      <slot :item="item" :index="startIndex + index"></slot>
    </div>
    
    <!-- Virtual spacer for items after visible range -->
    <div :style="{ height: remainingHeight + 'px' }"></div>
    
    <!-- Loading indicator -->
    <div v-if="loading" class="flex items-center justify-center py-8">
      <UiSpinner class="h-6 w-6" />
      <span class="ml-2 text-sm text-gray-500 dark:text-gray-400">Loading more items...</span>
    </div>
    
    <!-- End of list indicator -->
    <div v-if="!hasMore && items.length > 0" class="text-center py-4 text-sm text-gray-500 dark:text-gray-400">
      End of list
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, nextTick, onMounted, onUnmounted, ref, watch } from 'vue'

interface Props {
  items: any[]
  itemHeight: number
  containerHeight: number
  overscan?: number
  loading?: boolean
  hasMore?: boolean
  itemKey?: string | ((item: any, index: number) => string | number)
}

interface Emits {
  (e: 'load-more'): void
  (e: 'scroll', payload: { scrollTop: number; scrollLeft: number }): void
}

const props = withDefaults(defineProps<Props>(), {
  overscan: 5,
  loading: false,
  hasMore: true,
  itemKey: 'id'
})

const emit = defineEmits<Emits>()

// Refs
const containerRef = ref<HTMLElement>()
const scrollTop = ref(0)

// Computed properties
const totalHeight = computed(() => props.items.length * props.itemHeight)

const visibleCount = computed(() => Math.ceil(props.containerHeight / props.itemHeight))

const startIndex = computed(() => {
  const index = Math.floor(scrollTop.value / props.itemHeight) - props.overscan
  return Math.max(0, index)
})

const endIndex = computed(() => {
  const index = startIndex.value + visibleCount.value + props.overscan * 2
  return Math.min(props.items.length - 1, index)
})

const visibleItems = computed(() => {
  return props.items.slice(startIndex.value, endIndex.value + 1)
})

const offsetY = computed(() => startIndex.value * props.itemHeight)

const remainingHeight = computed(() => {
  const remaining = totalHeight.value - offsetY.value - (visibleItems.value.length * props.itemHeight)
  return Math.max(0, remaining)
})

// Methods
const handleScroll = (event: Event) => {
  const target = event.target as HTMLElement
  scrollTop.value = target.scrollTop
  
  emit('scroll', {
    scrollTop: target.scrollTop,
    scrollLeft: target.scrollLeft
  })
  
  // Check if we need to load more items
  const scrollPercentage = (target.scrollTop + target.clientHeight) / target.scrollHeight
  if (scrollPercentage > 0.8 && props.hasMore && !props.loading) {
    emit('load-more')
  }
}

const getItemKey = (item: any, index: number) => {
  if (typeof props.itemKey === 'function') {
    return props.itemKey(item, index)
  }
  return item[props.itemKey] || index
}

const scrollToIndex = (index: number, behavior: ScrollBehavior = 'smooth') => {
  if (containerRef.value) {
    const scrollTop = index * props.itemHeight
    containerRef.value.scrollTo({
      top: scrollTop,
      behavior
    })
  }
}

const scrollToTop = (behavior: ScrollBehavior = 'smooth') => {
  scrollToIndex(0, behavior)
}

const scrollToBottom = (behavior: ScrollBehavior = 'smooth') => {
  if (containerRef.value) {
    containerRef.value.scrollTo({
      top: containerRef.value.scrollHeight,
      behavior
    })
  }
}

// Performance optimization: throttle scroll events
let scrollTimeout: NodeJS.Timeout | null = null
const throttledHandleScroll = (event: Event) => {
  if (scrollTimeout) return
  
  scrollTimeout = setTimeout(() => {
    handleScroll(event)
    scrollTimeout = null
  }, 16) // ~60fps
}

// Intersection Observer for better performance
let intersectionObserver: IntersectionObserver | null = null

const setupIntersectionObserver = () => {
  if (!containerRef.value) return
  
  intersectionObserver = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          // Item is visible, can trigger any visibility-based logic
        }
      })
    },
    {
      root: containerRef.value,
      rootMargin: '50px',
      threshold: 0.1
    }
  )
}

// Resize observer for responsive behavior
let resizeObserver: ResizeObserver | null = null

const setupResizeObserver = () => {
  if (!containerRef.value) return
  
  resizeObserver = new ResizeObserver((entries) => {
    for (const entry of entries) {
      // Handle container resize if needed
      nextTick(() => {
        // Recalculate visible items after resize
      })
    }
  })
  
  resizeObserver.observe(containerRef.value)
}

// Lifecycle
onMounted(() => {
  if (containerRef.value) {
    // Use throttled scroll handler for better performance
    containerRef.value.addEventListener('scroll', throttledHandleScroll, { passive: true })
    
    // Setup observers
    setupIntersectionObserver()
    setupResizeObserver()
  }
})

onUnmounted(() => {
  if (containerRef.value) {
    containerRef.value.removeEventListener('scroll', throttledHandleScroll)
  }
  
  if (intersectionObserver) {
    intersectionObserver.disconnect()
  }
  
  if (resizeObserver) {
    resizeObserver.disconnect()
  }
  
  if (scrollTimeout) {
    clearTimeout(scrollTimeout)
  }
})

// Watch for items changes and maintain scroll position if needed
watch(() => props.items.length, (newLength, oldLength) => {
  if (newLength > oldLength && containerRef.value) {
    // New items added, maintain scroll position relative to content
    nextTick(() => {
      const currentScrollPercentage = scrollTop.value / (totalHeight.value - props.containerHeight)
      if (currentScrollPercentage > 0.8) {
        // User was near bottom, keep them there
        scrollToBottom('auto')
      }
    })
  }
})

// Expose methods to parent
defineExpose({
  scrollToIndex,
  scrollToTop,
  scrollToBottom,
  getVisibleRange: () => ({ start: startIndex.value, end: endIndex.value })
})
</script>

<style scoped>
.virtual-scroll-container {
  overflow-y: auto;
  overflow-x: hidden;
  position: relative;
}

.virtual-scroll-item {
  display: flex;
  align-items: center;
  width: 100%;
}

/* Custom scrollbar */
.virtual-scroll-container::-webkit-scrollbar {
  width: 8px;
}

.virtual-scroll-container::-webkit-scrollbar-track {
  background: var(--color-gray-100);
  border-radius: 4px;
}

.virtual-scroll-container::-webkit-scrollbar-thumb {
  background: var(--color-gray-400);
  border-radius: 4px;
}

.virtual-scroll-container::-webkit-scrollbar-thumb:hover {
  background: var(--color-gray-500);
}

/* Dark mode scrollbar */
@media (prefers-color-scheme: dark) {
  .virtual-scroll-container::-webkit-scrollbar-track {
    background: var(--color-gray-800);
  }
  
  .virtual-scroll-container::-webkit-scrollbar-thumb {
    background: var(--color-gray-600);
  }
  
  .virtual-scroll-container::-webkit-scrollbar-thumb:hover {
    background: var(--color-gray-500);
  }
}

/* Firefox scrollbar */
.virtual-scroll-container {
  scrollbar-width: thin;
  scrollbar-color: var(--color-gray-400) var(--color-gray-100);
}

@media (prefers-color-scheme: dark) {
  .virtual-scroll-container {
    scrollbar-color: var(--color-gray-600) var(--color-gray-800);
  }
}
</style>
