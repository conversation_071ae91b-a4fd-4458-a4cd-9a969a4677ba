import { ref, type Ref } from 'vue'
import { useGlobalModal } from './useGlobalModal'
import { useModalComponentStore } from '~/stores/modalComponentStore'

/**
 * Composable for accessing modal component content and methods
 */
export function useModalComponentAccess() {
  const store = useModalComponentStore()

  /**
   * Get component reference from a modal
   */
  const getModalComponent = (modalId: string) => {
    return store.getComponentRef(modalId)
  }

  /**
   * Get component data from a modal
   */
  const getModalComponentData = (modalId: string) => {
    return store.getComponentData(modalId)
  }

  /**
   * Call a method on a modal component
   */
  const callModalComponentMethod = (modalId: string, methodName: string, ...args: any[]) => {
    return store.callComponentMethod(modalId, methodName, ...args)
  }

  /**
   * Get a property value from a modal component
   */
  const getModalComponentProperty = (modalId: string, propertyName: string) => {
    return store.getComponentProperty(modalId, propertyName)
  }

  /**
   * Set a property value on a modal component
   */
  const setModalComponentProperty = (modalId: string, propertyName: string, value: any) => {
    return store.setComponentProperty(modalId, propertyName, value)
  }

  /**
   * Wait for modal component to be ready and then execute callback
   */
  const whenModalComponentReady = async (modalId: string, callback: (component: any) => void) => {
    store.whenComponentReady(modalId, callback)
  }

  /**
   * Create a reactive reference to a modal component property
   */
  const createModalPropertyRef = (modalId: string, propertyName: string): Ref<any> => {
    const propertyRef = ref(null)

    whenModalComponentReady(modalId, (component) => {
      // Set initial value
      propertyRef.value = getModalComponentProperty(modalId, propertyName)

      // Watch for changes if possible
      if (component.$watch) {
        component.$watch(propertyName, (newValue: any) => {
          propertyRef.value = newValue
        })
      }
    })

    return propertyRef
  }

  return {
    getModalComponent,
    getModalComponentData,
    callModalComponentMethod,
    getModalComponentProperty,
    setModalComponentProperty,
    whenModalComponentReady,
    createModalPropertyRef
  }
}

/**
 * Helper function to create a modal with enhanced component access
 */
export function createModalWithComponentAccess(options: any) {
  const { openModal } = useGlobalModal()
  const {
    getModalComponent,
    getModalComponentData,
    callModalComponentMethod,
    getModalComponentProperty,
    setModalComponentProperty,
    whenModalComponentReady
  } = useModalComponentAccess()

  const modal = openModal(options)

  return {
    ...modal,
    // Enhanced component access methods
    getComponent: () => getModalComponent(modal.id),
    getComponentData: () => getModalComponentData(modal.id),
    callMethod: (methodName: string, ...args: any[]) =>
      callModalComponentMethod(modal.id, methodName, ...args),
    getProperty: (propertyName: string) =>
      getModalComponentProperty(modal.id, propertyName),
    setProperty: (propertyName: string, value: any) =>
      setModalComponentProperty(modal.id, propertyName, value),
    whenReady: (callback: (component: any) => void) =>
      whenModalComponentReady(modal.id, callback)
  }
}
