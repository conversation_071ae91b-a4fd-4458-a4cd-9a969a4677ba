/**
 * Authentication Feature Module
 * 
 * This module exports all authentication-related functionality
 * following the barrel export pattern for clean imports.
 */

// Components
// export { default as LoginForm } from '~/components/auth/LoginForm.vue'
// export { default as Setup2FA } from '~/components/auth/Setup2FA.vue'
// export { default as AuthGuard } from '~/components/AuthGuard.vue'
// export { default as AuthLayout } from '~/components/AuthLayout.vue'

// Composables
export { useAuth } from '../../../composables/useAuth.js'

// Stores
export { useAuthStore } from '../../../stores/auth.js'

// Types
export type * from './types/api.js'
// Explicitly export from models, excluding conflicting names already in api.js
export type {
    User,
    PhoneNumber,
    PersonName,
    Address,
    UserProfile,
    SocialLinks,
    UserSecurity,
    TwoFactorMethod,
    SecurityQuestion,
    TrustedDevice,
    UserPreferences,
    LanguageCode,
    TimezoneCode,
    ThemeMode,
    AccessibilitySettings,
    // NotificationPreferences, // This conflicts, assume models.ts is canonical if different from ui.ts
    EmailNotificationSettings,
    PushNotificationSettings,
    SmsNotificationSettings,
    InAppNotificationSettings,
    NotificationFrequency,
    DigestFrequency,
    EmailNotificationType,
    PushNotificationType,
    SmsNotificationType,
    InAppNotificationType,
    QuietHours,
    DashboardPreferences,
    DashboardLayout,
    DisplayDensity,
    DashboardWidget,
    WidgetPosition,
    WidgetSize,
    PrivacySettings,
    ProfileVisibility,
    DataRetentionSettings,
    DataCategory,
    IntegrationSettings,
    CalendarIntegration,
    CalendarProvider,
    EmailIntegration,
    EmailProvider,
    EmailRule,
    EmailRuleCondition,
    EmailRuleAction,
    EmailField,
    EmailActionType,
    StorageIntegration,
    StorageProvider,
    ConflictResolution,
    ThirdPartyIntegration,
    UserTenantInfo,
    TenantMemberStatus,
    // AuthSession, // From api.js
    SessionTokens,
    JWTToken,
    JWTPayload,
    JWTHeader,
    RefreshToken,
    TokenType,
    SessionSecurity,
    GeoLocation,
    SessionContext,
    TenantContext,
    PermissionContext,
    PermissionRestriction,
    PermissionInheritance,
    PermissionSource,
    FeatureContext,
    SessionLimits,
    TimeRestriction,
    TenantLimits,
    TenantSettings,
    TenantSecuritySettings,
    PasswordPolicy,
    PasswordComplexity,
    TenantBranding,
    // BrandColors is too generic, might conflict. Assuming specific usage.
    BrandFonts,
    TenantIntegrations,
    SSOIntegration,
    SSOProvider,
    SSOConfig,
    AttributeMapping,
    DirectoryIntegration,
    DirectoryType,
    DirectoryConfig,
    BillingIntegration,
    BillingProvider,
    BillingConfig,
    ComplianceSettings,
    GDPRSettings,
    GDPRLawfulBasis,
    HIPAASettings,
    SOXSettings,
    CustomComplianceSettings,
    ComplianceRequirement,
    ComplianceType,
    ComplianceControl,
    SessionMetadata,
    DeviceInfo,
    DeviceType,
    OperatingSystem,
    BrowserInfo,
    ScreenInfo,
    ScreenOrientation,
    DeviceCapabilities,
    StorageCapabilities,
    ApplicationInfo,
    Environment,
    SessionFlags,
    // LoginCredentials, // From api.js
    RegisterData // Assuming this specific one from models is desired if different from api.js re-export
    // TwoFactorSetup, // From api.js
    // TwoFactorVerification // From api.js
} from './types/models.js'

// Explicitly export from ui, excluding NotificationPreferences if it's the conflicting one
export type {
    FormState,
    LoginFormData,
    LoginFormState,
    RegisterFormData,
    RegisterFormState,
    ChangePasswordFormData,
    ForgotPasswordFormData,
    ResetPasswordFormData,
    TwoFactorFormData,
    TwoFactorSetupData,
    PasswordStrength,
    ProfileFormData,
    // NotificationPreferences, // Exclude if this is the one from models.ts
    AuthLayoutProps,
    AuthFormProps,
    PasswordInputProps,
    TwoFactorInputProps,
    AuthModalState,
    AuthAlert,
    AuthAlertAction,
    AuthLoadingState,
    AuthErrorState,
    SessionUIState,
    SocialLoginProvider,
    SocialLoginState,
    AccountUIState,
    AuthNavigation,
    AuthNavigationStep,
    AuthResponsiveState,
    AuthTheme,
    AuthAccessibility,
    AuthFormEvents,
    ValidationEvents,
    AuthRedirect,
    AuthGuardState
} from './types/ui.js'

// Utils
export * from './utils/validators.js'
export * from './utils/formatters.js'
export * from './utils/helpers.js'

// Constants
export * from './constants/auth.js'
export * from './constants/roles.js'
