<!--
  Email Editor Wrapper Component
  
  Wrapper component to handle GrapesJS loading and error states
-->

<template>
  <div class="email-editor-wrapper h-full">
    <!-- Error State -->
    <div 
      v-if="hasError"
      class="flex items-center justify-center h-full bg-gray-50 dark:bg-gray-800"
    >
      <div class="text-center max-w-md">
        <Icon name="heroicons:exclamation-triangle" class="w-16 h-16 text-yellow-500 mx-auto mb-4" />
        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
          Editor Loading Error
        </h3>
        <p class="text-sm text-gray-600 dark:text-gray-400 mb-6">
          The email template editor failed to load. This might be due to browser compatibility or network issues.
        </p>
        <div class="space-y-3">
          <UiButton
            variant="primary"
            @click="retryLoad"
            :loading="isRetrying"
          >
            <Icon name="heroicons:arrow-path" class="w-4 h-4 mr-2" />
            Retry Loading
          </UiButton>
          <UiButton
            variant="ghost"
            @click="$emit('close')"
          >
            Go Back
          </UiButton>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div 
      v-else-if="isLoading"
      class="flex items-center justify-center h-full bg-gray-50 dark:bg-gray-800"
    >
      <div class="text-center">
        <UiSpinner size="lg" class="mb-4" />
        <p class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
          Loading Email Template Editor
        </p>
        <p class="text-sm text-gray-600 dark:text-gray-400">
          Initializing GrapesJS components...
        </p>
        <div class="mt-4 w-64 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
          <div 
            class="bg-brandPrimary-600 h-2 rounded-full transition-all duration-300"
            :style="{ width: `${loadingProgress}%` }"
          ></div>
        </div>
      </div>
    </div>

    <!-- Editor Component -->
    <EmailTemplateEditor
      v-else
      v-bind="$attrs"
      @close="$emit('close')"
      @save="$emit('save', $event)"
      @export="$emit('export', $event)"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onErrorCaptured } from 'vue'

// Define EmailTemplate interface locally to avoid import issues
interface EmailTemplate {
  id: string
  name: string
  description?: string
  category: string
  type: string
  subject: string
  content: {
    html: string
    css: string
    components: any[]
    assets: any[]
  }
  variables: Array<{
    name: string
    label: string
    type: string
    required: boolean
    defaultValue?: any
  }>
  settings: {
    responsive: boolean
    darkMode: boolean
    trackingEnabled: boolean
    unsubscribeLink: boolean
    socialLinks: boolean
  }
  metadata: {
    tags: string[]
    version: string
    lastModifiedBy: string
    usageCount: number
    isPublic: boolean
    isFavorite: boolean
  }
  status: string
  createdAt: string
  updatedAt: string
  createdBy: string
  tenantId: string
}

// Props
interface Props {
  template?: EmailTemplate | null
  height?: string
  readonly?: boolean
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  close: []
  save: [template: EmailTemplate]
  export: [content: string, format: 'html' | 'json']
}>()

// State
const isLoading = ref(true)
const hasError = ref(false)
const isRetrying = ref(false)
const loadingProgress = ref(0)

// Dynamic import of the actual editor
let EmailTemplateEditor: any = null

// Methods
const loadEditor = async () => {
  try {
    isLoading.value = true
    hasError.value = false
    loadingProgress.value = 0
    
    // Simulate loading progress
    const progressInterval = setInterval(() => {
      if (loadingProgress.value < 90) {
        loadingProgress.value += Math.random() * 20
      }
    }, 200)
    
    // Dynamic import with timeout
    const importPromise = import('~/components/email-templates/EmailTemplateEditor.vue')
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('Import timeout')), 10000)
    })
    
    const module = await Promise.race([importPromise, timeoutPromise])
    EmailTemplateEditor = module.default
    
    clearInterval(progressInterval)
    loadingProgress.value = 100
    
    // Small delay to show completion
    setTimeout(() => {
      isLoading.value = false
    }, 500)
    
  } catch (error) {
    console.error('Failed to load email editor:', error)
    hasError.value = true
    isLoading.value = false
  }
}

const retryLoad = async () => {
  isRetrying.value = true
  await loadEditor()
  isRetrying.value = false
}

// Error handling
onErrorCaptured((error) => {
  console.error('Email editor error:', error)
  hasError.value = true
  isLoading.value = false
  return false
})

// Lifecycle
onMounted(() => {
  loadEditor()
})
</script>

<style scoped>
.email-editor-wrapper {
  /* Ensure full height */
  min-height: 500px;
}

/* Loading animation */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
</style>
