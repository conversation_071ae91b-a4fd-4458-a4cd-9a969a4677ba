<template>
  <label class="flex items-center">
    <input type="checkbox" :checked="modelValue" @change="$emit('update:modelValue', $event.target.checked)" class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50" />
    <span class="ml-2">{{ label }}</span>
  </label>
</template>

<script setup lang="ts">
defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  label: {
    type: String,
    default: ''
  }
})
defineEmits(['update:modelValue'])
</script>