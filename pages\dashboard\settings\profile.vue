<template>
  <div class="p-6">


    <div class="mb-6 border-b border-gray-200 dark:border-gray-700">
      <nav class="-mb-px flex space-x-8" aria-label="Tabs">
        <button @click="activeTab = 'personal'" :class="[
          activeTab === 'personal'
            ? 'border-blue-500 text-blue-600'
            : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300',
          'whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm',
        ]">
          Personal Profile
        </button>
        <button @click="activeTab = 'security'" :class="[
          activeTab === 'security'
            ? 'border-blue-500 text-blue-600'
            : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300',
          'whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm',
        ]">
          Security
        </button>
        <button v-if="canManageTenant" @click="activeTab = 'tenant'" :class="[
          activeTab === 'tenant'
            ? 'border-blue-500 text-blue-600'
            : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300',
          'whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm',
        ]">
          Tenant Profile
        </button>
      </nav>
    </div>

    <div v-if="activeTab === 'personal'" class="bg-white shadow-md rounded-lg p-6">
      <h2 class="text-2xl font-semibold text-gray-900 mb-4">Your Personal Information</h2>
      <div v-if="isLoadingPersonal" class="text-center py-8">
        <UiSpinner class="inline-block h-8 w-8 text-blue-500" />
        <p class="text-gray-500 mt-2">Loading personal profile...</p>
      </div>
      <form v-else @submit.prevent="updatePersonalProfile" class="space-y-6">
        <div>
          <label for="firstName" class="block text-sm font-medium text-gray-700">Full Name</label>
          <UiInput name="fullName" id="fullName" v-model="editableUser.name" type="text" class="mt-1 block w-full" />
        </div>

        <div>
          <label for="email" class="block text-sm font-medium text-gray-700">Email Address</label>
          <UiInput name="email" id="email" v-model="editableUser.email" type="email" class="mt-1 block w-full"
            disabled />
          <p class="mt-2 text-sm text-gray-500">Email cannot be changed here.</p>
        </div>
        <div>
          <label for="phone" class="block text-sm font-medium text-gray-700">Phone Number</label>
          <UiInput name="phone" id="phone" v-model="editableUser.phone" type="tel" class="mt-1 block w-full" />
        </div>
        <div>
          <label for="address" class="block text-sm font-medium text-gray-700">Address</label>
          <UiInput name="address" id="address" v-model="editableUser.address" type="text" class="mt-1 block w-full" />
        </div>
        <UiButton type="submit" :disabled="isLoadingPersonal" variant="primary">
          {{ isLoadingPersonal ? 'Saving...' : 'Save Personal Changes' }}
        </UiButton>
      </form>
    </div>

    <div v-if="activeTab === 'security'" class="bg-white shadow-md rounded-lg p-6">
      <h2 class="text-2xl font-semibold text-gray-900 mb-4">Security Settings</h2>
      <div v-if="isLoadingSecurity" class="text-center py-8">
        <UiSpinner class="inline-block h-8 w-8 text-blue-500" />
        <p class="text-gray-500 mt-2">Loading security settings...</p>
      </div>
      <div v-else class="space-y-6">
        <div>
          <h3 class="text-xl font-medium text-gray-900 mb-2">Two-Factor Authentication (2FA)</h3>
          <p class="text-gray-700 mb-4">
            {{ otpEnabled ? '2FA is currently enabled for your account.' : 'Enhance your account security by enabling Two - Factor Authentication.' }}
          </p>
          <UiButton v-if="!otpEnabled" @click="enable2fa" :disabled="isLoadingSecurity" variant="primary">
            {{ isLoadingSecurity ? 'Enabling...' : 'Enable 2FA' }}
          </UiButton>
          <UiButton v-else @click="disable2fa" :disabled="isLoadingSecurity" variant="danger">
            {{ isLoadingSecurity ? 'Disabling...' : 'Disable 2FA' }}
          </UiButton>
        </div>

        <div class="border-t border-gray-200 pt-6">
          <h3 class="text-xl font-medium text-gray-900 mb-2">Change Password</h3>
          <p class="text-gray-700 mb-4">
            For security, please contact support or use the "Forgot Password" link on the login page to change your
            password.
            (Or integrate a dedicated change password form if backend supports it).
          </p>
        </div>
      </div>
    </div>

    <div v-if="activeTab === 'tenant' && canManageTenant" class="bg-white shadow-md rounded-lg p-6">
      <h2 class="text-2xl font-semibold text-gray-900 mb-4">Your Tenant Information</h2>
      <div v-if="isLoadingTenant" class="text-center py-8">
        <UiSpinner class="inline-block h-8 w-8 text-blue-500" />
        <p class="text-gray-500 mt-2">Loading tenant profile...</p>
      </div>
      <form v-else @submit.prevent="updateTenant" class="space-y-6">
        <div>
          <label for="tenantName" class="block text-sm font-medium text-gray-700">Tenant Name</label>
          <UiInput id="tenantName" v-model="editableTenant.name" type="text" class="mt-1 block w-full" />
        </div>
        <div>
          <label for="tenantIndustry" class="block text-sm font-medium text-gray-700">Industry</label>
          <UiInput id="tenantIndustry" v-model="editableTenant.industry" type="text" class="mt-1 block w-full" />
        </div>
        <div>
          <label for="tenantWebsite" class="block text-sm font-medium text-gray-700">Website</label>
          <UiInput id="tenantWebsite" v-model="editableTenant.website" type="url" class="mt-1 block w-full" />
        </div>
        <UiButton type="submit" :disabled="isLoadingTenant" variant="primary">
          {{ isLoadingTenant ? 'Saving...' : 'Save Tenant Changes' }}
        </UiButton>
      </form>
      <div v-else class="text-center py-8 text-gray-600">
        You do not have permission to view or manage tenant profile.
      </div>
    </div>

    <UiModal 
     title="Set up Two-Factor Authentication"
      subtitle="Set up Two-Factor Authentication for your account. This will require you to enter a 6-digit code from your app." 
      :show="show2faSetupModal"
      @close="show2faSetupModal = false" 
      @confirm="verify2fa" :disabled="is2faVerifying || twoFaToken.length !== 6"
      @cancel="show2faSetupModal = false" 
      cancelButtonText="Cancel" confirmButtonText="Verify & Enable 2FA">
     

      <template #default>
       
        <div class="flex flex-col items-center justify-center space-y-4 mb-6">
          <img :src="qrCodeSvg" alt="QR Code" class="p-2 border border-gray-200 rounded-md bg-white" />
          <p class="font-mono text-sm bg-gray-100 p-2 rounded-md">{{ secretKey }}</p>
          <p class="text-sm text-gray-600">Then, enter the 6-digit code from your app to verify:</p>
          <UiInput id="twoFaToken" name="twoFaToken" v-model="twoFaToken" type="text" placeholder="6-digit code"
            class="w-48 text-center" />
        </div>
        <p v-if="is2faVerifying" class="text-center text-sm text-blue-500 flex items-center justify-center">
          <UiSpinner class="inline-block h-4 w-4 mr-2" /> Verifying...
        </p>
      </template>
   
    </UiModal>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useAuthStore } from '~/stores/auth'; // Assuming Pinia store for auth
import { useToast } from '~/composables/useToast'; // Custom composable for toasts
import { useApi } from '~/composables/useApi'; // Custom composable for toasts
import type { User } from '~/types/auth'; // Assuming user type definition
import type { Tenant } from '~/types/tenant'; // Assuming tenant profile type definition
import { PlatformRoles, TenantRoles } from'~/app/features/auth/constants/roles';


definePageMeta({
  layout: 'dashboard',
  title: 'Profile Settings',
  middleware: ['rbac'],
  breadcrumb: [
    { label: 'Dashboard', href: '/dashboard' },
    { label: 'Settings', href: '/dashboard/settings' }, // Assuming /dashboard/settings is the parent settings page
    { label: 'Profile' },
  ],
});

const authStore = useAuthStore();
const { showToast } = useToast();
const { get, patch, post } = useApi();

const activeTab = ref<'personal' | 'security' | 'tenant'>('personal');
const isLoadingPersonal = ref<boolean>(false);
const isLoadingSecurity = ref<boolean>(false);
const isLoadingTenant = ref<boolean>(false);

// User Profile State
const userProfile = ref<User | null>(null);
const editableUser = ref<Partial<User>>({});

// 2FA State
const otpEnabled = ref<boolean>(false); // Renamed from is2faEnabled
const show2faSetupModal = ref<boolean>(false);
const qrCodeSvg = ref<string>('');
const secretKey = ref<string>('');
const twoFaToken = ref<string>('');
const is2faVerifying = ref<boolean>(false);

// Tenant Profile State
const tenantProfile = ref<Tenant | null>(null);
const editableTenant = ref<Partial<Tenant>>({});

// Computed property to check if current user has tenant admin/owner roles
const canManageTenant = computed(() => {
  const roles = authStore.user?.roles || [];
  return roles.includes('TENANT_OWNER') || roles.includes('ADMIN');
});

// --- Fetching Data ---
const fetchUserProfile = async () => {
  isLoadingPersonal.value = true;
  try {
    const response = await get<User>('/auth/me', {
      method: 'GET',
    });

    userProfile.value = response;
    editableUser.value = { ...response }; // Populate editable form
    otpEnabled.value = response?.otpEnabled || false;
  } catch (e) {
    console.error('Unexpected error fetching user profile:', e);

  } finally {
    isLoadingPersonal.value = false;
  }
};

const fetchTenant = async () => {
  if (!canManageTenant.value) return; // Only fetch if user has permission
  isLoadingTenant.value = true;
  try {
    const response = await get<Tenant>('/tenants/me/profile', {
      method: 'GET',
    });

    tenantProfile.value = response;
    editableTenant.value = { ...response }; // Populate editable form

  } catch (e) {
    console.error('Unexpected error fetching tenant profile:', e);

  } finally {
    isLoadingTenant.value = false;
  }
};

// --- Update Actions ---
const updatePersonalProfile = async () => {
  isLoadingPersonal.value = true;
  try {
    const userId = userProfile.value?.id;
    if (!userId) {
      showToast({
        title: 'User ID is missing. Please try again.',
        type: 'error',
      });
      return;
    }
    const { name, email, phone, address, otpEnabled } = editableUser.value

    await patch(`/users/${userId}`, { name, email, phone, address, otpEnabled });
    showToast({
      title: 'Personal profile updated successfully!',
      type: 'success',
    });
    await fetchUserProfile(); // Re-fetch to sync UI
  } catch (e) {
    console.error('Unexpected error updating personal profile:', e);

  } finally {
    isLoadingPersonal.value = false;
  }
};

const updateTenant = async () => {
  isLoadingTenant.value = true;
  try {
    // Note: PATCH /tenants/me/plan is for plan. For general profile update,
    // assuming backend allows PATCH /tenants/me/profile or similar.
    // The prompt only explicitly mentions PATCH /tenants/me/plan for Tenant Owners.
    // If other fields need to be updated, we might need a different backend endpoint.
    // For now, let's assume `PATCH /tenants/me/profile` is available for simplicity.
    // const {  name, email, phone, address, otpEnabled } = editableTenant.value
    await patch(`/tenants/me/profile`, editableTenant.value);
    showToast({
      title: 'Tenant profile updated successfully!',
      type: 'success',
    });
    await fetchTenant(); // Re-fetch to sync UI
  } catch (e) {
    console.error('Unexpected error updating tenant profile:', e);
  } finally {
    isLoadingTenant.value = false;
  }
};

// --- 2FA Actions ---
const enable2fa = async () => {
  isLoadingSecurity.value = true;
  try {
    const response = await post<{ qrCodeDataUrl: string; secret: string }>('/auth/2fa/enable', {});
    qrCodeSvg.value = response.qrCodeDataUrl;
    secretKey.value = response.secret;
    show2faSetupModal.value = true;
    showToast({
      title: '2FA setup initiated successfully!',
      type: 'success',
    })
  } catch (e) {
    console.error('Unexpected error enabling 2FA:', e);
  } finally {
    isLoadingSecurity.value = false;
  }
};

const verify2fa = async () => {
  is2faVerifying.value = true;
  try {
    await post('/auth/2fa/verify', { token: twoFaToken.value });
    showToast({
      title: '2FA verified successfully!',
      type: 'success',
    });
    otpEnabled.value = true; // Renamed from is2faEnabled
    show2faSetupModal.value = false;
    twoFaToken.value = ''; // Clear token
    qrCodeSvg.value = '';
    secretKey.value = '';
    await fetchUserProfile(); // Refresh user status
  } catch (e) {
    console.error('Unexpected error verifying 2FA:', e);

  } finally {
    is2faVerifying.value = false;
  }
};

const disable2fa = async () => {
  if (confirm('Are you sure you want to disable Two-Factor Authentication?')) {
    isLoadingSecurity.value = true;
    try {
      await post('/auth/2fa/disable', {});
      showToast({
        title: '2FA disabled successfully!',
        type: 'info',
      });
      otpEnabled.value = false; // Renamed from is2faEnabled
      await fetchUserProfile(); // Refresh user status
    } catch (e) {
      console.error('Unexpected error disabling 2FA:', e);
    } finally {
      isLoadingSecurity.value = false;
    }
  }
};


// --- Lifecycle Hooks ---
onMounted(() => {
  fetchUserProfile();
  if (canManageTenant.value) {
    fetchTenant();
  }
});

// Watch for changes in activeTab to trigger data fetching if needed
watch(activeTab, (newTab) => {
  if (newTab === 'personal' && !userProfile.value) {
    fetchUserProfile();
  } else if (newTab === 'tenant' && !tenantProfile.value && canManageTenant.value) { // Changed Tenant.value to tenantProfile.value
    fetchTenant();
  }
});
</script>