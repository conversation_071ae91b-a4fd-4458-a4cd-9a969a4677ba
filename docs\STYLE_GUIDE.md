# Legal SaaS Frontend Style Guide

## 🎯 Overview

This comprehensive style guide ensures consistency, maintainability, and scalability across the Legal SaaS Frontend codebase. It covers TypeScript, Vue 3, CSS, and architectural patterns.

## 📁 File and Directory Naming

### Directory Structure
```
app/
├── features/           # Feature-based modules (kebab-case)
│   ├── auth/          # Authentication feature
│   ├── case-management/  # Case management feature
│   └── document-management/  # Document management feature
├── shared/            # Shared application code
│   ├── components/    # Reusable UI components (PascalCase)
│   ├── composables/   # Shared composables (camelCase)
│   └── types/         # Global types (camelCase)
└── core/              # Core system layer
```

### File Naming Conventions
- **Components**: `PascalCase.vue` (e.g., `UserProfile.vue`)
- **Composables**: `camelCase.ts` (e.g., `useUserAuth.ts`)
- **Types**: `camelCase.ts` (e.g., `userTypes.ts`)
- **Stores**: `camelCase.ts` (e.g., `userStore.ts`)
- **Utils**: `camelCase.ts` (e.g., `dateUtils.ts`)
- **Constants**: `SCREAMING_SNAKE_CASE.ts` (e.g., `API_ENDPOINTS.ts`)

## 🔧 TypeScript Guidelines

### Type Definitions
```typescript
// ✅ Good - Use branded types for IDs
export type UserId = Brand<string, 'UserId'>
export type CaseId = Brand<string, 'CaseId'>

// ✅ Good - Use descriptive interface names
export interface UserProfile {
  id: UserId
  name: PersonName
  email: EmailAddress
  preferences: UserPreferences
}

// ✅ Good - Use union types for status
export type CaseStatus = 'draft' | 'open' | 'in_progress' | 'closed'

// ❌ Bad - Generic string types
export interface User {
  id: string // Could be any string
  status: string // No type safety
}
```

### Function Signatures
```typescript
// ✅ Good - Explicit return types
export function calculateBillableHours(
  timeEntries: TimeEntry[]
): BillableHoursResult {
  // Implementation
}

// ✅ Good - Use generics for reusable functions
export function createApiClient<T extends ApiEndpoints>(
  endpoints: T
): TypedApiClient<T> {
  // Implementation
}

// ❌ Bad - Implicit any return type
export function processData(data) {
  // Implementation
}
```

### Error Handling
```typescript
// ✅ Good - Typed error handling
export class ValidationError extends Error {
  constructor(
    public field: string,
    public code: string,
    message: string
  ) {
    super(message)
    this.name = 'ValidationError'
  }
}

// ✅ Good - Result pattern for error handling
export type Result<T, E = Error> = 
  | { success: true; data: T }
  | { success: false; error: E }

export async function fetchUser(id: UserId): Promise<Result<User>> {
  try {
    const user = await api.get(`/users/${id}`)
    return { success: true, data: user }
  } catch (error) {
    return { success: false, error }
  }
}
```

## 🎨 Vue 3 Component Guidelines

### Component Structure
```vue
<!-- ✅ Good - Consistent component structure -->
<template>
  <div class="user-profile">
    <header class="user-profile__header">
      <h1 class="user-profile__title">{{ user.name.display }}</h1>
    </header>
    
    <main class="user-profile__content">
      <!-- Component content -->
    </main>
  </div>
</template>

<script setup lang="ts">
// 1. Imports (external libraries first, then internal)
import { computed, ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@features/auth'
import type { User, UserPreferences } from '@features/auth/types'

// 2. Props and emits
interface Props {
  userId: UserId
  editable?: boolean
}

interface Emits {
  update: [user: User]
  error: [error: Error]
}

const props = withDefaults(defineProps<Props>(), {
  editable: false
})

const emit = defineEmits<Emits>()

// 3. Composables and stores
const router = useRouter()
const userStore = useUserStore()

// 4. Reactive state
const isLoading = ref(false)
const error = ref<Error | null>(null)

// 5. Computed properties
const user = computed(() => userStore.getUserById(props.userId))
const canEdit = computed(() => props.editable && userStore.canEditUser(props.userId))

// 6. Methods
const handleSave = async (userData: Partial<User>) => {
  try {
    isLoading.value = true
    const updatedUser = await userStore.updateUser(props.userId, userData)
    emit('update', updatedUser)
  } catch (err) {
    error.value = err instanceof Error ? err : new Error('Update failed')
    emit('error', error.value)
  } finally {
    isLoading.value = false
  }
}

// 7. Watchers
watch(() => props.userId, (newId) => {
  if (newId) {
    userStore.fetchUser(newId)
  }
}, { immediate: true })

// 8. Lifecycle hooks
onMounted(() => {
  // Component initialization
})
</script>

<style scoped>
/* Component-specific styles */
.user-profile {
  @apply max-w-4xl mx-auto p-6;
}

.user-profile__header {
  @apply mb-6 border-b border-gray-200 pb-4;
}

.user-profile__title {
  @apply text-2xl font-bold text-gray-900;
}

.user-profile__content {
  @apply space-y-6;
}
</style>
```

### Component Props
```typescript
// ✅ Good - Typed props with defaults
interface ButtonProps {
  variant?: 'primary' | 'secondary' | 'danger'
  size?: 'sm' | 'md' | 'lg'
  disabled?: boolean
  loading?: boolean
  icon?: string
}

const props = withDefaults(defineProps<ButtonProps>(), {
  variant: 'primary',
  size: 'md',
  disabled: false,
  loading: false
})

// ❌ Bad - Untyped props
const props = defineProps({
  variant: String,
  size: String,
  disabled: Boolean
})
```

### Event Handling
```typescript
// ✅ Good - Typed events
interface Emits {
  click: [event: MouseEvent]
  submit: [data: FormData]
  error: [error: Error]
}

const emit = defineEmits<Emits>()

// ✅ Good - Descriptive event handlers
const handleFormSubmit = (event: Event) => {
  event.preventDefault()
  const formData = new FormData(event.target as HTMLFormElement)
  emit('submit', formData)
}

// ❌ Bad - Generic event handlers
const onClick = (e: any) => {
  emit('click', e)
}
```

## 🎨 CSS and Styling Guidelines

### CSS Architecture
```css
/* ✅ Good - BEM methodology */
.case-card {
  @apply bg-white rounded-lg shadow-sm border border-gray-200;
}

.case-card__header {
  @apply p-4 border-b border-gray-100;
}

.case-card__title {
  @apply text-lg font-semibold text-gray-900;
}

.case-card__status {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.case-card__status--open {
  @apply bg-green-100 text-green-800;
}

.case-card__status--closed {
  @apply bg-gray-100 text-gray-800;
}

/* ❌ Bad - Nested selectors and unclear naming */
.card {
  .header {
    .title {
      color: black;
    }
  }
}
```

### Tailwind CSS Usage
```vue
<!-- ✅ Good - Semantic class grouping -->
<template>
  <div class="
    /* Layout */
    flex items-center justify-between
    /* Spacing */
    p-4 mb-6
    /* Appearance */
    bg-white rounded-lg shadow-sm
    /* Borders */
    border border-gray-200
    /* Responsive */
    sm:p-6 lg:p-8
  ">
    <h2 class="
      /* Typography */
      text-xl font-semibold text-gray-900
      /* Responsive */
      sm:text-2xl
    ">
      Case Details
    </h2>
  </div>
</template>

<!-- ❌ Bad - Long unorganized class strings -->
<template>
  <div class="flex items-center justify-between p-4 mb-6 bg-white rounded-lg shadow-sm border border-gray-200 sm:p-6 lg:p-8">
    <h2 class="text-xl font-semibold text-gray-900 sm:text-2xl">Case Details</h2>
  </div>
</template>
```

### Custom CSS Properties
```css
/* ✅ Good - CSS custom properties for theming */
:root {
  /* Colors */
  --color-primary: #3b82f6;
  --color-primary-dark: #1d4ed8;
  --color-secondary: #6b7280;
  
  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  
  /* Typography */
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
}

/* Usage */
.custom-button {
  background-color: var(--color-primary);
  padding: var(--spacing-md) var(--spacing-lg);
  font-size: var(--font-size-base);
  box-shadow: var(--shadow-sm);
}
```

## 🏗️ Architecture Patterns

### Composables Pattern
```typescript
// ✅ Good - Single responsibility composables
export function useUserAuth() {
  const user = ref<User | null>(null)
  const isAuthenticated = computed(() => user.value !== null)
  
  const login = async (credentials: LoginCredentials) => {
    // Login logic
  }
  
  const logout = async () => {
    // Logout logic
  }
  
  return {
    user: readonly(user),
    isAuthenticated,
    login,
    logout
  }
}

// ✅ Good - Composable composition
export function useUserProfile() {
  const { user, isAuthenticated } = useUserAuth()
  const { updateProfile, uploadAvatar } = useUserApi()
  
  const profile = computed(() => user.value?.profile)
  
  return {
    profile,
    isAuthenticated,
    updateProfile,
    uploadAvatar
  }
}
```

### Store Pattern
```typescript
// ✅ Good - Feature-based store
export const useCaseStore = defineStore('cases', () => {
  // State
  const cases = ref<Case[]>([])
  const selectedCase = ref<Case | null>(null)
  const isLoading = ref(false)
  
  // Getters
  const openCases = computed(() => 
    cases.value.filter(c => c.status === 'open')
  )
  
  const getCaseById = computed(() => (id: CaseId) =>
    cases.value.find(c => c.id === id)
  )
  
  // Actions
  const fetchCases = async (filters?: CaseFilters) => {
    isLoading.value = true
    try {
      const response = await caseApi.list(filters)
      cases.value = response.data
    } finally {
      isLoading.value = false
    }
  }
  
  const createCase = async (data: CaseCreateData) => {
    const response = await caseApi.create(data)
    cases.value.push(response.data)
    return response.data
  }
  
  return {
    // State
    cases: readonly(cases),
    selectedCase: readonly(selectedCase),
    isLoading: readonly(isLoading),
    
    // Getters
    openCases,
    getCaseById,
    
    // Actions
    fetchCases,
    createCase
  }
})
```

## 📝 Documentation Standards

### Component Documentation
```vue
<!--
  UserProfileCard Component
  
  A reusable card component for displaying user profile information
  with optional editing capabilities.
  
  @example
  <UserProfileCard
    :user="currentUser"
    :editable="canEdit"
    @update="handleUserUpdate"
  />
  
  @props
  - user: User - The user object to display
  - editable: boolean - Whether the profile can be edited
  - compact: boolean - Whether to show a compact version
  
  @emits
  - update: (user: User) - Emitted when user data is updated
  - error: (error: Error) - Emitted when an error occurs
  
  @slots
  - actions: Custom actions for the profile card
  - avatar: Custom avatar component
-->
<template>
  <!-- Component template -->
</template>
```

### Function Documentation
```typescript
/**
 * Calculates billable hours for a given time period
 * 
 * @param timeEntries - Array of time entries to calculate from
 * @param options - Configuration options for calculation
 * @param options.includeNonBillable - Whether to include non-billable hours
 * @param options.roundingMode - How to round fractional hours
 * @returns Object containing billable hours breakdown
 * 
 * @example
 * ```typescript
 * const result = calculateBillableHours(entries, {
 *   includeNonBillable: false,
 *   roundingMode: 'up'
 * })
 * console.log(result.totalHours) // 42.5
 * ```
 */
export function calculateBillableHours(
  timeEntries: TimeEntry[],
  options: BillingOptions = {}
): BillableHoursResult {
  // Implementation
}
```

## 🧪 Testing Guidelines

### Component Testing
```typescript
// ✅ Good - Comprehensive component test
import { describe, it, expect, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import UserProfileCard from './UserProfileCard.vue'
import type { User } from '@features/auth/types'

describe('UserProfileCard', () => {
  const mockUser: User = {
    id: 'user-1' as UserId,
    name: { display: 'John Doe', first: 'John', last: 'Doe' },
    email: '<EMAIL>' as EmailAddress,
    // ... other properties
  }

  it('renders user information correctly', () => {
    const wrapper = mount(UserProfileCard, {
      props: { user: mockUser }
    })
    
    expect(wrapper.text()).toContain('John Doe')
    expect(wrapper.text()).toContain('<EMAIL>')
  })

  it('emits update event when profile is saved', async () => {
    const wrapper = mount(UserProfileCard, {
      props: { user: mockUser, editable: true }
    })
    
    await wrapper.find('[data-testid="save-button"]').trigger('click')
    
    expect(wrapper.emitted('update')).toBeTruthy()
    expect(wrapper.emitted('update')?.[0]).toEqual([mockUser])
  })

  it('handles errors gracefully', async () => {
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
    
    const wrapper = mount(UserProfileCard, {
      props: { user: null } // Invalid prop
    })
    
    expect(wrapper.find('[data-testid="error-message"]').exists()).toBe(true)
    
    consoleSpy.mockRestore()
  })
})
```

### Composable Testing
```typescript
// ✅ Good - Composable test
import { describe, it, expect, vi } from 'vitest'
import { useUserAuth } from './useUserAuth'

describe('useUserAuth', () => {
  it('initializes with no user', () => {
    const { user, isAuthenticated } = useUserAuth()
    
    expect(user.value).toBeNull()
    expect(isAuthenticated.value).toBe(false)
  })

  it('authenticates user successfully', async () => {
    const { login, user, isAuthenticated } = useUserAuth()
    
    const credentials = {
      email: '<EMAIL>',
      password: 'password123'
    }
    
    await login(credentials)
    
    expect(user.value).toBeTruthy()
    expect(isAuthenticated.value).toBe(true)
  })
})
```

## 🚀 Performance Guidelines

### Code Splitting
```typescript
// ✅ Good - Route-based code splitting
const routes = [
  {
    path: '/cases',
    component: () => import('@features/cases/pages/CasesList.vue')
  },
  {
    path: '/documents',
    component: () => import('@features/documents/pages/DocumentsList.vue')
  }
]

// ✅ Good - Component lazy loading
const LazyModal = defineAsyncComponent(() => 
  import('@shared/components/ui/UiModal.vue')
)
```

### Reactive Performance
```typescript
// ✅ Good - Use shallowRef for large objects
const largeDataSet = shallowRef<LargeObject[]>([])

// ✅ Good - Use readonly for computed that don't need reactivity
const processedData = readonly(computed(() => 
  expensiveProcessing(largeDataSet.value)
))

// ✅ Good - Debounce expensive operations
const debouncedSearch = useDebounceFn((query: string) => {
  performSearch(query)
}, 300)
```

## 🔒 Security Guidelines

### Input Validation
```typescript
// ✅ Good - Validate all inputs
import { z } from 'zod'

const UserInputSchema = z.object({
  name: z.string().min(1).max(100),
  email: z.string().email(),
  age: z.number().min(18).max(120)
})

export function validateUserInput(input: unknown) {
  return UserInputSchema.safeParse(input)
}
```

### XSS Prevention
```vue
<!-- ✅ Good - Sanitized HTML -->
<template>
  <div v-html="sanitizedContent"></div>
</template>

<script setup lang="ts">
import DOMPurify from 'dompurify'

const props = defineProps<{
  content: string
}>()

const sanitizedContent = computed(() => 
  DOMPurify.sanitize(props.content)
)
</script>
```

## 📋 Code Review Checklist

### Before Submitting
- [ ] All TypeScript errors resolved
- [ ] ESLint warnings addressed
- [ ] Tests written and passing
- [ ] Documentation updated
- [ ] Performance impact considered
- [ ] Security implications reviewed
- [ ] Accessibility requirements met
- [ ] Browser compatibility verified

### Review Criteria
- [ ] Code follows established patterns
- [ ] Proper error handling implemented
- [ ] Type safety maintained
- [ ] Performance optimizations applied
- [ ] Security best practices followed
- [ ] Tests provide adequate coverage
- [ ] Documentation is clear and complete

This style guide ensures consistency and quality across the Legal SaaS Frontend codebase while promoting best practices for maintainability and scalability.
