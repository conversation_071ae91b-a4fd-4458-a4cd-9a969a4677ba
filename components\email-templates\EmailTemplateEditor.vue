<!--
  Email Template Editor Component
  
  Advanced email template editor using GrapesJS with platform theme integration
-->

<template>
  <div class="email-template-editor h-full flex flex-col bg-white dark:bg-gray-900">
    <!-- Editor Toolbar -->
    <div class="editor-toolbar flex items-center justify-between px-4 py-3 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
      <!-- Left Actions -->
      <div class="flex items-center space-x-3">
        <UiButton
          variant="ghost"
          size="sm"
          @click="$emit('close')"
          class="text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-100"
        >
          <Icon name="heroicons:arrow-left" class="w-4 h-4 mr-2" />
          Back
        </UiButton>
        
        <div class="h-6 w-px bg-gray-300 dark:bg-gray-600"></div>
        
        <div class="flex items-center space-x-2">
          <UiButton
            variant="ghost"
            size="sm"
            @click="undoAction"
            :disabled="!canUndo"
            class="text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-100"
          >
            <Icon name="heroicons:arrow-uturn-left" class="w-4 h-4" />
          </UiButton>
          
          <UiButton
            variant="ghost"
            size="sm"
            @click="redoAction"
            :disabled="!canRedo"
            class="text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-100"
          >
            <Icon name="heroicons:arrow-uturn-right" class="w-4 h-4" />
          </UiButton>
        </div>
      </div>

      <!-- Center - Template Info -->
      <div class="flex-1 flex items-center justify-center">
        <div class="text-center">
          <h2 class="text-sm font-medium text-gray-900 dark:text-gray-100">
            {{ templateName || 'Untitled Template' }}
          </h2>
          <p class="text-xs text-gray-500 dark:text-gray-400">
            {{ lastSaved ? `Saved ${formatTime(lastSaved)}` : 'Not saved' }}
          </p>
        </div>
      </div>

      <!-- Right Actions -->
      <div class="flex items-center space-x-3">
        <!-- Device Preview -->
        <div class="flex items-center space-x-1 bg-white dark:bg-gray-700 rounded-lg p-1 border border-gray-200 dark:border-gray-600">
          <UiButton
            v-for="device in devices"
            :key="device.id"
            variant="ghost"
            size="xs"
            :class="[
              'px-2 py-1',
              activeDevice === device.id 
                ? 'bg-brandPrimary-100 text-brandPrimary-700 dark:bg-brandPrimary-900 dark:text-brandPrimary-300' 
                : 'text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-100'
            ]"
            @click="setDevice(device.id)"
          >
            <Icon :name="device.icon" class="w-3 h-3" />
          </UiButton>
        </div>

        <div class="h-6 w-px bg-gray-300 dark:bg-gray-600"></div>

        <!-- Preview Toggle -->
        <UiButton
          variant="ghost"
          size="sm"
          @click="togglePreview"
          :class="[
            isPreviewMode 
              ? 'bg-brandPrimary-100 text-brandPrimary-700 dark:bg-brandPrimary-900 dark:text-brandPrimary-300' 
              : 'text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-100'
          ]"
        >
          <Icon name="heroicons:eye" class="w-4 h-4 mr-2" />
          Preview
        </UiButton>

        <!-- Save Button -->
        <UiButton
          variant="primary"
          size="sm"
          @click="saveTemplate"
          :loading="isSaving"
          :disabled="!canSave"
        >
          <Icon name="heroicons:document-arrow-down" class="w-4 h-4 mr-2" />
          Save
        </UiButton>

        <!-- More Actions -->
        <UiDropdown>
          <template #trigger>
            <UiButton
              variant="ghost"
              size="sm"
              class="text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-100"
            >
              <Icon name="heroicons:ellipsis-vertical" class="w-4 h-4" />
            </UiButton>
          </template>
          
          <template #content>
            <div class="py-1">
              <button
                @click="exportTemplate('html')"
                class="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-800"
              >
                <Icon name="heroicons:code-bracket" class="w-4 h-4 mr-3" />
                Export HTML
              </button>
              <button
                @click="exportTemplate('json')"
                class="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-800"
              >
                <Icon name="heroicons:document-text" class="w-4 h-4 mr-3" />
                Export JSON
              </button>
              <div class="border-t border-gray-100 dark:border-gray-700 my-1"></div>
              <button
                @click="resetTemplate"
                class="flex items-center w-full px-4 py-2 text-sm text-red-700 hover:bg-red-50 dark:text-red-400 dark:hover:bg-red-900/20"
              >
                <Icon name="heroicons:arrow-path" class="w-4 h-4 mr-3" />
                Reset Template
              </button>
            </div>
          </template>
        </UiDropdown>
      </div>
    </div>

    <!-- Editor Container -->
    <div class="flex-1 flex overflow-hidden">
      <!-- Blocks Panel -->
      <div
        v-show="!isPreviewMode"
        class="w-64 bg-white dark:bg-gray-900 border-r border-gray-200 dark:border-gray-700 overflow-y-auto"
      >
        <div class="p-4">
          <div class="mb-4">
            <h3 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
              Email Blocks
            </h3>
            <p class="text-xs text-gray-600 dark:text-gray-400 mb-3">
              Drag blocks to the canvas to build your email template
            </p>
          </div>

          <!-- Quick Tip -->
          <div class="mb-4 p-3 bg-brandPrimary-50 dark:bg-brandPrimary-900/20 rounded-lg border border-brandPrimary-200 dark:border-brandPrimary-800">
            <div class="flex items-start">
              <Icon name="heroicons:light-bulb" class="w-4 h-4 text-brandPrimary-600 dark:text-brandPrimary-400 mt-0.5 mr-2 flex-shrink-0" />
              <div>
                <p class="text-xs font-medium text-brandPrimary-800 dark:text-brandPrimary-200 mb-1">
                  Legal Blocks Available
                </p>
                <p class="text-xs text-brandPrimary-700 dark:text-brandPrimary-300">
                  Look for the "Legal" category with law firm specific components
                </p>
              </div>
            </div>
          </div>

          <div id="blocks-container" class="gjs-blocks-container"></div>
        </div>
      </div>

      <!-- Main Editor Canvas -->
      <div class="flex-1 flex flex-col">
        <!-- Canvas -->
        <div class="flex-1 relative">
          <div 
            id="gjs-editor"
            class="h-full w-full"
            :class="{ 'opacity-0': !isEditorReady }"
          ></div>
          
          <!-- Loading Overlay -->
          <div
            v-if="!isEditorReady"
            class="absolute inset-0 flex items-center justify-center bg-white dark:bg-gray-900 z-10"
          >
            <div class="text-center">
              <UiSpinner size="lg" class="mb-4" />
              <p class="text-sm text-gray-600 dark:text-gray-400">
                Loading email template editor...
              </p>
              <p class="text-xs text-gray-500 dark:text-gray-500 mt-2">
                This may take a few seconds
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- Properties Panel -->
      <div 
        v-show="!isPreviewMode"
        class="w-64 bg-white dark:bg-gray-900 border-l border-gray-200 dark:border-gray-700 overflow-y-auto"
      >
        <div class="p-4">
          <!-- Style Manager -->
          <div class="mb-6">
            <h3 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3">
              Styles
            </h3>
            <div id="styles-container" class="gjs-styles-container"></div>
          </div>

          <!-- Traits Manager -->
          <div class="mb-6">
            <h3 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3">
              Settings
            </h3>
            <div id="traits-container" class="gjs-traits-container"></div>
          </div>

          <!-- Layers Manager -->
          <div>
            <h3 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3">
              Layers
            </h3>
            <div id="layers-container" class="gjs-layers-container"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed, watch } from 'vue'
import { useEmailTemplateStore } from '~/stores/emailTemplate'
import type { EmailTemplate, EmailEditorConfig } from '~/app/shared/types'

// Props
interface Props {
  template?: EmailTemplate | null
  height?: string
  readonly?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  template: null,
  height: '100%',
  readonly: false
})

// Emits
const emit = defineEmits<{
  close: []
  save: [template: EmailTemplate]
  export: [content: string, format: 'html' | 'json']
}>()

// Store
const emailTemplateStore = useEmailTemplateStore()

// State
const isEditorReady = ref(false)
const editorInstance = ref<any>(null)
const canUndo = ref(false)
const canRedo = ref(false)
const activeDevice = ref('desktop')
const lastSaved = ref<Date | null>(null)

// Computed
const templateName = computed(() => props.template?.name || emailTemplateStore.currentTemplate?.name)
const isSaving = computed(() => emailTemplateStore.isSaving)
const isPreviewMode = computed(() => emailTemplateStore.isPreviewMode)
const canSave = computed(() => emailTemplateStore.canSaveTemplate && !props.readonly)

// Device configurations
const devices = [
  { id: 'desktop', name: 'Desktop', icon: 'heroicons:computer-desktop', width: '100%' },
  { id: 'tablet', name: 'Tablet', icon: 'heroicons:device-tablet', width: '768px' },
  { id: 'mobile', name: 'Mobile', icon: 'heroicons:device-phone-mobile', width: '375px' }
]

// Methods
const initializeEditor = async () => {
  try {
    // Dynamic import to avoid SSR issues
    const { default: grapesjs } = await import('grapesjs')
    const { default: newsletter } = await import('grapesjs-preset-newsletter')
    const { default: webpage } = await import('grapesjs-preset-webpage')
    const { default: basicBlocks } = await import('grapesjs-blocks-basic')
    const { default: forms } = await import('grapesjs-plugin-forms')
    const { registerLegalBlocks } = await import('~/components/email-templates/blocks/LegalBlocks')
    
    // Editor configuration
    const config: Partial<EmailEditorConfig> = {
      container: '#gjs-editor',
      height: props.height,
      width: '100%',

      // Disable problematic features to reduce warnings
      avoidInlineStyle: true,
      avoidFrameOffset: true,

      // Storage configuration
      storageManager: {
        type: 'local', // Use local storage to avoid remote calls
        autosave: false, // Disable autosave to reduce document.write issues
        autoload: false,
        stepsBeforeSave: 5
      },

      // Asset manager - simplified
      assetManager: {
        upload: false, // Disable upload to avoid document.write
        multiUpload: false,
        autoAdd: false
      },
      
      // Plugins
      plugins: [newsletter, webpage, basicBlocks, forms],
      pluginsOpts: {
        [newsletter]: {
          modalImportTitle: 'Import Template',
          modalImportLabel: '<div>Paste your HTML/CSS here</div>',
          modalImportContent: (editor: any) => editor.getHtml() + '<style>' + editor.getCss() + '</style>',
          importPlaceholder: '<table class="main-table">...</table>',
          cellHeight: 75,
          tableWidth: 900,
          categories: ['Basic', 'Layout', 'Content', 'Media']
        },
        [basicBlocks]: {
          blocks: ['column1', 'column2', 'column3', 'text', 'link', 'image', 'video'],
          flexGrid: true,
          stylePrefix: 'gjs-',
          addBasicStyle: true,
          category: 'Basic'
        },
        [forms]: {
          blocks: ['form', 'input', 'textarea', 'select', 'button', 'label', 'checkbox', 'radio']
        }
      },
      
      // Canvas configuration
      canvas: {
        styles: [],
        scripts: []
      },
      
      // Panels configuration
      panels: {
        defaults: [
          {
            id: 'basic-actions',
            el: '.panel__basic-actions',
            buttons: [
              {
                id: 'visibility',
                active: true,
                className: 'btn-toggle-borders',
                label: '<i class="fa fa-clone"></i>',
                command: 'sw-visibility'
              }
            ]
          }
        ]
      },
      
      // Block manager
      blockManager: {
        appendTo: '#blocks-container',
        blocks: []
      },
      
      // Style manager
      styleManager: {
        appendTo: '#styles-container',
        sectors: [
          {
            name: 'General',
            open: false,
            buildProps: ['float', 'display', 'position', 'top', 'right', 'left', 'bottom']
          },
          {
            name: 'Layout',
            open: false,
            buildProps: ['width', 'height', 'max-width', 'min-height', 'margin', 'padding']
          },
          {
            name: 'Typography',
            open: false,
            buildProps: ['font-family', 'font-size', 'font-weight', 'letter-spacing', 'color', 'line-height', 'text-align', 'text-decoration', 'text-shadow']
          },
          {
            name: 'Background',
            open: false,
            buildProps: ['background-color', 'background-image', 'background-repeat', 'background-position', 'background-attachment', 'background-size']
          },
          {
            name: 'Border',
            open: false,
            buildProps: ['border', 'border-radius', 'box-shadow']
          }
        ]
      },
      
      // Device manager
      deviceManager: {
        devices: devices.map(device => ({
          name: device.name,
          width: device.width,
          widthMedia: device.id === 'mobile' ? '480px' : device.id === 'tablet' ? '810px' : ''
        }))
      },
      
      // Traits manager
      traitManager: {
        appendTo: '#traits-container'
      },
      
      // Layers manager
      layerManager: {
        appendTo: '#layers-container'
      }
    }
    
    // Initialize editor
    editorInstance.value = grapesjs.init(config)

    // Register legal blocks
    registerLegalBlocks(editorInstance.value)

    // Add canvas styles properly
    const canvasStyles = `
      body {
        margin: 0;
        padding: 20px;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        background: #ffffff;
        color: #374151;
        line-height: 1.6;
      }
      * {
        box-sizing: border-box;
      }
      h1, h2, h3, h4, h5, h6 {
        margin-top: 0;
        margin-bottom: 0.5em;
        font-weight: 600;
      }
      p {
        margin-top: 0;
        margin-bottom: 1em;
      }
    `

    // Add styles to canvas
    editorInstance.value.setStyle(canvasStyles)

    // Set up event listeners
    setupEditorEvents()

    // Load template if provided
    if (props.template) {
      loadTemplate(props.template)
    }

    // Wait for editor to be fully loaded
    editorInstance.value.on('load', () => {
      // Mark editor as ready
      isEditorReady.value = true
      emailTemplateStore.setEditorInstance(editorInstance.value)
    })

    // Fallback timeout in case load event doesn't fire
    setTimeout(() => {
      if (!isEditorReady.value) {
        isEditorReady.value = true
        emailTemplateStore.setEditorInstance(editorInstance.value)
      }
    }, 2000)

  } catch (error) {
    console.error('Failed to initialize email editor:', error)
    // Still mark as ready to show error state
    isEditorReady.value = true
  }
}

const setupEditorEvents = () => {
  if (!editorInstance.value) return

  const editor = editorInstance.value

  // Update undo/redo state
  editor.on('change:changesCount', () => {
    canUndo.value = editor.UndoManager.hasUndo()
    canRedo.value = editor.UndoManager.hasRedo()
  })

  // Auto-save functionality
  let autoSaveTimeout: NodeJS.Timeout
  editor.on('component:update', () => {
    if (props.readonly) return

    clearTimeout(autoSaveTimeout)
    autoSaveTimeout = setTimeout(() => {
      if (canSave.value) {
        saveTemplate()
      }
    }, 3000) // Auto-save after 3 seconds of inactivity
  })

  // Device change events
  editor.on('change:device', () => {
    const device = editor.getDevice()
    activeDevice.value = device || 'desktop'
  })
}

const loadTemplate = (template: EmailTemplate) => {
  if (!editorInstance.value) return

  try {
    const editor = editorInstance.value

    // Load components and styles
    if (template.content.components) {
      editor.setComponents(template.content.components)
    }

    if (template.content.css) {
      editor.setStyle(template.content.css)
    }

    // Set current template in store
    emailTemplateStore.currentTemplate = template

  } catch (error) {
    console.error('Error loading template:', error)
  }
}

const saveTemplate = async () => {
  if (!editorInstance.value || !canSave.value) return

  try {
    await emailTemplateStore.saveEditorContent()
    lastSaved.value = new Date()

    if (emailTemplateStore.currentTemplate) {
      emit('save', emailTemplateStore.currentTemplate)
    }

  } catch (error) {
    console.error('Error saving template:', error)
    // TODO: Show error toast
  }
}

const undoAction = () => {
  if (editorInstance.value && canUndo.value) {
    editorInstance.value.UndoManager.undo()
  }
}

const redoAction = () => {
  if (editorInstance.value && canRedo.value) {
    editorInstance.value.UndoManager.redo()
  }
}

const setDevice = (deviceId: string) => {
  if (editorInstance.value) {
    editorInstance.value.setDevice(deviceId)
    activeDevice.value = deviceId
  }
}

const togglePreview = () => {
  emailTemplateStore.togglePreviewMode()
}

const exportTemplate = (format: 'html' | 'json') => {
  if (!editorInstance.value) return

  const content = emailTemplateStore.exportTemplate(format)
  if (content) {
    emit('export', content, format)

    // Download file
    const blob = new Blob([typeof content === 'string' ? content : JSON.stringify(content, null, 2)], {
      type: format === 'html' ? 'text/html' : 'application/json'
    })

    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${templateName.value || 'template'}.${format}`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }
}

const resetTemplate = () => {
  if (editorInstance.value && confirm('Are you sure you want to reset the template? This action cannot be undone.')) {
    editorInstance.value.setComponents('')
    editorInstance.value.setStyle('')
  }
}

const formatTime = (date: Date) => {
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  const minutes = Math.floor(diff / 60000)

  if (minutes < 1) return 'just now'
  if (minutes < 60) return `${minutes}m ago`

  const hours = Math.floor(minutes / 60)
  if (hours < 24) return `${hours}h ago`

  const days = Math.floor(hours / 24)
  return `${days}d ago`
}

// Lifecycle
onMounted(() => {
  initializeEditor()
})

onUnmounted(() => {
  if (editorInstance.value) {
    editorInstance.value.destroy()
  }
})

// Watch for template changes
watch(() => props.template, (newTemplate) => {
  if (newTemplate && editorInstance.value) {
    loadTemplate(newTemplate)
  }
}, { deep: true })
</script>

<style scoped>

/* Main Editor Container */
.email-template-editor {
  height: 100%;
  width: 100%;
  overflow: hidden;
}

.email-template-editor #gjs-editor {
  height: 100% !important;
  width: 100% !important;
}

/* GrapesJS Custom Styles - Reset and Normalize */
.email-template-editor :deep(.gjs-editor) {
  border: none !important;
  background: #ffffff !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
  font-size: 14px !important;
  line-height: 1.4 !important;
}

.email-template-editor :deep(.gjs-cv-canvas) {
  background: #f8fafc !important;
  border: 1px solid #e2e8f0 !important;
  border-radius: 8px;
  margin: 16px;
}

.email-template-editor :deep(.gjs-frame) {
  border-radius: 8px;
  overflow: hidden;
  background: #ffffff !important;
}

.email-template-editor :deep(.gjs-cv-canvas__frames) {
  background: #ffffff !important;
}

/* Reset GrapesJS element sizes */
.email-template-editor :deep(.gjs-editor *) {
  box-sizing: border-box !important;
}

.email-template-editor :deep(.gjs-toolbar) {
  height: auto !important;
  min-height: 40px !important;
}

.email-template-editor :deep(.gjs-toolbar-item) {
  width: auto !important;
  height: 32px !important;
  padding: 6px 8px !important;
  margin: 2px !important;
  font-size: 12px !important;
}

/* Dark mode styles */
.dark .email-template-editor :deep(.gjs-cv-canvas) {
  background: #1e293b !important;
  border-color: #374151 !important;
}

.dark .email-template-editor :deep(.gjs-editor) {
  background: #1f2937 !important;
}

.dark .email-template-editor :deep(.gjs-frame) {
  background: #1f2937 !important;
}

/* Block Manager Styles */
.email-template-editor :deep(.gjs-blocks-container) {
  background: #ffffff !important;
  border: none;
  color: #374151 !important;
  font-size: 12px !important;
}

.email-template-editor :deep(.gjs-block-category) {
  background: #ffffff !important;
  color: #374151 !important;
  border-bottom: 1px solid #e5e7eb !important;
  margin-bottom: 8px !important;
}

.email-template-editor :deep(.gjs-title) {
  background: #f9fafb !important;
  color: #374151 !important;
  border-bottom: 1px solid #e5e7eb !important;
  padding: 8px 12px !important;
  font-size: 12px !important;
  font-weight: 600 !important;
  margin: 0 !important;
}

.email-template-editor :deep(.gjs-blocks-c) {
  padding: 8px !important;
}

.email-template-editor :deep(.gjs-block) {
  background: #f8fafc !important;
  border: 1px solid #e2e8f0 !important;
  border-radius: 6px !important;
  margin: 0 0 8px 0 !important;
  padding: 8px !important;
  transition: all 0.2s ease;
  color: #374151 !important;
  cursor: pointer !important;
  width: 100% !important;
  height: auto !important;
  min-height: 60px !important;
  max-height: 80px !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: center !important;
}

.email-template-editor :deep(.gjs-block:hover) {
  border-color: #3b82f6 !important;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.1) !important;
  transform: translateY(-1px) !important;
}

.email-template-editor :deep(.gjs-block-label) {
  font-size: 11px !important;
  font-weight: 500 !important;
  color: #374151 !important;
  text-align: center !important;
  margin-top: 4px !important;
  line-height: 1.2 !important;
}

.email-template-editor :deep(.gjs-block svg) {
  width: 20px !important;
  height: 20px !important;
  fill: #6b7280 !important;
}

.dark .email-template-editor :deep(.gjs-blocks-container) {
  background: #374151 !important;
  color: #f3f4f6 !important;
}

.dark .email-template-editor :deep(.gjs-block-category) {
  background: #374151 !important;
  color: #f3f4f6 !important;
}

.dark .email-template-editor :deep(.gjs-title) {
  background: #4b5563 !important;
  color: #f3f4f6 !important;
}

.dark .email-template-editor :deep(.gjs-block) {
  background: #4b5563 !important;
  border-color: #6b7280 !important;
  color: #f3f4f6 !important;
}

.dark .email-template-editor :deep(.gjs-block:hover) {
  border-color: #60a5fa !important;
}

/* Style Manager Styles */
.email-template-editor :deep(.gjs-sm-sector) {
  background: #ffffff !important;
  border: none;
  border-bottom: 1px solid #e2e8f0 !important;
  color: #374151 !important;
}

.email-template-editor :deep(.gjs-sm-title) {
  background: #f9fafb !important;
  color: #374151 !important;
  font-weight: 500;
  padding: 12px 8px !important;
  border-bottom: 1px solid #e5e7eb !important;
}

.email-template-editor :deep(.gjs-sm-property) {
  background: #ffffff !important;
  border-bottom: 1px solid #f1f5f9 !important;
  padding: 8px !important;
  color: #374151 !important;
}

.email-template-editor :deep(.gjs-sm-label) {
  color: #374151 !important;
}

.dark .email-template-editor :deep(.gjs-sm-sector) {
  background: #374151 !important;
  border-bottom-color: #4b5563 !important;
  color: #f3f4f6 !important;
}

.dark .email-template-editor :deep(.gjs-sm-title) {
  background: #4b5563 !important;
  color: #f3f4f6 !important;
}

.dark .email-template-editor :deep(.gjs-sm-property) {
  background: #374151 !important;
  border-bottom-color: #4b5563 !important;
  color: #f3f4f6 !important;
}

.dark .email-template-editor :deep(.gjs-sm-label) {
  color: #f3f4f6 !important;
}

/* Layers Manager Styles */
.email-template-editor :deep(.gjs-lm-layer) {
  background: #ffffff !important;
  border-bottom: 1px solid #f1f5f9 !important;
  padding: 6px 8px !important;
  color: #374151 !important;
}

.email-template-editor :deep(.gjs-lm-name) {
  color: #374151 !important;
}

.dark .email-template-editor :deep(.gjs-lm-layer) {
  background: #374151 !important;
  border-bottom-color: #4b5563 !important;
  color: #f3f4f6 !important;
}

.dark .email-template-editor :deep(.gjs-lm-name) {
  color: #f3f4f6 !important;
}

/* Traits Manager Styles */
.email-template-editor :deep(.gjs-trt-trait) {
  background: #ffffff !important;
  border-bottom: 1px solid #f1f5f9 !important;
  padding: 8px !important;
  color: #374151 !important;
}

.email-template-editor :deep(.gjs-trt-label) {
  color: #374151 !important;
}

.dark .email-template-editor :deep(.gjs-trt-trait) {
  background: #374151 !important;
  border-bottom-color: #4b5563 !important;
  color: #f3f4f6 !important;
}

.dark .email-template-editor :deep(.gjs-trt-label) {
  color: #f3f4f6 !important;
}

.email-template-editor :deep(.gjs-field) {
  background: white !important;
  border: 1px solid #d1d5db !important;
  border-radius: 4px;
  color: #374151 !important;
  padding: 6px 8px;
}

.dark .email-template-editor :deep(.gjs-field) {
  background: #4b5563 !important;
  border-color: #6b7280 !important;
  color: #f3f4f6 !important;
}

/* Global GrapesJS Panel Styles */
.email-template-editor :deep(.gjs-pn-panel) {
  background: #ffffff !important;
  color: #374151 !important;
  font-size: 12px !important;
}

.email-template-editor :deep(.gjs-pn-views-container) {
  background: #ffffff !important;
}

.email-template-editor :deep(.gjs-pn-views) {
  background: #ffffff !important;
}

.email-template-editor :deep(.gjs-pn-buttons) {
  background: #f9fafb !important;
  border-bottom: 1px solid #e5e7eb !important;
  padding: 8px !important;
}

.email-template-editor :deep(.gjs-pn-btn) {
  background: #ffffff !important;
  border: 1px solid #d1d5db !important;
  border-radius: 4px !important;
  color: #374151 !important;
  font-size: 11px !important;
  padding: 6px 8px !important;
  margin: 0 2px !important;
  height: auto !important;
  min-height: 28px !important;
}

.email-template-editor :deep(.gjs-pn-btn:hover) {
  background: #f3f4f6 !important;
  border-color: #9ca3af !important;
}

.email-template-editor :deep(.gjs-pn-btn.gjs-pn-active) {
  background: #3b82f6 !important;
  border-color: #3b82f6 !important;
  color: #ffffff !important;
}

.dark .email-template-editor :deep(.gjs-pn-panel) {
  background: #374151 !important;
  color: #f3f4f6 !important;
}

.dark .email-template-editor :deep(.gjs-pn-views-container) {
  background: #374151 !important;
}

.dark .email-template-editor :deep(.gjs-pn-views) {
  background: #374151 !important;
}

/* Ensure all text is visible */
.email-template-editor :deep(*) {
  color: inherit;
}

.email-template-editor :deep(.gjs-block-label) {
  color: #374151 !important;
}

.dark .email-template-editor :deep(.gjs-block-label) {
  color: #f3f4f6 !important;
}

/* Canvas and Toolbar Fixes */
.email-template-editor :deep(.gjs-cv-canvas) {
  margin: 8px !important;
}

.email-template-editor :deep(.gjs-toolbar) {
  background: #f9fafb !important;
  border: 1px solid #e5e7eb !important;
  border-radius: 6px !important;
  padding: 4px !important;
  margin: 4px !important;
}

.email-template-editor :deep(.gjs-toolbar-item) {
  background: #ffffff !important;
  border: 1px solid #d1d5db !important;
  border-radius: 4px !important;
  color: #374151 !important;
  font-size: 11px !important;
  padding: 4px 6px !important;
  margin: 1px !important;
  height: 24px !important;
  line-height: 1 !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.email-template-editor :deep(.gjs-toolbar-item:hover) {
  background: #f3f4f6 !important;
  border-color: #9ca3af !important;
}

.email-template-editor :deep(.gjs-toolbar-item.gjs-toolbar-item--active) {
  background: #3b82f6 !important;
  border-color: #3b82f6 !important;
  color: #ffffff !important;
}

/* Fix canvas frame sizing */
.email-template-editor :deep(.gjs-frame) {
  width: 100% !important;
  height: 100% !important;
  border: none !important;
}

/* Compact the right panels */
.email-template-editor :deep(.gjs-sm-sector .gjs-sm-title) {
  padding: 6px 8px !important;
  font-size: 11px !important;
}

.email-template-editor :deep(.gjs-sm-property) {
  padding: 4px 8px !important;
  font-size: 11px !important;
}

.email-template-editor :deep(.gjs-lm-layer) {
  padding: 4px 8px !important;
  font-size: 11px !important;
}

.email-template-editor :deep(.gjs-trt-trait) {
  padding: 4px 8px !important;
  font-size: 11px !important;
}

/* Ensure proper editor layout */
.email-template-editor :deep(.gjs-editor-cont) {
  height: 100% !important;
  width: 100% !important;
  display: flex !important;
  flex-direction: row !important;
}

.email-template-editor :deep(.gjs-cv-canvas) {
  flex: 1 !important;
  height: 100% !important;
  overflow: auto !important;
}

/* Fix any oversized elements */
.email-template-editor :deep(*) {
  max-width: 100% !important;
}

.email-template-editor :deep(.gjs-block) {
  box-sizing: border-box !important;
}

/* Responsive adjustments for smaller screens */
@media (max-width: 1200px) {
  .email-template-editor .w-64 {
    width: 200px !important;
  }

  .email-template-editor :deep(.gjs-block) {
    min-height: 50px !important;
    max-height: 60px !important;
  }

  .email-template-editor :deep(.gjs-block-label) {
    font-size: 10px !important;
  }
}

/* Responsive adjustments */
@media (max-width: 1024px) {
  .email-template-editor .w-64 {
    width: 16rem;
  }
}

@media (max-width: 768px) {
  .email-template-editor .w-64 {
    width: 12rem;
  }

  .editor-toolbar {
    flex-wrap: wrap;
    gap: 8px;
  }

  .editor-toolbar > div {
    flex-shrink: 0;
  }
}
</style>
