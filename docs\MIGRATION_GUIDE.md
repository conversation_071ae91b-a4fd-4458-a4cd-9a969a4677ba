# Migration Guide: Enhanced File Structure

This guide will help you migrate from the current structure to the enhanced, scalable architecture.

## Overview

The migration involves moving from a type-based organization to a feature-based organization while maintaining backward compatibility during the transition.

## Migration Strategy

### Phase 1: Setup New Structure (Week 1)
1. Create new `app/` directory structure
2. Set up enhanced configurations
3. Update build tools and linting

### Phase 2: Migrate Core Features (Week 2-3)
1. Migrate authentication system
2. Migrate case management
3. Migrate document management

### Phase 3: Migrate Remaining Features (Week 4)
1. Migrate templates
2. Migrate settings
3. Migrate user management

### Phase 4: Update Dependencies (Week 5)
1. Update all imports
2. Remove legacy structure
3. Optimize bundle configuration

## Detailed Migration Steps

### 1. Authentication Feature Migration

#### Current Structure:
```
components/auth/
├── LoginForm.vue
└── Setup2FA.vue

composables/
├── useAuth.ts
└── useRefreshToken.ts

stores/
└── auth.ts

types/
└── auth.d.ts
```

#### New Structure:
```
app/features/auth/
├── components/
│   ├── LoginForm.vue
│   └── Setup2FA.vue
├── composables/
│   ├── useAuth.ts
│   └── useAuthApi.ts
├── stores/
│   └── authStore.ts
├── types/
│   ├── models.ts
│   └── api.ts
└── index.ts
```

#### Migration Commands:
```bash
# Create new auth feature structure
mkdir -p app/features/auth/{components,composables,stores,types,utils,constants}

# Move existing files
mv components/auth/* app/features/auth/components/
mv composables/useAuth.ts app/features/auth/composables/
mv stores/auth.ts app/features/auth/stores/authStore.ts
mv types/auth.d.ts app/features/auth/types/models.ts

# Create barrel export
touch app/features/auth/index.ts
```

### 2. Update Import Statements

#### Before:
```typescript
import { useAuth } from '~/composables/useAuth'
import { useAuthStore } from '~/stores/auth'
import LoginForm from '~/components/auth/LoginForm.vue'
```

#### After:
```typescript
import { useAuth, useAuthStore, LoginForm } from '@features/auth'
// or
import { useAuth } from '@features/auth/composables/useAuth'
```

### 3. Component Migration

#### Before:
```vue
<script setup lang="ts">
import { useAuth } from '~/composables/useAuth'
import { useAuthStore } from '~/stores/auth'

const { login } = useAuth()
const authStore = useAuthStore()
</script>
```

#### After:
```vue
<script setup lang="ts">
import { useAuth, useAuthStore } from '@features/auth'

const { login } = useAuth()
const authStore = useAuthStore()
</script>
```

### 4. Store Migration

#### Before (stores/auth.ts):
```typescript
import { defineStore } from 'pinia'

export const useAuthStore = defineStore('auth', {
  // store implementation
})
```

#### After (app/features/auth/stores/authStore.ts):
```typescript
import { defineStore } from 'pinia'
import type { User, AuthSession } from '../types/models'

export const useAuthStore = defineStore('auth', {
  // enhanced store implementation with better typing
})
```

### 5. Type Migration

#### Before (types/auth.d.ts):
```typescript
export interface User {
  id: string
  name: string
  email: string
}
```

#### After (app/features/auth/types/models.ts):
```typescript
export interface User {
  id: string
  name: string
  email: string
  // enhanced with more fields and better organization
}

export interface AuthSession {
  // new session interface
}
```

## Automated Migration Scripts

### Script 1: Create Feature Structure
```bash
#!/bin/bash
# create-feature-structure.sh

features=("auth" "cases" "documents" "templates" "users" "settings" "notifications")

for feature in "${features[@]}"; do
  mkdir -p "app/features/$feature"/{components,composables,stores,types,utils,constants,__tests__}
  touch "app/features/$feature/index.ts"
done

mkdir -p app/shared/{components,composables,stores,types,utils}
mkdir -p app/core/{api,auth,config,constants,plugins}
```

### Script 2: Update Imports
```bash
#!/bin/bash
# update-imports.sh

# Update component imports
find . -name "*.vue" -type f -exec sed -i 's|~/components/auth/|@features/auth/components/|g' {} \;
find . -name "*.vue" -type f -exec sed -i 's|~/composables/useAuth|@features/auth/composables/useAuth|g' {} \;
find . -name "*.vue" -type f -exec sed -i 's|~/stores/auth|@features/auth/stores/authStore|g' {} \;

# Update TypeScript imports
find . -name "*.ts" -type f -exec sed -i 's|~/composables/useAuth|@features/auth/composables/useAuth|g' {} \;
find . -name "*.ts" -type f -exec sed -i 's|~/stores/auth|@features/auth/stores/authStore|g' {} \;
```

## Testing Migration

### 1. Unit Tests
```typescript
// Before
import { useAuth } from '~/composables/useAuth'

// After
import { useAuth } from '@features/auth'
```

### 2. Integration Tests
Ensure all feature modules work together correctly after migration.

### 3. E2E Tests
Run full application tests to verify functionality is preserved.

## Rollback Plan

If issues arise during migration:

1. **Immediate Rollback**: Keep old structure alongside new during migration
2. **Gradual Rollback**: Revert one feature at a time
3. **Import Aliases**: Use aliases to maintain compatibility

```typescript
// In nuxt.config.ts - temporary compatibility
alias: {
  '~/composables/useAuth': './app/features/auth/composables/useAuth',
  '~/stores/auth': './app/features/auth/stores/authStore'
}
```

## Validation Checklist

- [ ] All imports updated and working
- [ ] No broken dependencies
- [ ] All tests passing
- [ ] Bundle size optimized
- [ ] Performance metrics maintained
- [ ] TypeScript compilation successful
- [ ] Linting passes
- [ ] Documentation updated

## Benefits After Migration

1. **Better Organization**: Feature-based structure scales with team size
2. **Improved Performance**: Code splitting and lazy loading
3. **Enhanced DX**: Better IDE support and autocomplete
4. **Easier Testing**: Isolated feature testing
5. **Better Maintainability**: Clear boundaries between features
6. **Scalable Architecture**: Easy to add new features

## Support

If you encounter issues during migration:

1. Check the troubleshooting section
2. Review the example implementations
3. Consult the team lead
4. Document any new issues for future reference
