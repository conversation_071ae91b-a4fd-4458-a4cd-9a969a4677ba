<template>
  <div class="ui-area-chart">
    <AreaChart
      :key="colorModeKey"
      :data="data"
      :height="height"
      :categories="categories"
      :y-grid-line="yGridLine"
      :x-formatter="xFormatter"
      :curve-type="curveType"
      :legend-position="legendPosition"
      :hide-legend="hideLegend"
    />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

// Types
interface BulletLegendItemInterface {
  name: string
  color: string
}

interface Props {
  data: any[]
  height?: number
  categories: Record<string, BulletLegendItemInterface>
  yGridLine?: boolean
  xFormatter?: (i: number) => string | number
  curveType?: string
  legendPosition?: string
  hideLegend?: boolean
  colorModeKey?: string
}

// Props with defaults
const props = withDefaults(defineProps<Props>(), {
  height: 300,
  yGridLine: true,
  curveType: 'monotoneX',
  legendPosition: 'top',
  hideLegend: false
})

// Composables
const colorMode = useColorMode()

// Computed
const colorModeKey = computed(() => colorMode.value)
</script>

<style scoped>
.ui-area-chart {
  width: 100%;
}
</style>
