/**
 * Global Properties Composable
 *
 * A simple composable for managing global properties (functions, strings, numbers, booleans, etc.)
 * that can be accessed throughout the entire application
 */

import { reactive, readonly } from 'vue'

// ============================================================================
// TYPES AND INTERFACES
// ============================================================================

export type GlobalPropertyValue = string | number | boolean | Function | object | null | undefined

export interface GlobalState {
  [key: string]: GlobalPropertyValue
}

export interface UseGlobalPropertiesReturn {
  // State access
  state: Readonly<GlobalState>

  // Methods
  add: (propertyName: string, value: GlobalPropertyValue) => void
  remove: (propertyName: string) => void
  get: (propertyName: string) => GlobalPropertyValue
  has: (propertyName: string) => boolean
  clear: () => void
  keys: () => string[]
}

// ============================================================================
// GLOBAL STATE REGISTRY
// ============================================================================

const globalState: GlobalState = reactive({})

// ============================================================================
// MAIN COMPOSABLE
// ============================================================================

export function useGlobalProperties(): UseGlobalPropertiesReturn {

  // ============================================================================
  // METHODS
  // ============================================================================

  const add = (propertyName: string, value: GlobalPropertyValue): void => {
    globalState[propertyName] = value
  }

  const remove = (propertyName: string): void => {
    delete globalState[propertyName]
  }

  const get = (propertyName: string): GlobalPropertyValue => {
    return globalState[propertyName]
  }

  const has = (propertyName: string): boolean => {
    return propertyName in globalState
  }

  const clear = (): void => {
    Object.keys(globalState).forEach(key => {
      delete globalState[key]
    })
  }

  const keys = (): string[] => {
    return Object.keys(globalState)
  }

  // ============================================================================
  // RETURN INTERFACE
  // ============================================================================

  return {
    state: readonly(globalState),
    add,
    remove,
    get,
    has,
    clear,
    keys
  }
}
