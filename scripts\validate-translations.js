#!/usr/bin/env node

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

const LOCALES_DIR = path.join(__dirname, '../locales')
const LANGUAGES = ['en', 'he', 'ar']
const CATEGORIES = [
  'common',
  'time', 
  'tenantList',
  'tenantDetail',
  'userManagement',
  'navigation',
  'caseManagement',
  'authPages',
  'ui',
  'dashboard',
  'documents',
  'templates',
  'notifications',
  'errors'
]

console.log('🔍 Validating translation files...\n')

// Function to get all keys from an object recursively
function getAllKeys(obj, prefix = '') {
  let keys = []
  for (const [key, value] of Object.entries(obj)) {
    const fullKey = prefix ? `${prefix}.${key}` : key
    if (typeof value === 'object' && value !== null) {
      keys = keys.concat(getAllKeys(value, fullKey))
    } else {
      keys.push(fullKey)
    }
  }
  return keys
}

// Function to load and validate a translation file
function loadTranslationFile(language, category) {
  const filePath = path.join(LOCALES_DIR, language, `${category}.json`)
  
  if (!fs.existsSync(filePath)) {
    return { error: `File not found: ${filePath}` }
  }

  try {
    const content = fs.readFileSync(filePath, 'utf8')
    const data = JSON.parse(content)
    return { data, keys: getAllKeys(data) }
  } catch (error) {
    return { error: `Invalid JSON in ${filePath}: ${error.message}` }
  }
}

// Main validation
let hasErrors = false
const results = {}

// Load English as reference
console.log('📚 Loading English translations as reference...')
const englishTranslations = {}
for (const category of CATEGORIES) {
  const result = loadTranslationFile('en', category)
  if (result.error) {
    console.error(`❌ ${result.error}`)
    hasErrors = true
    continue
  }
  englishTranslations[category] = result
  console.log(`✅ English ${category}: ${result.keys.length} keys`)
}

console.log('\n🌍 Validating other languages...')

// Validate other languages against English
for (const language of LANGUAGES.slice(1)) { // Skip English
  console.log(`\n--- ${language.toUpperCase()} ---`)
  results[language] = {}
  
  for (const category of CATEGORIES) {
    const result = loadTranslationFile(language, category)
    results[language][category] = result
    
    if (result.error) {
      console.error(`❌ ${language}/${category}: ${result.error}`)
      hasErrors = true
      continue
    }

    const englishKeys = englishTranslations[category]?.keys || []
    const currentKeys = result.keys
    
    // Find missing keys
    const missingKeys = englishKeys.filter(key => !currentKeys.includes(key))
    const extraKeys = currentKeys.filter(key => !englishKeys.includes(key))
    
    if (missingKeys.length === 0 && extraKeys.length === 0) {
      console.log(`✅ ${language}/${category}: ${currentKeys.length} keys (complete)`)
    } else {
      console.log(`⚠️  ${language}/${category}: ${currentKeys.length} keys`)
      if (missingKeys.length > 0) {
        console.log(`   Missing: ${missingKeys.slice(0, 5).join(', ')}${missingKeys.length > 5 ? ` (+${missingKeys.length - 5} more)` : ''}`)
      }
      if (extraKeys.length > 0) {
        console.log(`   Extra: ${extraKeys.slice(0, 5).join(', ')}${extraKeys.length > 5 ? ` (+${extraKeys.length - 5} more)` : ''}`)
      }
    }
  }
}

// Summary
console.log('\n📊 Summary:')
console.log(`Total categories: ${CATEGORIES.length}`)
console.log(`Total languages: ${LANGUAGES.length}`)

let totalFiles = 0
let validFiles = 0
for (const language of LANGUAGES) {
  for (const category of CATEGORIES) {
    totalFiles++
    const result = language === 'en' ? englishTranslations[category] : results[language]?.[category]
    if (result && !result.error) {
      validFiles++
    }
  }
}

console.log(`Valid files: ${validFiles}/${totalFiles}`)

if (hasErrors) {
  console.log('\n❌ Validation completed with errors')
  process.exit(1)
} else {
  console.log('\n✅ All translation files are valid!')
  process.exit(0)
}
