<template>
  <div class="p-6 space-y-6">
    <h2 class="text-2xl font-bold text-gray-900 dark:text-white">Custom Dropdown Examples</h2>
    
    <!-- Example 1: Quick Filter Presets (like your example) -->
    <div class="space-y-4">
      <h3 class="text-lg font-semibold text-gray-800 dark:text-gray-200">Quick Filter Presets</h3>
      
      <UiCustomDropdown
        trigger-text="Quick Filters"
        trigger-icon="material-symbols:speed"
        button-variant="outline"
        button-size="sm"
        align="right"
        width="64"
      >
        <template #default="{ close }">
          <UiCustomDropdownItem
            v-for="preset in quickFilterPresets"
            :key="preset.key"
            :icon="preset.icon"
            :label="preset.label"
            :badge="preset.count"
            @click="applyQuickFilter(preset, close)"
          />
        </template>
      </UiCustomDropdown>
    </div>

    <!-- Example 2: Actions Menu -->
    <div class="space-y-4">
      <h3 class="text-lg font-semibold text-gray-800 dark:text-gray-200">Actions Menu</h3>
      
      <UiCustomDropdown
        trigger-text="Actions"
        trigger-icon="material-symbols:more-vert"
        button-variant="ghost"
        button-size="sm"
        align="left"
        width="56"
      >
        <template #default="{ close }">
          <UiCustomDropdownItem
            icon="heroicons:eye"
            label="View Details"
            @click="handleAction('view', close)"
          />
          <UiCustomDropdownItem
            icon="heroicons:pencil"
            label="Edit"
            @click="handleAction('edit', close)"
          />
          <UiCustomDropdownItem
            icon="heroicons:document-duplicate"
            label="Duplicate"
            @click="handleAction('duplicate', close)"
          />
          <div class="border-t border-gray-200 dark:border-gray-600 my-1"></div>
          <UiCustomDropdownItem
            icon="heroicons:trash"
            label="Delete"
            item-class="text-red-600 hover:bg-red-50 dark:text-red-400 dark:hover:bg-red-900/20"
            @click="handleAction('delete', close)"
          />
        </template>
      </UiCustomDropdown>
    </div>

    <!-- Example 3: Status Filter -->
    <div class="space-y-4">
      <h3 class="text-lg font-semibold text-gray-800 dark:text-gray-200">Status Filter</h3>
      
      <UiCustomDropdown
        trigger-text="Filter by Status"
        trigger-icon="heroicons:funnel"
        button-variant="outline"
        button-size="md"
        align="auto"
        width="72"
      >
        <template #default="{ close }">
          <UiCustomDropdownItem
            icon="heroicons:check-circle"
            label="Active"
            badge="24"
            @click="filterByStatus('active', close)"
          />
          <UiCustomDropdownItem
            icon="heroicons:clock"
            label="Pending"
            badge="8"
            @click="filterByStatus('pending', close)"
          />
          <UiCustomDropdownItem
            icon="heroicons:x-circle"
            label="Inactive"
            badge="3"
            @click="filterByStatus('inactive', close)"
          />
          <div class="border-t border-gray-200 dark:border-gray-600 my-1"></div>
          <UiCustomDropdownItem
            icon="heroicons:squares-2x2"
            label="Show All"
            badge="35"
            @click="filterByStatus('all', close)"
          />
        </template>
      </UiCustomDropdown>
    </div>

    <!-- Example 4: User Menu -->
    <div class="space-y-4">
      <h3 class="text-lg font-semibold text-gray-800 dark:text-gray-200">User Menu</h3>
      
      <UiCustomDropdown
        trigger-text="John Doe"
        trigger-icon="heroicons:user-circle"
        button-variant="ghost"
        button-size="sm"
        align="right"
        width="48"
      >
        <template #default="{ close }">
          <UiCustomDropdownItem
            icon="heroicons:user"
            label="Profile"
            @click="handleUserAction('profile', close)"
          />
          <UiCustomDropdownItem
            icon="heroicons:cog-6-tooth"
            label="Settings"
            @click="handleUserAction('settings', close)"
          />
          <UiCustomDropdownItem
            icon="heroicons:question-mark-circle"
            label="Help"
            @click="handleUserAction('help', close)"
          />
          <div class="border-t border-gray-200 dark:border-gray-600 my-1"></div>
          <UiCustomDropdownItem
            icon="heroicons:arrow-right-on-rectangle"
            label="Sign Out"
            @click="handleUserAction('signout', close)"
          />
        </template>
      </UiCustomDropdown>
    </div>
  </div>
</template>

<script setup lang="ts">
import UiCustomDropdown from './UiCustomDropdown.vue';
import UiCustomDropdownItem from './UiCustomDropdownItem.vue';

// Sample data for quick filter presets
const quickFilterPresets = ref([
  {
    key: 'recent',
    label: 'Recent Items',
    icon: 'heroicons:clock',
    count: 12
  },
  {
    key: 'favorites',
    label: 'Favorites',
    icon: 'heroicons:star',
    count: 8
  },
  {
    key: 'shared',
    label: 'Shared with Me',
    icon: 'heroicons:share',
    count: 5
  },
  {
    key: 'archived',
    label: 'Archived',
    icon: 'heroicons:archive-box',
    count: 23
  }
]);

// Methods
const applyQuickFilter = (preset: any, close: () => void) => {
  console.log('Applying quick filter:', preset);
  // Your filter logic here
  close();
};

const handleAction = (action: string, close: () => void) => {
  console.log('Action:', action);
  // Your action logic here
  close();
};

const filterByStatus = (status: string, close: () => void) => {
  console.log('Filter by status:', status);
  // Your filter logic here
  close();
};

const handleUserAction = (action: string, close: () => void) => {
  console.log('User action:', action);
  // Your user action logic here
  close();
};
</script>

<style scoped>
/* Additional styles if needed */
</style>
