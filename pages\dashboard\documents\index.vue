<template>
  <div class="space-y-6">
    <!-- Page Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">{{ $t('documents.documents') }}</h1>
        <p class="text-gray-600 dark:text-gray-400">{{ $t('documents.documentManagement') }}</p>
      </div>
      <div class="flex items-center ltr:space-x-2 rtl:space-x-reverse">
        <UiButton @click="handleUploadDocument" variant="outline">
          <Icon name="material-symbols:upload" class="ltr:h-4 ltr:w-4 ltr:mr-2 rtl:h-4 rtl:w-4 rtl:ml-2" />
          {{ $t('documents.uploadDocument') }}
        </UiButton>
        <UiButton @click="handleCreateDocument" variant="primary">
          <Icon name="material-symbols:add" class="ltr:h-4 ltr:w-4 ltr:mr-2 rtl:h-4 rtl:w-4 rtl:ml-2" />
          {{ $t('common.create') }}
        </UiButton>
      </div>
    </div>

    <!-- Document Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <div v-for="document in sampleDocuments" :key="document.id"
           class="bg-white dark:bg-gray-800 rounded-lg shadow hover:shadow-md transition-shadow duration-200">
        <div class="p-6">
          <div class="flex items-center justify-between mb-4">
            <div class="flex items-center">
              <Icon :name="getDocumentIcon(document.type)" class="h-8 w-8 text-blue-500" />
              <div class="ltr:ml-3 rtl:mr-3">
                <h3 class="text-sm font-medium text-gray-900 dark:text-white">{{ document.name }}</h3>
                <p class="text-xs text-gray-500 dark:text-gray-400">{{ document.type }}</p>
              </div>
            </div>
            <UiButton size="sm" variant="ghost">
              <Icon name="material-symbols:more-vert" class="h-4 w-4" />
            </UiButton>
          </div>

          <div class="space-y-2 text-xs text-gray-500 dark:text-gray-400">
            <div class="flex justify-between">
              <span>{{ $t('documents.documentSize') }}:</span>
              <span>{{ document.size }}</span>
            </div>
            <div class="flex justify-between">
              <span>{{ $t('documents.lastModified') }}:</span>
              <span>{{ document.modified }}</span>
            </div>
            <div class="flex justify-between">
              <span>{{ $t('common.status') }}:</span>
              <span :class="[
                document.status === 'approved' ? 'text-green-600' :
                document.status === 'pending' ? 'text-yellow-600' :
                'text-gray-600'
              ]">
                {{ $t(`common.${document.status}`) }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

// Sample data
const sampleDocuments = ref([
  {
    id: 1,
    name: 'Contract Agreement.pdf',
    type: 'Contract',
    size: '2.4 MB',
    modified: '2 hours ago',
    status: 'approved'
  },
  {
    id: 2,
    name: 'Legal Brief.docx',
    type: 'Brief',
    size: '1.8 MB',
    modified: '1 day ago',
    status: 'pending'
  },
  {
    id: 3,
    name: 'Evidence Photos.zip',
    type: 'Evidence',
    size: '15.2 MB',
    modified: '3 days ago',
    status: 'approved'
  }
])

// Methods
const handleCreateDocument = () => {
  console.log('Create new document')
}

const handleUploadDocument = () => {
  console.log('Upload document')
}

const getDocumentIcon = (type: string) => {
  switch (type.toLowerCase()) {
    case 'contract':
      return 'material-symbols:description'
    case 'brief':
      return 'material-symbols:article'
    case 'evidence':
      return 'material-symbols:folder-zip'
    default:
      return 'material-symbols:insert-drive-file'
  }
}

definePageMeta({
  layout: 'dashboard',
  title: 'Documents',
  showViewToggle: true,
  showQuickFilters: true,
  showActionsMenu: true,
  showKeyboardShortcuts: true,
  showPageHeader: true,
  pageHeaderTitle: 'documents.documentManagement',
  pageHeaderDescription: 'documents.documentManagement',
  pageHeaderIcon: 'material-symbols:description',
  pageHeaderStats: [
    { key: 'contracts', label: 'documents.documentTypes', value: 45, color: 'blue' },
    { key: 'briefs', label: 'documents.recentDocuments', value: 23, color: 'purple' },
    { key: 'evidence', label: 'documents.documentLibrary', value: 67, color: 'green' }
  ],
  showPageHeaderActions: true,
  showPageHeaderExport: true,
  showPageHeaderCreate: true,
  pageHeaderCreateLabel: 'New Document',
  pageHeaderCreateIcon: 'material-symbols:note-add',
  breadcrumb: [
    { label: 'Dashboard', href: '/dashboard' },
    { label: 'Documents' },
  ],
});
</script>