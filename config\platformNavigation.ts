import { PlatformRoles, TenantRoles } from "~/app/features/auth/constants/roles";


export default
  {
    "menuItems": [
      {
        "id": "dashboard",
        "label": "Dashboard",
        "icon": "heroicons:home",
        "path": "/dashboard",
        "roles": [],
        "order": 1
      },
      {
        "id": "tenant-management",
        "label": "Tenant Management",
        "icon": "heroicons:building-office-2",
        "roles": [PlatformRoles.SUPER_ADMIN],
        "order": 2,
        "children": [
          {
            "id": "tenants-overview",
            "label": "Tenant Overview",
            "icon": "solar:graph-new-broken",
            "path": "/dashboard/tenants",
            "order": 1
          },
          {
            "id": "tenants-manage",
            "label": "Manage Tenants",
            "icon": "mdi:office-building-cog-outline",
            "path": "/dashboard/tenants/manage",

            "order": 2
          },
          {
            "id": "tenants-create",
            "label": "New Tenant",
            "icon": "i-mdi:office-building-plus-outline",
            "path": "/dashboard/tenants/create",
            "order": 3
          },
          {
            "id": "tenants-invite",
            "label": "Invite Users to Tenant",
            "icon": "heroicons:user-plus",
            "path": "/dashboard/tenants/invite",
            "order": 4
          },
          {
            "id": "tenant-reports",
            "label": "Reports",
            "icon": "heroicons:chart-bar",
            "path": "/dashboard/tenants/reports",
            "tenantRoles": [],
            "order": 5
          }
        ]
      },
      {
        "id": "users-management",
        "label": "Users Management",
        "icon": "heroicons:users",
        "roles": [PlatformRoles.SUPER_ADMIN],
        "order": 3,
        "children": [
          {
            "id": "uesrs-overview",
            "label": "Users Overview",
            "icon": "heroicons:user-group",
            "path": "/dashboard/users",
            "order": 1
          },
          {
            "id": "users-manage",
            "label": "Manage Users",
            "icon": "heroicons:user-group",
            "path": "/dashboard/users/manage",

            "order": 2
          },
          {
            "id": "users-invite",
            "label": "Invite a User",
            "icon": "heroicons:plus-circle",
            "path": "/dashboard/users/invite",
            "order": 3
          },


          {
            "id": "users-roles",
            "label": "Users Roles",
            "icon": "heroicons:shield-check",
            "path": "/dashboard/users/roles",
            "tenantRoles": [],
            "order": 4
          }
        ]
      },

      {
        "id": "templates",
        "label": "Templates",
        "icon": "heroicons:clipboard-document-list",
        "roles": [PlatformRoles.SUPER_ADMIN],
        "tenantRoles": [],
        "order": 4,
        "children": [
          {
            "id": "document-templates",
            "label": "Document Templates",
            "icon": "heroicons:document-text",
            "path": "/dashboard/templates/document",
            "order": 3
          },
          {
            "id": "notification-templates",
            "label": "Notification Templates",
            "icon": "heroicons:bell",
            "path": "/dashboard/templates/notification",
            "order": 4
          },
          {
            "id": "invoice-templates",
            "label": "Invoice Templates",
            "icon": "heroicons:currency-dollar",
            "path": "/dashboard/templates/invoice",
            "order": 5
          }
        ]
      },
      {
        "id": "notifications",
        "label": "Notifications",
        "icon": "heroicons:bell-alert",
        "roles": [PlatformRoles.SUPER_ADMIN],
        "order": 5,
        "children": [
          {
            "id": "notification-triggers",
            "label": "Notification Triggers",
            "icon": "heroicons:bolt",
            "path": "/dashboard/settings/notifications/triggers",
            "order": 1
          },
          {
            "id": "notification-logs",
            "label": "Notification Logs",
            "icon": "heroicons:clipboard-document-list",
            "path": "/dashboard/settings/notifications/logs",
            "order": 2
          },
          {
            "id": "notification-settings",
            "label": "Notification Settings",
            "icon": "heroicons:cog-6-tooth",
            "path": "/dashboard/settings/notifications",
            "order": 3
          }
        ]
      },
      {
        "id": "platform-system",
        "label": "System",
        "icon": "heroicons:server",
        "roles": [PlatformRoles.SUPER_ADMIN],
        "order": 6,
        "children": [
          {
            "id": "system-overview",
            "label": "System Overview",
            "icon": "heroicons:chart-bar-square",
            "path": "/dashboard/system/overview",
            "order": 1
          },
          {
            "id": "system-health",
            "label": "System Health",
            "icon": "heroicons:heart",
            "path": "/dashboard/system/health",
            "order": 2
          },
          {
            "id": "audit-logs",
            "label": "Audit Logs",
            "icon": "heroicons:clipboard-document-list",
            "path": "/dashboard/system/audit-logs",
            "order": 3
          },
          {
            "id": "system-settings",
            "label": "System Settings",
            "icon": "heroicons:cog-6-tooth",
            "path": "/dashboard/system/settings",
            "order": 4
          }
        ]
      },
      // Keep existing tenant-management and users-management sections
      // Add new analytics section
      {
        "id": "platform-analytics",
        "label": "Analytics",
        "icon": "heroicons:presentation-chart-line",
        "roles": [PlatformRoles.SUPER_ADMIN],
        "order": 7,
        "children": [
          {
            "id": "usage-analytics",
            "label": "Usage Analytics",
            "icon": "heroicons:chart-pie",
            "path": "/dashboard/system/analytics/usage",
            "order": 1
          },
          {
            "id": "performance-analytics",
            "label": "Performance",
            "icon": "heroicons:chart-bar",
            "path": "/dashboard/system/analytics/performance",
            "order": 2
          },
          {
            "id": "billing-analytics",
            "label": "Billing Analytics",
            "icon": "heroicons:currency-dollar",
            "path": "/dashboard/system/analytics/billing",
            "order": 3
          }
        ]
      }
    ],
    "roleHierarchy": {
      [PlatformRoles.SUPER_ADMIN]: [PlatformRoles.SUPPORT],
      [PlatformRoles.SUPPORT]: [],

    }
  }
