# GlobalPageHeaderFilters Component

A robust, flexible filters component designed to be integrated into the left side of `GlobalPageHeader.vue`. This component provides comprehensive filtering capabilities with both basic and advanced filter options.

## Features

- **Dual Layout Modes**: Compact horizontal layout and full vertical layout
- **Basic Filters**: Quick dropdown filters for common filtering needs
- **Advanced Filters**: Expandable panel with date ranges, number ranges, and custom filters
- **Responsive Design**: Adapts to different screen sizes
- **Dark Mode Support**: Full dark mode compatibility
- **TypeScript**: Fully typed with proper interfaces
- **Smooth Animations**: Elegant transitions for advanced filters panel
- **Custom Slots**: Support for custom filter components

## Integration with GlobalPageHeader

### Basic Usage

```vue
<template>
  <div>
    <GlobalPageHeader
      title="Tenants Management"
      description="Manage all tenant accounts and settings"
      icon="heroicons:building-office"
      :show-filters="true"
      :filter-configs="filterConfigs"
      :advanced-filter-configs="advancedFilterConfigs"
      :compact-filters="false"
      @filter-change="handleFilterChange"
      @filters-change="handleFiltersChange"
      @clear-filters="handleClearFilters"
      @advanced-filters-toggle="handleAdvancedToggle"
    />
  </div>
</template>

<script setup lang="ts">
const filterConfigs = [
  {
    key: 'status',
    options: [
      { label: 'Active', value: 'active' },
      { label: 'Inactive', value: 'inactive' },
      { label: 'Pending', value: 'pending' }
    ],
    placeholder: 'All Status'
  },
  {
    key: 'plan',
    options: [
      { label: 'Basic', value: 'basic' },
      { label: 'Pro', value: 'pro' },
      { label: 'Enterprise', value: 'enterprise' }
    ],
    placeholder: 'All Plans'
  }
]

const advancedFilterConfigs = [
  {
    key: 'createdDate',
    type: 'dateRange' as const,
    label: 'Created Date Range'
  },
  {
    key: 'userCount',
    type: 'numberRange' as const,
    label: 'User Count',
    minPlaceholder: 'Min Users',
    maxPlaceholder: 'Max Users'
  },
  {
    key: 'sortBy',
    type: 'select' as const,
    label: 'Sort By',
    placeholder: 'Default',
    options: [
      { label: 'Name A-Z', value: 'name_asc' },
      { label: 'Name Z-A', value: 'name_desc' },
      { label: 'Newest First', value: 'created_desc' },
      { label: 'Oldest First', value: 'created_asc' }
    ]
  }
]

const handleFilterChange = (data: { key: string; value: any }) => {
  console.log('Filter changed:', data.key, data.value)
  // Implement individual filter logic
}

const handleFiltersChange = (filters: Record<string, any>) => {
  console.log('All filters:', filters)
  // Implement bulk filter logic
}

const handleClearFilters = () => {
  console.log('Filters cleared')
  // Reset your data/state
}

const handleAdvancedToggle = (show: boolean) => {
  console.log('Advanced filters toggled:', show)
}
</script>
```

## Props

### GlobalPageHeader Filter Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `showFilters` | `boolean` | `false` | Whether to show the filters component |
| `filterConfigs` | `FilterConfig[]` | `[]` | Configuration for basic dropdown filters |
| `advancedFilterConfigs` | `AdvancedFilterConfig[]` | `[]` | Configuration for advanced filters |
| `initialFilters` | `Record<string, any>` | `{}` | Initial filter values |
| `showAdvancedFilters` | `boolean` | `true` | Whether to show advanced filters toggle |
| `compactFilters` | `boolean` | `false` | Use compact horizontal layout |

### FilterConfig Interface

```typescript
interface FilterConfig {
  key: string;           // Unique identifier for the filter
  options: FilterOption[]; // Available options
  placeholder: string;   // Placeholder text
  class?: string;       // Additional CSS classes
}

interface FilterOption {
  label: string;        // Display text
  value: string | number; // Filter value
}
```

### AdvancedFilterConfig Interface

```typescript
interface AdvancedFilterConfig {
  key: string;                    // Unique identifier
  type: 'dateRange' | 'numberRange' | 'select' | 'custom';
  label: string;                  // Display label
  placeholder?: string;           // For select type
  minPlaceholder?: string;        // For range types
  maxPlaceholder?: string;        // For range types
  options?: FilterOption[];       // For select type
}
```

## Layout Modes

### Compact Mode (`compactFilters: true`)
- Horizontal layout with filters in a single row
- Ideal for pages with limited vertical space
- Filters, advanced toggle, and clear button all inline

### Full Mode (`compactFilters: false`)
- Vertical layout with labeled filters
- Better for complex filtering scenarios
- More space for filter labels and organization

## Advanced Filter Types

### Date Range Filter
```typescript
{
  key: 'createdDate',
  type: 'dateRange',
  label: 'Created Date Range'
}
```
Creates start and end date inputs. Emits value as `{ start: 'YYYY-MM-DD', end: 'YYYY-MM-DD' }`.

### Number Range Filter
```typescript
{
  key: 'userCount',
  type: 'numberRange',
  label: 'User Count',
  minPlaceholder: 'Min Users',
  maxPlaceholder: 'Max Users'
}
```
Creates min and max number inputs. Emits value as `{ min: number, max: number }`.

### Select Filter
```typescript
{
  key: 'category',
  type: 'select',
  label: 'Category',
  placeholder: 'All Categories',
  options: [
    { label: 'Legal', value: 'legal' },
    { label: 'Business', value: 'business' }
  ]
}
```
Creates a dropdown select. Emits the selected value directly.

### Custom Filter
```typescript
{
  key: 'priority',
  type: 'custom',
  label: 'Priority Level'
}
```

Use with custom slot:
```vue
<GlobalPageHeader
  :show-filters="true"
  :advanced-filter-configs="advancedFilterConfigs"
>
  <template #advanced-filter-priority="{ filter, value }">
    <div class="flex gap-2">
      <UiButton
        v-for="priority in priorityOptions"
        :key="priority.value"
        @click="setPriority(priority.value)"
        :variant="value === priority.value ? 'contained' : 'outline'"
        size="sm"
      >
        {{ priority.label }}
      </UiButton>
    </div>
  </template>
</GlobalPageHeader>
```

## Events

| Event | Payload | Description |
|-------|---------|-------------|
| `filter-change` | `{ key: string, value: any }` | Emitted when a single filter changes |
| `filters-change` | `Record<string, any>` | Emitted with all current filter values |
| `clear-filters` | `void` | Emitted when filters are cleared |
| `advanced-filters-toggle` | `boolean` | Emitted when advanced panel is toggled |

## Styling

The component uses Tailwind CSS classes and follows the design system:
- Consistent spacing and typography
- Proper focus states and accessibility
- Smooth transitions and animations
- Dark mode support throughout

## Best Practices

1. **Keep basic filters simple**: Use for the most common filtering needs
2. **Use advanced filters for complex scenarios**: Date ranges, number ranges, etc.
3. **Provide clear labels**: Make filter purposes obvious to users
4. **Handle empty states**: Gracefully handle when no filters are active
5. **Debounce API calls**: Don't make requests on every filter change
6. **Persist filter state**: Consider saving filter preferences

## Example Implementation

See `pages/dashboard/test-filters.vue` for a complete working example with all filter types and event handling.
