<!--
  Template Create Form Component
  
  Form for creating new legal document templates
-->

<template>
  <div class="max-w-4xl mx-auto space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">Create New Template</h1>
        <p class="mt-1 text-sm text-gray-500">
          Create a reusable legal document template
        </p>
      </div>
      
      <NuxtLink
        to="/dashboard/templates"
        class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
      >
        <Icon name="heroicons:arrow-left" class="h-4 w-4 mr-2" />
        Back to Templates
      </NuxtLink>
    </div>

    <!-- Form -->
    <form @submit.prevent="handleSubmit" class="space-y-6">
      <!-- Template Information -->
      <div class="bg-white shadow rounded-lg p-6">
        <h2 class="text-lg font-medium text-gray-900 mb-6">{{ t('templates.templateInformation') }}</h2>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Template Name -->
          <div class="md:col-span-2">
            <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
              {{ t('templates.templateName') }} *
            </label>
            <input
              id="name"
              v-model="form.name"
              type="text"
              required
              class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              placeholder="Enter template name"
            />
          </div>

          <!-- Category -->
          <div>
            <label for="category" class="block text-sm font-medium text-gray-700 mb-2">
              Category *
            </label>
            <select
              id="category"
              v-model="form.category"
              required
              class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
            >
              <option value="">Select category</option>
              <option value="contract">Contract</option>
              <option value="legal-brief">Legal Brief</option>
              <option value="correspondence">Correspondence</option>
              <option value="form">Form</option>
              <option value="agreement">Agreement</option>
              <option value="other">Other</option>
            </select>
          </div>

          <!-- Status -->
          <div>
            <label for="status" class="block text-sm font-medium text-gray-700 mb-2">
              Status *
            </label>
            <select
              id="status"
              v-model="form.status"
              required
              class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
            >
              <option value="draft">Draft</option>
              <option value="active">Active</option>
            </select>
          </div>

          <!-- Description -->
          <div class="md:col-span-2">
            <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
              Description
            </label>
            <textarea
              id="description"
              v-model="form.description"
              rows="3"
              class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              placeholder="Brief description of the template"
            ></textarea>
          </div>
        </div>
      </div>

      <!-- Template Content -->
      <div class="bg-white shadow rounded-lg p-6">
        <h2 class="text-lg font-medium text-gray-900 mb-6">Template Content</h2>
        
        <div class="space-y-4">
          <!-- Content Editor -->
          <div>
            <label for="content" class="block text-sm font-medium text-gray-700 mb-2">
              {{ t('templates.templateContent') }} *
            </label>
            <textarea
              id="content"
              v-model="form.content"
              rows="20"
              required
              class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm font-mono"
              placeholder="Enter your template content here. Use {{variable_name}} for dynamic content."
            ></textarea>
            <p class="mt-2 text-sm text-gray-500">
              Use double curly braces for variables: {{client_name}}, {{date}}, {{amount}}, etc.
            </p>
          </div>

          <!-- Variables -->
          <div>
            <label for="variables" class="block text-sm font-medium text-gray-700 mb-2">
              Template Variables
            </label>
            <input
              id="variables"
              v-model="form.variables"
              type="text"
              class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              placeholder="client_name, date, amount, address"
            />
            <p class="mt-1 text-sm text-gray-500">
              Comma-separated list of variables used in this template
            </p>
          </div>
        </div>
      </div>

      <!-- Form Actions -->
      <div class="flex justify-end space-x-3">
        <NuxtLink
          to="/dashboard/templates"
          class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
        >
          Cancel
        </NuxtLink>
        
        <button
          type="submit"
          :disabled="isSubmitting"
          class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <UiSpinner v-if="isSubmitting" size="sm" color="white" class="mr-2" />
          <Icon v-else name="heroicons:plus" class="h-4 w-4 mr-2" />
          {{ isSubmitting ? 'Creating...' : 'Create Template' }}
        </button>
      </div>
    </form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'

// Composables
const router = useRouter()
const { t } = useI18n()

// State
const isSubmitting = ref(false)

const form = reactive({
  name: '',
  category: '',
  status: 'draft',
  description: '',
  content: '',
  variables: ''
})

// Methods
const handleSubmit = async () => {
  try {
    isSubmitting.value = true
    
    // Validate form
    if (!form.name || !form.category || !form.content) {
      alert('Please fill in all required fields')
      return
    }
    
    // Create template data
    const templateData = {
      ...form,
      variables: form.variables ? form.variables.split(',').map(v => v.trim()) : [],
      createdAt: new Date().toISOString()
    }
    
    // TODO: Replace with actual API call
    console.log('Creating template:', templateData)
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // Success - redirect to templates list
    router.push('/dashboard/templates')
    
  } catch (error) {
    console.error('Error creating template:', error)
    alert('Failed to create template. Please try again.')
  } finally {
    isSubmitting.value = false
  }
}
</script>
