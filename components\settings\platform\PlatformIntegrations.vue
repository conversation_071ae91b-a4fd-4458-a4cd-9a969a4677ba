<template>
  <div class="space-y-6">
    <div>
      <h2 class="text-md font-semibold text-gray-800 dark:text-gray-100 mb-4">Third-Party Integrations</h2>
      <p class="text-sm text-gray-600 dark:text-gray-400">Enable/disable and configure integrations like payment gateways, calendar sync, document storage, and communication tools.</p>
    </div>

    <!-- Calendar Integration Section -->
    <UiCard title="Calendar Integration">
      <div class="space-y-4">
        <UiToggle v-model="calendar.google.enabled" label="Enable Google Calendar Sync" />
        <div v-if="calendar.google.enabled" class="ml-6 space-y-2 border-l pl-4">
          <label for="googleCalendarApiKey" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Google Calendar API Key</label>
          <UiInput id="googleCalendarApiKey" :name="'googleCalendarApiKey'" v-model="calendar.google.apiKey" type="password" placeholder="Enter Google Calendar API Key" class="mt-1" />
        </div>

        <UiToggle v-model="calendar.outlook.enabled" label="Enable Outlook Calendar Sync" />
        <div v-if="calendar.outlook.enabled" class="ml-6 space-y-2 border-l pl-4">
          <label for="outlookCalendarApiKey" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Outlook Calendar API Key</label>
          <UiInput id="outlookCalendarApiKey" :name="'outlookCalendarApiKey'" v-model="calendar.outlook.apiKey" type="password" placeholder="Enter Outlook Calendar API Key" class="mt-1" />
        </div>
      </div>
    </UiCard>

    <!-- Document Storage Integration Section -->
    <UiCard title="Document Storage Integration">
      <div class="space-y-4">
        <UiToggle v-model="documentStorage.googleDrive.enabled" label="Enable Google Drive Integration" />
        <div v-if="documentStorage.googleDrive.enabled" class="ml-6 space-y-2 border-l pl-4">
          <label for="googleDriveApiKey" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Google Drive API Key</label>
          <UiInput id="googleDriveApiKey" :name="'googleDriveApiKey'" v-model="documentStorage.googleDrive.apiKey" type="password" placeholder="Enter Google Drive API Key" class="mt-1" />
        </div>

        <UiToggle v-model="documentStorage.dropbox.enabled" label="Enable Dropbox Integration" />
        <div v-if="documentStorage.dropbox.enabled" class="ml-6 space-y-2 border-l pl-4">
          <label for="dropboxApiKey" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Dropbox API Key</label>
          <UiInput id="dropboxApiKey" :name="'dropboxApiKey'" v-model="documentStorage.dropbox.apiKey" type="password" placeholder="Enter Dropbox API Key" class="mt-1" />
        </div>

        <UiToggle v-model="documentStorage.oneDrive.enabled" label="Enable OneDrive Integration" />
        <div v-if="documentStorage.oneDrive.enabled" class="ml-6 space-y-2 border-l pl-4">
          <label for="oneDriveApiKey" class="block text-sm font-medium text-gray-700 dark:text-gray-300">OneDrive API Key</label>
          <UiInput id="oneDriveApiKey" :name="'oneDriveApiKey'" v-model="documentStorage.oneDrive.apiKey" type="password" placeholder="Enter OneDrive API Key" class="mt-1" />
        </div>
      </div>
    </UiCard>

    <!-- Payment Gateway Integration Section -->
    <UiCard title="Payment Gateway Integration">
      <div class="space-y-4">
        <UiToggle v-model="paymentGateway.stripe.enabled" label="Enable Stripe Integration" />
        <div v-if="paymentGateway.stripe.enabled" class="ml-6 space-y-2 border-l pl-4">
          <div>
            <label for="stripePublishableKey" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Stripe Publishable Key</label>
            <UiInput id="stripePublishableKey" :name="'stripePublishableKey'" v-model="paymentGateway.stripe.publishableKey" type="text" placeholder="pk_live_..." class="mt-1" />
          </div>
          <div>
            <label for="stripeSecretKey" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Stripe Secret Key</label>
            <UiInput id="stripeSecretKey" :name="'stripeSecretKey'" v-model="paymentGateway.stripe.secretKey" type="password" placeholder="sk_live_..." class="mt-1" />
          </div>
        </div>

        <UiToggle v-model="paymentGateway.paypal.enabled" label="Enable PayPal Integration" />
        <div v-if="paymentGateway.paypal.enabled" class="ml-6 space-y-2 border-l pl-4">
          <div>
            <label for="paypalClientId" class="block text-sm font-medium text-gray-700 dark:text-gray-300">PayPal Client ID</label>
            <UiInput id="paypalClientId" :name="'paypalClientId'" v-model="paymentGateway.paypal.clientId" type="text" placeholder="Enter PayPal Client ID" class="mt-1" />
          </div>
          <div>
            <label for="paypalClientSecret" class="block text-sm font-medium text-gray-700 dark:text-gray-300">PayPal Client Secret</label>
            <UiInput id="paypalClientSecret" :name="'paypalClientSecret'" v-model="paymentGateway.paypal.clientSecret" type="password" placeholder="Enter PayPal Client Secret" class="mt-1" />
          </div>
        </div>
      </div>
    </UiCard>

     <!-- Communication Integration Section -->
    <UiCard title="Communication Integration">
      <div class="space-y-4">
        <UiToggle v-model="communication.slack.enabled" label="Enable Slack Notifications" />
        <div v-if="communication.slack.enabled" class="ml-6 space-y-2 border-l pl-4">
          <label for="slackWebhookUrl" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Slack Webhook URL</label>
          <UiInput id="slackWebhookUrl" :name="'slackWebhookUrl'" v-model="communication.slack.webhookUrl" type="url" placeholder="https://hooks.slack.com/services/..." class="mt-1" />
        </div>

        <UiToggle v-model="communication.teams.enabled" label="Enable Microsoft Teams Notifications" />
        <div v-if="communication.teams.enabled" class="ml-6 space-y-2 border-l pl-4">
          <label for="teamsWebhookUrl" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Microsoft Teams Webhook URL</label>
          <UiInput id="teamsWebhookUrl" :name="'teamsWebhookUrl'" v-model="communication.teams.webhookUrl" type="url" placeholder="Enter MS Teams Webhook URL" class="mt-1" />
        </div>
      </div>
    </UiCard>

  </div>
</template>

<script setup lang="ts">
import { reactive } from 'vue';
import UiCard from '~/components/ui/UiCard.vue';
import UiToggle from '~/components/ui/UiToggle.vue';
import UiInput from '~/components/ui/UiInput.vue';

// Calendar Integration
const calendar = reactive({
  google: { enabled: false, apiKey: '' },
  outlook: { enabled: false, apiKey: '' },
});

// Document Storage Integration
const documentStorage = reactive({
  googleDrive: { enabled: false, apiKey: '' },
  dropbox: { enabled: false, apiKey: '' },
  oneDrive: { enabled: false, apiKey: '' },
});

// Payment Gateway Integration
const paymentGateway = reactive({
  stripe: { enabled: false, publishableKey: '', secretKey: '' },
  paypal: { enabled: false, clientId: '', clientSecret: '' },
});

// Communication Integration
const communication = reactive({
  slack: { enabled: false, webhookUrl: '' },
  teams: { enabled: false, webhookUrl: '' },
});

// TODO: Load initial values from backend and implement save logic for these settings
</script>
