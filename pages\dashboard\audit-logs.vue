<template>
  <div>
    <UiTable
      :headers="columns"
      :items="auditLogs"
      :loading="isLoading"
      itemKey="id"
      :initialSort="initialSortValue"
      title="Audit Logs"
      @sort="handleSort"
    >
      <template #header-search-filter-area>
        <div class="flex items-center space-x-2">
           <!-- TODO: Implement Advanced Filters -->
          <UiButton variant="outline" @click="toggleFilterModal">
             <Icon name="material-symbols:filter-alt" class="h-5 w-5 mr-1" /> 
            Filters
          </UiButton>
          <UiInput
            id="auditLogSearch"
            name="auditLogSearch"
            v-model="searchInputText"
            placeholder="Search logs (e.g., action, user, IP)"
            class="w-full sm:w-72"
          />
         
        </div>
      </template>

      <template #header-actions>
        <div class="flex items-center space-x-2">
          <UiButton @click="fetchLogs" :disabled="isLoading" variant="outline">
            <IconCheckCircle v-if="!isLoading" class="h-5 w-5 mr-1" />
            <UiSpinner v-else class="h-5 w-5 mr-1" />
            Refresh
          </UiButton>
          <!-- No create action for audit logs -->
        </div>
      </template>

      <template #item.actions="{ item }">
        <UiButton size="sm" variant="ghost" @click="showLogDetails(item as AuditLog)" class="text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300 font-medium">
          Details
        </UiButton>
      </template>

      <template #item.actorId="{ item }">
        <span v-if="item.actorId">{{ item.actorEmail || item.actorId }}</span>
        <span v-else class="text-gray-400 dark:text-gray-500">System</span>
      </template>

      <template #item.createdAt="{ item }">
        <span>{{ formatters.dateTime(item.createdAt) }}</span>
      </template>
      
      <template #item.before="{ item }">
        <pre class="max-w-xs truncate text-xs">{{ item.before ? JSON.stringify(item.before) : '-' }}</pre>
      </template>

      <template #item.after="{ item }">
        <pre class="max-w-xs truncate text-xs">{{ item.after ? JSON.stringify(item.after) : '-' }}</pre>
      </template>

      <template #footer>
        <div v-if="auditLogsMeta && auditLogsMeta.total > 0" class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-700 dark:text-gray-300">
              Showing
              <span class="font-medium">{{ (currentPage - 1) * itemsPerPage + 1 }}</span>
              to
              <span class="font-medium">{{ Math.min(currentPage * itemsPerPage, auditLogsMeta.total) }}</span>
              of
              <span class="font-medium">{{ auditLogsMeta.total }}</span>
              results
            </p>
          </div>
          <div>
            <nav class="isolate inline-flex -space-x-px rounded-md shadow-sm" aria-label="Pagination">
              <UiButton
                @click="prevPage"
                :disabled="currentPage === 1 || isLoading"
                variant="outline"
                class="relative inline-flex items-center rounded-l-md px-2 py-2 !text-gray-400 dark:!text-gray-500 ring-1 ring-inset ring-gray-300 dark:ring-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700 focus:z-20 focus:outline-offset-0"
              >
                <span class="sr-only">Previous</span>
                <
              </UiButton>
              <UiButton
                @click="nextPage"
                :disabled="!auditLogsMeta || currentPage * itemsPerPage >= auditLogsMeta.total || isLoading"
                variant="outline"
                class="relative inline-flex items-center rounded-r-md px-2 py-2 !text-gray-400 dark:!text-gray-500 ring-1 ring-inset ring-gray-300 dark:ring-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700 focus:z-20 focus:outline-offset-0"
              >
                <span class="sr-only">Next</span>
                >
              </UiButton>
            </nav>
          </div>
        </div>
         <div v-else-if="!isLoading" class="text-sm text-gray-500 dark:text-gray-400">
            No audit logs found.
          </div>
      </template>
    </UiTable>

    <!-- Modal for Log Details -->
    <UiModal :show="isLogDetailModalOpen" @update:show="isLogDetailModalOpen = $event" title="Audit Log Details" size="2xl">
      <div v-if="selectedLog" class="space-y-4">
        <div><strong>ID:</strong> {{ selectedLog.id }}</div>
        <div><strong>Timestamp:</strong> {{ formatters.dateTime(selectedLog.createdAt) }}</div>
        <div><strong>Actor:</strong> {{ selectedLog.actorEmail || selectedLog.actorId || 'System' }}</div>
        <div v-if="selectedLog.actorRole"><strong>Actor Role:</strong> {{ selectedLog.actorRole }}</div>
        <div><strong>Action:</strong> {{ selectedLog.action }}</div>
        <div v-if="selectedLog.entityName"><strong>Entity Name:</strong> {{ selectedLog.entityName }}</div>
        <div v-if="selectedLog.entityId"><strong>Entity ID:</strong> {{ selectedLog.entityId }}</div>
        <div v-if="selectedLog.ipAddress"><strong>IP Address:</strong> {{ selectedLog.ipAddress }}</div>
        <div v-if="selectedLog.tenantId"><strong>Tenant ID:</strong> {{ selectedLog.tenantId }}</div>
        
        <div v-if="parsedDescription(selectedLog.description)">
          <strong>Details:</strong>
          <pre class="bg-gray-100 dark:bg-gray-800 p-2 rounded text-xs overflow-auto">{{ JSON.stringify(parsedDescription(selectedLog.description), null, 2) }}</pre>
        </div>
        <div v-else-if="selectedLog.description"><strong>Description:</strong> {{ selectedLog.description }}</div>
        
        <div v-if="selectedLog.before">
          <strong>Before:</strong>
          <pre class="bg-gray-100 dark:bg-gray-800 p-2 rounded text-xs overflow-auto">{{ JSON.stringify(selectedLog.before, null, 2) }}</pre>
        </div>
        <div v-if="selectedLog.after">
          <strong>After:</strong>
          <pre class="bg-gray-100 dark:bg-gray-800 p-2 rounded text-xs overflow-auto">{{ JSON.stringify(selectedLog.after, null, 2) }}</pre>
        </div>
         <div v-if="selectedLog.userAgent">
          <strong>User Agent:</strong>
          <pre class="bg-gray-100 dark:bg-gray-800 p-2 rounded text-xs overflow-auto">{{ selectedLog.userAgent }}</pre>
        </div>
      </div>
      <template #footer>
        <UiButton @click="isLogDetailModalOpen = false">Close</UiButton>
      </template>
    </UiModal>

    <!-- TODO: Advanced Filter Modal -->
    <UiModal :show="isFilterModalOpen" @update:show="isFilterModalOpen = $event" title="Filter Audit Logs" size="lg">
        <p>Advanced filter options will be here.</p>
        <!-- Form fields for filtering by date range, user, action, etc. -->
        <template #footer>
            <UiButton @click="applyFilters" variant="primary">Apply Filters</UiButton>
            <UiButton @click="isFilterModalOpen = false">Cancel</UiButton>
        </template>
    </UiModal>

  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue';
import { storeToRefs } from 'pinia';
import IconCheckCircle from '~/components/icons/IconCheckCircle.vue';
import { useAuditLogStore, type AuditLog, type GetRequestQuery } from '~/stores/auditLog';
import { formatters } from '~/utils/formatters'; // Assuming you have this
import { PlatformRoles, TenantRoles } from'~/app/features/auth/constants/roles';

definePageMeta({
  title: 'Audit Logs',
  subtitle: 'Review system and user activities.',
  meta: {
    title: 'Audit Logs',
    description: 'Review system and user activities across the platform.',
  },
  layout: 'dashboard',
  middleware: ['rbac'], // Ensure this middleware handles role checks
  roles: [PlatformRoles.SUPER_ADMIN, PlatformRoles.SUPPORT], // Example roles, adjust as needed
  breadcrumb: [
    { label: 'Dashboard', href: '/dashboard' },
    { label: 'Platform', href: '/dashboard/platform' }, // Assuming a parent platform page
    { label: 'Audit Logs' },
  ],
});

const auditLogStore = useAuditLogStore();
const { isLoading, auditLogsMeta } = storeToRefs(auditLogStore);

const auditLogs = computed(() => auditLogStore.auditLogs as AuditLog[]);

// State for query parameters
const searchInputText = ref('');
const searchQuery = ref(''); // Debounced value
const currentPage = ref(auditLogsMeta.value?.page || 1);
const itemsPerPage = ref(auditLogsMeta.value?.limit || 15); // Default items per page
const sortBy = ref<string | undefined>('createdAt');
const sortOrder = ref<'ASC' | 'DESC' | undefined>('DESC');

// Advanced filter states
const filterActorId = ref<string | undefined>(undefined);
const filterAction = ref<string | undefined>(undefined);
const filterEntityName = ref<string | undefined>(undefined);
const filterStartDate = ref<string | undefined>(undefined);
const filterEndDate = ref<string | undefined>(undefined);

const isLogDetailModalOpen = ref(false);
const selectedLog = ref<AuditLog | null>(null);

const isFilterModalOpen = ref(false);

const initialSortValue = computed<SortState>(() => ({
  key: sortBy.value || null,
  direction: sortOrder.value ? sortOrder.value.toLowerCase() as 'asc' | 'desc' : null,
}));

const currentQuery = computed<GetRequestQuery>(() => {
  const query: GetRequestQuery = {
    page: currentPage.value,
    limit: itemsPerPage.value,
    search: searchQuery.value || undefined,
    actorId: filterActorId.value || undefined,
    action: filterAction.value || undefined,
    entityName: filterEntityName.value || undefined,
    startDate: filterStartDate.value || undefined,
    endDate: filterEndDate.value || undefined,
  };
  if (sortBy.value && sortOrder.value) {
    query.sort = `${sortBy.value}:${sortOrder.value}`;
  }
  return query;
});

const fetchLogs = async () => {
  try {
    await auditLogStore.fetchAllAuditLogs(currentQuery.value);
  } catch (error) {
    console.error('Error fetching audit logs:', error);
    // TODO: Show toast notification for the error
  }
};

let debounceTimer: NodeJS.Timeout;
watch(searchInputText, (newValue) => {
  clearTimeout(debounceTimer);
  debounceTimer = setTimeout(() => {
    searchQuery.value = newValue;
    currentPage.value = 1; 
  }, 500);
});

watch(currentQuery, fetchLogs, { deep: true });

onMounted(() => {
  if (auditLogsMeta.value) {
    currentPage.value = auditLogsMeta.value.page;
    itemsPerPage.value = auditLogsMeta.value.limit;
  }
  fetchLogs();
});

const handleSort = (payload: SortState) => {
  sortBy.value = payload.key || undefined;
  sortOrder.value = payload.direction ? payload.direction.toUpperCase() as 'ASC' | 'DESC' : undefined;
  currentPage.value = 1; // Reset to first page on sort change
};

const nextPage = () => {
  if (auditLogsMeta.value && currentPage.value * itemsPerPage.value < auditLogsMeta.value.total) {
    currentPage.value++;
  }
};

const prevPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--;
  }
};

const showLogDetails = (log: AuditLog) => {
  selectedLog.value = log;
  isLogDetailModalOpen.value = true;
};

const toggleFilterModal = () => {
    isFilterModalOpen.value = !isFilterModalOpen.value;
};

const applyFilters = () => {
    // Logic to apply filters from modal inputs to ref values (filterActorId, filterAction, etc.)
    // For example:
    // filterActorId.value = valueFromModalInputForActorId;
    // filterAction.value = valueFromModalInputForAction;
    // ...
    currentPage.value = 1; // Reset to first page when filters change
    fetchLogs(); // Re-fetch with new filters
    isFilterModalOpen.value = false; // Close modal
};

const parsedDescription = (description: string | null) => {
  if (!description) return null;
  try {
    return JSON.parse(description);
  } catch (e) {
    // If it's not a valid JSON string, return null or the original string
    // console.warn("Description is not a valid JSON string:", description);
    return null;
  }
};

const columns = computed((): TableHeader[] => [
  { key: 'createdAt', label: 'Timestamp', sortable: true, class: 'w-1/6' },
  { key: 'actor.name', label: 'Actor', sortable: true, class: 'w-1/6' }, // Changed from userId
  { key: 'action', label: 'Action', sortable: true, class: 'w-1/5' },
  { key: 'entityName', label: 'Entity Name', sortable: true, class: 'w-1/6' }, // Changed from entityType
  { key: 'entityId', label: 'Entity ID', sortable: false, class: 'w-1/6' },
  { key: 'ipAddress', label: 'IP Address', sortable: true, class: 'w-1/6' },
  // { key: 'description', label: 'Description', sortable: false, class: 'w-1/4' },
  // { key: 'before', label: 'Before', sortable: false, class: 'w-1/4' }, // Changed from oldValues
  // { key: 'after', label: 'After', sortable: false, class: 'w-1/4' }, // Changed from newValues
  { key: 'actions', label: 'Details', class: 'text-center w-[10%]', cellClass: 'text-center' },
]);
</script>

<style scoped>
/* Add any page-specific styles here */
pre {
  white-space: pre-wrap; /* CSS3 */
  white-space: -moz-pre-wrap; /* Mozilla, since 1999 */
  white-space: -pre-wrap; /* Opera 4-6 */
  white-space: -o-pre-wrap; /* Opera 7 */
  word-wrap: break-word; /* Internet Explorer 5.5+ */
}
</style>