<template>
  <div class="document-editor-footer" :class="footerClasses">
    <!-- Left Section: Document Stats -->
    <div class="footer-left">
      <div class="footer-stats">
        <!-- Page Count -->
        <button class="footer-button" @click="showPageDialog = true">
          <Icon name="heroicons:document-text" size="calc(var(--spacing) * 4)" class=" " />
          <span>{{ t('documents.editor.footer.page') }} {{ currentPage }} {{ t('documents.editor.footer.of') }} {{ totalPages }}</span>
        </button>

        <!-- Word Count -->
        <button class="footer-button" @click="showWordCountDialog = true">
          <Icon name="heroicons:document-text" size="calc(var(--spacing) * 4)" class=" " />
          <span>{{ wordCount }} {{ t('documents.editor.footer.words') }}</span>
        </button>

        <!-- Character Count -->
        <span class="footer-stat">
          {{ characterCount }} {{ t('documents.editor.footer.characters') }}
        </span>
      </div>
    </div>

    <!-- Center Section: Status Indicators -->
    <div class="footer-center">
      <!-- Proofing Status -->
      <button class="footer-button" @click="runSpellCheck" :title="t('documents.editor.footer.proofing')">
        <Icon name="heroicons:check-circle" size="calc(var(--spacing) * 4)" class=" text-green-500" />
      </button>

      <!-- Language -->
      <div class="w-36">
        <UiSelect
          id="language-select"
          v-model="currentLanguage"
          :options="languageOptions"
          :leading-icon="'heroicons:language'"
          @update:modelValue="setLanguage"
          size="sm"
        />
      </div>
    </div>

    <!-- Right Section: View Controls -->
    <div class="footer-right">
      <!-- View Mode -->
      <div class="view-controls">
        <button
          v-for="view in viewModes"
          :key="view.key"
          @click="setViewMode(view.key)"
          :class="[
            'view-button',
            { 'active': currentViewMode === view.key }
          ]"
          :title="t(`documents.editor.footer.views.${view.key}`)"
        >
          <Icon :name="view.icon" size="calc(var(--spacing) * 4)" class=" " />
        </button>
      </div>

      <!-- Zoom Controls -->
      <div class="zoom-controls">
        <button class="zoom-button" @click="zoomOut" :disabled="zoomLevel <= 25">
          <Icon name="heroicons:minus" size="calc(var(--spacing) * 4)" class=" " />
        </button>

        <UiDropdown align="right" width="auto">
          <template #trigger>
            <button class="zoom-display">
              {{ zoomLevel }}%
            </button>
          </template>
          <div class="zoom-dropdown">
            <button
              v-for="zoom in zoomPresets"
              :key="zoom"
              @click="setZoom(zoom)"
              :class="[
                'zoom-option',
                { 'active': zoomLevel === zoom }
              ]"
            >
              {{ zoom }}%
            </button>
            <div class="zoom-separator"></div>
            <button class="zoom-option" @click="fitToWidth">
              {{ t('documents.editor.footer.fitToWidth') }}
            </button>
            <button class="zoom-option" @click="fitToPage">
              {{ t('documents.editor.footer.fitToPage') }}
            </button>
          </div>
        </UiDropdown>

        <button class="zoom-button" @click="zoomIn" :disabled="zoomLevel >= 500">
          <Icon name="heroicons:plus" size="calc(var(--spacing) * 4)" class=" " />
        </button>
      </div>

      <!-- Zoom Slider -->
      <div class="zoom-slider-container">
        <input
          type="range"
          v-model="zoomLevel"
          min="25"
          max="500"
          step="25"
          class="zoom-slider"
          @input="onZoomSliderChange"
        />
      </div>
    </div>

    <!-- Page Dialog -->
    <div v-if="showPageDialog" class="page-dialog-overlay" @click="showPageDialog = false">
      <div class="page-dialog" @click.stop>
        <h3 class="dialog-title">{{ t('documents.editor.footer.pageNavigation') }}</h3>
        <div class="dialog-content">
          <label class="dialog-label">{{ t('documents.editor.footer.goToPage') }}:</label>
          <input
            type="number"
            v-model="targetPage"
            :min="1"
            :max="totalPages"
            class="page-input"
            @keyup.enter="goToPage"
          />
        </div>
        <div class="dialog-actions">
          <UiButton variant="outline" size="sm" @click="showPageDialog = false">
            {{ t('common.cancel') }}
          </UiButton>
          <UiButton variant="contained" size="sm" @click="goToPage">
            {{ t('common.ok') }}
          </UiButton>
        </div>
      </div>
    </div>

    <!-- Word Count Dialog -->
    <div v-if="showWordCountDialog" class="word-count-dialog-overlay" @click="showWordCountDialog = false">
      <div class="word-count-dialog" @click.stop>
        <h3 class="dialog-title">{{ t('documents.editor.footer.wordCount') }}</h3>
        <div class="word-count-stats">
          <div class="stat-row">
            <span class="stat-label">{{ t('documents.editor.footer.pages') }}:</span>
            <span class="stat-value">{{ totalPages }}</span>
          </div>
          <div class="stat-row">
            <span class="stat-label">{{ t('documents.editor.footer.words') }}:</span>
            <span class="stat-value">{{ wordCount }}</span>
          </div>
          <div class="stat-row">
            <span class="stat-label">{{ t('documents.editor.footer.charactersWithSpaces') }}:</span>
            <span class="stat-value">{{ characterCount }}</span>
          </div>
          <div class="stat-row">
            <span class="stat-label">{{ t('documents.editor.footer.charactersWithoutSpaces') }}:</span>
            <span class="stat-value">{{ characterCountNoSpaces }}</span>
          </div>
          <div class="stat-row">
            <span class="stat-label">{{ t('documents.editor.footer.paragraphs') }}:</span>
            <span class="stat-value">{{ paragraphCount }}</span>
          </div>
          <div class="stat-row">
            <span class="stat-label">{{ t('documents.editor.footer.lines') }}:</span>
            <span class="stat-value">{{ lineCount }}</span>
          </div>
        </div>
        <div class="dialog-actions">
          <UiButton variant="contained" size="sm" @click="showWordCountDialog = false">
            {{ t('common.close') }}
          </UiButton>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// Props
interface Props {
  editor?: any
  wordCount?: number
  characterCount?: number
  currentPage?: number
  totalPages?: number
}

const props = withDefaults(defineProps<Props>(), {
  wordCount: 0,
  characterCount: 0,
  currentPage: 1,
  totalPages: 1
})

// Emits
const emit = defineEmits<{
  'zoom-change': [zoom: number]
  'view-change': [view: string]
  'language-change': [language: string]
  'go-to-page': [page: number]
  'spell-check': []
}>()

// Composables
const { t, locale, setLocale } = useI18n()

// RTL languages
const rtlLanguages = ['he', 'ar']
const isRTL = computed(() => rtlLanguages.includes(locale.value))

// Reactive state
const showPageDialog = ref(false)
const showWordCountDialog = ref(false)
const targetPage = ref(1)
const zoomLevel = ref(100)
const currentViewMode = ref('print')
const currentLanguage = ref('en')

// Available languages
const availableLanguages = [
  { code: 'en', name: 'English', nativeName: 'English', flag: '🇺🇸' },
  { code: 'he', name: 'Hebrew', nativeName: 'עברית', flag: '🇮🇱' },
  { code: 'ar', name: 'Arabic', nativeName: 'العربية', flag: '🇸🇦' }
]

// View modes
const viewModes = [
  { key: 'print', icon: 'heroicons:document-text' },
  { key: 'web', icon: 'heroicons:globe-alt' },
  { key: 'outline', icon: 'heroicons:list-bullet' },
  { key: 'draft', icon: 'heroicons:pencil-square' }
]

// Zoom presets
const zoomPresets = [25, 50, 75, 100, 125, 150, 200, 300, 400, 500]

// Computed properties
const footerClasses = computed(() => ({
  'rtl': isRTL.value,
  'ltr': !isRTL.value
}))

const languageOptions = computed(() => {
  return availableLanguages.map(lang => ({
    value: lang.code,
    label: lang.name,
  }));
});

const characterCountNoSpaces = computed(() => {
  if (props.editor) {
    const text = props.editor.getText()
    return text.replace(/\s/g, '').length
  }
  return props.characterCount.toString().replace(/\s/g, '').length
})

const paragraphCount = computed(() => {
  if (props.editor) {
    const text = props.editor.getText()
    return text.split(/\n\s*\n/).filter(p => p.trim().length > 0).length
  }
  return 1
})

const lineCount = computed(() => {
  if (props.editor) {
    const text = props.editor.getText()
    return text.split('\n').length
  }
  return 1
})

// Methods
const setLanguage = (languageCode: string) => {
  currentLanguage.value = languageCode
  setLocale(languageCode)
  emit('language-change', languageCode)
}

const setViewMode = (mode: string) => {
  currentViewMode.value = mode
  emit('view-change', mode)
}

const setZoom = (zoom: number) => {
  zoomLevel.value = zoom
  emit('zoom-change', zoom)
}

const zoomIn = () => {
  const currentIndex = zoomPresets.findIndex(z => z >= zoomLevel.value)
  if (currentIndex < zoomPresets.length - 1) {
    setZoom(zoomPresets[currentIndex + 1])
  }
}

const zoomOut = () => {
  const currentIndex = zoomPresets.findIndex(z => z >= zoomLevel.value)
  if (currentIndex > 0) {
    setZoom(zoomPresets[currentIndex - 1])
  }
}

const onZoomSliderChange = (event: Event) => {
  const target = event.target as HTMLInputElement
  setZoom(parseInt(target.value))
}

const fitToWidth = () => {
  // Calculate zoom to fit width
  setZoom(100) // Placeholder
}

const fitToPage = () => {
  // Calculate zoom to fit page
  setZoom(75) // Placeholder
}

const goToPage = () => {
  if (targetPage.value >= 1 && targetPage.value <= props.totalPages) {
    emit('go-to-page', targetPage.value)
    showPageDialog.value = false
  }
}

const runSpellCheck = () => {
  emit('spell-check')
}

// Initialize
onMounted(() => {
  currentLanguage.value = locale.value
  targetPage.value = props.currentPage
})

// Watch for prop changes
watch(() => props.currentPage, (newPage) => {
  targetPage.value = newPage
})
</script>

<style scoped>
@reference '~/assets/css/tailwind.css';

.document-editor-footer {
  @apply flex items-center justify-between px-4 py-2 bg-gray-50 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 text-sm flex-1;
  min-height: 32px;
}

.footer-left,
.footer-center,
.footer-right {
  @apply flex items-center gap-2;
}

.footer-left {
  @apply flex-1;
}

.footer-center {
  @apply flex-shrink-0;
}

.footer-right {
  @apply flex-shrink-0 gap-3;
}

.footer-stats {
  @apply flex items-center gap-4;
}

.footer-button {
  @apply flex items-center gap-1 px-2 py-1 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors duration-200 cursor-pointer;
}

.footer-stat {
  @apply text-gray-600 dark:text-gray-400;
}


/* View Controls */
.view-controls {
  @apply flex items-center border border-gray-300 dark:border-gray-600 rounded;
}

.view-button {
  @apply p-1.5 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200;
}

.view-button.active {
  @apply bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400;
}

.view-button:not(:last-child) {
  @apply border-r border-gray-300 dark:border-gray-600;
}

/* Zoom Controls */
.zoom-controls {
  @apply flex items-center border border-gray-300 dark:border-gray-600 rounded;
}

.zoom-button {
  @apply p-1.5 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed;
}

.zoom-display {
  @apply px-3 py-1.5 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200 min-w-16 text-center border-x border-gray-300 dark:border-gray-600;
}

.zoom-dropdown {
  @apply py-2 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg min-w-32;
}

.zoom-option {
  @apply block px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 w-full text-left transition-colors duration-200;
}

.zoom-option.active {
  @apply bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400;
}

.zoom-separator {
  @apply border-t border-gray-200 dark:border-gray-700 my-1;
}

/* Zoom Slider */
.zoom-slider-container {
  @apply flex items-center;
}

.zoom-slider {
  @apply w-20 h-1 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer;
}

.zoom-slider::-webkit-slider-thumb {
  @apply appearance-none w-3 h-3 bg-blue-500 rounded-full cursor-pointer;
}

.zoom-slider::-moz-range-thumb {
  @apply w-3 h-3 bg-blue-500 rounded-full cursor-pointer border-0;
}

/* Dialogs */
.page-dialog-overlay,
.word-count-dialog-overlay {
  @apply fixed inset-0 bg-black/50 flex items-center justify-center z-50;
}

.page-dialog,
.word-count-dialog {
  @apply bg-white dark:bg-gray-800 rounded-lg shadow-xl p-6 max-w-md w-full mx-4;
}

.dialog-title {
  @apply text-lg font-semibold text-gray-900 dark:text-white mb-4;
}

.dialog-content {
  @apply mb-6;
}

.dialog-label {
  @apply block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2;
}

.page-input {
  @apply w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500;
}

.dialog-actions {
  @apply flex justify-end gap-2;
}

/* Word Count Stats */
.word-count-stats {
  @apply space-y-3 mb-6;
}

.stat-row {
  @apply flex justify-between items-center;
}

.stat-label {
  @apply text-sm text-gray-600 dark:text-gray-400;
}

.stat-value {
  @apply text-sm font-medium text-gray-900 dark:text-white;
}

/* RTL Support */
.rtl .footer-left,
.rtl .footer-center,
.rtl .footer-right {
  @apply flex-row-reverse;
}

.rtl .footer-stats {
  @apply flex-row-reverse;
}

.rtl .language-option {
  @apply flex-row-reverse text-right;
}

.rtl .stat-row {
  @apply flex-row-reverse;
}
</style>