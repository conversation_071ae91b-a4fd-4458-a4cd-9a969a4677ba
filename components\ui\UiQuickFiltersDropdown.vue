<template>
  <!-- Quick Filter Presets - Exact match to your pattern -->
  <div class="relative">
    <UiButton
      @click="showQuickFiltersDropdown = !showQuickFiltersDropdown"
      variant="outline"
      size="sm"
    >
      <Icon name="material-symbols:speed" class="h-4 w-4 mr-1" />
      Quick Filters
      <Icon name="material-symbols:expand-more" class="h-4 w-4 ml-1" />
    </UiButton>

    <!-- Quick Filters Dropdown -->
    <Transition
      enter-active-class="transition ease-out duration-200"
      enter-from-class="transform opacity-0 scale-95"
      enter-to-class="transform opacity-100 scale-100"
      leave-active-class="transition ease-in duration-150"
      leave-from-class="transform opacity-100 scale-100"
      leave-to-class="transform opacity-0 scale-95"
    >
      <div
        v-if="showQuickFiltersDropdown"
        class="absolute right-0 top-10 w-64 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 py-2 z-20"
      >
        <button
          v-for="preset in computedQuickFilterPresets"
          :key="preset.key"
          @click="applyQuickFilter(preset)"
          class="w-full px-4 py-2 text-left text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center justify-between"
        >
          <div class="flex items-center">
            <Icon :name="preset.icon" class="h-4 w-4 mr-2" />
            {{ preset.label }}
          </div>
          <span
            class="text-xs text-gray-500 bg-gray-100 dark:bg-gray-700 px-2 py-0.5 rounded-full"
          >
            {{ preset.count }}
          </span>
        </button>
      </div>
    </Transition>
  </div>
</template>

<script setup lang="ts">
import UiButton from './UiButton.vue';
import { ref, computed, onMounted, onUnmounted } from 'vue';

export interface QuickFilterPreset {
  key: string;
  label: string;
  icon: string;
  count: number | string;
  filter?: any; // The actual filter data
}

export interface QuickFiltersDropdownProps {
  presets?: QuickFilterPreset[];
  showQuickFilters?: boolean;
}

interface Emits {
  (e: 'apply-filter', preset: QuickFilterPreset): void;
}

const props = withDefaults(defineProps<QuickFiltersDropdownProps>(), {
  presets: () => [],
  showQuickFilters: true
});

const emit = defineEmits<Emits>();

// State
const showQuickFiltersDropdown = ref(false);

// Default presets if none provided
const defaultPresets: QuickFilterPreset[] = [
  {
    key: 'recent',
    label: 'Recent Items',
    icon: 'heroicons:clock',
    count: 12
  },
  {
    key: 'favorites',
    label: 'Favorites',
    icon: 'heroicons:star',
    count: 8
  },
  {
    key: 'shared',
    label: 'Shared with Me',
    icon: 'heroicons:share',
    count: 5
  },
  {
    key: 'archived',
    label: 'Archived',
    icon: 'heroicons:archive-box',
    count: 23
  }
];

// Computed
const computedQuickFilterPresets = computed(() => {
  return props.presets.length > 0 ? props.presets : defaultPresets;
});

// Methods
const applyQuickFilter = (preset: QuickFilterPreset) => {
  emit('apply-filter', preset);
  showQuickFiltersDropdown.value = false;
};

// Click outside handler
const handleClickOutside = (event: MouseEvent) => {
  const target = event.target as HTMLElement;
  if (!target.closest('.relative')) {
    showQuickFiltersDropdown.value = false;
  }
};

// Lifecycle
onMounted(() => {
  document.addEventListener('click', handleClickOutside);
});

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside);
});
</script>

<style scoped>
/* Additional styles if needed */
</style>
