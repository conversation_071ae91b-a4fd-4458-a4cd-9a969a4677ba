<template>
  <div class="progress-examples space-y-8">
    <!-- Circular Progress Examples -->
    <div class="example-section">
      <h3 class="text-lg font-semibold mb-4">Circular Progress Examples</h3>
      
      <!-- Basic Circles -->
      <div class="bg-white dark:bg-gray-800 p-6 rounded-lg border mb-6">
        <h4 class="text-md font-medium mb-4">Basic Circular Progress</h4>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-6">
          <div class="text-center">
            <UiProgressCircle :value="25" :size="80" :show-label="true" />
            <p class="text-sm text-gray-600 dark:text-gray-400 mt-2">Low Progress</p>
          </div>
          
          <div class="text-center">
            <UiProgressCircle :value="55" :size="80" :show-label="true" />
            <p class="text-sm text-gray-600 dark:text-gray-400 mt-2">Medium Progress</p>
          </div>
          
          <div class="text-center">
            <UiProgressCircle :value="85" :size="80" :show-label="true" />
            <p class="text-sm text-gray-600 dark:text-gray-400 mt-2">High Progress</p>
          </div>
          
          <div class="text-center">
            <UiProgressCircle :value="100" :size="80" :show-label="true" />
            <p class="text-sm text-gray-600 dark:text-gray-400 mt-2">Complete</p>
          </div>
        </div>
      </div>

      <!-- Different Sizes -->
      <div class="bg-white dark:bg-gray-800 p-6 rounded-lg border mb-6">
        <h4 class="text-md font-medium mb-4">Different Sizes</h4>
        <div class="flex items-center justify-center gap-8">
          <div class="text-center">
            <UiProgressCircle :value="75" :size="60" :show-label="true" />
            <p class="text-sm text-gray-600 dark:text-gray-400 mt-2">Small (60px)</p>
          </div>
          
          <div class="text-center">
            <UiProgressCircle :value="75" :size="100" :show-label="true" />
            <p class="text-sm text-gray-600 dark:text-gray-400 mt-2">Medium (100px)</p>
          </div>
          
          <div class="text-center">
            <UiProgressCircle :value="75" :size="140" :show-label="true" />
            <p class="text-sm text-gray-600 dark:text-gray-400 mt-2">Large (140px)</p>
          </div>
        </div>
      </div>

      <!-- Code Example -->
      <details class="mt-4">
        <summary class="cursor-pointer text-sm font-medium text-gray-600 dark:text-gray-400">
          Show Code
        </summary>
        <pre class="mt-2 p-4 bg-gray-100 dark:bg-gray-900 rounded text-sm overflow-x-auto"><code>&lt;UiProgressCircle
  :value="75"
  :size="120"
  :show-label="true"
/&gt;</code></pre>
      </details>
    </div>

    <!-- Linear Progress Examples -->
    <div class="example-section">
      <h3 class="text-lg font-semibold mb-4">Linear Progress Examples</h3>
      
      <!-- Basic Linear -->
      <div class="bg-white dark:bg-gray-800 p-6 rounded-lg border mb-6">
        <h4 class="text-md font-medium mb-4">Basic Linear Progress</h4>
        <div class="space-y-4">
          <UiProgressLinear
            :value="25"
            label="Project Setup"
            :height="8"
          />
          
          <UiProgressLinear
            :value="60"
            label="Development"
            :height="8"
          />
          
          <UiProgressLinear
            :value="90"
            label="Testing"
            :height="8"
          />
          
          <UiProgressLinear
            :value="100"
            label="Deployment"
            :height="8"
          />
        </div>
      </div>

      <!-- Different Heights -->
      <div class="bg-white dark:bg-gray-800 p-6 rounded-lg border mb-6">
        <h4 class="text-md font-medium mb-4">Different Heights</h4>
        <div class="space-y-4">
          <UiProgressLinear
            :value="75"
            label="Thin Progress"
            :height="4"
          />
          
          <UiProgressLinear
            :value="75"
            label="Normal Progress"
            :height="8"
          />
          
          <UiProgressLinear
            :value="75"
            label="Thick Progress"
            :height="16"
          />
        </div>
      </div>

      <!-- Advanced Features -->
      <div class="bg-white dark:bg-gray-800 p-6 rounded-lg border mb-6">
        <h4 class="text-md font-medium mb-4">Advanced Features</h4>
        <div class="space-y-4">
          <UiProgressLinear
            :value="65"
            label="With Buffer"
            :height="8"
            :buffer-value="80"
          />
          
          <UiProgressLinear
            :value="45"
            label="Animated Progress"
            :height="10"
            :animated="true"
          />
          
          <UiProgressLinear
            :value="0"
            label="Indeterminate Loading"
            :height="6"
            :indeterminate="true"
          />
          
          <UiProgressLinear
            :value="70"
            label="With Gradient"
            :height="12"
            :gradient="{ from: '#3b82f6', to: '#10b981' }"
          />
        </div>
      </div>

      <!-- Interactive Example -->
      <div class="bg-white dark:bg-gray-800 p-6 rounded-lg border mb-6">
        <h4 class="text-md font-medium mb-4">Interactive Progress</h4>
        <div class="space-y-4">
          <div class="flex items-center gap-4 mb-4">
            <UiButton @click="decreaseProgress" size="sm" variant="secondary">
              <Icon name="material-symbols:remove" class="h-4 w-4" />
            </UiButton>
            <UiButton @click="increaseProgress" size="sm" variant="secondary">
              <Icon name="material-symbols:add" class="h-4 w-4" />
            </UiButton>
            <UiButton @click="resetProgress" size="sm" variant="ghost">
              Reset
            </UiButton>
          </div>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="text-center">
              <UiProgressCircle 
                :value="interactiveProgress" 
                :size="120" 
                :show-label="true" 
              />
              <p class="text-sm text-gray-600 dark:text-gray-400 mt-2">Circular</p>
            </div>
            
            <div class="flex flex-col justify-center">
              <UiProgressLinear
                :value="interactiveProgress"
                label="Linear Progress"
                :height="12"
                :animated="interactiveProgress > 0 && interactiveProgress < 100"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- Code Example -->
      <details class="mt-4">
        <summary class="cursor-pointer text-sm font-medium text-gray-600 dark:text-gray-400">
          Show Code
        </summary>
        <pre class="mt-2 p-4 bg-gray-100 dark:bg-gray-900 rounded text-sm overflow-x-auto"><code>&lt;UiProgressLinear
  :value="75"
  label="Task Progress"
  :height="8"
  :animated="true"
/&gt;

&lt;UiProgressLinear
  :value="65"
  label="With Buffer"
  :height="8"
  :buffer-value="80"
/&gt;

&lt;UiProgressLinear
  :value="0"
  label="Loading..."
  :height="4"
  :indeterminate="true"
/&gt;</code></pre>
      </details>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

// Interactive progress state
const interactiveProgress = ref(50)

// Methods
const increaseProgress = () => {
  interactiveProgress.value = Math.min(interactiveProgress.value + 10, 100)
}

const decreaseProgress = () => {
  interactiveProgress.value = Math.max(interactiveProgress.value - 10, 0)
}

const resetProgress = () => {
  interactiveProgress.value = 50
}
</script>

<style scoped>
.example-section {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 2rem;
}

.dark .example-section {
  border-bottom-color: #374151;
}

.example-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

details summary {
  transition: color 0.2s ease;
}

details summary:hover {
  color: #374151;
}

.dark details summary:hover {
  color: #d1d5db;
}

details[open] summary {
  margin-bottom: 0.5rem;
}

pre code {
  color: #374151;
}

.dark pre code {
  color: #d1d5db;
}
</style>
