/**
 * Utility CSS classes
 *
 * Custom utility classes that extend Tailwind CSS functionality
 * for the Legal SaaS Frontend application
 */

/* Design Token Utilities */

/* ============================================================================
   LAYOUT UTILITIES
   ============================================================================ */

.container-fluid {
  width: 100%;
  padding-left: 1rem;
  padding-right: 1rem;
  margin-left: auto;
  margin-right: auto;
}

.container-sm {
  max-width: 24rem;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

.container-md {
  max-width: 28rem;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

.container-lg {
  max-width: 32rem;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

.container-xl {
  max-width: 36rem;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

.container-2xl {
  max-width: 42rem;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

.container-4xl {
  max-width: 56rem;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

.container-6xl {
  max-width: 72rem;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

.full-height {
  min-height: 100vh;
}

.full-width {
  width: 100vw;
}

/* ============================================================================
   SPACING UTILITIES
   ============================================================================ */

.space-y-0\.5 > * + * {
  margin-top: 0.125rem;
}

.space-y-1\.5 > * + * {
  margin-top: 0.375rem;
}

.space-y-2\.5 > * + * {
  margin-top: 0.625rem;
}

.space-y-3\.5 > * + * {
  margin-top: 0.875rem;
}

.space-x-0\.5 > * + * {
  margin-left: 0.125rem;
}

.space-x-1\.5 > * + * {
  margin-left: 0.375rem;
}

.space-x-2\.5 > * + * {
  margin-left: 0.625rem;
}

.space-x-3\.5 > * + * {
  margin-left: 0.875rem;
}

/* ============================================================================
   TYPOGRAPHY UTILITIES
   ============================================================================ */

.text-balance {
  text-wrap: balance;
}

.text-pretty {
  text-wrap: pretty;
}

.font-display {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
}

.font-mono {
  font-family: 'JetBrains Mono', 'Fira Code', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
}

.text-gradient {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-gradient-primary {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-gradient-success {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* ============================================================================
   BACKGROUND UTILITIES
   ============================================================================ */

.bg-gradient-primary {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

.bg-gradient-secondary {
  background: linear-gradient(135deg, #6b7280 0%, #374151 100%);
}

.bg-gradient-success {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.bg-gradient-warning {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.bg-gradient-danger {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}

.bg-pattern-dots {
  background-image: radial-gradient(circle, #e5e7eb 1px, transparent 1px);
  background-size: 20px 20px;
}

.bg-pattern-grid {
  background-image: 
    linear-gradient(rgba(229, 231, 235, 0.5) 1px, transparent 1px),
    linear-gradient(90deg, rgba(229, 231, 235, 0.5) 1px, transparent 1px);
  background-size: 20px 20px;
}

/* ============================================================================
   BORDER UTILITIES
   ============================================================================ */

.border-dashed-2 {
  border-width: 2px;
  border-style: dashed;
}

.border-dotted-2 {
  border-width: 2px;
  border-style: dotted;
}

.border-gradient {
  border: 2px solid transparent;
  background: linear-gradient(white, white) padding-box,
              linear-gradient(135deg, #3b82f6, #1d4ed8) border-box;
}

/* ============================================================================
   SHADOW UTILITIES
   ============================================================================ */

.shadow-colored {
  box-shadow: 0 4px 6px -1px rgba(59, 130, 246, 0.1), 0 2px 4px -1px rgba(59, 130, 246, 0.06);
}

.shadow-colored-lg {
  box-shadow: 0 10px 15px -3px rgba(59, 130, 246, 0.1), 0 4px 6px -2px rgba(59, 130, 246, 0.05);
}

.shadow-inner-lg {
  box-shadow: inset 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.shadow-glow {
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
}

/* ============================================================================
   ANIMATION UTILITIES
   ============================================================================ */

.animate-fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.animate-fade-out {
  animation: fadeOut 0.3s ease-in-out;
}

.animate-slide-in-left {
  animation: slideInLeft 0.3s ease-out;
}

.animate-slide-in-right {
  animation: slideInRight 0.3s ease-out;
}

.animate-slide-in-up {
  animation: slideInUp 0.3s ease-out;
}

.animate-slide-in-down {
  animation: slideInDown 0.3s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.2s ease-out;
}

.animate-scale-out {
  animation: scaleOut 0.2s ease-in;
}

.animate-bounce-in {
  animation: bounceIn 0.5s ease-out;
}

.animate-pulse-slow {
  animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* ============================================================================
   KEYFRAME ANIMATIONS
   ============================================================================ */

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes fadeOut {
  from { opacity: 1; }
  to { opacity: 0; }
}

@keyframes slideInLeft {
  from { transform: translateX(-100%); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

@keyframes slideInRight {
  from { transform: translateX(100%); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

@keyframes slideInUp {
  from { transform: translateY(100%); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes slideInDown {
  from { transform: translateY(-100%); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes scaleIn {
  from { transform: scale(0.9); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}

@keyframes scaleOut {
  from { transform: scale(1); opacity: 1; }
  to { transform: scale(0.9); opacity: 0; }
}

@keyframes bounceIn {
  0% { transform: scale(0.3); opacity: 0; }
  50% { transform: scale(1.05); }
  70% { transform: scale(0.9); }
  100% { transform: scale(1); opacity: 1; }
}

/* ============================================================================
   INTERACTION UTILITIES
   ============================================================================ */

.hover-lift {
  transition: transform 0.2s ease-in-out;
}

.hover-lift:hover {
  transform: translateY(-2px);
}

.hover-scale {
  transition: transform 0.2s ease-in-out;
}

.hover-scale:hover {
  transform: scale(1.05);
}

.hover-glow {
  transition: box-shadow 0.2s ease-in-out;
}

.hover-glow:hover {
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.4);
}

.focus-ring:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5), 0 0 0 4px rgba(59, 130, 246, 0.2);
}

.focus-ring-inset:focus {
  outline: none;
  box-shadow: inset 0 0 0 2px rgba(59, 130, 246, 0.5);
}

/* ============================================================================
   ACCESSIBILITY UTILITIES
   ============================================================================ */

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.not-sr-only {
  position: static;
  width: auto;
  height: auto;
  padding: 0;
  margin: 0;
  overflow: visible;
  clip: auto;
  white-space: normal;
}

.skip-link {
  @apply sr-only focus:not-sr-only focus:absolute focus:top-0 focus:left-0 focus:z-50 focus:p-4 focus:bg-blue-600 focus:text-white focus:no-underline;
}

/* ============================================================================
   PRINT UTILITIES
   ============================================================================ */

@media print {
  .print-hidden {
    display: none !important;
  }
  
  .print-visible {
    display: block !important;
  }
  
  .print-break-before {
    page-break-before: always;
  }
  
  .print-break-after {
    page-break-after: always;
  }
  
  .print-break-inside-avoid {
    page-break-inside: avoid;
  }
}

/* ============================================================================
   DARK MODE UTILITIES
   ============================================================================ */

@media (prefers-color-scheme: dark) {
  .dark-auto {
    @apply dark:bg-gray-900 dark:text-white;
  }
  
  .dark-auto-border {
    @apply dark:border-gray-700;
  }
  
  .dark-auto-bg {
    @apply dark:bg-gray-800;
  }
}
