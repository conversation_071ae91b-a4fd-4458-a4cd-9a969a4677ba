<template>
  <div class="auth-loading-page">
    <div class="spinner"></div>
    <p>Loading authentication...</p>
  </div>
</template>

<script setup lang="ts">
// No specific script needed for this simple component
</script>

<style scoped>
.auth-loading-page {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100vh;
  width: 100vw;
  background-color: #f3f4f6; /* A light gray background */
  color: #1f2937; /* Dark gray text */
  font-family: sans-serif;
  text-align: center;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9999; /* Ensure it's on top */
}

.spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  width: 36px;
  height: 36px;
  border-radius: 50%;
  border-left-color: #09f; /* A blue color for the spinner */
  margin-bottom: 20px;
  animation: spin 1s ease infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
} /* Closing brace was missing here */

.auth-loading-page p {
  font-size: 1.2em;
}
</style>