<template>
  <UiCard class="ui-tracker">
    <template #header>
      <div class="flex items-center justify-between">
        <div class="flex items-center gap-2">
          <Icon
            name="material-symbols:timeline"
            class="h-6 w-6 text-(--ui-primary)"
          />
          <span class="font-medium">{{ domain }}</span>
        </div>
        <span class="text-sm text-(--ui-text-muted)">{{ uptime }}</span>
      </div>
    </template>

    <div class="mb-1 mb-2 flex justify-between px-1 text-xs text-gray-200">
      <span>{{ startLabel }}</span>
      <span>{{ endLabel }}</span>
    </div>

    <div ref="containerRef" class="flex h-8 w-full items-center justify-evenly">
      <div
        v-for="(bar, index) in statusBars"
        @mouseover="hoveredIndex = index"
        @mouseleave="hoveredIndex = null"
        :key="index"
        class="relative inline-block h-8 first:rounded-l last:rounded-r cursor-pointer transition-all duration-200"
        :style="{
          width: `${barWidth}px`,
          marginLeft: `${barGap}px`,
          marginRight: `${barGap}px`,
        }"
        :class="getBarClasses(bar.status)"
      >
        <!-- Tooltip -->
        <div
          v-if="index === hoveredIndex && bar.status !== 'empty'"
          class="absolute -top-10 left-1/2 -translate-x-1/2 z-10 rounded-lg border border-(--ui-border) bg-(--ui-bg-inverted) px-2 py-1 text-xs text-(--ui-bg) shadow-lg"
        >
          {{ getStatusLabel(bar.status) }}
          <div class="absolute top-full left-1/2 -translate-x-1/2 border-2 border-transparent border-t-(--ui-bg-inverted)"></div>
        </div>
      </div>
    </div>
  </UiCard>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, onUnmounted } from 'vue'

// Props interface
interface Props {
  domain: string
  uptime: string
  operationalStatus: boolean
  statusData: {
    status: string
  }[]
  barWidth: number
  barGap: number
  startLabel?: string
  endLabel?: string
}

// Props
const props = defineProps<Props>()

// Refs
const containerRef = ref<HTMLDivElement | null>(null)
const statusBars = ref<Array<{ status: string }>>([])
const resizeObserver = ref<ResizeObserver | null>(null)
const hoveredIndex = ref<number | null>(null)

// Functions
const updateStatusBars = () => {
  if (!containerRef.value) return

  const containerWidth = containerRef.value.offsetWidth
  const totalBarWidth = props.barWidth + props.barGap * 2
  const numberOfBars = Math.floor(containerWidth / totalBarWidth)

  if (props.statusData.length === 0) {
    statusBars.value = []
    return
  }

  // Take the most recent data points that will fit in the container
  const dataLength = props.statusData.length
  const startIndex = Math.max(0, dataLength - numberOfBars)
  const visibleBars = props.statusData.slice(startIndex)

  // Add empty bars if we don't have enough data to fill the container
  if (visibleBars.length < numberOfBars) {
    const emptyBars = Array(numberOfBars - visibleBars.length).fill({
      status: 'empty',
    })
    statusBars.value = [...emptyBars, ...visibleBars]
  } else {
    statusBars.value = visibleBars
  }
}

const getBarClasses = (status: string) => {
  return {
    'bg-(--ui-primary)': status === 'online',
    'bg-gray-200 dark:bg-gray-100': status === 'offline',
    'bg-transparent': status === 'empty',
    'bg-yellow-500': status === 'warning',
    'bg-red-500': status === 'error',
    'bg-green-500': status === 'success',
    'bg-blue-500': status === 'info'
  }
}

const getStatusLabel = (status: string): string => {
  const labels: Record<string, string> = {
    online: 'Online',
    offline: 'Offline',
    warning: 'Warning',
    error: 'Error',
    success: 'Success',
    info: 'Info',
    empty: 'No Data'
  }
  return labels[status] || status
}

// Watchers
watch(() => props.statusData, updateStatusBars, { deep: true })

// Lifecycle
onMounted(() => {
  updateStatusBars()

  resizeObserver.value = new ResizeObserver(updateStatusBars)

  if (containerRef.value) {
    resizeObserver.value.observe(containerRef.value)
  }
})

onUnmounted(() => {
  if (resizeObserver.value && containerRef.value) {
    resizeObserver.value.unobserve(containerRef.value)
    resizeObserver.value.disconnect()
  }
})
</script>

<style scoped>
/* Custom styles for the tracker component */
</style>
