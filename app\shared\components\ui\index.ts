/**
 * Shared UI Components
 *
 * Barrel export for all reusable UI components
 * These components are design system compliant and performance optimized
 */

// Base Components (only export existing components)
export { default as UiInput } from './UiInput.vue'
export { default as UiTextarea } from './UiTextarea.vue'
export { default as UiSelect } from './UiSelect.vue'
export { default as UiCheckbox } from './UiCheckbox.vue'
export { default as UiSlugInput } from './UiSlugInput.vue'
export { default as UiLogoUpload } from './UiLogoUpload.vue'
export { default as UiHeader } from './UiHeader.vue'

// Chart Components
export { default as UiBarChart } from './charts/UiBarChart.vue'
export { default as UiAreaChart } from './charts/UiAreaChart.vue'
export { default as UiLineChart } from './charts/UiLineChart.vue'
export { default as UiDonutChart } from './charts/UiDonutChart.vue'
export { default as UiProgressCircle } from './charts/UiProgressCircle.vue'
export { default as UiProgressLinear } from './charts/UiProgressLinear.vue'
export { default as UiTracker } from './charts/UiTracker.vue'

// Chart Types
export type * from './charts/types'
