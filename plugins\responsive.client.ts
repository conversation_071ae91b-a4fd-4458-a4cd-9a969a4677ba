// plugins/responsive.client.ts
import { useUiStore } from '~/stores/ui';
import { defineNuxtPlugin } from '#app';
 
export default defineNuxtPlugin(() => {
  // Only run on client side
  if (typeof window !== 'undefined') {
    const uiStore = useUiStore();

    // Set initial screen size
    uiStore.updateScreenSize(window.innerWidth, window.innerHeight);

    // Add resize listener with debouncing
    let timeoutId: number | null = null;

    const handleResize = () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }

      timeoutId = window.setTimeout(() => {
        uiStore.updateScreenSize(window.innerWidth, window.innerHeight);
      }, 100);
    };

    window.addEventListener('resize', handleResize, { passive: true });

    // Check for reduced motion preference
    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)');
    uiStore.setReducedMotion(prefersReducedMotion.matches);

    prefersReducedMotion.addEventListener('change', (e) => {
      uiStore.setReducedMotion(e.matches);
    });

    // Cleanup on app unmount (though this rarely happens in Nuxt)
    const cleanup = () => {
      window.removeEventListener('resize', handleResize);
      prefersReducedMotion.removeEventListener('change', () => {});
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };

    // Store cleanup function for potential future use
    (window as any).__responsiveCleanup = cleanup;
  }
});
