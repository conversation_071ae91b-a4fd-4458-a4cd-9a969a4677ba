<template>
  <div class="min-h-screen bg-gray-50/50">
    <!-- Settings Header -->
    <div class="bg-white border-b border-gray-200 sticky top-16 z-20">
      <div class="px-4 sm:px-6 lg:px-8 py-6">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 class="text-2xl font-bold text-gray-900">Settings</h1>
            <p class="mt-1 text-sm text-gray-500">Manage your application and account preferences</p>
          </div>

          <!-- Settings Search -->
          <div class="relative max-w-md w-full sm:w-auto">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Icon name="heroicons:magnifying-glass" class="h-5 w-5 text-gray-400" />
            </div>
            <input
              v-model="searchQuery"
              type="text"
              placeholder="Search settings..."
              class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-brandPrimary focus:border-brandPrimary sm:text-sm transition-colors duration-200"
            />
            <div v-if="searchQuery" class="absolute inset-y-0 right-0 pr-3 flex items-center">
              <button
                @click="searchQuery = ''"
                class="text-gray-400 hover:text-gray-600 transition-colors duration-200"
              >
                <Icon name="heroicons:x-mark" class="h-4 w-4" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Settings Content -->
    <div class="flex flex-1 overflow-hidden">
      <!-- Settings Navigation Sidebar -->
      <div class="hidden lg:flex lg:flex-shrink-0">
        <div class="w-80 bg-white border-r border-gray-200 overflow-y-auto">
          <nav class="p-6 space-y-1" aria-label="Settings navigation">
            <!-- Settings Categories -->
            <div v-for="category in settingsCategories" :key="category.id" class="space-y-1">
              <h3 class="px-3 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wider">
                {{ category.name }}
              </h3>

              <div class="space-y-1">
                <button
                  v-for="item in getFilteredItems(category.items)"
                  :key="item.value"
                  @click="setActiveTab(item.value)"
                  :class="[
                    'group w-full flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200',
                    activeTab === item.value
                      ? 'bg-brandPrimary text-white shadow-sm'
                      : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'
                  ]"
                >
                  <Icon
                    :name="item.icon"
                    :class="[
                      'mr-3 h-5 w-5 flex-shrink-0 transition-colors duration-200',
                      activeTab === item.value
                        ? 'text-white'
                        : 'text-gray-400 group-hover:text-gray-500'
                    ]"
                  />
                  <span class="truncate">{{ item.label }}</span>

                  <!-- Badge for items with notifications or updates -->
                  <span
                    v-if="item.badge"
                    :class="[
                      'ml-auto inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium',
                      activeTab === item.value
                        ? 'bg-white/20 text-white'
                        : 'bg-brandPrimary text-white'
                    ]"
                  >
                    {{ item.badge }}
                  </span>
                </button>
              </div>
            </div>
          </nav>
        </div>
      </div>

      <!-- Mobile Settings Navigation -->
      <div class="lg:hidden">
        <div class="bg-white border-b border-gray-200 px-4 py-3">
          <select
            v-model="activeTab"
            class="block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-brandPrimary focus:border-brandPrimary rounded-md"
          >
            <optgroup v-for="category in settingsCategories" :key="category.id" :label="category.name">
              <option
                v-for="item in getFilteredItems(category.items)"
                :key="item.value"
                :value="item.value"
              >
                {{ item.label }}
              </option>
            </optgroup>
          </select>
        </div>
      </div>

      <!-- Settings Content Area -->
      <div class="flex-1 overflow-y-auto">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <!-- Active Settings Panel -->
          <div v-if="activeSettingsItem" class="space-y-6">
            <!-- Panel Header -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <div class="flex items-start gap-4">
                <div class="flex-shrink-0">
                  <div class="w-12 h-12 bg-brandPrimary/10 rounded-xl flex items-center justify-center">
                    <Icon :name="activeSettingsItem.icon" class="h-6 w-6 text-brandPrimary" />
                  </div>
                </div>
                <div class="flex-1 min-w-0">
                  <h2 class="text-xl font-semibold text-gray-900">{{ activeSettingsItem.label }}</h2>
                  <p class="mt-1 text-sm text-gray-500">{{ activeSettingsItem.description }}</p>
                </div>
              </div>
            </div>

            <!-- Settings Component -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
              <component :is="dynamicComponent(activeSettingsItem)" />
            </div>
          </div>

          <!-- No Results State -->
          <div v-else-if="searchQuery && filteredSettingsItems.length === 0" class="text-center py-12">
            <Icon name="heroicons:magnifying-glass" class="mx-auto h-12 w-12 text-gray-400" />
            <h3 class="mt-4 text-lg font-medium text-gray-900">No settings found</h3>
            <p class="mt-2 text-sm text-gray-500">
              Try adjusting your search terms or browse the categories below.
            </p>
            <button
              @click="searchQuery = ''"
              class="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-brandPrimary bg-brandPrimary/10 hover:bg-brandPrimary/20 transition-colors duration-200"
            >
              Clear search
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useAuthStore } from '~/stores/auth';
import { RolesPlatform } from '~/app/features/auth/constants/roles';
import { capitalize } from '~/utils/helper';

const authStore = useAuthStore();
const currentUser = computed(() => authStore.currentUser);

const isSuperAdmin = computed(() => {
  return currentUser.value?.roles?.includes(PlatformRoles.SUPER_ADMIN) ?? false;
});

interface SettingsTab {
  label: string;
  value: string;
  icon: string;
  description: string;
  roles?: string[];
  componentName: string;
  category: string;
  keywords?: string[];
  badge?: string;
}

interface SettingsCategory {
  id: string;
  name: string;
  items: SettingsTab[];
}

// Search functionality
const searchQuery = ref('');

// Active tab management
const activeTab = ref('general');

// Settings configuration
const allSettingsItems = ref<SettingsTab[]>([
  {
    label: 'General Settings',
    value: 'general',
    icon: 'heroicons:cog-6-tooth',
    description: 'Basic platform configuration and branding settings',
    componentName: 'general',
    category: 'platform',
    keywords: ['platform', 'branding', 'name', 'logo', 'general']
  },
  {
    label: 'Security & Authentication',
    value: 'security',
    icon: 'heroicons:shield-check',
    description: 'Password policies, 2FA settings, and session management',
    componentName: 'security',
    category: 'platform',
    keywords: ['security', 'password', '2fa', 'authentication', 'session']
  },
  {
    label: 'Notifications',
    value: 'notifications',
    icon: 'heroicons:bell',
    description: 'Configure notification preferences and triggers',
    componentName: 'notifications',
    category: 'platform',
    keywords: ['notifications', 'alerts', 'email', 'triggers']
  },
  {
    label: 'SMTP & API Keys',
    value: 'smtp-api-keys',
    icon: 'heroicons:key',
    description: 'Outbound email configuration and API key management',
    componentName: 'outbound',
    category: 'integrations',
    keywords: ['smtp', 'email', 'api', 'keys', 'outbound']
  },
  {
    label: 'Third-Party Integrations',
    value: 'third-party-integrations',
    icon: 'heroicons:puzzle-piece',
    description: 'Connect with external services and applications',
    componentName: 'integrations',
    category: 'integrations',
    keywords: ['integrations', 'third-party', 'external', 'services']
  },
  {
    label: 'Legal Policy Management',
    value: 'legal-policy',
    icon: 'heroicons:document-text',
    description: 'Terms of service, privacy policy, and legal documents',
    componentName: 'policy',
    category: 'compliance',
    keywords: ['legal', 'policy', 'terms', 'privacy', 'documents']
  },
  {
    label: 'GDPR & Compliance',
    value: 'gdpr-israeli-compliance',
    icon: 'heroicons:shield-exclamation',
    description: 'Data protection and compliance settings',
    componentName: 'compliance',
    category: 'compliance',
    keywords: ['gdpr', 'compliance', 'data protection', 'retention']
  },
  {
    label: 'Localization & RTL',
    value: 'localization-rtl',
    icon: 'heroicons:language',
    description: 'Language settings and right-to-left configuration',
    componentName: 'localization',
    category: 'platform',
    keywords: ['localization', 'language', 'rtl', 'translation']
  },
  {
    label: 'Audit Logs',
    value: 'audit-logs',
    icon: 'heroicons:clipboard-document-list',
    description: 'System activity logs and audit trail configuration',
    componentName: 'auditLogs',
    category: 'security',
    keywords: ['audit', 'logs', 'activity', 'trail', 'monitoring']
  }
]);

// Filter items based on user roles
const filteredSettingsItems = computed(() => {
  let items = allSettingsItems.value.filter(item => {
    if (!item.roles || item.roles.length === 0) {
      return true;
    }
    return item.roles.some(role => currentUser.value?.roles?.includes(role));
  });

  // Apply search filter
  if (searchQuery.value.trim()) {
    const query = searchQuery.value.toLowerCase().trim();
    items = items.filter(item => {
      return (
        item.label.toLowerCase().includes(query) ||
        item.description.toLowerCase().includes(query) ||
        item.keywords?.some(keyword => keyword.toLowerCase().includes(query))
      );
    });
  }

  return items;
});

// Group settings into categories
const settingsCategories = computed<SettingsCategory[]>(() => {
  const categories: { [key: string]: SettingsCategory } = {};

  filteredSettingsItems.value.forEach(item => {
    if (!categories[item.category]) {
      categories[item.category] = {
        id: item.category,
        name: getCategoryName(item.category),
        items: []
      };
    }
    categories[item.category].items.push(item);
  });

  return Object.values(categories);
});

// Get category display name
const getCategoryName = (categoryId: string): string => {
  const categoryNames: { [key: string]: string } = {
    platform: 'Platform Settings',
    integrations: 'Integrations',
    compliance: 'Compliance & Legal',
    security: 'Security & Monitoring'
  };
  return categoryNames[categoryId] || categoryId;
};

// Get filtered items for a category
const getFilteredItems = (items: SettingsTab[]) => {
  return items;
};

// Get active settings item
const activeSettingsItem = computed(() => {
  return filteredSettingsItems.value.find(item => item.value === activeTab.value);
});

// Set active tab
const setActiveTab = (tabValue: string) => {
  activeTab.value = tabValue;
};

// Dynamic component loader
const dynamicComponent = (tab: SettingsTab) => {
  return defineAsyncComponent(() =>
    import(`@/components/settings/platform/Platform${capitalize(tab.componentName.trim())}.vue`)
  );
};

// Page meta
definePageMeta({
  layout: 'dashboard',
  title: 'Settings Overview',
  subtitle: 'Manage your application and account settings.',
  breadcrumb: [
    { label: 'Dashboard', href: '/dashboard' },
    { label: 'Settings' },
  ],
});
</script>