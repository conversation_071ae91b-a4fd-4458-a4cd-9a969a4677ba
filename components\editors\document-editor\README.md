# Document Editor Toolbar

An enhanced document editor toolbar component that matches Microsoft Word's ribbon interface design with full translation support for English, Hebrew, and Arabic languages.

## Features

### 🎨 Design
- **Microsoft Word-inspired interface** with tabbed toolbar design
- **Responsive layout** that works on all screen sizes
- **Dark mode support** with automatic theme switching
- **RTL language support** for Hebrew and Arabic
- **Professional styling** with proper grouping and spacing

### 🌍 Internationalization
- **Full translation support** for English, Hebrew, and Arabic
- **Dynamic language switching** with real-time toolbar updates
- **RTL layout adaptation** for right-to-left languages
- **Contextual translations** for all toolbar elements

### 📋 Toolbar Tabs

#### Home Tab
- **Clipboard Group**: Paste, Cut, Copy operations
- **Font Group**: Font family, size, bold, italic, underline
- **Paragraph Group**: Text alignment, bullet lists, numbered lists
- **Styles Group**: Normal text, headings (Title 1, 2, 3)

#### Insert Tab
- **Pages Group**: Link, Image, Video, Audio insertion

#### Table Tab
- **Table Group**: Insert table, add/delete rows

#### Tools Tab
- **Tools Group**: Search, Translate functions

#### Page Tab
- **Page Setup Group**: Page breaks, line breaks

#### Export Tab
- **Export Group**: PDF export, Word export, Share document

## Components

### Toolbar.vue
Main toolbar component with tab navigation and tool organization.

**Props:**
- `editor` - TipTap editor instance
- `modelValue` - Current document content

**Events:**
- `update:modelValue` - Content updates
- `command` - Toolbar command execution

### ToolbarButton.vue
Individual toolbar button component with consistent styling.

**Props:**
- `icon` - Icon name (Heroicons)
- `label` - Button label text
- `active` - Active state
- `disabled` - Disabled state
- `size` - Button size ('small' | 'large')
- `showLabel` - Show/hide label text

### Footer.vue
Enhanced document editor footer with statistics, controls, and language switching.

**Props:**
- `editor` - TipTap editor instance
- `wordCount` - Current word count
- `characterCount` - Current character count
- `currentPage` - Current page number
- `totalPages` - Total page count

**Events:**
- `zoom-change` - Zoom level changed
- `view-change` - View mode changed
- `language-change` - Language changed
- `go-to-page` - Navigate to specific page
- `spell-check` - Run spell check

**Features:**
- Document statistics (words, characters, pages)
- Language switcher with flag indicators
- Zoom controls with slider and presets
- View mode toggles (Print, Web, Outline, Draft)
- Page navigation dialog
- Word count statistics dialog
- Proofing status indicator

## Usage

```vue
<template>
  <div class="document-editor">
    <DocumentEditorToolbar
      :editor="editor"
      @command="handleToolbarCommand"
    />
    <div ref="editorElement"></div>
    <DocumentEditorFooter
      :editor="editor"
      :word-count="wordCount"
      :character-count="characterCount"
      :current-page="currentPage"
      :total-pages="totalPages"
      @zoom-change="handleZoomChange"
      @view-change="handleViewChange"
      @language-change="handleLanguageChange"
      @go-to-page="handleGoToPage"
      @spell-check="handleSpellCheck"
    />
  </div>
</template>

<script setup>
import DocumentEditorToolbar from '~/components/editors/document-editor/Toolbar.vue'
import DocumentEditorFooter from '~/components/editors/document-editor/Footer.vue'

const editor = ref(null)
const wordCount = ref(0)
const characterCount = ref(0)
const currentPage = ref(1)
const totalPages = ref(1)

const handleToolbarCommand = (command, params) => {
  switch (command) {
    case 'paste':
      // Handle paste
      break
    case 'insertImage':
      // Handle image insertion
      break
    // Add more handlers
  }
}

const handleZoomChange = (zoom) => {
  // Handle zoom change
}

const handleViewChange = (view) => {
  // Handle view mode change
}

const handleLanguageChange = (language) => {
  // Handle language change
}

const handleGoToPage = (page) => {
  // Handle page navigation
}

const handleSpellCheck = () => {
  // Handle spell check
}
</script>
```

## Translation Keys

All toolbar translations are stored in `i18n/locales/{lang}/documents.json` under the `editor.toolbar` namespace:

```json
{
  "editor": {
    "toolbar": {
      "savedAt": "Saved at {time}",
      "tabs": {
        "home": "Home",
        "insert": "Insert",
        "table": "Table",
        "tools": "Tools",
        "page": "Page",
        "export": "Export"
      },
      "groups": {
        "clipboard": "Clipboard",
        "font": "Font",
        "paragraph": "Paragraph"
      },
      "bold": "Bold",
      "italic": "Italic"
    }
  }
}
```

## Styling

The toolbar uses Tailwind CSS with custom component classes:

- `.document-editor-toolbar` - Main container
- `.toolbar-tabs` - Tab navigation
- `.toolbar-panel` - Tab content panels
- `.toolbar-group` - Tool groups
- `.toolbar-button` - Individual buttons

## RTL Support

The toolbar automatically adapts to RTL languages:

- Reverses flex direction for tab navigation
- Mirrors button group layouts
- Adjusts text alignment and spacing
- Maintains visual hierarchy in RTL context

## Demo

Visit `/demo/document-editor-toolbar` to see the toolbar in action with:

- Live language switching
- All toolbar functionality
- RTL/LTR layout demonstration
- Integration with TipTap editor

## Dependencies

- **@tiptap/vue-3** - Rich text editor
- **@tiptap/starter-kit** - Basic editor extensions
- **@nuxt/icon** - Icon system
- **@nuxtjs/i18n** - Internationalization
- **Tailwind CSS** - Styling framework

## Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## Contributing

When adding new toolbar features:

1. Add the tool to the appropriate tab in `Toolbar.vue`
2. Create translation keys in all language files
3. Add command handling in the parent component
4. Update this documentation

## License

Part of the Legal SaaS Frontend project.
