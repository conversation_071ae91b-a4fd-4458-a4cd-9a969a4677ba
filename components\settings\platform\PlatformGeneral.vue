<template>
  <div class="space-y-8">
    <!-- Platform Branding Section -->
    <SettingsCard
      :title="t('settings.platformBranding')"
      subtitle="Customize your platform's visual identity and branding elements"
      icon="heroicons:paint-brush"
    >
      <SettingsForm
        :loading="loading"
        :has-changes="hasChanges"
        :save-status="saveStatus"
        @submit="saveBrandingSettings"
        @reset="resetBrandingSettings"
      >
        <SettingsSection
          :title="t('settings.brandIdentity')"
          description="Configure your platform's name and visual elements"
        >
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label for="platformName" class="block text-sm font-medium text-gray-700 mb-2">{{ t('settings.platformName') }}</label>
              <UiInput
                id="platformName"
                name="platformName"
                v-model="branding.platformName"
                type="text"
                placeholder="e.g., LegalSaaS Platform"
                help-text="This name will appear in the header and throughout the application"
              />
            </div>

            <div>
              <label for="logoUrlLight" class="block text-sm font-medium text-gray-700 mb-2">Logo URL (Light Mode)</label>
              <UiInput
                id="logoUrlLight"
                name="logoUrlLight"
                v-model="branding.logoUrlLight"
                type="url"
                placeholder="https://example.com/logo-light.png"
                help-text="Logo displayed in light theme"
              />
            </div>

            <div>
              <label for="logoUrlDark" class="block text-sm font-medium text-gray-700 mb-2">Logo URL (Dark Mode)</label>
              <UiInput
                id="logoUrlDark"
                name="logoUrlDark"
                v-model="branding.logoUrlDark"
                type="url"
                placeholder="https://example.com/logo-dark.png"
                help-text="Logo displayed in dark theme (optional)"
              />
            </div>

            <div>
              <label for="faviconUrl" class="block text-sm font-medium text-gray-700 mb-2">Favicon URL</label>
              <UiInput
                id="faviconUrl"
                name="faviconUrl"
                v-model="branding.faviconUrl"
                type="url"
                placeholder="https://example.com/favicon.ico"
                help-text="Small icon displayed in browser tabs"
              />
            </div>
          </div>
        </SettingsSection>
      </SettingsForm>
    </SettingsCard>

    <!-- Default Regional Settings Section -->
    <SettingsCard
      title="Regional Settings"
      subtitle="Configure default language, timezone, and date format preferences"
      icon="heroicons:globe-alt"
    >
      <SettingsForm
        :loading="loading"
        :has-changes="hasChanges"
        :save-status="saveStatus"
        @submit="saveRegionalSettings"
        @reset="resetRegionalSettings"
      >
        <SettingsSection
          title="Localization Defaults"
          description="Set default regional preferences for new users"
        >
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <label for="defaultLanguage" class="block text-sm font-medium text-gray-700 mb-2">Default Language</label>
              <UiSelect
                id="defaultLanguage"
                name="defaultLanguage"
                v-model="regionalSettings.defaultLanguage"
                :options="languageOptions"
              />
            </div>

            <div>
              <label for="defaultTimezone" class="block text-sm font-medium text-gray-700 mb-2">Default Timezone</label>
              <UiSelect
                id="defaultTimezone"
                name="defaultTimezone"
                v-model="regionalSettings.defaultTimezone"
                :options="timezoneOptions"
              />
            </div>

            <div>
              <label for="defaultDateFormat" class="block text-sm font-medium text-gray-700 mb-2">Default Date Format</label>
              <UiSelect
                id="defaultDateFormat"
                name="defaultDateFormat"
                v-model="regionalSettings.defaultDateFormat"
                :options="dateFormatOptions"
              />
            </div>
          </div>
        </SettingsSection>
      </SettingsForm>
    </SettingsCard>

    <!-- Support & Contact Information Section -->
    <SettingsCard
      title="Support & Contact"
      subtitle="Configure support contact information and help resources"
      icon="heroicons:question-mark-circle"
    >
      <SettingsForm
        :loading="loading"
        :has-changes="hasChanges"
        :save-status="saveStatus"
        @submit="saveSupportSettings"
        @reset="resetSupportSettings"
      >
        <SettingsSection
          title="Contact Information"
          description="Provide support contact details for users"
        >
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label for="supportEmail" class="block text-sm font-medium text-gray-700 mb-2">Support Email Address</label>
              <UiInput
                id="supportEmail"
                name="supportEmail"
                v-model="supportInfo.email"
                type="email"
                placeholder="<EMAIL>"
                help-text="Primary email for user support inquiries"
              />
            </div>

            <div>
              <label for="supportPhone" class="block text-sm font-medium text-gray-700 mb-2">Support Phone Number</label>
              <UiInput
                id="supportPhone"
                name="supportPhone"
                v-model="supportInfo.phone"
                type="tel"
                placeholder="******-123-4567"
                help-text="Phone number for urgent support issues"
              />
            </div>

            <div class="md:col-span-2">
              <label for="helpLink" class="block text-sm font-medium text-gray-700 mb-2">Help Documentation URL</label>
              <UiInput
                id="helpLink"
                name="helpLink"
                v-model="supportInfo.helpLink"
                type="url"
                placeholder="https://docs.example.com"
                help-text="Link to your help documentation or knowledge base"
              />
            </div>
          </div>
        </SettingsSection>
      </SettingsForm>
    </SettingsCard>

  </div>
</template>

<script setup lang="ts">
import { reactive, ref, computed, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { useToast } from '~/composables/useToast';
import { useApi } from '~/composables/useApi';
import UiInput from '~/components/ui/UiInput.vue';
import UiSelect from '~/components/ui/UiSelect.vue';
import SettingsCard from '~/components/settings/SettingsCard.vue';
import SettingsForm from '~/components/settings/SettingsForm.vue';
import SettingsSection from '~/components/settings/SettingsSection.vue';

// Composables
const { t } = useI18n();
const { showToast } = useToast();
const { patch } = useApi();

// Loading and state management
const loading = ref(false);
const saveStatus = ref<'saving' | 'saved' | 'error' | null>(null);

// Platform Branding
const branding = reactive({
  platformName: 'LegalSaaS Platform',
  logoUrlLight: '',
  logoUrlDark: '',
  faviconUrl: '',
});

const originalBranding = reactive({ ...branding });

// Default Regional Settings
const regionalSettings = reactive({
  defaultLanguage: 'en',
  defaultTimezone: 'UTC',
  defaultDateFormat: 'MM/DD/YYYY',
});

const originalRegionalSettings = reactive({ ...regionalSettings });

// Support & Contact Information
const supportInfo = reactive({
  email: '',
  phone: '',
  helpLink: '',
});

const originalSupportInfo = reactive({ ...supportInfo });

// Check for changes
const hasChanges = computed(() => {
  return (
    JSON.stringify(branding) !== JSON.stringify(originalBranding) ||
    JSON.stringify(regionalSettings) !== JSON.stringify(originalRegionalSettings) ||
    JSON.stringify(supportInfo) !== JSON.stringify(originalSupportInfo)
  );
});

// Options
const languageOptions = [
  { label: 'English', value: 'en' },
  { label: 'Español', value: 'es' },
  { label: 'Français', value: 'fr' },
  { label: 'עברית', value: 'he' },
  { label: 'العربية', value: 'ar' },
];

const timezoneOptions = [
  { label: 'UTC', value: 'UTC' },
  { label: 'America/New York (EST/EDT)', value: 'America/New_York' },
  { label: 'Europe/London (GMT/BST)', value: 'Europe/London' },
  { label: 'Asia/Jerusalem (IST/IDT)', value: 'Asia/Jerusalem' },
];

const dateFormatOptions = [
  { label: 'MM/DD/YYYY', value: 'MM/DD/YYYY' },
  { label: 'DD/MM/YYYY', value: 'DD/MM/YYYY' },
  { label: 'YYYY-MM-DD', value: 'YYYY-MM-DD' },
];

// Save functions
const saveBrandingSettings = async () => {
  loading.value = true;
  saveStatus.value = 'saving';

  try {
    await patch('/settings/branding', branding);
    Object.assign(originalBranding, branding);
    saveStatus.value = 'saved';
    showToast({
      title: 'Success',
      message: 'Branding settings saved successfully',
      type: 'success'
    });

    setTimeout(() => {
      saveStatus.value = null;
    }, 3000);
  } catch (error) {
    saveStatus.value = 'error';
    showToast({
      title: 'Error',
      message: 'Failed to save branding settings',
      type: 'error'
    });
    console.error('Error saving branding settings:', error);
  } finally {
    loading.value = false;
  }
};

const saveRegionalSettings = async () => {
  loading.value = true;
  saveStatus.value = 'saving';

  try {
    await patch('/settings/regional', regionalSettings);
    Object.assign(originalRegionalSettings, regionalSettings);
    saveStatus.value = 'saved';
    showToast({
      title: 'Success',
      message: 'Regional settings saved successfully',
      type: 'success'
    });

    setTimeout(() => {
      saveStatus.value = null;
    }, 3000);
  } catch (error) {
    saveStatus.value = 'error';
    showToast({
      title: 'Error',
      message: 'Failed to save regional settings',
      type: 'error'
    });
    console.error('Error saving regional settings:', error);
  } finally {
    loading.value = false;
  }
};

const saveSupportSettings = async () => {
  loading.value = true;
  saveStatus.value = 'saving';

  try {
    await patch('/settings/support', supportInfo);
    Object.assign(originalSupportInfo, supportInfo);
    saveStatus.value = 'saved';
    showToast({
      title: 'Success',
      message: 'Support settings saved successfully',
      type: 'success'
    });

    setTimeout(() => {
      saveStatus.value = null;
    }, 3000);
  } catch (error) {
    saveStatus.value = 'error';
    showToast({
      title: 'Error',
      message: 'Failed to save support settings',
      type: 'error'
    });
    console.error('Error saving support settings:', error);
  } finally {
    loading.value = false;
  }
};

// Reset functions
const resetBrandingSettings = () => {
  Object.assign(branding, originalBranding);
  showToast({
    title: 'Reset',
    message: 'Branding settings reset',
    type: 'info'
  });
};

const resetRegionalSettings = () => {
  Object.assign(regionalSettings, originalRegionalSettings);
  showToast({
    title: 'Reset',
    message: 'Regional settings reset',
    type: 'info'
  });
};

const resetSupportSettings = () => {
  Object.assign(supportInfo, originalSupportInfo);
  showToast({
    title: 'Reset',
    message: 'Support settings reset',
    type: 'info'
  });
};
</script>
