<template>
  <div>
    <!-- Enhanced Loading State with Skeleton -->
    <div v-if="loading" :class="gridClasses">
      <div
        v-for="i in loadingCount"
        :key="`skeleton-${i}`"
        class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden animate-pulse-enhanced"
        :style="{ animationDelay: `${i * 100}ms` }"
      >
        <!-- Card Header Skeleton -->
        <div class="p-6 pb-4">
          <div class="flex items-start justify-between mb-4">
            <div class="flex items-center gap-3 flex-1">
              <!-- Avatar Skeleton -->
              <div class="w-12 h-12 bg-gray-200 dark:bg-gray-700 rounded-full animate-shimmer"></div>

              <!-- Content Skeleton -->
              <div class="flex-1 space-y-2">
                <div class="h-5 bg-gray-200 dark:bg-gray-700 rounded animate-shimmer" :style="{ width: `${60 + (i % 3) * 15}%` }"></div>
                <div class="h-3 bg-gray-200 dark:bg-gray-700 rounded animate-shimmer" :style="{ width: `${40 + (i % 4) * 10}%` }"></div>
              </div>
            </div>

            <!-- Menu Button Skeleton -->
            <div class="w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded animate-shimmer"></div>
          </div>

          <!-- Badges Skeleton -->
          <div class="flex gap-2 mb-4">
            <div v-for="j in (i % 3) + 1" :key="j" class="h-6 bg-gray-200 dark:bg-gray-700 rounded-full animate-shimmer" :style="{ width: `${50 + j * 20}px` }"></div>
          </div>

          <!-- Metadata Skeleton -->
          <div class="space-y-2">
            <div v-for="k in (i % 2) + 2" :key="k" class="flex items-center gap-2">
              <div class="w-4 h-4 bg-gray-200 dark:bg-gray-700 rounded animate-shimmer"></div>
              <div class="h-3 bg-gray-200 dark:bg-gray-700 rounded animate-shimmer" :style="{ width: `${80 + k * 15}px` }"></div>
            </div>
          </div>
        </div>

        <!-- Card Footer Skeleton -->
        <div class="px-6 py-4 bg-gray-50 dark:bg-gray-700/50 border-t border-gray-100 dark:border-gray-700">
          <div class="flex items-center justify-between">
            <div class="flex gap-2">
              <div v-for="l in (i % 2) + 1" :key="l" class="h-8 bg-gray-200 dark:bg-gray-700 rounded animate-shimmer" :style="{ width: `${60 + l * 20}px` }"></div>
            </div>
            <div class="w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded animate-shimmer"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div v-else-if="!items || items.length === 0" class="text-center py-12">
      <Icon :name="emptyStateIcon" class="h-16 w-16 text-gray-400 mx-auto mb-4" />
      <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">{{ emptyStateTitle }}</h3>
      <p class="text-gray-500 dark:text-gray-400 mb-6">
        {{ emptyStateDescription }}
      </p>
      <slot name="empty-action">
        <UiButton variant="primary" @click="$emit('empty-action')">
          <Icon name="material-symbols:add" class="h-4 w-4 mr-2" />
          {{ emptyActionLabel }}
        </UiButton>
      </slot>
    </div>

    <!-- Enhanced Items Grid -->
    <div v-else :class="gridClasses">
      <div
        v-for="(item, index) in items"
        :key="getItemKey(item)"
        class="group relative bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden transition-all duration-300 ease-out cursor-pointer animate-fade-in-up hover:animate-none"
        :style="{ animationDelay: `${index * 100}ms` }"
        @click="handleCardClick(item)"
        @mouseenter="handleCardHover(item, true)"
        @mouseleave="handleCardHover(item, false)"
      >
        <!-- Enhanced Hover Effects -->
        <div class="absolute inset-0 bg-gradient-to-br from-brandPrimary/5 to-brandSecondary/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
        <div class="absolute inset-0 ring-0 ring-brandPrimary/20 group-hover:ring-2 transition-all duration-300 rounded-xl pointer-events-none"></div>

        <!-- Floating Action Indicator -->
        <div class="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transform translate-y-2 group-hover:translate-y-0 transition-all duration-300 z-10">
          <div class="w-8 h-8 bg-brandPrimary text-white rounded-full flex items-center justify-center shadow-lg">
            <Icon name="material-symbols:arrow-outward" class="h-4 w-4" />
          </div>
        </div>
        <!-- Enhanced Card Header -->
        <div class="relative p-6 pb-4">
          <div class="flex items-start justify-between mb-4">
            <div class="flex items-center gap-4 flex-1 min-w-0">
              <!-- Enhanced Image/Logo -->
              <div class="relative flex-shrink-0">
                <div class="absolute inset-0 bg-brandPrimary/20 rounded-full scale-0 group-hover:scale-110 transition-transform duration-300"></div>
                <img
                  v-if="getItemImage(item)"
                  :src="getItemImage(item) || ''"
                  :alt="`${getItemTitle(item)} image`"
                  class="relative w-14 h-14 rounded-full object-cover border-2 border-gray-100 dark:border-gray-700 group-hover:border-brandPrimary/30 transition-all duration-300 group-hover:scale-105"
                />
                <div
                  v-else
                  class="relative w-14 h-14 rounded-full bg-gradient-to-br from-brandPrimary/10 to-brandPrimary/20 flex items-center justify-center group-hover:from-brandPrimary/20 group-hover:to-brandPrimary/30 transition-all duration-300 group-hover:scale-105"
                >
                  <Icon :name="defaultIcon" class="h-7 w-7 text-brandPrimary group-hover:scale-110 transition-transform duration-300" />
                </div>

                <!-- Status Indicator -->
                <div class="absolute -bottom-1 -right-1 w-4 h-4 bg-green-400 border-2 border-white dark:border-gray-800 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              </div>

              <!-- Enhanced Item Info -->
              <div class="flex-1 min-w-0">
                <h3 class="text-lg font-bold text-gray-900 dark:text-white truncate group-hover:text-brandPrimary transition-all duration-300 group-hover:translate-x-1">
                  {{ getItemTitle(item) }}
                </h3>
                <p v-if="getItemSubtitle(item)" class="text-sm text-gray-500 dark:text-gray-400 truncate mt-1 group-hover:text-gray-600 dark:group-hover:text-gray-300 transition-colors duration-300">
                  {{ getItemSubtitle(item) }}
                </p>

                <!-- Quick Stats -->
                <div class="flex items-center gap-3 mt-2 opacity-0 group-hover:opacity-100 transform translate-y-2 group-hover:translate-y-0 transition-all duration-300">
                  <div class="flex items-center gap-1 text-xs text-gray-400">
                    <Icon name="material-symbols:schedule" class="h-3 w-3" />
                    <span>Updated recently</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- Quick Actions Menu -->
            <div class="relative">
              <UiButton
                @click="toggleActionsMenu(getItemKey(item))"
                size="sm"
                variant="ghost"
                class="opacity-0 group-hover:opacity-100 transition-opacity duration-200"
              >
                <Icon name="material-symbols:more-vert" class="h-4 w-4" />
              </UiButton>

              <!-- Actions Dropdown -->
              <div
                v-if="activeMenu === getItemKey(item)"
                class="absolute right-0 top-full mt-1 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 py-1 z-10"
              >
                <template v-for="action in getMenuActions(item)" :key="action.key">
                  <button
                    v-if="!action.condition || action.condition(item)"
                    @click="handleAction(action.key, item)"
                    :class="[
                      'w-full text-left px-4 py-2 text-sm hover:bg-gray-50 dark:hover:bg-gray-700 flex items-center gap-2',
                      action.destructive ? 'text-red-600 dark:text-red-400' : 'text-gray-700 dark:text-gray-300'
                    ]"
                  >
                    <Icon :name="action.icon" class="h-4 w-4" />
                    {{ action.label }}
                  </button>
                </template>
              </div>
            </div>
          </div>

          <!-- Enhanced Badges -->
          <div v-if="getItemBadges(item).length > 0" class="flex flex-wrap gap-2 mb-4">
            <UiBadge
              v-for="(badge, badgeIndex) in getItemBadges(item)"
              :key="badgeIndex"
              :color="badge.variant"
              variant="subtle"
              size="sm"
              :animate="true"
              :style="{ animationDelay: `${badgeIndex * 100}ms` }"
              class="transform group-hover:scale-105 transition-transform duration-200"
            >
              {{ badge.text }}
            </UiBadge>
          </div>

          <!-- Enhanced Metadata -->
          <div v-if="getItemMetadata(item).length > 0" class="space-y-3 mb-4">
            <div
              v-for="(meta, metaIndex) in getItemMetadata(item)"
              :key="metaIndex"
              class="flex items-center gap-3 text-sm group/meta"
              :style="{ animationDelay: `${metaIndex * 150}ms` }"
            >
              <div class="flex-shrink-0">
                <div class="w-8 h-8 rounded-lg bg-gray-100 dark:bg-gray-700 flex items-center justify-center group-hover:bg-brandPrimary/10 transition-colors duration-300">
                  <Icon
                    v-if="meta.icon"
                    :name="meta.icon"
                    class="h-4 w-4 text-gray-500 dark:text-gray-400 group-hover:text-brandPrimary transition-colors duration-300"
                  />
                </div>
              </div>
              <div class="flex-1 min-w-0">
                <div class="flex items-center justify-between">
                  <span class="font-medium text-gray-600 dark:text-gray-300 text-xs uppercase tracking-wide">{{ meta.label }}</span>
                  <span class="text-gray-900 dark:text-white font-semibold truncate ml-2">{{ meta.value }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Enhanced Card Footer -->
        <div class="relative px-6 py-4 bg-gradient-to-r from-gray-50 to-gray-100/50 dark:from-gray-700/50 dark:to-gray-600/30 border-t border-gray-100 dark:border-gray-700">
          <!-- Background Pattern -->
          <div class="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
            <div class="absolute inset-0 bg-gradient-to-r from-brandPrimary/5 to-brandSecondary/5"></div>
          </div>

          <div class="relative flex items-center justify-between">
            <!-- Enhanced Primary Actions -->
            <div class="flex items-center gap-2">
              <template v-for="(action, actionIndex) in getItemActions(item)" :key="action.key">
                <UiButton
                  v-if="!action.condition || action.condition(item)"
                  @click.stop="handleAction(action.key, item)"
                  :variant="action.variant"
                  size="sm"
                  :ripple="true"
                  class="transform group-hover:scale-105 transition-all duration-200"
                  :style="{ animationDelay: `${actionIndex * 100}ms` }"
                >
                  <Icon :name="action.icon" class="h-4 w-4 mr-1" />
                  {{ action.label }}
                </UiButton>
              </template>

              <!-- Default action if no actions provided -->
              <UiButton
                v-if="getItemActions(item).length === 0"
                @click.stop="handleAction('view', item)"
                variant="primary"
                size="sm"
                :ripple="true"
                class="transform group-hover:scale-105 transition-all duration-200"
              >
                <Icon name="material-symbols:visibility" class="h-4 w-4 mr-1" />
                View Details
              </UiButton>
            </div>

            <!-- Enhanced Quick Actions -->
            <div class="flex items-center gap-1">
              <UiButton
                @click.stop="handleAction('favorite', item)"
                size="sm"
                variant="ghost"
                shape="circle"
                :ripple="true"
                class="opacity-0 group-hover:opacity-100 transform translate-x-2 group-hover:translate-x-0 transition-all duration-300 text-gray-400 hover:text-red-500"
                aria-label="Add to favorites"
              >
                <Icon name="material-symbols:favorite-border" class="h-4 w-4" />
              </UiButton>

              <UiButton
                @click.stop="handleAction('share', item)"
                size="sm"
                variant="ghost"
                shape="circle"
                :ripple="true"
                class="opacity-0 group-hover:opacity-100 transform translate-x-2 group-hover:translate-x-0 transition-all duration-300 delay-75 text-gray-400 hover:text-brandPrimary"
                aria-label="Share item"
              >
                <Icon name="material-symbols:share" class="h-4 w-4" />
              </UiButton>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" generic="T">
import { ref, computed, onMounted, onUnmounted } from 'vue'

interface CardConfig {
  title: (item: T) => string
  subtitle?: (item: T) => string
  image?: (item: T) => string | null
  badges?: (item: T) => Array<{ text: string; variant: string }>
  metadata?: (item: T) => Array<{ label: string; value: string; icon?: string }>
  actions?: Array<{
    key: string
    label: string
    icon: string
    variant: string
    condition?: (item: T) => boolean
  }>
  menuActions?: Array<{
    key: string
    label: string
    icon: string
    destructive?: boolean
    condition?: (item: T) => boolean
  }>
}

interface Props {
  items: T[]
  loading: boolean
  cardConfig: CardConfig
  itemKey?: string
  gridCols?: string
  loadingCount?: number
  emptyStateTitle?: string
  emptyStateDescription?: string
  emptyStateIcon?: string
  emptyActionLabel?: string
  defaultIcon?: string
  animated?: boolean
  hoverEffects?: boolean
  compactMode?: boolean
}

interface Emits {
  (e: 'item-action', payload: { action: string; item: T }): void
  (e: 'empty-action'): void
  (e: 'card-click', payload: { item: T }): void
  (e: 'card-hover', payload: { item: T; isHovering: boolean }): void
}

const props = withDefaults(defineProps<Props>(), {
  itemKey: 'id',
  gridCols: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
  loadingCount: 8,
  emptyStateTitle: 'No items found',
  emptyStateDescription: 'Get started by creating your first item.',
  emptyStateIcon: 'material-symbols:inbox',
  emptyActionLabel: 'Create Item',
  defaultIcon: 'material-symbols:folder',
  animated: true,
  hoverEffects: true,
  compactMode: false,
})

const emit = defineEmits<Emits>()

// State
const activeMenu = ref<string | null>(null)
const hoveredCard = ref<string | null>(null)

// Computed
const gridClasses = computed(() => {
  const baseClasses = `grid ${props.gridCols} gap-6`
  const animationClasses = props.animated ? 'animate-stagger-children' : ''
  return `${baseClasses} ${animationClasses}`.trim()
})

// Methods
const getItemKey = (item: T): string => {
  return (item as any)[props.itemKey] || Math.random().toString()
}

const getItemTitle = (item: T): string => {
  return props.cardConfig.title(item)
}

const getItemSubtitle = (item: T): string => {
  return props.cardConfig.subtitle?.(item) || ''
}

const getItemImage = (item: T): string | null => {
  return props.cardConfig.image?.(item) || null
}

const getItemBadges = (item: T) => {
  return props.cardConfig.badges?.(item) || []
}

const getItemMetadata = (item: T) => {
  return props.cardConfig.metadata?.(item) || []
}

const getItemActions = (_item: T) => {
  return props.cardConfig.actions || []
}

const getMenuActions = (_item: T) => {
  return props.cardConfig.menuActions || []
}

const toggleActionsMenu = (itemKey: string) => {
  activeMenu.value = activeMenu.value === itemKey ? null : itemKey
}

const handleAction = (action: string, item: T) => {
  activeMenu.value = null
  emit('item-action', { action, item })
}

// Enhanced interaction methods
const handleCardClick = (item: T) => {
  emit('card-click', { item })
}

const handleCardHover = (item: T, isHovering: boolean) => {
  const itemKey = getItemKey(item)
  hoveredCard.value = isHovering ? itemKey : null
  emit('card-hover', { item, isHovering })
}

// Close menu when clicking outside
const handleClickOutside = (event: Event) => {
  const target = event.target as Element
  if (!target.closest('.relative')) {
    activeMenu.value = null
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<style scoped>
/* Enhanced animations for cards grid */
@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

@keyframes pulse-enhanced {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

@keyframes stagger-children {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.animate-fade-in-up {
  animation: fade-in-up 0.6s ease-out;
}

/* Removed duplicate shimmer definition - using enhanced version below */

.animate-pulse-enhanced {
  animation: pulse-enhanced 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-stagger-children > * {
  animation: fade-in-up 0.6s ease-out;
}

/* Enhanced card hover effects */
.group:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Removed duplicate dark mode shimmer - using enhanced version below */

/* Enhanced focus states for accessibility */
.group:focus-visible {
  outline: 2px solid var(--color-brandPrimary);
  outline-offset: 4px;
}

/* Smooth transitions for all interactive elements */
.group * {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
}

/* Enhanced loading skeleton gradients - Fixed shimmer effect */
.animate-shimmer {
  background: linear-gradient(
    90deg,
    #f3f4f6 0%,
    #e5e7eb 20%,
    #f9fafb 60%,
    #f3f4f6 100%
  );
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

.dark .animate-shimmer {
  background: linear-gradient(
    90deg,
    #374151 0%,
    #4b5563 20%,
    #9ca3af 60%,
    #374151 100%
  );
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

/* Compact mode styles */
.compact-mode .group {
  padding: 1rem;
}

.compact-mode .group h3 {
  font-size: 1rem;
}

.compact-mode .group .w-14 {
  width: 2.5rem;
  height: 2.5rem;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .animate-fade-in-up,
  .animate-shimmer,
  .animate-pulse-enhanced,
  .animate-stagger-children > * {
    animation: none;
  }

  .group:hover {
    transform: none;
  }

  .group * {
    transition: none;
  }
}

/* Enhanced responsive design */
@media (max-width: 640px) {
  .group {
    margin-bottom: 1rem;
  }

  .group:hover {
    transform: translateY(-2px) scale(1.01);
  }
}

/* Print styles */
@media print {
  .group {
    break-inside: avoid;
    box-shadow: none;
    border: 1px solid #e5e7eb;
  }

  .group:hover {
    transform: none;
    box-shadow: none;
  }
}
</style>
