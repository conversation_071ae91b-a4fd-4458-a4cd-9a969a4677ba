<template>
  <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 px-6 py-4">
    <div class="flex flex-col sm:flex-row items-center justify-between gap-4">
      <!-- Results Info -->
      <div class="flex items-center gap-4">
        <div class="text-sm text-gray-700 dark:text-gray-300">
          Showing
          <span class="font-medium">{{ startItem }}</span>
          to
          <span class="font-medium">{{ endItem }}</span>
          of
          <span class="font-medium">{{ totalItems }}</span>
          results
        </div>
        
        <!-- Items per page selector -->
        <div class="flex items-center gap-2">
          <label class="text-sm text-gray-700 dark:text-gray-300">Show:</label>
          <UiSelect
            id="itemsPerPage"
            :model-value="itemsPerPage.toString()"
            @update:model-value="handleItemsPerPageChange"
            :options="itemsPerPageOptions"
            class="w-20"
          />
        </div>
      </div>

      <!-- Pagination Controls -->
      <div class="flex items-center gap-2">
        <!-- First Page -->
        <UiButton
          @click="goToPage(1)"
          :disabled="currentPage === 1"
          variant="outline"
          size="sm"
          class="hidden sm:flex"
        >
          <Icon name="material-symbols:first-page" class="h-4 w-4" />
        </UiButton>

        <!-- Previous Page -->
        <UiButton
          @click="goToPage(currentPage - 1)"
          :disabled="currentPage === 1"
          variant="outline"
          size="sm"
        >
          <Icon name="material-symbols:chevron-left" class="h-4 w-4" />
          <span class="hidden sm:inline ml-1">Previous</span>
        </UiButton>

        <!-- Page Numbers -->
        <div class="flex items-center gap-1">
          <!-- First page if not in visible range -->
          <template v-if="visiblePages[0] > 1">
            <UiButton
              @click="goToPage(1)"
              variant="outline"
              size="sm"
              class="w-10 h-10 p-0"
            >
              1
            </UiButton>
            <span v-if="visiblePages[0] > 2" class="px-2 text-gray-500">...</span>
          </template>

          <!-- Visible page numbers -->
          <UiButton
            v-for="page in visiblePages"
            :key="page"
            @click="goToPage(page)"
            :variant="page === currentPage ? 'primary' : 'outline'"
            size="sm"
            class="w-10 h-10 p-0"
          >
            {{ page }}
          </UiButton>

          <!-- Last page if not in visible range -->
          <template v-if="visiblePages[visiblePages.length - 1] < totalPages">
            <span v-if="visiblePages[visiblePages.length - 1] < totalPages - 1" class="px-2 text-gray-500">...</span>
            <UiButton
              @click="goToPage(totalPages)"
              variant="outline"
              size="sm"
              class="w-10 h-10 p-0"
            >
              {{ totalPages }}
            </UiButton>
          </template>
        </div>

        <!-- Next Page -->
        <UiButton
          @click="goToPage(currentPage + 1)"
          :disabled="currentPage === totalPages"
          variant="outline"
          size="sm"
        >
          <span class="hidden sm:inline mr-1">Next</span>
          <Icon name="material-symbols:chevron-right" class="h-4 w-4" />
        </UiButton>

        <!-- Last Page -->
        <UiButton
          @click="goToPage(totalPages)"
          :disabled="currentPage === totalPages"
          variant="outline"
          size="sm"
          class="hidden sm:flex"
        >
          <Icon name="material-symbols:last-page" class="h-4 w-4" />
        </UiButton>
      </div>
    </div>

    <!-- Mobile-friendly page input -->
    <div class="sm:hidden mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
      <div class="flex items-center justify-center gap-2">
        <span class="text-sm text-gray-700 dark:text-gray-300">Go to page:</span>
        <UiInput
          type="number"
          :min="1"
          :max="totalPages"
          :model-value="currentPage.toString()"
          @update:model-value="handlePageInput"
          @keyup.enter="handlePageInput"
          class="w-20 text-center"
        />
        <span class="text-sm text-gray-700 dark:text-gray-300">of {{ totalPages }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  currentPage: number
  totalPages: number
  totalItems: number
  itemsPerPage: number
}

interface Emits {
  (e: 'page-change', page: number): void
  (e: 'items-per-page-change', itemsPerPage: number): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Computed properties
const startItem = computed(() => {
  return (props.currentPage - 1) * props.itemsPerPage + 1
})

const endItem = computed(() => {
  return Math.min(props.currentPage * props.itemsPerPage, props.totalItems)
})

const visiblePages = computed(() => {
  const delta = 2 // Number of pages to show on each side of current page
  const range = []
  const rangeWithDots = []

  for (
    let i = Math.max(2, props.currentPage - delta);
    i <= Math.min(props.totalPages - 1, props.currentPage + delta);
    i++
  ) {
    range.push(i)
  }

  if (props.currentPage - delta > 2) {
    rangeWithDots.push(1, '...')
  } else {
    rangeWithDots.push(1)
  }

  rangeWithDots.push(...range)

  if (props.currentPage + delta < props.totalPages - 1) {
    rangeWithDots.push('...', props.totalPages)
  } else if (props.totalPages > 1) {
    rangeWithDots.push(props.totalPages)
  }

  return rangeWithDots.filter((page, index, array) => {
    // Remove duplicates
    return array.indexOf(page) === index
  }).filter(page => typeof page === 'number') as number[]
})

const itemsPerPageOptions = [
  { value: '10', label: '10' },
  { value: '25', label: '25' },
  { value: '50', label: '50' },
  { value: '100', label: '100' }
]

// Methods
const goToPage = (page: number) => {
  if (page >= 1 && page <= props.totalPages && page !== props.currentPage) {
    emit('page-change', page)
  }
}

const handleItemsPerPageChange = (value: string) => {
  const newItemsPerPage = parseInt(value, 10)
  if (newItemsPerPage !== props.itemsPerPage) {
    emit('items-per-page-change', newItemsPerPage)
  }
}

const handlePageInput = (value: string) => {
  const page = parseInt(value, 10)
  if (!isNaN(page)) {
    goToPage(page)
  }
}
</script>
