<template>
  <div class="ui-textarea-wrapper">
    <!-- Label -->
    <label
      v-if="label"
      :for="id"
      :class="[
        'block text-sm font-medium mb-2 transition-colors duration-200',
        errorMessage ? 'text-red-600' : 'text-gray-700',
        required ? 'after:content-[\'*\'] after:ml-1 after:text-red-500' : ''
      ]"
    >
      {{ label }}
    </label>

    <!-- Textarea Container -->
    <div class="relative">
      <!-- Leading Icon -->
      <div
        v-if="leadingIcon"
        class="absolute left-3 top-3 flex items-center pointer-events-none z-10"
      >
        <Icon :name="leadingIcon" :class="iconClasses" />
      </div>

      <!-- Enhanced Textarea -->
      <textarea
        :id="id"
        ref="textareaRef"
        v-model="value"
        :placeholder="placeholder"
        :rows="rows"
        :cols="cols"
        :required="required"
        :disabled="disabled"
        :readonly="readonly"
        :maxlength="maxlength"
        :minlength="minlength"
        :autocomplete="autocomplete"
        :spellcheck="spellcheck"
        :wrap="wrap"
        :resize="resize"
        :class="[
          'block w-full transition-all duration-200 ease-in-out',
          'border rounded-md leading-5 placeholder-gray-400',
          'focus:outline-none focus:ring-2 focus:ring-offset-0',
          'sm:text-sm font-medium',
          sizeClasses,
          stateClasses,
          resizeClasses,
          autoGrowClasses,
          leadingIcon ? 'pl-10' : '',
          trailingIcon || clearable || showCharCount ? 'pr-10' : '',
          attrs.class || ''
        ]"
        v-bind="filteredAttrs"
        @focus="handleFocus"
        @blur="handleBlur"
        @input="handleInput"
        @keydown="handleKeydown"
        @paste="handlePaste"
      />

      <!-- Trailing Icon / Clear Button -->
      <div
        v-if="trailingIcon || clearable"
        class="absolute right-3 top-3 flex items-center space-x-1"
      >
        <!-- Clear Button -->
        <button
          v-if="clearable && value && !disabled && !readonly"
          type="button"
          class="p-1 text-gray-400 hover:text-gray-600 transition-colors duration-200 rounded-sm hover:bg-gray-100"
          @click="clearValue"
          :aria-label="clearButtonLabel"
        >
          <Icon name="heroicons:x-mark" size="calc(var(--spacing) * 4)" />
        </button>

        <!-- Trailing Icon -->
        <Icon
          v-if="trailingIcon"
          :name="trailingIcon"
          :class="iconClasses"
          size="calc(var(--spacing) * 4)"

        />
      </div>

      <!-- Loading Spinner -->
      <div
        v-if="loading"
        class="absolute right-3 top-3 flex items-center"
      >
        <Icon name="heroicons:arrow-path" size="calc(var(--spacing) * 4)" class=" text-gray-400 animate-spin" />
      </div>
    </div>

    <!-- Character Count -->
    <div
      v-if="showCharCount && maxlength"
      class="flex justify-between items-center mt-1"
    >
      <div></div>
      <span
        :class="[
          'text-xs transition-colors duration-200',
          characterCountExceeded ? 'text-red-600' : 'text-gray-500'
        ]"
      >
        {{ characterCount }}/{{ maxlength }}
      </span>
    </div>

    <!-- Help Text -->
    <p
      v-if="helpText && !errorMessage && !successMessage"
      class="mt-2 text-xs text-gray-600"
    >
      {{ helpText }}
    </p>

    <!-- Success Message -->
    <div
      v-if="successMessage && showSuccess"
      class="mt-2 flex items-center text-sm text-green-600"
    >
      <Icon name="heroicons:check-circle" size="calc(var(--spacing) * 4)" class=" mr-1 flex-shrink-0" />
      {{ successMessage }}
    </div>

    <!-- Error Message -->
    <div
      v-if="errorMessage"
      class="mt-2 flex items-center text-sm text-red-600"
    >
      <Icon name="heroicons:exclamation-circle" size="calc(var(--spacing) * 4)" class=" mr-1 flex-shrink-0" />
      {{ errorMessage }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, computed, useAttrs, defineEmits, ref, nextTick, watch, onMounted } from 'vue';

interface Props {
  id: string;
  modelValue?: string | null;
  errorMessage?: string;
  label?: string;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  readonly?: boolean;
  helpText?: string;
  successMessage?: string;
  leadingIcon?: string;
  trailingIcon?: string;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'filled' | 'outlined';
  clearable?: boolean;
  loading?: boolean;
  showCharCount?: boolean;
  showSuccess?: boolean;
  autocomplete?: string;
  maxlength?: number;
  minlength?: number;
  rows?: number;
  cols?: number;
  spellcheck?: boolean;
  wrap?: 'soft' | 'hard' | 'off';
  resize?: 'none' | 'both' | 'horizontal' | 'vertical' | 'auto';
  autoResize?: boolean;
  autoGrow?: boolean;
  minHeight?: number;
  maxHeight?: number;
  growTransition?: boolean;
  clearButtonLabel?: string;
  name?: string;
}

interface Emits {
  (e: 'update:modelValue', payload: string | null | undefined): void;
  (e: 'focus', event: FocusEvent): void;
  (e: 'blur', event: FocusEvent): void;
  (e: 'input', event: Event): void;
  (e: 'keydown', event: KeyboardEvent): void;
  (e: 'paste', event: ClipboardEvent): void;
  (e: 'clear'): void;
}

const props = withDefaults(defineProps<Props>(), {
  size: 'md',
  variant: 'default',
  clearable: false,
  loading: false,
  showCharCount: false,
  showSuccess: false,
  readonly: false,
  rows: 4,
  spellcheck: true,
  wrap: 'soft',
  resize: 'vertical',
  autoResize: false,
  autoGrow: false,
  minHeight: 80,
  maxHeight: 400,
  growTransition: true,
  clearButtonLabel: 'Clear text',
});

const emit = defineEmits<Emits>();

// Refs
const textareaRef = ref<HTMLTextAreaElement | null>(null);

// Attrs handling
const attrs = useAttrs();
const filteredAttrs = computed(() => {
  const { class: _, ...rest } = attrs;
  return rest;
});

// Internal value management
const value = computed({
  get: () => props.modelValue || '',
  set: (newValue: string) => {
    emit('update:modelValue', newValue);
  }
});

// Character count
const characterCount = computed(() => {
  return (value.value || '').length;
});

const characterCountExceeded = computed(() => {
  return props.maxlength ? characterCount.value > props.maxlength : false;
});

// Size classes
const sizeClasses = computed(() => {
  const sizes = {
    sm: 'px-3 py-2 text-sm',
    md: 'px-3 py-2.5 text-sm',
    lg: 'px-4 py-3 text-base'
  };
  return sizes[props.size];
});

// State classes
const stateClasses = computed(() => {
  if (props.disabled) {
    return 'bg-gray-50 border-gray-200 text-gray-500 cursor-not-allowed';
  }

  if (props.readonly) {
    return 'bg-gray-50 border-gray-200 text-gray-700 cursor-default';
  }

  if (props.errorMessage) {
    return 'border-red-300 text-red-900 placeholder-red-300 focus:ring-red-500 focus:border-red-500';
  }

  if (props.successMessage && props.showSuccess) {
    return 'border-green-300 text-green-900 placeholder-green-300 focus:ring-green-500 focus:border-green-500';
  }

  // Variant-based classes
  switch (props.variant) {
    case 'filled':
      return 'bg-gray-50 border-gray-200 text-gray-900 focus:bg-white focus:ring-brandPrimary-500 focus:border-brandPrimary-500';
    case 'outlined':
      return 'bg-transparent border-gray-300 text-gray-900 focus:ring-brandPrimary-500 focus:border-brandPrimary-500';
    default:
      return 'bg-white border-gray-300 text-gray-900 focus:ring-brandPrimary-500 focus:border-brandPrimary-500';
  }
});

// Resize classes
const resizeClasses = computed(() => {
  // If auto-resize or auto-grow is enabled, disable manual resize
  if (props.autoResize || props.autoGrow) {
    return 'resize-none';
  }

  const resizeMap = {
    none: 'resize-none',
    both: 'resize',
    horizontal: 'resize-x',
    vertical: 'resize-y',
    auto: 'resize-none'
  };
  return resizeMap[props.resize];
});

// Auto-grow classes
const autoGrowClasses = computed(() => {
  const classes = [];

  if (props.autoGrow || props.autoResize) {
    classes.push('overflow-hidden');

    if (props.growTransition) {
      classes.push('transition-all duration-200 ease-in-out');
    }
  }

  return classes.join(' ');
});

// Icon classes
const iconClasses = computed(() => {
  const baseClasses = ' transition-colors duration-200';

  if (props.disabled) {
    return `${baseClasses} text-gray-400`;
  }

  if (props.errorMessage) {
    return `${baseClasses} text-red-400`;
  }

  if (props.successMessage && props.showSuccess) {
    return `${baseClasses} text-green-400`;
  }

  return `${baseClasses} text-gray-500`;
});

// Event handlers
const handleFocus = (event: FocusEvent) => {
  emit('focus', event);
};

const handleBlur = (event: FocusEvent) => {
  emit('blur', event);
};

const handleInput = (event: Event) => {
  const target = event.target as HTMLTextAreaElement;
  emit('update:modelValue', target.value);
  emit('input', event);

  // Auto-resize or auto-grow functionality
  if (props.autoResize || props.autoGrow) {
    nextTick(() => {
      autoGrowTextarea();
    });
  }
};

const handleKeydown = (event: KeyboardEvent) => {
  emit('keydown', event);

  // Handle Escape key to blur
  if (event.key === 'Escape') {
    textareaRef.value?.blur();
  }

  // Handle Ctrl+A to select all
  if (event.key === 'a' && (event.ctrlKey || event.metaKey)) {
    event.preventDefault();
    textareaRef.value?.select();
  }
};

const handlePaste = (event: ClipboardEvent) => {
  emit('paste', event);

  // Auto-resize after paste
  if (props.autoResize || props.autoGrow) {
    nextTick(() => {
      autoGrowTextarea();
    });
  }
};

const clearValue = () => {
  emit('update:modelValue', '');
  emit('clear');
  textareaRef.value?.focus();

  // Reset height after clearing if auto-grow is enabled
  if (props.autoGrow || props.autoResize) {
    nextTick(() => {
      autoGrowTextarea();
    });
  }
};

// Enhanced auto-grow functionality
const autoGrowTextarea = () => {
  if (!textareaRef.value || (!props.autoResize && !props.autoGrow)) return;

  const textarea = textareaRef.value;

  // Store current scroll position
  const scrollTop = textarea.scrollTop;

  // Reset height to auto to get the natural height
  textarea.style.height = 'auto';

  // Calculate the new height
  let newHeight = textarea.scrollHeight;

  // Apply min/max height constraints for autoGrow
  if (props.autoGrow) {
    newHeight = Math.max(newHeight, props.minHeight);
    newHeight = Math.min(newHeight, props.maxHeight);
  }

  // Set the new height
  textarea.style.height = `${newHeight}px`;

  // Restore scroll position if needed
  if (scrollTop > 0) {
    textarea.scrollTop = scrollTop;
  }

  // Handle overflow for max height constraint
  if (props.autoGrow && textarea.scrollHeight > props.maxHeight) {
    textarea.style.overflowY = 'auto';
  } else {
    textarea.style.overflowY = 'hidden';
  }
};

// Watch for value changes to trigger auto-grow
watch(
  () => props.modelValue,
  () => {
    if (props.autoResize || props.autoGrow) {
      nextTick(() => {
        autoGrowTextarea();
      });
    }
  }
);

// Initialize auto-grow on mount
onMounted(() => {
  if (props.autoResize || props.autoGrow) {
    nextTick(() => {
      autoGrowTextarea();
    });
  }
});

// Focus method for external access
const focus = () => {
  textareaRef.value?.focus();
};

const blur = () => {
  textareaRef.value?.blur();
};

const select = () => {
  textareaRef.value?.select();
};

// Expose methods for template refs
defineExpose({
  focus,
  blur,
  select,
  textareaRef
});
</script>