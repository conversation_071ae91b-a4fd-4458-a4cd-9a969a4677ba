<template>
  <div class="donut-examples space-y-8">
    <!-- Basic Donut Chart -->
    <div class="example-section">
      <h3 class="text-lg font-semibold mb-4">Basic Donut Chart</h3>
      
      <div class="bg-white dark:bg-gray-800 p-6 rounded-lg border mb-6">
        <h4 class="text-md font-medium mb-4">Simple Donut Chart</h4>
        <div class="max-w-md mx-auto">
          <UiDonutChart
            :data="basicDonutData"
            :height="300"
            :hide-legend="true"
            :radius="0"
            :center-value="basicTotal"
            center-label="Total Items"
          />
        </div>
      </div>

      <!-- Code Example -->
      <details class="mt-4">
        <summary class="cursor-pointer text-sm font-medium text-gray-600 dark:text-gray-400">
          Show Code
        </summary>
        <pre class="mt-2 p-4 bg-gray-100 dark:bg-gray-900 rounded text-sm overflow-x-auto"><code>&lt;UiDonutChart
  :data="[
    { color: '#3b82f6', name: 'Blue', value: 50 },
    { color: '#a855f7', name: '<PERSON>', value: 20 },
    { color: '#22c55e', name: 'Green', value: 30 }
  ]"
  :height="300"
  :hide-legend="true"
  :radius="0"
  :center-value="100"
  center-label="Total Items"
/&gt;</code></pre>
      </details>
    </div>

    <!-- Donut Chart with Custom Center -->
    <div class="example-section">
      <h3 class="text-lg font-semibold mb-4">Custom Center Content</h3>
      
      <div class="bg-white dark:bg-gray-800 p-6 rounded-lg border mb-6">
        <h4 class="text-md font-medium mb-4">Custom Center Slot</h4>
        <div class="max-w-md mx-auto">
          <UiDonutChart
            :data="customCenterData"
            :height="300"
            :hide-legend="true"
            :radius="0"
          >
            <template #center>
              <div class="text-center">
                <div class="font-semibold text-2xl text-gray-900 dark:text-white">
                  {{ customCenterTotal }}
                </div>
                <div class="text-(--ui-text-muted) text-sm">
                  Total Sales
                </div>
                <div class="text-xs text-green-600 mt-1">
                  +12% from last month
                </div>
              </div>
            </template>
          </UiDonutChart>
        </div>
      </div>

      <!-- Code Example -->
      <details class="mt-4">
        <summary class="cursor-pointer text-sm font-medium text-gray-600 dark:text-gray-400">
          Show Code
        </summary>
        <pre class="mt-2 p-4 bg-gray-100 dark:bg-gray-900 rounded text-sm overflow-x-auto"><code>&lt;UiDonutChart :data="data" :height="300"&gt;
  &lt;template #center&gt;
    &lt;div class="text-center"&gt;
      &lt;div class="font-semibold text-2xl"&gt;{{ total }}&lt;/div&gt;
      &lt;div class="text-sm text-gray-500"&gt;Total Sales&lt;/div&gt;
      &lt;div class="text-xs text-green-600"&gt;+12%&lt;/div&gt;
    &lt;/div&gt;
  &lt;/template&gt;
&lt;/UiDonutChart&gt;</code></pre>
      </details>
    </div>

    <!-- Donut Chart with Legend -->
    <div class="example-section">
      <h3 class="text-lg font-semibold mb-4">With Custom Legend</h3>
      
      <div class="bg-white dark:bg-gray-800 p-6 rounded-lg border mb-6">
        <h4 class="text-md font-medium mb-4">Interactive Legend</h4>
        <div class="max-w-lg mx-auto">
          <UiDonutChart
            :data="legendData"
            :height="250"
            :hide-legend="false"
            :show-legend="true"
            :radius="0"
            @segment-toggle="onSegmentToggle"
          />
        </div>
      </div>

      <!-- Code Example -->
      <details class="mt-4">
        <summary class="cursor-pointer text-sm font-medium text-gray-600 dark:text-gray-400">
          Show Code
        </summary>
        <pre class="mt-2 p-4 bg-gray-100 dark:bg-gray-900 rounded text-sm overflow-x-auto"><code>&lt;UiDonutChart
  :data="data"
  :height="250"
  :hide-legend="false"
  :show-legend="true"
  :radius="0"
  @segment-toggle="onSegmentToggle"
/&gt;</code></pre>
      </details>
    </div>

    <!-- Different Sizes -->
    <div class="example-section">
      <h3 class="text-lg font-semibold mb-4">Different Sizes</h3>
      
      <div class="bg-white dark:bg-gray-800 p-6 rounded-lg border mb-6">
        <h4 class="text-md font-medium mb-4">Various Chart Sizes</h4>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div class="text-center">
            <UiDonutChart
              :data="sizeData"
              :height="200"
              :hide-legend="true"
              :radius="0"
              :center-value="sizeTotal"
              center-label="Small"
            />
            <p class="text-sm text-gray-600 dark:text-gray-400 mt-2">200px Height</p>
          </div>
          
          <div class="text-center">
            <UiDonutChart
              :data="sizeData"
              :height="275"
              :hide-legend="true"
              :radius="0"
              :center-value="sizeTotal"
              center-label="Medium"
            />
            <p class="text-sm text-gray-600 dark:text-gray-400 mt-2">275px Height</p>
          </div>
          
          <div class="text-center">
            <UiDonutChart
              :data="sizeData"
              :height="350"
              :hide-legend="true"
              :radius="0"
              :center-value="sizeTotal"
              center-label="Large"
            />
            <p class="text-sm text-gray-600 dark:text-gray-400 mt-2">350px Height</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Interactive Example -->
    <div class="example-section">
      <h3 class="text-lg font-semibold mb-4">Interactive Donut Chart</h3>
      
      <div class="bg-white dark:bg-gray-800 p-6 rounded-lg border mb-6">
        <h4 class="text-md font-medium mb-4">Dynamic Data Updates</h4>
        
        <div class="mb-4 flex gap-2">
          <UiButton @click="randomizeData" size="sm">
            Randomize Data
          </UiButton>
          <UiButton @click="addSegment" size="sm" variant="secondary">
            Add Segment
          </UiButton>
          <UiButton @click="removeSegment" size="sm" variant="ghost">
            Remove Segment
          </UiButton>
        </div>
        
        <div class="max-w-md mx-auto">
          <UiDonutChart
            :data="interactiveData"
            :height="300"
            :hide-legend="false"
            :show-legend="true"
            :radius="0"
            :center-value="interactiveTotal"
            center-label="Dynamic Total"
          />
        </div>
        
        <div class="mt-4 text-center">
          <p class="text-sm text-gray-600 dark:text-gray-400">
            Total: {{ interactiveTotal }} | Segments: {{ interactiveData.length }}
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

// Basic donut data
const basicDonutData = ref([
  { color: '#3b82f6', name: 'Blue', value: 50 },
  { color: '#a855f7', name: 'Purple', value: 20 },
  { color: '#22c55e', name: 'Green', value: 30 }
])

// Custom center data
const customCenterData = ref([
  { color: '#10b981', name: 'Q1 Sales', value: 25000 },
  { color: '#f59e0b', name: 'Q2 Sales', value: 30000 },
  { color: '#ef4444', name: 'Q3 Sales', value: 28000 },
  { color: '#8b5cf6', name: 'Q4 Sales', value: 35000 }
])

// Legend data
const legendData = ref([
  { color: '#3b82f6', name: 'Desktop', value: 45 },
  { color: '#10b981', name: 'Mobile', value: 35 },
  { color: '#f59e0b', name: 'Tablet', value: 20 }
])

// Size data
const sizeData = ref([
  { color: '#ec4899', name: 'Category A', value: 40 },
  { color: '#06b6d4', name: 'Category B', value: 35 },
  { color: '#84cc16', name: 'Category C', value: 25 }
])

// Interactive data
const interactiveData = ref([
  { color: '#3b82f6', name: 'Segment 1', value: 30 },
  { color: '#10b981', name: 'Segment 2', value: 25 },
  { color: '#f59e0b', name: 'Segment 3', value: 20 },
  { color: '#ef4444', name: 'Segment 4', value: 25 }
])

// Computed totals
const basicTotal = computed(() => 
  basicDonutData.value.reduce((sum, item) => sum + item.value, 0)
)

const customCenterTotal = computed(() => 
  customCenterData.value.reduce((sum, item) => sum + item.value, 0).toLocaleString()
)

const sizeTotal = computed(() => 
  sizeData.value.reduce((sum, item) => sum + item.value, 0)
)

const interactiveTotal = computed(() => 
  interactiveData.value.reduce((sum, item) => sum + item.value, 0)
)

// Available colors for new segments
const availableColors = [
  '#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', 
  '#06b6d4', '#84cc16', '#f97316', '#ec4899', '#6b7280'
]

// Methods
const onSegmentToggle = (index: number) => {
  console.log('Segment toggled:', index)
}

const randomizeData = () => {
  interactiveData.value = interactiveData.value.map(item => ({
    ...item,
    value: Math.floor(Math.random() * 50) + 10
  }))
}

const addSegment = () => {
  if (interactiveData.value.length < 8) {
    const newIndex = interactiveData.value.length
    interactiveData.value.push({
      color: availableColors[newIndex % availableColors.length],
      name: `Segment ${newIndex + 1}`,
      value: Math.floor(Math.random() * 30) + 10
    })
  }
}

const removeSegment = () => {
  if (interactiveData.value.length > 2) {
    interactiveData.value.pop()
  }
}
</script>

<style scoped>
.example-section {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 2rem;
}

.dark .example-section {
  border-bottom-color: #374151;
}

.example-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

details summary {
  transition: color 0.2s ease;
}

details summary:hover {
  color: #374151;
}

.dark details summary:hover {
  color: #d1d5db;
}

details[open] summary {
  margin-bottom: 0.5rem;
}

pre code {
  color: #374151;
}

.dark pre code {
  color: #d1d5db;
}
</style>
