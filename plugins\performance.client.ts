/**
 * Performance Monitoring Plugin
 * 
 * Client-side performance monitoring and optimization
 */

import { getPerformanceConfig, PERFORMANCE_THRESHOLDS } from '~/app/core/config/performance';
import { defineNuxtPlugin, useRouter } from '#app';
import type { RouteLocationNormalized } from 'vue-router';
 
export default defineNuxtPlugin(() => {
  const config = getPerformanceConfig()
  
  if (!config.monitoring.enabled) {
    return
  }

  // Performance Observer for Core Web Vitals
  if (config.monitoring.vitals.lcp || config.monitoring.vitals.fid || config.monitoring.vitals.cls) {
    const observer = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        // Largest Contentful Paint
        if (entry.entryType === 'largest-contentful-paint' && config.monitoring.vitals.lcp) {
          const lcp = entry.startTime
          console.log(`LCP: ${lcp}ms`, lcp > PERFORMANCE_THRESHOLDS.LCP ? '⚠️' : '✅')
          
          // Send to analytics if available
          if ((window as any).gtag) {
            (window as any).gtag('event', 'web_vitals', {
              name: 'LCP',
              value: Math.round(lcp),
              event_category: 'performance'
            })
          }
        }
        
        // First Input Delay
        if (entry.entryType === 'first-input' && config.monitoring.vitals.fid) {
          if ('processingStart' in entry && 'startTime' in entry) {
            const firstInputEntry = entry as PerformanceEventTiming;
            const fid = firstInputEntry.processingStart - firstInputEntry.startTime;
            console.log(`FID: ${fid}ms`, fid > PERFORMANCE_THRESHOLDS.FID ? '⚠️' : '✅');
            
            if ((window as any).gtag) {
              (window as any).gtag('event', 'web_vitals', {
                name: 'FID',
                value: Math.round(fid),
                event_category: 'performance'
              });
            }
          }
        }
        
        // Layout Shift
        // Layout Shift
        // Ensure entry is treated as PerformanceEntry for type safety before checking specific properties
        const perfEntry = entry as PerformanceEntry;
        if (perfEntry.entryType === 'layout-shift' && config.monitoring.vitals.cls) {
          const layoutShiftEntry = perfEntry as any; // Cast to any for props not on base PerformanceEntry
          if (typeof layoutShiftEntry.value === 'number' && typeof layoutShiftEntry.hadRecentInput === 'boolean' && !layoutShiftEntry.hadRecentInput) {
            console.log(`CLS: ${layoutShiftEntry.value}`, layoutShiftEntry.value > PERFORMANCE_THRESHOLDS.CLS ? '⚠️' : '✅');
            
            if ((window as any).gtag) {
              (window as any).gtag('event', 'web_vitals', {
                name: 'CLS',
                value: Math.round(layoutShiftEntry.value * 1000),
                event_category: 'performance'
              });
            }
          }
        }
      }
    });

    try {
      observer.observe({ entryTypes: ['largest-contentful-paint', 'first-input', 'layout-shift'] });
    } catch (error) {
      console.warn('Performance Observer not supported:', error);
    }
  }

  // Memory usage monitoring
  if (config.monitoring.memoryUsage && 'memory' in performance) {
    const checkMemoryUsage = () => {
      const memory = (performance as any).memory;
      if (memory) {
        const heapUsed = memory.usedJSHeapSize;
        const heapTotal = memory.totalJSHeapSize;
        const heapLimit = memory.jsHeapSizeLimit;
        
        console.log(`Memory Usage: ${Math.round(heapUsed / 1024 / 1024)}MB / ${Math.round(heapLimit / 1024 / 1024)}MB`);
        
        if (heapUsed > PERFORMANCE_THRESHOLDS.HEAP_SIZE) {
          console.warn('High memory usage detected', {
            used: heapUsed,
            total: heapTotal,
            limit: heapLimit
          });
        }
      }
    };
    
    // Check memory usage every 30 seconds
    setInterval(checkMemoryUsage, 30000);
  }

  // Route change performance tracking
  const router = useRouter();
  let routeStartTime = Date.now();
  
  router.beforeEach(() => {
    routeStartTime = Date.now();
  });
  
  router.afterEach((to: RouteLocationNormalized) => {
    const routeLoadTime = Date.now() - routeStartTime;
    console.log(`Route load time for ${to.path}: ${routeLoadTime}ms`);
    
    if ((window as any).gtag) {
      (window as any).gtag('event', 'page_view', {
        page_title: to.name as string,
        page_location: to.path,
        custom_map: {
          load_time: routeLoadTime
        }
      });
    }
  });

  // Bundle size monitoring (development only)
  if (process.env.NODE_ENV === 'development' && config.monitoring.bundleAnalysis) {
    const logBundleInfo = () => {
      const scripts = document.querySelectorAll('script[src]');
      // totalSize was unused
      
      scripts.forEach(script => {
        const src = script.getAttribute('src');
        if (src && src.startsWith('/')) {
          // This is a rough estimation - in production you'd want more accurate measurements
          console.log(`Bundle: ${src}`);
        }
      });
    };
    
    // Log bundle info after initial load
    setTimeout(logBundleInfo, 1000);
  }

  // Provide performance utilities globally
  return {
    provide: {
      performance: {
        mark: (name: string) => {
          if ('mark' in performance) {
            performance.mark(name);
          }
        },
        measure: (name: string, startMark: string, endMark?: string) => {
          if ('measure' in performance) {
            try {
              performance.measure(name, startMark, endMark);
              const measure = performance.getEntriesByName(name, 'measure')[0];
              console.log(`${name}: ${measure.duration}ms`);
              return measure.duration;
            } catch (error) {
              console.warn(`Failed to measure ${name}:`, error);
            }
          }
          return 0;
        },
        clearMarks: () => {
          if ('clearMarks' in performance) {
            performance.clearMarks();
          }
        },
        clearMeasures: () => {
          if ('clearMeasures' in performance) {
            performance.clearMeasures();
          }
        }
      }
    }
  };
});
