# Chart Components Library

A comprehensive collection of robust, interactive chart components built with NuxtCharts for the Legal SaaS Frontend application.

## 🚀 Overview

This library provides seven powerful chart components designed for data visualization in modern web applications, built with NuxtCharts for optimal performance:

- **UiBarChart** - Modern bar charts with NuxtCharts integration
- **UiAreaChart** - Smooth area charts with curve interpolation
- **UiLineChart** - Interactive line charts with grid lines
- **UiDonutChart** - Donut/pie charts with center content
- **UiProgressCircle** - Circular progress indicators
- **UiProgressLinear** - Linear progress bars with advanced features
- **UiTracker** - Status tracking component with visual indicators

## 📦 Installation

The components are built on top of NuxtCharts, which is already installed in the project:

```bash
npm install nuxt-charts
```

## 🎯 Features

### Common Features (All Charts)
- **Responsive Design** - Adapts to container size
- **Dark Mode Support** - Automatic theme detection
- **Loading States** - Built-in loading indicators
- **Error Handling** - Graceful error display
- **TypeScript Support** - Full type safety
- **Accessibility** - ARIA labels and keyboard navigation
- **Animation Support** - Smooth transitions and effects
- **Custom Styling** - Tailwind CSS classes and custom themes

### Interactive Features
- **Click Events** - Handle chart element clicks
- **Hover Effects** - Interactive tooltips and highlights
- **Legend Control** - Show/hide data series
- **Data Export** - Export chart data and images

## 📊 Components

### UiBarChart

Modern bar chart component built with NuxtCharts package.

```vue
<template>
  <UiBarChart
    :data="[
      { month: 'January', revenue: 186 },
      { month: 'February', revenue: 305 },
      { month: 'March', revenue: 237 }
    ]"
    :height="300"
    :categories="{
      revenue: { name: 'Revenue', color: '#22c55e' }
    }"
    :y-axis="['revenue']"
    :x-num-ticks="6"
    :radius="4"
    :y-grid-line="true"
    :x-formatter="xFormatter"
    :y-formatter="yFormatter"
  />
</template>
```

**Props:**
- `data: any[]` - Chart data array with objects containing values
- `height?: number` - Chart height in pixels (default: 300)
- `categories: Record<string, BulletLegendItemInterface>` - Data series configuration
- `yAxis?: string[]` - Y-axis data keys to display
- `xNumTicks?: number` - Number of X-axis ticks (default: 6)
- `radius?: number` - Bar border radius (default: 4)
- `yGridLine?: boolean` - Show Y-axis grid lines (default: true)
- `xFormatter?: (i: number) => string` - X-axis label formatter
- `yFormatter?: (i: number) => number` - Y-axis value formatter

### UiAreaChart

Smooth area chart component built with NuxtCharts package.

```vue
<template>
  <UiAreaChart
    :data="[
      { date: '2024-04-01', desktop: 222, mobile: 150 },
      { date: '2024-04-02', desktop: 180, mobile: 97 },
      { date: '2024-04-03', desktop: 167, mobile: 120 }
    ]"
    :height="300"
    :categories="{
      desktop: { name: 'Desktop', color: '#3b82f6' },
      mobile: { name: 'Mobile', color: '#22c55e' }
    }"
    :y-grid-line="true"
    :x-formatter="xFormatter"
    :curve-type="CurveType.MonotoneX"
  />
</template>
```

**Props:**
- `data: any[]` - Chart data array with objects containing values
- `height?: number` - Chart height in pixels (default: 300)
- `categories: Record<string, BulletLegendItemInterface>` - Data series configuration
- `yGridLine?: boolean` - Show Y-axis grid lines (default: true)
- `xFormatter?: (i: number) => string | number` - X-axis label formatter
- `curveType?: CurveType` - Curve interpolation type (default: MonotoneX)
- `legendPosition?: LegendPosition` - Legend position (default: Top)
- `hideLegend?: boolean` - Hide legend (default: false)

### UiLineChart

Smooth line chart component built with NuxtCharts package.

```vue
<template>
  <UiLineChart
    :data="[
      { date: 'Jan 23', subscriptions: 2890, downloads: 2338 },
      { date: 'Feb 23', subscriptions: 2756, downloads: 2103 },
      { date: 'Mar 23', subscriptions: 3322, downloads: 2194 }
    ]"
    :height="300"
    y-label="Sales"
    :x-num-ticks="4"
    :y-num-ticks="4"
    :categories="{
      subscriptions: { name: 'Subscriptions', color: '#3b82f6' },
      downloads: { name: 'Downloads', color: '#22c55e' }
    }"
    :x-formatter="xFormatter"
    :y-grid-line="true"
    :curve-type="CurveType.Linear"
  />
</template>
```

**Props:**
- `data: any[]` - Chart data array with objects containing values
- `height?: number` - Chart height in pixels (default: 300)
- `yLabel?: string` - Y-axis label
- `xNumTicks?: number` - Number of X-axis ticks (default: 4)
- `yNumTicks?: number` - Number of Y-axis ticks (default: 4)
- `categories: Record<string, BulletLegendItemInterface>` - Data series configuration
- `xFormatter?: (i: number) => string | number` - X-axis label formatter
- `yGridLine?: boolean` - Show Y-axis grid lines (default: true)
- `curveType?: CurveType` - Curve interpolation type (default: Linear)
- `legendPosition?: LegendPosition` - Legend position (default: Top)
- `hideLegend?: boolean` - Hide legend (default: false)

### UiDonutChart

Modern donut charts built with NuxtCharts package.

```vue
<template>
  <UiDonutChart
    :data="[
      { color: '#3b82f6', name: 'Active', value: 45 },
      { color: '#f59e0b', name: 'Pending', value: 23 },
      { color: '#10b981', name: 'Closed', value: 32 }
    ]"
    :height="300"
    :hide-legend="false"
    :show-legend="true"
    :radius="0"
    :center-value="100"
    center-label="Total Cases"
  >
    <template #center>
      <div class="text-center">
        <div class="font-semibold">{{ total }}</div>
        <div class="text-(--ui-text-muted)">Total Cases</div>
      </div>
    </template>
  </UiDonutChart>
</template>
```

**Props:**
- `data: DonutDataItem[]` - Chart data with color, name, and value
- `height?: number` - Chart height in pixels (default: 275)
- `hideLegend?: boolean` - Hide NuxtCharts legend (default: true)
- `radius?: number` - Inner radius (default: 0)
- `showLegend?: boolean` - Show custom legend (default: false)
- `centerValue?: number | string` - Center display value
- `centerLabel?: string` - Center label text
- `centerValueFormatter?: (value: number | string) => string` - Value formatting
- `valueFormatter?: (value: number) => string` - Legend value formatting

**Features:**
- Built on NuxtCharts for optimal performance
- Custom center content via slots
- Interactive legend with click handlers
- Automatic percentage calculations
- Responsive design
- Dark mode support

### UiProgressCircle

Simplified circular progress indicators with automatic color coding.

```vue
<template>
  <UiProgressCircle
    :value="75"
    :size="120"
    :show-label="true"
  />
</template>
```

**Props:**
- `value: number` - Progress value (0-100)
- `size?: number` - Circle size in pixels (default: 50)
- `strokeWidth?: number` - Stroke width (default: 5)
- `showLabel?: boolean` - Show percentage label (default: true)

**Features:**
- Automatic color coding (red < 50, orange 50-75, green ≥ 75)
- Smooth animations
- Responsive sizing
- Dark mode support

### UiProgressLinear

Advanced linear progress bars with multiple features.

```vue
<template>
  <UiProgressLinear
    :value="65"
    label="Task Progress"
    :height="8"
    :buffer-value="80"
    :animated="true"
  />
</template>
```

**Props:**
- `value: number` - Progress value (0-100)
- `bufferValue?: number` - Buffer/secondary progress value
- `height?: number` - Progress bar height in pixels (default: 8)
- `label?: string` - Progress label
- `minLabel?: string` - Minimum value label
- `maxLabel?: string` - Maximum value label
- `showLabel?: boolean` - Show label (default: true)
- `showValue?: boolean` - Show value (default: true)
- `showPercentage?: boolean` - Show percentage symbol (default: true)
- `showDetails?: boolean` - Show min/max labels (default: false)
- `animated?: boolean` - Enable animations (default: false)
- `indeterminate?: boolean` - Indeterminate loading state (default: false)
- `gradient?: GradientConfig` - Custom gradient colors

**Features:**
- Buffer progress indication
- Indeterminate loading states
- Custom gradients
- Animated stripes
- Automatic color coding
- Responsive design

### UiTracker

Status tracking component with visual status bars and tooltips.

```vue
<template>
  <UiTracker
    domain="Legal Case Management"
    uptime="99.9% uptime"
    :operational-status="true"
    :status-data="[
      { status: 'online' },
      { status: 'warning' },
      { status: 'offline' },
      { status: 'online' }
    ]"
    :bar-width="4"
    :bar-gap="1"
    start-label="90 days ago"
    end-label="Today"
  />
</template>
```

**Props:**
- `domain: string` - Service or system name
- `uptime: string` - Uptime percentage or description
- `operationalStatus: boolean` - Current operational status
- `statusData: { status: string }[]` - Array of status data points
- `barWidth: number` - Width of each status bar
- `barGap: number` - Gap between status bars
- `startLabel?: string` - Label for the start of the timeline
- `endLabel?: string` - Label for the end of the timeline

**Features:**
- Responsive bar display based on container width
- Real-time resizing with ResizeObserver
- Interactive hover tooltips
- Multiple status types (online, offline, warning, error, success, info)
- Automatic empty state handling

## 🎨 Styling

All components support Tailwind CSS classes and custom styling:

```vue
<UiBarChart
  container-class="custom-chart-container"
  :class="['border', 'rounded-lg', 'shadow-lg']"
/>
```

## 🔧 Events

Common events across chart components:

- `@chart-ready` - Chart initialization complete
- `@chart-click` - Chart element clicked
- `@chart-hover` - Chart element hovered
- `@complete` - Progress completion (UiProgressCircle)
- `@item-complete` - Item completed (UiTracker)

## 📱 Responsive Design

All charts are responsive by default and adapt to their container:

```vue
<div class="w-full h-64 md:h-96">
  <UiBarChart :responsive="true" :maintain-aspect-ratio="false" />
</div>
```

## 🌙 Dark Mode

Charts automatically adapt to dark mode using CSS variables:

```css
:root {
  --chart-text-color: #374151;
  --chart-grid-color: #e5e7eb;
}

.dark {
  --chart-text-color: #d1d5db;
  --chart-grid-color: #374151;
}
```

## 🚀 Performance

- **Lazy Loading** - Components load only when needed
- **Efficient Updates** - Reactive data binding with minimal re-renders
- **Memory Management** - Proper cleanup on component unmount
- **Optimized Animations** - Hardware-accelerated transitions

## 📖 Examples

Visit `/dashboard/charts-demo` to see all components in action with interactive examples and code snippets.

## 🤝 Contributing

When adding new chart types or features:

1. Follow the existing component structure
2. Include TypeScript interfaces
3. Add comprehensive props documentation
4. Implement error handling and loading states
5. Ensure accessibility compliance
6. Add examples to the demo page

## 📄 License

Part of the Legal SaaS Frontend project. See main project license for details.
