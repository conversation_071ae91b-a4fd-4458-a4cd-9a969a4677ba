/**
 * Advanced TypeScript Utilities
 * 
 * Collection of advanced TypeScript utility types and helper functions
 * for enhanced type safety and developer experience
 */

// ============================================================================
// ADVANCED UTILITY TYPES
// ============================================================================

/**
 * Make all properties of T optional recursively
 */
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P]
}

/**
 * Make all properties of T required recursively
 */
export type DeepRequired<T> = {
  [P in keyof T]-?: T[P] extends object ? DeepRequired<T[P]> : T[P]
}

/**
 * Make all properties of T readonly recursively
 */
export type DeepReadonly<T> = {
  readonly [P in keyof T]: T[P] extends object ? DeepReadonly<T[P]> : T[P]
}

/**
 * Make all properties of T mutable recursively
 */
export type DeepMutable<T> = {
  -readonly [P in keyof T]: T[P] extends object ? DeepMutable<T[P]> : T[P]
}

/**
 * Make specific properties optional
 */
export type PartialBy<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>

/**
 * Make specific properties required
 */
export type RequiredBy<T, K extends keyof T> = Omit<T, K> & Required<Pick<T, K>>

/**
 * Make specific properties readonly
 */
export type ReadonlyBy<T, K extends keyof T> = Omit<T, K> & Readonly<Pick<T, K>>

/**
 * Make specific properties mutable
 */
export type MutableBy<T, K extends keyof T> = Omit<T, K> & {
  -readonly [P in K]: T[P]
}

/**
 * Extract keys of a specific type
 */
export type KeysOfType<T, U> = {
  [K in keyof T]: T[K] extends U ? K : never
}[keyof T]

/**
 * Extract values of a specific type
 */
export type ValuesOfType<T, U> = T[KeysOfType<T, U>]

/**
 * Get the type of a nested property
 */
export type NestedKeyOf<T> = {
  [K in keyof T & string]: T[K] extends object
    ? `${K}` | `${K}.${NestedKeyOf<T[K]>}`
    : `${K}`
}[keyof T & string]

/**
 * Get the value type of a nested property
 */
export type NestedValueOf<T, K extends NestedKeyOf<T>> = K extends `${infer Key}.${infer Rest}`
  ? Key extends keyof T
    ? T[Key] extends infer ActualTKey
      ? Rest extends (infer ActualRest extends NestedKeyOf<ActualTKey>)
        ? NestedValueOf<ActualTKey, ActualRest>
        : never
      : never // This branch should ideally be unreachable due to the nature of 'extends infer'
    : never
  : K extends keyof T
  ? T[K]
  : never

/**
 * Create a union of all possible paths in an object
 */
export type Paths<T> = T extends object
  ? {
      [K in keyof T]: K extends string
        ? T[K] extends object
          ? K | `${K}.${Paths<T[K]>}`
          : K
        : never
    }[keyof T]
  : never

/**
 * Get the value at a specific path
 */
export type PathValue<T, P extends Paths<T>> = P extends `${infer Key}.${infer Rest}`
  ? Key extends keyof T
    ? Rest extends Paths<T[Key]>
      ? PathValue<T[Key], Rest>
      : never
    : never
  : P extends keyof T
  ? T[P]
  : never

/**
 * Create a type with only the specified keys
 */
export type PickByValue<T, V> = Pick<T, KeysOfType<T, V>>

/**
 * Create a type without the specified value types
 */
export type OmitByValue<T, V> = Omit<T, KeysOfType<T, V>>

/**
 * Create a union type from an array
 */
export type ArrayToUnion<T extends readonly unknown[]> = T[number]

/**
 * Create a union type from object values
 */
export type ValueOf<T> = T[keyof T]

/**
 * Create a union type from object keys
 */
export type KeyOf<T> = keyof T

/**
 * Check if a type is never
 */
export type IsNever<T> = [T] extends [never] ? true : false

/**
 * Check if a type is any
 */
export type IsAny<T> = 0 extends 1 & T ? true : false

/**
 * Check if a type is unknown
 */
export type IsUnknown<T> = IsAny<T> extends true ? false : unknown extends T ? true : false

/**
 * Check if two types are equal
 */
export type IsEqual<T, U> = (<G>() => G extends T ? 1 : 2) extends <G>() => G extends U ? 1 : 2
  ? true
  : false

/**
 * Get the length of a tuple
 */
export type Length<T extends readonly unknown[]> = T['length']

/**
 * Get the first element of a tuple
 */
export type Head<T extends readonly unknown[]> = T extends readonly [infer H, ...unknown[]] ? H : never

/**
 * Get all elements except the first
 */
export type Tail<T extends readonly unknown[]> = T extends readonly [unknown, ...infer Rest] ? Rest : never

/**
 * Get the last element of a tuple
 */
export type Last<T extends readonly unknown[]> = T extends readonly [...unknown[], infer L] ? L : never

/**
 * Reverse a tuple
 */
export type Reverse<T extends readonly unknown[]> = T extends readonly [...infer Rest, infer Last]
  ? [Last, ...Reverse<Rest>]
  : []

/**
 * Flatten a nested array type
 */
export type Flatten<T> = T extends readonly (infer U)[] ? Flatten<U> : T

/**
 * Create a branded type for nominal typing
 */
export type Brand<T, B> = T & { readonly __brand: B }

/**
 * Extract the brand from a branded type
 */
export type BrandOf<T> = T extends Brand<unknown, infer B> ? B : never

/**
 * Remove the brand from a branded type
 */
export type Unbrand<T> = T extends Brand<infer U, unknown> ? U : T

/**
 * Create an opaque type (stronger than branded types)
 */
export type Opaque<T, K> = T & { readonly __opaque: K }

/**
 * Function type utilities
 */
export type Parameters<T extends (...args: any) => any> = T extends (...args: infer P) => any ? P : never
export type ReturnType<T extends (...args: any) => any> = T extends (...args: any) => infer R ? R : any
export type AsyncReturnType<T extends (...args: any) => Promise<any>> = T extends (
  ...args: any
) => Promise<infer R>
  ? R
  : any

/**
 * Promise utilities
 */
export type Awaited<T> = T extends Promise<infer U> ? Awaited<U> : T
export type PromiseValue<T> = T extends Promise<infer U> ? U : T

/**
 * Class utilities
 */
export type ConstructorParameters<T extends abstract new (...args: any) => any> = T extends abstract new (
  ...args: infer P
) => any
  ? P
  : never
export type InstanceType<T extends abstract new (...args: any) => any> = T extends abstract new (
  ...args: any
) => infer R
  ? R
  : any

/**
 * String manipulation utilities
 */
export type Uppercase<S extends string> = globalThis.Uppercase<S>;
export type Lowercase<S extends string> = globalThis.Lowercase<S>;
export type Capitalize<S extends string> = globalThis.Capitalize<S>;
export type Uncapitalize<S extends string> = globalThis.Uncapitalize<S>;

/**
 * Template literal utilities
 */
export type Split<S extends string, D extends string> = S extends `${infer T}${D}${infer U}`
  ? [T, ...Split<U, D>]
  : [S]

export type Join<T extends readonly string[], D extends string> = T extends readonly [
  infer F,
  ...infer R
]
  ? F extends string
    ? R extends readonly string[]
      ? R['length'] extends 0
        ? F
        : `${F}${D}${Join<R, D>}`
      : never
    : never
  : ''

export type Replace<
  S extends string,
  From extends string,
  To extends string
> = S extends `${infer Prefix}${From}${infer Suffix}` ? `${Prefix}${To}${Suffix}` : S

export type ReplaceAll<
  S extends string,
  From extends string,
  To extends string
> = S extends `${infer Prefix}${From}${infer Suffix}`
  ? `${Prefix}${To}${ReplaceAll<Suffix, From, To>}`
  : S

export type StartsWith<S extends string, T extends string> = S extends `${T}${string}` ? true : false
export type EndsWith<S extends string, T extends string> = S extends `${string}${T}` ? true : false
export type Includes<S extends string, T extends string> = S extends `${string}${T}${string}` ? true : false

/**
 * Number utilities
 */
export type IsPositive<N extends number> = `${N}` extends `-${string}` ? false : true
export type IsNegative<N extends number> = `${N}` extends `-${string}` ? true : false
export type IsZero<N extends number> = N extends 0 ? true : false

/**
 * Object manipulation utilities
 */
export type Merge<T, U> = Omit<T, keyof U> & U

export type DeepMerge<T, U> = {
  [K in keyof T | keyof U]: K extends keyof U
    ? K extends keyof T
      ? T[K] extends object
        ? U[K] extends object
          ? DeepMerge<T[K], U[K]>
          : U[K]
        : U[K]
      : U[K]
    : K extends keyof T
    ? T[K]
    : never
}

export type Override<T, U> = Omit<T, keyof U> & U

export type Assign<T, U> = {
  [K in keyof (T & U)]: K extends keyof U ? U[K] : K extends keyof T ? T[K] : never
}

/**
 * Conditional type utilities
 */
export type If<C extends boolean, T, F> = C extends true ? T : F
export type Not<C extends boolean> = C extends true ? false : true
export type And<A extends boolean, B extends boolean> = A extends true ? (B extends true ? true : false) : false
export type Or<A extends boolean, B extends boolean> = A extends true ? true : B extends true ? true : false

/**
 * Tuple manipulation utilities
 */
export type Prepend<T extends readonly unknown[], U> = [U, ...T]
export type Append<T extends readonly unknown[], U> = [...T, U]
export type Concat<T extends readonly unknown[], U extends readonly unknown[]> = [...T, ...U]

export type Take<T extends readonly unknown[], N extends number> = T extends readonly [
  infer H,
  ...infer Rest
]
  ? N extends 0
    ? []
    : [H, ...Take<Rest, Subtract<N, 1>>]
  : []

export type Drop<T extends readonly unknown[], N extends number> = T extends readonly [
  unknown,
  ...infer Rest
]
  ? N extends 0
    ? T
    : Drop<Rest, Subtract<N, 1>>
  : []

/**
 * Arithmetic utilities (limited to small numbers)
 */
type Subtract<A extends number, B extends number> = [A, B] extends [1, 1]
  ? 0
  : [A, B] extends [2, 1]
  ? 1
  : [A, B] extends [3, 1]
  ? 2
  : [A, B] extends [4, 1]
  ? 3
  : [A, B] extends [5, 1]
  ? 4
  : number

// ============================================================================
// TYPE GUARDS AND RUNTIME UTILITIES
// ============================================================================

/**
 * Type guard for checking if a value is defined
 */
export function isDefined<T>(value: T | undefined | null): value is T {
  return value !== undefined && value !== null
}

/**
 * Type guard for checking if a value is a string
 */
export function isString(value: unknown): value is string {
  return typeof value === 'string'
}

/**
 * Type guard for checking if a value is a number
 */
export function isNumber(value: unknown): value is number {
  return typeof value === 'number' && !isNaN(value)
}

/**
 * Type guard for checking if a value is a boolean
 */
export function isBoolean(value: unknown): value is boolean {
  return typeof value === 'boolean'
}

/**
 * Type guard for checking if a value is an object
 */
export function isObject(value: unknown): value is Record<string, unknown> {
  return typeof value === 'object' && value !== null && !Array.isArray(value)
}

/**
 * Type guard for checking if a value is an array
 */
export function isArray<T>(value: unknown): value is T[] {
  return Array.isArray(value)
}

/**
 * Type guard for checking if a value is a function
 */
export function isFunction(value: unknown): value is Function {
  return typeof value === 'function'
}

/**
 * Type guard for checking if a value is a promise
 */
export function isPromise<T>(value: unknown): value is Promise<T> {
  return value instanceof Promise || (isObject(value) && isFunction((value as any).then))
}

/**
 * Type guard for checking if a value is a date
 */
export function isDate(value: unknown): value is Date {
  return value instanceof Date && !isNaN(value.getTime())
}

/**
 * Type guard for checking if a value is a regular expression
 */
export function isRegExp(value: unknown): value is RegExp {
  return value instanceof RegExp
}

/**
 * Type guard for checking if a value is an error
 */
export function isError(value: unknown): value is Error {
  return value instanceof Error
}

/**
 * Type guard for checking if a value has a specific property
 */
export function hasProperty<T extends string>(
  obj: unknown,
  prop: T
): obj is Record<T, unknown> {
  return isObject(obj) && prop in obj
}

/**
 * Type guard for checking if a value is of a specific type
 */
export function isOfType<T>(value: unknown, typeGuard: (value: unknown) => value is T): value is T {
  return typeGuard(value)
}

/**
 * Assert that a value is defined
 */
export function assertDefined<T>(value: T | undefined | null, message?: string): asserts value is T {
  if (!isDefined(value)) {
    throw new Error(message || 'Value is not defined')
  }
}

/**
 * Assert that a value is of a specific type
 */
export function assertType<T>(
  value: unknown,
  typeGuard: (value: unknown) => value is T,
  message?: string
): asserts value is T {
  if (!typeGuard(value)) {
    throw new Error(message || 'Value is not of expected type')
  }
}

/**
 * Exhaustive check for union types
 */
export function assertNever(value: never): never {
  throw new Error(`Unexpected value: ${value}`)
}

/**
 * Safe property access with type narrowing
 */
export function safeGet<T, K extends keyof T>(obj: T, key: K): T[K] | undefined {
  return obj && Reflect.has(obj, key) ? obj[key] : undefined;
}

/**
 * Deep clone utility with type preservation
 */
export function deepClone<T>(obj: T): T {
  if (obj === null || typeof obj !== 'object') {
    return obj
  }

  if (obj instanceof Date) {
    return new Date(obj.getTime()) as T
  }

  if (obj instanceof Array) {
    return obj.map(item => deepClone(item)) as T
  }

  if (typeof obj === 'object') {
    const cloned = {} as T
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        cloned[key] = deepClone(obj[key])
      }
    }
    return cloned
  }

  return obj
}
