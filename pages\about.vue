<template>
  <div class="about-page">
    <!-- Hero Section -->
    <HeroSection
      title="Empowering Legal Professionals Worldwide"
      subtitle="Our mission is to revolutionize the legal industry through secure, intelligent, and user-friendly technology."
      :badge="{
        text: '🌍 Trusted by 2,500+ Law Firms',
        variant: 'secondary'
      }"
      :actions="[
        {
          text: 'Join Our Mission',
          variant: 'primary',
          to: '/auth/signup',
          icon: 'material-symbols:handshake'
        },
        {
          text: 'Our Story',
          variant: 'outline',
          to: '#our-story',
          icon: 'material-symbols:auto-stories'
        }
      ]"
      variant="gradient"
      size="lg"
    />

    <!-- Our Story Section -->
    <section id="our-story" class="py-16 bg-white">
      <div class="container mx-auto px-6 max-w-4xl text-center">
        <h2 class="text-3xl md:text-4xl font-bold text-gray-800 mb-8">Our Story & Mission</h2>
        <p class="text-lg text-gray-600 leading-relaxed mb-6">
          LegalFlow was founded by a team of legal tech enthusiasts and experienced software engineers with a shared vision: to bridge the gap between traditional legal practices and modern technological advancements. We recognized the unique challenges faced by law firms – from managing sensitive client data to ensuring regulatory compliance across jurisdictions, including adherence to privacy laws like Israeli privacy law and GDPR.
        </p>
        <p class="text-lg text-gray-600 leading-relaxed">
          Our platform, built on a robust NestJS backend and a Nuxt.js frontend, is engineered for security, scalability, and efficiency. We are committed to providing legal professionals with the tools they need to streamline their operations, enhance collaboration, and focus on delivering exceptional legal services without compromising on data integrity or privacy. Our goal is to make legal work more efficient, secure, and accessible.
        </p>
      </div>
    </section>

    <!-- Company Stats -->
    <StatsSection
      :stats="[
        {
          label: 'Years of Experience',
          value: 8,
          suffix: '+',
          icon: 'material-symbols:history'
        },
        {
          label: 'Team Members',
          value: 45,
          suffix: '+',
          icon: 'material-symbols:group'
        },
        {
          label: 'Countries Served',
          value: 25,
          suffix: '+',
          icon: 'material-symbols:public'
        },
        {
          label: 'Customer Satisfaction',
          value: 98,
          suffix: '%',
          icon: 'material-symbols:sentiment-satisfied'
        }
      ]"
      variant="minimal"
      :columns="4"
      background="gray"
    />

    <!-- Our Values -->
    <FeatureGrid
      title="Why LegalFlow?"
      subtitle="The core values and principles that drive everything we do."
      :features="[
        {
          title: 'Uncompromising Security',
          description: 'Your data\'s integrity and confidentiality are our top priority. We implement robust security measures and adhere to strict privacy regulations.',
          icon: 'material-symbols:security',
          items: [
            'Bank-level encryption',
            'Multi-tenancy for data isolation',
            'GDPR and privacy law compliance',
            'Regular security audits'
          ]
        },
        {
          title: 'Intuitive Experience',
          description: 'Designed with legal professionals in mind, our platform offers a seamless and intuitive user interface, minimizing learning curves.',
          icon: 'material-symbols:psychology',
          items: [
            'User-centered design',
            'Minimal learning curve',
            'Responsive interface',
            'Accessibility focused'
          ]
        },
        {
          title: 'Continuous Innovation',
          description: 'We leverage cutting-edge technologies to bring you powerful automation and insights, constantly evolving to meet modern demands.',
          icon: 'material-symbols:rocket-launch',
          items: [
            'AI-powered features',
            'Regular updates',
            'Modern tech stack',
            'Future-ready architecture'
          ]
        }
      ]"
      variant="cards"
      :columns="3"
      background="gray"
    />

    <!-- Team Section -->
    <section class="py-16 bg-white">
      <div class="container mx-auto px-6">
        <h2 class="text-3xl md:text-4xl font-bold text-gray-800 text-center mb-12">Meet Our Leadership</h2>
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8 max-w-5xl mx-auto">
          <div class="flex flex-col items-center text-center bg-gray-50 p-8 rounded-xl shadow-sm hover:shadow-lg transition-shadow duration-300">
            <div class="w-32 h-32 rounded-full bg-gradient-to-br from-brandPrimary-500 to-purple-600 flex items-center justify-center text-white font-bold text-3xl mb-6">
              JD
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-2">John Doe</h3>
            <p class="text-brandPrimary-600 mb-3 font-medium">CEO & Co-founder</p>
            <p class="text-gray-600 text-sm leading-relaxed">Visionary leader with 15+ years in legal tech and business strategy. Former partner at a top-tier law firm.</p>
          </div>
          <div class="flex flex-col items-center text-center bg-gray-50 p-8 rounded-xl shadow-sm hover:shadow-lg transition-shadow duration-300">
            <div class="w-32 h-32 rounded-full bg-gradient-to-br from-purple-500 to-pink-600 flex items-center justify-center text-white font-bold text-3xl mb-6">
              JS
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-2">Jane Smith</h3>
            <p class="text-brandPrimary-600 mb-3 font-medium">CTO & Co-founder</p>
            <p class="text-gray-600 text-sm leading-relaxed">Lead architect behind our secure, scalable, and compliant tech stack. Former senior engineer at major tech companies.</p>
          </div>
          <div class="flex flex-col items-center text-center bg-gray-50 p-8 rounded-xl shadow-sm hover:shadow-lg transition-shadow duration-300">
            <div class="w-32 h-32 rounded-full bg-gradient-to-br from-blue-500 to-brandPrimary-600 flex items-center justify-center text-white font-bold text-3xl mb-6">
              EW
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-2">Emily White</h3>
            <p class="text-brandPrimary-600 mb-3 font-medium">Head of Product</p>
            <p class="text-gray-600 text-sm leading-relaxed">Focuses on user experience and ensuring our solutions meet real-world legal needs. Expert in legal workflow optimization.</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Final CTA Section -->
    <CTASection
      title="Partner with LegalFlow Today"
      subtitle="Discover how our platform can empower your firm to achieve unprecedented efficiency and success."
      :actions="[
        {
          text: 'Schedule a Consultation',
          variant: 'primary',
          to: '/contact',
          icon: 'material-symbols:calendar-month',
          size: 'xl'
        },
        {
          text: 'Start Free Trial',
          variant: 'outline',
          to: '/auth/signup',
          icon: 'material-symbols:rocket-launch'
        }
      ]"
      :features="[
        'Personalized consultation',
        'Custom implementation plan',
        'Dedicated support team',
        'Success guarantee'
      ]"
      variant="primary"
      size="lg"
    />
  </div>
</template>

<script setup lang="ts">
// Import components
import HeroSection from '~/components/public/HeroSection.vue'
import StatsSection from '~/components/public/StatsSection.vue'
import FeatureGrid from '~/components/public/FeatureGrid.vue'
import CTASection from '~/components/public/CTASection.vue'

definePageMeta({
  layout: 'default', // This page uses the default layout (header, footer)
  middleware: ['guest']
})

useHead({
  title: 'About LegalFlow - Our Mission & Team',
  meta: [
    { name: 'description', content: 'Learn about LegalFlow\'s mission to revolutionize legal practice through secure, intelligent SaaS, and meet our expert team dedicated to innovation and compliance.' },
    { property: 'og:title', content: 'About LegalFlow - Empowering Legal Professionals' },
    { property: 'og:description', content: 'Discover the story behind LegalFlow, our commitment to security and user experience, and the team driving legal tech innovation.' },
    { property: 'og:image', content: 'https://www.yourdomain.com/images/legalflow-about-og-image.jpg' }, // Placeholder
    { property: 'og:url', content: 'https://www.yourdomain.com/about' },
    { name: 'twitter:card', content: 'summary_large_image' },
  ],
  link: [
    { rel: 'canonical', href: 'https://www.yourdomain.com/about' }
  ]
})
</script>

<style scoped>
/* Styles are now handled by the individual components */
</style>