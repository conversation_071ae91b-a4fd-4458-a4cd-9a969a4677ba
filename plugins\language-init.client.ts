import { initializeLanguageAttributes } from '~/utils/languageManager'

export default defineNuxtPlugin(() => {
  // Initialize language attributes on client side
  if (process.client) {
    // Wait for DOM to be ready
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', initializeLanguageAttributes)
    } else {
      initializeLanguageAttributes()
    }
  }
})
