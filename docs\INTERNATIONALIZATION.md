# Internationalization (i18n) Guide

This document provides a comprehensive guide to the internationalization system implemented in the Legal SaaS Frontend application.

## Overview

The application supports three languages:
- **English (en)** - Default language
- **Hebrew (he)** - RTL language
- **Arabic (ar)** - RTL language

The translation system is built using Nuxt i18n with a modular file structure for better maintainability and scalability.

## Architecture

### File Structure

```
locales/
├── en/                     # English translations
│   ├── index.ts           # Main export file
│   ├── common.json        # Common UI elements (380 keys)
│   ├── time.json          # Time-related translations (15 keys)
│   ├── tenantList.json    # Tenant list page (16 keys)
│   ├── tenantDetail.json  # Tenant detail page (58 keys)
│   ├── userManagement.json # User management (19 keys)
│   ├── navigation.json    # Navigation components (30 keys)
│   ├── caseManagement.json # Case management (53 keys)
│   ├── authPages.json     # Authentication pages (38 keys)
│   ├── ui.json           # UI components (64 keys)
│   ├── dashboard.json    # Dashboard pages (10 keys)
│   ├── documents.json    # Document management (13 keys)
│   ├── templates.json    # Template management (10 keys)
│   ├── notifications.json # Notifications (9 keys)
│   └── errors.json       # Error messages (10 keys)
├── he/                    # Hebrew translations (complete)
├── ar/                    # Arabic translations (complete)
├── README.md             # Translation documentation
└── i18n.config.ts        # i18n configuration
```

### Key Features

1. **Modular Structure**: Translations are split by feature/component
2. **Complete Coverage**: All 3 languages have complete translations (735 total keys)
3. **RTL Support**: Full right-to-left language support
4. **Fallback System**: Automatic fallback to English for missing translations
5. **Type Safety**: TypeScript support for translation keys
6. **Validation**: Automated validation script to check translation completeness

## Usage

### Basic Translation

```vue
<template>
  <div>
    <!-- Simple translation -->
    <h1>{{ $t('common.welcome') }}</h1>
    
    <!-- Translation with parameters -->
    <p>{{ $t('time.minutesAgo', { minutes: 5 }) }}</p>
    
    <!-- Category-specific translation -->
    <button>{{ $t('navigation.signOut') }}</button>
  </div>
</template>
```

### Using the Translation Composable

```vue
<script setup>
const { translate, formatTimeAgo, changeLanguage, isRTL } = useTranslations()

// Translate with fallback
const title = translate('common.title', {}, 'Default Title')

// Format relative time
const timeAgo = formatTimeAgo(new Date('2024-01-01'))

// Change language
const switchToHebrew = () => changeLanguage('he')
</script>
```

### Language Switcher Component

```vue
<template>
  <UiLanguageSwitcher 
    :show-label="true"
    button-class="custom-button-class"
    dropdown-class="custom-dropdown-class"
  />
</template>
```

## Translation Categories

### 1. Common (`common.json`)
Basic UI elements used throughout the application:
- Actions: save, cancel, delete, edit, create
- Status: active, inactive, enabled, disabled
- General terms: user, account, settings

### 2. Time (`time.json`)
Time-related translations:
- Relative time: "now", "minutes ago", "hours ago"
- Time formatting helpers

### 3. Navigation (`navigation.json`)
Navigation-related translations:
- Menu items, breadcrumbs, user menu options
- Sidebar elements

### 4. UI Components (`ui.json`)
UI component translations:
- Form validation messages
- Loading states, error handling
- Confirmation dialogs, file upload messages

### 5. Feature-Specific Categories
- `tenantList.json` - Tenant overview and list functionality
- `tenantDetail.json` - Tenant detail page
- `userManagement.json` - User creation, editing, roles
- `caseManagement.json` - Legal case management
- `authPages.json` - Login, signup, password reset
- `dashboard.json` - Dashboard overview elements
- `documents.json` - Document management
- `templates.json` - Template management
- `notifications.json` - Notification system
- `errors.json` - Error pages and messages

## RTL (Right-to-Left) Support

### Automatic RTL Detection

The system automatically detects RTL languages and applies appropriate styling:

```javascript
const { isRTL, currentLanguage } = useTranslations()

// isRTL.value will be true for Hebrew and Arabic
// currentLanguage.value contains language metadata
```

### RTL CSS Classes

The application includes comprehensive RTL CSS utilities:

```css
/* Automatic direction switching */
.rtl .text-left { text-align: right; }
.rtl .text-right { text-align: left; }

/* RTL-aware positioning */
.rtl .left-0 { left: unset; right: 0; }
.rtl .right-0 { right: unset; left: 0; }

/* Modern CSS logical properties */
.start-0 { inset-inline-start: 0; }
.end-0 { inset-inline-end: 0; }
.ps-4 { padding-inline-start: 1rem; }
.pe-4 { padding-inline-end: 1rem; }
```

### Document Direction

The document direction is automatically updated when changing languages:

```javascript
// Automatically handled by the i18n plugin
document.documentElement.dir = isRTL ? 'rtl' : 'ltr'
document.documentElement.lang = languageCode
```

## Development Workflow

### Adding New Translations

1. **Add to English first**:
   ```json
   // locales/en/common.json
   {
     "newFeature": {
       "title": "New Feature",
       "description": "Description of the new feature"
     }
   }
   ```

2. **Add to Hebrew and Arabic**:
   ```json
   // locales/he/common.json
   {
     "newFeature": {
       "title": "תכונה חדשה",
       "description": "תיאור של התכונה החדשה"
     }
   }
   ```

3. **Use in components**:
   ```vue
   <template>
     <h2>{{ $t('common.newFeature.title') }}</h2>
     <p>{{ $t('common.newFeature.description') }}</p>
   </template>
   ```

### Validation

Run the validation script to check translation completeness:

```bash
npm run i18n:validate
```

This will:
- Check all JSON files for syntax errors
- Compare translation keys across languages
- Report missing or extra keys
- Provide a summary of translation coverage

### Testing Translations

1. **Use the test page**: Navigate to `/test/translations` to test all translations
2. **Language switcher**: Test language switching functionality
3. **RTL layout**: Verify RTL languages display correctly
4. **Fallbacks**: Test missing translation fallbacks

## Best Practices

### 1. Key Naming
- Use descriptive, hierarchical key names
- Group related translations logically
- Use camelCase for consistency

```json
{
  "userActions": {
    "create": "Create User",
    "edit": "Edit User",
    "delete": "Delete User"
  }
}
```

### 2. Parameters
- Use parameters for dynamic content
- Avoid hardcoded numbers or names

```json
{
  "welcomeMessage": "Welcome, {name}!",
  "itemCount": "You have {count} items"
}
```

### 3. Pluralization
- Handle plural forms correctly for each language
- Use ICU message format when needed

```json
{
  "itemCount": "{count, plural, =0 {no items} =1 {one item} other {# items}}"
}
```

### 4. Context
- Provide context for translators
- Use descriptive keys that explain usage

```json
{
  "buttons": {
    "save": "Save", // Button text for saving forms
    "cancel": "Cancel" // Button text for canceling actions
  }
}
```

### 5. Testing
- Test with all supported languages
- Verify text doesn't overflow containers
- Check RTL layouts thoroughly
- Test with longer translations (German rule: +30% length)

## Troubleshooting

### Common Issues

1. **Missing translations**: Check validation output for missing keys
2. **RTL layout issues**: Use logical CSS properties instead of directional ones
3. **Text overflow**: Test with longer translations, adjust container sizes
4. **Font issues**: Ensure proper fonts are loaded for Hebrew/Arabic

### Debug Mode

Enable i18n debug mode in development:

```javascript
// nuxt.config.ts
export default defineNuxtConfig({
  i18n: {
    debug: true // Shows missing translation warnings
  }
})
```

## Performance Considerations

1. **Lazy loading**: Translations are loaded on demand
2. **Tree shaking**: Only used translations are included in bundles
3. **Caching**: Browser caches translation files
4. **Compression**: Translation files are compressed in production

## Future Enhancements

1. **Translation Management**: Integration with translation services
2. **Automatic Detection**: Browser language detection
3. **Regional Variants**: Support for regional language variants
4. **Dynamic Loading**: Runtime translation loading
5. **Translation Memory**: Reuse of existing translations

## Resources

- [Nuxt i18n Documentation](https://i18n.nuxtjs.org/)
- [Vue i18n Documentation](https://vue-i18n.intlify.dev/)
- [ICU Message Format](https://unicode-org.github.io/icu/userguide/format_parse/messages/)
- [RTL CSS Guidelines](https://rtlstyling.com/)
- [Arabic Typography](https://arabictypography.com/)
- [Hebrew Typography](https://hebrew-typography.com/)
