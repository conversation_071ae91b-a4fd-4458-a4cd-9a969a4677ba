<template>
  <div class="ui-line-chart">
    <LineChart
      :data="data"
      :height="height"
      :y-label="yLabel"
      :x-num-ticks="xNumTicks"
      :y-num-ticks="yNumTicks"
      :categories="categories"
      :x-formatter="xFormatter"
      :y-grid-line="yGridLine"
      :curve-type="curveType"
      :legend-position="legendPosition"
      :hide-legend="hideLegend"
    />
  </div>
</template>

<script setup lang="ts">
// Types
interface BulletLegendItemInterface {
  name: string
  color: string
}

interface Props {
  data: any[]
  height?: number
  yLabel?: string
  xNumTicks?: number
  yNumTicks?: number
  categories: Record<string, BulletLegendItemInterface>
  xFormatter?: (i: number) => string | number
  yGridLine?: boolean
  curveType?: string
  legendPosition?: string
  hideLegend?: boolean
}

// Props with defaults
const props = withDefaults(defineProps<Props>(), {
  height: 300,
  xNumTicks: 4,
  yNumTicks: 4,
  yGridLine: true,
  curveType: 'linear',
  legendPosition: 'top',
  hideLegend: false
})
</script>

<style scoped>
.ui-line-chart {
  width: 100%;
}
</style>
