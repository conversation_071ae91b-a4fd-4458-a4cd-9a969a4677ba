import { defineStore } from 'pinia';

interface UiState {
  isSidebarOpen: boolean;
  isSidebarCollapsed: boolean;
  isPageLoading: boolean;
  isLoading: boolean;
  theme: 'light' | 'dark';
  screenWidth: number;
  screenHeight: number;
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  prefersReducedMotion: boolean;
}

export const useUiStore = defineStore('ui', {
  state: (): UiState => ({
    isSidebarOpen: false, // Start closed on mobile
    isSidebarCollapsed: false,
    isPageLoading: false,
    isLoading: false,
    theme: 'light',
    screenWidth: 0,
    screenHeight: 0,
    isMobile: false,
    isTablet: false,
    isDesktop: true,
    prefersReducedMotion: false,
  }),
  actions: {
    // Sidebar actions
    toggleSidebar() {
      this.isSidebarOpen = !this.isSidebarOpen;
    },
    openSidebar() {
      this.isSidebarOpen = true;
    },
    closeSidebar() {
      this.isSidebarOpen = false;
    },
    toggleSidebarCollapse() {
      this.isSidebarCollapsed = !this.isSidebarCollapsed;
    },
    setSidebarCollapsed(collapsed: boolean) {
      this.isSidebarCollapsed = collapsed;
    },

    // Loading actions
    setLoading(loading: boolean) {
      this.isLoading = loading;
    },
    setPageLoading(loading: boolean) {
      this.isPageLoading = loading;
    },

    // Theme actions
    setTheme(theme: 'light' | 'dark') {
      this.theme = theme;
      if (typeof document !== 'undefined') {
        document.documentElement.setAttribute('data-theme', theme);
        document.documentElement.classList.toggle('dark', theme === 'dark');
      }
    },
    toggleTheme() {
      this.setTheme(this.theme === 'light' ? 'dark' : 'light');
    },

    // Responsive actions
    updateScreenSize(width: number, height: number) {
      this.screenWidth = width;
      this.screenHeight = height;

      // Update device type flags
      this.isMobile = width < 768;
      this.isTablet = width >= 768 && width < 1024;
      this.isDesktop = width >= 1024;

      // Auto-close sidebar on mobile
      if (this.isMobile && this.isSidebarOpen) {
        this.isSidebarOpen = false;
      }

      // Auto-expand sidebar on desktop if collapsed
      if (this.isDesktop && !this.isSidebarOpen && !this.isSidebarCollapsed) {
        this.isSidebarOpen = true;
      }
    },

    // Accessibility actions
    setReducedMotion(prefersReduced: boolean) {
      this.prefersReducedMotion = prefersReduced;
      if (typeof document !== 'undefined') {
        document.documentElement.classList.toggle('reduce-motion', prefersReduced);
      }
    }
  },
  getters: {
    isDarkTheme: (state): boolean => state.theme === 'dark',
    isLightTheme: (state): boolean => state.theme === 'light',

    // Responsive getters
    breakpoint: (state): string => {
      if (state.isMobile) return 'mobile';
      if (state.isTablet) return 'tablet';
      return 'desktop';
    },

    // Sidebar getters
    sidebarState: (state): 'open' | 'closed' | 'collapsed' => {
      if (state.isSidebarCollapsed) return 'collapsed';
      if (state.isSidebarOpen) return 'open';
      return 'closed';
    }
  }
});