# Updated GlobalPageHeader Filters Integration Guide

This guide shows the updated integration pattern that uses `useQuery`, `onQuerySynced`, and `fetchAllTenants` to work with server-side filtering and URL synchronization.

## Key Changes from Basic Integration

### ✅ **Server-Side Filtering**
- Filters are applied on the server via API query parameters
- No client-side filtering needed - data comes pre-filtered
- Better performance for large datasets
- URL synchronization for shareable filtered views

### ✅ **Query Management**
- Uses `useQuery` composable for URL state management
- Automatic debouncing of filter changes
- History management (push/replace)
- Query parameter synchronization

### ✅ **Proper Store Integration**
- Uses `fetchAllTenants` method from tenant store
- Handles pagination metadata properly
- Integrates with existing loading states

## Updated Integration Pattern

### 1. Import Required Dependencies

```vue
<script setup lang="ts">
import { ref, computed } from 'vue'
import { useTenantStore } from '~/stores/tenant'
import { useQuery } from '~/composables/useQuery'
import { PlatformRoles } from "~/app/features/auth/constants/roles"

// Store and utilities
const tenantStore = useTenantStore()
const { formatDate } = useDateFormatters()
</script>
```

### 2. Setup Fetch Function

```vue
<script setup lang="ts">
// Fetch function that handles query parameter conversion
const fetchTenants = async (q: any) => {
  try {
    console.log('Fetching tenants with query:', q)
    
    const parsedQuery = JSON.parse(JSON.stringify(q))
    
    // Handle sorting format conversion
    if (parsedQuery.sort && parsedQuery.sort.length > 0) {
      parsedQuery.sort = parsedQuery.sort.map((s: any) => 
        `${s.key}:${s.direction.toUpperCase()}`
      ).join(',')
    }
    
    // Map filter parameters to API format
    if (parsedQuery.planType) {
      parsedQuery.plan = parsedQuery.planType
      delete parsedQuery.planType
    }
    
    await tenantStore.fetchAllTenants(parsedQuery, 'your-page.vue')
  } catch (error) {
    console.error("Error fetching tenants:", error)
  }
}
</script>
```

### 3. Configure Query Management

```vue
<script setup lang="ts">
// Query management with URL sync
const { state: query, setQuery, setQueryDebounced } = useQuery({
  page: 1,
  limit: 10,
  search: "",
  sort: [],
  // Filter parameters
  status: "",
  plan: "",
  planType: "",
  createdDate_start: "",
  createdDate_end: "",
  userRange_min: "",
  userRange_max: "",
  sortBy: ""
}, {
  debounceDelay: 500,
  history: 'push',
  onQuerySynced: ({ state }: { state: any }) => {
    fetchTenants(state)
  },
})
</script>
```

### 4. Create Computed Filters

```vue
<script setup lang="ts">
// Computed property to get current filters from query
const currentFilters = computed(() => {
  const filters: Record<string, any> = {}
  
  if (query.status) filters.status = query.status
  if (query.plan) filters.plan = query.plan
  if (query.planType) filters.planType = query.planType
  if (query.sortBy) filters.sortBy = query.sortBy
  
  // Handle date range
  if (query.createdDate_start && query.createdDate_end) {
    filters.createdDate = {
      start: query.createdDate_start,
      end: query.createdDate_end
    }
  }
  
  // Handle user range
  if (query.userRange_min || query.userRange_max) {
    filters.userRange = {
      min: query.userRange_min ? parseInt(query.userRange_min) : undefined,
      max: query.userRange_max ? parseInt(query.userRange_max) : undefined
    }
  }
  
  return filters
})
</script>
```

### 5. Update Event Handlers

```vue
<script setup lang="ts">
// Methods that update query parameters
const handleFilterChange = (data: { key: string; value: any }) => {
  const updates: Record<string, any> = {}
  
  if (data.key === 'createdDate' && typeof data.value === 'object' && data.value) {
    updates.createdDate_start = data.value.start || ''
    updates.createdDate_end = data.value.end || ''
  } else if (data.key === 'userRange' && typeof data.value === 'object' && data.value) {
    updates.userRange_min = data.value.min?.toString() || ''
    updates.userRange_max = data.value.max?.toString() || ''
  } else {
    updates[data.key] = data.value || ''
  }
  
  setQuery(updates, { resetPage: true, history: 'push' })
}

const handleFiltersChange = (filters: Record<string, any>) => {
  const updates: Record<string, any> = {}
  
  Object.entries(filters).forEach(([key, value]) => {
    if (key === 'createdDate' && typeof value === 'object' && value) {
      updates.createdDate_start = value.start || ''
      updates.createdDate_end = value.end || ''
    } else if (key === 'userRange' && typeof value === 'object' && value) {
      updates.userRange_min = value.min?.toString() || ''
      updates.userRange_max = value.max?.toString() || ''
    } else {
      updates[key] = value || ''
    }
  })
  
  setQuery(updates, { resetPage: true, history: 'push' })
}

const handleClearFilters = () => {
  setQuery({
    status: '',
    plan: '',
    planType: '',
    createdDate_start: '',
    createdDate_end: '',
    userRange_min: '',
    userRange_max: '',
    sortBy: ''
  }, { resetPage: true, history: 'push' })
}

const handleSort = (sortConfig: { key: string; direction: "asc" | "desc" }) => {
  setQuery({ sort: [sortConfig] }, { resetPage: true, history: 'push' })
}

const handlePageChange = (page: number) => {
  setQuery({ page: page }, { history: 'push' })
}
</script>
```

### 6. Update Template

```vue
<template>
  <div class="space-y-6 py-6">
    <GlobalPageHeader
      title="Manage Tenants"
      :show-filters="true"
      :filter-configs="filterConfigs"
      :advanced-filter-configs="advancedFilterConfigs"
      :initial-filters="currentFilters"
      :stats="tenantStats"
      :loading="tenantStore.isLoading"
      @filter-change="handleFilterChange"
      @filters-change="handleFiltersChange"
      @clear-filters="handleClearFilters"
    />

    <!-- Results Table -->
    <UiTable
      :headers="tableHeaders"
      :items="tenantStore.getTenants"
      :loading="tenantStore.isLoading"
      @sort="handleSort"
      @selection-change="handleSelectionChange"
    />

    <!-- Pagination -->
    <UiPagination
      v-if="tenantStore.tenantsMeta && tenantStore.tenantsMeta.total > 0"
      :current-page="parseInt(query.page)"
      :total-pages="tenantStore.tenantsMeta.totalPages"
      :total-items="tenantStore.tenantsMeta.total"
      :items-per-page="parseInt(query.limit)"
      @page-change="handlePageChange"
    />
  </div>
</template>
```

## Key Benefits of Updated Pattern

### 🚀 **Performance**
- Server-side filtering reduces client-side processing
- Pagination works with filtered results
- Debounced API calls prevent excessive requests

### 🔗 **URL Synchronization**
- Shareable filtered views via URL
- Browser back/forward navigation works
- Bookmarkable filter states

### 📊 **Proper Data Management**
- Uses existing store patterns
- Handles loading states correctly
- Integrates with pagination metadata

### 🎯 **User Experience**
- Instant visual feedback
- Persistent filter states
- Smooth navigation between filtered views

## Migration from Client-Side Filtering

### Before (Client-Side)
```vue
const filteredTenants = computed(() => {
  let tenants = [...tenantStore.getTenants]
  // Apply filters client-side
  return tenants.filter(/* filtering logic */)
})
```

### After (Server-Side)
```vue
// No client-side filtering needed
const filteredTenants = computed(() => {
  return tenantStore.getTenants // Already filtered by server
})
```

## Complete Example

See `pages/dashboard/tenants/manage-with-filters.vue` for the complete implementation that demonstrates:

- ✅ Server-side filtering with `fetchAllTenants`
- ✅ URL synchronization with `useQuery`
- ✅ Proper pagination integration
- ✅ Custom filter components
- ✅ Loading states and error handling
- ✅ Statistics display with server data

This pattern can be applied to any data management page that needs robust filtering capabilities with server-side processing and URL synchronization.
