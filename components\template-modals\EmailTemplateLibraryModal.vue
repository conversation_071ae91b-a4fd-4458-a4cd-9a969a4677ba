<template>
  <UiModal @close="$emit('close')" size="xl">
    <div class="p-6">
      <!-- Header -->
      <div class="flex items-center space-x-3 mb-6">
        <div class="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
          <Icon name="heroicons:envelope" class="w-5 h-5 text-blue-600 dark:text-blue-400" />
        </div>
        <div>
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Email Template Library</h3>
          <p class="text-sm text-gray-600 dark:text-gray-400">Choose from pre-built email templates</p>
        </div>
      </div>

      <!-- Search and Filter -->
      <div class="flex items-center space-x-4 mb-6">
        <div class="flex-1">
          <UiInput
            v-model="searchQuery"
            placeholder="Search templates..."
            class="w-full"
          >
            <template #prefix>
              <Icon name="heroicons:magnifying-glass" class="w-4 h-4 text-gray-400" />
            </template>
          </UiInput>
        </div>
        <div>
          <select
            v-model="selectedCategory"
            class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm"
          >
            <option value="">All Categories</option>
            <option value="client-communication">Client Communication</option>
            <option value="case-updates">Case Updates</option>
            <option value="appointments">Appointments</option>
            <option value="billing">Billing</option>
            <option value="legal-notices">Legal Notices</option>
          </select>
        </div>
      </div>

      <!-- Template Grid -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4 max-h-96 overflow-y-auto">
        <div
          v-for="template in filteredTemplates"
          :key="template.id"
          class="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:border-blue-300 dark:hover:border-blue-600 cursor-pointer transition-colors"
          @click="selectTemplate(template)"
        >
          <div class="flex items-start justify-between mb-3">
            <div>
              <h4 class="text-sm font-medium text-gray-900 dark:text-white">{{ template.name }}</h4>
              <p class="text-xs text-gray-500 dark:text-gray-400">{{ template.category }}</p>
            </div>
            <span
              :class="[
                'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium',
                getCategoryColor(template.category)
              ]"
            >
              {{ template.category }}
            </span>
          </div>
          
          <p class="text-sm text-gray-600 dark:text-gray-300 mb-3">{{ template.description }}</p>
          
          <div class="space-y-2">
            <div class="text-xs font-medium text-gray-700 dark:text-gray-300">
              Subject: {{ template.subject }}
            </div>
            <div class="text-xs text-gray-500 dark:text-gray-400 bg-gray-50 dark:bg-gray-800 p-2 rounded">
              {{ template.preview }}
            </div>
          </div>
          
          <div v-if="template.variables.length > 0" class="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
            <div class="text-xs text-gray-500 dark:text-gray-400 mb-1">Variables:</div>
            <div class="flex flex-wrap gap-1">
              <span
                v-for="variable in template.variables.slice(0, 3)"
                :key="variable"
                class="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-mono bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
              >
                {{ variable }}
              </span>
              <span
                v-if="template.variables.length > 3"
                class="text-xs text-gray-500 dark:text-gray-400"
              >
                +{{ template.variables.length - 3 }} more
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- Actions -->
      <div class="flex items-center justify-end space-x-3 mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
        <UiButton @click="$emit('close')" variant="outline">
          Cancel
        </UiButton>
      </div>
    </div>
  </UiModal>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

// Emits
const emit = defineEmits<{
  close: []
  select: [template: any]
}>()

// State
const searchQuery = ref('')
const selectedCategory = ref('')

// Email templates library
const emailTemplates = [
  {
    id: 'welcome-client',
    name: 'Welcome New Client',
    category: 'client-communication',
    description: 'Welcome message for new clients with next steps',
    subject: 'Welcome to {companyName} - Your Legal Representation',
    preview: 'Dear {clientName}, Welcome to our law firm. We are pleased to represent you in your legal matter...',
    content: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #1f2937;">Welcome to {companyName}</h2>
        <p>Dear {clientName},</p>
        <p>Welcome to our law firm. We are pleased to represent you in your legal matter and look forward to working with you.</p>
        <h3>Next Steps:</h3>
        <ul>
          <li>Review and sign the retainer agreement</li>
          <li>Provide requested documentation</li>
          <li>Schedule your initial consultation</li>
        </ul>
        <p>If you have any questions, please don't hesitate to contact us at {companyPhone} or {companyEmail}.</p>
        <p>Best regards,<br>{attorneyName}<br>{companyName}</p>
      </div>
    `,
    variables: ['companyName', 'clientName', 'companyPhone', 'companyEmail', 'attorneyName']
  },
  {
    id: 'case-update',
    name: 'Case Status Update',
    category: 'case-updates',
    description: 'Notify clients about important case developments',
    subject: 'Case Update: {caseTitle} - {caseNumber}',
    preview: 'Dear {clientName}, I wanted to update you on the recent developments in your case...',
    content: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #1f2937;">Case Update</h2>
        <p>Dear {clientName},</p>
        <p>I wanted to update you on the recent developments in your case: <strong>{caseTitle}</strong> (Case #{caseNumber}).</p>
        <h3>Recent Developments:</h3>
        <p>{caseUpdate}</p>
        <h3>Next Steps:</h3>
        <p>{nextSteps}</p>
        <p>Please feel free to contact me if you have any questions or concerns.</p>
        <p>Best regards,<br>{attorneyName}<br>{companyName}</p>
      </div>
    `,
    variables: ['clientName', 'caseTitle', 'caseNumber', 'caseUpdate', 'nextSteps', 'attorneyName', 'companyName']
  },
  {
    id: 'appointment-confirmation',
    name: 'Appointment Confirmation',
    category: 'appointments',
    description: 'Confirm upcoming appointments with clients',
    subject: 'Appointment Confirmation - {appointmentDate}',
    preview: 'Dear {clientName}, This is to confirm your appointment scheduled for {appointmentDate}...',
    content: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #1f2937;">Appointment Confirmation</h2>
        <p>Dear {clientName},</p>
        <p>This is to confirm your appointment scheduled for:</p>
        <div style="background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <p><strong>Date:</strong> {appointmentDate}</p>
          <p><strong>Time:</strong> {appointmentTime}</p>
          <p><strong>Location:</strong> {companyAddress}</p>
          <p><strong>Attorney:</strong> {attorneyName}</p>
        </div>
        <p>Please bring any relevant documents and arrive 15 minutes early.</p>
        <p>If you need to reschedule, please contact us at {companyPhone}.</p>
        <p>Best regards,<br>{companyName}</p>
      </div>
    `,
    variables: ['clientName', 'appointmentDate', 'appointmentTime', 'companyAddress', 'attorneyName', 'companyPhone', 'companyName']
  },
  {
    id: 'invoice-notification',
    name: 'Invoice Notification',
    category: 'billing',
    description: 'Notify clients about new invoices',
    subject: 'Invoice #{invoiceNumber} - {companyName}',
    preview: 'Dear {clientName}, Please find attached your invoice for legal services...',
    content: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #1f2937;">Invoice Notification</h2>
        <p>Dear {clientName},</p>
        <p>Please find attached your invoice for legal services rendered.</p>
        <div style="background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <p><strong>Invoice Number:</strong> {invoiceNumber}</p>
          <p><strong>Invoice Date:</strong> {invoiceDate}</p>
          <p><strong>Amount Due:</strong> {amount}</p>
          <p><strong>Due Date:</strong> {dueDate}</p>
        </div>
        <p>Payment can be made by check, wire transfer, or online through our client portal.</p>
        <p>Thank you for your prompt attention to this matter.</p>
        <p>Best regards,<br>{companyName}</p>
      </div>
    `,
    variables: ['clientName', 'invoiceNumber', 'invoiceDate', 'amount', 'dueDate', 'companyName']
  },
  {
    id: 'document-ready',
    name: 'Document Ready for Review',
    category: 'legal-notices',
    description: 'Notify clients when documents are ready for review',
    subject: 'Document Ready for Review - {documentTitle}',
    preview: 'Dear {clientName}, The document {documentTitle} is now ready for your review...',
    content: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #1f2937;">Document Ready for Review</h2>
        <p>Dear {clientName},</p>
        <p>The document <strong>{documentTitle}</strong> is now ready for your review and signature.</p>
        <p>Please review the document carefully and let us know if you have any questions or require any changes.</p>
        <h3>Next Steps:</h3>
        <ul>
          <li>Review the document thoroughly</li>
          <li>Contact us with any questions or concerns</li>
          <li>Sign and return the document by {deadlineDate}</li>
        </ul>
        <p>You can access the document through our secure client portal or we can arrange for in-person signing.</p>
        <p>Best regards,<br>{attorneyName}<br>{companyName}</p>
      </div>
    `,
    variables: ['clientName', 'documentTitle', 'deadlineDate', 'attorneyName', 'companyName']
  },
  {
    id: 'payment-reminder',
    name: 'Payment Reminder',
    category: 'billing',
    description: 'Friendly reminder for overdue payments',
    subject: 'Payment Reminder - Invoice #{invoiceNumber}',
    preview: 'Dear {clientName}, This is a friendly reminder that payment for invoice #{invoiceNumber} is now overdue...',
    content: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #1f2937;">Payment Reminder</h2>
        <p>Dear {clientName},</p>
        <p>This is a friendly reminder that payment for Invoice #{invoiceNumber} was due on {dueDate}.</p>
        <div style="background-color: #fef3c7; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #f59e0b;">
          <p><strong>Invoice Number:</strong> {invoiceNumber}</p>
          <p><strong>Amount Due:</strong> {amount}</p>
          <p><strong>Original Due Date:</strong> {dueDate}</p>
        </div>
        <p>If payment has already been sent, please disregard this notice. If you have any questions about this invoice or need to discuss payment arrangements, please contact us immediately.</p>
        <p>Thank you for your prompt attention to this matter.</p>
        <p>Best regards,<br>{companyName}</p>
      </div>
    `,
    variables: ['clientName', 'invoiceNumber', 'dueDate', 'amount', 'companyName']
  }
]

// Computed
const filteredTemplates = computed(() => {
  let templates = emailTemplates
  
  // Filter by category
  if (selectedCategory.value) {
    templates = templates.filter(template => template.category === selectedCategory.value)
  }
  
  // Filter by search query
  if (searchQuery.value.trim()) {
    const query = searchQuery.value.toLowerCase()
    templates = templates.filter(template =>
      template.name.toLowerCase().includes(query) ||
      template.description.toLowerCase().includes(query) ||
      template.subject.toLowerCase().includes(query)
    )
  }
  
  return templates
})

// Methods
const selectTemplate = (template: any) => {
  emit('select', template)
}

const getCategoryColor = (category: string) => {
  const colors = {
    'client-communication': 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
    'case-updates': 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
    'appointments': 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200',
    'billing': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
    'legal-notices': 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
  }
  return colors[category as keyof typeof colors] || 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
}
</script>
