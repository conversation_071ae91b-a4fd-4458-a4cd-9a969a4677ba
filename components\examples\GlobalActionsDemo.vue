<template>
  <div class="space-y-8 p-6 bg-white dark:bg-gray-900 rounded-lg shadow-lg">
    <div class="text-center">
      <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-2">
        Global Properties Demo
      </h2>
      <p class="text-gray-600 dark:text-gray-400">
        Demonstrating the Global Properties Plugin functionality
      </p>
    </div>

    <!-- Add Properties Section -->
    <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
        Add Global Properties
      </h3>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Property Name
          </label>
          <input
            v-model="newPropertyName"
            type="text"
            placeholder="e.g., myFunction, userName, isLoggedIn"
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
          />
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Property Type
          </label>
          <select
            v-model="newPropertyType"
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
          >
            <option value="string">String</option>
            <option value="number">Number</option>
            <option value="boolean">Boolean</option>
            <option value="function">Function</option>
            <option value="object">Object</option>
          </select>
        </div>
      </div>

      <div class="mb-4">
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Property Value
        </label>
        <textarea
          v-model="newPropertyValue"
          :placeholder="getPlaceholderForType(newPropertyType)"
          rows="3"
          class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
        ></textarea>
      </div>

      <div class="flex space-x-2">
        <UiButton @click="addProperty" variant="primary">
          Add Property
        </UiButton>
        <UiButton @click="clearForm" variant="secondary">
          Clear Form
        </UiButton>
      </div>
    </div>

    <!-- Current Properties Section -->
    <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
          Current Global Properties ({{ propertyCount }})
        </h3>
        <UiButton @click="clearAllProperties" variant="danger" size="sm">
          Clear All
        </UiButton>
      </div>

      <div v-if="propertyCount === 0" class="text-center py-8 text-gray-500">
        No global properties available. Add some properties above to get started.
      </div>

      <div v-else class="space-y-2">
        <div
          v-for="propertyName in propertyNames"
          :key="propertyName"
          class="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-800"
        >
          <div class="flex-1">
            <div class="flex items-center space-x-2">
              <h4 class="font-medium text-gray-900 dark:text-white">
                {{ propertyName }}
              </h4>
              <span
                :class="[
                  'px-2 py-0.5 text-xs rounded',
                  getTypeClass(getPropertyType(propertyName))
                ]"
              >
                {{ getPropertyType(propertyName) }}
              </span>
            </div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mt-1 font-mono">
              {{ formatPropertyValue(propertyName) }}
            </p>
          </div>
          <div class="flex space-x-2">
            <UiButton
              v-if="getPropertyType(propertyName) === 'function'"
              @click="executeFunction(propertyName)"
              size="xs"
              variant="secondary"
            >
              Execute
            </UiButton>
            <UiButton
              @click="removeProperty(propertyName)"
              size="xs"
              variant="danger"
            >
              Remove
            </UiButton>
          </div>
        </div>
      </div>
    </div>

    <!-- Usage Examples -->
    <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
        Usage Examples
      </h3>

      <div class="space-y-4">
        <div class="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
          <h4 class="font-medium text-gray-900 dark:text-white mb-2">Add a simple function:</h4>
          <code class="text-sm text-gray-600 dark:text-gray-400">
            Property Name: sayHello<br>
            Type: function<br>
            Value: function() { return "Hello World!" }
          </code>
        </div>

        <div class="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
          <h4 class="font-medium text-gray-900 dark:text-white mb-2">Add user data:</h4>
          <code class="text-sm text-gray-600 dark:text-gray-400">
            Property Name: currentUser<br>
            Type: object<br>
            Value: {"name": "John Doe", "role": "admin"}
          </code>
        </div>

        <div class="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
          <h4 class="font-medium text-gray-900 dark:text-white mb-2">Add configuration:</h4>
          <code class="text-sm text-gray-600 dark:text-gray-400">
            Property Name: isDebugMode<br>
            Type: boolean<br>
            Value: true
          </code>
        </div>
      </div>
    </div>

    <!-- Statistics -->
    <div class="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
      <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">
        {{ propertyCount }}
      </div>
      <div class="text-sm text-blue-600 dark:text-blue-400">Global Properties</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

// Access the global properties plugin
const { $global } = useNuxtApp()

// Form data
const newPropertyName = ref('')
const newPropertyType = ref('string')
const newPropertyValue = ref('')

// Computed properties
const propertyNames = computed(() => $global.keys())
const propertyCount = computed(() => propertyNames.value.length)

// Methods
const addProperty = () => {
  if (!newPropertyName.value.trim()) {
    alert('Please enter a property name')
    return
  }

  let value: any

  try {
    switch (newPropertyType.value) {
      case 'string':
        value = newPropertyValue.value
        break
      case 'number':
        value = parseFloat(newPropertyValue.value)
        if (isNaN(value)) {
          alert('Invalid number value')
          return
        }
        break
      case 'boolean':
        value = newPropertyValue.value.toLowerCase() === 'true'
        break
      case 'function':
        // Create a function from the string
        value = new Function('return ' + newPropertyValue.value)()
        break
      case 'object':
        value = JSON.parse(newPropertyValue.value)
        break
      default:
        value = newPropertyValue.value
    }

    $global.add(newPropertyName.value, value)
    clearForm()
  } catch (error) {
    alert('Error adding property: ' + (error as Error).message)
  }
}

const removeProperty = (propertyName: string) => {
  $global.remove(propertyName)
}

const clearAllProperties = () => {
  $global.clear()
}

const clearForm = () => {
  newPropertyName.value = ''
  newPropertyType.value = 'string'
  newPropertyValue.value = ''
}

// Utility methods
const getPlaceholderForType = (type: string) => {
  switch (type) {
    case 'string':
      return 'e.g., "Hello World"'
    case 'number':
      return 'e.g., 42'
    case 'boolean':
      return 'true or false'
    case 'function':
      return 'e.g., function() { alert("Hello!") }'
    case 'object':
      return 'e.g., {"name": "John", "age": 30}'
    default:
      return 'Enter value...'
  }
}

const getPropertyType = (propertyName: string) => {
  const value = $global.get(propertyName)
  return typeof value
}

const formatPropertyValue = (propertyName: string) => {
  const value = $global.get(propertyName)
  const type = typeof value

  switch (type) {
    case 'function':
      return value.toString().substring(0, 100) + (value.toString().length > 100 ? '...' : '')
    case 'object':
      return JSON.stringify(value, null, 2).substring(0, 100) + (JSON.stringify(value).length > 100 ? '...' : '')
    case 'string':
      return `"${value}"`
    default:
      return String(value)
  }
}

const getTypeClass = (type: string) => {
  const classes = {
    string: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
    number: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
    boolean: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200',
    function: 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200',
    object: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
  }
  return classes[type as keyof typeof classes] || 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
}

const executeFunction = (propertyName: string) => {
  const func = $global.get(propertyName)
  if (typeof func === 'function') {
    try {
      const result = func()
      alert(`Function "${propertyName}" executed. Result: ${result}`)
    } catch (error) {
      alert(`Error executing function "${propertyName}": ${(error as Error).message}`)
    }
  }
}
</script>
