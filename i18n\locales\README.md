# Internationalization (i18n) Structure

This directory contains the translation files for the Legal SaaS frontend application, organized by language and feature for better maintainability.

## Directory Structure

```
locales/
├── en/                     # English translations
│   ├── index.ts           # Main export file
│   ├── common.json        # Common UI elements
│   ├── time.json          # Time-related translations
│   ├── tenantList.json    # Tenant list page
│   ├── tenantDetail.json  # Tenant detail page
│   ├── userManagement.json # User management
│   ├── navigation.json    # Navigation components
│   ├── caseManagement.json # Case management
│   ├── authPages.json     # Authentication pages
│   ├── ui.json           # UI components
│   ├── dashboard.json    # Dashboard pages
│   ├── documents.json    # Document management
│   ├── templates.json    # Template management
│   ├── notifications.json # Notifications
│   └── errors.json       # Error messages
├── he/                    # Hebrew translations
│   ├── index.ts          # Main export file
│   ├── common.json       # Common UI elements
│   ├── time.json         # Time-related translations
│   ├── tenantList.json   # Tenant list page
│   ├── userManagement.json # User management
│   ├── navigation.json   # Navigation components
│   └── ...               # Other files (fallback to English)
├── ar/                    # Arabic translations
│   ├── index.ts          # Main export file
│   ├── common.json       # Common UI elements
│   └── ...               # Other files (fallback to English)
└── README.md             # This file
```

## Translation Categories

### 1. **common.json**
Basic UI elements used throughout the application:
- Actions: save, cancel, delete, edit, create, etc.
- Status: active, inactive, enabled, disabled, etc.
- General terms: user, account, settings, etc.

### 2. **time.json**
Time-related translations:
- Relative time: "now", "minutes ago", "hours ago", etc.
- Time formatting helpers

### 3. **tenantList.json**
Tenant list/overview page translations:
- Recent tenants section
- Platform activity metrics
- Quick actions

### 4. **tenantDetail.json**
Tenant detail page translations:
- Tenant information fields
- Configuration options
- User management within tenant
- Activity logs

### 5. **userManagement.json**
User management translations:
- User creation and editing
- Role management
- User analytics
- Permissions

### 6. **navigation.json**
Navigation-related translations:
- Menu items
- Breadcrumbs
- User menu options
- Sidebar elements

### 7. **caseManagement.json**
Legal case management translations:
- Case creation and editing
- Case status and priority
- Legal-specific terminology
- Case analytics

### 8. **authPages.json**
Authentication page translations:
- Login/signup forms
- Password reset
- Account creation
- Security messages

### 9. **ui.json**
UI component translations:
- Form validation messages
- Loading states
- Error handling
- Confirmation dialogs
- File upload messages

### 10. **dashboard.json**
Dashboard page translations:
- Overview sections
- Quick stats
- Analytics labels

### 11. **documents.json**
Document management translations:
- Document upload/download
- Document types
- File management

### 12. **templates.json**
Template management translations:
- Template creation
- Template categories
- Template editor

### 13. **notifications.json**
Notification system translations:
- Notification types
- Settings
- Preferences

### 14. **errors.json**
Error page and message translations:
- HTTP error codes
- System errors
- Validation errors

## Usage in Components

### Basic Usage
```vue
<template>
  <div>
    <h1>{{ $t('common.welcome') }}</h1>
    <button>{{ $t('common.save') }}</button>
    <p>{{ $t('time.minutesAgo', { minutes: 5 }) }}</p>
  </div>
</template>

<script setup>
// useI18n is auto-imported in Nuxt
const { t: $t } = useI18n()
</script>
```

### With Parameters
```vue
<template>
  <p>{{ $t('tenantDetail.viewAllUsers', { count: userCount }) }}</p>
</template>
```

### Fallback Handling
If a translation is missing in Hebrew or Arabic, the system will automatically fall back to the English translation.

## Adding New Translations

### 1. Add to English First
Always add new translation keys to the appropriate English JSON file first.

### 2. Update Index Files
If you create a new translation category, update the `index.ts` files in each language directory.

### 3. Add to Other Languages
Add translations to Hebrew and Arabic files, or they will fallback to English.

### 4. Use Nested Keys
Organize related translations under logical groupings:
```json
{
  "userActions": {
    "create": "Create User",
    "edit": "Edit User",
    "delete": "Delete User"
  }
}
```

## Language Support

- **English (en)**: Complete translations (base language)
- **Hebrew (he)**: Complete translations for all categories
- **Arabic (ar)**: Complete translations for all categories

Both Hebrew and Arabic support RTL (right-to-left) text direction automatically.

## Best Practices

1. **Keep keys descriptive**: Use clear, hierarchical key names
2. **Group related translations**: Organize by feature or component
3. **Use parameters for dynamic content**: `{ count: 5 }` instead of hardcoded numbers
4. **Maintain consistency**: Use the same terminology across similar contexts
5. **Test with all languages**: Ensure UI works with different text lengths
6. **Consider RTL layouts**: Test Hebrew and Arabic for proper layout

## Development Workflow

1. Add English translations first
2. Test functionality with English
3. Add Hebrew/Arabic translations as needed
4. Test RTL layouts and text overflow
5. Update this README if adding new categories

## File Naming Convention

- Use camelCase for JSON files: `userManagement.json`
- Use descriptive names that match the feature: `caseManagement.json`
- Keep file names consistent across languages
- Use `index.ts` for the main export file in each language directory
