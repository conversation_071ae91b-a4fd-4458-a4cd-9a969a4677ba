/**
 * Real-time Events Composable
 * Handles WebSocket and Server-Sent Events integration with the event bus
 */

import { ref, onUnmounted, computed } from 'vue'
import { getEventBus } from '~/utils/eventBus'
import type { EventMap, RealtimeEvent, WebSocketEvent, ServerSentEvent } from '~/types/events'

// ============================================================================
// INTERFACES
// ============================================================================

export interface RealtimeConfig {
  websocketUrl?: string
  sseUrl?: string
  reconnectInterval?: number
  maxReconnectAttempts?: number
  heartbeatInterval?: number
  enableLogging?: boolean
}

export interface UseRealtimeEventsReturn {
  // Connection state
  isConnected: Ref<boolean>
  connectionStatus: Ref<'disconnected' | 'connecting' | 'connected' | 'error'>
  lastError: Ref<string | null>
  reconnectAttempts: Ref<number>
  
  // Methods
  connect: () => Promise<void>
  disconnect: () => void
  sendMessage: (message: any) => void
  
  // WebSocket specific
  connectWebSocket: (url: string) => Promise<void>
  disconnectWebSocket: () => void
  
  // SSE specific
  connectSSE: (url: string) => void
  disconnectSSE: () => void
}

// ============================================================================
// MAIN COMPOSABLE
// ============================================================================

export function useRealtimeEvents(config: RealtimeConfig = {}): UseRealtimeEventsReturn {
  const eventBus = getEventBus()
  
  // Configuration with defaults
  const {
    websocketUrl = '',
    sseUrl = '',
    reconnectInterval = 5000,
    maxReconnectAttempts = 5,
    heartbeatInterval = 30000,
    enableLogging = process.env.NODE_ENV === 'development'
  } = config

  // ============================================================================
  // STATE
  // ============================================================================

  const isConnected = ref(false)
  const connectionStatus = ref<'disconnected' | 'connecting' | 'connected' | 'error'>('disconnected')
  const lastError = ref<string | null>(null)
  const reconnectAttempts = ref(0)

  // Connection instances
  let websocket: WebSocket | null = null
  let eventSource: EventSource | null = null
  let heartbeatTimer: NodeJS.Timeout | null = null
  let reconnectTimer: NodeJS.Timeout | null = null

  // ============================================================================
  // WEBSOCKET METHODS
  // ============================================================================

  const connectWebSocket = async (url: string): Promise<void> => {
    if (websocket?.readyState === WebSocket.OPEN) {
      log('WebSocket already connected')
      return
    }

    connectionStatus.value = 'connecting'
    lastError.value = null

    try {
      websocket = new WebSocket(url)

      websocket.onopen = () => {
        log('WebSocket connected')
        connectionStatus.value = 'connected'
        isConnected.value = true
        reconnectAttempts.value = 0
        
        // Start heartbeat
        startHeartbeat()
        
        // Emit connection event
        eventBus.emit('realtime:connected', {
          timestamp: new Date().toISOString(),
          channel: 'websocket',
          eventType: 'connected',
          data: { url }
        })
      }

      websocket.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data)
          handleRealtimeMessage(message, 'websocket')
        } catch (error) {
          log('Failed to parse WebSocket message:', error)
          lastError.value = 'Failed to parse message'
        }
      }

      websocket.onclose = (event) => {
        log('WebSocket disconnected:', event.code, event.reason)
        isConnected.value = false
        connectionStatus.value = 'disconnected'
        stopHeartbeat()
        
        // Emit disconnection event
        eventBus.emit('realtime:disconnected', {
          timestamp: new Date().toISOString(),
          channel: 'websocket',
          eventType: 'disconnected',
          data: { code: event.code, reason: event.reason }
        })

        // Attempt reconnection if not intentional
        if (event.code !== 1000 && reconnectAttempts.value < maxReconnectAttempts) {
          scheduleReconnect(() => connectWebSocket(url))
        }
      }

      websocket.onerror = (error) => {
        log('WebSocket error:', error)
        connectionStatus.value = 'error'
        lastError.value = 'WebSocket connection error'
        
        // Emit error event
        eventBus.emit('realtime:error', {
          timestamp: new Date().toISOString(),
          channel: 'websocket',
          eventType: 'error',
          data: { error: 'Connection error' }
        })
      }

    } catch (error) {
      log('Failed to create WebSocket:', error)
      connectionStatus.value = 'error'
      lastError.value = 'Failed to create WebSocket connection'
      throw error
    }
  }

  const disconnectWebSocket = (): void => {
    if (websocket) {
      websocket.close(1000, 'Intentional disconnect')
      websocket = null
    }
    stopHeartbeat()
    clearReconnectTimer()
  }

  const sendMessage = (message: any): void => {
    if (websocket?.readyState === WebSocket.OPEN) {
      websocket.send(JSON.stringify(message))
      log('Message sent:', message)
    } else {
      log('Cannot send message: WebSocket not connected')
      lastError.value = 'WebSocket not connected'
    }
  }

  // ============================================================================
  // SERVER-SENT EVENTS METHODS
  // ============================================================================

  const connectSSE = (url: string): void => {
    if (eventSource?.readyState === EventSource.OPEN) {
      log('SSE already connected')
      return
    }

    connectionStatus.value = 'connecting'
    lastError.value = null

    try {
      eventSource = new EventSource(url)

      eventSource.onopen = () => {
        log('SSE connected')
        connectionStatus.value = 'connected'
        isConnected.value = true
        reconnectAttempts.value = 0
        
        // Emit connection event
        eventBus.emit('realtime:connected', {
          timestamp: new Date().toISOString(),
          channel: 'sse',
          eventType: 'connected',
          data: { url }
        })
      }

      eventSource.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data)
          handleRealtimeMessage(message, 'sse', event.lastEventId)
        } catch (error) {
          log('Failed to parse SSE message:', error)
          lastError.value = 'Failed to parse SSE message'
        }
      }

      eventSource.onerror = (error) => {
        log('SSE error:', error)
        
        if (eventSource?.readyState === EventSource.CLOSED) {
          connectionStatus.value = 'disconnected'
          isConnected.value = false
          
          // Emit disconnection event
          eventBus.emit('realtime:disconnected', {
            timestamp: new Date().toISOString(),
            channel: 'sse',
            eventType: 'disconnected',
            data: { error: 'Connection closed' }
          })

          // Attempt reconnection
          if (reconnectAttempts.value < maxReconnectAttempts) {
            scheduleReconnect(() => connectSSE(url))
          }
        } else {
          connectionStatus.value = 'error'
          lastError.value = 'SSE connection error'
          
          // Emit error event
          eventBus.emit('realtime:error', {
            timestamp: new Date().toISOString(),
            channel: 'sse',
            eventType: 'error',
            data: { error: 'Connection error' }
          })
        }
      }

    } catch (error) {
      log('Failed to create SSE connection:', error)
      connectionStatus.value = 'error'
      lastError.value = 'Failed to create SSE connection'
    }
  }

  const disconnectSSE = (): void => {
    if (eventSource) {
      eventSource.close()
      eventSource = null
    }
    clearReconnectTimer()
  }

  // ============================================================================
  // UNIFIED METHODS
  // ============================================================================

  const connect = async (): Promise<void> => {
    if (websocketUrl) {
      await connectWebSocket(websocketUrl)
    }
    if (sseUrl) {
      connectSSE(sseUrl)
    }
  }

  const disconnect = (): void => {
    disconnectWebSocket()
    disconnectSSE()
    connectionStatus.value = 'disconnected'
    isConnected.value = false
  }

  // ============================================================================
  // HELPER METHODS
  // ============================================================================

  const handleRealtimeMessage = (message: any, channel: string, eventId?: string): void => {
    log('Received message:', message)

    // Create realtime event
    const realtimeEvent: RealtimeEvent = {
      timestamp: new Date().toISOString(),
      channel,
      eventType: message.type || 'message',
      data: message.data || message
    }

    // Emit generic realtime message event
    eventBus.emit('realtime:message', realtimeEvent)

    // Route specific business events
    if (message.type) {
      switch (message.type) {
        case 'case_updated':
          eventBus.emit('case:updated', {
            ...message.data,
            timestamp: new Date().toISOString(),
            source: 'realtime'
          })
          break

        case 'document_uploaded':
          eventBus.emit('document:uploaded', {
            ...message.data,
            timestamp: new Date().toISOString(),
            source: 'realtime'
          })
          break

        case 'notification':
          eventBus.emit('notification:created', {
            ...message.data,
            timestamp: new Date().toISOString(),
            source: 'realtime'
          })
          break

        case 'user_status_changed':
          // Handle user status changes
          break

        default:
          log('Unknown message type:', message.type)
      }
    }
  }

  const startHeartbeat = (): void => {
    if (heartbeatTimer) clearInterval(heartbeatTimer)
    
    heartbeatTimer = setInterval(() => {
      if (websocket?.readyState === WebSocket.OPEN) {
        sendMessage({ type: 'ping', timestamp: Date.now() })
      }
    }, heartbeatInterval)
  }

  const stopHeartbeat = (): void => {
    if (heartbeatTimer) {
      clearInterval(heartbeatTimer)
      heartbeatTimer = null
    }
  }

  const scheduleReconnect = (reconnectFn: () => void): void => {
    reconnectAttempts.value++
    log(`Scheduling reconnect attempt ${reconnectAttempts.value}/${maxReconnectAttempts}`)
    
    reconnectTimer = setTimeout(() => {
      log(`Attempting reconnect ${reconnectAttempts.value}/${maxReconnectAttempts}`)
      reconnectFn()
    }, reconnectInterval)
  }

  const clearReconnectTimer = (): void => {
    if (reconnectTimer) {
      clearTimeout(reconnectTimer)
      reconnectTimer = null
    }
  }

  const log = (...args: any[]): void => {
    if (enableLogging) {
      console.log('[RealtimeEvents]', ...args)
    }
  }

  // ============================================================================
  // LIFECYCLE
  // ============================================================================

  onUnmounted(() => {
    disconnect()
  })

  // ============================================================================
  // RETURN INTERFACE
  // ============================================================================

  return {
    // State
    isConnected,
    connectionStatus,
    lastError,
    reconnectAttempts,
    
    // Methods
    connect,
    disconnect,
    sendMessage,
    connectWebSocket,
    disconnectWebSocket,
    connectSSE,
    disconnectSSE
  }
}
