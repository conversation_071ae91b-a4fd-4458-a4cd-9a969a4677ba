/**
 * Enhanced Performance Monitoring Composable
 * 
 * Comprehensive performance monitoring with Core Web Vitals,
 * resource tracking, memory monitoring, and optimization insights
 */

import { ref, computed, onMounted, onUnmounted, readonly } from 'vue' // Removed watch, Added readonly
import type { ISODateString } from '@shared/types/core'
import { useLogger } from '../core/useLogger.js' // Added .js
import { useAnalytics } from './useAnalytics.js' // Added .js

// ============================================================================
// PERFORMANCE MONITORING TYPES
// ============================================================================

export interface PerformanceConfig {
  enabled: boolean
  enableCoreWebVitals: boolean
  enableResourceTracking: boolean
  enableMemoryMonitoring: boolean
  enableUserTiming: boolean
  enableNavigationTiming: boolean
  reportingInterval: number
  thresholds: PerformanceThresholds
}

export interface PerformanceThresholds {
  lcp: { good: number; poor: number }
  fid: { good: number; poor: number }
  cls: { good: number; poor: number }
  ttfb: { good: number; poor: number }
  fcp: { good: number; poor: number }
  memoryUsage: { warning: number; critical: number }
}

export interface CoreWebVitals {
  lcp: number | null // Largest Contentful Paint
  fid: number | null // First Input Delay
  cls: number | null // Cumulative Layout Shift
  ttfb: number | null // Time to First Byte
  fcp: number | null // First Contentful Paint
  inp: number | null // Interaction to Next Paint
}

export interface NavigationMetrics {
  domContentLoaded: number
  loadComplete: number
  firstPaint: number
  firstContentfulPaint: number
  domInteractive: number
  redirectTime: number
  dnsLookupTime: number
  tcpConnectTime: number
  requestTime: number
  responseTime: number
}

export interface ResourceMetrics {
  totalResources: number
  totalSize: number
  totalDuration: number
  resourcesByType: Record<string, ResourceTypeMetrics>
  slowestResources: ResourceTiming[]
}

export interface ResourceTypeMetrics {
  count: number
  totalSize: number
  averageDuration: number
  maxDuration: number
}

export interface ResourceTiming {
  name: string
  type: string
  size: number
  duration: number
  startTime: number
}

export interface MemoryMetrics {
  usedJSHeapSize: number
  totalJSHeapSize: number
  jsHeapSizeLimit: number
  usagePercentage: number
  trend: 'increasing' | 'decreasing' | 'stable'
}

export interface PerformanceReport {
  timestamp: ISODateString
  url: string
  userAgent: string
  connectionType?: string
  coreWebVitals: CoreWebVitals
  navigation: NavigationMetrics
  resources: ResourceMetrics
  memory: MemoryMetrics
  customMetrics: Record<string, number>
  recommendations: PerformanceRecommendation[]
}

export interface PerformanceRecommendation {
  type: 'warning' | 'error' | 'info'
  metric: string
  message: string
  impact: 'low' | 'medium' | 'high'
  suggestion: string
}

// ============================================================================
// PERFORMANCE MONITORING COMPOSABLE
// ============================================================================

export function usePerformanceMonitoring(config: Partial<PerformanceConfig> = {}) {
  // ============================================================================
  // CONFIGURATION
  // ============================================================================
  
  const defaultConfig: PerformanceConfig = {
    enabled: true,
    enableCoreWebVitals: true,
    enableResourceTracking: true,
    enableMemoryMonitoring: true,
    enableUserTiming: true,
    enableNavigationTiming: true,
    reportingInterval: 30000, // 30 seconds
    thresholds: {
      lcp: { good: 2500, poor: 4000 },
      fid: { good: 100, poor: 300 },
      cls: { good: 0.1, poor: 0.25 },
      ttfb: { good: 800, poor: 1800 },
      fcp: { good: 1800, poor: 3000 },
      memoryUsage: { warning: 70, critical: 90 }
    }
  }
  
  const performanceConfig = ref({ ...defaultConfig, ...config })
  
  // ============================================================================
  // DEPENDENCIES
  // ============================================================================
  
  const logger = useLogger('PerformanceMonitoring')
  const analytics = useAnalytics()
  
  // ============================================================================
  // STATE
  // ============================================================================
  
  const isSupported = ref(typeof performance !== 'undefined')
  const isMonitoring = ref(false)
  const coreWebVitals = ref<CoreWebVitals>({
    lcp: null,
    fid: null,
    cls: null,
    ttfb: null,
    fcp: null,
    inp: null
  })
  
  const navigationMetrics = ref<NavigationMetrics | null>(null)
  const resourceMetrics = ref<ResourceMetrics | null>(null)
  const memoryMetrics = ref<MemoryMetrics | null>(null)
  const customMetrics = ref<Record<string, number>>({})
  const performanceHistory = ref<PerformanceReport[]>([])
  
  // ============================================================================
  // COMPUTED
  // ============================================================================
  
  const overallScore = computed(() => {
    const vitals = coreWebVitals.value
    if (!vitals.lcp || !vitals.fid || !vitals.cls) return null
    
    const lcpScore = getMetricScore('lcp', vitals.lcp)
    const fidScore = getMetricScore('fid', vitals.fid)
    const clsScore = getMetricScore('cls', vitals.cls)
    
    return Math.round((lcpScore + fidScore + clsScore) / 3)
  })
  
  const performanceGrade = computed(() => {
    const score = overallScore.value
    if (score === null) return 'N/A'
    if (score >= 90) return 'A'
    if (score >= 75) return 'B'
    if (score >= 60) return 'C'
    if (score >= 45) return 'D'
    return 'F'
  })
  
  const recommendations = computed(() => {
    const recs: PerformanceRecommendation[] = []
    const vitals = coreWebVitals.value
    const thresholds = performanceConfig.value.thresholds
    
    // LCP recommendations
    if (vitals.lcp && vitals.lcp > thresholds.lcp.poor) {
      recs.push({
        type: 'error',
        metric: 'LCP',
        message: `Largest Contentful Paint is ${vitals.lcp}ms (poor)`,
        impact: 'high',
        suggestion: 'Optimize images, reduce server response times, and eliminate render-blocking resources'
      })
    } else if (vitals.lcp && vitals.lcp > thresholds.lcp.good) {
      recs.push({
        type: 'warning',
        metric: 'LCP',
        message: `Largest Contentful Paint is ${vitals.lcp}ms (needs improvement)`,
        impact: 'medium',
        suggestion: 'Consider optimizing critical resources and improving server response times'
      })
    }
    
    // FID recommendations
    if (vitals.fid && vitals.fid > thresholds.fid.poor) {
      recs.push({
        type: 'error',
        metric: 'FID',
        message: `First Input Delay is ${vitals.fid}ms (poor)`,
        impact: 'high',
        suggestion: 'Reduce JavaScript execution time and break up long tasks'
      })
    }
    
    // CLS recommendations
    if (vitals.cls && vitals.cls > thresholds.cls.poor) {
      recs.push({
        type: 'error',
        metric: 'CLS',
        message: `Cumulative Layout Shift is ${vitals.cls} (poor)`,
        impact: 'high',
        suggestion: 'Set size attributes on images and videos, avoid inserting content above existing content'
      })
    }
    
    // Memory recommendations
    if (memoryMetrics.value) {
      const usage = memoryMetrics.value.usagePercentage
      if (usage > thresholds.memoryUsage.critical) {
        recs.push({
          type: 'error',
          metric: 'Memory',
          message: `Memory usage is ${usage}% (critical)`,
          impact: 'high',
          suggestion: 'Investigate memory leaks and optimize component lifecycle'
        })
      } else if (usage > thresholds.memoryUsage.warning) {
        recs.push({
          type: 'warning',
          metric: 'Memory',
          message: `Memory usage is ${usage}% (high)`,
          impact: 'medium',
          suggestion: 'Monitor memory usage and consider optimizing data structures'
        })
      }
    }
    
    return recs
  })
  
  // ============================================================================
  // CORE WEB VITALS TRACKING
  // ============================================================================
  
  const trackCoreWebVitals = () => {
    if (!performanceConfig.value.enableCoreWebVitals || !isSupported.value) return
    
    // Track LCP
    trackLCP()
    
    // Track FID
    trackFID()
    
    // Track CLS
    trackCLS()
    
    // Track TTFB
    trackTTFB()
    
    // Track FCP
    trackFCP()
    
    // Track INP (if supported)
    trackINP()
  }
  
  const trackLCP = () => {
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        const lastEntry = entries[entries.length - 1] as any
        
        coreWebVitals.value.lcp = lastEntry.startTime
        
        analytics.trackPerformance({
          largestContentfulPaint: lastEntry.startTime
        })
        
        logger.debug('LCP measured', { lcp: lastEntry.startTime })
      })
      
      observer.observe({ entryTypes: ['largest-contentful-paint'] })
    }
  }
  
  const trackFID = () => {
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        entries.forEach((entry: any) => {
          coreWebVitals.value.fid = entry.processingStart - entry.startTime
          
          analytics.trackPerformance({
            firstInputDelay: coreWebVitals.value.fid
          })
          
          logger.debug('FID measured', { fid: coreWebVitals.value.fid })
        })
      })
      
      observer.observe({ entryTypes: ['first-input'] })
    }
  }
  
  const trackCLS = () => {
    if ('PerformanceObserver' in window) {
      let clsValue = 0
      let sessionValue = 0
      let sessionEntries: any[] = []
      
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        
        entries.forEach((entry: any) => {
          if (!entry.hadRecentInput) {
            const firstSessionEntry = sessionEntries[0]
            const lastSessionEntry = sessionEntries[sessionEntries.length - 1]
            
            if (sessionValue && 
                entry.startTime - lastSessionEntry.startTime < 1000 &&
                entry.startTime - firstSessionEntry.startTime < 5000) {
              sessionValue += entry.value
              sessionEntries.push(entry)
            } else {
              sessionValue = entry.value
              sessionEntries = [entry]
            }
            
            if (sessionValue > clsValue) {
              clsValue = sessionValue
              coreWebVitals.value.cls = clsValue
              
              analytics.trackPerformance({
                cumulativeLayoutShift: clsValue
              })
              
              logger.debug('CLS measured', { cls: clsValue })
            }
          }
        })
      })
      
      observer.observe({ entryTypes: ['layout-shift'] })
    }
  }
  
  const trackTTFB = () => {
    const navEntry = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
    if (navEntry) {
      coreWebVitals.value.ttfb = navEntry.responseStart - navEntry.requestStart
      
      analytics.trackPerformance({
        ttfb: coreWebVitals.value.ttfb // Match the CoreWebVitals property name
      })
    }
  }
  
  const trackFCP = () => {
    const fcpEntry = performance.getEntriesByName('first-contentful-paint')[0]
    if (fcpEntry) {
      coreWebVitals.value.fcp = fcpEntry.startTime
      
      analytics.trackPerformance({
        firstContentfulPaint: fcpEntry.startTime
      })
    }
  }
  
  const trackINP = () => {
    // INP is still experimental, implement when available
    if ('PerformanceObserver' in window) {
      try {
        const observer = new PerformanceObserver((list) => {
          const entries = list.getEntries()
          entries.forEach((entry: any) => {
            if (entry.interactionId) {
              const inp = entry.processingEnd - entry.startTime
              coreWebVitals.value.inp = Math.max(coreWebVitals.value.inp || 0, inp)
            }
          })
        })
        
        observer.observe({ entryTypes: ['event'] })
      } catch (error) {
        // INP not supported
      }
    }
  }
  
  // ============================================================================
  // NAVIGATION TIMING
  // ============================================================================
  
  const trackNavigationTiming = () => {
    if (!performanceConfig.value.enableNavigationTiming || !isSupported.value) return
    
    const navEntry = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
    if (!navEntry) return
    
    navigationMetrics.value = {
      domContentLoaded: navEntry.domContentLoadedEventEnd - navEntry.domContentLoadedEventStart,
      loadComplete: navEntry.loadEventEnd - navEntry.loadEventStart,
      firstPaint: getFirstPaint(),
      firstContentfulPaint: getFirstContentfulPaint(),
      domInteractive: navEntry.domInteractive - navEntry.startTime, // Use startTime
      redirectTime: navEntry.redirectEnd - navEntry.redirectStart,
      dnsLookupTime: navEntry.domainLookupEnd - navEntry.domainLookupStart,
      tcpConnectTime: navEntry.connectEnd - navEntry.connectStart,
      requestTime: navEntry.responseStart - navEntry.requestStart, // This is TTFB essentially
      responseTime: navEntry.responseEnd - navEntry.responseStart
    }
    
    logger.debug('Navigation timing measured', navigationMetrics.value)
  }
  
  // ============================================================================
  // RESOURCE TRACKING
  // ============================================================================
  
  const trackResourceMetrics = () => {
    if (!performanceConfig.value.enableResourceTracking || !isSupported.value) return
    
    const resourceEntries = performance.getEntriesByType('resource') as PerformanceResourceTiming[]
    
    const resourcesByType: Record<string, ResourceTypeMetrics> = {}
    const slowestResources: ResourceTiming[] = []
    let totalSize = 0
    let totalDuration = 0
    
    resourceEntries.forEach((entry) => {
      const type = getResourceType(entry.name)
      const size = (entry as any).transferSize || 0
      const duration = entry.responseEnd - entry.startTime
      
      totalSize += size
      totalDuration += duration
      
      // Group by type
      if (!resourcesByType[type]) {
        resourcesByType[type] = {
          count: 0,
          totalSize: 0,
          averageDuration: 0,
          maxDuration: 0
        }
      }
      
      resourcesByType[type].count++
      resourcesByType[type].totalSize += size
      resourcesByType[type].maxDuration = Math.max(resourcesByType[type].maxDuration, duration)
      
      // Track slowest resources
      slowestResources.push({
        name: entry.name,
        type,
        size,
        duration,
        startTime: entry.startTime
      })
    })
    
    // Calculate averages
    Object.values(resourcesByType).forEach((metrics) => {
      metrics.averageDuration = metrics.count > 0 ? totalDuration / metrics.count : 0
    })
    
    // Sort slowest resources
    slowestResources.sort((a, b) => b.duration - a.duration)
    
    resourceMetrics.value = {
      totalResources: resourceEntries.length,
      totalSize,
      totalDuration,
      resourcesByType,
      slowestResources: slowestResources.slice(0, 10) // Top 10 slowest
    }
    
    logger.debug('Resource metrics measured', resourceMetrics.value)
  }
  
  // ============================================================================
  // MEMORY MONITORING
  // ============================================================================
  
  const trackMemoryMetrics = () => {
    if (!performanceConfig.value.enableMemoryMonitoring || !isSupported.value) return
    
    const memory = (performance as any).memory
    if (!memory) return
    
    const previousUsage = memoryMetrics.value?.usagePercentage || 0
    const currentUsage = (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100
    
    let trend: 'increasing' | 'decreasing' | 'stable' = 'stable'
    if (currentUsage > previousUsage + 5) trend = 'increasing'
    else if (currentUsage < previousUsage - 5) trend = 'decreasing'
    
    memoryMetrics.value = {
      usedJSHeapSize: memory.usedJSHeapSize,
      totalJSHeapSize: memory.totalJSHeapSize,
      jsHeapSizeLimit: memory.jsHeapSizeLimit,
      usagePercentage: currentUsage,
      trend
    }
    
    // Alert on high memory usage
    if (currentUsage > performanceConfig.value.thresholds.memoryUsage.critical) {
      logger.warn('Critical memory usage detected', { usage: currentUsage })
    }
  }
  
  // ============================================================================
  // CUSTOM METRICS
  // ============================================================================
  
  const startTiming = (name: string): string => {
    const markName = `${name}_start`
    performance.mark(markName)
    return markName
  }
  
  const endTiming = (name: string): number => {
    const startMark = `${name}_start`
    const endMark = `${name}_end`
    
    performance.mark(endMark)
    performance.measure(name, startMark, endMark)
    
    const measure = performance.getEntriesByName(name, 'measure')[0]
    const duration = measure.duration
    
    customMetrics.value[name] = duration
    
    // Clean up marks
    performance.clearMarks(startMark)
    performance.clearMarks(endMark)
    performance.clearMeasures(name)
    
    return duration
  }
  
  const recordMetric = (name: string, value: number) => {
    customMetrics.value[name] = value
  }
  
  // ============================================================================
  // UTILITY FUNCTIONS
  // ============================================================================
  
  const getMetricScore = (metric: keyof PerformanceThresholds, value: number): number => {
    const thresholds = performanceConfig.value.thresholds[metric]
    // Type guard to ensure thresholds has 'good' and 'poor'
    if ('good' in thresholds && 'poor' in thresholds) {
      if (value <= thresholds.good) return 100;
      if (value <= thresholds.poor) return 75;
    } else if ('warning' in thresholds && 'critical' in thresholds) {
      // Handle memoryUsage case differently if needed, or adjust scoring
      // For now, this will fall through to 50 or 0 for memoryUsage
    }
    return 50
  }
  
  const getResourceType = (url: string): string => {
    if (url.match(/\.(js|mjs)$/)) return 'script'
    if (url.match(/\.css$/)) return 'stylesheet'
    if (url.match(/\.(png|jpg|jpeg|gif|svg|webp)$/)) return 'image'
    if (url.match(/\.(woff|woff2|ttf|otf)$/)) return 'font'
    if (url.match(/\.(mp4|webm|ogg)$/)) return 'video'
    if (url.match(/\.(mp3|wav|ogg)$/)) return 'audio'
    return 'other'
  }
  
  const getFirstPaint = (): number => {
    const entry = performance.getEntriesByName('first-paint')[0]
    return entry ? entry.startTime : 0
  }
  
  const getFirstContentfulPaint = (): number => {
    const entry = performance.getEntriesByName('first-contentful-paint')[0]
    return entry ? entry.startTime : 0
  }
  
  // ============================================================================
  // REPORTING
  // ============================================================================
  
  const generateReport = (): PerformanceReport => {
    const report: PerformanceReport = {
      timestamp: new Date().toISOString() as ISODateString,
      url: window.location.href,
      userAgent: navigator.userAgent,
      connectionType: (navigator as any).connection?.effectiveType,
      coreWebVitals: { ...coreWebVitals.value },
      navigation: navigationMetrics.value || {} as NavigationMetrics,
      resources: resourceMetrics.value || {} as ResourceMetrics,
      memory: memoryMetrics.value || {} as MemoryMetrics,
      customMetrics: { ...customMetrics.value },
      recommendations: recommendations.value
    }
    
    performanceHistory.value.push(report)
    
    // Keep only last 10 reports
    if (performanceHistory.value.length > 10) {
      performanceHistory.value.shift()
    }
    
    return report
  }
  
  const sendReport = (report: PerformanceReport) => {
    analytics.track('performance_report', {
      score: overallScore.value,
      grade: performanceGrade.value,
      coreWebVitals: report.coreWebVitals,
      recommendations: report.recommendations.length
    })
    
    logger.info('Performance report generated', {
      score: overallScore.value,
      grade: performanceGrade.value
    })
  }
  
  // ============================================================================
  // MONITORING CONTROL
  // ============================================================================
  
  const startMonitoring = () => {
    if (!performanceConfig.value.enabled || !isSupported.value || isMonitoring.value) return
    
    logger.info('Starting performance monitoring')
    
    // Initial measurements
    trackCoreWebVitals()
    trackNavigationTiming()
    trackResourceMetrics()
    trackMemoryMetrics()
    
    // Set up periodic monitoring
    const interval = setInterval(() => {
      trackMemoryMetrics()
      
      const report = generateReport()
      sendReport(report)
    }, performanceConfig.value.reportingInterval)
    
    isMonitoring.value = true
    
    // Cleanup on unmount
    onUnmounted(() => {
      clearInterval(interval)
      isMonitoring.value = false
    })
  }
  
  const stopMonitoring = () => {
    isMonitoring.value = false
    logger.info('Performance monitoring stopped')
  }
  
  // ============================================================================
  // LIFECYCLE
  // ============================================================================
  
  onMounted(() => {
    if (performanceConfig.value.enabled) {
      startMonitoring()
    }
  })
  
  // ============================================================================
  // RETURN INTERFACE
  // ============================================================================
  
  return {
    // Configuration
    config: readonly(performanceConfig),
    isSupported,
    isMonitoring: readonly(isMonitoring),
    
    // Metrics
    coreWebVitals: readonly(coreWebVitals),
    navigationMetrics: readonly(navigationMetrics),
    resourceMetrics: readonly(resourceMetrics),
    memoryMetrics: readonly(memoryMetrics),
    customMetrics: readonly(customMetrics),
    
    // Computed
    overallScore,
    performanceGrade,
    recommendations,
    
    // Methods
    startTiming,
    endTiming,
    recordMetric,
    generateReport,
    startMonitoring,
    stopMonitoring,
    
    // History
    performanceHistory: readonly(performanceHistory)
  }
}
