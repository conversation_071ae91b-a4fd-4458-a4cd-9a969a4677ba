<template>
  <div>
    <AppBreadcrumbs :items="breadcrumbs" />
    <UiCard class="mt-4">
      <template #header>
        <h2 class="text-lg font-semibold">
          Edit Notification Trigger: {{ notificationTriggerStore.selectedTrigger?.name || triggerId }}
        </h2>
      </template>

      <div v-if="isLoadingDetails" class="text-center p-8">
        <UiSpinner />
        <p>Loading trigger details...</p>
      </div>
      <div v-else-if="loadingError" class="mt-4">
        <UiAlert type="error" title="Failed to load trigger details">
          {{ loadingError }}
        </UiAlert>
      </div>
      <NotificationTriggerForm
        v-else-if="notificationTriggerStore.selectedTrigger"
        :is-visible="true"
        :tenant-id="authStore.currentUser?.tenantId || ''"
        :trigger-to-edit="notificationTriggerStore.selectedTrigger"
        @trigger-saved="handleTriggerSaved"
        @close="navigateToTriggersList"
      />
      <div v-else class="mt-4">
        <UiAlert type="warning" title="Trigger not found">
          The requested notification trigger could not be found. It may have been deleted.
        </UiAlert>
      </div>
    </UiCard>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useRoute, useRouter } from '#app';
import AppBreadcrumbs from '~/components/layout/dashboard/AppBreadcrumbs.vue';
import UiCard from '~/components/ui/UiCard.vue';
import UiSpinner from '~/components/ui/UiSpinner.vue';
import UiAlert from '~/components/ui/UiAlert.vue';
import NotificationTriggerForm from '~/components/notifications/NotificationTriggerForm.vue';
import { useNotificationTriggerStore } from '~/stores/notificationTrigger';
import { useAuthStore } from '~/stores/auth';
import { PlatformRoles, TenantRoles } from'~/app/features/auth/constants/roles';

definePageMeta({
  layout: 'dashboard',
  middleware: ['rbac'], // 'auth' is global
  roles: [TenantRoles.TENANT_OWNER, TenantRoles.ADMIN],
  title: 'Edit Notification Trigger',
});

const router = useRouter();
const route = useRoute();
const notificationTriggerStore = useNotificationTriggerStore();
const authStore = useAuthStore();

const triggerId = route.params.id as string;
const isLoadingDetails = ref(false);
const loadingError = ref<string | null>(null);

onMounted(async () => {
  if (triggerId) {
    isLoadingDetails.value = true;
    loadingError.value = null;
    try {
      // Attempt to find in store first
      const existingTrigger = notificationTriggerStore.getTriggerById(triggerId);
      if (existingTrigger) {
        notificationTriggerStore.setSelectedTrigger(existingTrigger);
      } else {
        // If not found, fetch (assuming fetchById action exists or is added)
        // For now, we rely on the main list fetch and setSelectedTrigger
        // This might need a dedicated fetchById action in the store
        // await notificationTriggerStore.fetchNotificationTriggerById(triggerId);
        // For now, if not in list, it means it's not accessible or doesn't exist
        // The form will show "Trigger not found" if selectedTrigger remains null
        // To ensure it's loaded if directly navigated, we should fetch all if not present
        if (notificationTriggerStore.triggers.length === 0) {
            await notificationTriggerStore.fetchNotificationTriggers();
        }
        const found = notificationTriggerStore.getTriggerById(triggerId);
        if (found) {
            notificationTriggerStore.setSelectedTrigger(found);
        } else {
            loadingError.value = `Notification trigger with ID ${triggerId} not found.`;
            notificationTriggerStore.setSelectedTrigger(null);
        }

      }
    } catch (e: any) {
      loadingError.value = e.message || 'An unexpected error occurred while fetching trigger details.';
      notificationTriggerStore.setSelectedTrigger(null);
    } finally {
      isLoadingDetails.value = false;
    }
  } else {
    loadingError.value = 'No trigger ID provided.';
  }
});

const breadcrumbs = computed(() => [
  { label: 'Dashboard', href: '/dashboard' },
  { label: 'Settings', href: '/dashboard/settings' },
  { label: 'Notifications', href: '/dashboard/settings/notifications' },
  { label: 'Triggers', href: '/dashboard/settings/notifications/triggers' },
  { label: `Edit: ${notificationTriggerStore.selectedTrigger?.name || triggerId}`, current: true },
]);

const handleTriggerSaved = () => {
  router.push('/dashboard/settings/notifications/triggers');
};

const navigateToTriggersList = () => {
  router.push('/dashboard/settings/notifications/triggers');
};
</script>