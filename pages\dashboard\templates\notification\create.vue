<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <!-- Header Section -->
    <div class="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="py-6">
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
              <div class="flex items-center justify-center w-12 h-12 bg-orange-100 dark:bg-orange-900 rounded-xl">
                <Icon name="heroicons:bell" class="w-6 h-6 text-orange-600 dark:text-orange-400" />
              </div>
              <div>
                <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Create Notification Template</h1>
                <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                  Design multi-channel notification templates for email, SMS, push, and in-app messaging
                </p>
              </div>
            </div>
            
            <div class="flex items-center space-x-3">
              <UiButton @click="previewTemplate" variant="outline" :disabled="!canPreview">
                <Icon name="heroicons:eye" class="w-4 h-4 mr-2" />
                Preview
              </UiButton>
              <UiButton @click="saveDraft" variant="outline" :disabled="isSaving || !formData.name.trim()">
                <Icon name="heroicons:document-duplicate" class="w-4 h-4 mr-2" />
                Save Draft
              </UiButton>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
        <!-- Main Form -->
        <div class="lg:col-span-3 space-y-6">
          <form @submit.prevent="handleSubmit">
            <!-- Template Information Card -->
            <UiCard class="p-6">
              <div class="flex items-center space-x-3 mb-6">
                <div class="w-8 h-8 bg-orange-100 dark:bg-orange-900 rounded-lg flex items-center justify-center">
                  <Icon name="heroicons:information-circle" class="w-5 h-5 text-orange-600 dark:text-orange-400" />
                </div>
                <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Template Information</h2>
              </div>

              <div class="space-y-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div class="space-y-2">
                    <UiInput
                      id="templateName"
                      v-model="formData.name"
                      label="Template Name"
                      placeholder="e.g., Case Update Notification"
                      required
                      :error="errors.name"
                    />
                    <p class="text-xs text-gray-500 dark:text-gray-400">
                      Choose a descriptive name for easy identification
                    </p>
                  </div>

                  <div class="space-y-2">
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      Notification Category
                    </label>
                    <div class="relative">
                      <select
                        v-model="formData.category"
                        class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                        required
                      >
                        <option value="">Select a category</option>
                        <option
                          v-for="category in notificationCategories"
                          :key="category.id"
                          :value="category.id"
                        >
                          {{ category.name }}
                        </option>
                      </select>
                      <Icon name="heroicons:chevron-down" class="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" />
                    </div>
                  </div>
                </div>

                <div class="space-y-2">
                  <UiTextarea
                    id="templateDescription"
                    v-model="formData.description"
                    label="Description"
                    placeholder="Describe the purpose and usage of this notification template..."
                    :rows="3"
                  />
                </div>

                <!-- Category Details -->
                <div v-if="selectedCategory" class="p-4 bg-orange-50 dark:bg-orange-900/20 rounded-lg border border-orange-200 dark:border-orange-800">
                  <div class="flex items-start space-x-3">
                    <Icon :name="selectedCategory.icon" class="w-5 h-5 text-orange-600 dark:text-orange-400 mt-0.5" />
                    <div>
                      <h3 class="text-sm font-medium text-orange-900 dark:text-orange-100">{{ selectedCategory.name }}</h3>
                      <p class="text-sm text-orange-700 dark:text-orange-300 mt-1">{{ selectedCategory.description }}</p>
                      <div v-if="selectedCategory.metadata?.channels" class="flex flex-wrap gap-2 mt-2">
                        <span
                          v-for="channel in selectedCategory.metadata.channels"
                          :key="channel"
                          class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
                        >
                          <Icon :name="getChannelIcon(channel)" class="w-3 h-3 mr-1" />
                          {{ channel.toUpperCase() }}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </UiCard>

            <!-- Channel Selection -->
            <UiCard class="p-6">
              <div class="flex items-center space-x-3 mb-6">
                <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                  <Icon name="heroicons:signal" class="w-5 h-5 text-blue-600 dark:text-blue-400" />
                </div>
                <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Notification Channels</h2>
              </div>

              <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div
                  v-for="channel in availableChannels"
                  :key="channel.id"
                  class="relative"
                >
                  <label
                    :class="[
                      'flex items-center p-4 border-2 rounded-lg cursor-pointer transition-all duration-200',
                      formData.channels.includes(channel.id)
                        ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                        : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                    ]"
                  >
                    <input
                      type="checkbox"
                      :value="channel.id"
                      v-model="formData.channels"
                      class="sr-only"
                    />
                    <div class="flex items-center space-x-3">
                      <div
                        :class="[
                          'w-10 h-10 rounded-lg flex items-center justify-center',
                          `bg-${channel.color}-100 dark:bg-${channel.color}-900`
                        ]"
                      >
                        <Icon
                          :name="channel.icon"
                          :class="[
                            'w-5 h-5',
                            `text-${channel.color}-600 dark:text-${channel.color}-400`
                          ]"
                        />
                      </div>
                      <div>
                        <h3 class="text-sm font-medium text-gray-900 dark:text-white">{{ channel.name }}</h3>
                        <p class="text-xs text-gray-500 dark:text-gray-400">{{ channel.description }}</p>
                      </div>
                    </div>
                    <div
                      v-if="formData.channels.includes(channel.id)"
                      class="absolute top-2 right-2 w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center"
                    >
                      <Icon name="heroicons:check" class="w-3 h-3 text-white" />
                    </div>
                  </label>
                </div>
              </div>

              <div v-if="formData.channels.length === 0" class="mt-4 p-3 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg">
                <div class="flex items-center space-x-2">
                  <Icon name="heroicons:exclamation-triangle" class="w-4 h-4 text-amber-600 dark:text-amber-400" />
                  <p class="text-sm text-amber-700 dark:text-amber-300">
                    Please select at least one notification channel to continue.
                  </p>
                </div>
              </div>
            </UiCard>

            <!-- Channel Editors -->
            <div v-if="formData.channels.length > 0" class="space-y-6">
              <div
                v-for="channelId in formData.channels"
                :key="channelId"
                class="space-y-4"
              >
                <UiCard class="p-6">
                  <div class="flex items-center justify-between mb-6">
                    <div class="flex items-center space-x-3">
                      <div
                        :class="[
                          'w-8 h-8 rounded-lg flex items-center justify-center',
                          `bg-${getChannelColor(channelId)}-100 dark:bg-${getChannelColor(channelId)}-900`
                        ]"
                      >
                        <Icon
                          :name="getChannelIcon(channelId)"
                          :class="[
                            'w-5 h-5',
                            `text-${getChannelColor(channelId)}-600 dark:text-${getChannelColor(channelId)}-400`
                          ]"
                        />
                      </div>
                      <h2 class="text-lg font-semibold text-gray-900 dark:text-white">
                        {{ getChannelName(channelId) }} Template
                      </h2>
                    </div>
                    
                    <div class="flex items-center space-x-2">
                      <UiButton @click="previewChannel(channelId)" variant="outline" size="sm">
                        <Icon name="heroicons:eye" class="w-4 h-4 mr-1" />
                        Preview
                      </UiButton>
                      <UiButton @click="testChannel(channelId)" variant="outline" size="sm">
                        <Icon name="heroicons:paper-airplane" class="w-4 h-4 mr-1" />
                        Test
                      </UiButton>
                    </div>
                  </div>

                  <NotificationChannelEditor
                    :channel="channelId"
                    v-model:content="formData.channelContent[channelId]"
                    v-model:variables="formData.variables"
                    :template-data="formData"
                    :category="selectedCategory"
                    @content-change="handleChannelContentChange"
                  />
                </UiCard>
              </div>
            </div>

            <!-- Form Actions -->
            <div class="flex items-center justify-between">
              <UiButton @click="$router.back()" variant="outline" type="button">
                <Icon name="heroicons:arrow-left" class="w-4 h-4 mr-2" />
                Cancel
              </UiButton>
              
              <div class="flex items-center space-x-3">
                <UiButton @click="saveDraft" variant="outline" type="button" :disabled="isSaving || !formData.name.trim()">
                  <Icon name="heroicons:document-duplicate" class="w-4 h-4 mr-2" />
                  Save as Draft
                </UiButton>
                <UiButton type="submit" :disabled="isSaving || !isFormValid" class="bg-orange-600 hover:bg-orange-700">
                  <Icon name="heroicons:check" class="w-4 h-4 mr-2" />
                  Create Template
                </UiButton>
              </div>
            </div>
          </form>
        </div>

        <!-- Sidebar -->
        <div class="lg:col-span-1 space-y-6">
          <!-- Template Stats -->
          <UiCard class="p-4">
            <h3 class="text-sm font-medium text-gray-900 dark:text-white mb-3">Template Stats</h3>
            <div class="space-y-3">
              <div class="flex items-center justify-between text-sm">
                <span class="text-gray-600 dark:text-gray-400">Channels</span>
                <span class="font-medium text-gray-900 dark:text-white">{{ formData.channels.length }}</span>
              </div>
              <div class="flex items-center justify-between text-sm">
                <span class="text-gray-600 dark:text-gray-400">Variables</span>
                <span class="font-medium text-gray-900 dark:text-white">{{ formData.variables.length }}</span>
              </div>
              <div class="flex items-center justify-between text-sm">
                <span class="text-gray-600 dark:text-gray-400">Completion</span>
                <span class="font-medium text-gray-900 dark:text-white">{{ completionPercentage }}%</span>
              </div>
            </div>
          </UiCard>

          <!-- Selected Channels -->
          <UiCard class="p-4" v-if="formData.channels.length > 0">
            <h3 class="text-sm font-medium text-gray-900 dark:text-white mb-3">Selected Channels</h3>
            <div class="space-y-2">
              <div
                v-for="channelId in formData.channels"
                :key="channelId"
                class="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-700 rounded-lg"
              >
                <div class="flex items-center space-x-2">
                  <Icon :name="getChannelIcon(channelId)" class="w-4 h-4 text-gray-600 dark:text-gray-400" />
                  <span class="text-sm text-gray-700 dark:text-gray-300">{{ getChannelName(channelId) }}</span>
                </div>
                <UiButton @click="removeChannel(channelId)" variant="ghost" size="sm">
                  <Icon name="heroicons:x-mark" class="w-3 h-3" />
                </UiButton>
              </div>
            </div>
          </UiCard>

          <!-- Variables Panel -->
          <UiCard class="p-4" v-if="formData.variables.length > 0">
            <h3 class="text-sm font-medium text-gray-900 dark:text-white mb-3">Template Variables</h3>
            <div class="space-y-2">
              <div
                v-for="variable in formData.variables"
                :key="variable"
                class="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-700 rounded-lg"
              >
                <span class="text-sm font-mono text-gray-700 dark:text-gray-300">{{ variable }}</span>
                <UiButton @click="removeVariable(variable)" variant="ghost" size="sm">
                  <Icon name="heroicons:x-mark" class="w-3 h-3" />
                </UiButton>
              </div>
            </div>
          </UiCard>

          <!-- Tips & Guidelines -->
          <UiCard class="p-4">
            <h3 class="text-sm font-medium text-gray-900 dark:text-white mb-3">Tips & Guidelines</h3>
            <div class="space-y-3 text-sm text-gray-600 dark:text-gray-400">
              <div class="flex items-start space-x-2">
                <Icon name="heroicons:light-bulb" class="w-4 h-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                <p>Each channel has unique formatting and character limits</p>
              </div>
              <div class="flex items-start space-x-2">
                <Icon name="heroicons:bell" class="w-4 h-4 text-blue-500 mt-0.5 flex-shrink-0" />
                <p>Test notifications before publishing to ensure delivery</p>
              </div>
              <div class="flex items-start space-x-2">
                <Icon name="heroicons:clock" class="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                <p>Consider time zones for scheduled notifications</p>
              </div>
            </div>
          </UiCard>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive, watch, defineAsyncComponent } from 'vue'
import { useRouter } from 'vue-router'
import { useTemplateStore } from '~/stores/template'
import { getTemplateCategoriesByType, getTemplateCategoryById } from '~/utils/templateCategories'
import { NOTIFICATION_CHANNELS } from '~/utils/notificationsChannels'
import { PlatformRoles, TenantRoles } from '~/app/features/auth/constants/roles'

// Lazy load components
const NotificationChannelEditor = defineAsyncComponent(() => import('~/components/template-editors/NotificationChannelEditor.vue'))

// Page meta
definePageMeta({
  layout: 'dashboard',
  title: 'Create Notification Template',
  description: 'Create multi-channel notification templates for email, SMS, push, and in-app messaging',
  pageHeaderIcon: 'heroicons:bell',
  middleware: ['rbac'],
  roles: [PlatformRoles.SUPER_ADMIN, TenantRoles.TENANT_OWNER, TenantRoles.ADMIN, TenantRoles.LAWYER],
  breadcrumb: [
    { label: 'Dashboard', href: '/dashboard' },
    { label: 'Templates', href: '/dashboard/templates' },
    { label: 'Notification Templates', href: '/dashboard/templates/notification' },
    { label: 'Create' },
  ],
})

// Composables
const router = useRouter()
const templateStore = useTemplateStore()

// State
const isSaving = ref(false)
const errors = ref<Record<string, string>>({})

const formData = reactive({
  name: '',
  description: '',
  category: '',
  channels: [] as string[],
  channelContent: {} as Record<string, any>,
  variables: [] as string[],
  type: 'notification',
  isDraft: false,
  metadata: {},
})

// Available notification channels
const availableChannels = [
  {
    id: 'email',
    name: 'Email',
    description: 'Rich HTML email notifications',
    icon: 'heroicons:envelope',
    color: 'blue',
  },
  {
    id: 'sms',
    name: 'SMS',
    description: 'Short text message notifications',
    icon: 'heroicons:device-phone-mobile',
    color: 'green',
  },
  {
    id: 'push',
    name: 'Push Notification',
    description: 'Mobile and browser push notifications',
    icon: 'heroicons:bell',
    color: 'purple',
  },
  {
    id: 'in_app',
    name: 'In-App',
    description: 'Application interface notifications',
    icon: 'heroicons:computer-desktop',
    color: 'indigo',
  },
  {
    id: 'log',
    name: 'System Log',
    description: 'Internal system logging',
    icon: 'heroicons:document-text',
    color: 'gray',
  },
]

// Computed
const notificationCategories = computed(() => getTemplateCategoriesByType('notification'))

const selectedCategory = computed(() =>
  formData.category ? getTemplateCategoryById(formData.category) : null
)

const completionPercentage = computed(() => {
  let score = 0
  if (formData.name.trim()) score += 20
  if (formData.category) score += 20
  if (formData.description.trim()) score += 10
  if (formData.channels.length > 0) score += 25

  // Check if all selected channels have content
  const channelsWithContent = formData.channels.filter(channel =>
    formData.channelContent[channel] &&
    Object.keys(formData.channelContent[channel]).length > 0
  )
  if (channelsWithContent.length === formData.channels.length && formData.channels.length > 0) {
    score += 25
  }

  return score
})

const isFormValid = computed(() =>
  formData.name.trim() &&
  formData.category &&
  formData.channels.length > 0 &&
  formData.channels.every(channel =>
    formData.channelContent[channel] &&
    formData.channelContent[channel].content?.trim()
  ) &&
  !Object.keys(errors.value).length
)

const canPreview = computed(() =>
  formData.name.trim() && formData.channels.length > 0
)

// Watchers
watch(() => formData.name, (newName) => {
  if (newName.trim() && errors.value.name) {
    delete errors.value.name
  }
})

watch(() => formData.channels, (newChannels, oldChannels) => {
  // Initialize content for new channels
  newChannels.forEach(channel => {
    if (!formData.channelContent[channel]) {
      formData.channelContent[channel] = {
        content: '',
        subject: '',
        variables: [],
        settings: {}
      }
    }
  })

  // Remove content for removed channels
  if (oldChannels) {
    oldChannels.forEach(channel => {
      if (!newChannels.includes(channel)) {
        delete formData.channelContent[channel]
      }
    })
  }
}, { deep: true })

watch(() => formData.category, (newCategory) => {
  if (newCategory && selectedCategory.value?.metadata) {
    formData.metadata = { ...selectedCategory.value.metadata }

    // Auto-select recommended channels for this category
    if (selectedCategory.value.metadata.channels) {
      formData.channels = [...selectedCategory.value.metadata.channels]
    }
  }
})

// Methods
const validateForm = () => {
  errors.value = {}

  if (!formData.name.trim()) {
    errors.value.name = 'Template name is required'
  } else if (formData.name.length < 3) {
    errors.value.name = 'Template name must be at least 3 characters'
  }

  if (!formData.category) {
    errors.value.category = 'Please select a category'
  }

  if (formData.channels.length === 0) {
    errors.value.channels = 'Please select at least one notification channel'
  }

  // Validate each channel has content
  formData.channels.forEach(channel => {
    if (!formData.channelContent[channel]?.content?.trim()) {
      errors.value[`channel_${channel}`] = `${getChannelName(channel)} content is required`
    }
  })

  return Object.keys(errors.value).length === 0
}

const handleSubmit = async () => {
  if (!validateForm()) return

  isSaving.value = true
  try {
    const template = await templateStore.createTemplate({
      ...formData,
      isDraft: false,
    })

    if (template) {
      await router.push(`/dashboard/templates/notification/${template.id}`)
    }
  } catch (error) {
    console.error('Error creating template:', error)
  } finally {
    isSaving.value = false
  }
}

const saveDraft = async () => {
  if (!formData.name.trim()) {
    errors.value.name = 'Template name is required to save draft'
    return
  }

  isSaving.value = true
  try {
    const template = await templateStore.createTemplate({
      ...formData,
      isDraft: true,
    })

    if (template) {
      await router.push(`/dashboard/templates/notification/${template.id}/edit`)
    }
  } catch (error) {
    console.error('Error saving draft:', error)
  } finally {
    isSaving.value = false
  }
}

const previewTemplate = () => {
  if (!canPreview.value) return

  const previewData = {
    name: formData.name,
    channels: formData.channels,
    channelContent: formData.channelContent,
    variables: formData.variables,
    category: selectedCategory.value
  }

  sessionStorage.setItem('notificationTemplatePreview', JSON.stringify(previewData))
  window.open('/dashboard/templates/notification/preview', '_blank')
}

const previewChannel = (channelId: string) => {
  const channelData = {
    channel: channelId,
    content: formData.channelContent[channelId],
    variables: formData.variables
  }

  sessionStorage.setItem('channelPreview', JSON.stringify(channelData))
  window.open(`/dashboard/templates/notification/preview/${channelId}`, '_blank')
}

const testChannel = async (channelId: string) => {
  // Implement test notification functionality
  console.log(`Testing ${channelId} channel...`)
}

const removeChannel = (channelId: string) => {
  const index = formData.channels.indexOf(channelId)
  if (index > -1) {
    formData.channels.splice(index, 1)
    delete formData.channelContent[channelId]
  }
}

const removeVariable = (variable: string) => {
  const index = formData.variables.indexOf(variable)
  if (index > -1) {
    formData.variables.splice(index, 1)
  }
}

const handleChannelContentChange = (channelId: string, content: any, variables: string[]) => {
  formData.channelContent[channelId] = content

  // Merge variables from all channels
  const allVariables = new Set(formData.variables)
  variables.forEach(variable => allVariables.add(variable))
  formData.variables = Array.from(allVariables)
}

// Helper functions
const getChannelName = (channelId: string) => {
  return availableChannels.find(channel => channel.id === channelId)?.name || channelId
}

const getChannelIcon = (channelId: string) => {
  return availableChannels.find(channel => channel.id === channelId)?.icon || 'heroicons:bell'
}

const getChannelColor = (channelId: string) => {
  return availableChannels.find(channel => channel.id === channelId)?.color || 'gray'
}
</script>
