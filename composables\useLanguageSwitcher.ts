import { computed, watch, nextTick } from 'vue'
import { useI18n } from 'vue-i18n'
import { useHead } from '#imports'
import {
  isRTLLanguage,
  getDirection,
  updateHtmlAttributes as updateHtmlAttrs,
  saveLanguagePreference
} from '../utils/languageManager.js'

export const useLanguageSwitcher = () => {
  const { locale } = useI18n()
  const { $i18n } = useNuxtApp()

  // Computed properties
  const isRTL = computed(() => isRTLLanguage(locale.value))
  const currentDirection = computed(() => getDirection(locale.value))

  // Update HTML attributes and styles
  const updateHtmlAttributes = () => {
    if (typeof window !== 'undefined') {
      updateHtmlAttrs(locale.value)
    }
  }

  // Switch language function
  const switchLanguage = async (newLocale: string) => {
    try {
      console.log(`Switching language from ${locale.value} to ${newLocale}`)

      // Set the new locale using Nuxt's i18n
      await $i18n.setLocale(newLocale)

      // Wait for next tick to ensure reactivity
      await nextTick()

      // Force update HTML attributes after locale change
      setTimeout(() => {
        updateHtmlAttributes()
      }, 50)

      // Store in localStorage for persistence
      if (typeof window !== 'undefined') {
        saveLanguagePreference(newLocale)

        // Also update cookie for SSR
        const cookie = useCookie('i18n_redirected', {
          default: () => newLocale,
          maxAge: 60 * 60 * 24 * 365 // 1 year
        })
        cookie.value = newLocale
      }

      console.log(`Language switched successfully to: ${newLocale}`)
      return true
    } catch (error) {
      console.error('Error switching language:', error)
      return false
    }
  }

  // Initialize on mount
  const initializeLanguage = () => {
    if (typeof window !== 'undefined') {
      // Get stored language preference
      const storedLanguage = localStorage.getItem('preferred-language')

      if (storedLanguage && storedLanguage !== locale.value) {
        switchLanguage(storedLanguage)
      } else {
        updateHtmlAttributes()
      }
    }
  }

  // Watch for locale changes
  watch(locale, () => {
    updateHtmlAttributes()
  }, { immediate: true })

  // Update head meta for SEO
  useHead({
    htmlAttrs: {
      lang: locale,
      dir: currentDirection
    },
    meta: [
      {
        name: 'language',
        content: locale
      }
    ]
  })

  return {
    locale,
    isRTL,
    currentDirection,
    switchLanguage,
    initializeLanguage,
    updateHtmlAttributes
  }
}
