/**
 * Date Formatters Composable
 *
 * Provides reactive date formatting utilities for Vue components
 * This composable integrates with the global date formatters and Day.js
 */

import { computed, ref, type Ref } from 'vue'
import { dateFormatters, DATE_FORMATS, type DateInput } from '~/utils/dateFormatters'

export interface DateFormatterOptions {
  timezone?: string
  locale?: string
  format?: string
}

export interface UseDateFormattersReturn {
  // Core formatting functions
  formatDate: (date: DateInput, format?: string, timezone?: string) => string
  formatDateDisplay: (date: DateInput) => string
  formatDateFull: (date: DateInput) => string
  formatDateTime: (date: DateInput) => string
  formatDateTimeFull: (date: DateInput) => string
  formatTime: (date: DateInput) => string
  formatTime24: (date: DateInput) => string
  formatRelativeTime: (date: DateInput) => string
  formatRelativeTimeLong: (date: DateInput) => string
  formatLegalDate: (date: DateInput) => string
  formatLegalDateTime: (date: DateInput) => string
  formatFileTimestamp: (date: DateInput) => string
  formatLogTimestamp: (date: DateInput) => string
  formatDateWithTimezone: (date: DateInput, format?: string, timezone?: string) => string
  formatDuration: (startDate: DateInput, endDate?: DateInput) => string
  formatAge: (birthDate: DateInput) => string
  
  // Utility functions
  isValidDate: (date: DateInput) => boolean
  
  // Reactive formatting
  formatReactive: (date: Ref<DateInput>, format?: string) => Ref<string>
  formatDateTimeReactive: (date: Ref<DateInput>) => Ref<string>
  formatRelativeTimeReactive: (date: Ref<DateInput>) => Ref<string>
  
  // Constants
  DATE_FORMATS: typeof DATE_FORMATS
  
  // Configuration
  setDefaultTimezone: (timezone: string) => void
  getDefaultTimezone: () => string
}

/**
 * Main composable function
 */
export const useDateFormatters = (options: DateFormatterOptions = {}): UseDateFormattersReturn => {
  // Default configuration
  const defaultTimezone = ref(options.timezone || 'America/New_York')
  const defaultLocale = ref(options.locale || 'en')
  const defaultFormat = ref(options.format || DATE_FORMATS.DATE_DISPLAY)
  
  // Core formatting functions (wrapped for reactivity)
  const formatDate = (date: DateInput, format?: string, timezone?: string): string => {
    return dateFormatters.formatDate(
      date,
      format || defaultFormat.value,
      timezone || defaultTimezone.value
    )
  }
  
  const formatDateDisplay = (date: DateInput): string => {
    return dateFormatters.formatDateDisplay(date)
  }
  
  const formatDateFull = (date: DateInput): string => {
    return dateFormatters.formatDateFull(date)
  }
  
  const formatDateTime = (date: DateInput): string => {
    return dateFormatters.formatDateTime(date)
  }
  
  const formatDateTimeFull = (date: DateInput): string => {
    return dateFormatters.formatDateTimeFull(date)
  }
  
  const formatTime = (date: DateInput): string => {
    return dateFormatters.formatTime(date)
  }
  
  const formatTime24 = (date: DateInput): string => {
    return dateFormatters.formatTime24(date)
  }
  
  const formatRelativeTime = (date: DateInput): string => {
    return dateFormatters.formatRelativeTime(date)
  }
  
  const formatRelativeTimeLong = (date: DateInput): string => {
    return dateFormatters.formatRelativeTimeLong(date)
  }
  
  const formatLegalDate = (date: DateInput): string => {
    return dateFormatters.formatLegalDate(date)
  }
  
  const formatLegalDateTime = (date: DateInput): string => {
    return dateFormatters.formatLegalDateTime(date)
  }
  
  const formatFileTimestamp = (date: DateInput): string => {
    return dateFormatters.formatFileTimestamp(date)
  }
  
  const formatLogTimestamp = (date: DateInput): string => {
    return dateFormatters.formatLogTimestamp(date)
  }
  
  const formatDateWithTimezone = (
    date: DateInput,
    format?: string,
    timezone?: string
  ): string => {
    return dateFormatters.formatDateWithTimezone(
      date,
      format || defaultFormat.value,
      timezone || defaultTimezone.value
    )
  }
  
  const formatDuration = (startDate: DateInput, endDate?: DateInput): string => {
    return dateFormatters.formatDuration(startDate, endDate)
  }
  
  const formatAge = (birthDate: DateInput): string => {
    return dateFormatters.formatAge(birthDate)
  }
  
  const isValidDate = (date: DateInput): boolean => {
    return dateFormatters.isValidDate(date)
  }
  
  // Reactive formatting functions
  const formatReactive = (date: Ref<DateInput>, format?: string): Ref<string> => {
    return computed(() => formatDate(date.value, format))
  }
  
  const formatDateTimeReactive = (date: Ref<DateInput>): Ref<string> => {
    return computed(() => formatDateTime(date.value))
  }
  
  const formatRelativeTimeReactive = (date: Ref<DateInput>): Ref<string> => {
    return computed(() => formatRelativeTime(date.value))
  }
  
  // Configuration functions
  const setDefaultTimezone = (timezone: string): void => {
    defaultTimezone.value = timezone
  }
  
  const getDefaultTimezone = (): string => {
    return defaultTimezone.value
  }
  
  return {
    // Core formatting functions
    formatDate,
    formatDateDisplay,
    formatDateFull,
    formatDateTime,
    formatDateTimeFull,
    formatTime,
    formatTime24,
    formatRelativeTime,
    formatRelativeTimeLong,
    formatLegalDate,
    formatLegalDateTime,
    formatFileTimestamp,
    formatLogTimestamp,
    formatDateWithTimezone,
    formatDuration,
    formatAge,
    
    // Utility functions
    isValidDate,
    
    // Reactive formatting
    formatReactive,
    formatDateTimeReactive,
    formatRelativeTimeReactive,
    
    // Constants
    DATE_FORMATS,
    
    // Configuration
    setDefaultTimezone,
    getDefaultTimezone
  }
}

/**
 * Global date formatters composable (singleton pattern)
 */
let globalDateFormatters: UseDateFormattersReturn | null = null

export const useGlobalDateFormatters = (): UseDateFormattersReturn => {
  if (!globalDateFormatters) {
    globalDateFormatters = useDateFormatters()
  }
  return globalDateFormatters
}

/**
 * Shorthand composables for common use cases
 */

// For displaying dates in lists/tables
export const useDisplayDateFormatters = () => {
  const { formatDateDisplay, formatDateTime, formatRelativeTime } = useDateFormatters()
  return { formatDateDisplay, formatDateTime, formatRelativeTime }
}

// For legal documents
export const useLegalDateFormatters = () => {
  const { formatLegalDate, formatLegalDateTime } = useDateFormatters()
  return { formatLegalDate, formatLegalDateTime }
}

// For system/logging
export const useSystemDateFormatters = () => {
  const { formatFileTimestamp, formatLogTimestamp } = useDateFormatters()
  return { formatFileTimestamp, formatLogTimestamp }
}

// Export default
export default useDateFormatters
