<template>
  <div class="space-y-6">
    <!-- <PERSON> Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
          Global Search
        </h1>
        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
          Search across all your legal practice data
        </p>
      </div>
      
      <!-- Quick Actions -->
      <div class="flex items-center gap-3">
        <UiButton
          v-if="searchState.hasSearched && searchState.results.length > 0"
          variant="outline"
          @click="exportResults"
        >
          <Icon name="heroicons:arrow-down-tray" class="h-4 w-4 mr-2" />
          Export
        </UiButton>
        
        <UiButton
          variant="outline"
          @click="showSearchTips = true"
        >
          <Icon name="heroicons:question-mark-circle" class="h-4 w-4 mr-2" />
          Search Tips
        </UiButton>
      </div>
    </div>

    <!-- Search Filters -->
    <GlobalSearchFilters
      v-model="searchQuery"
      :selected-types="searchState.selectedTypes"
      :filters="searchState.filters"
      :sort-by="searchState.sortBy"
      :total-by-type="searchState.totalByType"
      :suggestions="searchState.suggestions"
      :is-searching="isSearching"
      @search="handleSearch"
      @type-change="handleTypeChange"
      @filter-change="handleFilterChange"
      @sort-change="handleSortChange"
      @clear-filters="handleClearFilters"
    />

    <!-- Search Results -->
    <SearchResultsList
      :results="searchState.results"
      :loading="isSearching"
      :loading-more="loadingMore"
      :total="searchState.total"
      :total-by-type="searchState.totalByType"
      :selected-types="searchState.selectedTypes"
      :view-mode="searchState.viewMode"
      :processing-time="processingTime"
      :highlight-query="searchQuery"
      :has-searched="searchState.hasSearched"
      :has-more="hasMoreResults"
      :show-view-toggle="true"
      :show-type-filter="false"
      :compact-mode="compactMode"
      @result-click="handleResultClick"
      @result-action="handleResultAction"
      @view-change="handleViewChange"
      @type-change="handleTypeChange"
      @clear-search="handleClearSearch"
      @modify-search="focusSearchInput"
      @load-more="loadMoreResults"
    />

    <!-- Search Tips Modal -->
    <UiModal
      v-model="showSearchTips"
      title="Search Tips & Tricks"
      size="lg"
    >
      <div class="space-y-6">
        <div>
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-3">
            Basic Search
          </h3>
          <ul class="space-y-2 text-sm text-gray-600 dark:text-gray-400">
            <li class="flex items-start gap-2">
              <Icon name="heroicons:check-circle" class="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
              <span>Enter keywords to search across all content types</span>
            </li>
            <li class="flex items-start gap-2">
              <Icon name="heroicons:check-circle" class="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
              <span>Use quotes for exact phrases: "contract review"</span>
            </li>
            <li class="flex items-start gap-2">
              <Icon name="heroicons:check-circle" class="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
              <span>Use wildcards: contract* finds contract, contracts, contracting</span>
            </li>
          </ul>
        </div>

        <div>
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-3">
            Advanced Search
          </h3>
          <ul class="space-y-2 text-sm text-gray-600 dark:text-gray-400">
            <li class="flex items-start gap-2">
              <Icon name="heroicons:check-circle" class="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
              <span>Use AND/OR operators: contract AND review</span>
            </li>
            <li class="flex items-start gap-2">
              <Icon name="heroicons:check-circle" class="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
              <span>Exclude terms with minus: contract -template</span>
            </li>
            <li class="flex items-start gap-2">
              <Icon name="heroicons:check-circle" class="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
              <span>Search specific fields: title:contract OR client:acme</span>
            </li>
          </ul>
        </div>

        <div>
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-3">
            Filters & Types
          </h3>
          <ul class="space-y-2 text-sm text-gray-600 dark:text-gray-400">
            <li class="flex items-start gap-2">
              <Icon name="heroicons:check-circle" class="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
              <span>Select specific content types to narrow your search</span>
            </li>
            <li class="flex items-start gap-2">
              <Icon name="heroicons:check-circle" class="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
              <span>Use date ranges to find recent or historical content</span>
            </li>
            <li class="flex items-start gap-2">
              <Icon name="heroicons:check-circle" class="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
              <span>Filter by status, priority, or other metadata</span>
            </li>
          </ul>
        </div>
      </div>

      <template #footer>
        <div class="flex justify-end">
          <UiButton @click="showSearchTips = false">
            Got it
          </UiButton>
        </div>
      </template>
    </UiModal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { useGlobalSearch } from '~/app/shared/composables/core/useGlobalSearch'
import { useToast } from '~/app/shared/composables/ui/useToast'
import type { GlobalSearchResultItem, SearchEntityType } from '~/app/shared/types/search'

// Page metadata
definePageMeta({
  layout: 'dashboard',
  title: 'Global Search',
  description: 'Search across all your legal practice data',
  icon: 'heroicons:magnifying-glass',
  breadcrumbs: [
    { label: 'Dashboard', to: '/dashboard' },
    { label: 'Search', to: '/dashboard/search' }
  ]
})

// Composables
const router = useRouter()
const { showToast } = useToast()

// Search composable
const {
  state: searchState,
  isSearching,
  hasResults,
  isEmpty,
  resultsByType,
  search,
  clearSearch,
  setQuery,
  setTypes,
  setFilters,
  setSortBy,
  setViewMode,
  getResultUrl
} = useGlobalSearch({
  initialQuery: '',
  initialTypes: ['all'],
  autoSearch: false,
  persistState: true
})

// Local state
const searchQuery = ref('')
const showSearchTips = ref(false)
const loadingMore = ref(false)
const compactMode = ref(false)
const processingTime = ref<number>()

// Computed properties
const hasMoreResults = computed(() => {
  // This would typically come from the API response
  return searchState.value.results.length < searchState.value.total && searchState.value.results.length >= 20
})

// Methods
async function handleSearch(query?: string) {
  const searchTerm = query || searchQuery.value
  if (!searchTerm.trim()) {
    showToast({
      type: 'warning',
      title: 'Search Required',
      message: 'Please enter a search term to continue.'
    })
    return
  }

  const startTime = Date.now()
  
  try {
    setQuery(searchTerm)
    await search(searchTerm)
    processingTime.value = Date.now() - startTime
    
    if (searchState.value.results.length === 0) {
      showToast({
        type: 'info',
        title: 'No Results',
        message: 'No results found for your search. Try different keywords or adjust your filters.'
      })
    }
  } catch (error) {
    console.error('Search error:', error)
    showToast({
      type: 'error',
      title: 'Search Error',
      message: 'An error occurred while searching. Please try again.'
    })
  }
}

function handleTypeChange(types: SearchEntityType[]) {
  setTypes(types)
  if (searchState.value.hasSearched) {
    search()
  }
}

function handleFilterChange(key: string, value: any) {
  const newFilters = { ...searchState.value.filters }
  if (value === '' || value === null || value === undefined) {
    delete newFilters[key]
  } else {
    newFilters[key] = value
  }
  setFilters(newFilters)
  
  if (searchState.value.hasSearched) {
    search()
  }
}

function handleSortChange(sortBy: string) {
  setSortBy(sortBy)
  if (searchState.value.hasSearched) {
    search()
  }
}

function handleViewChange(viewMode: 'list' | 'grid' | 'table') {
  setViewMode(viewMode)
}

function handleClearFilters() {
  searchQuery.value = ''
  setQuery('')
  setFilters({})
  clearSearch()
}

function handleClearSearch() {
  searchQuery.value = ''
  setQuery('')
  clearSearch()
}

function handleResultClick(result: GlobalSearchResultItem) {
  const url = getResultUrl(result)
  if (url.startsWith('http')) {
    window.open(url, '_blank')
  } else {
    router.push(url)
  }
}

function handleResultAction(action: string, result: GlobalSearchResultItem) {
  switch (action) {
    case 'favorite':
      // Implement favorite functionality
      showToast({
        type: 'success',
        title: 'Added to Favorites',
        message: `${result.title} has been added to your favorites.`
      })
      break
    case 'share':
      // Implement share functionality
      if (navigator.share) {
        navigator.share({
          title: result.title,
          text: result.description,
          url: getResultUrl(result)
        })
      } else {
        // Fallback to clipboard
        navigator.clipboard.writeText(getResultUrl(result))
        showToast({
          type: 'success',
          title: 'Link Copied',
          message: 'The link has been copied to your clipboard.'
        })
      }
      break
  }
}

async function loadMoreResults() {
  if (loadingMore.value) return
  
  loadingMore.value = true
  try {
    // This would typically load more results from the API
    await new Promise(resolve => setTimeout(resolve, 1000)) // Simulate API call
    showToast({
      type: 'info',
      title: 'Load More',
      message: 'This feature will be implemented with pagination support.'
    })
  } catch (error) {
    showToast({
      type: 'error',
      title: 'Error',
      message: 'Failed to load more results.'
    })
  } finally {
    loadingMore.value = false
  }
}

function exportResults() {
  // Implement export functionality
  showToast({
    type: 'info',
    title: 'Export',
    message: 'Export functionality will be implemented soon.'
  })
}

function focusSearchInput() {
  nextTick(() => {
    const searchInput = document.querySelector('#global-search') as HTMLInputElement
    if (searchInput) {
      searchInput.focus()
    }
  })
}

// Initialize search query from composable state
onMounted(() => {
  searchQuery.value = searchState.value.query

  // Auto-search if there's a query from URL or persisted state
  if (searchQuery.value.trim()) {
    handleSearch(searchQuery.value)
  }
})

// Keyboard shortcuts
onMounted(() => {
  const handleKeydown = (event: KeyboardEvent) => {
    // Ctrl/Cmd + K to focus search
    if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
      event.preventDefault()
      focusSearchInput()
    }

    // Escape to clear search
    if (event.key === 'Escape' && searchQuery.value) {
      handleClearSearch()
    }
  }

  document.addEventListener('keydown', handleKeydown)

  // Cleanup
  onUnmounted(() => {
    document.removeEventListener('keydown', handleKeydown)
  })
})
</script>
