<template>
  <div class="space-y-4">
    <!-- Section Header -->
    <div v-if="title || description || $slots.header" class="pb-4 border-b border-gray-100">
      <slot name="header">
        <div class="flex items-start justify-between">
          <div>
            <h4 class="text-base font-semibold text-gray-900">{{ title }}</h4>
            <p v-if="description" class="mt-1 text-sm text-gray-500">{{ description }}</p>
          </div>
          
          <!-- Section Actions -->
          <div v-if="$slots.actions" class="flex items-center gap-2">
            <slot name="actions" />
          </div>
        </div>
      </slot>
    </div>

    <!-- Section Content -->
    <div class="space-y-4">
      <slot />
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  title?: string;
  description?: string;
}

defineProps<Props>();
</script>
