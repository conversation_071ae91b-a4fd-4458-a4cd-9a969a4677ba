import { PlatformRoles, TenantRoles } from "~/app/features/auth/constants/roles";


export default
  {
    "menuItems": [
      {
        "id": "dashboard",
        "label": "Dashboard",
        "icon": "heroicons:home",
        "path": "/dashboard",
        "roles": [],
        "order": 1
      },
      {
        "id": "global-search",
        "label": "Global Search",
        "icon": "heroicons:magnifying-glass",
        "path": "/dashboard/search",
        "roles": [],
        "tenantRoles": [],
        "order": 2,
        "description": "Search across all your legal practice data"
      },
      {
        "id": "tenant-management",
        "label": "Tenant Management",
        "icon": "heroicons:building-office-2",
        "roles": [PlatformRoles.SUPER_ADMIN],
        "order": 3,
        "children": [
          {
            "id": "tenants-overview",
            "label": "Tenant Overview",
            "icon": "heroicons:chart-pie",
            "path": "/dashboard/tenants",
            "order": 1
          },
          {
            "id": "tenants-manage",
            "label": "Manage Tenants",
            "icon": "heroicons:user-group",
            "path": "/dashboard/tenants/manage",

            "order": 2
          },
          {
            "id": "tenants-create",
            "label": "New Tenant",
            "icon": "heroicons:plus-circle",
            "path": "/dashboard/tenants/create",
            "order": 3
          },

          {
            "id": "tenant-reports",
            "label": "Reports",
            "icon": "heroicons:chart-bar",
            "path": "/dashboard/tenants/reports",
            "tenantRoles": [],
            "order": 4
          }
        ]
      },
      {
        "id": "users-management",
        "label": "Users Management",
        "icon": "heroicons:users",
        "roles": [PlatformRoles.SUPER_ADMIN],
        "order": 4,
        "children": [
          {
            "id": "uesrs-overview",
            "label": "Users Overview",
            "icon": "heroicons:user-group",
            "path": "/dashboard/users",
            "order": 1
          },
          {
            "id": "users-manage",
            "label": "Manage Users",
            "icon": "heroicons:user-group",
            "path": "/dashboard/users/manage",

            "order": 2
          },
          {
            "id": "users-invite",
            "label": "Invite a User",
            "icon": "heroicons:plus-circle",
            "path": "/dashboard/users/invite",
            "order": 3
          },

          {
            "id": "users-analytics",
            "label": "Users Analytics",
            "icon": "heroicons:chart-bar",
            "path": "/dashboard/users/analytics",
            "tenantRoles": [],
            "order": 4
          },
          {
            "id": "users-roles",
            "label": "Users Roles",
            "icon": "heroicons:shield-check",
            "path": "/dashboard/users/roles",
            "tenantRoles": [],
            "order": 5
          }
        ]
      },
      {
        "id": "case-management",
        "label": "Case Management",
        "icon": "heroicons:briefcase",
        "roles": [TenantRoles.LAWYER, TenantRoles.ADMIN, PlatformRoles.SUPER_ADMIN],
        "tenantRoles": [TenantRoles.LAWYER, TenantRoles.ADMIN, TenantRoles.TENANT_OWNER],
        "requiresTenant": true,
        "order": 5,
        "children": [
          {
            "id": "cases-list",
            "label": "All Cases",
            "icon": "heroicons:queue-list",
            "path": "/dashboard/cases",
            "badge": {
              "text": "24",
              "type": "info"
            },
            "order": 1
          },
          {
            "id": "cases-create",
            "label": "New Case",
            "icon": "heroicons:plus-circle",
            "path": "/dashboard/cases/create",
            "order": 2
          },
          {
            "id": "case-types",
            "label": "Case Types",
            "icon": "heroicons:tag",
            "path": "/dashboard/cases/types",
            "tenantRoles": [TenantRoles.ADMIN, TenantRoles.TENANT_OWNER],
            "order": 3
          },
          {
            "id": "case-reports",
            "label": "Reports",
            "icon": "heroicons:chart-bar",
            "path": "/dashboard/cases/reports",
            "tenantRoles": [TenantRoles.ADMIN, TenantRoles.TENANT_OWNER],
            "order": 4
          }
        ]
      },
      {
        "id": "documents",
        "label": "Documents",
        "icon": "heroicons:document-text",
        "roles": [TenantRoles.LAWYER, TenantRoles.ADMIN, PlatformRoles.SUPER_ADMIN],
        "tenantRoles": [TenantRoles.LAWYER, TenantRoles.ADMIN, TenantRoles.TENANT_OWNER],
        "requiresTenant": true,
        "order": 6,
        "children": [
          {
            "id": "documents-list",
            "label": "All Documents",
            "icon": "heroicons:folder",
            "path": "/dashboard/documents",
            "badge": {
              "text": "156",
              "type": "success"
            },
            "order": 1
          },
          {
            "id": "documents-upload",
            "label": "Upload Document",
            "icon": "heroicons:arrow-up-tray",
            "path": "/dashboard/documents/upload",
            "order": 2
          },
          {
            "id": "document-categories",
            "label": "Categories",
            "icon": "heroicons:rectangle-stack",
            "path": "/dashboard/documents/categories",
            "tenantRoles": [TenantRoles.ADMIN, TenantRoles.TENANT_OWNER],
            "order": 3
          },
          {
            "id": "document-search",
            "label": "Advanced Search",
            "icon": "heroicons:magnifying-glass",
            "path": "/dashboard/documents/search",
            "order": 4
          }
        ]
      },
      {
        "id": "templates",
        "label": "Templates",
        "icon": "heroicons:clipboard-document-list",
        "roles": [PlatformRoles.SUPER_ADMIN],
        "tenantRoles": [],
        "order": 7,
        "children": [
          {
            "id": "templates-list",
            "label": "All Templates",
            "icon": "heroicons:document-duplicate",
            "path": "/dashboard/templates",
            "badge": {
              "text": "8",
              "type": "warning"
            },
            "order": 1
          },
          {
            "id": "templates-create",
            "label": "Create Template",
            "icon": "heroicons:plus",
            "path": "/dashboard/templates/create",
            "order": 2
          },
          {
            "id": "email-templates",
            "label": "Email Templates",
            "icon": "heroicons:envelope",
            "order": 3,
            "children": [
              {
                "id": "email-editor",
                "label": "Email Editor",
                "icon": "heroicons:pencil-square",
                "path": "/dashboard/templates/email/editor",
                "badge": {
                  "text": "New",
                  "type": "success"
                },
                "order": 1
              },
              {
                "id": "email-demo",
                "label": "Demo & Examples",
                "icon": "heroicons:play",
                "path": "/dashboard/templates/email/demo",
                "order": 2
              }
            ]
          },
          {
            "id": "template-categories",
            "label": "Template Categories",
            "icon": "heroicons:squares-2x2",
            "path": "/dashboard/templates/categories",
            "order": 4
          }
        ]
      },
     
      {
        "id": PlatformRoles.SUPPORT,
        "label": "Support",
        "icon": "heroicons:question-mark-circle",
        "roles": [TenantRoles.CLIENT, TenantRoles.ADMIN, PlatformRoles.SUPER_ADMIN, PlatformRoles.SUPPORT],
        "order": 8,
        "children": [
          {
            "id": "help-center",
            "label": "Help Center",
            "icon": "heroicons:book-open",
            "path": "/dashboard/support/help",
            "order": 1
          },
          {
            "id": "contact-support",
            "label": "Contact Support",
            "icon": "heroicons:chat-bubble-left-right",
            "path": "/dashboard/support/contact",
            "order": 2
          },
          {
            "id": "feature-requests",
            "label": "Feature Requests",
            "icon": "heroicons:light-bulb",
            "path": "/dashboard/support/features",
            "order": 3
          }
        ]
      }
    ],
    "roleHierarchy": {
      [PlatformRoles.SUPER_ADMIN]: [PlatformRoles.SUPPORT],
      // [TenantRoles.ADMIN]: [TenantRoles.LAWYER, TenantRoles.CLIENT],
      // [PlatformRoles.SUPPORT]: [TenantRoles.CLIENT],
      [PlatformRoles.SUPPORT]: [],
      // [TenantRoles.LAWYER]: [TenantRoles.CLIENT],
      // [TenantRoles.CLIENT]: []
    },
    "tenantRoleHierarchy": {
      [TenantRoles.TENANT_OWNER]: [TenantRoles.ADMIN, TenantRoles.LAWYER, TenantRoles.PARALEGAL, TenantRoles.CLIENT],
      [TenantRoles.ADMIN]: [TenantRoles.LAWYER, TenantRoles.PARALEGAL, TenantRoles.CLIENT],
      [TenantRoles.LAWYER]: [TenantRoles.CLIENT],
      [TenantRoles.PARALEGAL]: [TenantRoles.CLIENT],
      [TenantRoles.CLIENT]: []
    }
  }
