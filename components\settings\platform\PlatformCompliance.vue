<template>
  <div class="space-y-6">
    <div>
      <h2 class="text-md font-semibold text-gray-800 dark:text-gray-100 mb-4">Toggle compliance features, set data retention policies, and select default jurisdiction.</h2>
    </div>

    <UiCard title="Data Retention">
      <div class="space-y-4">
        <div>
          <label for="retentionPeriod" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Retention Period (days)</label>
          <UiInput id="retentionPeriod" name="retentionPeriod" v-model="retentionPeriod" type="number" placeholder="e.g., 365" class="mt-1" />
        </div>
        <div>
          <UiToggle v-model="enableAutoDeletion" label="Enable Auto-Deletion" description="Automatically delete data after the retention period." />
        </div>
      </div>
    </UiCard>

  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import UiInput from '~/components/ui/UiInput.vue';
import UiToggle from '~/components/ui/UiToggle.vue';
import UiCard from '~/components/ui/UiCard.vue';

const retentionPeriod = ref<number | null>(null);
const enableAutoDeletion = ref(false);

// TODO: Load initial values and implement save logic
</script>
