# API Documentation

## 🌐 Overview

The Legal SaaS Frontend integrates with a comprehensive REST API that provides all backend functionality for case management, document handling, user authentication, and billing operations.

## 🔗 Base Configuration

### API Base URL
```typescript
// Environment-based configuration
const API_CONFIG = {
  development: 'http://localhost:3001/api/v1',
  staging: 'https://api-staging.legalsaas.com/v1',
  production: 'https://api.legalsaas.com/v1'
}
```

### Authentication
All API requests require authentication via JWT tokens:

```typescript
// Request headers
{
  'Authorization': 'Bearer <access_token>',
  'Content-Type': 'application/json',
  'X-Client-Version': '1.0.0'
}
```

## 🔐 Authentication Endpoints

### POST /auth/login
Authenticate user with email and password.

**Request:**
```typescript
interface LoginRequest {
  email: string
  password: string
  rememberMe?: boolean
  deviceInfo?: {
    userAgent: string
    ipAddress: string
    deviceType: 'desktop' | 'mobile' | 'tablet'
  }
}
```

**Response:**
```typescript
interface LoginResponse {
  success: boolean
  data: {
    user: User
    session: AuthSession
    tokens: {
      accessToken: string
      refreshToken: string
      expiresAt: string
      refreshExpiresAt: string
    }
  }
  message: string
}
```

**Example:**
```typescript
const response = await api.post('/auth/login', {
  email: '<EMAIL>',
  password: 'securePassword123',
  rememberMe: true
})
```

### POST /auth/refresh
Refresh access token using refresh token.

**Request:**
```typescript
interface RefreshRequest {
  refreshToken: string
}
```

**Response:**
```typescript
interface RefreshResponse {
  success: boolean
  data: {
    accessToken: string
    expiresAt: string
  }
}
```

### POST /auth/logout
Logout user and invalidate tokens.

**Request:**
```typescript
interface LogoutRequest {
  refreshToken?: string
  allDevices?: boolean
}
```

## 👤 User Management Endpoints

### GET /users/profile
Get current user profile.

**Response:**
```typescript
interface UserProfileResponse {
  success: boolean
  data: User
}
```

### PUT /users/profile
Update user profile.

**Request:**
```typescript
interface UpdateProfileRequest {
  name?: {
    first: string
    last: string
  }
  phone?: PhoneNumber
  address?: Address
  preferences?: UserPreferences
}
```

### POST /users/change-password
Change user password.

**Request:**
```typescript
interface ChangePasswordRequest {
  currentPassword: string
  newPassword: string
}
```

## 📋 Case Management Endpoints

### GET /cases
List cases with filtering and pagination.

**Query Parameters:**
```typescript
interface CaseListParams {
  page?: number
  limit?: number
  status?: CaseStatus[]
  priority?: CasePriority[]
  type?: CaseType[]
  clientId?: string
  assignedTo?: string
  search?: string
  sortBy?: 'createdAt' | 'updatedAt' | 'dueDate' | 'priority'
  sortOrder?: 'asc' | 'desc'
  dateFrom?: string
  dateTo?: string
}
```

**Response:**
```typescript
interface CaseListResponse {
  success: boolean
  data: Case[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
}
```

**Example:**
```typescript
const cases = await api.get('/cases', {
  params: {
    status: ['open', 'in_progress'],
    limit: 20,
    sortBy: 'priority',
    sortOrder: 'desc'
  }
})
```

### POST /cases
Create a new case.

**Request:**
```typescript
interface CreateCaseRequest {
  title: string
  description?: string
  type: CaseType
  priority: CasePriority
  clientId: string
  assignedTo?: string[]
  dueDate?: string
  tags?: string[]
  customFields?: Record<string, any>
}
```

**Response:**
```typescript
interface CreateCaseResponse {
  success: boolean
  data: Case
  message: string
}
```

### GET /cases/:id
Get case details by ID.

**Response:**
```typescript
interface CaseDetailResponse {
  success: boolean
  data: Case & {
    participants: CaseParticipant[]
    documents: CaseDocument[]
    timeline: CaseTimelineEntry[]
    billing?: CaseBilling
  }
}
```

### PUT /cases/:id
Update case information.

**Request:**
```typescript
interface UpdateCaseRequest {
  title?: string
  description?: string
  status?: CaseStatus
  priority?: CasePriority
  assignedTo?: string[]
  dueDate?: string
  tags?: string[]
  customFields?: Record<string, any>
}
```

### DELETE /cases/:id
Delete a case (soft delete).

**Response:**
```typescript
interface DeleteCaseResponse {
  success: boolean
  message: string
}
```

## 📄 Document Management Endpoints

### GET /documents
List documents with filtering.

**Query Parameters:**
```typescript
interface DocumentListParams {
  page?: number
  limit?: number
  caseId?: string
  type?: DocumentType[]
  category?: DocumentCategory[]
  search?: string
  tags?: string[]
  uploadedBy?: string
  dateFrom?: string
  dateTo?: string
}
```

### POST /documents/upload
Upload a new document.

**Request (multipart/form-data):**
```typescript
interface DocumentUploadRequest {
  file: File
  caseId?: string
  type: DocumentType
  category: DocumentCategory
  title?: string
  description?: string
  tags?: string[]
  classification: SecurityClassification
}
```

**Response:**
```typescript
interface DocumentUploadResponse {
  success: boolean
  data: Document
  message: string
}
```

**Example:**
```typescript
const formData = new FormData()
formData.append('file', file)
formData.append('caseId', caseId)
formData.append('type', 'contract')
formData.append('classification', 'confidential')

const response = await api.post('/documents/upload', formData, {
  headers: { 'Content-Type': 'multipart/form-data' }
})
```

### GET /documents/:id
Get document details.

**Response:**
```typescript
interface DocumentDetailResponse {
  success: boolean
  data: Document & {
    versions: DocumentVersion[]
    annotations: DocumentAnnotation[]
    permissions: DocumentPermission[]
  }
}
```

### GET /documents/:id/download
Download document file.

**Response:** Binary file stream

### POST /documents/:id/annotate
Add annotation to document.

**Request:**
```typescript
interface AnnotateDocumentRequest {
  type: AnnotationType
  content: string
  position: AnnotationPosition
  tags?: string[]
}
```

## 💰 Billing Endpoints

### GET /billing/time-entries
List time entries.

**Query Parameters:**
```typescript
interface TimeEntryListParams {
  page?: number
  limit?: number
  caseId?: string
  userId?: string
  billable?: boolean
  dateFrom?: string
  dateTo?: string
}
```

### POST /billing/time-entries
Create time entry.

**Request:**
```typescript
interface CreateTimeEntryRequest {
  caseId: string
  date: string
  hours: number
  description: string
  billable: boolean
  rate?: number
  tags?: string[]
}
```

### GET /billing/invoices
List invoices.

**Query Parameters:**
```typescript
interface InvoiceListParams {
  page?: number
  limit?: number
  clientId?: string
  status?: InvoiceStatus[]
  dateFrom?: string
  dateTo?: string
}
```

### POST /billing/invoices
Generate invoice.

**Request:**
```typescript
interface GenerateInvoiceRequest {
  clientId: string
  caseIds?: string[]
  timeEntryIds?: string[]
  dateFrom: string
  dateTo: string
  dueDate?: string
  notes?: string
}
```

## 🔍 Search Endpoints

### GET /search
Global search across all entities.

**Query Parameters:**
```typescript
interface SearchParams {
  q: string
  types?: ('cases' | 'documents' | 'users' | 'clients')[]
  limit?: number
  filters?: Record<string, any>
}
```

**Response:**
```typescript
interface SearchResponse {
  success: boolean
  data: {
    cases: SearchResult<Case>[]
    documents: SearchResult<Document>[]
    users: SearchResult<User>[]
    clients: SearchResult<Client>[]
  }
  total: number
  processingTime: number
}
```

## 📊 Analytics Endpoints

### GET /analytics/dashboard
Get dashboard analytics.

**Query Parameters:**
```typescript
interface DashboardAnalyticsParams {
  period?: 'week' | 'month' | 'quarter' | 'year'
  dateFrom?: string
  dateTo?: string
}
```

**Response:**
```typescript
interface DashboardAnalyticsResponse {
  success: boolean
  data: {
    cases: {
      total: number
      open: number
      closed: number
      overdue: number
    }
    billing: {
      totalRevenue: number
      billableHours: number
      averageRate: number
      pendingInvoices: number
    }
    documents: {
      total: number
      recentUploads: number
      storageUsed: number
    }
    trends: {
      casesOverTime: TimeSeriesData[]
      revenueOverTime: TimeSeriesData[]
    }
  }
}
```

## 🚨 Error Handling

### Error Response Format
```typescript
interface ErrorResponse {
  success: false
  error: {
    code: string
    message: string
    details?: any
    field?: string
    timestamp: string
    requestId: string
  }
}
```

### Common Error Codes
- `VALIDATION_ERROR`: Request validation failed
- `UNAUTHORIZED`: Authentication required
- `FORBIDDEN`: Insufficient permissions
- `NOT_FOUND`: Resource not found
- `CONFLICT`: Resource conflict
- `RATE_LIMITED`: Too many requests
- `SERVER_ERROR`: Internal server error

### Error Handling Example
```typescript
try {
  const response = await api.post('/cases', caseData)
  return response.data
} catch (error) {
  if (error.response?.status === 422) {
    // Handle validation errors
    const validationErrors = error.response.data.error.details
    showValidationErrors(validationErrors)
  } else if (error.response?.status === 401) {
    // Handle authentication error
    await refreshToken()
    // Retry request
  } else {
    // Handle other errors
    showErrorMessage(error.response?.data?.error?.message || 'An error occurred')
  }
}
```

## 🔄 Real-time Updates

### WebSocket Connection
```typescript
const ws = new WebSocket('wss://api.legalsaas.com/ws')

ws.onmessage = (event) => {
  const message = JSON.parse(event.data)
  
  switch (message.type) {
    case 'case_updated':
      updateCaseInStore(message.data)
      break
    case 'document_uploaded':
      addDocumentToStore(message.data)
      break
    case 'notification':
      showNotification(message.data)
      break
  }
}
```

### Server-Sent Events
```typescript
const eventSource = new EventSource('/api/v1/events')

eventSource.onmessage = (event) => {
  const data = JSON.parse(event.data)
  handleRealtimeUpdate(data)
}
```

## 📝 API Client Implementation

### Type-Safe API Client
```typescript
class ApiClient {
  private baseURL: string
  private headers: Record<string, string>
  
  constructor(config: ApiConfig) {
    this.baseURL = config.baseURL
    this.headers = config.headers
  }
  
  async request<T>(config: RequestConfig): Promise<ApiResponse<T>> {
    const response = await fetch(`${this.baseURL}${config.url}`, {
      method: config.method,
      headers: { ...this.headers, ...config.headers },
      body: config.data ? JSON.stringify(config.data) : undefined
    })
    
    if (!response.ok) {
      throw new ApiError(response)
    }
    
    return response.json()
  }
  
  get<T>(url: string, params?: any): Promise<ApiResponse<T>> {
    return this.request({ method: 'GET', url, params })
  }
  
  post<T>(url: string, data?: any): Promise<ApiResponse<T>> {
    return this.request({ method: 'POST', url, data })
  }
  
  // ... other HTTP methods
}
```

## 🧪 API Testing

### Mock API Responses
```typescript
// For testing and development
export const mockApiResponses = {
  'GET /cases': {
    success: true,
    data: [
      {
        id: 'case-1',
        title: 'Contract Dispute',
        status: 'open',
        priority: 'high'
      }
    ],
    pagination: {
      page: 1,
      limit: 20,
      total: 1,
      totalPages: 1
    }
  }
}
```

This API documentation provides comprehensive coverage of all endpoints used by the Legal SaaS Frontend, ensuring developers can effectively integrate with the backend services.
