import { describe, it, expect, beforeEach, vi } from 'vitest'
// import { mount, VueWrapper } from '@vue/test-utils'
import { nextTick } from 'vue'
import DynamicForm from '~/components/forms/DynamicForm.vue'
import type { DynamicFormConfig, ComponentSize } from '~/app/shared/types'
import { DynamicFormFieldType } from '~/app/shared/types'

// Mock VeeValidate
vi.mock('vee-validate', () => ({
  Form: {
    name: 'Form',
    template: '<form @submit.prevent="$emit(\'submit\', $event)"><slot /></form>',
    emits: ['submit', 'invalid-submit']
  },
  useField: () => ({
    value: { value: '' },
    errorMessage: { value: null },
    setValue: vi.fn(),
    handleBlur: vi.fn(),
    handleChange: vi.fn()
  })
}))

// Mock @vee-validate/zod
vi.mock('@vee-validate/zod', () => ({
  toTypedSchema: vi.fn((schema) => schema)
}))

// Mock zod
vi.mock('zod', () => ({
  z: {
    object: vi.fn(() => ({})),
    string: vi.fn(() => ({})),
    number: vi.fn(() => ({})),
    boolean: vi.fn(() => ({})),
    any: vi.fn(() => ({}))
  }
}))

describe('DynamicForm', () => {
  let wrapper: VueWrapper<any>
  let basicConfig: DynamicFormConfig

  beforeEach(() => {
    basicConfig = {
      id: 'test-form',
      title: 'Test Form',
      description: 'A test form',
      fields: [
        {
          id: 'firstName',
          name: 'firstName',
          label: 'First Name',
          type: DynamicFormFieldType.TEXT,
          required: true,
          validation: {
            required: true,
            minLength: 2
          }
        },
        {
          id: 'email',
          name: 'email',
          label: 'Email',
          type: DynamicFormFieldType.EMAIL,
          required: true,
          validation: {
            required: true,
            email: true
          }
        },
        {
          id: 'age',
          name: 'age',
          label: 'Age',
          type: DynamicFormFieldType.NUMBER,
          validation: {
            min: 18,
            max: 120
          }
        }
      ],
      layout: {
        columns: 2,
        spacing: 'md' as ComponentSize
      }
    }
  })

  afterEach(() => {
    if (wrapper) {
      wrapper.unmount()
    }
  })

  describe('Rendering', () => {
    it('renders form with title and description', async () => {
      wrapper = mount(DynamicForm, {
        props: {
          config: basicConfig
        }
      })

      expect(wrapper.find('h2').text()).toBe('Test Form')
      expect(wrapper.find('p').text()).toBe('A test form')
    })

    it('renders all form fields', async () => {
      wrapper = mount(DynamicForm, {
        props: {
          config: basicConfig
        }
      })

      await nextTick()

      // Check that all fields are rendered
      expect(wrapper.findAll('[data-field-name]')).toHaveLength(3)
      expect(wrapper.find('[data-field-name="firstName"]').exists()).toBe(true)
      expect(wrapper.find('[data-field-name="email"]').exists()).toBe(true)
      expect(wrapper.find('[data-field-name="age"]').exists()).toBe(true)
    })

    it('applies correct grid classes based on layout', async () => {
      wrapper = mount(DynamicForm, {
        props: {
          config: basicConfig
        }
      })

      const fieldsContainer = wrapper.find('.dynamic-form__fields')
      expect(fieldsContainer.classes()).toContain('grid')
      expect(fieldsContainer.classes()).toContain('grid-cols-1')
      expect(fieldsContainer.classes()).toContain('md:grid-cols-2')
      expect(fieldsContainer.classes()).toContain('gap-4')
    })

    it('shows default actions when showDefaultActions is true', async () => {
      wrapper = mount(DynamicForm, {
        props: {
          config: basicConfig,
          showDefaultActions: true
        }
      })

      expect(wrapper.find('button[type="submit"]').exists()).toBe(true)
    })

    it('shows reset button when showResetButton is true', async () => {
      wrapper = mount(DynamicForm, {
        props: {
          config: basicConfig,
          showResetButton: true
        }
      })

      expect(wrapper.find('button[type="button"]').text()).toBe('Reset')
    })
  })

  describe('Form State', () => {
    it('initializes with default values', async () => {
      const configWithDefaults = {
        ...basicConfig,
        fields: [
          ...basicConfig.fields,
          {
            id: 'newsletter',
            name: 'newsletter',
            label: 'Newsletter',
            type: 'checkbox' as const,
            defaultValue: true
          }
        ]
      }

      wrapper = mount(DynamicForm, {
        props: {
          config: configWithDefaults,
          modelValue: {}
        }
      })

      await nextTick()

      // Check that default values are set
      expect(wrapper.vm.formData.newsletter).toBe(true)
    })

    it('updates form data when modelValue changes', async () => {
      wrapper = mount(DynamicForm, {
        props: {
          config: basicConfig,
          modelValue: { firstName: 'John' }
        }
      })

      await nextTick()

      expect(wrapper.vm.formData.firstName).toBe('John')

      // Update modelValue
      await wrapper.setProps({
        modelValue: { firstName: 'Jane', email: '<EMAIL>' }
      })

      expect(wrapper.vm.formData.firstName).toBe('Jane')
      expect(wrapper.vm.formData.email).toBe('<EMAIL>')
    })

    it('emits update:modelValue when form data changes', async () => {
      wrapper = mount(DynamicForm, {
        props: {
          config: basicConfig,
          modelValue: {}
        }
      })

      // Simulate field change
      wrapper.vm.handleFieldChange('firstName', 'John')
      await nextTick()

      expect(wrapper.emitted('update:modelValue')).toBeTruthy()
      expect(wrapper.emitted('field-change')).toBeTruthy()
    })
  })

  describe('Validation', () => {
    it('creates validation schema from field configurations', async () => {
      wrapper = mount(DynamicForm, {
        props: {
          config: basicConfig
        }
      })

      await nextTick()

      // Check that validation schema is created
      expect(wrapper.vm.validationSchema).toBeDefined()
    })

    it('shows error summary when configured', async () => {
      const configWithErrorSummary = {
        ...basicConfig,
        validation: {
          showErrorSummary: true
        }
      }

      wrapper = mount(DynamicForm, {
        props: {
          config: configWithErrorSummary
        }
      })

      // Set form state to have errors
      wrapper.vm.formState.errors = {
        firstName: ['First name is required'],
        email: ['Email is required']
      }
      wrapper.vm.formState.touched = { firstName: true, email: true }

      await nextTick()

      expect(wrapper.find('.dynamic-form__error-summary').exists()).toBe(true)
      expect(wrapper.text()).toContain('Please correct the following errors')
    })
  })

  describe('Events', () => {
    it('emits submit event when form is submitted', async () => {
      wrapper = mount(DynamicForm, {
        props: {
          config: basicConfig,
          modelValue: {
            firstName: 'John',
            email: '<EMAIL>',
            age: 25
          }
        }
      })

      await wrapper.find('form').trigger('submit')

      expect(wrapper.emitted('submit')).toBeTruthy()
      expect(wrapper.emitted('submit')?.[0]?.[0]).toEqual({
        firstName: 'John',
        email: '<EMAIL>',
        age: 25
      })
    })

    it('emits field-change event when field value changes', async () => {
      wrapper = mount(DynamicForm, {
        props: {
          config: basicConfig
        }
      })

      wrapper.vm.handleFieldChange('firstName', 'John')

      expect(wrapper.emitted('field-change')).toBeTruthy()
      expect(wrapper.emitted('field-change')?.[0]).toEqual(['firstName', 'John', expect.any(Object)])
    })

    it('emits reset event when form is reset', async () => {
      wrapper = mount(DynamicForm, {
        props: {
          config: basicConfig,
          showResetButton: true
        }
      })

      await wrapper.find('button[type="button"]').trigger('click')

      expect(wrapper.emitted('reset')).toBeTruthy()
    })
  })

  describe('Conditional Logic', () => {
    it('shows/hides fields based on conditions', async () => {
      const conditionalConfig: DynamicFormConfig = {
        id: 'conditional-form',
        fields: [
          {
            id: 'accountType',
            name: 'accountType',
            label: 'Account Type',
            type: DynamicFormFieldType.SELECT,
            options: [
              { label: 'Personal', value: 'personal' },
              { label: 'Business', value: 'business' }
            ]
          },
          {
            id: 'companyName',
            name: 'companyName',
            label: 'Company Name',
            type: DynamicFormFieldType.TEXT,
            conditional: {
              show: [
                { field: 'accountType', operator: 'equals', value: 'business' }
              ]
            }
          }
        ]
      }

      wrapper = mount(DynamicForm, {
        props: {
          config: conditionalConfig,
          modelValue: { accountType: 'personal' }
        }
      })

      await nextTick()

      // Company name should be hidden when account type is personal
      expect(wrapper.vm.visibleFields.find((f: any) => f.name === 'companyName')).toBeUndefined()

      // Change account type to business
      await wrapper.setProps({
        modelValue: { accountType: 'business' }
      })

      await nextTick()

      // Company name should now be visible
      expect(wrapper.vm.visibleFields.find((f: any) => f.name === 'companyName')).toBeDefined()
    })
  })

  describe('Accessibility', () => {
    it('provides proper ARIA attributes', async () => {
      wrapper = mount(DynamicForm, {
        props: {
          config: basicConfig
        }
      })

      await nextTick()

      // Check for ARIA attributes on form
      const form = wrapper.find('form')
      expect(form.attributes('role')).toBe('form')
      expect(form.attributes('aria-label')).toBe('Sign in form')
    })

    it('shows error summary with proper ARIA attributes', async () => {
      const configWithErrorSummary = {
        ...basicConfig,
        validation: {
          showErrorSummary: true
        }
      }

      wrapper = mount(DynamicForm, {
        props: {
          config: configWithErrorSummary
        }
      })

      // Set errors
      wrapper.vm.formState.errors = { firstName: ['Required'] }
      wrapper.vm.formState.touched = { firstName: true }

      await nextTick()

      const errorSummary = wrapper.find('.dynamic-form__error-summary')
      expect(errorSummary.attributes('role')).toBe('alert')
      expect(errorSummary.attributes('aria-live')).toBe('polite')
    })
  })

  describe('Loading States', () => {
    it('shows loading overlay when loading prop is true', async () => {
      wrapper = mount(DynamicForm, {
        props: {
          config: basicConfig,
          loading: true
        }
      })

      expect(wrapper.find('.absolute.inset-0').exists()).toBe(true)
      expect(wrapper.text()).toContain('Loading...')
    })

    it('disables submit button when submitting', async () => {
      wrapper = mount(DynamicForm, {
        props: {
          config: basicConfig
        }
      })

      // Set submitting state
      wrapper.vm.formState.isSubmitting = true
      await nextTick()

      const submitButton = wrapper.find('button[type="submit"]')
      expect(submitButton.attributes('disabled')).toBeDefined()
    })
  })

  describe('Styling and Theming', () => {
    it('applies theme classes', async () => {
      const themedConfig = {
        ...basicConfig,
        styling: {
          theme: 'bordered',
          size: 'lg',
          variant: 'outlined'
        }
      }

      wrapper = mount(DynamicForm, {
        props: {
          config: themedConfig
        }
      })

      const formElement = wrapper.find('.dynamic-form')
      expect(formElement.classes()).toContain('dynamic-form--bordered')
      expect(formElement.classes()).toContain('dynamic-form--lg')
      expect(formElement.classes()).toContain('dynamic-form--outlined')
    })

    it('applies custom CSS classes', async () => {
      const customConfig = {
        ...basicConfig,
        styling: {
          className: 'my-custom-form'
        }
      }

      wrapper = mount(DynamicForm, {
        props: {
          config: customConfig
        }
      })

      expect(wrapper.find('.my-custom-form').exists()).toBe(true)
    })
  })
})
