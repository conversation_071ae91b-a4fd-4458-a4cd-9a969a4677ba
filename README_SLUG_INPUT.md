# Slug Input Component

A robust slug input component for the Legal SaaS Frontend that handles URL-friendly slug creation with validation and uniqueness checking.

## 🚀 Quick Start

```vue
<template>
  <UiSlugInput
    id="my-slug"
    name="mySlug"
    v-model="slug"
    label="URL Slug"
    placeholder="Enter slug"
    :check-uniqueness="checkUniqueness"
    required
  />
</template>

<script setup>
const slug = ref('')

const checkUniqueness = async (slug) => {
  const response = await $fetch(`/api/check-slug?slug=${slug}`)
  return response.isUnique
}
</script>
```

## ✨ Features

- **Auto-transformation**: Spaces → hyphens, invalid chars filtered
- **Real-time validation**: Visual feedback as you type
- **Async uniqueness check**: Debounced API validation
- **Customizable patterns**: Configure allowed characters
- **VeeValidate integration**: Works with form validation
- **Accessibility**: Full a11y support
- **Loading states**: Visual indicators during validation

## 📁 Files Created

```
components/
├── ui/
│   └── UiSlugInput.vue           # Main component
└── examples/
    └── SlugInputExample.vue      # Usage examples

composables/
└── useSlugValidation.ts          # Validation utilities

pages/examples/
└── slug-input.vue               # Demo page

tests/components/
└── UiSlugInput.test.ts          # Unit tests

docs/
└── SLUG_INPUT_COMPONENT.md      # Full documentation
```

## 🎯 Key Props

| Prop | Type | Description |
|------|------|-------------|
| `checkUniqueness` | `(slug: string) => Promise<boolean>` | Async uniqueness validator |
| `allowedPattern` | `RegExp` | Allowed characters (default: a-z, 0-9, -) |
| `transformSpaces` | `boolean` | Convert spaces to hyphens (default: true) |
| `debounceMs` | `number` | Validation delay (default: 500ms) |

## 🔧 Using with Composables

```vue
<script setup>
// Pre-configured for different entity types
const tenantValidator = useTenantSlugValidation()
const userValidator = useUserSlugValidation()
const templateValidator = useTemplateSlugValidation()

// Generate slugs
const slug = tenantValidator.generateSlug('My Company Name') // → 'my-company-name'
const uniqueSlug = await tenantValidator.generateUniqueSlug('my-company') // → 'my-company' or 'my-company-1'
</script>
```

## 🎨 Examples

Visit `/examples/slug-input` to see interactive examples including:

- Basic slug input
- Async uniqueness validation
- Custom patterns and configurations
- Form integration
- Composable usage

## 🧪 Testing

Run tests with:
```bash
npm run test tests/components/UiSlugInput.test.ts
```

## 📖 Full Documentation

See `docs/SLUG_INPUT_COMPONENT.md` for complete API reference and advanced usage examples.

## 🔗 Integration

The component integrates seamlessly with:
- VeeValidate for form validation
- Existing UI components (UiInput patterns)
- API endpoints for uniqueness checking
- Tailwind CSS for styling
- Vue 3 Composition API

## 🎯 Use Cases

Perfect for:
- Tenant slug creation
- User profile URLs
- Template identifiers
- Case reference slugs
- Document permalinks
- Any URL-friendly identifier
