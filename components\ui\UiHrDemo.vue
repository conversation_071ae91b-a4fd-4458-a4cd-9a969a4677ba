<template>
  <div class="p-8 space-y-8 bg-white dark:bg-gray-900">
    <div class="max-w-4xl mx-auto">
      <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-8">
        UiHr Component Demo
      </h1>

      <!-- Basic Usage -->
      <section class="space-y-4">
        <h2 class="text-xl font-semibold text-gray-800 dark:text-gray-200">
          Basic Usage
        </h2>
        <div class="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg">
          <p class="text-gray-600 dark:text-gray-400 mb-4">Default horizontal rule:</p>
          <UiHr />
          <p class="text-gray-600 dark:text-gray-400 mt-4">Content after the hr</p>
        </div>
      </section>

      <!-- Variants -->
      <section class="space-y-4">
        <h2 class="text-xl font-semibold text-gray-800 dark:text-gray-200">
          Variants
        </h2>
        <div class="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg space-y-6">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">Solid (default):</p>
            <UiHr variant="solid" />
          </div>
          
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">Dashed:</p>
            <UiHr variant="dashed" />
          </div>
          
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">Dotted:</p>
            <UiHr variant="dotted" />
          </div>
          
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">Double:</p>
            <UiHr variant="double" />
          </div>
          
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">Gradient:</p>
            <UiHr variant="gradient" color="primary" />
          </div>
        </div>
      </section>

      <!-- Sizes -->
      <section class="space-y-4">
        <h2 class="text-xl font-semibold text-gray-800 dark:text-gray-200">
          Sizes
        </h2>
        <div class="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg space-y-6">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">Extra Small (xs):</p>
            <UiHr size="xs" />
          </div>
          
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">Small (sm) - Default:</p>
            <UiHr size="sm" />
          </div>
          
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">Medium (md):</p>
            <UiHr size="md" />
          </div>
          
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">Large (lg):</p>
            <UiHr size="lg" />
          </div>
          
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">Extra Large (xl):</p>
            <UiHr size="xl" />
          </div>
        </div>
      </section>

      <!-- Colors -->
      <section class="space-y-4">
        <h2 class="text-xl font-semibold text-gray-800 dark:text-gray-200">
          Colors
        </h2>
        <div class="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg space-y-6">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">Gray (default):</p>
            <UiHr color="gray" />
          </div>
          
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">Primary:</p>
            <UiHr color="primary" />
          </div>
          
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">Success:</p>
            <UiHr color="success" />
          </div>
          
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">Warning:</p>
            <UiHr color="warning" />
          </div>
          
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">Danger:</p>
            <UiHr color="danger" />
          </div>
          
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">Info:</p>
            <UiHr color="info" />
          </div>
        </div>
      </section>

      <!-- Spacing -->
      <section class="space-y-4">
        <h2 class="text-xl font-semibold text-gray-800 dark:text-gray-200">
          Spacing
        </h2>
        <div class="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg">
          <p class="text-sm text-gray-600 dark:text-gray-400">Content before</p>
          <UiHr spacing="none" />
          <p class="text-sm text-gray-600 dark:text-gray-400">No spacing</p>
          <UiHr spacing="xs" />
          <p class="text-sm text-gray-600 dark:text-gray-400">Extra small spacing</p>
          <UiHr spacing="sm" />
          <p class="text-sm text-gray-600 dark:text-gray-400">Small spacing</p>
          <UiHr spacing="md" />
          <p class="text-sm text-gray-600 dark:text-gray-400">Medium spacing (default)</p>
          <UiHr spacing="lg" />
          <p class="text-sm text-gray-600 dark:text-gray-400">Large spacing</p>
          <UiHr spacing="xl" />
          <p class="text-sm text-gray-600 dark:text-gray-400">Extra large spacing</p>
        </div>
      </section>

      <!-- Special Effects -->
      <section class="space-y-4">
        <h2 class="text-xl font-semibold text-gray-800 dark:text-gray-200">
          Special Effects
        </h2>
        <div class="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg space-y-6">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">Faded:</p>
            <UiHr :faded="true" />
          </div>
          
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">Animated:</p>
            <UiHr :animated="true" color="primary" />
          </div>
          
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">Combined effects:</p>
            <UiHr variant="gradient" color="primary" size="md" :animated="true" />
          </div>
        </div>
      </section>

      <!-- Usage Examples -->
      <section class="space-y-4">
        <h2 class="text-xl font-semibold text-gray-800 dark:text-gray-200">
          Usage Examples
        </h2>
        <div class="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg space-y-8">
          <!-- Card with dividers -->
          <div class="bg-white dark:bg-gray-700 p-6 rounded-lg shadow-sm">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white">Card Title</h3>
            <UiHr spacing="sm" />
            <p class="text-gray-600 dark:text-gray-300">Card content goes here...</p>
            <UiHr variant="dashed" spacing="sm" />
            <div class="flex justify-end">
              <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                Action
              </button>
            </div>
          </div>

          <!-- Section divider -->
          <div>
            <h3 class="text-lg font-medium text-gray-900 dark:text-white">Section 1</h3>
            <p class="text-gray-600 dark:text-gray-300 mt-2">Some content for section 1...</p>
            
            <UiHr variant="gradient" color="primary" spacing="lg" />
            
            <h3 class="text-lg font-medium text-gray-900 dark:text-white">Section 2</h3>
            <p class="text-gray-600 dark:text-gray-300 mt-2">Some content for section 2...</p>
          </div>
        </div>
      </section>
    </div>
  </div>
</template>

<script setup lang="ts">
// Demo component - no additional logic needed
</script>
