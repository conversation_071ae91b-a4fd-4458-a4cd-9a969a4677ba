<template>
  <div class="space-y-6 py-6">
    <!-- Quick Stats -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <!-- Total Users -->
      <UiCard hover-effect>
        <div class="flex items-center justify-between">
          <div class="p-3 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
            <Icon name="material-symbols:group" class="h-6 w-6 text-blue-600 dark:text-blue-400" />
          </div>
          <div class="text-right">
            <p class="text-2xl font-bold text-gray-900 dark:text-white">
              {{ tenant?.userStats?.total }}
            </p>
            <p class="text-sm text-gray-500 dark:text-gray-400">
              {{ $t("tenantDetail.totalUsers") }}
            </p>
          </div>
        </div>
        <div class="mt-4 flex items-center text-sm">
          <Icon name="material-symbols:person-check" class="h-4 w-4 text-green-500 mr-1" />
          <span class="text-green-600 dark:text-green-400">{{ tenant?.userStats?.active }} {{ $t("tenantDetail.active")
            }}</span>
        </div>
      </UiCard>

      <!-- Total Cases -->
      <UiCard hover-effect>
        <div class="flex items-center justify-between">
          <div class="p-3 bg-green-100 dark:bg-green-900/20 rounded-lg">
            <Icon name="material-symbols:folder" class="h-6 w-6 text-green-600 dark:text-green-400" />
          </div>
          <div class="text-right">
            <p class="text-2xl font-bold text-gray-900 dark:text-white">
              {{ tenant?.caseStats?.total }}
            </p>
            <p class="text-sm text-gray-500 dark:text-gray-400">
              {{ $t("tenantDetail.totalCases") }}
            </p>
          </div>
        </div>
        <div class="mt-4 flex items-center text-sm">
          <Icon name="hugeicons:task-done-02" class="h-4 w-4 text-green-500 mr-1" />
          <span class="text-green-600 dark:text-green-400">{{ tenant?.caseStats?.closed }} {{ $t("tenantDetail.closed")
            }}</span>
        </div>
      </UiCard>

      <!-- Documents -->
      <UiCard hover-effect>
        <div class="flex items-center justify-between">
          <div class="p-3 bg-orange-100 dark:bg-orange-900/20 rounded-lg">
            <Icon name="material-symbols:description" class="h-6 w-6 text-orange-600 dark:text-orange-400" />
          </div>
          <div class="text-right">
            <p class="text-2xl font-bold text-gray-900 dark:text-white">
              {{ tenant?.documentStats?.total || 0 }}
            </p>
            <p class="text-sm text-gray-500 dark:text-gray-400">
              {{ $t("tenantDetail.documents") }}
            </p>
          </div>
        </div>
        <div class="mt-4 flex items-center text-sm">
          <Icon name="fluent:signed-16-regular" class="h-4 w-4 text-orange-500 mr-1" />
          <span class="text-orange-600 dark:text-orange-400">{{
            tenant?.documentStats?.signed
          }}</span>
        </div>
      </UiCard>

      <!-- Storage Usage -->
      <UiCard hover-effect>
        <div class="flex items-center justify-between">
          <div class="p-3 bg-purple-100 dark:bg-purple-900/20 rounded-lg">
            <Icon name="material-symbols:cloud-upload" class="h-6 w-6 text-purple-600 dark:text-purple-400" />
          </div>
          <div class="text-right">
            <p class="text-2xl font-bold text-gray-900 dark:text-white">
              {{ tenant?.usageStatistics?.storageUsed }}
            </p>
            <p class="text-sm text-gray-500 dark:text-gray-400">
              {{ $t("tenantDetail.storageUsed") }}
            </p>
          </div>
        </div>
        <div class="mt-4">
          <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div class="h-2 rounded-full transition-all duration-300" :class="getStorageBarColor(storagePercentages)"
              :style="{ width: `${Math.min(storagePercentages, 100)}%` }"></div>
          </div>
        </div>
      </UiCard>
    </div>

    <!-- Main Content Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- Main Content -->
      <div class="lg:col-span-2 space-y-6">
        <!-- Tenant Configuration -->
        <UiCard :title="$t('tenantDetail.configuration')" :subtitle="$t('tenantDetail.tenantSettingsDescription')"
          icon="material-symbols:settings" icon-class="text-blue-600 dark:text-blue-400" class="space-y-4">
          <!-- actions -->
          <template #header>
            <div class="flex items-center justify-between">
              <UiButton variant="flat" size="sm" @click="editTenant">
                <Icon name="material-symbols:edit" size="calc(var(--spacing) * 4)" />
              </UiButton>
            </div>
          </template>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="space-y-4">
              <div>
                <label class="text-sm font-medium text-gray-700 dark:text-gray-300">{{
                  $t("tenantDetail.tenantName")
                }}</label>
                <p class="text-sm text-gray-900 dark:text-white mt-1">
                  {{ tenant?.name }}
                </p>
              </div>
              <div>
                <label class="text-sm font-medium text-gray-700 dark:text-gray-300">{{
                  $t("tenantDetail.slug")
                }}</label>
                <p class="text-sm text-gray-900 dark:text-white mt-1">
                  {{ tenant?.slug }}
                </p>
              </div>
              <div>
                <label class="text-sm font-medium text-gray-700 dark:text-gray-300">{{
                  $t("tenantDetail.plan")
                }}</label>
                <div class="flex items-center gap-2 mt-1">
                  <UiBadge color="primary">{{ tenant?.plan || "Basic" }}</UiBadge>
                  <UiButton variant="ghost" size="sm" @click="upgradePlan">
                    {{ $t("tenantDetail.upgrade") }}
                  </UiButton>
                </div>
              </div>
            </div>
            <div class="space-y-4">
              <div>
                <label class="text-sm font-medium text-gray-700 dark:text-gray-300">{{
                  $t("tenantDetail.locale")
                }}</label>
                <p class="text-sm text-gray-900 dark:text-white mt-1">
                  {{ tenant?.locale || "en" }}
                </p>
              </div>
              <div>
                <label class="text-sm font-medium text-gray-700 dark:text-gray-300">{{
                  $t("tenantDetail.timezone")
                }}</label>
                <p class="text-sm text-gray-900 dark:text-white mt-1">
                  {{ tenant?.timezone || "UTC" }}
                </p>
              </div>
              <div>
                <label class="text-sm font-medium text-gray-700 dark:text-gray-300">{{
                  $t("tenantDetail.status")
                }}</label>
                <div class="flex items-center gap-2 mt-1">
                  <UiBadge :color="tenant?.isActive ? 'success' : 'warning'">
                    {{ tenant?.isActive ? $t("common.active") : $t("common.inactive") }}
                  </UiBadge>
                  <UiButton variant="ghost" size="sm" @click="toggleTenantStatus" :loading="isUpdatingStatus">
                    {{
                      tenant?.isActive
                        ? $t("tenantDetail.deactivate")
                        : $t("tenantDetail.activate")
                    }}
                  </UiButton>
                </div>
              </div>
            </div>
          </div>
        </UiCard>

        <!-- Recent Activity -->
        <UiCard content-height="300px" icon="material-symbols:history"
          :icon-props="{ color: 'success', background: 'success' }" :title="$t('tenantDetail.recentActivity')"
          :subtitle="$t('tenantDetail.latestTenantActivities')">
          <template #header>
            <div class="flex items-center justify-between">
              <UiButton variant="ghost" size="sm" @click="viewAllActivity">
                {{ $t("tenantDetail.viewAll") }}
              </UiButton>
            </div>
          </template>
          <div class="space-y-4">
            <div v-for="activity in usersStore.userActivity?.data || []" :key="activity.id"
              class="flex items-start gap-4 p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">

              <div class="flex-shrink-0 mt-1">
                <div class="w-8 h-8 rounded-full flex items-center justify-center"
                  :class="getActivityIconBg(activity.activityType)">
                  <Icon :name="getActivityIcon(activity.activityType)" class="h-4 w-4"
                    :class="getActivityIconColor(activity.activityType)" />
                </div>
              </div>
              <div class="flex-1 min-w-0">
                <div class="flex items-center justify-between">
                  <p class="text-sm font-medium text-gray-900 dark:text-white">
                    {{ activity.activityType }}
                  </p>
                  <span class="text-xs text-gray-500 dark:text-gray-400">
                    {{ fromNow(activity.createdAt) }}
                  </span>
                </div>
                <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                  {{ activity.description }}
                </p>
                <div v-if="activity.user" class="flex items-center gap-1 mt-2 text-xs text-gray-500 dark:text-gray-400">
                  <Icon name="material-symbols:person" class="h-3 w-3" />
                  <span>{{ activity.user.name }}</span>
                </div>
              </div>
            </div>
            <div v-if="usersStore.userActivity?.data && usersStore.userActivity?.data.length === 0"
              class="text-center py-8">
              <Icon name="material-symbols:history" class="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p class="text-sm text-gray-500 dark:text-gray-400">
                {{ $t("tenantDetail.noRecentActivity") }}
              </p>
            </div>
          </div>
        </UiCard>


        <UiCard content-height="300px" icon="material-symbols:group"
          :icon-props="{ color: 'purple', background: 'purple' }" :title="$t('tenantDetail.users')"
          :subtitle="$t('tenantDetail.tenantUsersDescription')">
          <template #header>
            <div class="flex items-center justify-between">

              <div class="flex items-center gap-2">
                <UiButton variant="ghost" size="sm" @click="inviteUsers">
                  <Icon name="material-symbols:person-add" class="h-4 w-4 mr-1" />
                  {{ $t('tenantDetail.invite') }}
                </UiButton>
                <UiButton variant="ghost" size="sm" @click="manageUsers">
                  {{ $t('tenantDetail.viewAll') }}
                </UiButton>
              </div>
            </div>
          </template>
          <div class="space-y-3">
            <div v-for="user in tenantUsers.slice(0, 5)" :key="user.id"
              class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
              <div class="flex items-center gap-3">
                <div class="w-10 h-10 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center">
                  <Icon name="material-symbols:person" class="h-5 w-5 text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                  <p class="text-sm font-medium text-gray-900 dark:text-white">{{ user.name }}</p>
                  <p class="text-xs text-gray-500 dark:text-gray-400">{{ user.email }}</p>
                </div>
              </div>
              <div class="flex items-center gap-2" v-for="role in user.roles" :key="role">
                <UiBadge :color="getRoleVariant(role)" size="sm" variant="outline">
                  {{ role }}
                </UiBadge>
                <UiBadge :color="user.isActive ? 'success' : 'neutral'" size="sm">
                  {{ user.isActive ? $t('common.active') : $t('common.inactive') }}
                </UiBadge>
              </div>
            </div>
            <div v-if="tenantUsers.length === 0" class="text-center py-8">
              <Icon name="material-symbols:group" class="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p class="text-sm text-gray-500 dark:text-gray-400">{{ $t('tenantDetail.noUsersFound') }}</p>
              <UiButton variant="outline" size="sm" @click="inviteUsers" class="mt-3">
                {{ $t('tenantDetail.inviteFirstUser') }}
              </UiButton>
            </div>
            <div v-if="tenantUsers.length > 5" class="text-center pt-3 border-t border-gray-200 dark:border-gray-700">
              <UiButton variant="ghost" size="sm" @click="manageUsers">
                View All {{ tenantUsers.length }} Users
              </UiButton>
            </div>
          </div>
        </UiCard>
      </div>

      <!-- Sidebar -->
      <div class="space-y-6">
        <!-- System Information -->
        <UiCard icon="material-symbols:info" :icon-props="{ color: 'info', background: 'info' }"
          :title="$t('tenantDetail.systemInformation')" :subtitle="$t('tenantDetail.tenantSystemInformation')">
          <div class="space-y-4">
            <div>
              <label class="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">{{
                $t("tenantDetail.tenantId") }}</label>
              <div class="flex items-center gap-2 mt-1">
                <code class="text-sm bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded font-mono">
            {{ tenant?.id }}
          </code>
                <UiButton variant="ghost" size="sm" shape="circle" @click="copyToClipboard(tenant?.id)"
                  :aria-label="$t('tenantDetail.copyTenantId')">
                  <Icon name="material-symbols:content-copy" class="h-3 w-3" />
                </UiButton>
              </div>
            </div>
            <div>
              <label class="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">{{
                $t("tenantDetail.createdAt") }}</label>
              <p class="text-sm text-gray-900 dark:text-white mt-1">
                {{ $formatDate(tenant?.createdAt) }}
              </p>
            </div>
            <div>
              <label class="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">{{
                $t("tenantDetail.lastUpdated") }}</label>
              <p class="text-sm text-gray-900 dark:text-white mt-1">
                {{ $formatDate(tenant?.updatedAt) }}
              </p>
            </div>
            <div v-if="tenant?.createdById">
              <label class="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">{{
                $t("tenantDetail.createdBy") }}</label>
              <p class="text-sm text-gray-900 dark:text-white mt-1">
                {{ tenant.createdBy.name }}
              </p>
            </div>
          </div>
        </UiCard>

        <!-- Plan Details -->
        <UiCard icon="material-symbols:workspace-premium" :icon-props="{ color: 'purple', background: 'purple' }"
          :title="$t('tenantDetail.planDetails')" :subtitle="$t('tenantDetail.currentSubscription')">
          <template #header>
            <div class="flex items-center justify-between">
              <UiButton variant="ghost" size="sm" @click="upgradePlan">
                {{ $t("tenantDetail.upgrade") }}
              </UiButton>
            </div>
          </template>
          <div class="space-y-4">
            <!-- Plan Limits -->
            <div class="space-y-3">
              <div v-for="limit in planLimits" :key="limit.name" class="space-y-1">
                <div class="flex items-center justify-between text-sm">
                  <span class="text-gray-700 dark:text-gray-300">{{ limit.name }}</span>
                  <span class="text-gray-500 dark:text-gray-400">
                    {{ limit.used }} / {{ limit.limit === -1 ? "∞" : limit.limit }}
                  </span>
                </div>
                <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5">
                  <div class="h-1.5 rounded-full transition-all duration-300"
                    :class="getLimitBarColor(limit.percentage)"
                    :style="{ width: `${Math.min(limit.percentage, 100)}%` }"></div>
                </div>
              </div>
            </div>
          </div>
        </UiCard>

        <!-- Danger Zone -->
        <UiCard variant="outlined" icon="material-symbols:warning" :icon-props="{ color: 'error', background: 'none' }"
          :title="$t('tenantDetail.dangerZone')" :subtitle="$t('tenantDetail.irreversibleActions')"
          class="border-red-200 dark:border-red-800">
          <div class="space-y-3">
            <div class="p-3 bg-red-50 dark:bg-red-900/10 rounded-lg">
              <p class="text-sm text-red-800 dark:text-red-200 mb-3">
                {{ $t("tenantDetail.irreversibleActions") }}
              </p>
              <div class="space-y-2">
                <UiButton variant="outline" size="sm" @click="toggleTenantStatus" :loading="isUpdatingStatus"
                  class="w-full border-red-300 text-red-700 hover:bg-red-50 dark:border-red-700 dark:text-red-300 dark:hover:bg-red-900/20">
                  {{
                    tenant?.isActive
                      ? $t("tenantDetail.deactivateTenant")
                      : $t("tenantDetail.activateTenant")
                  }}
                </UiButton>
                <UiButton variant="outline" size="sm" @click="showDeleteConfirmation = true"
                  class="w-full border-red-300 text-red-700 hover:bg-red-50 dark:border-red-700 dark:text-red-300 dark:hover:bg-red-900/20">
                  {{ $t("tenantDetail.deleteTenant") }}
                </UiButton>
              </div>
            </div>
          </div>
        </UiCard>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { useTenantStore } from "~/stores/tenant";
import { useUserStore } from "~/stores/user";
import { useRoute } from "vue-router";
import { PlatformRoles, TenantRoles } from "~/app/features/auth/constants/roles";
import type { fromNow } from "utils/dateFormatters";

const tenantStore = useTenantStore();
const usersStore = useUserStore();
const route = useRoute();
const tenantId = computed(() => route.params.id as string);
const tenant = computed(() => tenantStore.selectedTenantWithLogo);

const Roles = { ...PlatformRoles, ...TenantRoles }

definePageMeta({
  layout: "dashboard",
  title: () => {
    const tenant = useTenantStore().selectedTenant;
    if (!tenant) return "Tenant Details";
    return tenant.name;
  },
  meta: () => ({ title: "Tenant Details" }),

  showPageHeader: true,
  showPageHeaderTitle: true,
  pageHeaderItemStats: () => {
    const tenant = useTenantStore().selectedTenant;
    if (!tenant) return '';

    return tenant.isActive ? { key: "active", label: "Active", color: "green" } : { key: "inactive", label: "Inactive", color: "red" };
  },
  pageHeaderLogoImage: () => {
    const tenant = useTenantStore().selectedTenantWithLogo;
    if (!tenant) return "";
    return tenant.logoUrl;
  },
  pageHeaderTags: () => {
    const tenant = useTenantStore().selectedTenant;
    if (!tenant) return [];
    return [
      {
        key: "plan",
        label: tenant.plan,
        class: "text-purple-600",
        icon: "i-material-symbols:workspace-premium",
      },
      {
        key: "locale",
        label: tenant.locale,
        class: "text-brandSecondary",
        icon: "i-heroicons:globe-alt",
      },
      {
        key: "timezone",
        label: tenant.timezone,
        class: "text-brandPrimary",
        icon: "i-heroicons:clock",
      },
    ];
  },
  pageHeaderActions: () => {
    const { $global } = useNuxtApp()
    // const tenant = useTenantStore().selectedTenant;
    const tenantId = useRoute().params.id as string;
    if (!tenantId) return [];
    return [
      {
        label: "Edit Tenant",
        icon: "heroicons:pencil-square",
        color: "outline",
        click: () => navigateTo(`/dashboard/tenants/${tenantId}/edit`),
      },
      {
        label: "Invite Users",
        icon: "i-heroicons:user-plus",
        color: "success",
        click: () => navigateTo(`/dashboard/tenants/${tenantId}/invite`),
      },
      {
        label: "Refresh",
        icon: "i-mdi:refresh",
        color: "secondary",
        click: () => $global.get('refreshData')(),
      },
    ].reverse();
  },

  showActionsMenu: true,

  middleware: ["rbac"],
  roles: [PlatformRoles.SUPER_ADMIN],
  breadcrumbs: () => {
    const tenant = useTenantStore().selectedTenant;
    if (!tenant) return [];
    return [
      { label: "Dashboard", href: "/dashboard" },
      { label: "Tenants", href: "/dashboard/tenants" },
      { label: tenant.name, href: "" }
    ];

  }
});

const storagePercentages = computed(() => {
  if (!tenant.value?.usageStatistics) return 0;
  const total = tenant.value?.usageStatistics?.storageLimitGB;
  const used = tenant.value?.usageStatistics?.storageUsedGB;
  const percentage = (used / total) * 100;
  return percentage;
});

const getStorageBarColor = (percentage: number) => {
  if (percentage >= 90) return "bg-red-500";
  if (percentage >= 75) return "bg-orange-500";
  return "bg-green-500";
};

const getLimitBarColor = (percentage: number) => {
  if (percentage >= 90) return "bg-red-500";
  if (percentage >= 75) return "bg-orange-500";
  return "bg-green-500";
};

const planLimits = computed(() => {
  if (!tenant.value?.usageStatistics) return [];
  console.log(tenant.value?.usageStatistics);

  return [
    {
      name: "Documents",
      used: tenant.value?.usageStatistics?.documentsUsed,
      limit: tenant.value?.usageStatistics?.documentsLimit,
      percentage:
        (tenant.value?.usageStatistics?.documentsUsed /
          tenant.value?.usageStatistics?.documentsLimit) *
        100,
    },
    {
      name: "Storage",
      used: tenant.value?.usageStatistics?.storageUsedGB,
      limit: tenant.value?.usageStatistics?.storageLimitGB,
      percentage:
        (tenant.value?.usageStatistics?.storageUsedGB /
          tenant.value?.usageStatistics?.storageLimitGB) *
        100,
    },
    {
      name: "Users",
      used: tenant.value?.usageStatistics?.users || 0,
      limit: tenant.value?.usageStatistics?.usersLimit || 0,
      percentage:
        (tenant.value?.usageStatistics?.users ||
          0 / tenant.value?.usageStatistics?.usersLimit ||
          0) * 100,
    },
    {
      name: "Cases",
      used: tenant.value?.usageStatistics?.cases,
      limit: tenant.value?.usageStatistics?.casesLimit,
      percentage:
        (tenant.value?.usageStatistics?.cases /
          tenant.value?.usageStatistics?.casesLimit) *
        100,
    },
    {
      name: "API Calls",
      used: tenant.value?.usageStatistics?.apiCallsUsed,
      limit: tenant.value?.usageStatistics?.apiCallsLimit,
      percentage:
        (tenant.value?.usageStatistics?.apiCallsUsed /
          tenant.value?.usageStatistics?.apiCallsLimit) *
        100,
    },
  ];
});

const getPlanLimit = (plan: string) => {
  const planLimits = tenant.value?.usageStatistics?.planLimits;
  if (!planLimits) return 0;
  return planLimits.find((limit: any) => limit.plan === plan)?.limit;
};

const getPlanUsage = (plan: string) => {
  const planLimits = tenant.value?.usageStatistics?.planLimits;
  if (!planLimits) return 0;
  return planLimits.find((limit: any) => limit.plan === plan)?.usage;
};

const editTenant = () => {
  navigateTo(`/dashboard/tenants/${tenantId.value}/edit`);
};

const upgradePlan = () => {
  navigateTo(`/dashboard/tenants/${tenantId.value}/upgrade`);
};

const isUpdatingStatus = ref(false);
const toggleTenantStatus = async () => {
  if (!tenant.value) return;

  isUpdatingStatus.value = true;
  try {
    await tenantStore.updateTenant(tenantId.value, { isActive: !tenant.value.isActive });
    console.log(`Tenant ${tenant.value.isActive ? "activated" : "deactivated"}`);
  } catch (error) {
    console.error("Error updating tenant status:", error);
  } finally {
    isUpdatingStatus.value = false;
  }
};

const isUpgradeAvailable = computed(() => {
  if (!tenant.value?.plan) return false;
  return tenant.value?.plan !== "enterprise";
});

const viewAllActivity = () => {
  navigateTo(`/dashboard/tenants/${tenantId.value}/activity`);
};

const showDeleteConfirmation = ref(false);

const getActivityIconBg = (type: string) => {
  const backgrounds: Record<string, string> = {
    CREATE_CASE: "bg-blue-100 dark:bg-blue-900/20",
    INVITE_USER: "bg-green-100 dark:bg-green-900/20",
    UPLOAD_DOCUMENT: "bg-purple-100 dark:bg-purple-900/20",
    UPDATE_CASE: "bg-orange-100 dark:bg-orange-900/20",
    LOGIN: "bg-gray-100 dark:bg-gray-900/20",
  };
  return backgrounds[type] || "bg-gray-100 dark:bg-gray-900/20";
};

const getActivityIcon = (type: string) => {
  const icons: Record<string, string> = {
    CREATE_CASE: "material-symbols:task-outlined",
    INVITE_USER: "material-symbols:person-add",
    UPLOAD_DOCUMENT: "material-symbols:upload",
    UPDATE_CASE: "material-symbols:edit",
    LOGIN: "material-symbols:login",
  };
  return icons[type] || "material-symbols:help";
};

const getActivityIconColor = (type: string) => {
  const colors: Record<string, string> = {
    CREATE_CASE: "text-blue-600 dark:text-blue-400",
    INVITE_USER: "text-green-600 dark:text-green-400",
    UPLOAD_DOCUMENT: "text-purple-600 dark:text-purple-400",
    UPDATE_CASE: "text-orange-600 dark:text-orange-400",
    LOGIN: "text-gray-600 dark:text-gray-400",
  };
  return colors[type] || "text-gray-600 dark:text-gray-400";
};


const tenantUsers = ref([])

const manageUsers = () => {
  navigateTo(`/dashboard/tenants/${tenantId.value}/users`);
};
const inviteUsers = () => {
  navigateTo(`/dashboard/tenants/${tenantId.value}/invite`);
};

const getRoleVariant = (role: string) => {
  const variants: Record<string, string> = {
    [Roles.ADMIN]: 'primary',
    [Roles.LAWYER]: 'success',
    [Roles.PARALEGAL]: 'warning',
    [Roles.CLIENT]: 'neutral',
  }
  return variants[role] || 'neutral'
}

const fetchTenantData = async () => {
  await tenantStore.fetchTenantById(tenantId.value);
  await usersStore.fetchUserActivity({ tenantId: tenantId.value });
  await usersStore.fetchAllUsers({ tenantId: tenantId.value });
  tenantUsers.value = usersStore.users;
}
const { $global } = useNuxtApp()
onMounted(async () => {
  fetchTenantData()
  $global.add('refreshData', fetchTenantData)
});
onUnmounted(() => {
  $global.remove('refreshData', fetchTenantData)
});
</script>
