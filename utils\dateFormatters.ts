/**
 * Centralized Date Formatting Utilities using Day.js
 *
 * This utility provides consistent date formatting across the entire application
 * using the dayjs-nuxt module for better performance and consistency.
 */

import type { Dayjs } from 'dayjs'

// Type definitions for date input
export type DateInput = string | Date | Dayjs | null | undefined

// Common date format patterns
export const DATE_FORMATS = {
  // Standard formats
  DATE_ONLY: 'YYYY-MM-DD',
  DATE_DISPLAY: 'MMM D, YYYY',
  DATE_FULL: 'MMMM D, YYYY',
  DATE_SHORT: 'MM/DD/YYYY',
  
  // Time formats
  TIME_12H: 'h:mm A',
  TIME_24H: 'HH:mm',
  TIME_WITH_SECONDS: 'h:mm:ss A',
  
  // DateTime formats
  DATETIME_DISPLAY: 'MMM D, YYYY h:mm A',
  DATETIME_FULL: 'MMMM D, YYYY h:mm:ss A',
  DATETIME_SHORT: 'MM/DD/YYYY h:mm A',
  DATETIME_ISO: 'YYYY-MM-DDTHH:mm:ss',
  
  // Relative formats
  RELATIVE_SHORT: 'relative',
  RELATIVE_LONG: 'relativeLong',
  
  // Legal document formats
  LEGAL_DATE: 'MMMM D, YYYY',
  LEGAL_DATETIME: 'MMMM D, YYYY [at] h:mm A',
  
  // File/system formats
  FILE_TIMESTAMP: 'YYYY-MM-DD_HH-mm-ss',
  LOG_TIMESTAMP: 'YYYY-MM-DD HH:mm:ss'
} as const

/**
 * Main date formatting function using Day.js
 */
export const formatDate = (
  date: DateInput,
  format: string = DATE_FORMATS.DATE_DISPLAY,
  timezone?: string
): string => {
  // Handle null/undefined
  if (!date) return '-'

  try {
    // Access dayjs from global context (injected by dayjs-nuxt)
    const dayjs = useDayjs()

    if (!dayjs) {
      console.warn('Day.js not available, falling back to native Date')
      return fallbackDateFormat(date, format)
    }

    let dayjsDate = dayjs(date)

    // Apply timezone if specified
    if (timezone && dayjsDate.tz) {
      dayjsDate = dayjsDate.tz(timezone)
    }

    // Handle relative formats
    if (format === DATE_FORMATS.RELATIVE_SHORT) {
      return dayjsDate.fromNow()
    }

    if (format === DATE_FORMATS.RELATIVE_LONG) {
      return dayjsDate.fromNow(true)
    }

    // Standard formatting
    return dayjsDate.format(format)
  } catch (error) {
    console.error('Error formatting date:', error)
    return fallbackDateFormat(date, format)
  }
}

/**
 * Fallback date formatting using native Date API
 */
const fallbackDateFormat = (date: DateInput, format: string): string => {
  if (!date) return '-'
  
  try {
    const dateObj = new Date(date as string | Date)
    
    if (isNaN(dateObj.getTime())) {
      return String(date)
    }
    
    // Basic format mapping for fallback
    switch (format) {
      case DATE_FORMATS.DATE_DISPLAY:
        return dateObj.toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'short',
          day: 'numeric'
        })
      case DATE_FORMATS.DATE_FULL:
        return dateObj.toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        })
      case DATE_FORMATS.DATETIME_DISPLAY:
        return dateObj.toLocaleString('en-US', {
          year: 'numeric',
          month: 'short',
          day: 'numeric',
          hour: 'numeric',
          minute: '2-digit',
          hour12: true
        })
      default:
        return dateObj.toLocaleDateString()
    }
  } catch (error) {
    console.error('Fallback date formatting error:', error)
    return String(date)
  }
}

/**
 * Specific formatting functions for common use cases
 */

// Display formats
export const formatDateDisplay = (date: DateInput): string => 
  formatDate(date, DATE_FORMATS.DATE_DISPLAY)

export const formatDateFull = (date: DateInput): string => 
  formatDate(date, DATE_FORMATS.DATE_FULL)

export const formatDateTime = (date: DateInput): string => 
  formatDate(date, DATE_FORMATS.DATETIME_DISPLAY)

export const formatDateTimeFull = (date: DateInput): string => 
  formatDate(date, DATE_FORMATS.DATETIME_FULL)

// Time formats
export const formatTime = (date: DateInput): string => 
  formatDate(date, DATE_FORMATS.TIME_12H)

export const formatTime24 = (date: DateInput): string => 
  formatDate(date, DATE_FORMATS.TIME_24H)

// Relative formats
export const formatRelativeTime = (date: DateInput): string => 
  formatDate(date, DATE_FORMATS.RELATIVE_SHORT)

export const formatRelativeTimeLong = (date: DateInput): string => 
  formatDate(date, DATE_FORMATS.RELATIVE_LONG)

// Legal document formats
export const formatLegalDate = (date: DateInput): string => 
  formatDate(date, DATE_FORMATS.LEGAL_DATE)

export const formatLegalDateTime = (date: DateInput): string => 
  formatDate(date, DATE_FORMATS.LEGAL_DATETIME)

// System formats
export const formatFileTimestamp = (date: DateInput): string => 
  formatDate(date, DATE_FORMATS.FILE_TIMESTAMP)

export const formatLogTimestamp = (date: DateInput): string => 
  formatDate(date, DATE_FORMATS.LOG_TIMESTAMP)

/**
 * Utility functions for date operations
 */

// Check if date is valid
export const isValidDate = (date: DateInput): boolean => {
  if (!date) return false

  try {
    const dayjs = useDayjs()
    if (dayjs) {
      return dayjs(date).isValid()
    }

    // Fallback to native Date
    const dateObj = new Date(date as string | Date)
    return !isNaN(dateObj.getTime())
  } catch {
    return false
  }
}

// Get timezone-aware date
export const formatDateWithTimezone = (
  date: DateInput,
  format: string = DATE_FORMATS.DATETIME_DISPLAY,
  timezone: string = 'America/New_York'
): string => {
  return formatDate(date, format, timezone)
}

// Duration formatting
export const formatDuration = (
  startDate: DateInput,
  endDate: DateInput = new Date()
): string => {
  if (!startDate) return '-'

  try {
    const dayjs = useDayjs()
    if (!dayjs) return '-'

    const start = dayjs(startDate)
    const end = dayjs(endDate)
    const diffMs = end.diff(start)

    // Calculate duration components
    const days = Math.floor(diffMs / (1000 * 60 * 60 * 24))
    const hours = Math.floor((diffMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
    const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60))

    if (days > 0) {
      return `${days}d ${hours}h ${minutes}m`
    } else if (hours > 0) {
      return `${hours}h ${minutes}m`
    } else {
      return `${minutes}m`
    }
  } catch (error) {
    console.error('Error formatting duration:', error)
    return '-'
  }
}

// Age calculation
export const formatAge = (birthDate: DateInput): string => {
  if (!birthDate) return '-'

  try {
    const dayjs = useDayjs()
    if (!dayjs) return '-'

    const birth = dayjs(birthDate)
    const now = dayjs()
    const years = now.diff(birth, 'year')

    return `${years} year${years === 1 ? '' : 's'}`
  } catch (error) {
    console.error('Error calculating age:', error)
    return '-'
  }
}

export const fromNow = (date: DateInput): string => {
 
  console.log('fromNow', )
  if (!date) return '-'

  try {
    const dayjs = useDayjs()
    if (!dayjs) return '-'
 
    return dayjs(date).fromNow()
  } catch (error) {
    console.error('Error calculating fromNow:', error)
    return '-'
  }
}

/**
 * Export all formatters as a single object for easy importing
 */
export const dateFormatters = {
  formatDate,
  formatDateDisplay,
  formatDateFull,
  formatDateTime,
  formatDateTimeFull,
  formatTime,
  formatTime24,
  formatRelativeTime,
  formatRelativeTimeLong,
  formatLegalDate,
  formatLegalDateTime,
  formatFileTimestamp,
  formatLogTimestamp,
  formatDateWithTimezone,
  formatDuration,
  formatAge,
  fromNow,
  isValidDate
}

// Default export
export default dateFormatters
