# Enhanced Navigation System

The Legal SaaS platform features a sophisticated role-based navigation system with multi-level menu support, JSON configuration, and dynamic role filtering.

## Features

### ✨ **Core Features**
- **Role-Based Access Control** - Menu items filtered by user and tenant roles
- **Multi-Level Menus** - Support for 3 levels of nested navigation
- **JSON Configuration** - External configuration file for easy menu management
- **Dynamic Badges** - Status indicators and counters on menu items
- **Auto-Expansion** - Automatically expands to show active menu items
- **Responsive Design** - Collapsible sidebar with mobile support
- **Smooth Animations** - Elegant transitions and hover effects

### 🔐 **Role System**
- **Platform Roles**: `super_admin`, `admin`, `support`, `lawyer`, `user`
- **Tenant Roles**: `tenant_owner`, `admin`, `lawyer`, `user`
- **Role Hierarchy** - Higher roles inherit permissions from lower roles
- **Tenant Context** - Some menu items require an active tenant

## Usage

### Basic Implementation

```vue
<template>
  <PlatformNavigation 
    :collapsed="false"
    :user-roles="['super_admin']"
    :tenant-roles="['tenant_owner']"
    :active-tenant-id="'tenant-123'"
    @toggle-collapse="handleToggle"
  />
</template>
```

### With Navigation Composable

```typescript
import { useNavigation } from '~/composables/useNavigation'

const navigation = useNavigation({
  userRoles: ['admin', 'lawyer'],
  tenantRoles: ['admin'],
  activeTenantId: 'tenant-456'
})

// Access filtered menu items
const { filteredMenuItems, expandedItems, toggleSubmenu } = navigation
```

## Configuration

### Menu Configuration (`config/navigation.json`)

```json
{
  "menuItems": [
    {
      "id": "dashboard",
      "label": "Dashboard",
      "icon": "heroicons:home",
      "path": "/dashboard",
      "roles": ["user", "admin", "super_admin"],
      "order": 1
    },
    {
      "id": "case-management",
      "label": "Case Management",
      "icon": "heroicons:briefcase",
      "roles": ["lawyer", "admin", "super_admin"],
      "tenantRoles": ["lawyer", "admin", "tenant_owner"],
      "requiresTenant": true,
      "order": 2,
      "children": [
        {
          "id": "cases-list",
          "label": "All Cases",
          "icon": "heroicons:queue-list",
          "path": "/dashboard/cases",
          "badge": { "text": "24", "type": "info" },
          "order": 1
        }
      ]
    }
  ]
}
```

### Menu Item Properties

| Property | Type | Description |
|----------|------|-------------|
| `id` | string | Unique identifier for the menu item |
| `label` | string | Display text for the menu item |
| `icon` | string | Heroicons icon name |
| `path` | string | Route path (optional for parent items) |
| `roles` | string[] | Required platform roles |
| `tenantRoles` | string[] | Required tenant roles |
| `requiresTenant` | boolean | Whether item requires active tenant |
| `badge` | object | Badge configuration with text and type |
| `order` | number | Sort order within parent |
| `children` | array | Child menu items |

### Badge Types

- `success` - Green badge (e.g., active status)
- `warning` - Yellow badge (e.g., pending items)
- `error` - Red badge (e.g., errors or issues)
- `info` - Blue badge (e.g., counts or information)

## Role Hierarchy

### Platform Roles
```
super_admin → admin, support, lawyer, user
admin → lawyer, user
support → user
lawyer → user
user → (base role)
```

### Tenant Roles
```
tenant_owner → admin, lawyer, user
admin → lawyer, user
lawyer → user
user → (base role)
```

## API Reference

### `useNavigation(options)`

#### Options
- `userRoles: string[]` - User's platform roles
- `tenantRoles: string[]` - User's tenant roles
- `activeTenantId: string | null` - Current active tenant ID

#### Returns
- `filteredMenuItems` - Menu items filtered by user permissions
- `expandedItems` - Currently expanded menu item IDs
- `toggleSubmenu(id)` - Toggle submenu expansion
- `expandToPath(path)` - Expand menu to show specific path
- `hasActiveChild(item, path)` - Check if item has active children
- `canAccessItem(item)` - Check if user can access menu item

### Component Props

#### `PlatformNavigation`
- `collapsed?: boolean` - Whether sidebar is collapsed
- `userRoles?: string[]` - User's platform roles
- `tenantRoles?: string[]` - User's tenant roles
- `activeTenantId?: string | null` - Active tenant ID

#### Events
- `@toggle-collapse` - Emitted when collapse button is clicked

## Examples

### Super Admin Navigation
```typescript
// Super admin sees all menu items
const navigation = useNavigation({
  userRoles: ['super_admin'],
  tenantRoles: ['tenant_owner'],
  activeTenantId: 'tenant-123'
})
```

### Lawyer Navigation
```typescript
// Lawyer sees case and document management
const navigation = useNavigation({
  userRoles: ['lawyer'],
  tenantRoles: ['lawyer'],
  activeTenantId: 'tenant-123'
})
```

### Support User Navigation
```typescript
// Support user sees platform admin tools
const navigation = useNavigation({
  userRoles: ['support'],
  tenantRoles: [],
  activeTenantId: null
})
```

## Customization

### Adding New Menu Items

1. Edit `config/navigation.json`
2. Add new menu item with appropriate roles
3. Navigation will automatically update

### Custom Icons

Use any Heroicons icon name:
```json
{
  "icon": "heroicons:chart-bar",
  "icon": "heroicons:document-text",
  "icon": "heroicons:users"
}
```

### Custom Badges

```json
{
  "badge": {
    "text": "New",
    "type": "warning"
  }
}
```

## Best Practices

1. **Role Assignment** - Use the principle of least privilege
2. **Menu Organization** - Group related items under parent menus
3. **Badge Usage** - Use badges sparingly for important information
4. **Icon Consistency** - Use consistent icon styles throughout
5. **Order Values** - Use increments of 10 for easy reordering

## Troubleshooting

### Menu Items Not Showing
- Check user roles match required roles
- Verify tenant context for tenant-specific items
- Check role hierarchy configuration

### Icons Not Loading
- Ensure Heroicons is properly installed
- Verify icon names are correct
- Check icon component registration

### Performance Issues
- Menu items are filtered reactively
- Use `order` property for consistent sorting
- Consider lazy loading for large menu structures
