/**
 * Composable for handling RTL support in Tiptap editor
 */
export const useEditorRTL = () => {
  const { locale } = useI18n()

  // RTL languages
  const rtlLanguages = ['he', 'ar', 'fa', 'ur']
  
  // Check if current locale is RTL
  const isRTL = computed(() => rtlLanguages.includes(locale.value))
  
  // Get text direction
  const textDirection = computed(() => isRTL.value ? 'rtl' : 'ltr')
  
  // Get text alignment based on direction
  const defaultAlignment = computed(() => isRTL.value ? 'right' : 'left')
  
  // CSS classes for RTL support
  const directionClasses = computed(() => ({
    'rtl': isRTL.value,
    'ltr': !isRTL.value,
    'text-right': isRTL.value,
    'text-left': !isRTL.value
  }))
  
  // Editor props for RTL
  const editorProps = computed(() => ({
    attributes: {
      dir: textDirection.value,
      class: `prose prose-sm sm:prose lg:prose-lg xl:prose-2xl mx-auto focus:outline-none ${isRTL.value ? 'rtl' : 'ltr'}`
    }
  }))
  
  // Apply RTL styles to editor element
  const applyRTLStyles = (element: HTMLElement) => {
    if (!element) return
    
    element.style.direction = textDirection.value
    element.style.textAlign = defaultAlignment.value
    
    // Add RTL classes
    element.classList.remove('rtl', 'ltr')
    element.classList.add(textDirection.value)
  }
  
  // Get localized placeholder text
  const getLocalizedPlaceholder = (key: string) => {
    const { t } = useI18n()
    return t(key)
  }
  
  return {
    isRTL: readonly(isRTL),
    textDirection: readonly(textDirection),
    defaultAlignment: readonly(defaultAlignment),
    directionClasses: readonly(directionClasses),
    editorProps: readonly(editorProps),
    applyRTLStyles,
    getLocalizedPlaceholder,
    rtlLanguages
  }
}
