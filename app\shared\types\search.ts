/**
 * Search Types and Interfaces
 *
 * Comprehensive type definitions for the global search functionality
 */

import type { Ref, ComputedRef } from 'vue'
import type {
  SearchEntityType,
  GlobalSearchResultItem,
  GlobalSearchState,
  SearchViewMode,
  SearchSortOption,
  GlobalSearchFilterConfig
} from './core'

// Re-export core search types for external use
export type {
  SearchEntityType,
  GlobalSearchResultItem,
  GlobalSearchState,
  SearchViewMode,
  SearchSortOption,
  GlobalSearchFilterConfig
} from './core'

// ============================================================================
// SEARCH RESULT ENTITY TYPES
// ============================================================================

/**
 * Case search result
 */
export interface CaseSearchResult extends GlobalSearchResultItem {
  type: 'cases'
  client: string
  caseType: string
  status: 'active' | 'closed' | 'pending' | 'on-hold'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  assignedLawyer?: string
  dueDate?: string
  courtDate?: string
}

/**
 * Document search result
 */
export interface DocumentSearchResult extends GlobalSearchResultItem {
  type: 'documents'
  fileName: string
  fileType: string
  fileSize: number
  category: string
  caseId?: string
  caseName?: string
  uploadedBy: string
  isConfidential: boolean
  version?: number
}

/**
 * User search result
 */
export interface UserSearchResult extends GlobalSearchResultItem {
  type: 'users'
  email: string
  role: string
  tenantName?: string
  department?: string
  isActive: boolean
  lastLogin?: string
  avatar?: string
}

/**
 * Client search result
 */
export interface ClientSearchResult extends GlobalSearchResultItem {
  type: 'clients'
  email?: string
  phone?: string
  company?: string
  address?: string
  clientType: 'individual' | 'business'
  activeCases: number
  totalCases: number
}

/**
 * Template search result
 */
export interface TemplateSearchResult extends GlobalSearchResultItem {
  type: 'templates'
  category: string
  templateType: 'document' | 'email' | 'contract' | 'form'
  isPublic: boolean
  usageCount: number
  lastUsed?: string
  createdBy: string
}

// ============================================================================
// SEARCH CONFIGURATION
// ============================================================================

/**
 * Search entity configuration
 */
export interface SearchEntityConfig {
  type: SearchEntityType
  label: string
  icon: string
  color: string
  searchFields: string[]
  displayFields: string[]
  sortOptions: SearchSortOption[]
  filters: GlobalSearchFilterConfig[]
}

/**
 * Search result display configuration
 */
export interface SearchResultDisplayConfig {
  showThumbnail: boolean
  showMetadata: boolean
  showTags: boolean
  showActions: boolean
  compactMode: boolean
  highlightMatches: boolean
}

// ============================================================================
// SEARCH API TYPES
// ============================================================================

/**
 * Global search API request
 */
export interface GlobalSearchRequest {
  query: string
  types?: SearchEntityType[]
  filters?: Record<string, any>
  sort?: {
    field: string
    direction: 'asc' | 'desc'
  }
  pagination?: {
    page: number
    limit: number
  }
  highlight?: boolean
  facets?: boolean
}

/**
 * Global search API response
 */
export interface GlobalSearchResponse {
  success: boolean
  data: {
    results: GlobalSearchResultItem[]
    total: number
    totalByType: Record<SearchEntityType, number>
    facets?: Record<string, Array<{ value: string; count: number }>>
    suggestions?: string[]
    processingTime: number
  }
  pagination?: {
    page: number
    limit: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
}

// ============================================================================
// SEARCH COMPOSABLE TYPES
// ============================================================================

/**
 * Search composable options
 */
export interface UseGlobalSearchOptions {
  initialQuery?: string
  initialTypes?: SearchEntityType[]
  initialFilters?: Record<string, any>
  initialSort?: string
  initialViewMode?: SearchViewMode
  debounceMs?: number
  autoSearch?: boolean
  persistState?: boolean
}

/**
 * Search composable return type
 */
export interface UseGlobalSearchReturn {
  // State
  state: Ref<GlobalSearchState>
  
  // Computed
  isSearching: ComputedRef<boolean>
  hasResults: ComputedRef<boolean>
  isEmpty: ComputedRef<boolean>
  resultsByType: ComputedRef<Record<SearchEntityType, GlobalSearchResultItem[]>>
  
  // Methods
  search: (query?: string) => Promise<void>
  clearSearch: () => void
  setQuery: (query: string) => void
  setTypes: (types: SearchEntityType[]) => void
  setFilters: (filters: Record<string, any>) => void
  setSortBy: (sortBy: string) => void
  setViewMode: (viewMode: SearchViewMode) => void
  toggleType: (type: SearchEntityType) => void
  addFilter: (key: string, value: any) => void
  removeFilter: (key: string) => void
  clearFilters: () => void
  
  // Utilities
  getResultUrl: (result: GlobalSearchResultItem) => string
  getResultIcon: (result: GlobalSearchResultItem) => string
  getResultColor: (result: GlobalSearchResultItem) => string
  formatResultDate: (date: string) => string
}

// ============================================================================
// SEARCH COMPONENT PROPS
// ============================================================================

/**
 * Global search results component props
 */
export interface GlobalSearchResultsProps {
  query?: string
  types?: SearchEntityType[]
  filters?: Record<string, any>
  viewMode?: SearchViewMode
  sortBy?: string
  showFilters?: boolean
  showTypeToggle?: boolean
  showViewToggle?: boolean
  showSortOptions?: boolean
  compactMode?: boolean
  maxResults?: number
}

/**
 * Search result item component props
 */
export interface SearchResultItemProps {
  result: GlobalSearchResultItem
  viewMode: SearchViewMode
  showThumbnail?: boolean
  showMetadata?: boolean
  showTags?: boolean
  showActions?: boolean
  highlightQuery?: string
  compactMode?: boolean
}

// ============================================================================
// SEARCH EVENTS
// ============================================================================

/**
 * Search result item events
 */
export interface SearchResultItemEvents {
  click: (result: GlobalSearchResultItem) => void
  action: (action: string, result: GlobalSearchResultItem) => void
  favorite: (result: GlobalSearchResultItem) => void
  share: (result: GlobalSearchResultItem) => void
}

/**
 * Global search events
 */
export interface GlobalSearchEvents {
  search: (query: string) => void
  'type-change': (types: SearchEntityType[]) => void
  'filter-change': (filters: Record<string, any>) => void
  'sort-change': (sortBy: string) => void
  'view-change': (viewMode: SearchViewMode) => void
  'result-click': (result: GlobalSearchResultItem) => void
  'clear-search': () => void
}

// ============================================================================
// UTILITY TYPES
// ============================================================================

/**
 * Search highlight match
 */
export interface SearchHighlight {
  field: string
  matches: Array<{
    text: string
    highlighted: boolean
  }>
}

/**
 * Search suggestion
 */
export interface SearchSuggestion {
  text: string
  type: 'query' | 'filter' | 'entity'
  count?: number
}

/**
 * Search facet
 */
export interface SearchFacet {
  field: string
  values: Array<{
    value: string
    count: number
    selected?: boolean
  }>
}
