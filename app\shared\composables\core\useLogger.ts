/**
 * Enhanced Logger Composable
 * 
 * Comprehensive logging system with levels, formatting, persistence,
 * and performance monitoring
 */

import { ref, computed } from 'vue'
import type { ISODateString } from '@shared/types/core'

// ============================================================================
// LOGGER TYPES
// ============================================================================

export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
  FATAL = 4
}

export interface LogEntry {
  id: string
  timestamp: ISODateString
  level: LogLevel
  logger: string
  message: string
  data?: any
  error?: Error // Already optional, which is good
  context?: LogContext
  performance?: PerformanceEntry
}

export interface LogContext {
  userId?: string
  sessionId?: string
  requestId?: string
  component?: string
  action?: string
  metadata?: Record<string, any>
}

export interface LoggerConfig {
  level: LogLevel
  enabled: boolean
  console: boolean
  persist: boolean
  maxEntries: number
  format: 'json' | 'text'
  includeStackTrace: boolean
  performance: boolean
}

export interface PerformanceEntry {
  startTime: number
  endTime?: number
  duration?: number
  memory?: {
    used: number
    total: number
  }
}

// ============================================================================
// GLOBAL LOGGER STATE
// ============================================================================

const globalConfig = ref<LoggerConfig>({
  level: process.env.NODE_ENV === 'development' ? LogLevel.DEBUG : LogLevel.INFO,
  enabled: true,
  console: true,
  persist: process.env.NODE_ENV === 'production',
  maxEntries: 1000,
  format: 'json',
  includeStackTrace: process.env.NODE_ENV === 'development',
  performance: true
})

const logEntries = ref<LogEntry[]>([])
const loggerInstances = ref(new Map<string, any>())

// ============================================================================
// LOGGER IMPLEMENTATION
// ============================================================================

export function useLogger(name: string, config: Partial<LoggerConfig> = {}) {
  // ============================================================================
  // CONFIGURATION
  // ============================================================================
  
  const loggerConfig = computed(() => ({
    ...globalConfig.value,
    ...config
  }))
  
  // ============================================================================
  // STATE
  // ============================================================================
  
  const context = ref<LogContext>({})
  const performanceMarks = ref(new Map<string, PerformanceEntry>())
  
  // ============================================================================
  // CORE LOGGING METHODS
  // ============================================================================
  
  const log = (level: LogLevel, message: string, data?: any, error?: Error) => {
    if (!loggerConfig.value.enabled || level < loggerConfig.value.level) {
      return
    }
    
    const entryBase: Omit<LogEntry, 'error' | 'performance'> = {
      id: generateLogId(),
      timestamp: new Date().toISOString() as ISODateString,
      level,
      logger: name,
      message,
      data,
      context: { ...context.value }
    };

    const entry: LogEntry = { ...entryBase };
    if (error) {
      entry.error = error;
    }
    
    // Add performance data if available
    if (loggerConfig.value.performance && typeof performance !== 'undefined') {
      const memUsage = getMemoryUsage();
      const perfEntry: PerformanceEntry = {
        startTime: performance.now()
      };
      if (memUsage) {
        perfEntry.memory = { used: Number(memUsage.used), total: Number(memUsage.total) };
      }
      entry.performance = perfEntry;
    }
    
    // Add to log entries
    addLogEntry(entry)
    
    // Output to console
    if (loggerConfig.value.console) {
      outputToConsole(entry)
    }
    
    // Persist if enabled
    if (loggerConfig.value.persist) {
      persistLogEntry(entry)
    }
  }
  
  const debug = (message: string, data?: any) => {
    log(LogLevel.DEBUG, message, data)
  }
  
  const info = (message: string, data?: any) => {
    log(LogLevel.INFO, message, data)
  }
  
  const warn = (message: string, data?: any) => {
    log(LogLevel.WARN, message, data)
  }
  
  const error = (message: string, data?: any, error?: Error) => {
    log(LogLevel.ERROR, message, data, error)
  }
  
  const fatal = (message: string, data?: any, error?: Error) => {
    log(LogLevel.FATAL, message, data, error)
  }
  
  // ============================================================================
  // PERFORMANCE LOGGING
  // ============================================================================
  
  const startPerformance = (operation: string): string => {
    const markId = `${name}_${operation}_${Date.now()}`
    
    const memUsage = getMemoryUsage();
    const entry: PerformanceEntry = {
      startTime: performance.now()
    };
    if (memUsage) {
      entry.memory = { used: Number(memUsage.used), total: Number(memUsage.total) };
    }
    
    performanceMarks.value.set(markId, entry)
    
    if (typeof performance !== 'undefined' && performance.mark) {
      performance.mark(`${markId}_start`)
    }
    
    debug(`Performance tracking started: ${operation}`, { markId })
    return markId
  }
  
  const endPerformance = (markId: string, message?: string): PerformanceEntry | null => {
    const entry = performanceMarks.value.get(markId)
    if (!entry) {
      warn(`Performance mark not found: ${markId}`)
      return null
    }
    
    const endTime = performance.now()
    const duration = endTime - entry.startTime
    
    entry.endTime = endTime
    entry.duration = duration
    
    if (typeof performance !== 'undefined' && performance.mark && performance.measure) {
      performance.mark(`${markId}_end`)
      performance.measure(markId, `${markId}_start`, `${markId}_end`)
    }
    
    const logMessage = message || `Performance measurement completed`
    info(logMessage, {
      markId,
      duration: `${duration.toFixed(2)}ms`,
      memory: entry.memory
    })
    
    performanceMarks.value.delete(markId)
    return entry
  }
  
  const measureAsync = async <T>(
    operation: string,
    fn: () => Promise<T>
  ): Promise<{ result: T; performance: PerformanceEntry }> => {
    const markId = startPerformance(operation)
    
    try {
      const result = await fn()
      const performanceEntry = endPerformance(markId, `Async operation completed: ${operation}`)
      
      return {
        result,
        performance: performanceEntry!
      }
    } catch (error) {
      endPerformance(markId, `Async operation failed: ${operation}`)
      throw error
    }
  }
  
  const measureSync = <T>(
    operation: string,
    fn: () => T
  ): { result: T; performance: PerformanceEntry } => {
    const markId = startPerformance(operation)
    
    try {
      const result = fn()
      const performanceEntry = endPerformance(markId, `Sync operation completed: ${operation}`)
      
      return {
        result,
        performance: performanceEntry!
      }
    } catch (error) {
      endPerformance(markId, `Sync operation failed: ${operation}`)
      throw error
    }
  }
  
  // ============================================================================
  // CONTEXT MANAGEMENT
  // ============================================================================
  
  const setContext = (newContext: Partial<LogContext>) => {
    context.value = { ...context.value, ...newContext }
  }
  
  const clearContext = () => {
    context.value = {}
  }
  
  const withContext = <T>(contextData: Partial<LogContext>, fn: () => T): T => {
    const originalContext = { ...context.value }
    setContext(contextData)
    
    try {
      return fn()
    } finally {
      context.value = originalContext
    }
  }
  
  // ============================================================================
  // UTILITY FUNCTIONS
  // ============================================================================
  
  const generateLogId = (): string => {
    return `log_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }
  
  const getMemoryUsage = () => {
    if (typeof performance !== 'undefined' && (performance as any).memory) {
      const memory = (performance as any).memory
      return {
        used: Number(memory.usedJSHeapSize), // Cast to number
        total: Number(memory.totalJSHeapSize) // Cast to number
      };
    }
    return undefined
  }
  
  const addLogEntry = (entry: LogEntry) => {
    logEntries.value.push(entry)
    
    // Maintain max entries limit
    if (logEntries.value.length > loggerConfig.value.maxEntries) {
      logEntries.value.shift()
    }
  }
  
  const outputToConsole = (entry: LogEntry) => {
    const levelNames = ['DEBUG', 'INFO', 'WARN', 'ERROR', 'FATAL']
    const levelColors = ['#888', '#007acc', '#ff8c00', '#ff4444', '#cc0000']
    
    const timestamp = new Date(entry.timestamp).toLocaleTimeString()
    const levelName = levelNames[entry.level]
    const color = levelColors[entry.level]
    
    const prefix = `%c[${timestamp}] ${levelName} [${entry.logger}]`
    const style = `color: ${color}; font-weight: bold;`
    
    const args = [prefix, style, entry.message]
    
    if (entry.data) {
      args.push('\nData:', entry.data)
    }
    
    if (entry.error) {
      args.push('\nError:', entry.error.message); // Push message string
      
      if (loggerConfig.value.includeStackTrace && entry.error.stack) {
        args.push('\nStack:', entry.error.stack);
      }
    }
    
    if (entry.context && Object.keys(entry.context).length > 0) {
      args.push('\nContext:', JSON.stringify(entry.context, null, 2)); // Stringify context
    }
    
    if (entry.performance) {
      args.push('\nPerformance:', JSON.stringify(entry.performance, null, 2)); // Stringify performance
    }
    
    // Use appropriate console method
    switch (entry.level) {
      case LogLevel.DEBUG:
        console.debug(...args)
        break
      case LogLevel.INFO:
        console.info(...args)
        break
      case LogLevel.WARN:
        console.warn(...args)
        break
      case LogLevel.ERROR:
      case LogLevel.FATAL:
        console.error(...args)
        break
    }
  }
  
  const persistLogEntry = (entry: LogEntry) => {
    try {
      if (typeof window === 'undefined') return
      
      const storageKey = 'legal_saas_logs'
      const existingLogs = localStorage.getItem(storageKey)
      const logs = existingLogs ? JSON.parse(existingLogs) : []
      
      logs.push(entry)
      
      // Maintain storage limit
      if (logs.length > loggerConfig.value.maxEntries) {
        logs.splice(0, logs.length - loggerConfig.value.maxEntries)
      }
      
      localStorage.setItem(storageKey, JSON.stringify(logs))
    } catch (error) {
      console.warn('Failed to persist log entry:', error)
    }
  }
  
  // ============================================================================
  // LOG MANAGEMENT
  // ============================================================================
  
  const getEntries = (filter?: {
    level?: LogLevel
    logger?: string
    startTime?: Date
    endTime?: Date
  }) => {
    let filtered = [...logEntries.value]
    
    if (filter) {
      if (filter.level !== undefined) {
        filtered = filtered.filter(entry => entry.level >= filter.level!)
      }
      
      if (filter.logger) {
        filtered = filtered.filter(entry => entry.logger === filter.logger)
      }
      
      if (filter.startTime) {
        filtered = filtered.filter(entry => 
          new Date(entry.timestamp) >= filter.startTime!
        )
      }
      
      if (filter.endTime) {
        filtered = filtered.filter(entry => 
          new Date(entry.timestamp) <= filter.endTime!
        )
      }
    }
    
    return filtered
  }
  
  const clearEntries = () => {
    logEntries.value = []
    
    if (typeof window !== 'undefined') {
      localStorage.removeItem('legal_saas_logs')
    }
  }
  
  const exportLogs = (format: 'json' | 'csv' = 'json') => {
    const entries = getEntries()
    
    if (format === 'json') {
      return JSON.stringify(entries, null, 2)
    } else {
      // CSV format
      const headers = ['timestamp', 'level', 'logger', 'message', 'data']
      const rows = entries.map(entry => [
        entry.timestamp,
        LogLevel[entry.level],
        entry.logger,
        entry.message,
        entry.data ? JSON.stringify(entry.data) : ''
      ])
      
      return [headers, ...rows]
        .map(row => row.map(cell => `"${cell}"`).join(','))
        .join('\n')
    }
  }
  
  // ============================================================================
  // LOGGER INSTANCE MANAGEMENT
  // ============================================================================
  
  const instance = {
    name,
    debug,
    info,
    warn,
    error,
    fatal,
    startPerformance,
    endPerformance,
    measureAsync,
    measureSync,
    setContext,
    clearContext,
    withContext,
    getEntries,
    clearEntries,
    exportLogs
  }
  
  // Register instance
  loggerInstances.value.set(name, instance)
  
  return instance
}

// ============================================================================
// GLOBAL LOGGER UTILITIES
// ============================================================================

export function configureLogger(config: Partial<LoggerConfig>) {
  globalConfig.value = { ...globalConfig.value, ...config }
}

export function getLoggerInstance(name: string) {
  return loggerInstances.value.get(name)
}

export function getAllLogEntries() {
  return [...logEntries.value]
}

export function clearAllLogs() {
  logEntries.value = []
  
  if (typeof window !== 'undefined') {
    localStorage.removeItem('legal_saas_logs')
  }
}

export function getLoggerMetrics() {
  const entries = getAllLogEntries()
  const now = Date.now()
  const oneHourAgo = now - 60 * 60 * 1000
  
  const recentEntries = entries.filter(entry => 
    new Date(entry.timestamp).getTime() > oneHourAgo
  )
  
  const levelCounts = {
    debug: 0,
    info: 0,
    warn: 0,
    error: 0,
    fatal: 0
  }
  
  recentEntries.forEach(entry => {
    switch (entry.level) {
      case LogLevel.DEBUG: levelCounts.debug++; break
      case LogLevel.INFO: levelCounts.info++; break
      case LogLevel.WARN: levelCounts.warn++; break
      case LogLevel.ERROR: levelCounts.error++; break
      case LogLevel.FATAL: levelCounts.fatal++; break
    }
  })
  
  return {
    totalEntries: entries.length,
    recentEntries: recentEntries.length,
    levelCounts,
    loggers: Array.from(loggerInstances.value.keys()),
    memoryUsage: getMemoryUsage()
  }
}

// Helper function for memory usage
function getMemoryUsage() {
  if (typeof performance !== 'undefined' && (performance as any).memory) {
    const memory = (performance as any).memory
    return {
      used: memory.usedJSHeapSize,
      total: memory.totalJSHeapSize,
      limit: memory.jsHeapSizeLimit
    }
  }
  return null
}
