// server/api/auth/login.post.ts

// These imports are standard for Nuxt 3 server routes.
// If TypeScript shows errors, it's likely an environment/IDE issue.
// The code should still attempt to run correctly in a Nuxt environment.
import { 
  defineEvent<PERSON><PERSON><PERSON>, 
  readBody, 
  setC<PERSON>ie, 
  create<PERSON>rror, 
  append<PERSON><PERSON>er, 
  H3Event 
} from 'h3'; 
import { useRuntimeConfig } from '#imports';

// Ensure this file exists at `your-nuxt-project/server/constants.ts` (or .js)
// and exports ACCESS_TOKEN_COOKIE.
import { ACCESS_TOKEN_COOKIE } from '~/server/constants'; 

const accessTokenCookieOptions = {
  httpOnly: true,
  secure: process.env.NODE_ENV === 'production',
  sameSite: process.env.NODE_ENV === 'production' ? 'strict' as const : 'lax' as const,
  path: '/',
  maxAge: 60 * 15, // 15 minutes
};

export default defineEventHandler(async (event: H3Event) => {
  // useRuntimeConfig and $fetch are globally available in Nuxt 3 server context.
  // TypeScript errors here might not prevent runtime execution.
  const config = useRuntimeConfig();
  const body = await readBody(event); 

  try {
    const apiBase = config.public.apiBase;
    if (!apiBase) {
      console.error('[Nuxt Login Proxy] CRITICAL: apiBase is not configured in Nuxt runtimeConfig.public.');
      throw createError({ statusCode: 500, statusMessage: 'Proxy configuration error: API base URL not set.' });
    }

    // $fetch is globally available.
    const rawBackendResponse = await $fetch.raw<{ accessToken: string, user: any }>(
      `${apiBase}/auth/login`,
      {
        method: 'POST',
        body: body,
      }
    );

    const backendData = rawBackendResponse._data;
    if (!backendData || !backendData.accessToken || !backendData.user) {
      throw createError({ statusCode: 500, statusMessage: 'Main backend did not return expected data.' });
    }
    const { accessToken, user } = backendData;

    const backendSetCookieHeaders = rawBackendResponse.headers.getSetCookie();
    if (backendSetCookieHeaders && backendSetCookieHeaders.length > 0) {
      backendSetCookieHeaders.forEach((cookieHeader: string) => {
        let modifiedCookieHeader = cookieHeader;
        // Check if this is the refresh_token cookie and adjust its path
        if (cookieHeader.toLowerCase().startsWith('refresh_token=')) {
          // Replace Path=/auth/refresh (or other specific path from backend) with Path=/api/
          // This makes the refresh_token cookie available to all /api/* paths on this domain.
          // It allows apiInstance.ts (used in /api/* routes) and /api/auth/refresh-token to access it,
          // while being more secure than Path=/.
          // The \b ensures we match /auth/refresh and not /auth/refreshsomething else.
          // We also match if the backend sends Path=/ (less likely for refresh_token but possible)
          // or if it sends no Path attribute at all for the refresh_token.
          if (cookieHeader.match(/Path=\/[^;]*/i)) { // If a Path attribute exists
            modifiedCookieHeader = cookieHeader.replace(/Path=\/[^;]*/i, 'Path=/api/');
          } else { // If no Path attribute, add Path=/api/
            // Ensure we add it before other attributes like HttpOnly or SameSite
            modifiedCookieHeader = cookieHeader.replace(/(;|$)/, '; Path=/api/$1');
          }
          
         
        }
        appendHeader(event, 'Set-Cookie', modifiedCookieHeader);
        
      });
    } else {
      console.log('[Nuxt Login Proxy] No Set-Cookie headers received from backend to relay.');
    }

    setCookie(event, ACCESS_TOKEN_COOKIE, accessToken, accessTokenCookieOptions);
    

    return { user: user, message: 'Login successful' };

  } catch (error: any) {
    console.error('[Nuxt Login Proxy] Error:', error.response?.data || error.message || error);
    setCookie(event, ACCESS_TOKEN_COOKIE, '', { ...accessTokenCookieOptions, maxAge: -1 });
    
    throw createError({
      statusCode: error.response?.status || error.statusCode || 500,
      statusMessage: error.response?.data?.message || error.message || 'Login failed.',
      data: error.response?.data || error.data,
    });
  }
});