<template>
  <div class="space-y-6 py-6">
  <ui-alert v-if="formHasErrors" type="error" :title="$t('Please review the form and correct the errors.')" />
    <!-- Main Content -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- Invite Form -->
      <div class="lg:col-span-2">
        <UiCard :title="$t('tenantDetail.sendInvitations')" :subtitle="$t('tenantDetail.inviteUsersDescription')"
          icon="fluent:send-person-16-regular" icon-class="text-green-600 dark:text-green-400" class="py-6">
          <template #header>
            <div class="flex items-center justify-between">
              <UiBadge v-if="pendingInvites.length > 0" color="warning" variant="outline">
                {{ pendingInvites.length }} pending
              </UiBadge>
            </div>
          </template>
 
          <forms-form ref="inviteFormRef" :schema="inviteFormSchema" @submit="handleSubmit" @errors="handleErrors" :show-errors="false" />
        </UiCard>
      </div>

      <!-- Sidebar -->
      <div class="space-y-6">
        <!-- Current Users -->
        <UiCard :title="$t('tenantDetail.currentUsers')" :subtitle="$t('tenantDetail.currentUsersDescription')"
          icon="i-heroicons:users" icon-class="text-blue-600 dark:text-blue-400">
          <template #header>
            <div class="flex items-center justify-between">
              <UiBadge variant="neutral">{{ currentUsers.length }}</UiBadge>
            </div>
          </template>
          <div class="space-y-3">
            <div v-for="user in currentUsers.slice(0, 5)" :key="user.id"
              class="flex items-center gap-3 p-2 rounded-lg bg-gray-50 dark:bg-gray-700/50">
              <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center">
                <Icon name="heroicons:user-circle" size="calc(var(--spacing) * 7)"
                  class="text-blue-600 dark:text-blue-400" />
              </div>
              <div class="flex-1 min-w-0">
                <p class="text-sm font-medium text-gray-900 dark:text-white truncate">
                  {{ user.name }}
                </p>
                <p class="text-xs text-gray-500 dark:text-gray-400">
                  {{ user.role }}
                </p>
              </div>
            </div>
            <div v-if="currentUsers.length === 0" class="text-center py-4">
              <p class="text-sm text-gray-500 dark:text-gray-400">No users yet</p>
            </div>
            <div v-if="currentUsers.length > 5" class="text-center pt-2">
              <UiButton variant="ghost" size="sm" @click="viewTenantUsers">
                View All {{ currentUsers.length }} Users
              </UiButton>
            </div>
          </div>
        </UiCard>

        <!-- Pending Invitations -->
        <UiCard :title="$t('tenantDetail.pendingInvitations')"
          :subtitle="$t('tenantDetail.pendingInvitationsDescription')" icon="material-symbols:schedule-send-outline"
          :icon-props="{ color: 'gray', background: 'warning' }">
          <template #header>
            <div class="flex items-center justify-between">
              <UiBadge size="xs" variant="outline" color="warning">{{
                pendingInvites.length
              }}</UiBadge>
            </div>
          </template>
          <div class="space-y-3">
            <div v-for="invite in pendingInvites.slice(0, 5)" :key="invite.id"
              class="flex items-center justify-between p-3 bg-yellow-50 dark:bg-yellow-900/10 rounded-lg border border-yellow-200 dark:border-yellow-800">
              <div class="flex-1 min-w-0">
                <p class="text-sm font-medium text-gray-900 dark:text-white truncate">
                  {{ invite.email }}
                </p>
                <p class="text-xs text-gray-500 dark:text-gray-400">
                  {{ invite.role }} • {{ formatDate(invite.sentAt) }}
                </p>
              </div>
              <div class="flex items-center gap-2">
                <UiButton variant="ghost" size="sm" shape="circle" @click="resendInvite(invite.id)"
                  aria-label="Resend invitation">
                  <Icon name="material-symbols:refresh" class="h-4 w-4" />
                </UiButton>
                <UiButton variant="ghost" size="sm" shape="circle" @click="cancelInvite(invite.id)"
                  aria-label="Cancel invitation">
                  <Icon name="material-symbols:close" class="h-4 w-4" />
                </UiButton>
              </div>
            </div>
            <div v-if="pendingInvites.length === 0" class="text-center py-4">
              <p class="text-sm text-gray-500 dark:text-gray-400">
                No pending invitations
              </p>
            </div>
          </div>
        </UiCard>
      </div>
    </div>

 
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useTenantStore, type TenantInvitePayload, type Tenant } from '~/stores/tenant'
import { PlatformRoles, TenantRoles } from '~/app/features/auth/constants/roles'
import { useUserStore } from '~/stores/user'
import { z } from 'zod'

import ImportEmailsCsv from '~/components/tenants/ImportEmailsCsv.vue'

import { useGlobalModal, ComponentSize } from '~/composables/useGlobalModal'
import { useModalComponentAccess, createModalWithComponentAccess } from '~/composables/useModalComponentAccess'

const { openModal } = useGlobalModal()
const {
  getModalComponentData,
  callModalComponentMethod,
  whenModalComponentReady
} = useModalComponentAccess()


// Page meta
definePageMeta({
  layout: 'dashboard',
  showPageHeader: true,
  showPageHeaderTitle: true,
  pageHeaderItemStats: () => {
    const tenant = useTenantStore().selectedTenant;
    if (!tenant) return '';

    return tenant.isActive ? { key: "active", label: "Active", color: "green" } : { key: "inactive", label: "Inactive", color: "red" };
  },
  title: () => {
    const tenant = useTenantStore().selectedTenant;
    if (!tenant) return '';
    return 'Invite Users to ' + tenant.name;
  },
  description: 'Send invitations to new users and manage their roles',
  pageHeaderIcon: 'i-heroicons:user-plus',
  pageHeaderActions: () => {
    const tenant = useTenantStore().selectedTenant;
    if (!tenant) return [];
    return [
      {
        label: "Send invitations",
        action: () => router.push({ name: 'dashboard.tenants.invite' }),
        icon: "material-symbols:send-rounded",
        disabled: !tenant.isActive,
        disabledText: "Tenant is not active",
        click() {
          const { $global } = useNuxtApp()
          const form = ($global as any).get('inviteFormRef')
          form?.submitForm()
        }
      },
      {
        label: "Reset",
        action: () => router.push({ name: 'dashboard.tenants.invite' }),
        icon: "material-symbols:refresh",
        disabled: !tenant.isActive,
        disabledText: "Tenant is not active",
        variant: "secondary",
          click() {
          const { $global } = useNuxtApp()
          const form = ($global as any).get('inviteFormRef')
          form?.resetForm()
        }
      },
      {
        label: "Back to Tenant",
        action: () => router.push({ name: 'dashboard.tenants.id', params: { id: tenant.id } }),
        icon: "i-heroicons:arrow-left",
        variant: "flat"
      },
    ].reverse();
  },
  pageHeaderTags: () => {
    const tenant = useTenantStore().selectedTenant;
    if (!tenant) return [];
    return [
      {
        key: "plan",
        label: tenant.plan,
        class: "text-purple-600",
        icon: "i-material-symbols:workspace-premium",
      },
      {
        key: "locale",
        label: tenant.locale,
        class: "text-brandSecondary",
        icon: "i-heroicons:globe-alt",
      },
      {
        key: "timezone",
        label: tenant.timezone,
        class: "text-brandPrimary",
        icon: "i-heroicons:clock",
      },
      {
        key: "createdAt",
        label: formatDate(tenant.createdAt),
        class: "text-brandPrimary",
        icon: "i-material-symbols:calendar-today",
      },
    ];
  },
  middleware: ['rbac'],
  roles: [PlatformRoles.SUPER_ADMIN],
  breadcrumbs: () => {
    const tenant = useTenantStore().selectedTenant;
    if (!tenant) return [];
    return [
      { label: 'Dashboard', href: '/dashboard' },
      { label: 'Tenants', href: '/dashboard/tenants' },
      { label: tenant.name, href: '/dashboard/tenants/' + tenant.id },
      { label: 'Invite Users' },
    ];
  }
})

// Composables
const route = useRoute()
const router = useRouter()
const tenantStore = useTenantStore()

// Get tenant ID from route
const tenantId = computed(() => route.params.id as string)

// Form data interface
interface InviteFormData {
  emails: string[]
  role: string
  personalMessage: string
  sendWelcomeEmail: boolean
  requirePasswordReset: boolean
  notifyOnAccept: boolean
  setExpiration: boolean
  expirationDate: string
}

// Reactive state
const formData = ref<InviteFormData>({
  emails: [''],
  role: '',
  personalMessage: '',
  sendWelcomeEmail: true,
  requirePasswordReset: true,
  notifyOnAccept: true,
  setExpiration: false,
  expirationDate: '',
})
const roleOptions = [
  { value: TenantRoles.ADMIN, label: 'Administrator' },
  { value: TenantRoles.LAWYER, label: 'Lawyer' },
  { value: TenantRoles.PARALEGAL, label: 'Paralegal' },
  { value: TenantRoles.CLIENT, label: 'Client' },
]

const inviteFormSchema = [
  {
    name: "tenantId",
    label: "Tenant ID",
    component: "UiInputHidden",
    rules: z.string().min(1),
    initialValue: tenantId.value,
  },
  {
    name: "emails",
    label: "Email Addresses",
    component: "UiEmailsInput",
    rules: z.array(z.string().email()).min(1),
    props: {
      type: "email",
      placeholder: "Enter email address",
      leadingIcon: "material-symbols:mail",
      helpText: "Add multiple email addresses to send bulk invitations to this tenant.",
      showBulkImport: true,
      bulkImportText: "Import Emails",
    },
    events: () => ({
      bulkImport: () => {
        const modal = createModalWithComponentAccess({
          title: 'Import Email Addresses',
          component: ImportEmailsCsv,
          size: ComponentSize.LG,
          okText: 'Import Emails',
          onOk: async () => {
            // Wait for component to be ready and then access its data/methods
            modal.whenReady((component) => {
              console.log('Modal component is ready:', component)

              // Get component data
              const componentData = modal.getComponentData()
              console.log('Component data:', componentData)

              // Call component methods
              const result = modal.callMethod('getImportedEmails')
              console.log('Imported emails:', result)

              // Get specific properties
              const isValid = modal.getProperty('isValid')
              console.log('Is form valid:', isValid)

              // You can also access the component directly
              if (component.getImportedEmails) {
                const emails = component.getImportedEmails()
                console.log('Direct method call result:', emails)
              }
            })
          },
          onCancel: () => {
             modal.close()
          }
        })
      }
    })
  },
  {
    name: "role",
    label: "Assign Role",
    component: "UiSelect",
    rules: z.string().min(1),
    props: {
      options: roleOptions,
      placeholder: "Select a role for the invited users",
      leadingIcon: "material-symbols:admin-panel-settings",
      helpText: "All invited users will be assigned this role within the tenant",
    }
  },
  {
    name: "personalMessage",
    label: "Personal Message (Optional)",
    component: "UiTextarea",
    rules: z.string().optional().nullable(),
    props: {
      placeholder: "Add a personal welcome message for the invited users...",
      rows: 4,
      leadingIcon: "material-symbols:chat",
      helpText: "This message will be included in the invitation email to provide context",
      autoGrow: true,
      minHeight: 80,
      maxHeight: 400,
      growTransition: true,
    }
  },
  {
    asyncComponent: defineAsyncComponent(() => import("@/components/ui/UiHr.vue")), props: { size: "xs" }
  },
  {
    asyncComponent: defineAsyncComponent(() => import("~/app/shared/components/ui/UiHeader.vue")), props: { tag: "label", text: "Invitation Settings" }
  },
  {
    grid: {
      cols: 2,
      responsive: true,
      content: [
        {
          name: "sendWelcomeEmail",
          label: "Send welcome email after account creation",
          component: "UiCheckbox",
          rules: z.boolean(),
          props: {
            helpText: "Send a welcome email to the new user with login instructions",
          }
        },
        {
          name: "notifyOnAccept",
          label: "Notify me when accepted",
          component: "UiCheckbox",
          rules: z.boolean(),
          initialValue: true,
          props: {
            helpText: "Notify me when invitation is accepted",
          }
        },
        {
          name: "setExpiration",
          label: "Set Expiration Date",
          component: "UiCheckbox",
          rules: z.boolean(),
          props: {
            helpText: "Set an expiration date for the invitation",
          }
        },
      ]
    }
  },

  {
    name: "expirationDate",
    label: "Expiration Date",
    component: "UiInput",
    rules: z.string().optional().nullable(),
    showIf: ({ values }: any) => !!values.setExpiration,
    props: {
      type: "date",
      placeholder: "Select a date",
      helpText: "Select a date for the invitation to expire",
      min: () => {
        const tomorrow = new Date()
        tomorrow.setDate(tomorrow.getDate() + 1)
        return tomorrow.toISOString().split('T')[0]
      },
    }
  },

]



const isSubmitting = ref(false)
const error = ref('')
const successMessage = ref('')
const emailErrors = ref<string[]>([])
const showBulkImport = ref(false)
const bulkEmailText = ref('')

// Mock data (replace with real data from store)
const currentUsers = computed(() => useUserStore().users)

const pendingInvites = computed(() => [])

// Computed properties
const selectedTenant = computed(() => tenantStore.selectedTenant)



const validEmailCount = computed(() => {
  return formData.value.emails.filter(email => email.trim()).length
})

const canSubmit = computed(() => {
  return (
    formData.value.role &&
    validEmailCount.value > 0 &&
    !isSubmitting.value
  )
})

const minExpirationDate = computed(() => {
  const tomorrow = new Date()
  tomorrow.setDate(tomorrow.getDate() + 1)
  return tomorrow.toISOString().split('T')[0]
})


const handleSubmit = async (values: any) => {

  
try {
    await tenantStore.sendInvitesToTenant(values)
  } catch (error) {
    console.error(error)
  }
}



const handleErrors = async (errors: any) => {
  console.log(errors);
  

}

const resetForm = () => {
  formData.value = {
    emails: [''],
    role: '',
    personalMessage: '',
    sendWelcomeEmail: true,
    requirePasswordReset: true,
    notifyOnAccept: true,
    setExpiration: false,
    expirationDate: '',
  }
  emailErrors.value = []
  error.value = ''
  successMessage.value = ''
}

const processBulkEmails = () => {
  if (!bulkEmailText.value.trim()) return

  // Parse emails from text (handle different formats)
  const emails = bulkEmailText.value
    .split(/[\n,;]/)
    .map(email => email.trim())
    .filter(email => email && email.includes('@'))

  // Replace current emails with parsed ones
  formData.value.emails = emails.length > 0 ? emails : ['']

  showBulkImport.value = false
  bulkEmailText.value = ''
}

const previewInvitation = () => {
  // Implementation for previewing invitation
  console.log('Previewing invitation...', formData.value)
}

const goBackToTenant = () => {
  router.push(`/dashboard/tenants/${tenantId.value}`)
}

const viewTenantUsers = () => {
  router.push(`/dashboard/tenants/${tenantId.value}/users`)
}

const editTenantSettings = () => {
  router.push(`/dashboard/tenants/${tenantId.value}/edit`)
}

const viewInviteHistory = () => {
  router.push(`/dashboard/tenants/${tenantId.value}/invites/history`)
}

const resendInvite = async (inviteId: string) => {
  // Implementation for resending invite
  console.log('Resending invite:', inviteId)
}

const cancelInvite = async (inviteId: string) => {
  // Implementation for canceling invite
  console.log('Canceling invite:', inviteId)
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString()
}

const inviteFormRef = ref(null)
const { $global } = useNuxtApp()

const formHasErrors = computed(() => {
  const form = ($global as any).get('inviteFormRef')
  if (!form)
    return false;
  return form.hasErrors
})

// Lifecycle
onMounted(async () => {

  ($global as any).add('inviteFormRef', inviteFormRef)


  await useUserStore().fetchAllUsers({ tenantId: tenantId.value });



  // Fetch tenant details if not already loaded
  if (!selectedTenant.value || selectedTenant.value.id !== tenantId.value) {
    await tenantStore.fetchTenantById(tenantId.value)
  }
})

onUnmounted(() => {
  ($global as any).remove('inviteFormRef')
})

// Watch for tenant store errors
watch(
  () => tenantStore.error,
  (newError) => {
    if (newError) {
      error.value = newError
    }
  }
)

</script>
