<template>
  <div class="space-y-6">
    <!-- Report Generation Section -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- Report Builder -->
      <div class="lg:col-span-2">
        <UiCard>
          <template #header>
            <div class="flex items-center justify-between">
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Generate Report</h3>
              <UiButton variant="ghost" size="sm" @click="handleSaveTemplate">
                <Icon name="material-symbols:save" class="h-4 w-4 mr-2" />
                Save Template
              </UiButton>
            </div>
          </template>

          <form @submit.prevent="handleGenerateReport" class="space-y-6">
            <!-- Report Type -->
            <div class="space-y-4">
              <h4 class="text-md font-medium text-gray-900 dark:text-white">Report Type</h4>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <label v-for="type in reportTypes" :key="type.value"
                       class="relative flex cursor-pointer rounded-lg border p-4 focus:outline-none"
                       :class="selectedReportType === type.value
                         ? 'border-brandPrimary bg-brandPrimary/5'
                         : 'border-gray-300 dark:border-gray-600'">
                  <input
                    type="radio"
                    v-model="selectedReportType"
                    :value="type.value"
                    class="sr-only"
                  />
                  <div class="flex items-center">
                    <div class="flex-shrink-0">
                      <Icon :name="type.icon" class="h-6 w-6"
                            :class="selectedReportType === type.value
                              ? 'text-brandPrimary'
                              : 'text-gray-400'" />
                    </div>
                    <div class="ml-3">
                      <p class="text-sm font-medium text-gray-900 dark:text-white">{{ type.label }}</p>
                      <p class="text-xs text-gray-500 dark:text-gray-400">{{ type.description }}</p>
                    </div>
                  </div>
                </label>
              </div>
            </div>

            <!-- Filters -->
            <div class="space-y-4">
              <h4 class="text-md font-medium text-gray-900 dark:text-white">Filters</h4>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <!-- Date Range -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Date Range
                  </label>
                  <UiSelect
                    v-model="selectedDateRange"
                    :options="dateRangeOptions"
                    placeholder="Select date range"
                  />
                </div>

                <!-- Tenant Selection -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Tenants
                  </label>
                  <UiSelect
                    v-model="selectedTenants"
                    :options="tenantOptions"
                    multiple
                    placeholder="Select tenants (all if none selected)"
                  />
                </div>

                <!-- Status Filter -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Status
                  </label>
                  <UiSelect
                    v-model="selectedStatus"
                    :options="statusOptions"
                    multiple
                    placeholder="Select status"
                  />
                </div>

                <!-- Plan Filter -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Plans
                  </label>
                  <UiSelect
                    v-model="selectedPlans"
                    :options="planOptions"
                    multiple
                    placeholder="Select plans"
                  />
                </div>
              </div>

              <!-- Custom Date Range -->
              <div v-if="selectedDateRange === 'custom'" class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <UiInput
                  v-model="customDateRange.start"
                  type="date"
                  label="Start Date"
                />
                <UiInput
                  v-model="customDateRange.end"
                  type="date"
                  label="End Date"
                />
              </div>
            </div>

            <!-- Output Options -->
            <div class="space-y-4">
              <h4 class="text-md font-medium text-gray-900 dark:text-white">Output Options</h4>

              <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <UiSelect
                  v-model="outputFormat"
                  :options="formatOptions"
                  label="Format"
                />
                <UiCheckbox
                  v-model="includeCharts"
                  label="Include Charts"
                />
                <UiCheckbox
                  v-model="includeRawData"
                  label="Include Raw Data"
                />
              </div>
            </div>

            <!-- Generate Button -->
            <div class="flex items-center justify-between pt-6 border-t border-gray-200 dark:border-gray-700">
              <div class="flex items-center space-x-4">
                <UiButton type="button" variant="outline" @click="handlePreviewReport">
                  <Icon name="material-symbols:preview" class="h-4 w-4 mr-2" />
                  Preview
                </UiButton>
                <UiButton type="button" variant="secondary" @click="handleScheduleReport">
                  <Icon name="material-symbols:schedule" class="h-4 w-4 mr-2" />
                  Schedule
                </UiButton>
              </div>
              <UiButton type="submit" :loading="isGenerating">
                <Icon name="material-symbols:download" class="h-4 w-4 mr-2" />
                Generate Report
              </UiButton>
            </div>
          </form>
        </UiCard>
      </div>

      <!-- Sidebar -->
      <div class="space-y-6">
        <!-- Quick Stats -->
        <UiCard>
          <template #header>
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Report Statistics</h3>
          </template>

          <div class="space-y-4">
            <div class="flex justify-between items-center">
              <span class="text-sm text-gray-500 dark:text-gray-400">Generated Today</span>
              <span class="text-sm font-medium text-gray-900 dark:text-white">{{ reportStats.today }}</span>
            </div>
            <div class="flex justify-between items-center">
              <span class="text-sm text-gray-500 dark:text-gray-400">This Week</span>
              <span class="text-sm font-medium text-gray-900 dark:text-white">{{ reportStats.thisWeek }}</span>
            </div>
            <div class="flex justify-between items-center">
              <span class="text-sm text-gray-500 dark:text-gray-400">This Month</span>
              <span class="text-sm font-medium text-gray-900 dark:text-white">{{ reportStats.thisMonth }}</span>
            </div>
            <div class="flex justify-between items-center">
              <span class="text-sm text-gray-500 dark:text-gray-400">Total Reports</span>
              <span class="text-sm font-medium text-gray-900 dark:text-white">{{ reportStats.total }}</span>
            </div>
          </div>
        </UiCard>

        <!-- Report Templates -->
        <UiCard>
          <template #header>
            <div class="flex items-center justify-between">
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Saved Templates</h3>
              <UiButton variant="ghost" size="sm" @click="handleManageTemplates">
                <Icon name="material-symbols:settings" class="h-4 w-4" />
              </UiButton>
            </div>
          </template>

          <div class="space-y-3">
            <div v-for="template in savedTemplates" :key="template.id"
                 class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <div>
                <p class="text-sm font-medium text-gray-900 dark:text-white">{{ template.name }}</p>
                <p class="text-xs text-gray-500 dark:text-gray-400">{{ template.type }} • {{ template.lastUsed }}</p>
              </div>
              <div class="flex items-center space-x-2">
                <UiButton variant="ghost" size="sm" @click="handleLoadTemplate(template)">
                  <Icon name="material-symbols:play-arrow" class="h-4 w-4" />
                </UiButton>
                <UiButton variant="ghost" size="sm" @click="handleDeleteTemplate(template.id)">
                  <Icon name="material-symbols:delete" class="h-4 w-4" />
                </UiButton>
              </div>
            </div>

            <div v-if="savedTemplates.length === 0" class="text-center py-4">
              <Icon name="material-symbols:description-outline" class="h-8 w-8 text-gray-400 mx-auto mb-2" />
              <p class="text-sm text-gray-500 dark:text-gray-400">No saved templates</p>
            </div>
          </div>
        </UiCard>

        <!-- Scheduled Reports -->
        <UiCard>
          <template #header>
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Scheduled Reports</h3>
          </template>

          <div class="space-y-3">
            <div v-for="scheduled in scheduledReports" :key="scheduled.id"
                 class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <div>
                <p class="text-sm font-medium text-gray-900 dark:text-white">{{ scheduled.name }}</p>
                <p class="text-xs text-gray-500 dark:text-gray-400">{{ scheduled.frequency }} • Next: {{ scheduled.nextRun }}</p>
              </div>
              <div class="flex items-center space-x-2">
                <UiBadge :variant="scheduled.active ? 'success' : 'error'">
                  {{ scheduled.active ? 'Active' : 'Paused' }}
                </UiBadge>
                <UiButton variant="ghost" size="sm" @click="handleToggleSchedule(scheduled.id)">
                  <Icon :name="scheduled.active ? 'material-symbols:pause' : 'material-symbols:play-arrow'" class="h-4 w-4" />
                </UiButton>
              </div>
            </div>

            <div v-if="scheduledReports.length === 0" class="text-center py-4">
              <Icon name="material-symbols:schedule" class="h-8 w-8 text-gray-400 mx-auto mb-2" />
              <p class="text-sm text-gray-500 dark:text-gray-400">No scheduled reports</p>
            </div>
          </div>
        </UiCard>
      </div>
    </div>

    <!-- Recent Reports -->
    <UiCard>
      <template #header>
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Recent Reports</h3>
          <div class="flex items-center space-x-2">
            <UiButton variant="ghost" size="sm" @click="handleRefreshReports">
              <Icon name="material-symbols:refresh" class="h-4 w-4" />
            </UiButton>
            <UiButton variant="outline" size="sm" @click="handleViewAllReports">
              View All
            </UiButton>
          </div>
        </div>
      </template>

      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead class="bg-gray-50 dark:bg-gray-800">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Report
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Type
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Generated
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Size
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Status
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
            <tr v-for="report in recentReports" :key="report.id">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <Icon :name="getReportIcon(report.type)" class="h-5 w-5 text-gray-400 mr-3" />
                  <div>
                    <p class="text-sm font-medium text-gray-900 dark:text-white">{{ report.name }}</p>
                    <p class="text-xs text-gray-500 dark:text-gray-400">{{ report.description }}</p>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <UiBadge variant="info">{{ report.type }}</UiBadge>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                {{ report.generatedAt }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                {{ report.size }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <UiBadge :variant="report.status === 'completed' ? 'success' : report.status === 'failed' ? 'error' : 'warning'">
                  {{ report.status }}
                </UiBadge>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <div class="flex items-center space-x-2">
                  <UiButton v-if="report.status === 'completed'" variant="ghost" size="sm" @click="handleDownloadReport(report)">
                    <Icon name="material-symbols:download" class="h-4 w-4" />
                  </UiButton>
                  <UiButton variant="ghost" size="sm" @click="handleViewReport(report)">
                    <Icon name="material-symbols:visibility" class="h-4 w-4" />
                  </UiButton>
                  <UiButton variant="ghost" size="sm" @click="handleDeleteReport(report.id)">
                    <Icon name="material-symbols:delete" class="h-4 w-4" />
                  </UiButton>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </UiCard>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { PlatformRoles } from '~/app/features/auth/constants/roles'

definePageMeta({
  title: 'Tenant Reports',
  description: 'Generate and manage comprehensive reports for tenant analytics',
  pageHeaderIcon: 'material-symbols:analytics',
  pageHeaderStats: [
    { key: 'reports', label: 'Total Reports', value: 156, color: 'blue' },
    { key: 'scheduled', label: 'Scheduled', value: 8, color: 'green' },
    { key: 'templates', label: 'Templates', value: 12, color: 'purple' },
    { key: 'generated', label: 'Generated Today', value: 3, color: 'yellow' }
  ],
  showRealTimeStatus: true,
  autoRefreshEnabled: true,
  refreshInterval: 300000, // 5 minutes
  isLoading: false,
  showActionsMenu: true,
  showKeyboardShortcuts: true,
  layout: 'dashboard',
  middleware: ['rbac'],
  breadcrumb: [
    { label: 'Dashboard', href: '/dashboard' },
    { label: 'Platform', href: '/dashboard/platform' },
    { label: 'Tenants', href: '/dashboard/platform/tenants' },
    { label: 'Reports' },
  ],
  roles: [PlatformRoles.SUPER_ADMIN, PlatformRoles.SUPPORT],
})

// State
const selectedReportType = ref('usage')
const selectedDateRange = ref('30d')
const selectedTenants = ref<string[]>([])
const selectedStatus = ref<string[]>([])
const selectedPlans = ref<string[]>([])
const outputFormat = ref('pdf')
const includeCharts = ref(true)
const includeRawData = ref(false)
const isGenerating = ref(false)

const customDateRange = ref({
  start: '',
  end: ''
})

// Options
const reportTypes = [
  {
    value: 'usage',
    label: 'Usage Report',
    description: 'Tenant resource usage and limits',
    icon: 'material-symbols:analytics'
  },
  {
    value: 'billing',
    label: 'Billing Report',
    description: 'Revenue and billing analytics',
    icon: 'material-symbols:payments'
  },
  {
    value: 'activity',
    label: 'Activity Report',
    description: 'User activity and engagement',
    icon: 'material-symbols:trending-up'
  },
  {
    value: 'performance',
    label: 'Performance Report',
    description: 'System performance metrics',
    icon: 'material-symbols:speed'
  }
]

const dateRangeOptions = [
  { value: '7d', label: 'Last 7 days' },
  { value: '30d', label: 'Last 30 days' },
  { value: '90d', label: 'Last 90 days' },
  { value: '1y', label: 'Last year' },
  { value: 'custom', label: 'Custom range' }
]

const tenantOptions = [
  { value: 'all', label: 'All Tenants' },
  { value: 'tenant1', label: 'Acme Corp' },
  { value: 'tenant2', label: 'Legal Partners LLC' },
  { value: 'tenant3', label: 'Justice & Associates' }
]

const statusOptions = [
  { value: 'active', label: 'Active' },
  { value: 'inactive', label: 'Inactive' },
  { value: 'suspended', label: 'Suspended' }
]

const planOptions = [
  { value: 'starter', label: 'Starter' },
  { value: 'professional', label: 'Professional' },
  { value: 'enterprise', label: 'Enterprise' }
]

const formatOptions = [
  { value: 'pdf', label: 'PDF' },
  { value: 'excel', label: 'Excel' },
  { value: 'csv', label: 'CSV' },
  { value: 'json', label: 'JSON' }
]

// Mock data
const reportStats = computed(() => ({
  today: 3,
  thisWeek: 12,
  thisMonth: 45,
  total: 156
}))

const savedTemplates = ref([
  {
    id: 1,
    name: 'Monthly Usage Summary',
    type: 'Usage',
    lastUsed: '2 days ago'
  },
  {
    id: 2,
    name: 'Billing Analytics',
    type: 'Billing',
    lastUsed: '1 week ago'
  },
  {
    id: 3,
    name: 'Performance Dashboard',
    type: 'Performance',
    lastUsed: '3 days ago'
  }
])

const scheduledReports = ref([
  {
    id: 1,
    name: 'Weekly Usage Report',
    frequency: 'Weekly',
    nextRun: 'Monday 9:00 AM',
    active: true
  },
  {
    id: 2,
    name: 'Monthly Billing Summary',
    frequency: 'Monthly',
    nextRun: '1st of next month',
    active: true
  },
  {
    id: 3,
    name: 'Quarterly Performance',
    frequency: 'Quarterly',
    nextRun: 'Jan 1, 2024',
    active: false
  }
])

const recentReports = ref([
  {
    id: 1,
    name: 'December Usage Report',
    description: 'Monthly usage analytics for all tenants',
    type: 'Usage',
    generatedAt: '2 hours ago',
    size: '2.4 MB',
    status: 'completed'
  },
  {
    id: 2,
    name: 'Q4 Billing Summary',
    description: 'Quarterly billing and revenue report',
    type: 'Billing',
    generatedAt: '1 day ago',
    size: '1.8 MB',
    status: 'completed'
  },
  {
    id: 3,
    name: 'Performance Metrics',
    description: 'System performance analysis',
    type: 'Performance',
    generatedAt: '3 days ago',
    size: '3.2 MB',
    status: 'completed'
  },
  {
    id: 4,
    name: 'Activity Analysis',
    description: 'User activity and engagement metrics',
    type: 'Activity',
    generatedAt: '1 week ago',
    size: '1.5 MB',
    status: 'failed'
  }
])

// Methods
const handleGenerateReport = async () => {
  isGenerating.value = true

  try {
    // Simulate report generation
    await new Promise(resolve => setTimeout(resolve, 2000))

    // Add to recent reports
    const newReport = {
      id: Date.now(),
      name: `${reportTypes.find(t => t.value === selectedReportType.value)?.label} - ${new Date().toLocaleDateString()}`,
      description: `Generated ${selectedReportType.value} report`,
      type: selectedReportType.value,
      generatedAt: 'Just now',
      size: '1.2 MB',
      status: 'completed'
    }

    recentReports.value.unshift(newReport)

    // Trigger download
    console.log('Report generated and downloaded')
  } catch (error) {
    console.error('Error generating report:', error)
  } finally {
    isGenerating.value = false
  }
}

const handlePreviewReport = () => {
  console.log('Preview report with current settings')
}

const handleScheduleReport = () => {
  console.log('Open schedule report modal')
}

const handleSaveTemplate = () => {
  const templateName = prompt('Enter template name:')
  if (templateName) {
    savedTemplates.value.push({
      id: Date.now(),
      name: templateName,
      type: reportTypes.find(t => t.value === selectedReportType.value)?.label || 'Custom',
      lastUsed: 'Just created'
    })
  }
}

const handleLoadTemplate = (template: any) => {
  console.log('Loading template:', template.name)
  // Load template settings
}

const handleDeleteTemplate = (templateId: number) => {
  const confirmed = confirm('Are you sure you want to delete this template?')
  if (confirmed) {
    const index = savedTemplates.value.findIndex(t => t.id === templateId)
    if (index > -1) {
      savedTemplates.value.splice(index, 1)
    }
  }
}

const handleToggleSchedule = (scheduleId: number) => {
  const schedule = scheduledReports.value.find(s => s.id === scheduleId)
  if (schedule) {
    schedule.active = !schedule.active
  }
}

const handleManageTemplates = () => {
  console.log('Open template management modal')
}

const handleRefreshReports = () => {
  console.log('Refreshing reports list')
}

const handleViewAllReports = () => {
  console.log('Navigate to all reports page')
}

const handleDownloadReport = (report: any) => {
  console.log('Downloading report:', report.name)
}

const handleViewReport = (report: any) => {
  console.log('Viewing report:', report.name)
}

const handleDeleteReport = (reportId: number) => {
  const confirmed = confirm('Are you sure you want to delete this report?')
  if (confirmed) {
    const index = recentReports.value.findIndex(r => r.id === reportId)
    if (index > -1) {
      recentReports.value.splice(index, 1)
    }
  }
}

const getReportIcon = (type: string): string => {
  const icons = {
    usage: 'material-symbols:analytics',
    billing: 'material-symbols:payments',
    activity: 'material-symbols:trending-up',
    performance: 'material-symbols:speed'
  }
  return icons[type] || 'material-symbols:description'
}

// Lifecycle
onMounted(() => {
  // Initialize any data if needed
  console.log('Reports page mounted')
})
</script>