<template>
  <label
    :for="id"
    class="relative inline-flex items-center cursor-pointer select-none transition-opacity duration-200"
    :class="{ 'opacity-50 cursor-not-allowed': disabled }"
  >
    <input
      :id="id"
      type="checkbox"
      :checked="modelValue"
      :disabled="disabled"
      @change="handleChange"
      class="sr-only peer"
      role="switch"
      :aria-checked="modelValue"
      :aria-labelledby="labelId"
    />

    <div
      class="w-10 h-6 rounded-full transition-colors duration-200 ease-in-out"
      :class="[
        modelValue
          ? activeTrackClasses[color] || activeTrackClasses.primary
          : 'bg-gray-200 dark:bg-gray-600',
        disabled ? 'pointer-events-none' : '',
        'peer-focus-visible:ring-2 peer-focus-visible:ring-offset-2',
        modelValue ? 'peer-focus-visible:ring-' + color + '-500 peer-focus-visible:ring-offset-white dark:peer-focus-visible:ring-offset-gray-900' : 'peer-focus-visible:ring-gray-400 peer-focus-visible:ring-offset-white dark:peer-focus-visible:ring-offset-gray-900',
      ]"
    >
      <div
        class="absolute left-0.5 top-0.5 w-5 h-5 bg-white dark:bg-gray-300 rounded-full shadow-md transform transition-transform duration-200 ease-in-out"
        :class="{
          'translate-x-full': modelValue,
          'translate-x-0': !modelValue,
        }"
      ></div>
    </div>

    <span
      v-if="label"
      :id="labelId"
      class="ml-3 text-sm font-medium text-gray-700 dark:text-gray-300"
    >
      {{ label }}
    </span>
  </label>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';

const props = withDefaults(
  defineProps<{
    modelValue: boolean;
    disabled?: boolean;
    color?: 'primary' | 'red' | 'green' | 'blue' | string; // Allows for custom Tailwind colors
    label?: string;
  }>(),
  {
    disabled: false,
    color: 'primary',
    label: '',
  }
);

const emit = defineEmits<{
  (e: 'update:modelValue', value: boolean): void;
  (e: 'change', value: boolean): void; // Expose a change event
}>();

// Generate a unique ID for accessibility
const id = `toggle-${Math.random().toString(36).substring(2, 9)}`;
const labelId = `toggle-label-${id}`;

// Tailwind CSS classes for active state based on color prop
const activeTrackClasses = computed(() => ({
  primary: 'bg-brandPrimary', // Assuming 'primary' is defined in your Tailwind config
  red: 'bg-brandDanger',
  green: 'bg-green-600',
  blue: 'bg-blue-600',
  // Add more default colors or use direct color string if needed
  // e.g., if props.color is 'purple-500', it will be directly used
  [`${props.color}-600`]: `bg-${props.color}-600`, // Dynamic class for custom colors
}));

const handleChange = (event: Event) => {
  const target = event.target as HTMLInputElement;
  const newValue = target.checked;
  emit('update:modelValue', newValue);
  emit('change', newValue);
};
</script>

<style scoped>
/*
  The `sr-only` class (from Tailwind) hides the checkbox visually
  but keeps it accessible to screen readers and allows native focus.
  The `peer` class makes it easy to style siblings based on the checkbox's state.
*/
.peer:checked + div {
  /* This style will be applied to the 'track' (the div sibling) when the checkbox is checked */
}

/* Ensure focus outline is visible for accessibility */
.peer:focus-visible + div {
  outline: 2px solid transparent; /* Reset */
  outline-offset: 2px;
}
</style>