/**
 * Comprehensive Type System Index
 * 
 * Central export point for all types in the application
 * Provides a single source of truth for type imports
 */

// ============================================================================
// CORE TYPES
// ============================================================================

export type * from './core.js'
export type * from './api.js'
export type * from './validation.js'

// Re-export commonly used core types for convenience
export type {
  UUID,
  UserId,
  TenantId,
  CaseId,
  DocumentId,
  TemplateId,
  ISODateString,
  UnixTimestamp,
  BaseEntity,
  EntityStatus,
  ProcessingStatus,
  SecurityClassification,
  FileInfo,
  AuditInfo,
  SoftDeleteInfo,
  PaginationParams,
  PaginationMeta,
  SortParams,
  FilterCondition,
  FilterGroup,
  ApiResponse,
  PaginatedResponse,
  ApiError,
  ValidationResult,
  ValidationError,
  PermissionContext,
  NotificationPriority,
  NotificationChannel,
  SearchParams,
  SearchResult,
  SearchResponse
} from './core.js' // Added .js

// Re-export commonly used API types
export type {
  HttpMethod, // Already here
  ContentType,
  ApiRequestConfig,
  AuthenticatedRequest,
  FileUploadRequest,
  UploadProgress,
  EnhancedApiResponse,
  StreamingResponse,
  ApiClientConfig,
  CacheStrategy,
  AuthType,
  ApiEndpoint,
  QueryBuilder,
  QueryParams,
  BatchRequest,
  BatchResponse,
  WebhookConfig,
  WebhookPayload,
  WebSocketConfig,
  WebSocketMessage,
  RealtimeSubscription,
  EnhancedApiError,
  ErrorRecoveryStrategy,
  RecoveryType,
  BackoffStrategy
  // PaginationParams, // This comes from core.js
  // ApiRequestConfig, // Covered by export *
  // EnhancedApiResponse, // Covered by export *
  // ApiError as SharedApiError // This comes from core.js
} from './api.js' // Added .js

// ============================================================================
// FEATURE TYPES
// ============================================================================

// Authentication types
export type * from '../features/auth/types/models.js'
export type * from '../features/auth/types/api.js'

// Cases types
export type * from '../features/cases/types/models.js'
export type * from '../features/cases/types/api.js'

// Documents types
export type * from '../features/documents/types/models.js'
export type * from '../features/documents/types/api.js'

// Templates types
export type * from '../features/templates/types/models.js'
export type * from '../features/templates/types/api.js'

// Users types
export type * from '../features/users/types/models.js'
export type * from '../features/users/types/api.js'

// Settings types
export type * from '../features/settings/types/models.js'
export type * from '../features/settings/types/api.js'

// Notifications types
export type * from '../features/notifications/types/models.js'
export type * from '../features/notifications/types/api.js'

// ============================================================================
// UI AND COMPONENT TYPES
// ============================================================================

/**
 * Common UI component props
 */
export interface BaseComponentProps {
  id?: string
  class?: string | string[] | Record<string, boolean>
  style?: string | Record<string, string>
  testId?: string
}

/**
 * Loading states
 */
export enum LoadingState {
  IDLE = 'idle',
  LOADING = 'loading',
  SUCCESS = 'success',
  ERROR = 'error'
}

/**
 * Component sizes
 */
export enum ComponentSize {
  XS = 'xs',
  SM = 'sm',
  MD = 'md',
  LG = 'lg',
  XL = 'xl'
}

/**
 * Component variants
 */
export enum ComponentVariant {
  PRIMARY = 'primary',
  SECONDARY = 'secondary',
  SUCCESS = 'success',
  WARNING = 'warning',
  ERROR = 'error',
  INFO = 'info'
}

/**
 * Button types
 */
export interface ButtonProps extends BaseComponentProps {
  type?: 'button' | 'submit' | 'reset'
  variant?: ComponentVariant
  size?: ComponentSize
  disabled?: boolean
  loading?: boolean
  icon?: string
  iconPosition?: 'left' | 'right'
  fullWidth?: boolean
  rounded?: boolean
  outline?: boolean
  ghost?: boolean
}

/**
 * Input types
 */
export interface InputProps extends BaseComponentProps {
  type?: 'text' | 'email' | 'password' | 'number' | 'tel' | 'url' | 'search'
  placeholder?: string
  value?: string | number
  disabled?: boolean
  readonly?: boolean
  required?: boolean
  size?: ComponentSize
  variant?: ComponentVariant
  error?: string
  hint?: string
  prefix?: string
  suffix?: string
  icon?: string
  clearable?: boolean
  maxLength?: number
  minLength?: number
  pattern?: string
}

/**
 * Modal types
 */
export interface ModalProps extends BaseComponentProps {
  open?: boolean
  title?: string
  size?: ComponentSize | 'full'
  closable?: boolean
  maskClosable?: boolean
  keyboard?: boolean
  centered?: boolean
  destroyOnClose?: boolean
  zIndex?: number
  onClose?: () => void
  onCancel?: () => void
  onOk?: () => void
}

/**
 * Table types
 */
export interface TableColumn<T = any> {
  key: string
  title: string
  dataIndex?: keyof T
  width?: number | string
  minWidth?: number
  maxWidth?: number
  align?: 'left' | 'center' | 'right'
  sortable?: boolean
  filterable?: boolean
  resizable?: boolean
  fixed?: 'left' | 'right'
  render?: (value: any, record: T, index: number) => any
  sorter?: (a: T, b: T) => number
  filters?: TableFilter[]
  defaultSortOrder?: 'asc' | 'desc'
  ellipsis?: boolean
}

export interface TableFilter {
  text: string
  value: any
  children?: TableFilter[]
}

export interface TableProps<T = any> extends BaseComponentProps {
  columns: TableColumn<T>[]
  dataSource: T[]
  loading?: boolean
  pagination?: PaginationParams | false
  rowKey?: string | ((record: T) => string)
  rowSelection?: TableRowSelection<T>
  expandable?: TableExpandable<T>
  scroll?: { x?: number | string; y?: number | string }
  size?: ComponentSize
  bordered?: boolean
  showHeader?: boolean
  sticky?: boolean
  virtual?: boolean
  onRow?: (record: T, index: number) => Record<string, any>
  onChange?: (pagination: PaginationParams, filters: Record<string, any>, sorter: any) => void
}

export interface TableRowSelection<T = any> {
  type?: 'checkbox' | 'radio'
  selectedRowKeys?: string[]
  onChange?: (selectedRowKeys: string[], selectedRows: T[]) => void
  onSelect?: (record: T, selected: boolean, selectedRows: T[], nativeEvent: Event) => void
  onSelectAll?: (selected: boolean, selectedRows: T[], changeRows: T[]) => void
  getCheckboxProps?: (record: T) => Record<string, any>
  hideSelectAll?: boolean
  preserveSelectedRowKeys?: boolean
}

export interface TableExpandable<T = any> {
  expandedRowKeys?: string[]
  defaultExpandedRowKeys?: string[]
  expandedRowRender?: (record: T, index: number, indent: number, expanded: boolean) => any
  expandRowByClick?: boolean
  expandIcon?: (props: any) => any
  onExpand?: (expanded: boolean, record: T) => void
  onExpandedRowsChange?: (expandedKeys: string[]) => void
  indentSize?: number
}

// ============================================================================
// DYNAMIC FORM TYPES
// ============================================================================

/**
 * Enhanced form field types for dynamic forms
 */
export interface DynamicFormField {
  id: string
  name: string
  label?: string
  type: DynamicFormFieldType
  required?: boolean
  disabled?: boolean
  readonly?: boolean
  placeholder?: string
  hint?: string
  tooltip?: string
  description?: string
  defaultValue?: any
  validation?: DynamicFormFieldValidation
  options?: DynamicFormFieldOption[]
  conditional?: DynamicFormFieldConditional
  layout?: DynamicFormFieldLayout
  props?: Record<string, any>
  component?: string
  group?: string
  order?: number
}

export enum DynamicFormFieldType {
  TEXT = 'text',
  EMAIL = 'email',
  PASSWORD = 'password',
  NUMBER = 'number',
  TEXTAREA = 'textarea',
  SELECT = 'select',
  MULTISELECT = 'multiselect',
  CHECKBOX = 'checkbox',
  RADIO = 'radio',
  SWITCH = 'switch',
  DATE = 'date',
  TIME = 'time',
  DATETIME = 'datetime',
  FILE = 'file',
  IMAGE = 'image',
  LOGO = 'logo',
  SLUG = 'slug',
  RICH_TEXT = 'rich_text',
  JSON = 'json',
  SLIDER = 'slider',
  RATING = 'rating',
  COLOR = 'color',
  CUSTOM = 'custom'
}

export interface DynamicFormFieldValidation {
  required?: boolean
  minLength?: number
  maxLength?: number
  min?: number
  max?: number
  pattern?: string | RegExp
  email?: boolean
  url?: boolean
  numeric?: boolean
  integer?: boolean
  positive?: boolean
  custom?: (value: any, formData: Record<string, any>) => string | null
  customAsync?: (value: any, formData: Record<string, any>) => Promise<string | null>
  messages?: Record<string, string>
}

export interface DynamicFormFieldOption {
  label: string
  value: any
  disabled?: boolean
  group?: string
  icon?: string
  description?: string
  color?: string
}

export interface DynamicFormFieldConditional {
  show?: DynamicFormCondition[]
  hide?: DynamicFormCondition[]
  enable?: DynamicFormCondition[]
  disable?: DynamicFormCondition[]
  require?: DynamicFormCondition[]
  readonly?: DynamicFormCondition[]
}

export interface DynamicFormCondition {
  field: string
  operator: 'equals' | 'not_equals' | 'contains' | 'not_contains' | 'in' | 'not_in' | 'greater_than' | 'less_than' | 'greater_equal' | 'less_equal' | 'empty' | 'not_empty' | 'regex'
  value?: any
  values?: any[]
  pattern?: string
  logic?: 'and' | 'or'
}

export interface DynamicFormFieldLayout {
  span?: number
  offset?: number
  order?: number
  breakpoint?: Record<string, Partial<DynamicFormFieldLayout>>
  className?: string
  style?: Record<string, any>
}

/**
 * Dynamic form configuration
 */
export interface DynamicFormConfig {
  id?: string
  title?: string
  description?: string
  fields: DynamicFormField[]
  layout?: DynamicFormLayout
  validation?: DynamicFormValidation
  submission?: DynamicFormSubmission
  persistence?: DynamicFormPersistence
  styling?: DynamicFormStyling
  hooks?: DynamicFormHooks
}

export interface DynamicFormLayout {
  columns?: number
  spacing?: ComponentSize
  labelPosition?: 'top' | 'left' | 'right' | 'floating'
  labelWidth?: number | string
  colon?: boolean
  requiredMark?: boolean | 'optional'
  compact?: boolean
  bordered?: boolean
  className?: string
  fieldClassName?: string
}

export interface DynamicFormValidation {
  validateOnChange?: boolean
  validateOnBlur?: boolean
  validateOnSubmit?: boolean
  validateOnMount?: boolean
  stopOnFirstError?: boolean
  scrollToError?: boolean
  showErrorSummary?: boolean
  debounceMs?: number
}

export interface DynamicFormSubmission {
  url?: string
  method?: HttpMethod
  headers?: Record<string, string>
  transform?: (data: any) => any
  beforeSubmit?: (data: any) => boolean | Promise<boolean>
  onSuccess?: (response: any, data: any) => void
  onError?: (error: any, data: any) => void
  onProgress?: (progress: number) => void
  loadingText?: string
  successText?: string
  errorText?: string
}

export interface DynamicFormPersistence {
  enabled?: boolean
  key?: string
  storage?: 'localStorage' | 'sessionStorage'
  exclude?: string[]
  encrypt?: boolean
  debounceMs?: number
  clearOnSubmit?: boolean
}

export interface DynamicFormStyling {
  theme?: 'default' | 'minimal' | 'bordered' | 'filled'
  size?: ComponentSize
  variant?: 'default' | 'outlined' | 'filled' | 'ghost'
  colorScheme?: string
  className?: string
  fieldClassName?: string
  labelClassName?: string
  errorClassName?: string
}

export interface DynamicFormHooks {
  onMount?: (form: any) => void
  onUnmount?: (form: any) => void
  onFieldChange?: (field: string, value: any, formData: Record<string, any>) => void
  onFieldBlur?: (field: string, value: any, formData: Record<string, any>) => void
  onFieldFocus?: (field: string, value: any, formData: Record<string, any>) => void
  onValidationChange?: (isValid: boolean, errors: Record<string, string[]>) => void
  onSubmit?: (data: any) => void
  onReset?: () => void
}

/**
 * Form state and events
 */
export interface DynamicFormState {
  isValid: boolean
  isDirty: boolean
  isSubmitting: boolean
  isLoading: boolean
  errors: Record<string, string[]>
  touched: Record<string, boolean>
  values: Record<string, any>
  meta: {
    submitCount: number
    validationCount: number
    lastSubmission?: Date
    lastValidation?: Date
  }
}

export interface DynamicFormEvents {
  'update:modelValue': [value: Record<string, any>]
  'field-change': [field: string, value: any, formData: Record<string, any>]
  'validation-change': [isValid: boolean, errors: Record<string, string[]>]
  'submit': [data: Record<string, any>]
  'reset': []
  'mount': [form: any]
  'unmount': [form: any]
}

// Legacy form types for backward compatibility
export interface FormField extends DynamicFormField {}
export const FormFieldType = DynamicFormFieldType
export interface FormFieldValidation extends DynamicFormFieldValidation {}
export interface FormFieldOption extends DynamicFormFieldOption {}
export interface FormFieldDependency extends DynamicFormCondition {}
export interface FormFieldConditional extends DynamicFormFieldConditional {}
export interface FormFieldLayout extends DynamicFormFieldLayout {}
export interface FormConfig extends DynamicFormConfig {}
export interface FormLayout extends DynamicFormLayout {}
export interface FormValidation extends DynamicFormValidation {}
export interface FormSubmission extends DynamicFormSubmission {}
export interface FormPersistence extends DynamicFormPersistence {}

// ============================================================================
// NAVIGATION TYPES
// ============================================================================

/**
 * Navigation item
 */
export interface NavigationItem {
  id: string
  label: string
  icon?: string
  path?: string
  external?: boolean
  target?: '_blank' | '_self' | '_parent' | '_top'
  badge?: NavigationBadge
  children?: NavigationItem[]
  permissions?: string[]
  roles?: string[]
  meta?: Record<string, any>
  order?: number
  hidden?: boolean
  disabled?: boolean
}

export interface NavigationBadge {
  text?: string
  count?: number
  color?: string
  variant?: ComponentVariant
  dot?: boolean
}

/**
 * Breadcrumb item
 */
export interface BreadcrumbItem {
  label: string
  path?: string
  icon?: string
  disabled?: boolean
}

/**
 * Menu configuration
 */
export interface MenuConfig {
  items: NavigationItem[]
  mode?: 'horizontal' | 'vertical' | 'inline'
  theme?: 'light' | 'dark'
  collapsed?: boolean
  collapsible?: boolean
  accordion?: boolean
  selectable?: boolean
  multiple?: boolean
  openKeys?: string[]
  selectedKeys?: string[]
  onSelect?: (keys: string[], item: NavigationItem) => void
  onOpenChange?: (keys: string[]) => void
}

// ============================================================================
// EMAIL TEMPLATE TYPES
// ============================================================================

/**
 * Email template configuration
 */
export interface EmailTemplate {
  id: string
  name: string
  description?: string
  category: EmailTemplateCategory
  type: EmailTemplateType
  subject: string
  content: EmailTemplateContent
  variables: EmailTemplateVariable[]
  settings: EmailTemplateSettings
  metadata: EmailTemplateMetadata
  status: EmailTemplateStatus
  createdAt: string
  updatedAt: string
  createdBy: string
  tenantId?: string
}

export interface EmailTemplateContent {
  html: string
  css: string
  components: any[] // GrapesJS components
  assets: EmailTemplateAsset[]
}

export interface EmailTemplateVariable {
  name: string
  label: string
  type: 'text' | 'number' | 'date' | 'boolean' | 'select' | 'multiselect'
  required: boolean
  defaultValue?: any
  options?: { label: string; value: any }[]
  description?: string
  validation?: {
    min?: number
    max?: number
    pattern?: string
    message?: string
  }
}

export interface EmailTemplateSettings {
  responsive: boolean
  darkMode: boolean
  preheader?: string
  trackingEnabled: boolean
  unsubscribeLink: boolean
  socialLinks: boolean
  footerText?: string
  customCss?: string
}

export interface EmailTemplateAsset {
  id: string
  name: string
  type: 'image' | 'font' | 'icon'
  url: string
  size: number
  dimensions?: { width: number; height: number }
  alt?: string
}

export interface EmailTemplateMetadata {
  tags: string[]
  version: string
  lastModifiedBy: string
  usageCount: number
  averageRating?: number
  isPublic: boolean
  isFavorite: boolean
}

export type EmailTemplateCategory =
  | 'legal-notice'
  | 'client-communication'
  | 'case-update'
  | 'invoice'
  | 'reminder'
  | 'welcome'
  | 'newsletter'
  | 'marketing'
  | 'system'
  | 'custom'

export type EmailTemplateType =
  | 'transactional'
  | 'promotional'
  | 'notification'
  | 'newsletter'
  | 'automated'

export type EmailTemplateStatus =
  | 'draft'
  | 'active'
  | 'inactive'
  | 'archived'

/**
 * Email template editor configuration
 */
export interface EmailEditorConfig {
  container: string | HTMLElement
  height: string
  width: string
  storageManager: EmailEditorStorageConfig
  assetManager: EmailEditorAssetConfig
  plugins: string[]
  pluginsOpts: Record<string, any>
  canvas: EmailEditorCanvasConfig
  panels: EmailEditorPanelConfig
  blockManager: EmailEditorBlockConfig
  styleManager: EmailEditorStyleConfig
  deviceManager: EmailEditorDeviceConfig
  commands: EmailEditorCommandConfig
  keymaps: EmailEditorKeymapConfig
}

export interface EmailEditorStorageConfig {
  type: 'local' | 'remote'
  autosave: boolean
  autoload: boolean
  stepsBeforeSave: number
  options?: {
    remote?: {
      urlStore: string
      urlLoad: string
      headers?: Record<string, string>
    }
  }
}

export interface EmailEditorAssetConfig {
  upload: string | boolean
  uploadName: string
  multiUpload: boolean
  autoAdd: boolean
  headers?: Record<string, string>
  params?: Record<string, any>
}

export interface EmailEditorCanvasConfig {
  styles: string[]
  scripts: string[]
  customBadgeLabel?: string
}

export interface EmailEditorPanelConfig {
  defaults: EmailEditorPanel[]
}

export interface EmailEditorPanel {
  id: string
  el: string
  resizable?: {
    tc: number
    cl: number
    cr: number
    bc: number
    keyWidth: string
    keyHeight: string
    unitWidth: string
    unitHeight: string
    step: number
    minDim: number
    maxDim: number
    currentPointer: number
    onMove?: (event: any) => void
    onStart?: (event: any) => void
    onEnd?: (event: any) => void
  }
}

export interface EmailEditorBlockConfig {
  appendTo: string
  blocks: EmailEditorBlock[]
}

export interface EmailEditorBlock {
  id: string
  label: string
  category: string
  content: string | (() => string)
  media?: string
  attributes?: Record<string, any>
}

export interface EmailEditorStyleConfig {
  appendTo: string
  sectors: EmailEditorStyleSector[]
}

export interface EmailEditorStyleSector {
  name: string
  open: boolean
  buildProps: string[]
  properties: EmailEditorStyleProperty[]
}

export interface EmailEditorStyleProperty {
  name?: string
  property: string
  type: string
  defaults?: any
  options?: { id: string; name: string }[]
}

export interface EmailEditorDeviceConfig {
  devices: EmailEditorDevice[]
}

export interface EmailEditorDevice {
  name: string
  width: string
  height?: string
  widthMedia?: string
}

export interface EmailEditorCommandConfig {
  defaults: EmailEditorCommand[]
}

export interface EmailEditorCommand {
  id: string
  run: (editor: any, sender: any, options?: any) => void
  stop?: (editor: any, sender: any, options?: any) => void
}

export interface EmailEditorKeymapConfig {
  defaults: EmailEditorKeymap[]
}

export interface EmailEditorKeymap {
  keys: string
  handler: string | ((editor: any) => void)
}

// ============================================================================
// THEME TYPES
// ============================================================================

/**
 * Theme configuration
 */
export interface ThemeConfig {
  colors: ThemeColors
  typography: ThemeTypography
  spacing: ThemeSpacing
  breakpoints: ThemeBreakpoints
  shadows: ThemeShadows
  borders: ThemeBorders
  animations: ThemeAnimations
}

export interface ThemeColors {
  primary: ColorPalette
  secondary: ColorPalette
  success: ColorPalette
  warning: ColorPalette
  error: ColorPalette
  info: ColorPalette
  neutral: ColorPalette
  background: BackgroundColors
  text: TextColors
}

export interface ColorPalette {
  50: string
  100: string
  200: string
  300: string
  400: string
  500: string
  600: string
  700: string
  800: string
  900: string
}

export interface BackgroundColors {
  primary: string
  secondary: string
  paper: string
  default: string
}

export interface TextColors {
  primary: string
  secondary: string
  disabled: string
  hint: string
}

export interface ThemeTypography {
  fontFamily: FontFamily
  fontSize: FontSizes
  fontWeight: FontWeights
  lineHeight: LineHeights
  letterSpacing: LetterSpacing
}

export interface FontFamily {
  sans: string[]
  serif: string[]
  mono: string[]
}

export interface FontSizes {
  xs: string
  sm: string
  base: string
  lg: string
  xl: string
  '2xl': string
  '3xl': string
  '4xl': string
  '5xl': string
  '6xl': string
}

export interface FontWeights {
  thin: number
  light: number
  normal: number
  medium: number
  semibold: number
  bold: number
  extrabold: number
  black: number
}

export interface LineHeights {
  none: number
  tight: number
  snug: number
  normal: number
  relaxed: number
  loose: number
}

export interface LetterSpacing {
  tighter: string
  tight: string
  normal: string
  wide: string
  wider: string
  widest: string
}

export interface ThemeSpacing {
  0: string
  1: string
  2: string
  3: string
  4: string
  5: string
  6: string
  8: string
  10: string
  12: string
  16: string
  20: string
  24: string
  32: string
  40: string
  48: string
  56: string
  64: string
}

export interface ThemeBreakpoints {
  xs: string
  sm: string
  md: string
  lg: string
  xl: string
  '2xl': string
}

export interface ThemeShadows {
  xs: string
  sm: string
  md: string
  lg: string
  xl: string
  '2xl': string
  inner: string
  none: string
}

export interface ThemeBorders {
  width: BorderWidths
  radius: BorderRadius
  style: BorderStyles
}

export interface BorderWidths {
  0: string
  1: string
  2: string
  4: string
  8: string
}

export interface BorderRadius {
  none: string
  sm: string
  md: string
  lg: string
  xl: string
  '2xl': string
  '3xl': string
  full: string
}

export interface BorderStyles {
  solid: string
  dashed: string
  dotted: string
  double: string
  none: string
}

export interface ThemeAnimations {
  duration: AnimationDurations
  easing: AnimationEasing
  keyframes: AnimationKeyframes
}

export interface AnimationDurations {
  75: string
  100: string
  150: string
  200: string
  300: string
  500: string
  700: string
  1000: string
}

export interface AnimationEasing {
  linear: string
  in: string
  out: string
  'in-out': string
}

export interface AnimationKeyframes {
  spin: Record<string, Record<string, string>>
  ping: Record<string, Record<string, string>>
  pulse: Record<string, Record<string, string>>
  bounce: Record<string, Record<string, string>>
}
