// server/api/auth/me.get.ts
import { define<PERSON><PERSON><PERSON><PERSON><PERSON>, create<PERSON>rror, set<PERSON><PERSON><PERSON>, getC<PERSON>ie } from 'h3';
import { createApiInstance } from '~/server/utils/apiInstance'; // Import the shared utility
import { ACCESS_TOKEN_COOKIE } from '~/server/constants';

export default defineEventHandler(async (event) => {
  // console.log('Event context keys in me.get.ts:', Object.keys(event.context)); // For debugging event.context
  const access_token = getCookie(event, ACCESS_TOKEN_COOKIE);
  // return {user:null} if user not logged in
 
  if(!access_token)
    return {user:null}

  const api = createApiInstance(event); // Create an API instance with the current event context
// console.log(api);

  try {
    // The Authorization header with Bearer token will be automatically added by the interceptor in createApiInstance
    const response = await api.get('/auth/me'); // Make the request to your backend

    // Return the user object to the client
    return { user: response.data }; // Assuming your backend returns { user: ... }

  } catch (error: any) {
    console.error('Nuxt Server /me API Error:', error.response?.data || error.message);

    // If the error is due to an invalid/expired token (e.g., 401 from your backend),
    // the interceptor in createApiInstance would have tried to refresh it.
    // If refresh failed or it's another error, we might want to clear cookies here too,
    // though the interceptor already attempts to clear them on refresh failure.
    // For robustness, ensure cookies are cleared if a 401 still bubbles up.
    if (error.response?.status === 401) {
      setCookie(event, ACCESS_TOKEN_COOKIE, '', { expires: new Date(0), httpOnly: true, secure: process.env.NODE_ENV === 'production', sameSite: process.env.NODE_ENV === 'production' ? 'strict' as const : 'lax' as const, path: '/' });
      // Optionally clear refresh token cookie too, if not handled by interceptor on final failure
      // import { REFRESH_TOKEN_COOKIE } from '~/server/constants';
      // setCookie(event, REFRESH_TOKEN_COOKIE, '', { expires: new Date(0), httpOnly: true, secure: process.env.NODE_ENV === 'production', sameSite: 'lax', path: '/' });
    }

    throw createError({
      statusCode: error.response?.status || 500,
      statusMessage: error.response?.data?.message || 'Failed to fetch user data.',
      data: error.response?.data,
    });
  }
});