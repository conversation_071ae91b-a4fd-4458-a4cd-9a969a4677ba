# Dynamic Forms Documentation

## Overview

The Dynamic Forms system provides a powerful, flexible, and accessible way to create complex forms with conditional logic, validation, and state management. Built with Vue 3, TypeScript, VeeValidate, and Zod.

## Features

- ✅ **Dynamic Field Rendering** - Support for 15+ field types
- ✅ **Conditional Logic** - Show/hide fields based on other field values
- ✅ **Comprehensive Validation** - Built-in and custom validation rules
- ✅ **Accessibility** - WCAG 2.1 compliant with ARIA support
- ✅ **State Management** - Form state, persistence, and auto-save
- ✅ **Responsive Design** - Mobile-first responsive layouts
- ✅ **TypeScript Support** - Full type safety and IntelliSense
- ✅ **Theming** - Customizable themes and styling

## Quick Start

### Basic Usage

```vue
<template>
  <DynamicForm
    :config="formConfig"
    v-model="formData"
    @submit="handleSubmit"
  />
</template>

<script setup lang="ts">
import { ref } from 'vue'
import type { DynamicFormConfig } from '~/app/shared/types'

const formData = ref({})

const formConfig: DynamicFormConfig = {
  id: 'my-form',
  title: 'Contact Form',
  fields: [
    {
      id: 'name',
      name: 'name',
      label: 'Full Name',
      type: 'text',
      required: true,
      validation: {
        required: true,
        minLength: 2
      }
    },
    {
      id: 'email',
      name: 'email',
      label: 'Email',
      type: 'email',
      required: true,
      validation: {
        required: true,
        email: true
      }
    }
  ]
}

const handleSubmit = (data: Record<string, any>) => {
  console.log('Form submitted:', data)
}
</script>
```

## Field Types

### Text Fields
- `text` - Basic text input
- `email` - Email input with validation
- `password` - Password input with strength indicator
- `textarea` - Multi-line text input
- `number` - Numeric input

### Selection Fields
- `select` - Dropdown selection
- `multiselect` - Multiple selection dropdown
- `radio` - Radio button group
- `checkbox` - Checkbox or checkbox group
- `switch` - Toggle switch

### Date/Time Fields
- `date` - Date picker
- `time` - Time picker
- `datetime` - Date and time picker

### Special Fields
- `file` - File upload
- `image` - Image upload
- `slider` - Range slider
- `rating` - Star rating
- `color` - Color picker

## Field Configuration

```typescript
interface DynamicFormField {
  id: string                              // Unique field identifier
  name: string                           // Field name for form data
  label?: string                         // Display label
  type: DynamicFormFieldType            // Field type
  required?: boolean                     // Required field
  disabled?: boolean                     // Disabled state
  readonly?: boolean                     // Read-only state
  placeholder?: string                   // Placeholder text
  hint?: string                         // Help text
  tooltip?: string                      // Tooltip text
  description?: string                  // Field description
  defaultValue?: any                    // Default value
  validation?: DynamicFormFieldValidation // Validation rules
  options?: DynamicFormFieldOption[]    // Options for select/radio/checkbox
  conditional?: DynamicFormFieldConditional // Conditional logic
  layout?: DynamicFormFieldLayout       // Layout configuration
  props?: Record<string, any>           // Additional props
  component?: string                    // Custom component
  group?: string                        // Field group
  order?: number                        // Display order
}
```

## Validation

### Built-in Validation Rules

```typescript
interface DynamicFormFieldValidation {
  required?: boolean                    // Required field
  minLength?: number                   // Minimum length
  maxLength?: number                   // Maximum length
  min?: number                         // Minimum value
  max?: number                         // Maximum value
  pattern?: string | RegExp            // Pattern validation
  email?: boolean                      // Email validation
  url?: boolean                        // URL validation
  numeric?: boolean                    // Numeric validation
  integer?: boolean                    // Integer validation
  positive?: boolean                   // Positive number validation
  custom?: (value: any, formData: Record<string, any>) => string | null
  customAsync?: (value: any, formData: Record<string, any>) => Promise<string | null>
  messages?: Record<string, string>    // Custom error messages
}
```

### Custom Validation

```typescript
{
  id: 'password',
  name: 'password',
  type: 'password',
  validation: {
    required: true,
    minLength: 8,
    custom: (value: string) => {
      if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(value)) {
        return 'Password must contain uppercase, lowercase, and number'
      }
      return null
    },
    messages: {
      required: 'Password is required',
      minLength: 'Password must be at least 8 characters'
    }
  }
}
```

## Conditional Logic

### Show/Hide Fields

```typescript
{
  id: 'companyName',
  name: 'companyName',
  type: 'text',
  conditional: {
    show: [
      { field: 'accountType', operator: 'equals', value: 'business' }
    ]
  }
}
```

### Complex Conditions

```typescript
{
  id: 'phoneNumber',
  name: 'phoneNumber',
  type: 'text',
  conditional: {
    show: [
      { field: 'contactMethod', operator: 'in', values: ['phone', 'both'] }
    ],
    require: [
      { field: 'contactMethod', operator: 'equals', value: 'phone' }
    ]
  }
}
```

### Condition Operators

- `equals` - Field equals value
- `not_equals` - Field does not equal value
- `contains` - Field contains value
- `not_contains` - Field does not contain value
- `in` - Field value is in array
- `not_in` - Field value is not in array
- `greater_than` - Field value is greater than
- `less_than` - Field value is less than
- `greater_equal` - Field value is greater than or equal
- `less_equal` - Field value is less than or equal
- `empty` - Field is empty
- `not_empty` - Field is not empty
- `regex` - Field matches regex pattern

## Form Configuration

```typescript
interface DynamicFormConfig {
  id?: string                          // Form identifier
  title?: string                       // Form title
  description?: string                 // Form description
  fields: DynamicFormField[]          // Form fields
  layout?: DynamicFormLayout          // Layout configuration
  validation?: DynamicFormValidation  // Validation settings
  submission?: DynamicFormSubmission  // Submission handling
  persistence?: DynamicFormPersistence // Data persistence
  styling?: DynamicFormStyling        // Styling options
  hooks?: DynamicFormHooks            // Event hooks
}
```

### Layout Options

```typescript
interface DynamicFormLayout {
  columns?: number                     // Number of columns (1-4)
  spacing?: 'sm' | 'md' | 'lg'        // Field spacing
  labelPosition?: 'top' | 'left' | 'right' | 'floating'
  labelWidth?: number | string         // Label width
  colon?: boolean                      // Show colon after labels
  requiredMark?: boolean | 'optional'  // Required field marking
  compact?: boolean                    // Compact layout
  bordered?: boolean                   // Bordered layout
  className?: string                   // Custom CSS classes
  fieldClassName?: string              // Field wrapper classes
}
```

## Events

```typescript
interface DynamicFormEvents {
  'update:modelValue': [value: Record<string, any>]
  'field-change': [field: string, value: any, formData: Record<string, any>]
  'validation-change': [isValid: boolean, errors: Record<string, string[]>]
  'submit': [data: Record<string, any>]
  'reset': []
  'mount': [form: any]
  'unmount': [form: any]
}
```

## Accessibility Features

- **ARIA Support** - Comprehensive ARIA attributes
- **Keyboard Navigation** - Full keyboard support
- **Screen Reader** - Screen reader announcements
- **Focus Management** - Intelligent focus handling
- **Error Announcements** - Accessible error reporting
- **High Contrast** - High contrast mode support

## Theming and Styling

### Built-in Themes

```typescript
{
  styling: {
    theme: 'default' | 'minimal' | 'bordered' | 'filled',
    size: 'sm' | 'md' | 'lg',
    variant: 'default' | 'outlined' | 'filled' | 'ghost',
    colorScheme: 'blue' | 'green' | 'purple' | 'red',
    className: 'custom-form-class'
  }
}
```

### Custom Styling

```css
.dynamic-form--custom {
  @apply bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-xl;
}

.dynamic-form--custom .dynamic-form-field {
  @apply mb-6;
}

.dynamic-form--custom .dynamic-form-field__label {
  @apply text-indigo-700 font-semibold;
}
```

## Advanced Features

### Form Persistence

```typescript
{
  persistence: {
    enabled: true,
    key: 'my-form-data',
    storage: 'localStorage',
    debounceMs: 1000,
    clearOnSubmit: true
  }
}
```

### Auto-save

```typescript
const formOptions = {
  autoSave: {
    enabled: true,
    debounceMs: 2000,
    onSave: async (data) => {
      await api.saveFormDraft(data)
    }
  }
}
```

### Custom Field Components

```typescript
{
  id: 'customField',
  name: 'customField',
  type: 'custom',
  component: 'MyCustomFieldComponent',
  props: {
    customProp: 'value'
  }
}
```

## Best Practices

1. **Field Naming** - Use descriptive, consistent field names
2. **Validation** - Provide clear, helpful error messages
3. **Accessibility** - Always include labels and descriptions
4. **Performance** - Use conditional logic to minimize DOM updates
5. **User Experience** - Group related fields and provide helpful hints
6. **Testing** - Test forms with keyboard navigation and screen readers

## Examples

See the `components/forms/examples/` directory for comprehensive examples:

- `BasicFormExample.vue` - Simple contact form
- `ConditionalFormExample.vue` - Complex conditional logic
- `WizardFormExample.vue` - Multi-step form wizard
- `CustomFieldExample.vue` - Custom field components

## API Reference

For complete API documentation, see the TypeScript interfaces in `app/shared/types/index.ts`.
