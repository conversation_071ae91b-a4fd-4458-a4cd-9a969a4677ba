/**
 * Event Bus Plugin for Nuxt 3
 * Provides global event management and integration with existing systems
 */

import { getEventBus } from '~/utils/eventBus'
import type { EventMap } from '~/types/events'

export default defineNuxtPlugin((nuxtApp) => {
  const eventBus = getEventBus()

  // ============================================================================
  // PROVIDE EVENT BUS TO NUXT APP
  // ============================================================================

  nuxtApp.provide('eventBus', eventBus)

  // ============================================================================
  // INTEGRATION WITH EXISTING SYSTEMS
  // ============================================================================

  // Integration with analytics system
  if (nuxtApp.$analytics) {
    // Track business events
    eventBus.on('case:created', (data) => {
      nuxtApp.$trackCaseEvent('created', data.caseId, {
        caseType: data.caseType,
        clientId: data.clientId,
        assignedTo: data.assignedTo
      })
    })

    eventBus.on('case:updated', (data) => {
      nuxtApp.$trackCaseEvent('updated', data.caseId, {
        changes: Object.keys(data.changes),
        updatedBy: data.updatedBy
      })
    })

    eventBus.on('document:uploaded', (data) => {
      nuxtApp.$trackDocumentEvent('uploaded', data.documentId, {
        fileSize: data.fileSize,
        mimeType: data.mimeType,
        caseId: data.caseId
      })
    })

    // Track UI events
    eventBus.on('ui:search', (data) => {
      nuxtApp.$trackEvent('search_performed', {
        query: data.query,
        filters: data.filters,
        resultsCount: data.resultsCount,
        component: data.component
      })
    })

    eventBus.on('ui:bulk-action', (data) => {
      nuxtApp.$trackEvent('bulk_action_performed', {
        action: data.action,
        itemCount: data.itemCount,
        entityType: data.entityType
      })
    })
  }

  // Integration with error tracking
  if (nuxtApp.$captureError) {
    eventBus.on('system:error', (data) => {
      if (data.error instanceof Error) {
        nuxtApp.$captureError(data.error, data.context)
      } else {
        nuxtApp.$captureError(new Error(data.error), data.context)
      }
    })
  }

  // Integration with performance monitoring
  if (nuxtApp.$trackPerformance) {
    eventBus.on('system:performance', (data) => {
      nuxtApp.$trackPerformance(data.metric, data.value)
    })
  }

  // ============================================================================
  // GLOBAL EVENT HANDLERS
  // ============================================================================

  // Handle language changes
  eventBus.on('app:language-changed', (data) => {
    // Update document attributes
    if (process.client) {
      document.documentElement.dir = data.isRTL ? 'rtl' : 'ltr'
      document.documentElement.lang = data.language
    }
  })

  // Handle theme changes
  eventBus.on('app:theme-changed', (data) => {
    if (process.client) {
      document.documentElement.classList.toggle('dark', data.theme === 'dark')
      localStorage.setItem('theme', data.theme)
    }
  })

  // Handle authentication events
  eventBus.on('user:login', (data) => {
    if (data.success) {
      console.log('User logged in successfully')
      // Could trigger other actions like refreshing user data
    }
  })

  eventBus.on('user:logout', (data) => {
    if (data.success) {
      console.log('User logged out successfully')
      // Could trigger cleanup actions
    }
  })

  // ============================================================================
  // NOTIFICATION SYSTEM INTEGRATION
  // ============================================================================

  // Handle notification events
  eventBus.on('notification:created', (data) => {
    // You can integrate with your toast/notification system here
    console.log('New notification:', data.title, data.message)
    
    // Example: Show toast notification
    // if (nuxtApp.$toast) {
    //   nuxtApp.$toast.show({
    //     type: data.type,
    //     title: data.title,
    //     message: data.message,
    //     duration: data.priority === 'urgent' ? 0 : 5000
    //   })
    // }
  })

  // ============================================================================
  // REAL-TIME INTEGRATION
  // ============================================================================

  // Handle WebSocket events
  eventBus.on('realtime:connected', (data) => {
    console.log('Real-time connection established:', data.channel)
  })

  eventBus.on('realtime:disconnected', (data) => {
    console.log('Real-time connection lost:', data.channel)
  })

  eventBus.on('realtime:message', (data) => {
    // Handle incoming real-time messages
    console.log('Real-time message received:', data.eventType, data.data)
    
    // Route to appropriate handlers based on event type
    switch (data.eventType) {
      case 'case_updated':
        eventBus.emit('case:updated', {
          ...data.data,
          timestamp: new Date().toISOString(),
          source: 'realtime'
        })
        break
      
      case 'document_uploaded':
        eventBus.emit('document:uploaded', {
          ...data.data,
          timestamp: new Date().toISOString(),
          source: 'realtime'
        })
        break
      
      case 'notification':
        eventBus.emit('notification:created', {
          ...data.data,
          timestamp: new Date().toISOString(),
          source: 'realtime'
        })
        break
    }
  })

  // ============================================================================
  // DEVELOPMENT HELPERS
  // ============================================================================

  if (process.env.NODE_ENV === 'development') {
    // Log all events in development
    const originalEmit = eventBus.emit.bind(eventBus)
    eventBus.emit = function<K extends keyof EventMap>(event: K, data: EventMap[K]) {
      console.log(`🔔 [EventBus] ${String(event)}:`, data)
      return originalEmit(event, data)
    }

    // Expose event bus to window for debugging
    if (typeof window !== 'undefined') {
      (window as any).__eventBus = eventBus
      console.log('🔧 Event Bus available at window.__eventBus')
    }
  }

  // ============================================================================
  // CLEANUP ON APP UNMOUNT
  // ============================================================================

  nuxtApp.hook('app:beforeMount', () => {
    eventBus.emit('app:ready', {
      timestamp: new Date().toISOString(),
      source: 'nuxt-app'
    })
  })

  // Note: Nuxt doesn't have a direct unmount hook, but you can handle cleanup
  // in specific components or pages as needed
})

// ============================================================================
// TYPE DECLARATIONS
// ============================================================================

declare module '#app' {
  interface NuxtApp {
    $eventBus: ReturnType<typeof getEventBus>
  }
}

declare module 'vue' {
  interface ComponentCustomProperties {
    $eventBus: ReturnType<typeof getEventBus>
  }
}
