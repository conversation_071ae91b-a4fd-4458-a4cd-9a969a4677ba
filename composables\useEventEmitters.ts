/**
 * Event Emitters Composable
 * Provides convenient methods for emitting events throughout the application
 */

import { getEventBus } from '~/utils/eventBus'
import type { EventMap } from '~/types/events'

// ============================================================================
// MAIN EMITTERS COMPOSABLE
// ============================================================================

export function useEventEmitters() {
  const eventBus = getEventBus()
  const { $trackEvent, $trackCaseEvent, $trackDocumentEvent } = useNuxtApp()

  // ============================================================================
  // GENERIC EMITTER
  // ============================================================================

  const emit = (event: string, data: any = {}) => eventBus.emit(event, data);

  // ============================================================================
  // CASE EVENT EMITTERS
  // ============================================================================

  const emitCaseCreated = (data: Omit<EventMap['case:created'], 'timestamp'>) => {
    eventBus.emit('case:created', {
      ...data,
      timestamp: new Date().toISOString()
    })
  }

  const emitCaseUpdated = (data: Omit<EventMap['case:updated'], 'timestamp'>) => {
    eventBus.emit('case:updated', {
      ...data,
      timestamp: new Date().toISOString()
    })
  }

  const emitCaseStatusChanged = (data: Omit<EventMap['case:status-changed'], 'timestamp'>) => {
    eventBus.emit('case:status-changed', {
      ...data,
      timestamp: new Date().toISOString()
    })
  }

  const emitCaseDeleted = (data: Omit<EventMap['case:deleted'], 'timestamp'>) => {
    eventBus.emit('case:deleted', {
      ...data,
      timestamp: new Date().toISOString()
    })
  }

  const emitCaseAssigned = (data: Omit<EventMap['case:assigned'], 'timestamp'>) => {
    eventBus.emit('case:assigned', {
      ...data,
      timestamp: new Date().toISOString()
    })
  }

  // ============================================================================
  // DOCUMENT EVENT EMITTERS
  // ============================================================================

  const emitDocumentUploaded = (data: Omit<EventMap['document:uploaded'], 'timestamp'>) => {
    eventBus.emit('document:uploaded', {
      ...data,
      timestamp: new Date().toISOString()
    })
  }

  const emitDocumentShared = (data: Omit<EventMap['document:shared'], 'timestamp'>) => {
    eventBus.emit('document:shared', {
      ...data,
      timestamp: new Date().toISOString()
    })
  }

  const emitDocumentDownloaded = (data: Omit<EventMap['document:downloaded'], 'timestamp'>) => {
    eventBus.emit('document:downloaded', {
      ...data,
      timestamp: new Date().toISOString()
    })
  }

  const emitDocumentDeleted = (data: Omit<EventMap['document:deleted'], 'timestamp'>) => {
    eventBus.emit('document:deleted', {
      ...data,
      timestamp: new Date().toISOString()
    })
  }

  const emitDocumentVersionCreated = (data: Omit<EventMap['document:version-created'], 'timestamp'>) => {
    eventBus.emit('document:version-created', {
      ...data,
      timestamp: new Date().toISOString()
    })
  }

  // ============================================================================
  // TEMPLATE EVENT EMITTERS
  // ============================================================================

  const emitTemplateCreated = (data: Omit<EventMap['template:created'], 'timestamp'>) => {
    eventBus.emit('template:created', {
      ...data,
      timestamp: new Date().toISOString()
    })
  }

  const emitTemplateUpdated = (data: Omit<EventMap['template:updated'], 'timestamp'>) => {
    eventBus.emit('template:updated', {
      ...data,
      timestamp: new Date().toISOString()
    })
  }

  const emitTemplateUsed = (data: Omit<EventMap['template:used'], 'timestamp'>) => {
    eventBus.emit('template:used', {
      ...data,
      timestamp: new Date().toISOString()
    })
  }

  const emitTemplateDeleted = (data: Omit<EventMap['template:deleted'], 'timestamp'>) => {
    eventBus.emit('template:deleted', {
      ...data,
      timestamp: new Date().toISOString()
    })
  }

  const emitTemplatePublished = (data: Omit<EventMap['template:published'], 'timestamp'>) => {
    eventBus.emit('template:published', {
      ...data,
      timestamp: new Date().toISOString()
    })
  }

  // ============================================================================
  // USER EVENT EMITTERS
  // ============================================================================

  const emitUserInvited = (data: Omit<EventMap['user:invited'], 'timestamp'>) => {
    eventBus.emit('user:invited', {
      ...data,
      timestamp: new Date().toISOString()
    })
  }

  const emitUserRoleChanged = (data: Omit<EventMap['user:role-changed'], 'timestamp'>) => {
    eventBus.emit('user:role-changed', {
      ...data,
      timestamp: new Date().toISOString()
    })
  }

  const emitUserActivated = (data: Omit<EventMap['user:activated'], 'timestamp'>) => {
    eventBus.emit('user:activated', {
      ...data,
      timestamp: new Date().toISOString()
    })
  }

  const emitUserDeactivated = (data: Omit<EventMap['user:deactivated'], 'timestamp'>) => {
    eventBus.emit('user:deactivated', {
      ...data,
      timestamp: new Date().toISOString()
    })
  }

  const emitUserLogin = (data: Omit<EventMap['user:login'], 'timestamp'>) => {
    eventBus.emit('user:login', {
      ...data,
      timestamp: new Date().toISOString()
    })
  }

  const emitUserLogout = (data: Omit<EventMap['user:logout'], 'timestamp'>) => {
    eventBus.emit('user:logout', {
      ...data,
      timestamp: new Date().toISOString()
    })
  }

  // ============================================================================
  // UI EVENT EMITTERS
  // ============================================================================

  const emitNavigation = (data: Omit<EventMap['ui:navigation'], 'timestamp'>) => {
    eventBus.emit('ui:navigation', {
      ...data,
      timestamp: new Date().toISOString()
    })
  }

  const emitSearch = (data: Omit<EventMap['ui:search'], 'timestamp'>) => {
    eventBus.emit('ui:search', {
      ...data,
      timestamp: new Date().toISOString()
    })
  }

  const emitFilter = (data: Omit<EventMap['ui:filter'], 'timestamp'>) => {
    eventBus.emit('ui:filter', {
      ...data,
      timestamp: new Date().toISOString()
    })
  }

  const emitBulkAction = (data: Omit<EventMap['ui:bulk-action'], 'timestamp'>) => {
    eventBus.emit('ui:bulk-action', {
      ...data,
      timestamp: new Date().toISOString()
    })
  }

  const emitViewChanged = (data: Omit<EventMap['ui:view-changed'], 'timestamp'>) => {
    eventBus.emit('ui:view-changed', {
      ...data,
      timestamp: new Date().toISOString()
    })
  }

  const emitModalOpened = (data: Omit<EventMap['ui:modal-opened'], 'timestamp'>) => {
    eventBus.emit('ui:modal-opened', {
      ...data,
      timestamp: new Date().toISOString()
    })
  }

  const emitModalClosed = (data: Omit<EventMap['ui:modal-closed'], 'timestamp'>) => {
    eventBus.emit('ui:modal-closed', {
      ...data,
      timestamp: new Date().toISOString()
    })
  }

  // ============================================================================
  // NOTIFICATION EVENT EMITTERS
  // ============================================================================

  const emitNotificationCreated = (data: Omit<EventMap['notification:created'], 'timestamp'>) => {
    eventBus.emit('notification:created', {
      ...data,
      timestamp: new Date().toISOString()
    })
  }

  const emitNotificationRead = (data: Omit<EventMap['notification:read'], 'timestamp'>) => {
    eventBus.emit('notification:read', {
      ...data,
      timestamp: new Date().toISOString()
    })
  }

  const emitNotificationDismissed = (data: Omit<EventMap['notification:dismissed'], 'timestamp'>) => {
    eventBus.emit('notification:dismissed', {
      ...data,
      timestamp: new Date().toISOString()
    })
  }

  // ============================================================================
  // SYSTEM EVENT EMITTERS
  // ============================================================================

  const emitSystemError = (data: Omit<EventMap['system:error'], 'timestamp'>) => {
    eventBus.emit('system:error', {
      ...data,
      timestamp: new Date().toISOString()
    })
  }

  const emitSystemPerformance = (data: Omit<EventMap['system:performance'], 'timestamp'>) => {
    eventBus.emit('system:performance', {
      ...data,
      timestamp: new Date().toISOString()
    })
  }

  const emitSystemMaintenance = (data: Omit<EventMap['system:maintenance'], 'timestamp'>) => {
    eventBus.emit('system:maintenance', {
      ...data,
      timestamp: new Date().toISOString()
    })
  }

  // ============================================================================
  // APP EVENT EMITTERS
  // ============================================================================

  const emitAppError = (data: Omit<EventMap['app:error'], 'timestamp'>) => {
    eventBus.emit('app:error', {
      ...data,
      timestamp: new Date().toISOString()
    })
  }

  const emitThemeChanged = (data: Omit<EventMap['app:theme-changed'], 'timestamp'>) => {
    eventBus.emit('app:theme-changed', {
      ...data,
      timestamp: new Date().toISOString()
    })
  }

  const emitLanguageChanged = (data: Omit<EventMap['app:language-changed'], 'timestamp'>) => {
    eventBus.emit('app:language-changed', {
      ...data,
      timestamp: new Date().toISOString()
    })
  }

  // ============================================================================
  // RETURN INTERFACE
  // ============================================================================

  return {
    // Generic emitter
    emit,
    // Case events
    emitCaseCreated,
    emitCaseUpdated,
    emitCaseStatusChanged,
    emitCaseDeleted,
    emitCaseAssigned,

    // Document events
    emitDocumentUploaded,
    emitDocumentShared,
    emitDocumentDownloaded,
    emitDocumentDeleted,
    emitDocumentVersionCreated,

    // Template events
    emitTemplateCreated,
    emitTemplateUpdated,
    emitTemplateUsed,
    emitTemplateDeleted,
    emitTemplatePublished,

    // User events
    emitUserInvited,
    emitUserRoleChanged,
    emitUserActivated,
    emitUserDeactivated,
    emitUserLogin,
    emitUserLogout,

    // UI events
    emitNavigation,
    emitSearch,
    emitFilter,
    emitBulkAction,
    emitViewChanged,
    emitModalOpened,
    emitModalClosed,

    // Notification events
    emitNotificationCreated,
    emitNotificationRead,
    emitNotificationDismissed,

    // System events
    emitSystemError,
    emitSystemPerformance,
    emitSystemMaintenance,

    // App events
    emitAppError,
    emitThemeChanged,
    emitLanguageChanged
  }
}
