<template>
  <div class="space-y-6">
    <div>
      <h2 class="text-md font-semibold text-gray-800 dark:text-gray-100 mb-4">Audit & System Log Settings</h2>
      <p class="text-sm text-gray-600 dark:text-gray-400">Configure log retention, levels, and access platform activity logs.</p>
    </div>

    <!-- Audit Log Settings Section -->
    <UiCard title="Audit Log Settings">
      <div class="space-y-4">
        <UiToggle v-model="auditLogSettings.enabled" label="Enable Audit Logging" />
        <div>
          <label for="auditLogRetention" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Audit Log Retention Period (days)</label>
          <UiInput id="auditLogRetention" :name="'auditLogRetention'" v-model.number="auditLogSettings.retentionDays" type="number" placeholder="e.g., 180 (0 for indefinite)" class="mt-1" />
        </div>
        <div>
          <label for="auditLogLevel" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Log Level for Audit Trail</label>
          <UiSelect id="auditLogLevel" :name="'auditLogLevel'" v-model="auditLogSettings.level" :options="auditLogLevelOptions" class="mt-1" />
        </div>
      </div>
    </UiCard>

    <!-- System Event Logging Section -->
    <UiCard title="System Event Logging">
      <div class="space-y-4">
        <UiToggle v-model="systemEventLogSettings.enabled" label="Enable System Event Logging" description="Logs application errors, warnings, etc." />
        <div>
          <label for="systemLogRetention" class="block text-sm font-medium text-gray-700 dark:text-gray-300">System Log Retention Period (days)</label>
          <UiInput id="systemLogRetention" :name="'systemLogRetention'" v-model.number="systemEventLogSettings.retentionDays" type="number" placeholder="e.g., 90" class="mt-1" />
        </div>
        <div>
          <label for="systemLogLevel" class="block text-sm font-medium text-gray-700 dark:text-gray-300">System Log Level</label>
          <UiSelect id="systemLogLevel" :name="'systemLogLevel'" v-model="systemEventLogSettings.level" :options="systemLogLevelOptions" class="mt-1" />
        </div>
      </div>
    </UiCard>

    <!-- Log Export Section -->
    <UiCard title="Log Export">
      <div class="space-y-3">
        <p class="text-sm text-gray-600 dark:text-gray-400">Export logs for external analysis or archiving. This may take a few moments for large datasets.</p>
        <div class="flex space-x-3">
          <UiButton variant="outline" @click="exportAuditLogs">Export Audit Logs</UiButton>
          <UiButton variant="outline" @click="exportSystemLogs">Export System Logs</UiButton>
        </div>
        <!-- TODO: Implement date range selection modals for exports -->
      </div>
    </UiCard>

    <!-- View Logs Section -->
    <UiCard title="View Logs">
      <div class="space-y-2">
        <p class="text-sm text-gray-600 dark:text-gray-400">Access detailed audit trails and system activity logs directly within the platform.</p>
        <NuxtLink to="/dashboard/platform/audit-logs">
          <UiButton variant="outline">Go to Audit Logs Page</UiButton>
        </NuxtLink>
      </div>
    </UiCard>

  </div>
</template>

<script setup lang="ts">
import { reactive } from 'vue';
import UiCard from '~/components/ui/UiCard.vue';
import UiToggle from '~/components/ui/UiToggle.vue';
import UiInput from '~/components/ui/UiInput.vue';
import UiSelect from '~/components/ui/UiSelect.vue';
import UiButton from '~/components/ui/UiButton.vue';
// NuxtLink is globally available

// Audit Log Settings
const auditLogSettings = reactive({
  enabled: true,
  retentionDays: 180,
  level: 'detailed', // 'basic', 'detailed', 'verbose'
});

const auditLogLevelOptions = [
  { label: 'Basic (Key actions)', value: 'basic' },
  { label: 'Detailed (Most actions)', value: 'detailed' },
  { label: 'Verbose (All actions, including reads)', value: 'verbose' },
];

// System Event Logging
const systemEventLogSettings = reactive({
  enabled: true,
  retentionDays: 90,
  level: 'WARN', // 'ERROR', 'WARN', 'INFO', 'DEBUG'
});

const systemLogLevelOptions = [
  { label: 'ERROR', value: 'ERROR' },
  { label: 'WARN', value: 'WARN' },
  { label: 'INFO', value: 'INFO' },
  { label: 'DEBUG', value: 'DEBUG' },
];

// Log Export Functions
const exportAuditLogs = () => {
  // TODO: Implement audit log export functionality
  // Consider prompting for date range, format (CSV, JSON), etc.
  alert('Audit log export initiated (placeholder).');
};

const exportSystemLogs = () => {
  // TODO: Implement system log export functionality
  // Consider prompting for date range, log level, format, etc.
  alert('System log export initiated (placeholder).');
};

// TODO: Load initial values from backend and implement save logic for these settings
</script>
