/**
 * Authentication API Types
 * 
 * Type definitions for authentication API requests and responses
 */

import type { 
  User, 
  AuthSession, 
  LoginCredentials, 
  RegisterData, 
  PasswordResetRequest, 
  PasswordReset,
  TwoFactorSetup,
  TwoFactorVerification 
} from './models.js'; // Added .js extension

// Re-export types for direct import
export type {
  User,
  AuthSession,
  LoginCredentials,
  RegisterData,
  PasswordResetRequest,
  PasswordReset,
  TwoFactorSetup,
  TwoFactorVerification
};

// API Response wrapper
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  errors?: Record<string, string[]>
  meta?: {
    pagination?: {
      page: number
      limit: number
      total: number
      totalPages: number
    }
    timestamp: string
    requestId: string
  }
}

// Authentication API endpoints
export interface AuthApiEndpoints {
  // Authentication
  login: {
    request: LoginCredentials
    response: ApiResponse<AuthSession>
  }
  
  register: {
    request: RegisterData
    response: ApiResponse<{ user: User; requiresVerification: boolean }>
  }
  
  logout: {
    request: void
    response: ApiResponse<void>
  }
  
  refresh: {
    request: { refreshToken: string }
    response: ApiResponse<{ accessToken: string; expiresAt: string }>
  }
  
  // Profile management
  profile: {
    request: void
    response: ApiResponse<User>
  }
  
  updateProfile: {
    request: Partial<Pick<User, 'name' | 'phone' | 'address' | 'preferences'>>
    response: ApiResponse<User>
  }
  
  // Password management
  requestPasswordReset: {
    request: PasswordResetRequest
    response: ApiResponse<{ message: string }>
  }
  
  resetPassword: {
    request: PasswordReset
    response: ApiResponse<{ message: string }>
  }
  
  changePassword: {
    request: { currentPassword: string; newPassword: string; confirmPassword: string }
    response: ApiResponse<{ message: string }>
  }
  
  // Two-factor authentication
  setup2FA: {
    request: void
    response: ApiResponse<TwoFactorSetup>
  }
  
  verify2FA: {
    request: TwoFactorVerification
    response: ApiResponse<{ backupCodes: string[] }>
  }
  
  disable2FA: {
    request: { password: string; code?: string; backupCode?: string }
    response: ApiResponse<{ message: string }>
  }
  
  // Email verification
  verifyEmail: {
    request: { token: string }
    response: ApiResponse<{ message: string }>
  }
  
  resendVerification: {
    request: void
    response: ApiResponse<{ message: string }>
  }
}

// Error types
export interface AuthError {
  code: string
  message: string
  field?: string
}

export interface AuthValidationError {
  field: string
  message: string
  code: string
}

// Password reset types
export interface PasswordResetRequest {
  email: string
}

export interface PasswordReset {
  token: string
  password: string
  confirmPassword: string
}

// Token refresh types
export interface RefreshTokenRequest {
  refreshToken: string
}

// API client configuration
export interface AuthApiConfig {
  baseURL: string
  timeout: number
  retries: number
  headers: Record<string, string>
}
