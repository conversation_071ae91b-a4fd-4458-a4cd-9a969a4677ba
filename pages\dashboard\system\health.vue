<template>
  <div class="space-y-8">
    <!-- Health Overview -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <!-- Overall Health Score -->
      <UiCard
        icon="material-symbols:health-and-safety"
        icon-color="green"
        :loading="isLoading"
        hover-effect
      >
        <template #header>
          <div class="flex items-center justify-between w-full">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Health Score</h3>
            <UiBadge :variant="getHealthVariant(overallHealth.score)">
              {{ getHealthStatus(overallHealth.score) }}
            </UiBadge>
          </div>
        </template>
        <div class="space-y-3">
          <p class="text-4xl font-bold text-gray-900 dark:text-white">{{ overallHealth.score }}%</p>
          <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3">
            <div 
              :class="[
                'h-3 rounded-full transition-all duration-500',
                getHealthColor(overallHealth.score)
              ]"
              :style="{ width: `${overallHealth.score}%` }"
            ></div>
          </div>
          <p class="text-sm text-gray-500 dark:text-gray-400">
            Last updated: {{ formatTime(overallHealth.lastUpdated) }}
          </p>
        </div>
      </UiCard>

      <!-- System Uptime -->
      <UiCard
        icon="material-symbols:schedule"
        icon-color="blue"
        :loading="isLoading"
        hover-effect
      >
        <template #header>
          <div class="flex items-center justify-between w-full">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Uptime</h3>
            <UiBadge variant="success">{{ uptime.current }}%</UiBadge>
          </div>
        </template>
        <div class="space-y-3">
          <p class="text-4xl font-bold text-gray-900 dark:text-white">{{ uptime.days }}d</p>
          <div class="space-y-2">
            <div class="flex justify-between text-sm">
              <span class="text-gray-500 dark:text-gray-400">This Month</span>
              <span class="text-gray-900 dark:text-white">{{ uptime.thisMonth }}%</span>
            </div>
            <div class="flex justify-between text-sm">
              <span class="text-gray-500 dark:text-gray-400">This Year</span>
              <span class="text-gray-900 dark:text-white">{{ uptime.thisYear }}%</span>
            </div>
          </div>
        </div>
      </UiCard>

      <!-- Response Time -->
      <UiCard
        icon="material-symbols:speed"
        icon-color="purple"
        :loading="isLoading"
        hover-effect
      >
        <template #header>
          <div class="flex items-center justify-between w-full">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Response Time</h3>
            <UiBadge :variant="responseTime.avg <= 200 ? 'success' : responseTime.avg <= 500 ? 'warning' : 'error'">
              {{ responseTime.avg }}ms
            </UiBadge>
          </div>
        </template>
        <div class="space-y-3">
          <p class="text-4xl font-bold text-gray-900 dark:text-white">{{ responseTime.avg }}ms</p>
          <div class="space-y-2">
            <div class="flex justify-between text-sm">
              <span class="text-gray-500 dark:text-gray-400">P95</span>
              <span class="text-gray-900 dark:text-white">{{ responseTime.p95 }}ms</span>
            </div>
            <div class="flex justify-between text-sm">
              <span class="text-gray-500 dark:text-gray-400">P99</span>
              <span class="text-gray-900 dark:text-white">{{ responseTime.p99 }}ms</span>
            </div>
          </div>
        </div>
      </UiCard>

      <!-- Error Rate -->
      <UiCard
        icon="material-symbols:error"
        icon-color="red"
        :loading="isLoading"
        hover-effect
      >
        <template #header>
          <div class="flex items-center justify-between w-full">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Error Rate</h3>
            <UiBadge :variant="errorRate.current <= 0.1 ? 'success' : errorRate.current <= 1 ? 'warning' : 'error'">
              {{ errorRate.current }}%
            </UiBadge>
          </div>
        </template>
        <div class="space-y-3">
          <p class="text-4xl font-bold text-gray-900 dark:text-white">{{ errorRate.current }}%</p>
          <div class="space-y-2">
            <div class="flex justify-between text-sm">
              <span class="text-gray-500 dark:text-gray-400">24h Avg</span>
              <span class="text-gray-900 dark:text-white">{{ errorRate.avg24h }}%</span>
            </div>
            <div class="flex justify-between text-sm">
              <span class="text-gray-500 dark:text-gray-400">7d Avg</span>
              <span class="text-gray-900 dark:text-white">{{ errorRate.avg7d }}%</span>
            </div>
          </div>
        </div>
      </UiCard>
    </div>

    <!-- Service Health Details -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Services Status -->
      <UiCard>
        <template #header>
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Service Health</h3>
            <div class="flex items-center gap-2">
              <UiButton @click="refreshServices" variant="ghost" size="sm" :loading="refreshing">
                <Icon name="material-symbols:refresh" class="h-4 w-4" />
              </UiButton>
              <UiBadge :variant="allServicesHealthy ? 'success' : 'warning'">
                {{ healthyServicesCount }}/{{ services.length }} Healthy
              </UiBadge>
            </div>
          </div>
        </template>
        <div class="space-y-4">
          <div
            v-for="service in services"
            :key="service.id"
            class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors duration-200"
          >
            <div class="flex items-center gap-3">
              <div class="relative">
                <div :class="[
                  'w-4 h-4 rounded-full',
                  service.status === 'healthy' ? 'bg-green-500' :
                  service.status === 'degraded' ? 'bg-yellow-500' :
                  service.status === 'down' ? 'bg-red-500' : 'bg-gray-400'
                ]"></div>
                <div v-if="service.status === 'healthy'" class="absolute inset-0 w-4 h-4 rounded-full bg-green-500 animate-ping opacity-75"></div>
              </div>
              <div>
                <p class="text-sm font-medium text-gray-900 dark:text-white">{{ service.name }}</p>
                <p class="text-xs text-gray-500 dark:text-gray-400">{{ service.description }}</p>
              </div>
            </div>
            <div class="text-right">
              <p class="text-sm font-medium text-gray-900 dark:text-white">{{ service.uptime }}%</p>
              <p class="text-xs text-gray-500 dark:text-gray-400">{{ service.responseTime }}ms avg</p>
            </div>
          </div>
        </div>
      </UiCard>

      <!-- Health Checks -->
      <UiCard>
        <template #header>
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Health Checks</h3>
            <UiButton @click="runHealthChecks" variant="outline" size="sm" :loading="runningChecks">
              <Icon name="material-symbols:play-arrow" class="h-4 w-4 mr-2" />
              Run Checks
            </UiButton>
          </div>
        </template>
        <div class="space-y-4">
          <div
            v-for="check in healthChecks"
            :key="check.id"
            class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg"
          >
            <div class="flex items-center gap-3">
              <Icon 
                :name="check.status === 'passed' ? 'material-symbols:check-circle' : 
                      check.status === 'failed' ? 'material-symbols:error' : 
                      'material-symbols:schedule'"
                :class="[
                  'h-5 w-5',
                  check.status === 'passed' ? 'text-green-600' :
                  check.status === 'failed' ? 'text-red-600' :
                  'text-yellow-600'
                ]"
              />
              <div>
                <p class="text-sm font-medium text-gray-900 dark:text-white">{{ check.name }}</p>
                <p class="text-xs text-gray-500 dark:text-gray-400">{{ check.description }}</p>
              </div>
            </div>
            <div class="text-right">
              <UiBadge :variant="check.status === 'passed' ? 'success' : check.status === 'failed' ? 'error' : 'warning'">
                {{ check.status }}
              </UiBadge>
              <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">{{ check.lastRun }}</p>
            </div>
          </div>
        </div>
      </UiCard>
    </div>

    <!-- Resource Monitoring -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
      <!-- CPU Usage -->
      <UiCard>
        <template #header>
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">CPU Usage</h3>
            <UiBadge :variant="resources.cpu.current > 80 ? 'error' : resources.cpu.current > 60 ? 'warning' : 'success'">
              {{ resources.cpu.current }}%
            </UiBadge>
          </div>
        </template>
        <div class="space-y-4">
          <div class="text-center">
            <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ resources.cpu.current }}%</p>
          </div>
          <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3">
            <div 
              :class="[
                'h-3 rounded-full transition-all duration-300',
                resources.cpu.current > 80 ? 'bg-red-600' :
                resources.cpu.current > 60 ? 'bg-yellow-600' : 'bg-green-600'
              ]"
              :style="{ width: `${resources.cpu.current}%` }"
            ></div>
          </div>
          <div class="space-y-1">
            <div class="flex justify-between text-sm">
              <span class="text-gray-500 dark:text-gray-400">Average (24h)</span>
              <span class="text-gray-900 dark:text-white">{{ resources.cpu.avg24h }}%</span>
            </div>
            <div class="flex justify-between text-sm">
              <span class="text-gray-500 dark:text-gray-400">Peak (24h)</span>
              <span class="text-gray-900 dark:text-white">{{ resources.cpu.peak24h }}%</span>
            </div>
          </div>
        </div>
      </UiCard>

      <!-- Memory Usage -->
      <UiCard>
        <template #header>
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Memory Usage</h3>
            <UiBadge :variant="resources.memory.current > 85 ? 'error' : resources.memory.current > 70 ? 'warning' : 'success'">
              {{ resources.memory.current }}%
            </UiBadge>
          </div>
        </template>
        <div class="space-y-4">
          <div class="text-center">
            <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ resources.memory.current }}%</p>
            <p class="text-sm text-gray-500 dark:text-gray-400">{{ resources.memory.used }}GB / {{ resources.memory.total }}GB</p>
          </div>
          <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3">
            <div 
              :class="[
                'h-3 rounded-full transition-all duration-300',
                resources.memory.current > 85 ? 'bg-red-600' :
                resources.memory.current > 70 ? 'bg-yellow-600' : 'bg-blue-600'
              ]"
              :style="{ width: `${resources.memory.current}%` }"
            ></div>
          </div>
          <div class="space-y-1">
            <div class="flex justify-between text-sm">
              <span class="text-gray-500 dark:text-gray-400">Available</span>
              <span class="text-gray-900 dark:text-white">{{ resources.memory.available }}GB</span>
            </div>
            <div class="flex justify-between text-sm">
              <span class="text-gray-500 dark:text-gray-400">Cached</span>
              <span class="text-gray-900 dark:text-white">{{ resources.memory.cached }}GB</span>
            </div>
          </div>
        </div>
      </UiCard>

      <!-- Disk Usage -->
      <UiCard>
        <template #header>
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Disk Usage</h3>
            <UiBadge :variant="resources.disk.current > 90 ? 'error' : resources.disk.current > 75 ? 'warning' : 'success'">
              {{ resources.disk.current }}%
            </UiBadge>
          </div>
        </template>
        <div class="space-y-4">
          <div class="text-center">
            <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ resources.disk.current }}%</p>
            <p class="text-sm text-gray-500 dark:text-gray-400">{{ resources.disk.used }}TB / {{ resources.disk.total }}TB</p>
          </div>
          <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3">
            <div 
              :class="[
                'h-3 rounded-full transition-all duration-300',
                resources.disk.current > 90 ? 'bg-red-600' :
                resources.disk.current > 75 ? 'bg-yellow-600' : 'bg-purple-600'
              ]"
              :style="{ width: `${resources.disk.current}%` }"
            ></div>
          </div>
          <div class="space-y-1">
            <div class="flex justify-between text-sm">
              <span class="text-gray-500 dark:text-gray-400">Free Space</span>
              <span class="text-gray-900 dark:text-white">{{ resources.disk.free }}TB</span>
            </div>
            <div class="flex justify-between text-sm">
              <span class="text-gray-500 dark:text-gray-400">Growth Rate</span>
              <span class="text-gray-900 dark:text-white">{{ resources.disk.growthRate }}GB/day</span>
            </div>
          </div>
        </div>
      </UiCard>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { PlatformRoles } from '~/app/features/auth/constants/roles'

// Page meta configuration
definePageMeta({
  layout: 'dashboard',
  title: 'System Health',
  description: 'Monitor system health, performance metrics, and service status',
  pageHeaderIcon: 'material-symbols:monitor-heart',
  pageHeaderStats: [
    { key: 'health', label: 'Health Score', value: '98%', color: 'green' },
    { key: 'uptime', label: 'Uptime', value: '99.9%', color: 'blue' },
    { key: 'services', label: 'Healthy Services', value: '12/12', color: 'purple' },
    { key: 'response', label: 'Avg Response', value: '145ms', color: 'yellow' }
  ],
  showRealTimeStatus: true,
  autoRefreshEnabled: true,
  refreshInterval: 15000, // 15 seconds for health monitoring
  middleware: ['rbac'],
  roles: [PlatformRoles.SUPER_ADMIN],
  breadcrumb: [
    { label: 'Dashboard', href: '/dashboard' },
    { label: 'Platform', href: '/dashboard/platform' },
    { label: 'System Health' },
  ],
})

// Reactive state
const isLoading = ref(true)
const refreshing = ref(false)
const runningChecks = ref(false)
const refreshInterval = ref<NodeJS.Timeout | null>(null)

// Health metrics
const overallHealth = ref({
  score: 98,
  lastUpdated: new Date()
})

const uptime = ref({
  current: 99.9,
  days: 127,
  thisMonth: 99.8,
  thisYear: 99.7
})

const responseTime = ref({
  avg: 145,
  p95: 280,
  p99: 450
})

const errorRate = ref({
  current: 0.05,
  avg24h: 0.08,
  avg7d: 0.12
})

// Services data
const services = ref([
  {
    id: 1,
    name: 'API Gateway',
    description: 'Core API routing and load balancing',
    status: 'healthy',
    uptime: 99.9,
    responseTime: 120
  },
  {
    id: 2,
    name: 'Database Cluster',
    description: 'Primary PostgreSQL cluster',
    status: 'healthy',
    uptime: 99.8,
    responseTime: 45
  },
  {
    id: 3,
    name: 'Redis Cache',
    description: 'In-memory data structure store',
    status: 'healthy',
    uptime: 99.9,
    responseTime: 12
  },
  {
    id: 4,
    name: 'File Storage',
    description: 'S3-compatible object storage',
    status: 'healthy',
    uptime: 99.7,
    responseTime: 89
  },
  {
    id: 5,
    name: 'Email Service',
    description: 'SMTP and notification delivery',
    status: 'degraded',
    uptime: 99.5,
    responseTime: 234
  },
  {
    id: 6,
    name: 'Search Engine',
    description: 'Elasticsearch document indexing',
    status: 'healthy',
    uptime: 98.9,
    responseTime: 156
  },
  {
    id: 7,
    name: 'Authentication',
    description: 'OAuth2 and JWT token service',
    status: 'healthy',
    uptime: 99.9,
    responseTime: 78
  },
  {
    id: 8,
    name: 'Background Jobs',
    description: 'Async task processing queue',
    status: 'healthy',
    uptime: 99.6,
    responseTime: 95
  }
])

// Health checks
const healthChecks = ref([
  {
    id: 1,
    name: 'Database Connectivity',
    description: 'Test database connection and query performance',
    status: 'passed',
    lastRun: '2 minutes ago'
  },
  {
    id: 2,
    name: 'External API Endpoints',
    description: 'Verify third-party service availability',
    status: 'passed',
    lastRun: '5 minutes ago'
  },
  {
    id: 3,
    name: 'SSL Certificate Validity',
    description: 'Check SSL certificate expiration',
    status: 'passed',
    lastRun: '1 hour ago'
  },
  {
    id: 4,
    name: 'Disk Space Monitoring',
    description: 'Monitor available disk space across servers',
    status: 'passed',
    lastRun: '10 minutes ago'
  },
  {
    id: 5,
    name: 'Memory Leak Detection',
    description: 'Scan for potential memory leaks',
    status: 'running',
    lastRun: 'In progress'
  },
  {
    id: 6,
    name: 'Security Vulnerability Scan',
    description: 'Check for known security vulnerabilities',
    status: 'passed',
    lastRun: '6 hours ago'
  }
])

// Resource monitoring
const resources = ref({
  cpu: {
    current: 45,
    avg24h: 52,
    peak24h: 78
  },
  memory: {
    current: 68,
    used: 27.2,
    total: 40,
    available: 12.8,
    cached: 8.4
  },
  disk: {
    current: 42,
    used: 2.1,
    total: 5.0,
    free: 2.9,
    growthRate: 15.2
  }
})

// Computed properties
const allServicesHealthy = computed(() =>
  services.value.every(service => service.status === 'healthy')
)

const healthyServicesCount = computed(() =>
  services.value.filter(service => service.status === 'healthy').length
)

// Utility functions
const getHealthVariant = (score: number) => {
  if (score >= 95) return 'success'
  if (score >= 80) return 'warning'
  return 'error'
}

const getHealthStatus = (score: number) => {
  if (score >= 95) return 'Excellent'
  if (score >= 80) return 'Good'
  if (score >= 60) return 'Fair'
  return 'Poor'
}

const getHealthColor = (score: number) => {
  if (score >= 95) return 'bg-green-600'
  if (score >= 80) return 'bg-yellow-600'
  return 'bg-red-600'
}

const formatTime = (date: Date) => {
  return date.toLocaleTimeString('en-US', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// Methods
const refreshServices = async () => {
  try {
    refreshing.value = true

    // Simulate API call to refresh service status
    await new Promise(resolve => setTimeout(resolve, 1500))

    // In a real app, you would fetch fresh data from monitoring APIs
    // const serviceStatus = await $api.get('/platform/services/status')

    // Simulate some status changes
    services.value.forEach(service => {
      if (Math.random() > 0.9) {
        service.responseTime = Math.max(50, service.responseTime + (Math.random() - 0.5) * 50)
      }
    })

    console.log('Services refreshed')

  } catch (error) {
    console.error('Error refreshing services:', error)
  } finally {
    refreshing.value = false
  }
}

const runHealthChecks = async () => {
  try {
    runningChecks.value = true

    // Simulate running health checks
    for (const check of healthChecks.value) {
      check.status = 'running'
      check.lastRun = 'Running...'
    }

    // Simulate check completion
    await new Promise(resolve => setTimeout(resolve, 3000))

    healthChecks.value.forEach(check => {
      check.status = Math.random() > 0.1 ? 'passed' : 'failed'
      check.lastRun = 'Just now'
    })

    console.log('Health checks completed')

  } catch (error) {
    console.error('Error running health checks:', error)
  } finally {
    runningChecks.value = false
  }
}

const fetchHealthMetrics = async () => {
  try {
    // In a real app, fetch from monitoring APIs
    // const metrics = await $api.get('/platform/health/metrics')

    // Simulate metric updates
    overallHealth.value.lastUpdated = new Date()

    // Add some realistic variation to metrics
    resources.value.cpu.current = Math.max(20, Math.min(90, resources.value.cpu.current + (Math.random() - 0.5) * 10))
    resources.value.memory.current = Math.max(30, Math.min(95, resources.value.memory.current + (Math.random() - 0.5) * 5))
    resources.value.disk.current = Math.max(20, Math.min(95, resources.value.disk.current + (Math.random() - 0.5) * 2))

    responseTime.value.avg = Math.max(100, Math.min(300, responseTime.value.avg + (Math.random() - 0.5) * 20))
    errorRate.value.current = Math.max(0, Math.min(2, errorRate.value.current + (Math.random() - 0.5) * 0.1))

    // Update overall health score based on metrics
    const healthFactors = [
      resources.value.cpu.current < 80 ? 100 : 100 - (resources.value.cpu.current - 80) * 2,
      resources.value.memory.current < 85 ? 100 : 100 - (resources.value.memory.current - 85) * 3,
      responseTime.value.avg < 200 ? 100 : 100 - (responseTime.value.avg - 200) / 10,
      errorRate.value.current < 0.5 ? 100 : 100 - errorRate.value.current * 20
    ]

    overallHealth.value.score = Math.round(healthFactors.reduce((a, b) => a + b) / healthFactors.length)

  } catch (error) {
    console.error('Error fetching health metrics:', error)
  }
}

// Lifecycle hooks
onMounted(async () => {
  // Initial data load
  await fetchHealthMetrics()
  isLoading.value = false

  // Set up auto-refresh interval
  refreshInterval.value = setInterval(fetchHealthMetrics, 15000)
})

onUnmounted(() => {
  if (refreshInterval.value) {
    clearInterval(refreshInterval.value)
  }
})
</script>

<style scoped>
/* Enhanced animations and transitions */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* Pulse animation for healthy status indicators */
@keyframes pulse-green {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-ping {
  animation: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;
}

@keyframes ping {
  75%, 100% {
    transform: scale(2);
    opacity: 0;
  }
}

/* Progress bar animations */
.transition-all {
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Card hover effects */
.hover\:bg-gray-100:hover {
  background-color: rgb(243 244 246);
}

.dark .hover\:bg-gray-600:hover {
  background-color: rgb(75 85 99);
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .grid-cols-1 {
    gap: 1rem;
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Focus states */
.focus-ring:focus {
  outline: 2px solid var(--color-brandPrimary-500);
  outline-offset: 2px;
}
</style>
