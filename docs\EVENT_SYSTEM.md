# Event System Documentation

The Legal SaaS Frontend includes a comprehensive event system that provides type-safe, reactive event handling across the entire application. This system enables loose coupling between components, real-time updates, and centralized event management.

## 🏗️ Architecture Overview

The event system consists of several key components:

- **Event Bus** (`utils/eventBus.ts`) - Core event emitter using mitt
- **Event Types** (`types/events.ts`) - Type-safe event definitions
- **Event Listeners** (`composables/useEventListeners.ts`) - Vue composable for listening to events
- **Event Emitters** (`composables/useEventEmitters.ts`) - Vue composable for emitting events
- **Real-time Events** (`composables/useRealtimeEvents.ts`) - WebSocket and SSE integration
- **Plugin Integration** (`plugins/eventBus.client.ts`) - Nuxt plugin for global access

## 🚀 Quick Start

### Basic Usage

```vue
<script setup>
import { useEventListeners, useEventEmitters } from '#imports'

const { on, once, off } = useEventListeners()
const { emitCaseCreated, emitDocumentUploaded } = useEventEmitters()

// Listen to events
on('case:created', (data) => {
  console.log('New case created:', data.caseTitle)
})

// Emit events
const createCase = () => {
  emitCaseCreated({
    caseId: 'case-123',
    caseTitle: 'New Legal Case',
    caseType: 'litigation',
    clientId: 'client-456',
    assignedTo: ['user-1']
  })
}
</script>
```

### Global Access

```vue
<script setup>
const { $eventBus } = useNuxtApp()

// Direct access to event bus
$eventBus.emit('case:created', data)
$eventBus.on('case:updated', handler)
</script>
```

## 📋 Event Categories

### Business Domain Events

#### Case Events
- `case:created` - New case created
- `case:updated` - Case information updated
- `case:status-changed` - Case status changed
- `case:deleted` - Case deleted
- `case:assigned` - Case assigned to users

#### Document Events
- `document:uploaded` - Document uploaded
- `document:shared` - Document shared with users
- `document:downloaded` - Document downloaded
- `document:deleted` - Document deleted
- `document:version-created` - New document version

#### Template Events
- `template:created` - Template created
- `template:updated` - Template updated
- `template:used` - Template used
- `template:deleted` - Template deleted
- `template:published` - Template published

#### User Events
- `user:invited` - User invited
- `user:role-changed` - User role changed
- `user:activated` - User activated
- `user:deactivated` - User deactivated
- `user:login` - User logged in
- `user:logout` - User logged out

### UI Events
- `ui:navigation` - Navigation occurred
- `ui:search` - Search performed
- `ui:filter` - Filter applied
- `ui:bulk-action` - Bulk action performed
- `ui:view-changed` - View mode changed
- `ui:modal-opened` - Modal opened
- `ui:modal-closed` - Modal closed

### System Events
- `system:error` - System error occurred
- `system:performance` - Performance metric recorded
- `system:maintenance` - Maintenance mode

### Application Events
- `app:ready` - Application ready
- `app:error` - Application error
- `app:theme-changed` - Theme changed
- `app:language-changed` - Language changed

## 🎯 Advanced Features

### Event Listener Options

```typescript
// Listen once
on('case:created', handler, { once: true })

// Debounced listener (300ms)
on('ui:search', handler, { debounce: 300 })

// Throttled listener (1000ms)
on('ui:filter', handler, { throttle: 1000 })
```

### Specialized Composables

```vue
<script setup>
import { useCaseEvents, useDocumentEvents, useUIEvents } from '#imports'

// Case-specific events
const { onCaseCreated, onCaseUpdated, emitCaseCreated } = useCaseEvents()

// Document-specific events
const { onDocumentUploaded, emitDocumentUploaded } = useDocumentEvents()

// UI-specific events
const { onNavigation, onSearch, emitSearch } = useUIEvents()
</script>
```

### Real-time Integration

```vue
<script setup>
import { useRealtimeEvents } from '#imports'

const { 
  isConnected, 
  connectionStatus, 
  connect, 
  disconnect 
} = useRealtimeEvents({
  websocketUrl: 'wss://api.example.com/ws',
  sseUrl: '/api/events',
  reconnectInterval: 5000,
  maxReconnectAttempts: 5
})

// Connect to real-time events
await connect()
</script>
```

## 🔧 Integration Examples

### Analytics Integration

The event system automatically integrates with the analytics system:

```typescript
// Automatically tracked
emitCaseCreated(data) // → trackCaseEvent('created', caseId, properties)
emitDocumentUploaded(data) // → trackDocumentEvent('uploaded', docId, properties)
emitSearch(data) // → trackEvent('search_performed', properties)
```

### Error Tracking Integration

```typescript
// System errors are automatically captured
emitSystemError({
  error: new Error('Something went wrong'),
  context: { component: 'CaseForm', action: 'submit' },
  severity: 'high'
})
// → Automatically sent to error tracking service
```

### Notification System Integration

```typescript
// Notifications are automatically displayed
emitNotificationCreated({
  type: 'success',
  title: 'Case Created',
  message: 'Your case has been created successfully',
  priority: 'medium'
})
// → Automatically shown as toast notification
```

## 🧪 Testing

### Unit Testing Events

```typescript
import { describe, it, expect, vi } from 'vitest'
import { getEventBus } from '~/utils/eventBus'

describe('Event System', () => {
  it('should emit and receive events', () => {
    const eventBus = getEventBus()
    const handler = vi.fn()
    
    eventBus.on('case:created', handler)
    eventBus.emit('case:created', {
      caseId: 'test-case',
      caseTitle: 'Test Case',
      timestamp: new Date().toISOString()
    })
    
    expect(handler).toHaveBeenCalledWith(
      expect.objectContaining({
        caseId: 'test-case',
        caseTitle: 'Test Case'
      })
    )
  })
})
```

### Component Testing

```vue
<!-- TestComponent.vue -->
<script setup>
import { useEventListeners } from '#imports'

const { on } = useEventListeners()
const receivedEvents = ref([])

on('case:created', (data) => {
  receivedEvents.value.push(data)
})
</script>
```

## 🔍 Debugging

### Development Tools

In development mode, the event system provides debugging tools:

```javascript
// Browser console access
window.__eventBus.listenerCount() // Get total listeners
window.__eventBus.getEvents() // Get all registered events
window.__eventBus.setDebugMode(true) // Enable debug logging
```

### Event Logging

All events are automatically logged in development:

```
🔔 [EventBus] case:created: { caseId: 'case-123', caseTitle: 'New Case', ... }
👂 Listener added for: case:created
🔇 Listener removed for: case:created
```

## 📊 Performance Considerations

### Memory Management

- Event listeners are automatically cleaned up on component unmount
- Use `clear()` to manually remove all listeners
- Use `once()` for one-time listeners to prevent memory leaks

### Debouncing and Throttling

```typescript
// Debounce search events
on('ui:search', handler, { debounce: 300 })

// Throttle scroll events
on('ui:scroll', handler, { throttle: 100 })
```

### Event Batching

For high-frequency events, consider batching:

```typescript
const batchedEvents = []
const flushBatch = debounce(() => {
  emitBulkAction({
    action: 'batch-update',
    items: batchedEvents.splice(0)
  })
}, 500)

// Add to batch instead of emitting immediately
batchedEvents.push(eventData)
flushBatch()
```

## 🛠️ Best Practices

1. **Use Type-Safe Events**: Always use the predefined event types
2. **Clean Up Listeners**: Let composables handle cleanup automatically
3. **Avoid Event Chains**: Don't emit events in response to other events
4. **Use Specific Events**: Prefer specific events over generic ones
5. **Document Custom Events**: Add new events to the EventMap interface
6. **Test Event Flows**: Write tests for critical event interactions
7. **Monitor Performance**: Use debouncing/throttling for high-frequency events

## � Error Handling

### Event Error Boundaries

```typescript
// Wrap event handlers in try-catch
on('case:created', async (data) => {
  try {
    await processCase(data)
  } catch (error) {
    emitSystemError({
      error,
      context: { event: 'case:created', caseId: data.caseId },
      severity: 'medium'
    })
  }
})
```

### Graceful Degradation

```typescript
// Handle missing event data gracefully
on('document:uploaded', (data) => {
  if (!data.documentId) {
    console.warn('Document upload event missing documentId')
    return
  }

  // Process valid event
  processDocumentUpload(data)
})
```

## 🔄 Migration Guide

### From Direct Component Communication

**Before:**
```vue
<!-- Parent.vue -->
<ChildComponent @case-created="handleCaseCreated" />

<!-- Child.vue -->
<script setup>
const emit = defineEmits(['case-created'])
const createCase = () => emit('case-created', caseData)
</script>
```

**After:**
```vue
<!-- Parent.vue -->
<script setup>
const { on } = useEventListeners()
on('case:created', handleCaseCreated)
</script>

<!-- Child.vue -->
<script setup>
const { emitCaseCreated } = useEventEmitters()
const createCase = () => emitCaseCreated(caseData)
</script>
```

### From Pinia Store Events

**Before:**
```typescript
// store/cases.ts
export const useCaseStore = defineStore('cases', () => {
  const cases = ref([])

  const addCase = (caseData) => {
    cases.value.push(caseData)
    // Manual notification
    notifyComponents('case-added', caseData)
  }
})
```

**After:**
```typescript
// store/cases.ts
export const useCaseStore = defineStore('cases', () => {
  const cases = ref([])
  const { emitCaseCreated } = useEventEmitters()

  const addCase = (caseData) => {
    cases.value.push(caseData)
    // Automatic event emission
    emitCaseCreated(caseData)
  }
})
```

## 🎨 Custom Event Types

### Adding New Events

1. **Define the event interface:**

```typescript
// types/events.ts
export interface CustomBusinessEvent extends BaseEvent {
  customId: string
  customData: any
}
```

2. **Add to EventMap:**

```typescript
// types/events.ts
export interface EventMap {
  // ... existing events
  'custom:business-action': CustomBusinessEvent
}
```

3. **Create emitter helper:**

```typescript
// composables/useEventEmitters.ts
const emitCustomBusinessAction = (data: Omit<EventMap['custom:business-action'], 'timestamp'>) => {
  eventBus.emit('custom:business-action', {
    ...data,
    timestamp: new Date().toISOString()
  })
}
```

### Event Naming Conventions

- Use kebab-case: `case:created`, `document:uploaded`
- Include entity type: `case:*`, `document:*`, `user:*`
- Use action verbs: `created`, `updated`, `deleted`, `shared`
- Be specific: `case:status-changed` vs `case:changed`

## �🔗 Related Documentation

- [Vue 3 Composables](https://vuejs.org/guide/reusability/composables.html)
- [Mitt Event Emitter](https://github.com/developit/mitt)
- [WebSocket API](https://developer.mozilla.org/en-US/docs/Web/API/WebSocket)
- [Server-Sent Events](https://developer.mozilla.org/en-US/docs/Web/API/Server-sent_events)
- [TypeScript Event Handling](https://www.typescriptlang.org/docs/handbook/2/functions.html#function-overloads)
