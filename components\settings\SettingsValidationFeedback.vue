<template>
  <div v-if="showFeedback" class="space-y-4">
    <!-- Success State -->
    <div
      v-if="type === 'success'"
      class="rounded-lg bg-brandSuccess-lightBg border border-brandSuccess-lightBorder p-4"
    >
      <div class="flex items-start">
        <div class="flex-shrink-0">
          <Icon name="heroicons:check-circle" class="h-5 w-5 text-brandSuccess" />
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-brandSuccess-darkText">
            {{ title || 'Success' }}
          </h3>
          <div v-if="message" class="mt-1 text-sm text-brandSuccess-darkText">
            {{ message }}
          </div>
          <div v-if="details && details.length > 0" class="mt-2">
            <ul class="list-disc list-inside text-sm text-brandSuccess-darkText space-y-1">
              <li v-for="detail in details" :key="detail">{{ detail }}</li>
            </ul>
          </div>
        </div>
        <div v-if="dismissible" class="ml-auto pl-3">
          <button
            @click="$emit('dismiss')"
            class="inline-flex rounded-md bg-brandSuccess-lightBg text-brandSuccess-darkText hover:bg-brandSuccess-lightBorder focus:outline-none focus:ring-2 focus:ring-brandSuccess focus:ring-offset-2 focus:ring-offset-brandSuccess-lightBg"
          >
            <Icon name="heroicons:x-mark" class="h-4 w-4" />
          </button>
        </div>
      </div>
    </div>

    <!-- Error State -->
    <div
      v-else-if="type === 'error'"
      class="rounded-lg bg-brandDanger-lightBg border border-brandDanger-lightBorder p-4"
    >
      <div class="flex items-start">
        <div class="flex-shrink-0">
          <Icon name="heroicons:exclamation-circle" class="h-5 w-5 text-brandDanger" />
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-brandDanger-darkText">
            {{ title || 'Error' }}
          </h3>
          <div v-if="message" class="mt-1 text-sm text-brandDanger-darkText">
            {{ message }}
          </div>
          <div v-if="details && details.length > 0" class="mt-2">
            <ul class="list-disc list-inside text-sm text-brandDanger-darkText space-y-1">
              <li v-for="detail in details" :key="detail">{{ detail }}</li>
            </ul>
          </div>
        </div>
        <div v-if="dismissible" class="ml-auto pl-3">
          <button
            @click="$emit('dismiss')"
            class="inline-flex rounded-md bg-brandDanger-lightBg text-brandDanger-darkText hover:bg-brandDanger-lightBorder focus:outline-none focus:ring-2 focus:ring-brandDanger focus:ring-offset-2 focus:ring-offset-brandDanger-lightBg"
          >
            <Icon name="heroicons:x-mark" class="h-4 w-4" />
          </button>
        </div>
      </div>
    </div>

    <!-- Warning State -->
    <div
      v-else-if="type === 'warning'"
      class="rounded-lg bg-brandWarning-lightBg border border-brandWarning-lightBorder p-4"
    >
      <div class="flex items-start">
        <div class="flex-shrink-0">
          <Icon name="heroicons:exclamation-triangle" class="h-5 w-5 text-brandWarning-darkText" />
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-brandWarning-darkText">
            {{ title || 'Warning' }}
          </h3>
          <div v-if="message" class="mt-1 text-sm text-brandWarning-darkText">
            {{ message }}
          </div>
          <div v-if="details && details.length > 0" class="mt-2">
            <ul class="list-disc list-inside text-sm text-brandWarning-darkText space-y-1">
              <li v-for="detail in details" :key="detail">{{ detail }}</li>
            </ul>
          </div>
        </div>
        <div v-if="dismissible" class="ml-auto pl-3">
          <button
            @click="$emit('dismiss')"
            class="inline-flex rounded-md bg-brandWarning-lightBg text-brandWarning-darkText hover:bg-brandWarning-lightBorder focus:outline-none focus:ring-2 focus:ring-brandWarning focus:ring-offset-2 focus:ring-offset-brandWarning-lightBg"
          >
            <Icon name="heroicons:x-mark" class="h-4 w-4" />
          </button>
        </div>
      </div>
    </div>

    <!-- Info State -->
    <div
      v-else-if="type === 'info'"
      class="rounded-lg bg-brandInfo-lightBg border border-brandInfo-lightBorder p-4"
    >
      <div class="flex items-start">
        <div class="flex-shrink-0">
          <Icon name="heroicons:information-circle" class="h-5 w-5 text-brandInfo" />
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-brandInfo-darkText">
            {{ title || 'Information' }}
          </h3>
          <div v-if="message" class="mt-1 text-sm text-brandInfo-darkText">
            {{ message }}
          </div>
          <div v-if="details && details.length > 0" class="mt-2">
            <ul class="list-disc list-inside text-sm text-brandInfo-darkText space-y-1">
              <li v-for="detail in details" :key="detail">{{ detail }}</li>
            </ul>
          </div>
        </div>
        <div v-if="dismissible" class="ml-auto pl-3">
          <button
            @click="$emit('dismiss')"
            class="inline-flex rounded-md bg-brandInfo-lightBg text-brandInfo-darkText hover:bg-brandInfo-lightBorder focus:outline-none focus:ring-2 focus:ring-brandInfo focus:ring-offset-2 focus:ring-offset-brandInfo-lightBg"
          >
            <Icon name="heroicons:x-mark" class="h-4 w-4" />
          </button>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div
      v-else-if="type === 'loading'"
      class="rounded-lg bg-gray-50 border border-gray-200 p-4"
    >
      <div class="flex items-start">
        <div class="flex-shrink-0">
          <Icon name="heroicons:arrow-path" class="h-5 w-5 text-gray-400 animate-spin" />
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-gray-900">
            {{ title || 'Loading' }}
          </h3>
          <div v-if="message" class="mt-1 text-sm text-gray-600">
            {{ message }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

interface Props {
  type: 'success' | 'error' | 'warning' | 'info' | 'loading';
  title?: string;
  message?: string;
  details?: string[];
  dismissible?: boolean;
  show?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  dismissible: false,
  show: true
});

defineEmits<{
  dismiss: [];
}>();

const showFeedback = computed(() => {
  return props.show && (props.title || props.message || (props.details && props.details.length > 0));
});
</script>
