<template>
  <div class="space-y-6"> <!-- Changed space-y-4 to space-y-6 for consistency with other main setting pages -->
    <UiCard title="Existing Default Behaviors">
      <div class="space-y-4">
        <div>
          <label for="defaultCaseStatus" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Default Case Status for New Cases</label>
          <UiSelect
            id="defaultCaseStatus"
            :name="'defaultCaseStatus'"
            :options="[{ value: 'DRAFT', label: 'Draft' }, { value: 'OPEN', label: 'Open' }, { value: 'PENDING', label: 'Pending' }]"
            v-model="form.defaultCaseStatus"
            @update:modelValue="updateSetting('defaultCaseStatus', $event)"
            class="mt-1"/>
          <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">Sets the initial status for newly created cases.</p>
        </div>

        <div>
          <div class="flex items-center justify-between">
            <span class="flex-grow text-sm font-medium text-gray-700 dark:text-gray-300">Require 2FA for all new users</span>
            <UiToggle
              v-model="form.require2FAForNewUsers"
              @update:modelValue="updateSetting('require2FAForNewUsers', $event)"
              label="Require 2FA for new users"
              :hide-label="true"
            />
          </div>
          <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">Mandates Two-Factor Authentication setup for all new user registrations.</p>
        </div>
      </div>
    </UiCard>

    <!-- Default User Experience Section -->
    <UiCard title="Default User Experience">
      <div class="space-y-4">
        <div>
          <div class="flex items-center justify-between">
            <span class="flex-grow text-sm font-medium text-gray-700 dark:text-gray-300">Enable Dark Mode by Default for New Users</span>
            <UiToggle v-model="form.defaultDarkModeForNewUsers" @update:modelValue="updateSetting('defaultDarkModeForNewUsers', $event)" label="Dark mode default" :hide-label="true" />
          </div>
          <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">Sets the initial theme for new users.</p>
        </div>
        <div>
          <label for="defaultDashboardLandingPage" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Default Dashboard Landing Page</label>
          <UiSelect id="defaultDashboardLandingPage" :name="'defaultDashboardLandingPage'" v-model="form.defaultDashboardLandingPage" :options="dashboardLandingPageOptions" @update:modelValue="updateSetting('defaultDashboardLandingPage', $event)" class="mt-1" />
          <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">The first page users see after logging into the dashboard.</p>
        </div>
        <div>
          <label for="sessionTimeoutWarningMinutes" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Session Timeout Warning (minutes before timeout)</label>
          <UiInput id="sessionTimeoutWarningMinutes" :name="'sessionTimeoutWarningMinutes'" v-model.number="form.sessionTimeoutWarningMinutes" type="number" placeholder="e.g., 5" @update:modelValue="updateSetting('sessionTimeoutWarningMinutes', $event)" class="mt-1" />
          <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">Shows a warning this many minutes before automatic logout due to inactivity.</p>
        </div>
      </div>
    </UiCard>

    <!-- Default Content & Editor Settings Section -->
    <UiCard title="Default Content & Editor Settings">
      <div class="space-y-4">
        <div>
          <label for="defaultEditorFont" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Default Font for Editors</label>
          <UiSelect id="defaultEditorFont" :name="'defaultEditorFont'" v-model="form.defaultEditorFont" :options="editorFontOptions" @update:modelValue="updateSetting('defaultEditorFont', $event)" class="mt-1" />
        </div>
        <div>
          <label for="defaultEditorFontSize" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Default Font Size for Editors (px)</label>
          <UiInput id="defaultEditorFontSize" :name="'defaultEditorFontSize'" v-model.number="form.defaultEditorFontSize" type="number" placeholder="e.g., 14" @update:modelValue="updateSetting('defaultEditorFontSize', $event)" class="mt-1" />
        </div>
        <div>
          <div class="flex items-center justify-between">
            <span class="flex-grow text-sm font-medium text-gray-700 dark:text-gray-300">Enable Autosave in Editors by Default</span>
            <UiToggle v-model="form.enableEditorAutosave" @update:modelValue="updateSetting('enableEditorAutosave', $event)" label="Autosave default" :hide-label="true" />
          </div>
        </div>
        <div v-if="form.enableEditorAutosave">
          <label for="editorAutosaveInterval" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Autosave Interval (seconds)</label>
          <UiInput id="editorAutosaveInterval" :name="'editorAutosaveInterval'" v-model.number="form.editorAutosaveInterval" type="number" placeholder="e.g., 60" @update:modelValue="updateSetting('editorAutosaveInterval', $event)" class="mt-1" />
        </div>
      </div>
    </UiCard>

    <!-- Default File & Upload Behaviors Section -->
    <UiCard title="Default File & Upload Behaviors">
      <div class="space-y-4">
        <div>
          <label for="maxFileUploadSizeMB" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Maximum File Upload Size (MB)</label>
          <UiInput id="maxFileUploadSizeMB" :name="'maxFileUploadSizeMB'" v-model.number="form.maxFileUploadSizeMB" type="number" placeholder="e.g., 25" @update:modelValue="updateSetting('maxFileUploadSizeMB', $event)" class="mt-1" />
        </div>
        <div>
          <label for="allowedFileUploadTypes" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Allowed File Types for Upload (comma-separated)</label>
          <UiInput id="allowedFileUploadTypes" :name="'allowedFileUploadTypes'" v-model="form.allowedFileUploadTypes" type="text" placeholder=".pdf,.doc,.docx,.xls,.xlsx,.jpg,.png" @update:modelValue="updateSetting('allowedFileUploadTypes', $event)" class="mt-1" />
          <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">Enter extensions with a leading dot, separated by commas.</p>
        </div>
        <div>
          <label for="defaultDocumentVisibility" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Default Document Visibility for New Uploads</label>
          <UiSelect id="defaultDocumentVisibility" :name="'defaultDocumentVisibility'" v-model="form.defaultDocumentVisibility" :options="documentVisibilityOptions" @update:modelValue="updateSetting('defaultDocumentVisibility', $event)" class="mt-1" />
        </div>
      </div>
    </UiCard>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useApi } from '~/composables/useApi';
import { useToast } from '~/composables/useToast';
import UiCard from '~/components/ui/UiCard.vue'; // Added UiCard
import UiInput from '~/components/ui/UiInput.vue'; // Added UiInput
import UiSelect from '~/components/ui/UiSelect.vue'; // Ensured UiSelect is imported (was already used)
import UiToggle from '~/components/ui/UiToggle.vue'; // Ensured UiToggle is imported (was already used)


const { get, patch } = useApi();
const { showToast } = useToast();

const form = ref<{
  defaultCaseStatus: string;
  require2FAForNewUsers: boolean;
  // New fields for Default User Experience
  defaultDarkModeForNewUsers: boolean;
  defaultDashboardLandingPage: string;
  sessionTimeoutWarningMinutes: number;
  // New fields for Default Content & Editor Settings
  defaultEditorFont: string;
  defaultEditorFontSize: number;
  enableEditorAutosave: boolean;
  editorAutosaveInterval: number;
  // New fields for Default File & Upload Behaviors
  maxFileUploadSizeMB: number;
  allowedFileUploadTypes: string;
  defaultDocumentVisibility: string;
}>({
  defaultCaseStatus: 'DRAFT',
  require2FAForNewUsers: false,
  // Defaults for User Experience
  defaultDarkModeForNewUsers: false,
  defaultDashboardLandingPage: 'overview',
  sessionTimeoutWarningMinutes: 5,
  // Defaults for Content & Editor
  defaultEditorFont: 'Arial',
  defaultEditorFontSize: 14,
  enableEditorAutosave: true,
  editorAutosaveInterval: 60,
  // Defaults for File & Upload
  maxFileUploadSizeMB: 25,
  allowedFileUploadTypes: '.pdf,.doc,.docx,.xls,.xlsx,.jpg,.png',
  defaultDocumentVisibility: 'private',
});

// Options for new dropdowns
const dashboardLandingPageOptions = [
  { value: 'overview', label: 'Dashboard Overview' },
  { value: 'cases', label: 'Cases List' },
  { value: 'documents', label: 'Documents List' },
  // Add other relevant dashboard pages
];

const editorFontOptions = [
  { value: 'Arial', label: 'Arial' },
  { value: 'Verdana', label: 'Verdana' },
  { value: 'Times New Roman', label: 'Times New Roman' },
  { value: 'Courier New', label: 'Courier New' },
  // Add other common fonts
];

const documentVisibilityOptions = [
  { value: 'private', label: 'Private to Uploader' },
  { value: 'team', label: 'Shared with Team (if applicable)' },
  // Add other visibility options like 'Specific Users/Groups' if logic exists
];


onMounted(async () => {
  await fetchSettings();
});

const fetchSettings = async () => {
  const settingsToFetch = [
    'defaultCaseStatus', 'require2FAForNewUsers',
    'defaultDarkModeForNewUsers', 'defaultDashboardLandingPage', 'sessionTimeoutWarningMinutes',
    'defaultEditorFont', 'defaultEditorFontSize', 'enableEditorAutosave', 'editorAutosaveInterval',
    'maxFileUploadSizeMB', 'allowedFileUploadTypes', 'defaultDocumentVisibility'
  ];

  for (const key of settingsToFetch) {
    try {
      const settingValue = await get(`/settings/${key}`);
      if (settingValue !== undefined && settingValue !== null) {
        (form.value as any)[key] = settingValue;
      }
    } catch (error) {
      console.error(`Failed to fetch platform default behavior setting for ${key}:`, error);
      // Optionally show a toast for individual fetch errors or a general one
    }
  }
};

const updateSetting = async (key: string, value: any) => {
  try {
    // For boolean values from UiToggle, ensure they are true/false not undefined
    const payloadValue = typeof value === 'boolean' ? value : (value === undefined ? null : value) ;
    await patch(`/settings/${key}`, { value: payloadValue });
    showToast({
      title: 'Success',
      message: `Successfully updated ${key}.`, // Simplified message
      type: 'success'
    });
  } catch (error: any) {
    console.error(`Failed to update default behavior setting ${key}:`, error);
    showToast({
      title: 'Error',
      message: `Failed to update ${key}. Please try again.`,
      type: 'error'
    });
    // Optionally, re-fetch settings to revert optimistic update or show persistent error
    // await fetchSettings(); // Re-fetch to ensure UI consistency if update fails
  }
};
</script>