<template>
  <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
    <!-- Search Input -->
    <div class="flex flex-col lg:flex-row gap-4 mb-6">
      <div class="flex-1">
        <div class="relative">
          <Icon 
            name="heroicons:magnifying-glass"
            class="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" 
          />
          <UiInput 
            id="global-search"
            name="global-search"
            v-model="searchQuery"
            placeholder="Search across cases, documents, users, clients, and templates..."
            class="pl-10 w-full text-lg"
            @keydown.enter="handleSearch"
          />
          <button
            v-if="searchQuery"
            @click="clearSearch"
            class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <Icon name="heroicons:x-mark" class="h-5 w-5" />
          </button>
        </div>
      </div>
      
      <div class="flex items-center gap-2">
        <UiButton
          variant="primary"
          :loading="isSearching"
          @click="handleSearch"
        >
          <Icon name="heroicons:magnifying-glass" class="h-4 w-4 mr-2" />
          Search
        </UiButton>
        
        <UiButton
          v-if="hasActiveFilters"
          variant="outline"
          @click="clearAllFilters"
        >
          <Icon name="heroicons:x-mark" class="h-4 w-4 mr-2" />
          Clear
        </UiButton>
      </div>
    </div>

    <!-- Entity Type Filters -->
    <div class="mb-6">
      <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Search In</h3>
      <div class="flex flex-wrap gap-2">
        <button
          v-for="type in entityTypes"
          :key="type.key"
          :class="[
            'inline-flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200',
            selectedTypes.includes(type.key)
              ? 'bg-brandPrimary text-white shadow-sm'
              : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
          ]"
          @click="toggleEntityType(type.key)"
        >
          <Icon :name="type.icon" class="h-4 w-4 mr-2" />
          {{ type.label }}
          <span
            v-if="totalByType && totalByType[type.key] > 0"
            :class="[
              'ml-2 px-2 py-0.5 rounded-full text-xs',
              selectedTypes.includes(type.key)
                ? 'bg-white/20 text-white'
                : 'bg-gray-200 dark:bg-gray-600 text-gray-600 dark:text-gray-400'
            ]"
          >
            {{ totalByType[type.key].toLocaleString() }}
          </span>
        </button>
      </div>
    </div>

    <!-- Advanced Filters Toggle -->
    <div class="flex items-center justify-between mb-4">
      <button
        @click="showAdvancedFilters = !showAdvancedFilters"
        class="flex items-center gap-2 text-sm font-medium text-gray-700 dark:text-gray-300 hover:text-brandPrimary transition-colors"
      >
        <Icon 
          :name="showAdvancedFilters ? 'heroicons:chevron-up' : 'heroicons:chevron-down'" 
          class="h-4 w-4" 
        />
        Advanced Filters
      </button>
      
      <div v-if="activeFiltersCount > 0" class="text-sm text-gray-500 dark:text-gray-400">
        {{ activeFiltersCount }} {{ activeFiltersCount === 1 ? 'filter' : 'filters' }} active
      </div>
    </div>

    <!-- Advanced Filters Panel -->
    <Transition
      enter-active-class="transition-all duration-300 ease-out"
      enter-from-class="opacity-0 max-h-0"
      enter-to-class="opacity-100 max-h-96"
      leave-active-class="transition-all duration-300 ease-in"
      leave-from-class="opacity-100 max-h-96"
      leave-to-class="opacity-0 max-h-0"
    >
      <div v-if="showAdvancedFilters" class="border-t border-gray-200 dark:border-gray-700 pt-6 overflow-hidden">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <!-- Date Range Filter -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Date Range
            </label>
            <div class="grid grid-cols-2 gap-2">
              <UiInput
                id="date-from"
                name="date-from"
                type="date"
                v-model="filters.dateFrom"
                placeholder="From"
                @input="handleFilterChange('dateFrom', $event.target.value)"
              />
              <UiInput
                id="date-to"
                name="date-to"
                type="date"
                v-model="filters.dateTo"
                placeholder="To"
                @input="handleFilterChange('dateTo', $event.target.value)"
              />
            </div>
          </div>

          <!-- Status Filter -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Status
            </label>
            <select
              v-model="filters.status"
              @change="handleFilterChange('status', $event.target.value)"
              class="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-brandPrimary focus:border-brandPrimary"
            >
              <option value="">All Statuses</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
              <option value="pending">Pending</option>
              <option value="closed">Closed</option>
              <option value="draft">Draft</option>
            </select>
          </div>

          <!-- Priority Filter (for cases) -->
          <div v-if="selectedTypes.includes('cases') || selectedTypes.includes('all')">
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Priority
            </label>
            <select
              v-model="filters.priority"
              @change="handleFilterChange('priority', $event.target.value)"
              class="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-brandPrimary focus:border-brandPrimary"
            >
              <option value="">All Priorities</option>
              <option value="low">Low</option>
              <option value="medium">Medium</option>
              <option value="high">High</option>
              <option value="urgent">Urgent</option>
            </select>
          </div>

          <!-- File Type Filter (for documents) -->
          <div v-if="selectedTypes.includes('documents') || selectedTypes.includes('all')">
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              File Type
            </label>
            <select
              v-model="filters.fileType"
              @change="handleFilterChange('fileType', $event.target.value)"
              class="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-brandPrimary focus:border-brandPrimary"
            >
              <option value="">All File Types</option>
              <option value="pdf">PDF</option>
              <option value="doc">Word Document</option>
              <option value="xls">Excel</option>
              <option value="ppt">PowerPoint</option>
              <option value="txt">Text</option>
              <option value="img">Image</option>
            </select>
          </div>

          <!-- Role Filter (for users) -->
          <div v-if="selectedTypes.includes('users') || selectedTypes.includes('all')">
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Role
            </label>
            <select
              v-model="filters.role"
              @change="handleFilterChange('role', $event.target.value)"
              class="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-brandPrimary focus:border-brandPrimary"
            >
              <option value="">All Roles</option>
              <option value="admin">Admin</option>
              <option value="lawyer">Lawyer</option>
              <option value="paralegal">Paralegal</option>
              <option value="client">Client</option>
            </select>
          </div>

          <!-- Sort Options -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Sort By
            </label>
            <select
              v-model="sortBy"
              @change="handleSortChange($event.target.value)"
              class="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-brandPrimary focus:border-brandPrimary"
            >
              <option value="relevance">Relevance</option>
              <option value="date_desc">Newest First</option>
              <option value="date_asc">Oldest First</option>
              <option value="title_asc">Title A-Z</option>
              <option value="title_desc">Title Z-A</option>
            </select>
          </div>
        </div>
      </div>
    </Transition>

    <!-- Search Suggestions -->
    <div v-if="suggestions.length > 0" class="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
      <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Suggestions</h4>
      <div class="flex flex-wrap gap-2">
        <button
          v-for="suggestion in suggestions.slice(0, 5)"
          :key="suggestion"
          @click="applySuggestion(suggestion)"
          class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
        >
          <Icon name="heroicons:light-bulb" class="h-3 w-3 mr-1" />
          {{ suggestion }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import type { SearchEntityType } from '~/app/shared/types/search'

interface Props {
  modelValue: string
  selectedTypes: SearchEntityType[]
  filters: Record<string, any>
  sortBy: string
  totalByType?: Record<SearchEntityType, number>
  suggestions?: string[]
  isSearching?: boolean
}

interface Emits {
  (e: 'update:modelValue', value: string): void
  (e: 'search', query: string): void
  (e: 'type-change', types: SearchEntityType[]): void
  (e: 'filter-change', key: string, value: any): void
  (e: 'sort-change', sortBy: string): void
  (e: 'clear-filters'): void
}

const props = withDefaults(defineProps<Props>(), {
  suggestions: () => [],
  isSearching: false
})

const emit = defineEmits<Emits>()

// Local state
const searchQuery = ref(props.modelValue)
const showAdvancedFilters = ref(false)

// Entity types configuration
const entityTypes = [
  { key: 'all' as SearchEntityType, label: 'All', icon: 'heroicons:magnifying-glass' },
  { key: 'cases' as SearchEntityType, label: 'Cases', icon: 'heroicons:briefcase' },
  { key: 'documents' as SearchEntityType, label: 'Documents', icon: 'heroicons:document-text' },
  { key: 'users' as SearchEntityType, label: 'Users', icon: 'heroicons:users' },
  { key: 'clients' as SearchEntityType, label: 'Clients', icon: 'heroicons:user-group' },
  { key: 'templates' as SearchEntityType, label: 'Templates', icon: 'heroicons:document-duplicate' }
]

// Computed properties
const hasActiveFilters = computed(() => {
  return searchQuery.value.trim() !== '' || 
         Object.values(props.filters).some(value => value !== '' && value !== null && value !== undefined)
})

const activeFiltersCount = computed(() => {
  return Object.values(props.filters).filter(value => value !== '' && value !== null && value !== undefined).length
})

// Watch for external changes
watch(() => props.modelValue, (newValue) => {
  searchQuery.value = newValue
})

watch(searchQuery, (newValue) => {
  emit('update:modelValue', newValue)
})

// Methods
function handleSearch() {
  emit('search', searchQuery.value)
}

function clearSearch() {
  searchQuery.value = ''
  emit('update:modelValue', '')
}

function toggleEntityType(type: SearchEntityType) {
  const types = [...props.selectedTypes]
  const index = types.indexOf(type)
  
  if (type === 'all') {
    emit('type-change', ['all'])
  } else {
    if (index > -1) {
      types.splice(index, 1)
    } else {
      types.push(type)
    }
    
    // Remove 'all' if specific types are selected
    const allIndex = types.indexOf('all')
    if (allIndex > -1 && types.length > 1) {
      types.splice(allIndex, 1)
    }
    
    // Add 'all' if no types are selected
    if (types.length === 0) {
      types.push('all')
    }
    
    emit('type-change', types)
  }
}

function handleFilterChange(key: string, value: any) {
  emit('filter-change', key, value)
}

function handleSortChange(sortBy: string) {
  emit('sort-change', sortBy)
}

function clearAllFilters() {
  searchQuery.value = ''
  emit('update:modelValue', '')
  emit('clear-filters')
}

function applySuggestion(suggestion: string) {
  searchQuery.value = suggestion
  emit('update:modelValue', suggestion)
  handleSearch()
}
</script>
