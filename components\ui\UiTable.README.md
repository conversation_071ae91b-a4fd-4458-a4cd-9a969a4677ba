# Enhanced UiTable Component

The `UiTable.vue` component is a comprehensive, feature-rich table component that provides advanced functionality for displaying and interacting with tabular data.

## Features

### ✅ **Core Features**
- **Responsive Design**: Mobile-first approach with horizontal scrolling
- **Dark Mode Support**: Full dark mode compatibility
- **TypeScript Support**: Fully typed with generic support
- **Customizable Styling**: Extensive styling options and slots

### ✅ **Data Management**
- **Sorting**: Multi-column sorting with visual indicators
- **Filtering**: Column-level filtering with custom filter components
- **Pagination**: Built-in pagination with customizable controls
- **Loading States**: Elegant loading indicators and empty states
- **Data Formatting**: Custom formatters for cell values

### ✅ **Selection & Interaction**
- **Row Selection**: Single and multi-row selection with checkboxes
- **Bulk Actions**: Custom bulk action buttons for selected rows
- **Row Expansion**: Expandable rows with custom content
- **Row Hover**: Smooth hover effects and transitions
- **Click Handlers**: Row click and action events

### ✅ **Customization**
- **Column Customization**: Show/hide columns with drag-and-drop reordering
- **Flexible Headers**: Custom header content and styling
- **Custom Cells**: Slot-based cell customization
- **Empty States**: Customizable empty state with actions
- **Themes**: Striped rows, borders, dense layout options

### ✅ **Advanced Features**
- **Sticky Columns**: Pin important columns to the left
- **Column Filters**: Individual column filtering
- **Keyboard Navigation**: Accessibility-focused keyboard support
- **Export Methods**: Exposed methods for programmatic control
- **Performance**: Optimized for large datasets

## Basic Usage

```vue
<template>
  <UiTable
    :headers="headers"
    :items="data"
    :loading="loading"
    item-key="id"
    @sort="handleSort"
  />
</template>

<script setup>
const headers = [
  { key: 'name', label: 'Name', sortable: true },
  { key: 'email', label: 'Email', sortable: true },
  { key: 'status', label: 'Status' }
]

const data = [
  { id: 1, name: 'John Doe', email: '<EMAIL>', status: 'active' },
  { id: 2, name: 'Jane Smith', email: '<EMAIL>', status: 'inactive' }
]
</script>
```

## Advanced Usage

```vue
<template>
  <UiTable
    :headers="headers"
    :items="data"
    :loading="loading"
    :selectable="true"
    :expandable="true"
    :striped="true"
    :column-customization="true"
    :pagination="pagination"
    item-key="id"
    @sort="handleSort"
    @selection-change="handleSelection"
    @bulk-action="handleBulkAction"
  >
    <!-- Custom header actions -->
    <template #header-actions>
      <UiButton @click="addNew" variant="primary">Add New</UiButton>
    </template>

    <!-- Custom bulk actions -->
    <template #bulk-actions="{ selectedItems }">
      <UiButton @click="deleteSelected(selectedItems)">Delete</UiButton>
    </template>

    <!-- Custom cell content -->
    <template #item.name="{ item, value }">
      <div class="flex items-center gap-2">
        <img :src="item.avatar" class="w-8 h-8 rounded-full" />
        <span class="font-medium">{{ value }}</span>
      </div>
    </template>

    <!-- Expanded row content -->
    <template #expanded-row="{ item }">
      <div class="p-4">
        <h4>Details for {{ item.name }}</h4>
        <p>Additional information...</p>
      </div>
    </template>
  </UiTable>
</template>
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `headers` | `TableHeader[]` | `[]` | Table column definitions |
| `items` | `T[]` | `[]` | Table data items |
| `loading` | `boolean` | `false` | Loading state |
| `itemKey` | `string` | - | Unique key for items |
| `title` | `string` | - | Table title |
| `description` | `string` | - | Table description |
| `selectable` | `boolean` | `false` | Enable row selection |
| `expandable` | `boolean` | `false` | Enable row expansion |
| `striped` | `boolean` | `false` | Alternating row colors |
| `columnCustomization` | `boolean` | `false` | Enable column customization |
| `pagination` | `PaginationState` | - | Pagination configuration |
| `dense` | `boolean` | `false` | Compact layout |
| `bordered` | `boolean` | `false` | Add table borders |

## Header Configuration

```typescript
interface TableHeader {
  key: string;                    // Column key
  label: string;                  // Display label
  sortable?: boolean;             // Enable sorting
  filterable?: boolean;           // Enable filtering
  class?: string;                 // Header CSS classes
  cellClass?: string;             // Cell CSS classes
  format?: (value, item, index) => string; // Value formatter
  wrap?: boolean;                 // Text wrapping
  width?: string;                 // Column width
  align?: 'left' | 'center' | 'right'; // Text alignment
  sticky?: boolean;               // Sticky column
}
```

## Events

| Event | Payload | Description |
|-------|---------|-------------|
| `sort` | `SortState` | Column sort changed |
| `selection-change` | `string[]` | Selected items changed |
| `bulk-action` | `{action, items}` | Bulk action triggered |
| `row-click` | `{item, index}` | Row clicked |
| `row-expand` | `{item, index, expanded}` | Row expansion changed |
| `page-change` | `number` | Page changed |
| `column-filter` | `{column, value}` | Column filter applied |

## Slots

| Slot | Props | Description |
|------|-------|-------------|
| `header-title` | - | Custom header title |
| `header-description` | - | Custom header description |
| `header-search-filter-area` | - | Search and filter controls |
| `header-actions` | - | Header action buttons |
| `bulk-actions` | `{selectedItems, clearSelection}` | Custom bulk actions |
| `item.{key}` | `{item, value, index, header}` | Custom cell content |
| `expanded-row` | `{item, index}` | Expanded row content |
| `empty-state-icon` | - | Custom empty state icon |
| `empty-state-action` | - | Empty state action button |
| `footer` | - | Custom footer content |
| `column-filter` | `{column, closeFilter}` | Custom column filter |

## Exposed Methods

```typescript
// Selection methods
clearSelection(): void
selectAll(): void
getSelectedItems(): string[]

// Expansion methods
expandAll(): void
collapseAll(): void
getExpandedRows(): string[]
```

## Styling Classes

The component supports various styling options:

- `table-dense`: Compact row height
- `table-bordered`: Add borders
- `sticky-column`: Sticky column positioning

## Accessibility

- Full keyboard navigation support
- ARIA labels and roles
- Screen reader friendly
- Focus management
- High contrast support

## Performance

- Virtual scrolling for large datasets (planned)
- Optimized re-rendering
- Lazy loading support
- Memory efficient selection tracking

## Examples

See `pages/dashboard/enhanced-table-demo.vue` for a comprehensive example showcasing all features.
