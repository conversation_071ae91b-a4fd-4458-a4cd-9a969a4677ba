<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <!-- Header Section -->
    <div class="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="py-6">
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
              <div class="flex items-center justify-center w-12 h-12 bg-green-100 dark:bg-green-900 rounded-xl">
                <Icon name="heroicons:currency-dollar" class="w-6 h-6 text-green-600 dark:text-green-400" />
              </div>
              <div>
                <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Create Invoice Template</h1>
                <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                  Design professional invoice templates for billing and payment processing
                </p>
              </div>
            </div>
            
            <div class="flex items-center space-x-3">
              <UiButton @click="previewTemplate" variant="outline" :disabled="!canPreview">
                <Icon name="heroicons:eye" class="w-4 h-4 mr-2" />
                Preview
              </UiButton>
              <UiButton @click="saveDraft" variant="outline" :disabled="isSaving || !formData.name.trim()">
                <Icon name="heroicons:document-duplicate" class="w-4 h-4 mr-2" />
                Save Draft
              </UiButton>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
        <!-- Main Form -->
        <div class="lg:col-span-3 space-y-6">
          <form @submit.prevent="handleSubmit">
            <!-- Template Information Card -->
            <UiCard class="p-6">
              <div class="flex items-center space-x-3 mb-6">
                <div class="w-8 h-8 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center">
                  <Icon name="heroicons:information-circle" class="w-5 h-5 text-green-600 dark:text-green-400" />
                </div>
                <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Template Information</h2>
              </div>

              <div class="space-y-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div class="space-y-2">
                    <UiInput
                      id="templateName"
                      v-model="formData.name"
                      label="Template Name"
                      placeholder="e.g., Standard Legal Invoice"
                      required
                      :error="errors.name"
                    />
                    <p class="text-xs text-gray-500 dark:text-gray-400">
                      Choose a descriptive name for easy identification
                    </p>
                  </div>

                  <div class="space-y-2">
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      Invoice Category
                    </label>
                    <div class="relative">
                      <select
                        v-model="formData.category"
                        class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                        required
                      >
                        <option value="">Select a category</option>
                        <option
                          v-for="category in invoiceCategories"
                          :key="category.id"
                          :value="category.id"
                        >
                          {{ category.name }}
                        </option>
                      </select>
                      <Icon name="heroicons:chevron-down" class="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" />
                    </div>
                  </div>
                </div>

                <div class="space-y-2">
                  <UiTextarea
                    id="templateDescription"
                    v-model="formData.description"
                    label="Description"
                    placeholder="Describe the purpose and usage of this invoice template..."
                    :rows="3"
                  />
                </div>

                <!-- Category Details -->
                <div v-if="selectedCategory" class="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
                  <div class="flex items-start space-x-3">
                    <Icon :name="selectedCategory.icon" class="w-5 h-5 text-green-600 dark:text-green-400 mt-0.5" />
                    <div>
                      <h3 class="text-sm font-medium text-green-900 dark:text-green-100">{{ selectedCategory.name }}</h3>
                      <p class="text-sm text-green-700 dark:text-green-300 mt-1">{{ selectedCategory.description }}</p>
                      <div v-if="selectedCategory.metadata" class="flex flex-wrap gap-2 mt-2">
                        <span
                          v-if="selectedCategory.metadata.taxCalculation"
                          class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
                        >
                          <Icon name="heroicons:calculator" class="w-3 h-3 mr-1" />
                          Tax Calculation
                        </span>
                        <span
                          v-if="selectedCategory.metadata.paymentTerms"
                          class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200"
                        >
                          <Icon name="heroicons:calendar" class="w-3 h-3 mr-1" />
                          Payment Terms
                        </span>
                        <span
                          v-if="selectedCategory.metadata.multiCurrency"
                          class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-200"
                        >
                          <Icon name="heroicons:banknotes" class="w-3 h-3 mr-1" />
                          Multi-Currency
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </UiCard>

            <!-- Invoice Design -->
            <UiCard class="p-6">
              <div class="flex items-center justify-between mb-6">
                <div class="flex items-center space-x-3">
                  <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                    <Icon name="heroicons:document-text" class="w-5 h-5 text-blue-600 dark:text-blue-400" />
                  </div>
                  <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Invoice Design</h2>
                </div>
                
                <div class="flex items-center space-x-2">
                  <UiButton @click="insertVariable" variant="outline" size="sm">
                    <Icon name="heroicons:plus" class="w-4 h-4 mr-1" />
                    Variable
                  </UiButton>
                  <UiButton @click="showTemplateLibrary = true" variant="outline" size="sm">
                    <Icon name="heroicons:book-open" class="w-4 h-4 mr-1" />
                    Library
                  </UiButton>
                </div>
              </div>

              <InvoiceTemplateEditor
                v-model:content="formData.content"
                v-model:variables="formData.variables"
                v-model:settings="formData.settings"
                :template-data="formData"
                :category="selectedCategory"
                @content-change="handleContentChange"
              />
            </UiCard>

            <!-- Form Actions -->
            <div class="flex items-center justify-between">
              <UiButton @click="$router.back()" variant="outline" type="button">
                <Icon name="heroicons:arrow-left" class="w-4 h-4 mr-2" />
                Cancel
              </UiButton>
              
              <div class="flex items-center space-x-3">
                <UiButton @click="saveDraft" variant="outline" type="button" :disabled="isSaving || !formData.name.trim()">
                  <Icon name="heroicons:document-duplicate" class="w-4 h-4 mr-2" />
                  Save as Draft
                </UiButton>
                <UiButton type="submit" :disabled="isSaving || !isFormValid" class="bg-green-600 hover:bg-green-700">
                  <Icon name="heroicons:check" class="w-4 h-4 mr-2" />
                  Create Template
                </UiButton>
              </div>
            </div>
          </form>
        </div>

        <!-- Sidebar -->
        <div class="lg:col-span-1 space-y-6">
          <!-- Template Stats -->
          <UiCard class="p-4">
            <h3 class="text-sm font-medium text-gray-900 dark:text-white mb-3">Template Stats</h3>
            <div class="space-y-3">
              <div class="flex items-center justify-between text-sm">
                <span class="text-gray-600 dark:text-gray-400">Variables</span>
                <span class="font-medium text-gray-900 dark:text-white">{{ formData.variables.length }}</span>
              </div>
              <div class="flex items-center justify-between text-sm">
                <span class="text-gray-600 dark:text-gray-400">Line Items</span>
                <span class="font-medium text-gray-900 dark:text-white">{{ formData.settings.lineItems?.length || 0 }}</span>
              </div>
              <div class="flex items-center justify-between text-sm">
                <span class="text-gray-600 dark:text-gray-400">Completion</span>
                <span class="font-medium text-gray-900 dark:text-white">{{ completionPercentage }}%</span>
              </div>
            </div>
          </UiCard>

          <!-- Variables Panel -->
          <UiCard class="p-4" v-if="formData.variables.length > 0">
            <h3 class="text-sm font-medium text-gray-900 dark:text-white mb-3">Template Variables</h3>
            <div class="space-y-2">
              <div
                v-for="variable in formData.variables"
                :key="variable"
                class="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-700 rounded-lg"
              >
                <span class="text-sm font-mono text-gray-700 dark:text-gray-300">{{ variable }}</span>
                <UiButton @click="removeVariable(variable)" variant="ghost" size="sm">
                  <Icon name="heroicons:x-mark" class="w-3 h-3" />
                </UiButton>
              </div>
            </div>
          </UiCard>

          <!-- Invoice Features -->
          <UiCard class="p-4">
            <h3 class="text-sm font-medium text-gray-900 dark:text-white mb-3">Invoice Features</h3>
            <div class="space-y-3">
              <label class="flex items-center space-x-2">
                <input
                  type="checkbox"
                  v-model="formData.settings.enableTax"
                  class="rounded border-gray-300 text-green-600 focus:ring-green-500"
                />
                <span class="text-sm text-gray-700 dark:text-gray-300">Tax calculation</span>
              </label>
              
              <label class="flex items-center space-x-2">
                <input
                  type="checkbox"
                  v-model="formData.settings.enableDiscount"
                  class="rounded border-gray-300 text-green-600 focus:ring-green-500"
                />
                <span class="text-sm text-gray-700 dark:text-gray-300">Discount support</span>
              </label>
              
              <label class="flex items-center space-x-2">
                <input
                  type="checkbox"
                  v-model="formData.settings.enableMultiCurrency"
                  class="rounded border-gray-300 text-green-600 focus:ring-green-500"
                />
                <span class="text-sm text-gray-700 dark:text-gray-300">Multi-currency</span>
              </label>
              
              <label class="flex items-center space-x-2">
                <input
                  type="checkbox"
                  v-model="formData.settings.enablePaymentTerms"
                  class="rounded border-gray-300 text-green-600 focus:ring-green-500"
                />
                <span class="text-sm text-gray-700 dark:text-gray-300">Payment terms</span>
              </label>
            </div>
          </UiCard>

          <!-- Tips & Guidelines -->
          <UiCard class="p-4">
            <h3 class="text-sm font-medium text-gray-900 dark:text-white mb-3">Tips & Guidelines</h3>
            <div class="space-y-3 text-sm text-gray-600 dark:text-gray-400">
              <div class="flex items-start space-x-2">
                <Icon name="heroicons:light-bulb" class="w-4 h-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                <p>Include all necessary legal and tax information</p>
              </div>
              <div class="flex items-start space-x-2">
                <Icon name="heroicons:currency-dollar" class="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                <p>Use clear pricing and payment terms</p>
              </div>
              <div class="flex items-start space-x-2">
                <Icon name="heroicons:document-check" class="w-4 h-4 text-blue-500 mt-0.5 flex-shrink-0" />
                <p>Test template with sample data before publishing</p>
              </div>
            </div>
          </UiCard>
        </div>
      </div>
    </div>

    <!-- Template Library Modal -->
    <InvoiceTemplateLibraryModal
      v-if="showTemplateLibrary"
      @close="showTemplateLibrary = false"
      @select="insertTemplate"
    />

    <!-- Variable Insert Modal -->
    <VariableInsertModal
      v-if="showVariableModal"
      @close="showVariableModal = false"
      @insert="handleVariableInsert"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive, watch, defineAsyncComponent } from 'vue'
import { useRouter } from 'vue-router'
import { useTemplateStore } from '~/stores/template'
import { getTemplateCategoriesByType, getTemplateCategoryById } from '~/utils/templateCategories'
import { PlatformRoles, TenantRoles } from '~/app/features/auth/constants/roles'

// Lazy load components
const InvoiceTemplateEditor = defineAsyncComponent(() => import('~/components/template-editors/InvoiceTemplateEditor.vue'))
const InvoiceTemplateLibraryModal = defineAsyncComponent(() => import('~/components/template-modals/InvoiceTemplateLibraryModal.vue'))
const VariableInsertModal = defineAsyncComponent(() => import('~/components/template-modals/VariableInsertModal.vue'))

// Page meta
definePageMeta({
  layout: 'dashboard',
  title: 'Create Invoice Template',
  description: 'Create professional invoice templates for billing and payment processing',
  pageHeaderIcon: 'heroicons:currency-dollar',
  middleware: ['rbac'],
  roles: [PlatformRoles.SUPER_ADMIN, TenantRoles.TENANT_OWNER, TenantRoles.ADMIN, TenantRoles.LAWYER],
  breadcrumb: [
    { label: 'Dashboard', href: '/dashboard' },
    { label: 'Templates', href: '/dashboard/templates' },
    { label: 'Invoice Templates', href: '/dashboard/templates/invoice' },
    { label: 'Create' },
  ],
})

// Composables
const router = useRouter()
const templateStore = useTemplateStore()

// State
const isSaving = ref(false)
const showTemplateLibrary = ref(false)
const showVariableModal = ref(false)
const errors = ref<Record<string, string>>({})

const formData = reactive({
  name: '',
  description: '',
  category: '',
  content: '',
  variables: [] as string[],
  type: 'invoice',
  isDraft: false,
  settings: {
    enableTax: true,
    enableDiscount: true,
    enableMultiCurrency: false,
    enablePaymentTerms: true,
    lineItems: [],
    currency: 'USD',
    taxRate: 0,
    paymentTerms: '30',
    logoUrl: '',
    companyInfo: {},
  },
  metadata: {},
})

// Computed
const invoiceCategories = computed(() => getTemplateCategoriesByType('invoice'))

const selectedCategory = computed(() =>
  formData.category ? getTemplateCategoryById(formData.category) : null
)

const completionPercentage = computed(() => {
  let score = 0
  if (formData.name.trim()) score += 25
  if (formData.category) score += 25
  if (formData.description.trim()) score += 15
  if (formData.content.trim()) score += 35
  return score
})

const isFormValid = computed(() =>
  formData.name.trim() &&
  formData.category &&
  formData.content.trim() &&
  !Object.keys(errors.value).length
)

const canPreview = computed(() =>
  formData.content.trim() && formData.name.trim()
)

// Watchers
watch(() => formData.name, (newName) => {
  if (newName.trim() && errors.value.name) {
    delete errors.value.name
  }
})

watch(() => formData.category, (newCategory) => {
  if (newCategory && selectedCategory.value?.metadata) {
    formData.metadata = { ...selectedCategory.value.metadata }

    // Apply category-specific settings
    if (selectedCategory.value.metadata.taxCalculation) {
      formData.settings.enableTax = true
    }
    if (selectedCategory.value.metadata.paymentTerms) {
      formData.settings.enablePaymentTerms = true
    }
    if (selectedCategory.value.metadata.multiCurrency) {
      formData.settings.enableMultiCurrency = true
    }
  }
})

// Methods
const validateForm = () => {
  errors.value = {}

  if (!formData.name.trim()) {
    errors.value.name = 'Template name is required'
  } else if (formData.name.length < 3) {
    errors.value.name = 'Template name must be at least 3 characters'
  }

  if (!formData.category) {
    errors.value.category = 'Please select a category'
  }

  if (!formData.content.trim()) {
    errors.value.content = 'Template content is required'
  }

  return Object.keys(errors.value).length === 0
}

const handleSubmit = async () => {
  if (!validateForm()) return

  isSaving.value = true
  try {
    const template = await templateStore.createTemplate({
      ...formData,
      isDraft: false,
    })

    if (template) {
      await router.push(`/dashboard/templates/invoice/${template.id}`)
    }
  } catch (error) {
    console.error('Error creating template:', error)
  } finally {
    isSaving.value = false
  }
}

const saveDraft = async () => {
  if (!formData.name.trim()) {
    errors.value.name = 'Template name is required to save draft'
    return
  }

  isSaving.value = true
  try {
    const template = await templateStore.createTemplate({
      ...formData,
      isDraft: true,
    })

    if (template) {
      await router.push(`/dashboard/templates/invoice/${template.id}/edit`)
    }
  } catch (error) {
    console.error('Error saving draft:', error)
  } finally {
    isSaving.value = false
  }
}

const previewTemplate = () => {
  if (!canPreview.value) return

  const previewData = {
    name: formData.name,
    content: formData.content,
    variables: formData.variables,
    settings: formData.settings,
    category: selectedCategory.value
  }

  sessionStorage.setItem('invoiceTemplatePreview', JSON.stringify(previewData))
  window.open('/dashboard/templates/invoice/preview', '_blank')
}

const insertVariable = () => {
  showVariableModal.value = true
}

const handleVariableInsert = (variable: string) => {
  if (!formData.variables.includes(variable)) {
    formData.variables.push(variable)
  }
  showVariableModal.value = false
}

const removeVariable = (variable: string) => {
  const index = formData.variables.indexOf(variable)
  if (index > -1) {
    formData.variables.splice(index, 1)
  }
}

const insertTemplate = (template: any) => {
  formData.content = template.content
  formData.variables = [...formData.variables, ...template.variables]
  formData.settings = { ...formData.settings, ...template.settings }
  showTemplateLibrary.value = false
}

const handleContentChange = (content: string, variables: string[], settings: any) => {
  formData.content = content
  formData.variables = variables
  formData.settings = { ...formData.settings, ...settings }
}
</script>
