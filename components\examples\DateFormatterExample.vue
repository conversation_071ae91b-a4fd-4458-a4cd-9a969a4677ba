<template>
  <div class="p-6 bg-white dark:bg-gray-800 rounded-lg shadow-lg">
    <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-6">
      Date Formatter Examples
    </h2>
    
    <!-- Sample Date Input -->
    <div class="mb-6">
      <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
        Sample Date
      </label>
      <input
        v-model="sampleDate"
        type="datetime-local"
        class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
      />
    </div>
    
    <!-- Formatting Examples -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <!-- Basic Formats -->
      <div class="space-y-4">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
          Basic Formats
        </h3>
        
        <div class="space-y-2">
          <div class="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-700 rounded">
            <span class="text-sm font-medium text-gray-600 dark:text-gray-300">Date Display:</span>
            <span class="text-sm text-gray-900 dark:text-white">{{ $formatDateDisplay(sampleDate) }}</span>
          </div>
          
          <div class="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-700 rounded">
            <span class="text-sm font-medium text-gray-600 dark:text-gray-300">Date Full:</span>
            <span class="text-sm text-gray-900 dark:text-white">{{ $formatDateFull(sampleDate) }}</span>
          </div>
          
          <div class="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-700 rounded">
            <span class="text-sm font-medium text-gray-600 dark:text-gray-300">Date Time:</span>
            <span class="text-sm text-gray-900 dark:text-white">{{ $formatDateTime(sampleDate) }}</span>
          </div>
          
          <div class="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-700 rounded">
            <span class="text-sm font-medium text-gray-600 dark:text-gray-300">Time Only:</span>
            <span class="text-sm text-gray-900 dark:text-white">{{ $formatTime(sampleDate) }}</span>
          </div>
        </div>
      </div>
      
      <!-- Relative Formats -->
      <div class="space-y-4">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
          Relative Formats
        </h3>
        
        <div class="space-y-2">
          <div class="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-700 rounded">
            <span class="text-sm font-medium text-gray-600 dark:text-gray-300">Relative Time:</span>
            <span class="text-sm text-gray-900 dark:text-white">{{ $formatRelativeTime(sampleDate) }}</span>
          </div>
          
          <div class="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-700 rounded">
            <span class="text-sm font-medium text-gray-600 dark:text-gray-300">Duration:</span>
            <span class="text-sm text-gray-900 dark:text-white">{{ $formatDuration(sampleDate, new Date()) }}</span>
          </div>
          
          <div class="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-700 rounded">
            <span class="text-sm font-medium text-gray-600 dark:text-gray-300">Age (if birthdate):</span>
            <span class="text-sm text-gray-900 dark:text-white">{{ $formatAge(birthDate) }}</span>
          </div>
        </div>
      </div>
      
      <!-- Legal Formats -->
      <div class="space-y-4">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
          Legal Document Formats
        </h3>
        
        <div class="space-y-2">
          <div class="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-700 rounded">
            <span class="text-sm font-medium text-gray-600 dark:text-gray-300">Legal Date:</span>
            <span class="text-sm text-gray-900 dark:text-white">{{ $formatLegalDate(sampleDate) }}</span>
          </div>
          
          <div class="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-700 rounded">
            <span class="text-sm font-medium text-gray-600 dark:text-gray-300">Legal DateTime:</span>
            <span class="text-sm text-gray-900 dark:text-white">{{ $formatLegalDateTime(sampleDate) }}</span>
          </div>
        </div>
      </div>
      
      <!-- System Formats -->
      <div class="space-y-4">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
          System Formats
        </h3>
        
        <div class="space-y-2">
          <div class="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-700 rounded">
            <span class="text-sm font-medium text-gray-600 dark:text-gray-300">File Timestamp:</span>
            <span class="text-sm text-gray-900 dark:text-white font-mono">{{ $formatFileTimestamp(sampleDate) }}</span>
          </div>
          
          <div class="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-700 rounded">
            <span class="text-sm font-medium text-gray-600 dark:text-gray-300">Log Timestamp:</span>
            <span class="text-sm text-gray-900 dark:text-white font-mono">{{ $formatLogTimestamp(sampleDate) }}</span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Custom Format Example -->
    <div class="mt-6">
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
        Custom Format
      </h3>
      
      <div class="flex gap-4 items-end">
        <div class="flex-1">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Custom Format Pattern
          </label>
          <input
            v-model="customFormat"
            type="text"
            placeholder="e.g., YYYY-MM-DD HH:mm:ss"
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
          />
        </div>
        
        <div class="flex-1">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Result
          </label>
          <div class="px-3 py-2 bg-gray-50 dark:bg-gray-700 rounded-md text-sm text-gray-900 dark:text-white">
            {{ $formatDate(sampleDate, customFormat) }}
          </div>
        </div>
      </div>
    </div>
    
    <!-- Timezone Example -->
    <div class="mt-6">
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
        Timezone Examples
      </h3>
      
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div class="p-3 bg-gray-50 dark:bg-gray-700 rounded">
          <div class="text-sm font-medium text-gray-600 dark:text-gray-300 mb-1">New York</div>
          <div class="text-sm text-gray-900 dark:text-white">
            {{ $formatDateWithTimezone(sampleDate, 'MMM D, YYYY h:mm A', 'America/New_York') }}
          </div>
        </div>
        
        <div class="p-3 bg-gray-50 dark:bg-gray-700 rounded">
          <div class="text-sm font-medium text-gray-600 dark:text-gray-300 mb-1">London</div>
          <div class="text-sm text-gray-900 dark:text-white">
            {{ $formatDateWithTimezone(sampleDate, 'MMM D, YYYY h:mm A', 'Europe/London') }}
          </div>
        </div>
        
        <div class="p-3 bg-gray-50 dark:bg-gray-700 rounded">
          <div class="text-sm font-medium text-gray-600 dark:text-gray-300 mb-1">Tokyo</div>
          <div class="text-sm text-gray-900 dark:text-white">
            {{ $formatDateWithTimezone(sampleDate, 'MMM D, YYYY h:mm A', 'Asia/Tokyo') }}
          </div>
        </div>
      </div>
    </div>
    
    <!-- Composable Example -->
    <div class="mt-6">
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
        Reactive Composable Example
      </h3>
      
      <div class="p-4 bg-gray-50 dark:bg-gray-700 rounded">
        <div class="text-sm text-gray-600 dark:text-gray-300 mb-2">
          Reactive formatted date (updates automatically):
        </div>
        <div class="text-lg font-semibold text-gray-900 dark:text-white">
          {{ reactiveFormattedDate }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useDateFormatters } from '~/composables/useDateFormatters'
import { useDayjs } from '#dayjs'

// Component data
const sampleDate = ref(new Date().toISOString().slice(0, 16))
const customFormat = ref('YYYY-MM-DD HH:mm:ss')
const birthDate = ref('1990-01-01')

// Use the composable and direct Day.js access
const { formatReactive } = useDateFormatters()
const dayjs = useDayjs()

// Reactive formatted date
const reactiveFormattedDate = formatReactive(
  computed(() => sampleDate.value),
  'MMMM D, YYYY [at] h:mm:ss A'
)
</script>
