/**
 * Global Properties Plugin
 *
 * Provides simple global access to properties (functions, strings, numbers, booleans, etc.)
 * throughout the Nuxt app via the $global context.
 */

import { defineNuxtPlugin } from '#app'
import { useGlobalProperties } from '~/app/shared/composables/core/useGlobalActions'
import type { GlobalPropertyValue } from '~/app/shared/composables/core/useGlobalActions'

// ============================================================================
// PLUGIN IMPLEMENTATION
// ============================================================================

export default defineNuxtPlugin((nuxtApp) => {
  // Get the global properties composable
  const globalProps = useGlobalProperties()

  // Create a simple interface for the plugin
  const globalPlugin = {
    // Add a property
    add: (propertyName: string, value: GlobalPropertyValue) => {
      globalProps.add(propertyName, value)
    },

    // Remove a property
    remove: (propertyName: string) => {
      globalProps.remove(propertyName)
    },

    // Get a property
    get: (propertyName: string) => {
      return globalProps.get(propertyName)
    },

    // Check if property exists
    has: (propertyName: string) => {
      return globalProps.has(propertyName)
    },

    // Get all property names
    keys: () => {
      return globalProps.keys()
    },

    // Clear all properties
    clear: () => {
      globalProps.clear()
    },

    // Direct access to state
    state: globalProps.state
  }

  // ============================================================================
  // PROVIDE GLOBAL ACCESS
  // ============================================================================

  // Provide the plugin through Nuxt's provide system
  nuxtApp.provide('global', globalPlugin)

  // ============================================================================
  // DEVELOPMENT HELPERS
  // ============================================================================

  if (process.env.NODE_ENV === 'development') {
    // Expose global properties to window for debugging
    if (typeof window !== 'undefined') {
      ;(window as any).__global = globalPlugin
      console.log('🔧 Global Properties available at window.__global')
    }
  }
})

// ============================================================================
// TYPE DECLARATIONS
// ============================================================================

declare module 'vue' {
  interface ComponentCustomProperties {
    $global: {
      add: (propertyName: string, value: GlobalPropertyValue) => void
      remove: (propertyName: string) => void
      get: (propertyName: string) => GlobalPropertyValue
      has: (propertyName: string) => boolean
      keys: () => string[]
      clear: () => void
      state: Record<string, GlobalPropertyValue>
    }
  }
}
