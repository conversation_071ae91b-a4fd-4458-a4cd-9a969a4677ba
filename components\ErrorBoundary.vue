<template>
  <div v-if="internalHasError" class="error-boundary">
    <h2>Something went wrong.</h2>
    <p>We're sorry for the inconvenience. Please try refreshing the page, or contact support if the problem persists.</p>
    <details v-if="isDevelopmentMode" style="white-space: pre-wrap;">
      <summary>Error Details (Development Mode)</summary>
      <p v-if="internalError">{{ internalError.message }}</p>
      <pre v-if="internalError">{{ internalError.stack }}</pre>
    </details>
    <button @click="resetInternalError">Try again</button>
  </div>
  <slot v-else></slot>
</template>

<script setup lang="ts">
import { ref, onErrorCaptured, computed, getCurrentInstance } from 'vue';

const internalHasError = ref(false);
const internalError = ref<Error | null>(null);
const instance = getCurrentInstance();

// Computed property to check if in development mode
const isDevelopmentMode = computed(() => {
  // In Nuxt 3, process.dev is a common way to check for development mode
  // or use runtimeConfig if available
  return process.dev;
});

onErrorCaptured((err, vm, info) => {
  console.error("Error captured by boundary:", err, vm, info);
  internalError.value = err;
  internalHasError.value = true;
  // Return false to prevent the error from propagating further up the component tree
  return false;
});

function resetInternalError() {
  internalHasError.value = false;
  internalError.value = null;
  // Optionally, you could try to force a re-render or navigate
  // Forcing a reload is a simple approach:
  // window.location.reload();
  // Or, if you want to re-render the slot content, you might need a key change strategy
  // on the slot or its parent.
  if (instance) {
    // This is a more advanced way and might not always work as expected
    // instance.proxy?.$forceUpdate(); // or instance.ctx?.$forceUpdate();
  }
}
</script>

<style scoped>
.error-boundary {
  padding: 20px;
  border: 1px solid #d32f2f; /* Red border */
  background-color: #ffebee; /* Light red background */
  color: #c62828; /* Darker red text */
  text-align: center;
  border-radius: 8px;
  margin: 10px;
}
.error-boundary h2 {
  margin-top: 0;
  color: #b71c1c; /* Even darker red for heading */
}
.error-boundary button {
  margin-top: 15px;
  padding: 10px 20px;
  cursor: pointer;
  background-color: #d32f2f;
  color: white;
  border: none;
  border-radius: 4px;
  font-weight: bold;
}
.error-boundary button:hover {
  background-color: #b71c1c;
}
details {
  margin-top: 20px;
  text-align: left;
  background-color: #fce4ec; /* Lighter pink for details */
  padding: 15px;
  border-radius: 4px;
  border: 1px dashed #e91e63;
}
summary {
  cursor: pointer;
  font-weight: bold;
  color: #ad1457;
}
pre {
  white-space: pre-wrap; /* Ensures stack trace wraps */
  word-wrap: break-word;
  background-color: #f9f2f4;
  padding: 10px;
  border-radius: 4px;
  margin-top: 10px;
  max-height: 300px;
  overflow-y: auto;
}
</style>
