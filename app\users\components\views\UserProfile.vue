<!--
  User Profile View Component
  
  Displays detailed user profile information
-->

<template>
  <div class="space-y-6">
    <!-- Loading State -->
    <div v-if="isLoading" class="flex items-center justify-center py-12">
      <UiSpinner size="lg" />
      <span class="ml-3 text-gray-600">Loading user profile...</span>
    </div>

    <!-- User Profile -->
    <template v-else-if="user">
      <!-- Header -->
      <div class="bg-white shadow rounded-lg p-6">
        <div class="flex items-center space-x-6">
          <img class="h-20 w-20 rounded-full" :src="user.avatar" :alt="user.name" />
          <div class="flex-1">
            <h1 class="text-2xl font-bold text-gray-900">{{ user.name }}</h1>
            <p class="text-gray-600">{{ user.email }}</p>
            <div class="mt-2 flex items-center space-x-4">
              <span :class="getRoleBadgeClass(user.role)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                {{ user.role }}
              </span>
              <span :class="getStatusBadgeClass(user.status)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                {{ user.status }}
              </span>
            </div>
          </div>
          <div>
            <button
              @click="editProfile"
              class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
            >
              <Icon name="heroicons:pencil" class="h-4 w-4 mr-2" />
              Edit Profile
            </button>
          </div>
        </div>
      </div>

      <!-- Profile Details -->
      <div class="bg-white shadow rounded-lg p-6">
        <h2 class="text-lg font-medium text-gray-900 mb-6">Profile Information</h2>
        
        <dl class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <dt class="text-sm font-medium text-gray-500">Full Name</dt>
            <dd class="text-sm text-gray-900">{{ user.name }}</dd>
          </div>
          <div>
            <dt class="text-sm font-medium text-gray-500">Email</dt>
            <dd class="text-sm text-gray-900">{{ user.email }}</dd>
          </div>
          <div>
            <dt class="text-sm font-medium text-gray-500">Phone</dt>
            <dd class="text-sm text-gray-900">{{ user.phone || 'Not provided' }}</dd>
          </div>
          <div>
            <dt class="text-sm font-medium text-gray-500">Department</dt>
            <dd class="text-sm text-gray-900">{{ user.department || 'Not specified' }}</dd>
          </div>
          <div>
            <dt class="text-sm font-medium text-gray-500">Joined</dt>
            <dd class="text-sm text-gray-900">{{ formatDate(user.createdAt) }}</dd>
          </div>
          <div>
            <dt class="text-sm font-medium text-gray-500">Last Active</dt>
            <dd class="text-sm text-gray-900">{{ formatDate(user.lastActive) }}</dd>
          </div>
        </dl>
      </div>
    </template>

    <!-- User Not Found -->
    <div v-else class="text-center py-12">
      <Icon name="heroicons:exclamation-triangle" class="mx-auto h-12 w-12 text-gray-400" />
      <h3 class="mt-2 text-sm font-medium text-gray-900">User not found</h3>
      <p class="mt-1 text-sm text-gray-500">The user profile you're looking for doesn't exist.</p>
      <div class="mt-6">
        <NuxtLink
          to="/dashboard/users"
          class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-indigo-600 bg-indigo-100 hover:bg-indigo-200"
        >
          Back to Users
        </NuxtLink>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'

// Composables
const route = useRoute()

// State
const isLoading = ref(true)
const user = ref(null)

// Mock user data
const mockUser = {
  id: '1',
  name: 'John Doe',
  email: '<EMAIL>',
  phone: '+****************',
  role: 'admin',
  status: 'active',
  department: 'Legal',
  createdAt: '2023-01-15T10:30:00Z',
  lastActive: '2024-01-15T10:30:00Z',
  avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80'
}

// Methods
const loadUser = async () => {
  try {
    const userId = route.query.id
    console.log('Loading user:', userId)
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    user.value = mockUser
  } catch (error) {
    console.error('Error loading user:', error)
  } finally {
    isLoading.value = false
  }
}

const getRoleBadgeClass = (role: string) => {
  const classes = {
    admin: 'bg-purple-100 text-purple-800',
    lawyer: 'bg-blue-100 text-blue-800',
    paralegal: 'bg-green-100 text-green-800',
    client: 'bg-gray-100 text-gray-800'
  }
  return classes[role as keyof typeof classes] || 'bg-gray-100 text-gray-800'
}

const getStatusBadgeClass = (status: string) => {
  const classes = {
    active: 'bg-green-100 text-green-800',
    inactive: 'bg-gray-100 text-gray-800',
    pending: 'bg-yellow-100 text-yellow-800'
  }
  return classes[status as keyof typeof classes] || 'bg-gray-100 text-gray-800'
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

const editProfile = () => {
  console.log('Edit profile')
  // Navigate to edit form or open modal
}

// Lifecycle
onMounted(() => {
  loadUser()
})
</script>
