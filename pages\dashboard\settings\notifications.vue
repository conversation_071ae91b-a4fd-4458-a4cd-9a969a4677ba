<template>
  <div class="space-y-6">
    <!-- Notification Preferences -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
      <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <h2 class="text-xl font-semibold text-gray-900 dark:text-white">Notification Preferences</h2>
        <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
          Configure how and when you receive notifications
        </p>
      </div>

      <div class="p-6 space-y-6">
        <!-- Email Notifications -->
        <div>
          <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Email Notifications</h3>
          <div class="space-y-4">
            <div v-for="setting in emailSettings" :key="setting.key" class="flex items-center justify-between">
              <div>
                <label class="text-sm font-medium text-gray-900 dark:text-white">{{ setting.label }}</label>
                <p class="text-xs text-gray-500 dark:text-gray-400">{{ setting.description }}</p>
              </div>
              <input
                v-model="setting.enabled"
                type="checkbox"
                class="h-4 w-4 text-brandPrimary focus:ring-brandPrimary border-gray-300 rounded"
              />
            </div>
          </div>
        </div>

        <!-- Push Notifications -->
        <div>
          <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Push Notifications</h3>
          <div class="space-y-4">
            <div v-for="setting in pushSettings" :key="setting.key" class="flex items-center justify-between">
              <div>
                <label class="text-sm font-medium text-gray-900 dark:text-white">{{ setting.label }}</label>
                <p class="text-xs text-gray-500 dark:text-gray-400">{{ setting.description }}</p>
              </div>
              <input
                v-model="setting.enabled"
                type="checkbox"
                class="h-4 w-4 text-brandPrimary focus:ring-brandPrimary border-gray-300 rounded"
              />
            </div>
          </div>
        </div>

        <!-- Notification Frequency -->
        <div>
          <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Notification Frequency</h3>
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-900 dark:text-white mb-2">
                Email Digest Frequency
              </label>
              <select
                v-model="digestFrequency"
                class="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-brandPrimary focus:border-brandPrimary dark:bg-gray-700 dark:text-white"
              >
                <option value="immediate">Immediate</option>
                <option value="hourly">Hourly</option>
                <option value="daily">Daily</option>
                <option value="weekly">Weekly</option>
                <option value="never">Never</option>
              </select>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-900 dark:text-white mb-2">
                Quiet Hours
              </label>
              <div class="flex items-center gap-4">
                <div>
                  <label class="block text-xs text-gray-500 dark:text-gray-400 mb-1">From</label>
                  <input
                    v-model="quietHours.start"
                    type="time"
                    class="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-brandPrimary focus:border-brandPrimary dark:bg-gray-700 dark:text-white"
                  />
                </div>
                <div>
                  <label class="block text-xs text-gray-500 dark:text-gray-400 mb-1">To</label>
                  <input
                    v-model="quietHours.end"
                    type="time"
                    class="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-brandPrimary focus:border-brandPrimary dark:bg-gray-700 dark:text-white"
                  />
                </div>
              </div>
              <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                No notifications will be sent during these hours
              </p>
            </div>
          </div>
        </div>

        <!-- Priority Filters -->
        <div>
          <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Priority Filters</h3>
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-900 dark:text-white mb-2">
                Minimum Priority for Email
              </label>
              <select
                v-model="minEmailPriority"
                class="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-brandPrimary focus:border-brandPrimary dark:bg-gray-700 dark:text-white"
              >
                <option value="low">Low</option>
                <option value="normal">Normal</option>
                <option value="high">High</option>
                <option value="urgent">Urgent</option>
              </select>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-900 dark:text-white mb-2">
                Minimum Priority for Push
              </label>
              <select
                v-model="minPushPriority"
                class="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-brandPrimary focus:border-brandPrimary dark:bg-gray-700 dark:text-white"
              >
                <option value="low">Low</option>
                <option value="normal">Normal</option>
                <option value="high">High</option>
                <option value="urgent">Urgent</option>
              </select>
            </div>
          </div>
        </div>

        <!-- Actions -->
        <div class="flex justify-end gap-3 pt-6 border-t border-gray-200 dark:border-gray-700">
          <UiButton @click="resetToDefaults" variant="outline">
            Reset to Defaults
          </UiButton>
          <UiButton @click="saveSettings" variant="primary">
            Save Settings
          </UiButton>
        </div>
      </div>
    </div>

    <!-- Notification History -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
      <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between">
          <div>
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">Notification History</h2>
            <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
              Recent notification activity and delivery status
            </p>
          </div>
          <UiButton @click="clearHistory" variant="outline" size="sm">
            Clear History
          </UiButton>
        </div>
      </div>

      <div class="p-6">
        <div class="space-y-4">
          <div v-for="item in notificationHistory" :key="item.id" class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <div class="flex items-center gap-3">
              <div :class="['w-2 h-2 rounded-full', getStatusColor(item.status)]"></div>
              <div>
                <p class="text-sm font-medium text-gray-900 dark:text-white">{{ item.title }}</p>
                <p class="text-xs text-gray-500 dark:text-gray-400">
                  {{ item.type }} • {{ formatTimestamp(item.timestamp) }}
                </p>
              </div>
            </div>
            <div class="text-right">
              <span :class="['text-xs px-2 py-1 rounded-full', getStatusClass(item.status)]">
                {{ item.status }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

// Notification settings
const emailSettings = ref([
  {
    key: 'security_alerts',
    label: 'Security Alerts',
    description: 'Critical security events and threats',
    enabled: true
  },
  {
    key: 'system_updates',
    label: 'System Updates',
    description: 'Platform updates and maintenance notifications',
    enabled: true
  },
  {
    key: 'user_activity',
    label: 'User Activity',
    description: 'New user registrations and role changes',
    enabled: false
  },
  {
    key: 'tenant_activity',
    label: 'Tenant Activity',
    description: 'Tenant creation, updates, and plan changes',
    enabled: true
  },
  {
    key: 'backup_reports',
    label: 'Backup Reports',
    description: 'Daily backup completion and failure reports',
    enabled: false
  }
])

const pushSettings = ref([
  {
    key: 'urgent_alerts',
    label: 'Urgent Alerts',
    description: 'High priority alerts requiring immediate attention',
    enabled: true
  },
  {
    key: 'security_events',
    label: 'Security Events',
    description: 'Security-related notifications',
    enabled: true
  },
  {
    key: 'system_status',
    label: 'System Status',
    description: 'System health and performance alerts',
    enabled: false
  }
])

const digestFrequency = ref('daily')
const quietHours = ref({
  start: '22:00',
  end: '08:00'
})
const minEmailPriority = ref('normal')
const minPushPriority = ref('high')

// Notification history
const notificationHistory = ref([
  {
    id: '1',
    title: 'Security Alert: Failed Login Attempts',
    type: 'Email',
    status: 'delivered',
    timestamp: new Date(Date.now() - 1000 * 60 * 30).toISOString()
  },
  {
    id: '2',
    title: 'System Update Available',
    type: 'Push',
    status: 'delivered',
    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString()
  },
  {
    id: '3',
    title: 'Backup Completed Successfully',
    type: 'Email',
    status: 'failed',
    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 6).toISOString()
  }
])

// Methods
const saveSettings = () => {
  console.log('Saving notification settings...')
  // Implement save logic
}

const resetToDefaults = () => {
  console.log('Resetting to defaults...')
  // Implement reset logic
}

const clearHistory = () => {
  notificationHistory.value = []
}

const getStatusColor = (status: string) => {
  const colors = {
    delivered: 'bg-green-500',
    failed: 'bg-red-500',
    pending: 'bg-yellow-500'
  }
  return colors[status as keyof typeof colors] || 'bg-gray-500'
}

const getStatusClass = (status: string) => {
  const classes = {
    delivered: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
    failed: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
    pending: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'
  }
  return classes[status as keyof typeof classes] || 'bg-gray-100 text-gray-800'
}

const formatTimestamp = (timestamp: string) => {
  return new Date(timestamp).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

definePageMeta({
  layout: 'dashboard',
  title: 'Notification Settings',
  showPageHeader: true,
  pageHeaderTitle: 'Notification Settings',
  pageHeaderDescription: 'Configure your notification preferences and delivery options',
  pageHeaderIcon: 'material-symbols:notifications-active',
  breadcrumb: [
    { label: 'Dashboard', href: '/dashboard' },
    { label: 'Settings', href: '/dashboard/settings' },
    { label: 'Notifications' },
  ],
});
</script>
