<template>
  
  <ui-tabs v-model="activeTab" v-bind="tabProps" :tabs="tabs">
   
    <template v-for="tab in tabs" :key="tab.value" v-slot:[`panel-${tab.value}`] :error="hasErrors(tab)">
      
      <slot :name="tab.value" />
    </template>
  </ui-tabs>
</template>
<script setup lang="ts">
import { ref } from "vue";
import { useFormsProps } from "~/app/shared/composables/useFormsProps";
import { useFormErrors } from 'vee-validate'

defineOptions({
  inheritAttrs: false,
});
interface Props {
  tabs: any[];
  props?: any;
}

const props = withDefaults(defineProps<Props>(), {
  props: () => ({}),
});

const { props: tabProps } = useFormsProps(props);

const activeTab = ref(props.tabs[0].name);

const errors = useFormErrors()

 const hasErrors = (tab : any) => {
  const fieldNames = getFieldNames(tab)
  return fieldNames.some(field => errors.value[field])
}


const getFieldNames = (obj : any, results = []) => { // Recursive function to search for fields in nested objects
  if (typeof obj !== 'object' || obj === null) return results;

  if ('component' in obj && 'name' in obj) {
    results.push(obj.name);
  }

  for (const key in obj) {
    if (typeof obj[key] === 'object') {
      getFieldNames(obj[key], results); // Recursive call to search for fields in nested objects
    }
  }

  return results;
}

const tabs = computed(() => {
  return props.tabs.map(tab => ({
    ...tab,
    error: hasErrors(tab)
  }))
})
 

</script>