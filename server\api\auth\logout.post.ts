// server/api/auth/logout.post.ts
import { defineEventHandler, setCookie } from 'h3';
import { createApiInstance } from '~/server/utils/apiInstance';
import { ACCESS_TOKEN_COOKIE, REFRESH_TOKEN_COOKIE } from '~/server/constants'; // REFRESH_TOKEN_COOKIE no longer needed here

// Define cookie options for the access token
const accessTokenCookieOptions = (refresh = false) =>({
  httpOnly: true,
  secure: process.env.NODE_ENV === 'production',
  sameSite:  process.env.NODE_ENV === 'production' ? 'strict' as const : 'lax' as const,
  path: refresh ? '/api/' : '/',
  maxAge: -1, // Expire the cookie
});

export default defineEventHandler(async (event) => {
  const api = createApiInstance(event); // Get API instance with current event context

  try {
    // 1. Inform the main backend to invalidate its session (and its refresh token cookie).
    // The browser will send the main backend's HttpOnly refresh_token cookie with this request
    // because `withCredentials: true` is set in our `createApiInstance` utility.
    // The access token is also sent via the interceptor.
    await api.post('/auth/logout');

    // 2. Clear only the Nuxt server's access_token HttpOnly cookie on the client
    setCookie(event, ACCESS_TOKEN_COOKIE, '', accessTokenCookieOptions());
    setCookie(event, REFRESH_TOKEN_COOKIE, '', accessTokenCookieOptions(true));

    // The main backend's refresh_token cookie is cleared by the main backend itself upon successful logout.
    // The Nuxt server does not (and should not) try to clear cookies for another domain.

    return { message: 'Logged out successfully' };

  } catch (error: any) {
    console.error('Nuxt Server Logout Error:', error.response?.data || error.message);
    
    // Even if backend logout fails, clear the Nuxt server's access_token cookie for security
    setCookie(event, ACCESS_TOKEN_COOKIE, '', accessTokenCookieOptions());
    setCookie(event, REFRESH_TOKEN_COOKIE, '', accessTokenCookieOptions(true));
    
    // The client's local session is now terminated.
    // The status of the main backend's session is its own concern (and has been logged).
    return { message: 'Local logout successful; backend logout may have encountered an issue (see server logs).' };
    // Optionally, re-throw if you need to inform the client of the backend error:
    // throw createError({
    //   statusCode: error.response?.status || 500,
    //   statusMessage: error.response?.data?.message || 'Logout failed, but client access token cleared.',
    //   data: error.response?.data,
    // });
  }
});