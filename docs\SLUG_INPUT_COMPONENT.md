# UiSlugInput Component

A specialized input component for handling URL-friendly slugs with automatic transformation, validation, and async uniqueness checking.

## Features

- ✅ **Automatic Transformation**: Converts spaces to hyphens and filters invalid characters
- ✅ **Real-time Validation**: Validates input as user types with visual feedback
- ✅ **Async Uniqueness Check**: Debounced API calls to verify slug availability
- ✅ **Customizable Pattern**: Configure allowed characters via regex
- ✅ **VeeValidate Integration**: Works seamlessly with form validation
- ✅ **Accessibility**: Full keyboard navigation and screen reader support
- ✅ **Loading States**: Visual indicators during validation
- ✅ **Error Handling**: Comprehensive error messages and states

## Basic Usage

```vue
<template>
  <UiSlugInput
    id="tenant-slug"
    name="tenantSlug"
    v-model="slug"
    label="Tenant Slug"
    placeholder="Enter slug"
    :check-uniqueness="checkSlugUniqueness"
    required
  />
</template>

<script setup>
import { ref } from 'vue'

const slug = ref('')

const checkSlugUniqueness = async (slug) => {
  const response = await $fetch(`/api/check-slug?slug=${slug}`)
  return response.isUnique
}
</script>
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `id` | `string` | - | **Required.** Input element ID |
| `name` | `string` | - | **Required.** Field name for form validation |
| `modelValue` | `string` | `''` | Input value (v-model) |
| `label` | `string` | - | Input label text |
| `placeholder` | `string` | - | Placeholder text |
| `required` | `boolean` | `false` | Whether field is required |
| `disabled` | `boolean` | `false` | Disable input |
| `readonly` | `boolean` | `false` | Make input read-only |
| `helpText` | `string` | - | Help text below input |
| `successMessage` | `string` | - | Success message to display |
| `leadingIcon` | `string` | `'material-symbols:link'` | Icon before input |
| `trailingIcon` | `string` | - | Icon after input |
| `size` | `'sm' \| 'md' \| 'lg'` | `'md'` | Input size |
| `variant` | `'default' \| 'filled' \| 'outlined'` | `'default'` | Input style variant |
| `showCharCount` | `boolean` | `false` | Show character counter |
| `showSuccess` | `boolean` | `true` | Show success states |
| `maxlength` | `number` | - | Maximum character length |
| `minlength` | `number` | - | Minimum character length |
| `checkUniqueness` | `(slug: string) => Promise<boolean>` | - | Async function to check uniqueness |
| `debounceMs` | `number` | `500` | Debounce delay for uniqueness check |
| `allowedPattern` | `RegExp` | `/^[a-z0-9-]*$/` | Regex pattern for allowed characters |
| `transformSpaces` | `boolean` | `true` | Convert spaces to hyphens |

## Events

| Event | Payload | Description |
|-------|---------|-------------|
| `update:modelValue` | `string` | Emitted when value changes |
| `focus` | `FocusEvent` | Input focus event |
| `blur` | `FocusEvent` | Input blur event |
| `input` | `Event` | Input event |
| `keydown` | `KeyboardEvent` | Keydown event |
| `validation-change` | `(isValid: boolean, isValidating: boolean)` | Validation state changes |

## Validation States

The component provides visual feedback for different validation states:

- **Default**: Normal input state
- **Validating**: Shows spinner while checking uniqueness
- **Valid**: Green border and checkmark icon
- **Invalid**: Red border and error icon with message

## Using with Composables

The component works great with the `useSlugValidation` composable:

```vue
<template>
  <UiSlugInput
    id="tenant-slug"
    name="tenantSlug"
    v-model="slug"
    label="Tenant Slug"
    :check-uniqueness="slugValidator.checkSlugUniqueness"
  />
</template>

<script setup>
const slug = ref('')
const slugValidator = useTenantSlugValidation()

// Generate slug from text
const generateSlug = () => {
  slug.value = slugValidator.generateSlug('My Company Name')
}

// Generate unique slug
const generateUniqueSlug = async () => {
  slug.value = await slugValidator.generateUniqueSlug('my-company')
}
</script>
```

## Customization Examples

### Custom Pattern (Allow Underscores)

```vue
<UiSlugInput
  v-model="slug"
  :allowed-pattern="/^[a-z0-9_-]*$/"
  :transform-spaces="false"
/>
```

### Different Debounce Timing

```vue
<UiSlugInput
  v-model="slug"
  :debounce-ms="300"
  :check-uniqueness="checkUniqueness"
/>
```

### Custom Size and Styling

```vue
<UiSlugInput
  v-model="slug"
  size="lg"
  variant="filled"
  show-char-count
  :maxlength="50"
/>
```

## Form Integration

Works seamlessly with VeeValidate and other form libraries:

```vue
<template>
  <Form @submit="handleSubmit" :validation-schema="schema">
    <UiSlugInput
      name="slug"
      v-model="formData.slug"
      label="Slug"
      :check-uniqueness="checkUniqueness"
      required
    />
    <button type="submit">Submit</button>
  </Form>
</template>

<script setup>
import { Form } from 'vee-validate'
import * as yup from 'yup'

const schema = yup.object({
  slug: yup.string()
    .required('Slug is required')
    .matches(/^[a-z0-9-]+$/, 'Invalid slug format')
})
</script>
```

## Accessibility

The component includes comprehensive accessibility features:

- Proper ARIA labels and descriptions
- Keyboard navigation support
- Screen reader announcements for validation states
- Focus management
- High contrast support

## API Integration

For the uniqueness check to work, your API should provide an endpoint that:

1. Accepts a `slug` parameter
2. Returns a boolean indicating availability
3. Optionally accepts an `excludeId` parameter for edit scenarios

Example API response:
```json
{
  "isUnique": true,
  "exists": false
}
```

## Best Practices

1. **Always provide uniqueness checking** for user-facing slugs
2. **Use appropriate debounce timing** (300-500ms recommended)
3. **Provide helpful error messages** for validation failures
4. **Consider reserved words** in your uniqueness logic
5. **Test with various input patterns** including edge cases
6. **Provide visual feedback** during validation states
