// composables/useAuth.ts
import { useRouter } from 'vue-router'
import { storeToRefs } from 'pinia'
import { useAuthStore } from '../stores/auth'
import { PlatformRoles, TenantRoles } from '../app/features/auth/constants/roles' // Updated to new role enums
import type { TenantRoleAssignment } from '../types/auth' // Import TenantRoleAssignment
import { computed, onUnmounted, getCurrentInstance } from 'vue' // Import computed for role getters

export const useAuth = () => {
  const authStore = useAuthStore();
  const router = useRouter();
  // useToast(); // Assuming useToast() might have side effects or is used elsewhere implicitly

  // Destructure reactive state from the store to maintain reactivity.
  // Note: refreshToken is NOT in the store for security reasons (HTTP-only cookie).
  const {
    getToken: accessToken,
    isAuthenticated,
    currentUser,
    isLoading,
    userPermissions, // Assuming this is still relevant or will be adapted
    platformUserRoles, // New getter from store
    activeTenantUserRoles, // New getter from store
    activeTenantId // New getter from store
  } = storeToRefs(authStore);

  // Local unsubscribe function for this composable instance
  let unsubscribe: (() => void) | null = null;

  unsubscribe = authStore.$onAction(({ name, args, store, after }: {
    name: string;
    args: any[];
    store: ReturnType<typeof useAuthStore>;
    after: (callback: (resolvedValue: any) => void) => void; // Corrected 'after' signature
    onError?: (error: any) => void; // Optional: handle errors
  }) => {
    after((resolvedValue: any) => { // Callback for after takes the resolved value
      if (name === 'login' || name === 'logout') {
         if (store.isAuthenticated) {
          router.push('/dashboard');
        } else {
          router.push('/auth/login');
        }
      }

      if (name === 'initAuth') {
        console.log(name, args, store.isAuthenticated, resolvedValue);
      }
    })



  })
  // Function to initialize authentication state
  const initAuth = async () => {
    return await authStore.initAuth();
  }

  // Function to handle user login
  const login = async (credentials: any, options: any) => {
    return authStore.login(credentials, options);
  };

  // Function to handle user logout
  const logout = async () => {
    return authStore.logout();
  };

  // Utility function to check if the current user has a specific platform role
  const hasPlatformRole = (role: PlatformRoles): boolean => {
    console.log('hasPlatformRole', role, platformUserRoles.value);
    return platformUserRoles.value?.includes(role) || false;
  };

  // Utility function to check if the current user has a specific role in the active tenant
  const hasActiveTenantRole = (role: TenantRoles): boolean => {
    return activeTenantUserRoles.value?.includes(role) || false;
  };
  
  // Utility function to check if the user has a specific role in ANY of their assigned tenants
  // This might be useful for some UI elements, but RBAC middleware should be more specific.
  const hasTenantRoleInAnyAssignedTenant = (role: TenantRoles): boolean => {
    return currentUser.value?.tenantAccess?.some((ta: TenantRoleAssignment) => ta.roles.includes(role)) || false;
  };

  // Convenience getters for common roles using new scoped checkers
  const isSuperAdmin = computed(() => hasPlatformRole(PlatformRoles.SUPER_ADMIN));
  const isSupport = computed(() => hasPlatformRole(PlatformRoles.SUPPORT));
  const isPlatformUser = computed(() => hasPlatformRole(PlatformRoles.SUPER_ADMIN) || hasPlatformRole(PlatformRoles.SUPPORT));  
  
  const isTenantOwner = computed(() => hasActiveTenantRole(TenantRoles.TENANT_OWNER));
  const isAdminInActiveTenant = computed(() => hasActiveTenantRole(TenantRoles.ADMIN)); // Renamed for clarity
  const isLawyerInActiveTenant = computed(() => hasActiveTenantRole(TenantRoles.LAWYER)); // Renamed for clarity
  const isParalegalInActiveTenant = computed(() => hasActiveTenantRole(TenantRoles.PARALEGAL)); // Renamed for clarity
  const isClientInActiveTenant = computed(() => hasActiveTenantRole(TenantRoles.CLIENT)); // Renamed for clarity

  // Check if user has any platform role
  const hasAnyPlatformRole = computed(() => platformUserRoles.value && platformUserRoles.value.length > 0);
  // Check if user has any role in the active tenant
  const hasAnyActiveTenantRole = computed(() => activeTenantUserRoles.value && activeTenantUserRoles.value.length > 0);

  // Cleanup on unmount
  if (getCurrentInstance()) {
    onUnmounted(() => {
      if (unsubscribe) {
        unsubscribe()
        unsubscribe = null
      }
    })
  }

  // Expose authentication state and actions
  return {
    // State (from storeToRefs)
    accessToken,
    isAuthenticated,
    currentUser,
    isLoading,
    userPermissions, // Keep if still used
    platformUserRoles, // Expose new scoped roles
    activeTenantUserRoles, // Expose new scoped roles
    activeTenantId, // Expose active tenant ID

    // Actions (from authStore)
    login,
    logout,
    initAuth,
    setActiveTenant: authStore.setActiveTenant, // Expose action from store

    // Role-based access control (new scoped functions and computed properties)
    hasPlatformRole,
    hasActiveTenantRole,
    hasTenantRoleInAnyAssignedTenant,
    isSuperAdmin,
    isSupport,
    isPlatformUser,
    isTenantOwner, // This now refers to the active tenant
    isAdminInActiveTenant,
    isLawyerInActiveTenant,
    isParalegalInActiveTenant,
    isClientInActiveTenant,
    hasAnyPlatformRole,
    hasAnyActiveTenantRole,

    // Expose 2FA status from the store
    is2faEnabled: computed(() => authStore.isOtpEnabled), // Updated to use isOtpEnabled from store
  };
};