<template>
  <UiModal @close="$emit('close')" size="xl">
    <div class="p-6">
      <!-- Header -->
      <div class="flex items-center space-x-3 mb-6">
        <div class="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
          <Icon name="heroicons:document-text" class="w-5 h-5 text-blue-600 dark:text-blue-400" />
        </div>
        <div>
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Document Template Library</h3>
          <p class="text-sm text-gray-600 dark:text-gray-400">Choose from professional legal document templates</p>
        </div>
      </div>

      <!-- Search and Filter -->
      <div class="flex items-center space-x-4 mb-6">
        <div class="flex-1">
          <UiInput
            v-model="searchQuery"
            placeholder="Search document templates..."
            class="w-full"
          >
            <template #prefix>
              <Icon name="heroicons:magnifying-glass" class="w-4 h-4 text-gray-400" />
            </template>
          </UiInput>
        </div>
        <div>
          <select
            v-model="selectedCategory"
            class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm"
          >
            <option value="">All Categories</option>
            <option value="contracts">Contracts</option>
            <option value="agreements">Agreements</option>
            <option value="letters">Letters</option>
            <option value="forms">Forms</option>
            <option value="pleadings">Pleadings</option>
          </select>
        </div>
      </div>

      <!-- Template Grid -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4 max-h-96 overflow-y-auto">
        <div
          v-for="template in filteredTemplates"
          :key="template.id"
          class="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:border-blue-300 dark:hover:border-blue-600 cursor-pointer transition-colors"
          @click="selectTemplate(template)"
        >
          <div class="flex items-start justify-between mb-3">
            <div>
              <h4 class="text-sm font-medium text-gray-900 dark:text-white">{{ template.name }}</h4>
              <p class="text-xs text-gray-500 dark:text-gray-400">{{ template.category }}</p>
            </div>
            <span
              :class="[
                'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium',
                getCategoryColor(template.category)
              ]"
            >
              {{ template.category }}
            </span>
          </div>
          
          <p class="text-sm text-gray-600 dark:text-gray-300 mb-3">{{ template.description }}</p>
          
          <!-- Document Preview -->
          <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-3 mb-3">
            <div class="text-xs text-gray-500 dark:text-gray-400 mb-2">Preview:</div>
            <div class="text-xs text-gray-900 dark:text-white font-mono leading-relaxed">
              {{ template.preview }}
            </div>
          </div>
          
          <!-- Template Features -->
          <div class="space-y-2">
            <div class="text-xs text-gray-500 dark:text-gray-400">Features:</div>
            <div class="flex flex-wrap gap-1">
              <span
                v-for="feature in template.features"
                :key="feature"
                class="inline-flex items-center px-1.5 py-0.5 rounded text-xs bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
              >
                {{ feature }}
              </span>
            </div>
          </div>
          
          <div v-if="template.variables.length > 0" class="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
            <div class="text-xs text-gray-500 dark:text-gray-400 mb-1">Variables:</div>
            <div class="flex flex-wrap gap-1">
              <span
                v-for="variable in template.variables.slice(0, 4)"
                :key="variable"
                class="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-mono bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
              >
                {{ variable }}
              </span>
              <span
                v-if="template.variables.length > 4"
                class="text-xs text-gray-500 dark:text-gray-400"
              >
                +{{ template.variables.length - 4 }} more
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- Actions -->
      <div class="flex items-center justify-end space-x-3 mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
        <UiButton @click="$emit('close')" variant="outline">
          Cancel
        </UiButton>
      </div>
    </div>
  </UiModal>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

// Emits
const emit = defineEmits<{
  close: []
  select: [template: any]
}>()

// State
const searchQuery = ref('')
const selectedCategory = ref('')

// Document templates library
const documentTemplates = [
  {
    id: 'service-agreement',
    name: 'Service Agreement',
    category: 'agreements',
    description: 'Professional service agreement template for legal services',
    preview: 'This Service Agreement ("Agreement") is entered into on {date} between {companyName} ("Provider") and {clientName} ("Client")...',
    content: `
      <h1>SERVICE AGREEMENT</h1>
      <p>This Service Agreement ("Agreement") is entered into on {date} between {companyName} ("Provider") and {clientName} ("Client").</p>
      
      <h2>1. SERVICES</h2>
      <p>Provider agrees to provide the following legal services: {serviceDescription}</p>
      
      <h2>2. COMPENSATION</h2>
      <p>Client agrees to pay Provider {hourlyRate} per hour for services rendered.</p>
      
      <h2>3. TERM</h2>
      <p>This Agreement shall commence on {startDate} and continue until {endDate} unless terminated earlier.</p>
      
      <h2>4. TERMINATION</h2>
      <p>Either party may terminate this Agreement with {noticePeriod} days written notice.</p>
      
      <h2>5. GOVERNING LAW</h2>
      <p>This Agreement shall be governed by the laws of {jurisdiction}.</p>
      
      <div style="margin-top: 50px;">
        <p>Provider: _________________________ Date: _________</p>
        <p>{attorneyName}, {companyName}</p>
        <br>
        <p>Client: _________________________ Date: _________</p>
        <p>{clientName}</p>
      </div>
    `,
    features: ['Digital Signature', 'Hourly Billing', 'Termination Clause'],
    variables: ['date', 'companyName', 'clientName', 'serviceDescription', 'hourlyRate', 'startDate', 'endDate', 'noticePeriod', 'jurisdiction', 'attorneyName']
  },
  {
    id: 'retainer-agreement',
    name: 'Retainer Agreement',
    category: 'agreements',
    description: 'Standard retainer agreement for ongoing legal representation',
    preview: 'RETAINER AGREEMENT between {companyName} and {clientName} for legal representation in {caseType}...',
    content: `
      <h1>RETAINER AGREEMENT</h1>
      <p>This Retainer Agreement is made between {companyName} ("Attorney") and {clientName} ("Client") for legal representation.</p>
      
      <h2>1. SCOPE OF REPRESENTATION</h2>
      <p>Attorney agrees to represent Client in the matter of: {caseDescription}</p>
      
      <h2>2. RETAINER FEE</h2>
      <p>Client agrees to pay a retainer fee of {retainerAmount} to be held in Attorney's trust account.</p>
      
      <h2>3. HOURLY RATE</h2>
      <p>Attorney's hourly rate is {hourlyRate}. Time will be billed in {billingIncrement} increments.</p>
      
      <h2>4. COSTS AND EXPENSES</h2>
      <p>Client is responsible for all costs and expenses including court fees, filing fees, and expert witness fees.</p>
      
      <h2>5. BILLING</h2>
      <p>Client will receive monthly statements. Payment is due within {paymentTerms} days of receipt.</p>
      
      <div style="margin-top: 50px;">
        <p>Attorney: _________________________ Date: _________</p>
        <p>{attorneyName}, {companyName}</p>
        <br>
        <p>Client: _________________________ Date: _________</p>
        <p>{clientName}</p>
      </div>
    `,
    features: ['Trust Account', 'Billing Terms', 'Scope Definition'],
    variables: ['companyName', 'clientName', 'caseDescription', 'retainerAmount', 'hourlyRate', 'billingIncrement', 'paymentTerms', 'attorneyName']
  },
  {
    id: 'demand-letter',
    name: 'Demand Letter',
    category: 'letters',
    description: 'Professional demand letter template for debt collection',
    preview: 'Dear {recipientName}, This letter serves as formal demand for payment of {amount} owed to {clientName}...',
    content: `
      <div style="text-align: right; margin-bottom: 30px;">
        <p>{companyName}</p>
        <p>{companyAddress}</p>
        <p>{companyPhone}</p>
        <p>{date}</p>
      </div>
      
      <p>{recipientName}</p>
      <p>{recipientAddress}</p>
      
      <p><strong>Re: Demand for Payment - {subject}</strong></p>
      
      <p>Dear {recipientName}:</p>
      
      <p>This letter serves as formal demand for payment of {amount} owed to {clientName} for {description}.</p>
      
      <p>Despite previous attempts to collect this debt, payment remains outstanding as of {currentDate}.</p>
      
      <p>DEMAND IS HEREBY MADE that you pay the full amount of {amount} within {demandPeriod} days of receipt of this letter.</p>
      
      <p>If payment is not received within the specified time period, we will pursue all available legal remedies including but not limited to:</p>
      <ul>
        <li>Filing a lawsuit for the full amount plus interest and costs</li>
        <li>Seeking attorney's fees as provided by law</li>
        <li>Pursuing collection through all legal means</li>
      </ul>
      
      <p>This letter is not intended as a threat but as a statement of our client's intent to pursue legal action if necessary.</p>
      
      <p>Sincerely,</p>
      <br>
      <p>{attorneyName}</p>
      <p>Attorney for {clientName}</p>
    `,
    features: ['Formal Demand', 'Legal Remedies', 'Professional Format'],
    variables: ['companyName', 'companyAddress', 'companyPhone', 'date', 'recipientName', 'recipientAddress', 'subject', 'clientName', 'amount', 'description', 'currentDate', 'demandPeriod', 'attorneyName']
  },
  {
    id: 'nda-agreement',
    name: 'Non-Disclosure Agreement',
    category: 'agreements',
    description: 'Mutual non-disclosure agreement for confidential information',
    preview: 'MUTUAL NON-DISCLOSURE AGREEMENT between {party1Name} and {party2Name} regarding confidential information...',
    content: `
      <h1>MUTUAL NON-DISCLOSURE AGREEMENT</h1>
      <p>This Mutual Non-Disclosure Agreement ("Agreement") is entered into on {date} between {party1Name} ("Party 1") and {party2Name} ("Party 2").</p>
      
      <h2>1. DEFINITION OF CONFIDENTIAL INFORMATION</h2>
      <p>Confidential Information includes all non-public information disclosed by either party, including but not limited to: {confidentialityScope}</p>
      
      <h2>2. OBLIGATIONS</h2>
      <p>Each party agrees to:</p>
      <ul>
        <li>Hold all Confidential Information in strict confidence</li>
        <li>Not disclose Confidential Information to third parties</li>
        <li>Use Confidential Information solely for {purpose}</li>
      </ul>
      
      <h2>3. TERM</h2>
      <p>This Agreement shall remain in effect for {term} from the date of execution.</p>
      
      <h2>4. RETURN OF INFORMATION</h2>
      <p>Upon termination, each party shall return or destroy all Confidential Information.</p>
      
      <h2>5. GOVERNING LAW</h2>
      <p>This Agreement shall be governed by the laws of {jurisdiction}.</p>
      
      <div style="margin-top: 50px;">
        <p>Party 1: _________________________ Date: _________</p>
        <p>{party1Name}</p>
        <br>
        <p>Party 2: _________________________ Date: _________</p>
        <p>{party2Name}</p>
      </div>
    `,
    features: ['Mutual Protection', 'Scope Definition', 'Return Clause'],
    variables: ['date', 'party1Name', 'party2Name', 'confidentialityScope', 'purpose', 'term', 'jurisdiction']
  },
  {
    id: 'power-of-attorney',
    name: 'Power of Attorney',
    category: 'forms',
    description: 'General power of attorney form for legal representation',
    preview: 'POWER OF ATTORNEY - {principalName} hereby appoints {agentName} as attorney-in-fact...',
    content: `
      <h1>POWER OF ATTORNEY</h1>
      
      <p>I, {principalName}, of {principalAddress}, hereby appoint {agentName}, of {agentAddress}, as my attorney-in-fact ("Agent") to act in my name, place, and stead.</p>
      
      <h2>POWERS GRANTED</h2>
      <p>My Agent is granted the following powers:</p>
      <ul>
        <li>{power1}</li>
        <li>{power2}</li>
        <li>{power3}</li>
      </ul>
      
      <h2>EFFECTIVE DATE</h2>
      <p>This Power of Attorney shall become effective on {effectiveDate} and shall remain in effect until {expirationDate} unless revoked earlier.</p>
      
      <h2>REVOCATION</h2>
      <p>I reserve the right to revoke this Power of Attorney at any time by written notice to my Agent.</p>
      
      <h2>THIRD PARTY RELIANCE</h2>
      <p>Any third party may rely upon this Power of Attorney until actual notice of revocation is received.</p>
      
      <div style="margin-top: 50px;">
        <p>Principal: _________________________ Date: _________</p>
        <p>{principalName}</p>
        <br>
        <p>Notary Public: _________________________</p>
        <p>My commission expires: _________</p>
      </div>
    `,
    features: ['Notarization Required', 'Revocation Rights', 'Third Party Protection'],
    variables: ['principalName', 'principalAddress', 'agentName', 'agentAddress', 'power1', 'power2', 'power3', 'effectiveDate', 'expirationDate']
  }
]

// Computed
const filteredTemplates = computed(() => {
  let templates = documentTemplates
  
  // Filter by category
  if (selectedCategory.value) {
    templates = templates.filter(template => template.category === selectedCategory.value)
  }
  
  // Filter by search query
  if (searchQuery.value.trim()) {
    const query = searchQuery.value.toLowerCase()
    templates = templates.filter(template =>
      template.name.toLowerCase().includes(query) ||
      template.description.toLowerCase().includes(query) ||
      template.preview.toLowerCase().includes(query)
    )
  }
  
  return templates
})

// Methods
const selectTemplate = (template: any) => {
  emit('select', template)
}

const getCategoryColor = (category: string) => {
  const colors = {
    'contracts': 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
    'agreements': 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
    'letters': 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200',
    'forms': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
    'pleadings': 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
  }
  return colors[category as keyof typeof colors] || 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
}
</script>
