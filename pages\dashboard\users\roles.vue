<template>
  <div class="space-y-8">
    <!-- Roles Management Header -->
    <div class="bg-gradient-to-r from-purple-600 to-blue-600 rounded-xl p-8 text-white">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-3xl font-bold mb-2">User Roles Management</h1>
          <p class="text-purple-100 text-lg">
            Manage user roles, permissions, and access control across the platform
          </p>
        </div>
        <div class="hidden md:flex items-center gap-4">
          <UiButton @click="showCreateRoleModal = true" class="bg-white/10 border-white/20 text-white hover:bg-white/20">
            <Icon name="material-symbols:add" class="h-4 w-4 mr-2" />
            Create Role
          </UiButton>
          <UiButton @click="exportRoles" variant="outline" class="border-white/20 text-white hover:bg-white/10">
            <Icon name="material-symbols:download" class="h-4 w-4 mr-2" />
            Export
          </UiButton>
        </div>
      </div>
    </div>

    <!-- Roles Overview -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <!-- Total Roles -->
      <UiCard
        icon="material-symbols:admin-panel-settings"
        icon-color="purple"
        :loading="isLoading"
        hover-effect
      >
        <template #header>
          <div class="flex items-center justify-between w-full">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Total Roles</h3>
            <UiBadge variant="info">{{ roles.length }}</UiBadge>
          </div>
        </template>
        <div class="space-y-3">
          <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ roles.length }}</p>
          <p class="text-sm text-gray-500 dark:text-gray-400">
            {{ activeRoles }} active, {{ inactiveRoles }} inactive
          </p>
          <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div 
              class="bg-purple-600 h-2 rounded-full transition-all duration-300"
              :style="{ width: `${(activeRoles / roles.length) * 100}%` }"
            ></div>
          </div>
        </div>
      </UiCard>

      <!-- Users with Roles -->
      <UiCard
        icon="material-symbols:group"
        icon-color="blue"
        :loading="isLoading"
        hover-effect
      >
        <template #header>
          <div class="flex items-center justify-between w-full">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Assigned Users</h3>
            <UiBadge variant="success">{{ totalAssignedUsers }}</UiBadge>
          </div>
        </template>
        <div class="space-y-3">
          <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ totalAssignedUsers }}</p>
          <p class="text-sm text-gray-500 dark:text-gray-400">
            users with assigned roles
          </p>
          <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div 
              class="bg-blue-600 h-2 rounded-full transition-all duration-300"
              :style="{ width: `${(totalAssignedUsers / 1000) * 100}%` }"
            ></div>
          </div>
        </div>
      </UiCard>

      <!-- Permissions -->
      <UiCard
        icon="material-symbols:security"
        icon-color="green"
        :loading="isLoading"
        hover-effect
      >
        <template #header>
          <div class="flex items-center justify-between w-full">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Permissions</h3>
            <UiBadge variant="info">{{ totalPermissions }}</UiBadge>
          </div>
        </template>
        <div class="space-y-3">
          <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ totalPermissions }}</p>
          <p class="text-sm text-gray-500 dark:text-gray-400">
            available permissions
          </p>
          <div class="space-y-1">
            <div class="flex justify-between text-sm">
              <span class="text-gray-500 dark:text-gray-400">Platform</span>
              <span class="text-gray-900 dark:text-white">{{ platformPermissions }}</span>
            </div>
            <div class="flex justify-between text-sm">
              <span class="text-gray-500 dark:text-gray-400">Tenant</span>
              <span class="text-gray-900 dark:text-white">{{ tenantPermissions }}</span>
            </div>
          </div>
        </div>
      </UiCard>

      <!-- Recent Changes -->
      <UiCard
        icon="material-symbols:history"
        icon-color="yellow"
        :loading="isLoading"
        hover-effect
      >
        <template #header>
          <div class="flex items-center justify-between w-full">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Recent Changes</h3>
            <UiBadge variant="warning">{{ recentChanges.length }}</UiBadge>
          </div>
        </template>
        <div class="space-y-3">
          <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ recentChanges.length }}</p>
          <p class="text-sm text-gray-500 dark:text-gray-400">
            changes this week
          </p>
          <div class="space-y-1">
            <div class="flex justify-between text-sm">
              <span class="text-gray-500 dark:text-gray-400">Created</span>
              <span class="text-gray-900 dark:text-white">{{ recentChanges.filter(c => c.type === 'created').length }}</span>
            </div>
            <div class="flex justify-between text-sm">
              <span class="text-gray-500 dark:text-gray-400">Modified</span>
              <span class="text-gray-900 dark:text-white">{{ recentChanges.filter(c => c.type === 'modified').length }}</span>
            </div>
          </div>
        </div>
      </UiCard>
    </div>

    <!-- Roles Management -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Platform Roles -->
      <UiCard>
        <template #header>
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Platform Roles</h3>
            <UiBadge variant="info">{{ platformRoles.length }} roles</UiBadge>
          </div>
        </template>
        <div class="space-y-4">
          <div
            v-for="role in platformRoles"
            :key="role.id"
            class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors duration-200"
          >
            <div class="flex items-center gap-3">
              <div :class="[
                'w-10 h-10 rounded-full flex items-center justify-center text-white font-semibold',
                role.color
              ]">
                <Icon :name="role.icon" class="h-5 w-5" />
              </div>
              <div>
                <p class="text-sm font-medium text-gray-900 dark:text-white">{{ role.name }}</p>
                <p class="text-xs text-gray-500 dark:text-gray-400">{{ role.description }}</p>
              </div>
            </div>
            <div class="flex items-center gap-2">
              <UiBadge :variant="role.isActive ? 'success' : 'neutral'" size="sm">
                {{ role.isActive ? 'Active' : 'Inactive' }}
              </UiBadge>
              <span class="text-sm text-gray-500 dark:text-gray-400">{{ role.userCount }} users</span>
              <UiButton @click="editRole(role)" variant="ghost" size="sm">
                <Icon name="material-symbols:edit" class="h-4 w-4" />
              </UiButton>
            </div>
          </div>
        </div>
      </UiCard>

      <!-- Tenant Roles -->
      <UiCard>
        <template #header>
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Tenant Roles</h3>
            <UiBadge variant="info">{{ tenantRoles.length }} roles</UiBadge>
          </div>
        </template>
        <div class="space-y-4">
          <div
            v-for="role in tenantRoles"
            :key="role.id"
            class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors duration-200"
          >
            <div class="flex items-center gap-3">
              <div :class="[
                'w-10 h-10 rounded-full flex items-center justify-center text-white font-semibold',
                role.color
              ]">
                <Icon :name="role.icon" class="h-5 w-5" />
              </div>
              <div>
                <p class="text-sm font-medium text-gray-900 dark:text-white">{{ role.name }}</p>
                <p class="text-xs text-gray-500 dark:text-gray-400">{{ role.description }}</p>
              </div>
            </div>
            <div class="flex items-center gap-2">
              <UiBadge :variant="role.isActive ? 'success' : 'neutral'" size="sm">
                {{ role.isActive ? 'Active' : 'Inactive' }}
              </UiBadge>
              <span class="text-sm text-gray-500 dark:text-gray-400">{{ role.userCount }} users</span>
              <UiButton @click="editRole(role)" variant="ghost" size="sm">
                <Icon name="material-symbols:edit" class="h-4 w-4" />
              </UiButton>
            </div>
          </div>
        </div>
      </UiCard>
    </div>

    <!-- Permissions Matrix -->
    <UiCard>
      <template #header>
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Permissions Matrix</h3>
          <div class="flex items-center gap-2">
            <UiButton @click="refreshPermissions" variant="ghost" size="sm" :loading="refreshingPermissions">
              <Icon name="material-symbols:refresh" class="h-4 w-4" />
            </UiButton>
            <UiSelect
              id="role-type-select"
              v-model="selectedRoleType"
              :options="roleTypeOptions"
              size="sm"
            />
          </div>
        </div>
      </template>
      <div class="overflow-x-auto">
        <table class="w-full">
          <thead>
            <tr class="border-b border-gray-200 dark:border-gray-700">
              <th class="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">Permission</th>
              <th
                v-for="role in filteredRoles"
                :key="role.id"
                class="text-center py-3 px-4 font-medium text-gray-900 dark:text-white min-w-[100px]"
              >
                {{ role.name }}
              </th>
            </tr>
          </thead>
          <tbody>
            <tr
              v-for="permission in filteredPermissions"
              :key="permission.id"
              class="border-b border-gray-100 dark:border-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700"
            >
              <td class="py-3 px-4">
                <div>
                  <p class="text-sm font-medium text-gray-900 dark:text-white">{{ permission.name }}</p>
                  <p class="text-xs text-gray-500 dark:text-gray-400">{{ permission.description }}</p>
                </div>
              </td>
              <td
                v-for="role in filteredRoles"
                :key="`${permission.id}-${role.id}`"
                class="text-center py-3 px-4"
              >
                <UiCheckbox
                  :id="`permission-${permission.id}-${role.id}`"
                  :model-value="hasPermission(role, permission)"
                  @update:model-value="togglePermission(role, permission, $event)"
                  :disabled="!canEditRole(role)"
                />
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </UiCard>

    <!-- Recent Role Changes -->
    <UiCard>
      <template #header>
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Recent Role Changes</h3>
          <UiButton @click="viewAuditLog" variant="ghost" size="sm">
            View Full Log
          </UiButton>
        </div>
      </template>
      <div class="space-y-3">
        <div
          v-for="change in recentChanges"
          :key="change.id"
          class="flex items-start gap-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg"
        >
          <div class="flex-shrink-0">
            <div :class="[
              'w-8 h-8 rounded-full flex items-center justify-center',
              change.type === 'created' ? 'bg-green-100 dark:bg-green-900/20' :
              change.type === 'modified' ? 'bg-blue-100 dark:bg-blue-900/20' :
              'bg-red-100 dark:bg-red-900/20'
            ]">
              <Icon :name="change.icon" :class="[
                'h-4 w-4',
                change.type === 'created' ? 'text-green-600 dark:text-green-400' :
                change.type === 'modified' ? 'text-blue-600 dark:text-blue-400' :
                'text-red-600 dark:text-red-400'
              ]" />
            </div>
          </div>
          <div class="flex-1 min-w-0">
            <p class="text-sm text-gray-900 dark:text-white">{{ change.description }}</p>
            <div class="flex items-center gap-2 mt-1">
              <p class="text-xs text-gray-500 dark:text-gray-400">{{ change.timestamp }}</p>
              <span class="text-xs text-gray-400">•</span>
              <p class="text-xs text-gray-500 dark:text-gray-400">by {{ change.user }}</p>
            </div>
          </div>
        </div>

        <div v-if="recentChanges.length === 0" class="text-center py-6">
          <Icon name="material-symbols:history" class="h-8 w-8 text-gray-400 mx-auto mb-2" />
          <p class="text-sm text-gray-500 dark:text-gray-400">No recent changes</p>
        </div>
      </div>
    </UiCard>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { PlatformRoles, TenantRoles } from '~/app/features/auth/constants/roles'

// Page meta configuration
definePageMeta({
  layout: 'dashboard',
  title: 'User Roles Management',
  description: 'Manage user roles, permissions, and access control across the platform',
  pageHeaderIcon: 'material-symbols:admin-panel-settings',
  pageHeaderStats: [
    { key: 'roles', label: 'Total Roles', value: '12', color: 'purple' },
    { key: 'users', label: 'Assigned Users', value: '847', color: 'blue' },
    { key: 'permissions', label: 'Permissions', value: '45', color: 'green' },
    { key: 'changes', label: 'Recent Changes', value: '8', color: 'yellow' }
  ],
  showRealTimeStatus: false,
  autoRefreshEnabled: false,
  middleware: ['rbac'],
  roles: [PlatformRoles.SUPER_ADMIN],
  breadcrumb: [
    { label: 'Dashboard', href: '/dashboard' },
    { label: 'Users', href: '/dashboard/users' },
    { label: 'Roles' },
  ],
})

// Reactive state
const isLoading = ref(true)
const refreshingPermissions = ref(false)
const showCreateRoleModal = ref(false)
const selectedRoleType = ref('all')

// Role type options
const roleTypeOptions = [
  { value: 'all', label: 'All Roles' },
  { value: 'platform', label: 'Platform Roles' },
  { value: 'tenant', label: 'Tenant Roles' }
]

// Platform roles data
const platformRoles = ref([
  {
    id: 1,
    name: 'Super Admin',
    description: 'Full platform access and control',
    icon: 'material-symbols:admin-panel-settings',
    color: 'bg-red-500',
    isActive: true,
    userCount: 3,
    permissions: ['platform.admin', 'platform.users', 'platform.tenants', 'platform.settings']
  },
  {
    id: 2,
    name: 'Platform Admin',
    description: 'Platform administration without system settings',
    icon: 'material-symbols:manage-accounts',
    color: 'bg-orange-500',
    isActive: true,
    userCount: 8,
    permissions: ['platform.users', 'platform.tenants', 'platform.analytics']
  },
  {
    id: 3,
    name: 'Support Agent',
    description: 'Customer support and basic user management',
    icon: 'material-symbols:support-agent',
    color: 'bg-blue-500',
    isActive: true,
    userCount: 15,
    permissions: ['platform.support', 'platform.users.view']
  }
])

// Tenant roles data
const tenantRoles = ref([
  {
    id: 4,
    name: 'Tenant Admin',
    description: 'Full control within tenant scope',
    icon: 'material-symbols:business',
    color: 'bg-purple-500',
    isActive: true,
    userCount: 45,
    permissions: ['tenant.admin', 'tenant.users', 'tenant.documents', 'tenant.cases']
  },
  {
    id: 5,
    name: 'Lawyer',
    description: 'Legal professional with case management access',
    icon: 'material-symbols:gavel',
    color: 'bg-green-500',
    isActive: true,
    userCount: 234,
    permissions: ['tenant.cases', 'tenant.documents', 'tenant.clients']
  },
  {
    id: 6,
    name: 'Paralegal',
    description: 'Legal assistant with limited case access',
    icon: 'material-symbols:assignment',
    color: 'bg-teal-500',
    isActive: true,
    userCount: 156,
    permissions: ['tenant.cases.view', 'tenant.documents.view', 'tenant.clients.view']
  },
  {
    id: 7,
    name: 'Client',
    description: 'Client access to their own cases and documents',
    icon: 'material-symbols:person',
    color: 'bg-gray-500',
    isActive: true,
    userCount: 389,
    permissions: ['tenant.own.cases', 'tenant.own.documents']
  }
])

// All roles combined
const roles = computed(() => [...platformRoles.value, ...tenantRoles.value])

// Permissions data
const permissions = ref([
  // Platform permissions
  { id: 1, name: 'platform.admin', description: 'Full platform administration', type: 'platform' },
  { id: 2, name: 'platform.users', description: 'Manage platform users', type: 'platform' },
  { id: 3, name: 'platform.tenants', description: 'Manage tenants', type: 'platform' },
  { id: 4, name: 'platform.settings', description: 'Modify platform settings', type: 'platform' },
  { id: 5, name: 'platform.analytics', description: 'View platform analytics', type: 'platform' },
  { id: 6, name: 'platform.support', description: 'Access support tools', type: 'platform' },
  { id: 7, name: 'platform.users.view', description: 'View platform users', type: 'platform' },

  // Tenant permissions
  { id: 8, name: 'tenant.admin', description: 'Full tenant administration', type: 'tenant' },
  { id: 9, name: 'tenant.users', description: 'Manage tenant users', type: 'tenant' },
  { id: 10, name: 'tenant.documents', description: 'Manage all documents', type: 'tenant' },
  { id: 11, name: 'tenant.cases', description: 'Manage all cases', type: 'tenant' },
  { id: 12, name: 'tenant.clients', description: 'Manage all clients', type: 'tenant' },
  { id: 13, name: 'tenant.cases.view', description: 'View all cases', type: 'tenant' },
  { id: 14, name: 'tenant.documents.view', description: 'View all documents', type: 'tenant' },
  { id: 15, name: 'tenant.clients.view', description: 'View all clients', type: 'tenant' },
  { id: 16, name: 'tenant.own.cases', description: 'Access own cases', type: 'tenant' },
  { id: 17, name: 'tenant.own.documents', description: 'Access own documents', type: 'tenant' }
])

// Recent changes data
const recentChanges = ref([
  {
    id: 1,
    type: 'created',
    icon: 'material-symbols:add',
    description: 'New role "Legal Assistant" created with document access permissions',
    timestamp: '2 hours ago',
    user: 'John Admin'
  },
  {
    id: 2,
    type: 'modified',
    icon: 'material-symbols:edit',
    description: 'Updated "Paralegal" role permissions to include client management',
    timestamp: '1 day ago',
    user: 'Sarah Manager'
  },
  {
    id: 3,
    type: 'modified',
    icon: 'material-symbols:edit',
    description: 'Removed document deletion permission from "Client" role',
    timestamp: '2 days ago',
    user: 'Mike Admin'
  },
  {
    id: 4,
    type: 'created',
    icon: 'material-symbols:add',
    description: 'Created custom role "Billing Manager" for financial operations',
    timestamp: '3 days ago',
    user: 'Lisa Admin'
  }
])

// Computed properties
const activeRoles = computed(() => roles.value.filter(role => role.isActive).length)
const inactiveRoles = computed(() => roles.value.filter(role => !role.isActive).length)
const totalAssignedUsers = computed(() => roles.value.reduce((sum, role) => sum + role.userCount, 0))
const totalPermissions = computed(() => permissions.value.length)
const platformPermissions = computed(() => permissions.value.filter(p => p.type === 'platform').length)
const tenantPermissions = computed(() => permissions.value.filter(p => p.type === 'tenant').length)

const filteredRoles = computed(() => {
  switch (selectedRoleType.value) {
    case 'platform':
      return platformRoles.value
    case 'tenant':
      return tenantRoles.value
    default:
      return roles.value
  }
})

const filteredPermissions = computed(() => {
  switch (selectedRoleType.value) {
    case 'platform':
      return permissions.value.filter(p => p.type === 'platform')
    case 'tenant':
      return permissions.value.filter(p => p.type === 'tenant')
    default:
      return permissions.value
  }
})

// Methods
const hasPermission = (role: any, permission: any): boolean => {
  return role.permissions.includes(permission.name)
}

const canEditRole = (role: any): boolean => {
  // Super Admin can edit all roles, others can only edit tenant roles
  return role.id !== 1 // Cannot edit Super Admin role
}

const togglePermission = async (role: any, permission: any, value: boolean) => {
  try {
    if (value) {
      if (!role.permissions.includes(permission.name)) {
        role.permissions.push(permission.name)
      }
    } else {
      const index = role.permissions.indexOf(permission.name)
      if (index > -1) {
        role.permissions.splice(index, 1)
      }
    }

    // In a real app, you would save the changes to the API
    // await $api.put(`/roles/${role.id}/permissions`, { permissions: role.permissions })

    console.log(`Permission ${permission.name} ${value ? 'added to' : 'removed from'} role ${role.name}`)

  } catch (error) {
    console.error('Error updating role permissions:', error)
  }
}

const editRole = (role: any) => {
  console.log('Editing role:', role.name)
  // In a real app, you would open an edit modal or navigate to edit page
  // navigateTo(`/dashboard/users/roles/${role.id}/edit`)
}

const refreshPermissions = async () => {
  try {
    refreshingPermissions.value = true

    // Simulate API call to refresh permissions
    await new Promise(resolve => setTimeout(resolve, 1000))

    // In a real app, you would fetch fresh permissions data
    // const freshPermissions = await $api.get('/permissions')
    // permissions.value = freshPermissions

    console.log('Permissions refreshed')

  } catch (error) {
    console.error('Error refreshing permissions:', error)
  } finally {
    refreshingPermissions.value = false
  }
}

const exportRoles = async () => {
  try {
    // Create export data
    const exportData = {
      roles: roles.value,
      permissions: permissions.value,
      exportedAt: new Date().toISOString()
    }

    const jsonContent = JSON.stringify(exportData, null, 2)
    const blob = new Blob([jsonContent], { type: 'application/json' })
    const url = URL.createObjectURL(blob)

    const link = document.createElement('a')
    link.href = url
    link.download = `user-roles-export-${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    URL.revokeObjectURL(url)
    console.log('Roles exported successfully')

  } catch (error) {
    console.error('Error exporting roles:', error)
  }
}

const viewAuditLog = () => {
  navigateTo('/dashboard/platform/audit-logs?filter=roles')
}

// Lifecycle hooks
onMounted(async () => {
  // Initial data load
  isLoading.value = false
  console.log('User roles management loaded')
})
</script>

<style scoped>
/* Enhanced animations and transitions */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* Table styles */
table {
  border-collapse: collapse;
}

th, td {
  border-bottom: 1px solid rgb(229 231 235);
}

.dark th,
.dark td {
  border-bottom-color: rgb(55 65 81);
}

/* Card hover effects */
.hover\:bg-gray-100:hover {
  background-color: rgb(243 244 246);
}

.dark .hover\:bg-gray-600:hover {
  background-color: rgb(75 85 99);
}

.dark .hover\:bg-gray-700:hover {
  background-color: rgb(55 65 81);
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .grid-cols-1 {
    gap: 1rem;
  }

  .overflow-x-auto {
    -webkit-overflow-scrolling: touch;
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
</style>
