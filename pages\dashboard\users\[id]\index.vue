<template>
  <div class="space-y-6 py-6">
    <!-- Quick Stats -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <!-- Total Cases -->
      <UiCard hover-effect>
        <div class="flex items-center justify-between">
          <div class="p-3 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
            <Icon name="material-symbols:folder" class="h-6 w-6 text-blue-600 dark:text-blue-400" />
          </div>
          <div class="text-right">
            <p class="text-2xl font-bold text-gray-900 dark:text-white">
              {{ user?.caseStats?.total || 0 }}
            </p>
            <p class="text-sm text-gray-500 dark:text-gray-400">
              {{ $t("userDetail.totalCases") }}
            </p>
          </div>
        </div>
        <div class="mt-4 flex items-center text-sm">
          <Icon name="hugeicons:task-done-02" class="h-4 w-4 text-green-500 mr-1" />
          <span class="text-green-600 dark:text-green-400">{{ user?.caseStats?.closed || 0 }} {{ $t("userDetail.closed") }}</span>
        </div>
      </UiCard>

      <!-- Documents -->
      <UiCard hover-effect>
        <div class="flex items-center justify-between">
          <div class="p-3 bg-green-100 dark:bg-green-900/20 rounded-lg">
            <Icon name="material-symbols:description" class="h-6 w-6 text-green-600 dark:text-green-400" />
          </div>
          <div class="text-right">
            <p class="text-2xl font-bold text-gray-900 dark:text-white">
              {{ user?.documentStats?.total || 0 }}
            </p>
            <p class="text-sm text-gray-500 dark:text-gray-400">
              {{ $t("userDetail.documents") }}
            </p>
          </div>
        </div>
        <div class="mt-4 flex items-center text-sm">
          <Icon name="fluent:signed-16-regular" class="h-4 w-4 text-green-500 mr-1" />
          <span class="text-green-600 dark:text-green-400">{{ user?.documentStats?.signed || 0 }}</span>
        </div>
      </UiCard>

      <!-- Login Activity -->
      <UiCard hover-effect>
        <div class="flex items-center justify-between">
          <div class="p-3 bg-orange-100 dark:bg-orange-900/20 rounded-lg">
            <Icon name="material-symbols:login" class="h-6 w-6 text-orange-600 dark:text-orange-400" />
          </div>
          <div class="text-right">
            <p class="text-2xl font-bold text-gray-900 dark:text-white">
              {{ user?.loginStats?.thisMonth || 0 }}
            </p>
            <p class="text-sm text-gray-500 dark:text-gray-400">
              {{ $t("userDetail.loginsThisMonth") }}
            </p>
          </div>
        </div>
        <div class="mt-4 flex items-center text-sm">
          <Icon name="material-symbols:schedule" class="h-4 w-4 text-orange-500 mr-1" />
          <span class="text-orange-600 dark:text-orange-400">{{ user?.lastLoginAt ? fromNow(user.lastLoginAt) : $t("common.never") }}</span>
        </div>
      </UiCard>

      <!-- User Status -->
      <UiCard hover-effect>
        <div class="flex items-center justify-between">
          <div class="p-3 bg-purple-100 dark:bg-purple-900/20 rounded-lg">
            <Icon name="material-symbols:person-check" class="h-6 w-6 text-purple-600 dark:text-purple-400" />
          </div>
          <div class="text-right">
            <p class="text-2xl font-bold text-gray-900 dark:text-white">
              <UiBadge :color="user?.isActive ? 'success' : 'warning'">
                {{ user?.isActive ? $t("common.active") : $t("common.inactive") }}
              </UiBadge>
            </p>
            <p class="text-sm text-gray-500 dark:text-gray-400">
              {{ $t("userDetail.status") }}
            </p>
          </div>
        </div>
        <div class="mt-4 flex items-center text-sm">
          <Icon name="material-symbols:security" class="h-4 w-4 text-purple-500 mr-1" />
          <span class="text-purple-600 dark:text-purple-400">{{ user?.otpEnabled ? $t("userDetail.twoFactorEnabled") : $t("userDetail.twoFactorDisabled") }}</span>
        </div>
      </UiCard>
    </div>

    <!-- Main Content Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- Main Content -->
      <div class="lg:col-span-2 space-y-6">
        <!-- User Configuration -->
        <UiCard :title="$t('userDetail.configuration')" :subtitle="$t('userDetail.userSettingsDescription')"
          icon="material-symbols:person" icon-class="text-blue-600 dark:text-blue-400" class="space-y-4">
          <!-- actions -->
          <template #header>
            <div class="flex items-center justify-between">
              <UiButton variant="flat" size="sm" @click="editUser">
                <Icon name="material-symbols:edit" size="calc(var(--spacing) * 4)" />
              </UiButton>
            </div>
          </template>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="space-y-4">
              <div>
                <label class="text-sm font-medium text-gray-700 dark:text-gray-300">{{
                  $t("userDetail.fullName")
                }}</label>
                <p class="text-sm text-gray-900 dark:text-white mt-1">
                  {{ user?.name }}
                </p>
              </div>
              <div>
                <label class="text-sm font-medium text-gray-700 dark:text-gray-300">{{
                  $t("userDetail.email")
                }}</label>
                <p class="text-sm text-gray-900 dark:text-white mt-1">
                  {{ user?.email }}
                </p>
              </div>
              <div>
                <label class="text-sm font-medium text-gray-700 dark:text-gray-300">{{
                  $t("userDetail.title")
                }}</label>
                <p class="text-sm text-gray-900 dark:text-white mt-1">
                  {{ user?.title || $t("common.notSet") }}
                </p>
              </div>
            </div>
            <div class="space-y-4">
              <div>
                <label class="text-sm font-medium text-gray-700 dark:text-gray-300">{{
                  $t("userDetail.roles")
                }}</label>
                <div class="flex flex-wrap gap-2 mt-1">
                  <UiBadge v-for="role in user?.roles" :key="role" :color="getRoleVariant(role)" size="sm">
                    {{ role }}
                  </UiBadge>
                </div>
              </div>
              <div>
                <label class="text-sm font-medium text-gray-700 dark:text-gray-300">{{
                  $t("userDetail.tenant")
                }}</label>
                <p class="text-sm text-gray-900 dark:text-white mt-1">
                  {{ user?.tenantId ? getTenantName(user.tenantId) : $t("common.notAssigned") }}
                </p>
              </div>
              <div>
                <label class="text-sm font-medium text-gray-700 dark:text-gray-300">{{
                  $t("userDetail.status")
                }}</label>
                <div class="flex items-center gap-2 mt-1">
                  <UiBadge :color="user?.isActive ? 'success' : 'warning'">
                    {{ user?.isActive ? $t("common.active") : $t("common.inactive") }}
                  </UiBadge>
                  <UiButton variant="ghost" size="sm" @click="toggleUserStatus" :loading="isUpdatingStatus">
                    {{
                      user?.isActive
                        ? $t("userDetail.deactivate")
                        : $t("userDetail.activate")
                    }}
                  </UiButton>
                </div>
              </div>
            </div>
          </div>
        </UiCard>

        <!-- Recent Activity -->
        <UiCard content-height="300px" icon="material-symbols:history"
          :icon-props="{ color: 'success', background: 'success' }" :title="$t('userDetail.recentActivity')"
          :subtitle="$t('userDetail.latestUserActivities')">
          <template #header>
            <div class="flex items-center justify-between">
              <UiButton variant="ghost" size="sm" @click="viewAllActivity">
                {{ $t("userDetail.viewAll") }}
              </UiButton>
            </div>
          </template>
          <div class="space-y-4">
            <div v-for="activity in userActivity?.data || []" :key="activity.id"
              class="flex items-start gap-4 p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
              <div class="flex-shrink-0 mt-1">
                <div class="w-8 h-8 rounded-full flex items-center justify-center"
                  :class="getActivityIconBg(activity.activityType)">
                  <Icon :name="getActivityIcon(activity.activityType)" class="h-4 w-4"
                    :class="getActivityIconColor(activity.activityType)" />
                </div>
              </div>
              <div class="flex-1 min-w-0">
                <div class="flex items-center justify-between">
                  <p class="text-sm font-medium text-gray-900 dark:text-white">
                    {{ activity.activityType }}
                  </p>
                  <span class="text-xs text-gray-500 dark:text-gray-400">
                    {{ fromNow(activity.createdAt) }}
                  </span>
                </div>
                <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                  {{ activity.description }}
                </p>
                <div v-if="activity.case" class="flex items-center gap-1 mt-2 text-xs text-gray-500 dark:text-gray-400">
                  <Icon name="material-symbols:folder" class="h-3 w-3" />
                  <span>{{ activity.case.title }}</span>
                </div>
              </div>
            </div>
            <div v-if="userActivity?.data && userActivity?.data.length === 0"
              class="text-center py-8">
              <Icon name="material-symbols:history" class="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p class="text-sm text-gray-500 dark:text-gray-400">
                {{ $t("userDetail.noRecentActivity") }}
              </p>
            </div>
          </div>
        </UiCard>

      </div>

      <!-- Sidebar -->
      <div class="space-y-6">
        <!-- User Information -->
        <UiCard icon="material-symbols:info" :icon-props="{ color: 'info', background: 'info' }"
          :title="$t('userDetail.userInformation')" :subtitle="$t('userDetail.userSystemInformation')">
          <div class="space-y-4">
            <div>
              <label class="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">{{
                $t("userDetail.userId") }}</label>
              <div class="flex items-center gap-2 mt-1">
                <code class="text-sm bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded font-mono">
                  {{ user?.id }}
                </code>
                <UiButton variant="ghost" size="sm" shape="circle" @click="copyToClipboard(user?.id)"
                  :aria-label="$t('userDetail.copyUserId')">
                  <Icon name="material-symbols:content-copy" class="h-3 w-3" />
                </UiButton>
              </div>
            </div>
            <div>
              <label class="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">{{
                $t("userDetail.createdAt") }}</label>
              <p class="text-sm text-gray-900 dark:text-white mt-1">
                {{ $formatDate(user?.createdAt) }}
              </p>
            </div>
            <div>
              <label class="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">{{
                $t("userDetail.lastUpdated") }}</label>
              <p class="text-sm text-gray-900 dark:text-white mt-1">
                {{ $formatDate(user?.updatedAt) }}
              </p>
            </div>
            <div>
              <label class="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">{{
                $t("userDetail.lastLogin") }}</label>
              <p class="text-sm text-gray-900 dark:text-white mt-1">
                {{ user?.lastLoginAt ? $formatDate(user.lastLoginAt) : $t("common.never") }}
              </p>
            </div>
          </div>
        </UiCard>

        <!-- Security Settings -->
        <UiCard icon="material-symbols:security" :icon-props="{ color: 'warning', background: 'warning' }"
          :title="$t('userDetail.securitySettings')" :subtitle="$t('userDetail.userSecurityInformation')">
          <template #header>
            <div class="flex items-center justify-between">
              <UiButton variant="ghost" size="sm" @click="manageUserSecurity">
                {{ $t("userDetail.manage") }}
              </UiButton>
            </div>
          </template>
          <div class="space-y-4">
            <div class="flex items-center justify-between">
              <span class="text-sm text-gray-700 dark:text-gray-300">{{ $t("userDetail.twoFactorAuth") }}</span>
              <UiBadge :color="user?.otpEnabled ? 'success' : 'neutral'">
                {{ user?.otpEnabled ? $t("common.enabled") : $t("common.disabled") }}
              </UiBadge>
            </div>
            <div class="flex items-center justify-between">
              <span class="text-sm text-gray-700 dark:text-gray-300">{{ $t("userDetail.accountStatus") }}</span>
              <UiBadge :color="user?.isActive ? 'success' : 'error'">
                {{ user?.isActive ? $t("common.active") : $t("common.inactive") }}
              </UiBadge>
            </div>
            <div class="pt-3 border-t border-gray-200 dark:border-gray-700">
              <UiButton variant="outline" size="sm" @click="resetUserPassword" class="w-full mb-2">
                {{ $t("userDetail.resetPassword") }}
              </UiButton>
              <UiButton variant="outline" size="sm" @click="toggleTwoFactor" class="w-full">
                {{ user?.otpEnabled ? $t("userDetail.disableTwoFactor") : $t("userDetail.enableTwoFactor") }}
              </UiButton>
            </div>
          </div>
        </UiCard>

        <!-- Danger Zone -->
        <UiCard variant="outlined" icon="material-symbols:warning" :icon-props="{ color: 'error', background: 'none' }"
          :title="$t('userDetail.dangerZone')" :subtitle="$t('userDetail.irreversibleActions')"
          class="border-red-200 dark:border-red-800">
          <div class="space-y-3">
            <div class="p-3 bg-red-50 dark:bg-red-900/10 rounded-lg">
              <p class="text-sm text-red-800 dark:text-red-200 mb-3">
                {{ $t("userDetail.irreversibleActions") }}
              </p>
              <div class="space-y-2">
                <UiButton variant="outline" size="sm" @click="toggleUserStatus" :loading="isUpdatingStatus"
                  class="w-full border-red-300 text-red-700 hover:bg-red-50 dark:border-red-700 dark:text-red-300 dark:hover:bg-red-900/20">
                  {{
                    user?.isActive
                      ? $t("userDetail.deactivateUser")
                      : $t("userDetail.activateUser")
                  }}
                </UiButton>
                <UiButton variant="outline" size="sm" @click="showDeleteConfirmation = true"
                  class="w-full border-red-300 text-red-700 hover:bg-red-50 dark:border-red-700 dark:text-red-300 dark:hover:bg-red-900/20">
                  {{ $t("userDetail.deleteUser") }}
                </UiButton>
              </div>
            </div>
          </div>
        </UiCard>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useUserStore } from "~/stores/user";
import { useRoute } from "vue-router";
import { PlatformRoles, TenantRoles } from "~/app/features/auth/constants/roles";
import type { fromNow } from "utils/dateFormatters";

const userStore = useUserStore();
const route = useRoute();
const userId = computed(() => route.params.id as string);
const user = computed(() => userStore.selectedUser);

const Roles = { ...PlatformRoles, ...TenantRoles }

definePageMeta({
  layout: "dashboard",
  title: () => {
    const userStore = useUserStore();
    const user = userStore.selectedUser;
    if (!user) return "User Details";
    return user.name;
  },
  meta: () => ({ title: "User Details" }),

  showPageHeader: true,
  showPageHeaderTitle: true,
  pageHeaderItemStats: () => {
    const userStore = useUserStore();
    const user = userStore.selectedUser;
    if (!user) return '';

    return user.isActive ? { key: "active", label: "Active", color: "green" } : { key: "inactive", label: "Inactive", color: "red" };
  },
  pageHeaderLogoImage: () => {
    const userStore = useUserStore();
    const user = userStore.selectedUser;
    if (!user) return "";
    return user.avatarUrl || "";
  },
  pageHeaderTags: () => {
    const userStore = useUserStore();
    const user = userStore.selectedUser;
    if (!user) return [];
    return [
      {
        key: "roles",
        label: user.roles.join(", "),
        class: "text-purple-600",
        icon: "i-material-symbols:admin-panel-settings",
      },
      {
        key: "tenant",
        label: user?.tenant ? user.tenant.name  : "No Tenant",
        class: "text-brandSecondary",
        icon: "i-heroicons:building-office",
      },
      {
        key: "lastLogin",
        label: user.lastLoginAt ? "Recently Active" : "Never Logged In",
        class: "text-brandPrimary",
        icon: "i-heroicons:clock",
      },
    ];
  },
  pageHeaderActions: () => {
    const { $global } = useNuxtApp()
    const userId = useRoute().params.id as string;
    if (!userId) return [];
    return [
      {
        label: "Edit User",
        icon: "heroicons:pencil-square",
        color: "outline",
        click: () => navigateTo(`/dashboard/users/${userId}/edit`),
      },
      {
        label: "View Activity",
        icon: "i-heroicons:chart-bar",
        color: "success",
        click: () => navigateTo(`/dashboard/users/${userId}/activity`),
      },
      {
        label: "Refresh",
        icon: "i-mdi:refresh",
        color: "secondary",
        click: () => $global.get('refreshData')(),
      },
    ].reverse();
  },

  showActionsMenu: true,

  middleware: ["rbac"],
  roles: [PlatformRoles.SUPER_ADMIN],
  breadcrumbs: () => {
    const userStore = useUserStore();
    const user = userStore.selectedUser;
    if (!user) return [];
    return [
      { label: "Dashboard", href: "/dashboard" },
      { label: "Users", href: "/dashboard/users" },
      { label: user.name, href: "" }
    ];
  }
});

const editUser = () => {
  navigateTo(`/dashboard/users/${userId.value}/edit`);
};

const isUpdatingStatus = ref(false);
const toggleUserStatus = async () => {
  if (!user.value) return;

  isUpdatingStatus.value = true;
  try {
    await userStore.updateUser(userId.value, { isActive: !user.value.isActive });
    console.log(`User ${user.value.isActive ? "activated" : "deactivated"}`);
  } catch (error) {
    console.error("Error updating user status:", error);
  } finally {
    isUpdatingStatus.value = false;
  }
};

const viewAllActivity = () => {
  navigateTo(`/dashboard/users/${userId.value}/activity`);
};

const showDeleteConfirmation = ref(false);

const getActivityIconBg = (type: string) => {
  const backgrounds: Record<string, string> = {
    CREATE_CASE: "bg-blue-100 dark:bg-blue-900/20",
    LOGIN: "bg-green-100 dark:bg-green-900/20",
    UPLOAD_DOCUMENT: "bg-purple-100 dark:bg-purple-900/20",
    UPDATE_CASE: "bg-orange-100 dark:bg-orange-900/20",
    LOGOUT: "bg-gray-100 dark:bg-gray-900/20",
  };
  return backgrounds[type] || "bg-gray-100 dark:bg-gray-900/20";
};

const getActivityIcon = (type: string) => {
  const icons: Record<string, string> = {
    CREATE_CASE: "material-symbols:task-outlined",
    LOGIN: "material-symbols:login",
    UPLOAD_DOCUMENT: "material-symbols:upload",
    UPDATE_CASE: "material-symbols:edit",
    LOGOUT: "material-symbols:logout",
  };
  return icons[type] || "material-symbols:help";
};

const getActivityIconColor = (type: string) => {
  const colors: Record<string, string> = {
    CREATE_CASE: "text-blue-600 dark:text-blue-400",
    LOGIN: "text-green-600 dark:text-green-400",
    UPLOAD_DOCUMENT: "text-purple-600 dark:text-purple-400",
    UPDATE_CASE: "text-orange-600 dark:text-orange-400",
    LOGOUT: "text-gray-600 dark:text-gray-400",
  };
  return colors[type] || "text-gray-600 dark:text-gray-400";
};

const userCases = ref([])
const userActivity = ref({ data: [] })

const manageCases = () => {
  navigateTo(`/dashboard/users/${userId.value}/cases`);
};

const createCase = () => {
  navigateTo(`/dashboard/cases/create?userId=${userId.value}`);
};

const getRoleVariant = (role: string) => {
  const variants: Record<string, string> = {
    [Roles.ADMIN]: 'primary',
    [Roles.LAWYER]: 'success',
    [Roles.PARALEGAL]: 'warning',
    [Roles.CLIENT]: 'neutral',
    [Roles.SUPER_ADMIN]: 'error',
  }
  return variants[role] || 'neutral'
}

const getCaseStatusVariant = (status: string) => {
  const variants: Record<string, string> = {
    'open': 'primary',
    'in_progress': 'warning',
    'closed': 'success',
    'archived': 'neutral',
  }
  return variants[status] || 'neutral'
}

const getTenantName = (tenantId: string) => {
  // This would typically fetch from a tenant store or API
  return tenantId; // Placeholder
};

const manageUserSecurity = () => {
  navigateTo(`/dashboard/users/${userId.value}/security`);
};

const resetUserPassword = async () => {
  if (!user.value) return;

  try {
    // This would call an API to reset password
    console.log('Resetting password for user:', user.value.id);
    // Show success notification
  } catch (error) {
    console.error('Error resetting password:', error);
  }
};

const toggleTwoFactor = async () => {
  if (!user.value) return;

  try {
    await userStore.updateUser(userId.value, { otpEnabled: !user.value.otpEnabled });
    console.log(`Two-factor ${user.value.otpEnabled ? 'disabled' : 'enabled'} for user`);
  } catch (error) {
    console.error('Error toggling two-factor:', error);
  }
};

const copyToClipboard = async (text: string | undefined) => {
  if (!text) return;

  try {
    await navigator.clipboard.writeText(text);
    // Show success notification
  } catch (error) {
    console.error('Error copying to clipboard:', error);
  }
};

const fetchUserData = async () => {

    try {
      await userStore.fetchUserById(userId.value);
    } catch (error) {
      console.error('Error fetching user data:', error);
    }
  };


const { $global } = useNuxtApp()
onMounted(async () => {
  fetchUserData()
  $global.add('refreshData', fetchUserData)
});

onUnmounted(() => {
  $global.remove('refreshData', fetchUserData)
});
</script>