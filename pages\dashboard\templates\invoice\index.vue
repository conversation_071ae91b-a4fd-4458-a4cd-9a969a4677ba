<template>
  <div class="space-y-8">
    <!-- Invoice Templates Header -->
    <div class="bg-gradient-to-r from-green-600 to-emerald-600 rounded-xl p-8 text-white">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-3xl font-bold mb-2">Invoice Templates</h1>
          <p class="text-green-100 text-lg">
            Create and manage professional invoice templates for billing and payments
          </p>
        </div>
        <div class="hidden md:flex items-center gap-4">
          <UiButton @click="navigateTo('/dashboard/templates/invoice/create')" class="bg-white/10 border-white/20 text-white hover:bg-white/20">
            <Icon name="material-symbols:add" class="h-4 w-4 mr-2" />
            Create Template
          </UiButton>
          <UiButton @click="previewTemplate" variant="outline" class="border-white/20 text-white hover:bg-white/10">
            <Icon name="material-symbols:preview" class="h-4 w-4 mr-2" />
            Preview
          </UiButton>
        </div>
      </div>
    </div>

    <!-- Templates Overview -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <!-- Total Templates -->
      <UiCard
        icon="material-symbols:receipt-long"
        icon-color="green"
        :loading="isLoading"
        hover-effect
      >
        <template #header>
          <div class="flex items-center justify-between w-full">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Total Templates</h3>
            <UiBadge variant="info">{{ invoiceTemplates.length }}</UiBadge>
          </div>
        </template>
        <div class="space-y-3">
          <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ invoiceTemplates.length }}</p>
          <p class="text-sm text-gray-500 dark:text-gray-400">
            {{ activeTemplates }} active, {{ inactiveTemplates }} inactive
          </p>
          <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div 
              class="bg-green-600 h-2 rounded-full transition-all duration-300"
              :style="{ width: `${(activeTemplates / invoiceTemplates.length) * 100}%` }"
            ></div>
          </div>
        </div>
      </UiCard>

      <!-- Generated This Month -->
      <UiCard
        icon="material-symbols:trending-up"
        icon-color="blue"
        :loading="isLoading"
        hover-effect
      >
        <template #header>
          <div class="flex items-center justify-between w-full">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Generated This Month</h3>
            <UiBadge variant="success">+{{ generationGrowth }}%</UiBadge>
          </div>
        </template>
        <div class="space-y-3">
          <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ monthlyGenerated }}</p>
          <p class="text-sm text-gray-500 dark:text-gray-400">
            invoices created
          </p>
          <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div 
              class="bg-blue-600 h-2 rounded-full transition-all duration-300"
              :style="{ width: `${Math.min((monthlyGenerated / monthlyTarget) * 100, 100)}%` }"
            ></div>
          </div>
        </div>
      </UiCard>

      <!-- Total Revenue -->
      <UiCard
        icon="material-symbols:attach-money"
        icon-color="yellow"
        :loading="isLoading"
        hover-effect
      >
        <template #header>
          <div class="flex items-center justify-between w-full">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Total Revenue</h3>
            <UiBadge variant="success">${{ formatCurrency(monthlyRevenue) }}</UiBadge>
          </div>
        </template>
        <div class="space-y-3">
          <p class="text-3xl font-bold text-gray-900 dark:text-white">${{ formatCurrency(monthlyRevenue) }}</p>
          <p class="text-sm text-gray-500 dark:text-gray-400">
            from generated invoices
          </p>
          <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div 
              class="bg-yellow-600 h-2 rounded-full transition-all duration-300"
              :style="{ width: `${Math.min((monthlyRevenue / revenueTarget) * 100, 100)}%` }"
            ></div>
          </div>
        </div>
      </UiCard>

      <!-- Payment Rate -->
      <UiCard
        icon="material-symbols:payments"
        icon-color="purple"
        :loading="isLoading"
        hover-effect
      >
        <template #header>
          <div class="flex items-center justify-between w-full">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Payment Rate</h3>
            <UiBadge :variant="paymentRate >= 90 ? 'success' : paymentRate >= 80 ? 'warning' : 'error'">
              {{ paymentRate }}%
            </UiBadge>
          </div>
        </template>
        <div class="space-y-3">
          <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ paymentRate }}%</p>
          <p class="text-sm text-gray-500 dark:text-gray-400">
            invoices paid on time
          </p>
          <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div 
              :class="[
                'h-2 rounded-full transition-all duration-300',
                paymentRate >= 90 ? 'bg-green-600' :
                paymentRate >= 80 ? 'bg-yellow-600' : 'bg-red-600'
              ]"
              :style="{ width: `${paymentRate}%` }"
            ></div>
          </div>
        </div>
      </UiCard>
    </div>

    <!-- Template Categories -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <div
        v-for="category in templateCategories"
        :key="category.id"
        class="space-y-4"
      >
        <UiCard>
          <template #header>
            <div class="flex items-center justify-between">
              <div class="flex items-center gap-3">
                <div :class="[
                  'w-10 h-10 rounded-full flex items-center justify-center text-white',
                  category.color
                ]">
                  <Icon :name="category.icon" class="h-5 w-5" />
                </div>
                <div>
                  <h3 class="text-lg font-semibold text-gray-900 dark:text-white">{{ category.name }}</h3>
                  <p class="text-sm text-gray-500 dark:text-gray-400">{{ category.description }}</p>
                </div>
              </div>
              <UiBadge variant="info">{{ getTemplatesByCategory(category.slug).length }}</UiBadge>
            </div>
          </template>
          <div class="space-y-3">
            <div
              v-for="template in getTemplatesByCategory(category.slug).slice(0, 3)"
              :key="template.id"
              class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors duration-200 cursor-pointer"
              @click="viewTemplate(template)"
            >
              <div class="flex items-center gap-3">
                <div class="w-8 h-8 bg-gray-200 dark:bg-gray-600 rounded flex items-center justify-center">
                  <Icon name="material-symbols:receipt" class="h-4 w-4 text-gray-500 dark:text-gray-400" />
                </div>
                <div>
                  <p class="text-sm font-medium text-gray-900 dark:text-white">{{ template.name }}</p>
                  <p class="text-xs text-gray-500 dark:text-gray-400">{{ template.usage }} uses</p>
                </div>
              </div>
              <div class="flex items-center gap-2">
                <UiBadge :variant="template.isActive ? 'success' : 'neutral'" size="sm">
                  {{ template.isActive ? 'Active' : 'Inactive' }}
                </UiBadge>
                <UiButton @click.stop="editTemplate(template)" variant="ghost" size="sm">
                  <Icon name="material-symbols:edit" class="h-4 w-4" />
                </UiButton>
              </div>
            </div>
            
            <UiButton 
              v-if="getTemplatesByCategory(category.slug).length > 3"
              @click="viewCategoryTemplates(category)"
              variant="outline" 
              size="sm" 
              class="w-full"
            >
              View All {{ getTemplatesByCategory(category.slug).length }} Templates
            </UiButton>
            
            <UiButton 
              @click="createTemplate(category)"
              variant="outline" 
              size="sm" 
              class="w-full"
            >
              <Icon name="material-symbols:add" class="h-4 w-4 mr-2" />
              Create {{ category.name }} Template
            </UiButton>
          </div>
        </UiCard>
      </div>
    </div>

    <!-- Popular Templates -->
    <UiCard>
      <template #header>
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Most Used Templates</h3>
          <UiButton @click="viewAllTemplates" variant="ghost" size="sm">
            View All
          </UiButton>
        </div>
      </template>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <div
          v-for="template in popularTemplates"
          :key="template.id"
          class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors duration-200 cursor-pointer"
          @click="viewTemplate(template)"
        >
          <div class="flex items-center gap-3">
            <div :class="[
              'w-12 h-12 rounded-lg flex items-center justify-center text-white',
              template.category.color
            ]">
              <Icon :name="template.category.icon" class="h-6 w-6" />
            </div>
            <div>
              <p class="text-sm font-medium text-gray-900 dark:text-white">{{ template.name }}</p>
              <p class="text-xs text-gray-500 dark:text-gray-400">{{ template.category.name }}</p>
              <p class="text-xs text-gray-500 dark:text-gray-400">${{ formatCurrency(template.revenue) }} revenue</p>
            </div>
          </div>
          <div class="flex items-center gap-2">
            <UiBadge variant="success" size="sm">Popular</UiBadge>
            <UiButton @click.stop="generateInvoice(template)" variant="outline" size="sm">
              <Icon name="material-symbols:play-arrow" class="h-4 w-4 mr-1" />
              Use
            </UiButton>
          </div>
        </div>
      </div>
    </UiCard>

    <!-- Invoice Analytics -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Payment Status -->
      <UiCard>
        <template #header>
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Payment Status</h3>
            <UiButton @click="refreshStats" variant="ghost" size="sm" :loading="refreshingStats">
              <Icon name="material-symbols:refresh" class="h-4 w-4" />
            </UiButton>
          </div>
        </template>
        <div class="space-y-4">
          <div class="space-y-2">
            <div class="flex justify-between text-sm">
              <span class="text-gray-500 dark:text-gray-400">Paid</span>
              <span class="text-gray-900 dark:text-white">{{ paymentStats.paid }}</span>
            </div>
            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div class="bg-green-600 h-2 rounded-full" :style="{ width: `${(paymentStats.paid / paymentStats.total) * 100}%` }"></div>
            </div>
          </div>
          
          <div class="space-y-2">
            <div class="flex justify-between text-sm">
              <span class="text-gray-500 dark:text-gray-400">Pending</span>
              <span class="text-gray-900 dark:text-white">{{ paymentStats.pending }}</span>
            </div>
            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div class="bg-yellow-600 h-2 rounded-full" :style="{ width: `${(paymentStats.pending / paymentStats.total) * 100}%` }"></div>
            </div>
          </div>
          
          <div class="space-y-2">
            <div class="flex justify-between text-sm">
              <span class="text-gray-500 dark:text-gray-400">Overdue</span>
              <span class="text-gray-900 dark:text-white">{{ paymentStats.overdue }}</span>
            </div>
            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div class="bg-red-600 h-2 rounded-full" :style="{ width: `${(paymentStats.overdue / paymentStats.total) * 100}%` }"></div>
            </div>
          </div>
        </div>
      </UiCard>

      <!-- Recent Activity -->
      <UiCard>
        <template #header>
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Recent Activity</h3>
            <UiButton @click="viewActivityLog" variant="ghost" size="sm">
              View Full Log
            </UiButton>
          </div>
        </template>
        <div class="space-y-3">
          <div
            v-for="activity in recentActivity"
            :key="activity.id"
            class="flex items-start gap-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg"
          >
            <div class="flex-shrink-0">
              <div :class="[
                'w-8 h-8 rounded-full flex items-center justify-center',
                activity.type === 'generated' ? 'bg-green-100 dark:bg-green-900/20' :
                activity.type === 'paid' ? 'bg-blue-100 dark:bg-blue-900/20' :
                activity.type === 'created' ? 'bg-purple-100 dark:bg-purple-900/20' :
                'bg-gray-100 dark:bg-gray-900/20'
              ]">
                <Icon :name="activity.icon" :class="[
                  'h-4 w-4',
                  activity.type === 'generated' ? 'text-green-600 dark:text-green-400' :
                  activity.type === 'paid' ? 'text-blue-600 dark:text-blue-400' :
                  activity.type === 'created' ? 'text-purple-600 dark:text-purple-400' :
                  'text-gray-600 dark:text-gray-400'
                ]" />
              </div>
            </div>
            <div class="flex-1 min-w-0">
              <p class="text-sm text-gray-900 dark:text-white">{{ activity.description }}</p>
              <div class="flex items-center gap-2 mt-1">
                <p class="text-xs text-gray-500 dark:text-gray-400">{{ activity.timestamp }}</p>
                <span class="text-xs text-gray-400">•</span>
                <p class="text-xs text-gray-500 dark:text-gray-400">{{ activity.user }}</p>
              </div>
            </div>
          </div>

          <div v-if="recentActivity.length === 0" class="text-center py-6">
            <Icon name="material-symbols:receipt" class="h-8 w-8 text-gray-400 mx-auto mb-2" />
            <p class="text-sm text-gray-500 dark:text-gray-400">No recent activity</p>
          </div>
        </div>
      </UiCard>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { PlatformRoles } from '~/app/features/auth/constants/roles'

// Page meta configuration
definePageMeta({
  layout: 'dashboard',
  title: 'Invoice Templates',
  description: 'Create and manage professional invoice templates for billing and payments',
  pageHeaderIcon: 'material-symbols:receipt-long',
  pageHeaderStats: [
    { key: 'templates', label: 'Total Templates', value: '15', color: 'green' },
    { key: 'generated', label: 'Generated', value: '1,456', color: 'blue' },
    { key: 'revenue', label: 'Revenue', value: '$87.5K', color: 'yellow' },
    { key: 'payment', label: 'Payment Rate', value: '92.3%', color: 'purple' }
  ],
  showRealTimeStatus: false,
  autoRefreshEnabled: false,
  middleware: ['rbac'],
  roles: [PlatformRoles.SUPER_ADMIN],
  breadcrumb: [
    { label: 'Dashboard', href: '/dashboard' },
    { label: 'Templates', href: '/dashboard/templates' },
    { label: 'Invoice Templates' },
  ],
})

// Reactive state
const isLoading = ref(true)
const refreshingStats = ref(false)
const monthlyGenerated = ref(1456)
const monthlyTarget = ref(1800)
const generationGrowth = ref(12.4)
const monthlyRevenue = ref(87500)
const revenueTarget = ref(100000)
const paymentRate = ref(92.3)

// Template categories
const templateCategories = ref([
  {
    id: 1,
    name: 'Legal Services',
    slug: 'legal-services',
    description: 'Standard legal service invoices',
    icon: 'material-symbols:gavel',
    color: 'bg-blue-500'
  },
  {
    id: 2,
    name: 'Consultation',
    slug: 'consultation',
    description: 'Consultation and advisory invoices',
    icon: 'material-symbols:chat',
    color: 'bg-green-500'
  },
  {
    id: 3,
    name: 'Retainer',
    slug: 'retainer',
    description: 'Retainer fee invoices',
    icon: 'material-symbols:account-balance',
    color: 'bg-purple-500'
  },
  {
    id: 4,
    name: 'Court Fees',
    slug: 'court-fees',
    description: 'Court filing and legal fees',
    icon: 'material-symbols:balance',
    color: 'bg-yellow-500'
  },
  {
    id: 5,
    name: 'Expenses',
    slug: 'expenses',
    description: 'Expense reimbursement invoices',
    icon: 'material-symbols:receipt',
    color: 'bg-red-500'
  }
])

// Invoice templates data
const invoiceTemplates = ref([
  // Legal Services
  { id: 1, name: 'Hourly Legal Services', category: 'legal-services', isActive: true, usage: 234, revenue: 45600, categoryObj: templateCategories.value[0] },
  { id: 2, name: 'Contract Review', category: 'legal-services', isActive: true, usage: 156, revenue: 31200, categoryObj: templateCategories.value[0] },
  { id: 3, name: 'Document Preparation', category: 'legal-services', isActive: true, usage: 189, revenue: 28350, categoryObj: templateCategories.value[0] },

  // Consultation
  { id: 4, name: 'Initial Consultation', category: 'consultation', isActive: true, usage: 345, revenue: 17250, categoryObj: templateCategories.value[1] },
  { id: 5, name: 'Follow-up Consultation', category: 'consultation', isActive: true, usage: 123, revenue: 9225, categoryObj: templateCategories.value[1] },
  { id: 6, name: 'Expert Opinion', category: 'consultation', isActive: true, usage: 67, revenue: 13400, categoryObj: templateCategories.value[1] },

  // Retainer
  { id: 7, name: 'Monthly Retainer', category: 'retainer', isActive: true, usage: 89, revenue: 44500, categoryObj: templateCategories.value[2] },
  { id: 8, name: 'Annual Retainer', category: 'retainer', isActive: true, usage: 23, revenue: 23000, categoryObj: templateCategories.value[2] },

  // Court Fees
  { id: 9, name: 'Filing Fees', category: 'court-fees', isActive: true, usage: 78, revenue: 7800, categoryObj: templateCategories.value[3] },
  { id: 10, name: 'Court Appearance', category: 'court-fees', isActive: true, usage: 45, revenue: 9000, categoryObj: templateCategories.value[3] },

  // Expenses
  { id: 11, name: 'Travel Expenses', category: 'expenses', isActive: true, usage: 56, revenue: 2800, categoryObj: templateCategories.value[4] },
  { id: 12, name: 'Research Costs', category: 'expenses', isActive: true, usage: 34, revenue: 1700, categoryObj: templateCategories.value[4] },
  { id: 13, name: 'Administrative Fees', category: 'expenses', isActive: true, usage: 67, revenue: 3350, categoryObj: templateCategories.value[4] }
])

// Payment statistics
const paymentStats = reactive({
  total: 1456,
  paid: 1344,
  pending: 89,
  overdue: 23
})

// Recent activity data
const recentActivity = ref([
  {
    id: 1,
    type: 'generated',
    icon: 'material-symbols:receipt',
    description: 'Generated 15 invoices using "Hourly Legal Services" template',
    timestamp: '2 hours ago',
    user: 'System'
  },
  {
    id: 2,
    type: 'paid',
    icon: 'material-symbols:payments',
    description: 'Invoice #INV-2024-0156 paid ($2,500)',
    timestamp: '4 hours ago',
    user: 'Client Portal'
  },
  {
    id: 3,
    type: 'created',
    icon: 'material-symbols:add',
    description: 'New "Mediation Services" invoice template created',
    timestamp: '1 day ago',
    user: 'Sarah Admin'
  },
  {
    id: 4,
    type: 'generated',
    icon: 'material-symbols:receipt',
    description: 'Generated monthly retainer invoices for 23 clients',
    timestamp: '2 days ago',
    user: 'System'
  }
])

// Computed properties
const activeTemplates = computed(() => invoiceTemplates.value.filter(t => t.isActive).length)
const inactiveTemplates = computed(() => invoiceTemplates.value.filter(t => !t.isActive).length)

const popularTemplates = computed(() =>
  invoiceTemplates.value
    .sort((a, b) => b.revenue - a.revenue)
    .slice(0, 6)
    .map(template => ({
      ...template,
      category: templateCategories.value.find(cat => cat.slug === template.category) || templateCategories.value[0]
    }))
)

// Utility functions
const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('en-US').format(amount)
}

// Methods
const getTemplatesByCategory = (categorySlug: string) => {
  return invoiceTemplates.value.filter(template => template.category === categorySlug)
}

const viewTemplate = (template: any) => {
  navigateTo(`/dashboard/templates/${template.id}`)
}

const editTemplate = (template: any) => {
  navigateTo(`/dashboard/templates/${template.id}/edit`)
}

const generateInvoice = (template: any) => {
  navigateTo(`/dashboard/invoices/create?template=${template.id}`)
}

const createTemplate = (category: any) => {
  navigateTo(`/dashboard/templates/create/invoice?category=${category.slug}`)
}

const viewCategoryTemplates = (category: any) => {
  navigateTo(`/dashboard/templates?category=${category.slug}&type=invoice`)
}

const viewAllTemplates = () => {
  navigateTo('/dashboard/templates?type=invoice')
}

const viewActivityLog = () => {
  navigateTo('/dashboard/templates/activity?type=invoice')
}

const previewTemplate = async () => {
  try {
    console.log('Opening template preview...')

    // In a real app, you would open a modal to select template for preview
    const templateId = prompt('Enter template ID to preview:')

    if (templateId) {
      // Open preview in new window/tab
      window.open(`/dashboard/templates/${templateId}/preview`, '_blank')
    }

  } catch (error) {
    console.error('Error opening template preview:', error)
  }
}

const refreshStats = async () => {
  try {
    refreshingStats.value = true

    // Simulate API call to refresh statistics
    await new Promise(resolve => setTimeout(resolve, 1000))

    // In a real app, you would fetch fresh stats
    // const stats = await $api.get('/invoices/stats')

    // Simulate some updates
    paymentStats.paid = Math.max(1200, paymentStats.paid + Math.floor((Math.random() - 0.5) * 20))
    paymentStats.pending = Math.max(50, paymentStats.pending + Math.floor((Math.random() - 0.5) * 10))
    paymentStats.overdue = Math.max(10, paymentStats.overdue + Math.floor((Math.random() - 0.5) * 5))
    paymentStats.total = paymentStats.paid + paymentStats.pending + paymentStats.overdue

    console.log('Invoice statistics refreshed')

  } catch (error) {
    console.error('Error refreshing statistics:', error)
  } finally {
    refreshingStats.value = false
  }
}

// Lifecycle hooks
onMounted(async () => {
  // Initial data load
  isLoading.value = false
  console.log('Invoice templates loaded')
})
</script>

<style scoped>
/* Enhanced animations and transitions */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* Card hover effects */
.hover\:bg-gray-100:hover {
  background-color: rgb(243 244 246);
}

.dark .hover\:bg-gray-600:hover {
  background-color: rgb(75 85 99);
}

/* Progress bar animations */
.transition-all {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .grid-cols-1 {
    gap: 1rem;
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
</style>
