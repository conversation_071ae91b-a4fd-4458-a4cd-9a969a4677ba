<template>
    <!-- Invite Layout - Specialized for invitation flows -->
    <div class="min-h-screen flex relative overflow-hidden bg-gray-50 dark:bg-gray-900">
        <!-- Left Side - Invitation Branding -->
        <div class="hidden lg:flex lg:w-1/2 bg-gradient-to-br from-blue-600 via-blue-700 to-indigo-800 relative overflow-hidden">
            <!-- Animated Background Pattern -->
            <div class="absolute inset-0 opacity-10">
                <div class="absolute inset-0 animate-pulse" style="background-image: radial-gradient(circle at 1px 1px, rgba(255, 255, 255, 0.3) 1px, transparent 0); background-size: 30px 30px;"></div>
            </div>
            
            <!-- Floating Elements -->
            <div class="absolute top-20 left-20 w-32 h-32 bg-white/10 rounded-full animate-float"></div>
            <div class="absolute bottom-32 right-16 w-24 h-24 bg-white/5 rounded-full animate-float-delayed"></div>
            <div class="absolute top-1/2 left-1/3 w-16 h-16 bg-white/15 rounded-full animate-bounce-slow"></div>
            
            <!-- Content Container -->
            <div class="relative z-10 flex flex-col justify-center px-12 py-16 text-white">
                <!-- Logo -->
                <div class="mb-12 animate-fade-in">
                    <div class="inline-flex items-center justify-center w-20 h-20 bg-white/20 backdrop-blur-sm rounded-3xl mb-6 shadow-2xl">
                        <img class="h-10 w-auto" src="/logos/legal-saas-logo-white.svg" alt="Legal SaaS Platform" onerror="this.src='/logos/legal-saas-logo-full.svg'" />
                    </div>
                    <h1 class="text-4xl font-bold mb-4 leading-tight">
                        Join Our Platform
                    </h1>
                    <p class="text-xl text-blue-100 leading-relaxed max-w-md">
                        You've been invited to join a powerful legal practice management platform.
                    </p>
                </div>
                
                <!-- Features List -->
                <div class="space-y-6 animate-fade-in-up">
                    <div class="flex items-start space-x-4">
                        <div class="flex-shrink-0 w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center">
                            <Icon name="material-symbols:security" class="h-4 w-4 text-white" />
                        </div>
                        <div>
                            <h3 class="font-semibold text-lg">Secure & Compliant</h3>
                            <p class="text-blue-100 text-sm">Enterprise-grade security with full compliance support</p>
                        </div>
                    </div>
                    
                    <div class="flex items-start space-x-4">
                        <div class="flex-shrink-0 w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center">
                            <Icon name="material-symbols:group" class="h-4 w-4 text-white" />
                        </div>
                        <div>
                            <h3 class="font-semibold text-lg">Team Collaboration</h3>
                            <p class="text-blue-100 text-sm">Work seamlessly with your legal team and clients</p>
                        </div>
                    </div>
                    
                    <div class="flex items-start space-x-4">
                        <div class="flex-shrink-0 w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center">
                            <Icon name="material-symbols:analytics" class="h-4 w-4 text-white" />
                        </div>
                        <div>
                            <h3 class="font-semibold text-lg">Advanced Analytics</h3>
                            <p class="text-blue-100 text-sm">Insights and reporting to optimize your practice</p>
                        </div>
                    </div>
                </div>
                
                <!-- Trust Indicators -->
                <div class="mt-12 pt-8 border-t border-white/20 animate-fade-in-up">
                    <p class="text-sm text-blue-100 mb-4">Trusted by legal professionals worldwide</p>
                    <div class="flex items-center space-x-6">
                        <div class="flex items-center space-x-2">
                            <Icon name="material-symbols:verified" class="h-5 w-5 text-green-400" />
                            <span class="text-sm">SOC 2 Certified</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <Icon name="material-symbols:shield-lock" class="h-5 w-5 text-green-400" />
                            <span class="text-sm">GDPR Compliant</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Right Side - Form Container -->
        <div class="flex-1 flex flex-col justify-center px-6 py-12 lg:px-16 xl:px-20">
            <!-- Mobile Header -->
            <div class="lg:hidden text-center mb-8 animate-fade-in-scale">
                <div class="inline-flex items-center justify-center w-16 h-16 bg-blue-600 rounded-2xl mb-4 shadow-lg">
                    <img class="h-8 w-auto" src="/logos/legal-saas-logo-white.svg" alt="Legal SaaS Platform" onerror="this.src='/logos/legal-saas-logo-full.svg'" />
                </div>
                <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-2">Join Our Platform</h2>
                <p class="text-gray-600 dark:text-gray-400">Complete your invitation to get started</p>
            </div>
            
            <!-- Progress Indicator (if needed) -->
            <div v-if="showProgress" class="mb-8 animate-fade-in">
                <div class="flex items-center justify-center space-x-2">
                    <div class="flex items-center space-x-2">
                        <div class="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium">
                            1
                        </div>
                        <span class="text-sm font-medium text-blue-600">Verify Invitation</span>
                    </div>
                    <div class="w-16 h-0.5 bg-gray-300 dark:bg-gray-600"></div>
                    <div class="flex items-center space-x-2">
                        <div class="w-8 h-8 bg-gray-300 dark:bg-gray-600 text-gray-500 rounded-full flex items-center justify-center text-sm font-medium">
                            2
                        </div>
                        <span class="text-sm text-gray-500">Complete Setup</span>
                    </div>
                </div>
            </div>
            
            <!-- Loading Overlay -->
            <div v-if="isLoading" class="loading-overlay">
                <div class="flex flex-col items-center space-y-4">
                    <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
                    <p class="text-sm text-gray-600 dark:text-gray-400">Processing your invitation...</p>
                </div>
            </div>
            
            <!-- Form Container -->
            <div class="relative z-10 mx-auto w-full max-w-md">
                <!-- Success Message -->
                <Transition name="success-slide">
                    <div v-if="successMessage"
                         class="mb-6 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 text-green-700 dark:text-green-400 px-4 py-3 rounded-xl relative shadow-sm animate-bounce-in"
                         role="alert">
                        <div class="flex items-start">
                            <Icon name="material-symbols:check-circle" class="h-5 w-5 text-green-500 mt-0.5 mr-3 flex-shrink-0" />
                            <div class="flex-1">
                                <strong class="font-semibold">Success!</strong>
                                <p class="text-sm mt-1">{{ successMessage }}</p>
                            </div>
                            <button @click="clearSuccess"
                                    class="ml-3 flex-shrink-0 p-1 rounded-lg hover:bg-green-100 dark:hover:bg-green-800 transition-colors duration-200">
                                <Icon name="material-symbols:close" class="h-4 w-4 text-green-500" />
                            </button>
                        </div>
                    </div>
                </Transition>
                
                <!-- Error Display -->
                <Transition name="error-slide">
                    <div v-if="inviteError"
                         class="mb-6 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-400 px-4 py-3 rounded-xl relative shadow-sm animate-shake"
                         role="alert">
                        <div class="flex items-start">
                            <Icon name="material-symbols:error" class="h-5 w-5 text-red-500 mt-0.5 mr-3 flex-shrink-0" />
                            <div class="flex-1">
                                <strong class="font-semibold">Invitation Error</strong>
                                <p class="text-sm mt-1">{{ inviteError }}</p>
                            </div>
                            <button @click="clearError"
                                    class="ml-3 flex-shrink-0 p-1 rounded-lg hover:bg-red-100 dark:hover:bg-red-800 transition-colors duration-200">
                                <Icon name="material-symbols:close" class="h-4 w-4 text-red-500" />
                            </button>
                        </div>
                    </div>
                </Transition>
                
                <!-- Warning Message -->
                <Transition name="warning-slide">
                    <div v-if="warningMessage"
                         class="mb-6 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 text-yellow-700 dark:text-yellow-400 px-4 py-3 rounded-xl relative shadow-sm animate-pulse-once"
                         role="alert">
                        <div class="flex items-start">
                            <Icon name="material-symbols:warning" class="h-5 w-5 text-yellow-500 mt-0.5 mr-3 flex-shrink-0" />
                            <div class="flex-1">
                                <strong class="font-semibold">Notice</strong>
                                <p class="text-sm mt-1">{{ warningMessage }}</p>
                            </div>
                            <button @click="clearWarning"
                                    class="ml-3 flex-shrink-0 p-1 rounded-lg hover:bg-yellow-100 dark:hover:bg-yellow-800 transition-colors duration-200">
                                <Icon name="material-symbols:close" class="h-4 w-4 text-yellow-500" />
                            </button>
                        </div>
                    </div>
                </Transition>
                
                <!-- Main Content Slot -->
                <div class="animate-fade-in-up">
                    <slot />
                </div>
                
                <!-- Help Section -->
                <div class="mt-8 text-center animate-fade-in-up">
                    <div class="bg-gray-50 dark:bg-gray-800/50 rounded-xl p-4 border border-gray-200 dark:border-gray-700">
                        <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-2">Need Help?</h4>
                        <div class="flex flex-col sm:flex-row items-center justify-center space-y-2 sm:space-y-0 sm:space-x-4 text-sm">
                            <a href="mailto:<EMAIL>" 
                               class="flex items-center text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-colors">
                                <Icon name="material-symbols:mail" class="h-4 w-4 mr-1" />
                                Contact Support
                            </a>
                            <span class="hidden sm:block text-gray-300 dark:text-gray-600">•</span>
                            <a href="/help/invitations" 
                               class="flex items-center text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-colors">
                                <Icon name="material-symbols:help" class="h-4 w-4 mr-1" />
                                Invitation Help
                            </a>
                        </div>
                    </div>
                </div>
                
                <!-- Footer -->
                <div class="text-center pt-6 mt-6 border-t border-gray-200 dark:border-gray-700 animate-fade-in">
                    <p class="text-xs text-gray-500 dark:text-gray-400">
                        © 2024 Legal SaaS™ • All rights reserved • 
                        <a href="/privacy" class="hover:text-gray-700 dark:hover:text-gray-300 transition-colors">Privacy Policy</a> • 
                        <a href="/terms" class="hover:text-gray-700 dark:hover:text-gray-300 transition-colors">Terms of Service</a>
                    </p>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, onUnmounted, provide } from 'vue';

// Enhanced state management for invite notifications
const inviteError = ref<string | null>(null);
const successMessage = ref<string | null>(null);
const warningMessage = ref<string | null>(null);
const isLoading = ref(false);
const showProgress = ref(false);

// Auto-clear timers
let errorTimer: NodeJS.Timeout | null = null;
let successTimer: NodeJS.Timeout | null = null;
let warningTimer: NodeJS.Timeout | null = null;

// Enhanced notification methods
const clearError = () => {
    inviteError.value = null;
    if (errorTimer) {
        clearTimeout(errorTimer);
        errorTimer = null;
    }
};

const clearSuccess = () => {
    successMessage.value = null;
    if (successTimer) {
        clearTimeout(successTimer);
        successTimer = null;
    }
};

const clearWarning = () => {
    warningMessage.value = null;
    if (warningTimer) {
        clearTimeout(warningTimer);
        warningTimer = null;
    }
};

const setError = (message: string, autoHide = true) => {
    inviteError.value = message;
    if (autoHide) {
        errorTimer = setTimeout(clearError, 8000);
    }
};

const setSuccess = (message: string, autoHide = true) => {
    successMessage.value = message;
    if (autoHide) {
        successTimer = setTimeout(clearSuccess, 6000);
    }
};

const setWarning = (message: string, autoHide = true) => {
    warningMessage.value = message;
    if (autoHide) {
        warningTimer = setTimeout(clearWarning, 7000);
    }
};

const setLoading = (loading: boolean) => {
    isLoading.value = loading;
};

const setProgress = (show: boolean) => {
    showProgress.value = show;
};

// Provide methods to child components
provide('inviteNotifications', {
    setError,
    setSuccess,
    setWarning,
    clearError,
    clearSuccess,
    clearWarning,
    setLoading,
    setProgress,
    isLoading,
    showProgress
});

// Cleanup timers on unmount
onUnmounted(() => {
    if (errorTimer) clearTimeout(errorTimer);
    if (successTimer) clearTimeout(successTimer);
    if (warningTimer) clearTimeout(warningTimer);
});
</script>

<style scoped>
/* Enhanced animations for invite layout */
@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(5deg); }
}

@keyframes float-delayed {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-15px) rotate(-3deg); }
}

@keyframes bounce-slow {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes fade-in {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes fade-in-up {
  from { opacity: 0; transform: translateY(30px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes fade-in-scale {
  from { opacity: 0; transform: scale(0.95); }
  to { opacity: 1; transform: scale(1); }
}

@keyframes bounce-in {
  0% { opacity: 0; transform: scale(0.3); }
  50% { opacity: 1; transform: scale(1.05); }
  70% { transform: scale(0.9); }
  100% { opacity: 1; transform: scale(1); }
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
  20%, 40%, 60%, 80% { transform: translateX(5px); }
}

@keyframes pulse-once {
  0% { opacity: 1; }
  50% { opacity: 0.7; }
  100% { opacity: 1; }
}

/* Apply animations */
.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-float-delayed {
  animation: float-delayed 8s ease-in-out infinite;
  animation-delay: 2s;
}

.animate-bounce-slow {
  animation: bounce-slow 4s ease-in-out infinite;
  animation-delay: 1s;
}

.animate-fade-in {
  animation: fade-in 0.8s ease-out;
}

.animate-fade-in-up {
  animation: fade-in-up 1s ease-out;
  animation-delay: 0.3s;
  animation-fill-mode: both;
}

.animate-fade-in-scale {
  animation: fade-in-scale 0.6s ease-out;
}

.animate-bounce-in {
  animation: bounce-in 0.6s ease-out;
}

.animate-shake {
  animation: shake 0.6s ease-in-out;
}

.animate-pulse-once {
  animation: pulse-once 1s ease-in-out;
}

/* Transition styles */
.success-slide-enter-active,
.success-slide-leave-active,
.error-slide-enter-active,
.error-slide-leave-active,
.warning-slide-enter-active,
.warning-slide-leave-active {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.success-slide-enter-from,
.error-slide-enter-from,
.warning-slide-enter-from {
  opacity: 0;
  transform: translateY(-20px) scale(0.95);
}

.success-slide-leave-to,
.error-slide-leave-to,
.warning-slide-leave-to {
  opacity: 0;
  transform: translateY(-10px) scale(0.98);
}

/* Loading state styles */
.loading-overlay {
  position: absolute;
  inset: 0;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 50;
  border-radius: 1rem;
}

/* Dark mode loading overlay */
.dark .loading-overlay {
  background: rgba(17, 24, 39, 0.9);
}

/* Enhanced backdrop blur for better visual separation */
.backdrop-blur-sm {
  backdrop-filter: blur(4px);
}

/* Responsive adjustments */
@media (max-width: 1024px) {
  .animate-float,
  .animate-float-delayed,
  .animate-bounce-slow {
    animation-duration: 3s;
  }
}

/* Accessibility: Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
  .animate-float,
  .animate-float-delayed,
  .animate-bounce-slow,
  .animate-fade-in,
  .animate-fade-in-up,
  .animate-fade-in-scale,
  .animate-bounce-in,
  .animate-shake,
  .animate-pulse-once {
    animation: none;
  }

  .success-slide-enter-active,
  .success-slide-leave-active,
  .error-slide-enter-active,
  .error-slide-leave-active,
  .warning-slide-enter-active,
  .warning-slide-leave-active {
    transition: opacity 0.2s ease;
  }
}

/* Focus styles for better accessibility */
a:focus,
button:focus {
  outline: 2px solid var(--color-blue-500);
  outline-offset: 2px;
}

/* Enhanced hover states */
a:hover {
  text-decoration: none;
}

/* Custom scrollbar for webkit browsers */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: var(--color-gray-100);
}

::-webkit-scrollbar-thumb {
  background: var(--color-gray-400);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-gray-500);
}

/* Dark mode scrollbar */
.dark ::-webkit-scrollbar-track {
  background: var(--color-gray-800);
}

.dark ::-webkit-scrollbar-thumb {
  background: var(--color-gray-600);
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: var(--color-gray-500);
}
</style>
