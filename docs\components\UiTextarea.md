# UiTextarea Component

A comprehensive, accessible textarea component with advanced features for the Legal SaaS Frontend.

## Features

- ✅ **Multiple Variants**: Default, filled, and outlined styles
- ✅ **Size Options**: Small, medium, and large sizes
- ✅ **Icon Support**: Leading and trailing icons
- ✅ **Character Count**: Optional character counting with limits
- ✅ **Auto-resize**: Automatic height adjustment based on content
- ✅ **Auto-grow**: Enhanced auto-sizing with min/max height constraints and smooth transitions
- ✅ **Clearable**: Optional clear button
- ✅ **Validation States**: Error, success, disabled, readonly states
- ✅ **Loading State**: Loading spinner indicator
- ✅ **Accessibility**: Full ARIA support and keyboard navigation
- ✅ **TypeScript**: Full type safety
- ✅ **Responsive**: Works across all screen sizes

## Basic Usage

```vue
<template>
  <UiTextarea
    id="description"
    v-model="description"
    label="Description"
    placeholder="Enter description..."
  />
</template>

<script setup lang="ts">
import { ref } from 'vue';

const description = ref('');
</script>
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `id` | `string` | **Required** | Unique identifier for the textarea |
| `modelValue` | `string \| null` | `undefined` | The textarea value (v-model) |
| `label` | `string` | `undefined` | Label text displayed above the textarea |
| `placeholder` | `string` | `undefined` | Placeholder text |
| `required` | `boolean` | `false` | Whether the field is required |
| `disabled` | `boolean` | `false` | Whether the textarea is disabled |
| `readonly` | `boolean` | `false` | Whether the textarea is readonly |
| `helpText` | `string` | `undefined` | Help text displayed below the textarea |
| `errorMessage` | `string` | `undefined` | Error message (shows error state) |
| `successMessage` | `string` | `undefined` | Success message |
| `leadingIcon` | `string` | `undefined` | Icon name for leading icon |
| `trailingIcon` | `string` | `undefined` | Icon name for trailing icon |
| `size` | `'sm' \| 'md' \| 'lg'` | `'md'` | Size variant |
| `variant` | `'default' \| 'filled' \| 'outlined'` | `'default'` | Style variant |
| `clearable` | `boolean` | `false` | Show clear button when text is present |
| `loading` | `boolean` | `false` | Show loading spinner |
| `showCharCount` | `boolean` | `false` | Show character count |
| `showSuccess` | `boolean` | `false` | Show success state styling |
| `maxlength` | `number` | `undefined` | Maximum character length |
| `minlength` | `number` | `undefined` | Minimum character length |
| `rows` | `number` | `4` | Number of visible text lines |
| `cols` | `number` | `undefined` | Number of visible character widths |
| `spellcheck` | `boolean` | `true` | Enable spell checking |
| `wrap` | `'soft' \| 'hard' \| 'off'` | `'soft'` | Text wrapping behavior |
| `resize` | `'none' \| 'both' \| 'horizontal' \| 'vertical' \| 'auto'` | `'vertical'` | Resize behavior |
| `autoResize` | `boolean` | `false` | Automatically resize height based on content (legacy) |
| `autoGrow` | `boolean` | `false` | Enhanced auto-sizing with constraints and transitions |
| `minHeight` | `number` | `80` | Minimum height in pixels (for autoGrow) |
| `maxHeight` | `number` | `400` | Maximum height in pixels (for autoGrow) |
| `growTransition` | `boolean` | `true` | Enable smooth transitions for auto-grow |
| `autocomplete` | `string` | `undefined` | Autocomplete attribute |
| `clearButtonLabel` | `string` | `'Clear text'` | Aria label for clear button |
| `name` | `string` | `undefined` | Name attribute for form submission |

## Events

| Event | Payload | Description |
|-------|---------|-------------|
| `update:modelValue` | `string \| null \| undefined` | Emitted when value changes |
| `focus` | `FocusEvent` | Emitted when textarea receives focus |
| `blur` | `FocusEvent` | Emitted when textarea loses focus |
| `input` | `Event` | Emitted on input event |
| `keydown` | `KeyboardEvent` | Emitted on keydown event |
| `paste` | `ClipboardEvent` | Emitted on paste event |
| `clear` | `void` | Emitted when clear button is clicked |

## Exposed Methods

| Method | Description |
|--------|-------------|
| `focus()` | Focus the textarea |
| `blur()` | Blur the textarea |
| `select()` | Select all text in the textarea |

## Examples

### With Icons

```vue
<UiTextarea
  id="comments"
  v-model="comments"
  label="Comments"
  placeholder="Add your comments..."
  leading-icon="heroicons:chat-bubble-left-ellipsis"
  trailing-icon="heroicons:information-circle"
/>
```

### Character Count

```vue
<UiTextarea
  id="limited-text"
  v-model="text"
  label="Description"
  placeholder="Enter description..."
  :maxlength="200"
  show-char-count
  help-text="Maximum 200 characters"
/>
```

### Auto Resize (Legacy)

```vue
<UiTextarea
  id="auto-resize"
  v-model="content"
  label="Content"
  placeholder="Start typing..."
  auto-resize
  :rows="2"
/>
```

### Auto Grow (Enhanced)

```vue
<UiTextarea
  id="auto-grow"
  v-model="content"
  label="Auto-Growing Content"
  placeholder="Start typing to see smooth growth..."
  auto-grow
  :min-height="80"
  :max-height="300"
  :grow-transition="true"
/>
```

### Auto Grow without Transitions

```vue
<UiTextarea
  id="instant-grow"
  v-model="content"
  label="Instant Growth"
  placeholder="Grows instantly without animation..."
  auto-grow
  :grow-transition="false"
  :min-height="100"
  :max-height="250"
/>
```

### Error State

```vue
<UiTextarea
  id="required-field"
  v-model="value"
  label="Required Field"
  placeholder="This field is required..."
  error-message="This field cannot be empty"
  required
/>
```

### Success State

```vue
<UiTextarea
  id="validated-field"
  v-model="value"
  label="Validated Field"
  placeholder="Enter text..."
  success-message="Input is valid!"
  show-success
/>
```

### Clearable

```vue
<UiTextarea
  id="clearable-field"
  v-model="value"
  label="Clearable Field"
  placeholder="Type something..."
  clearable
  @clear="handleClear"
/>
```

### Size Variants

```vue
<!-- Small -->
<UiTextarea
  id="small"
  v-model="value"
  size="sm"
  label="Small Size"
/>

<!-- Medium (Default) -->
<UiTextarea
  id="medium"
  v-model="value"
  size="md"
  label="Medium Size"
/>

<!-- Large -->
<UiTextarea
  id="large"
  v-model="value"
  size="lg"
  label="Large Size"
/>
```

### Style Variants

```vue
<!-- Default -->
<UiTextarea
  id="default"
  v-model="value"
  variant="default"
  label="Default Style"
/>

<!-- Filled -->
<UiTextarea
  id="filled"
  v-model="value"
  variant="filled"
  label="Filled Style"
/>

<!-- Outlined -->
<UiTextarea
  id="outlined"
  v-model="value"
  variant="outlined"
  label="Outlined Style"
/>
```

## Accessibility

The component follows WAI-ARIA guidelines:

- Proper labeling with `aria-label` or associated `<label>`
- Error states announced to screen readers
- Keyboard navigation support
- Focus management
- Required field indication

## Styling

The component uses Tailwind CSS classes and CSS custom properties for theming:

- Respects the project's color system
- Supports dark mode (when implemented)
- Consistent with other UI components
- Responsive design

## Integration with Forms

Works seamlessly with form libraries and validation:

```vue
<template>
  <form @submit.prevent="handleSubmit">
    <UiTextarea
      id="message"
      v-model="form.message"
      label="Message"
      :error-message="errors.message"
      required
    />
    <button type="submit">Submit</button>
  </form>
</template>
```

## Auto-Resize vs Auto-Grow

### Auto-Resize (Legacy)
- Simple height adjustment based on content
- No height constraints
- No transition animations
- Suitable for basic use cases

### Auto-Grow (Enhanced)
- Advanced height management with min/max constraints
- Smooth transition animations (optional)
- Better performance with large content
- Scroll handling when max height is reached
- More control over behavior

```vue
<!-- Legacy auto-resize -->
<UiTextarea
  auto-resize
  :rows="2"
/>

<!-- Enhanced auto-grow -->
<UiTextarea
  auto-grow
  :min-height="80"
  :max-height="300"
  :grow-transition="true"
/>
```

## Browser Support

- Modern browsers (Chrome, Firefox, Safari, Edge)
- Mobile browsers (iOS Safari, Chrome Mobile)
- Keyboard navigation
- Screen reader compatible
