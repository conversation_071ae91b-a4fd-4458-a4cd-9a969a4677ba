<template>
  <footer class="bg-gradient-to-r from-gray-900 via-brandPrimary-900 to-gray-900 text-gray-300 py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-8 lg:gap-12">
        <!-- Brand Section -->
        <div class="md:col-span-2">
          <NuxtLink to="/" class="flex items-center mb-6 group">
            <div class="relative mr-3">
              <Icon name="material-symbols:gavel" class="h-10 w-10 text-brandSecondary-400 group-hover:scale-110 transition-transform duration-300" />
              <div class="absolute inset-0 bg-brandSecondary-400 rounded-full opacity-20 blur-sm"></div>
            </div>
            <span class="text-3xl font-bold bg-gradient-to-r from-white to-brandSecondary-200 bg-clip-text text-transparent">
              LegalFlow
            </span>
          </NuxtLink>
          <p class="text-gray-300 mb-6 max-w-md leading-relaxed">
            Empowering legal professionals with secure, intelligent, and user-friendly technology for modern practice management.
          </p>

          <!-- Social Links -->
          <div class="flex space-x-4 mb-6">
            <a
              href="#"
              class="p-2 bg-white/10 rounded-lg text-gray-300 hover:text-white hover:bg-brandPrimary-600 transition-all duration-300 group"
              aria-label="LinkedIn"
            >
              <Icon name="mdi:linkedin" class="h-5 w-5 group-hover:scale-110 transition-transform duration-300" />
            </a>
            <a
              href="#"
              class="p-2 bg-white/10 rounded-lg text-gray-300 hover:text-white hover:bg-brandPrimary-600 transition-all duration-300 group"
              aria-label="Twitter"
            >
              <Icon name="mdi:twitter" class="h-5 w-5 group-hover:scale-110 transition-transform duration-300" />
            </a>
            <a
              href="#"
              class="p-2 bg-white/10 rounded-lg text-gray-300 hover:text-white hover:bg-brandPrimary-600 transition-all duration-300 group"
              aria-label="GitHub"
            >
              <Icon name="mdi:github" class="h-5 w-5 group-hover:scale-110 transition-transform duration-300" />
            </a>
          </div>

          <!-- Newsletter Signup -->
          <div class="max-w-md">
            <h4 class="text-lg font-semibold mb-3 text-brandSecondary-200">Stay Updated</h4>
            <div class="flex gap-2">
              <input
                type="email"
                placeholder="Enter your email"
                class="flex-1 px-4 py-2 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-brandPrimary-500 focus:border-transparent"
              >
              <UiButton
                variant="primary"
                size="sm"
                class="bg-brandPrimary-600 hover:bg-brandPrimary-700 px-6"
              >
                Subscribe
              </UiButton>
            </div>
          </div>
        </div>

        <!-- Product Links -->
        <div>
          <h3 class="text-lg font-semibold mb-6 text-brandSecondary-200">Product</h3>
          <ul class="space-y-3">
            <li>
              <NuxtLink
                to="/features"
                class="text-gray-300 hover:text-white hover:translate-x-1 transition-all duration-200 flex items-center group"
              >
                <Icon name="material-symbols:arrow-forward" class="w-4 h-4 mr-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
                Features
              </NuxtLink>
            </li>
            <li>
              <NuxtLink
                to="/pricing"
                class="text-gray-300 hover:text-white hover:translate-x-1 transition-all duration-200 flex items-center group"
              >
                <Icon name="material-symbols:arrow-forward" class="w-4 h-4 mr-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
                Pricing
              </NuxtLink>
            </li>
            <li>
              <a
                href="#"
                class="text-gray-300 hover:text-white hover:translate-x-1 transition-all duration-200 flex items-center group"
              >
                <Icon name="material-symbols:arrow-forward" class="w-4 h-4 mr-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
                Security
              </a>
            </li>
            <li>
              <a
                href="#"
                class="text-gray-300 hover:text-white hover:translate-x-1 transition-all duration-200 flex items-center group"
              >
                <Icon name="material-symbols:arrow-forward" class="w-4 h-4 mr-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
                Integrations
              </a>
            </li>
          </ul>
        </div>

        <!-- Company Links -->
        <div>
          <h3 class="text-lg font-semibold mb-6 text-brandSecondary-200">Company</h3>
          <ul class="space-y-3">
            <li>
              <NuxtLink
                to="/about"
                class="text-gray-300 hover:text-white hover:translate-x-1 transition-all duration-200 flex items-center group"
              >
                <Icon name="material-symbols:arrow-forward" class="w-4 h-4 mr-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
                About Us
              </NuxtLink>
            </li>
            <li>
              <NuxtLink
                to="/contact"
                class="text-gray-300 hover:text-white hover:translate-x-1 transition-all duration-200 flex items-center group"
              >
                <Icon name="material-symbols:arrow-forward" class="w-4 h-4 mr-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
                Contact
              </NuxtLink>
            </li>
            <li>
              <a
                href="#"
                class="text-gray-300 hover:text-white hover:translate-x-1 transition-all duration-200 flex items-center group"
              >
                <Icon name="material-symbols:arrow-forward" class="w-4 h-4 mr-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
                Careers
              </a>
            </li>
            <li>
              <a
                href="#"
                class="text-gray-300 hover:text-white hover:translate-x-1 transition-all duration-200 flex items-center group"
              >
                <Icon name="material-symbols:arrow-forward" class="w-4 h-4 mr-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
                Blog
              </a>
            </li>
          </ul>
        </div>
      </div>

      <!-- Bottom Section -->
      <div class="border-t border-white/20 mt-12 pt-8">
        <div class="flex flex-col lg:flex-row justify-between items-center gap-6">
          <!-- Copyright -->
          <div class="flex flex-col sm:flex-row items-center gap-4">
            <p class="text-gray-400 text-sm">
              &copy; {{ new Date().getFullYear() }} LegalFlow Inc. All rights reserved.
            </p>
            <div class="flex items-center gap-2 text-sm text-gray-400">
              <Icon name="material-symbols:security" class="w-4 h-4" />
              <span>SOC 2 Certified</span>
              <span class="mx-2">•</span>
              <Icon name="material-symbols:verified" class="w-4 h-4" />
              <span>GDPR Compliant</span>
            </div>
          </div>

          <!-- Legal Links and Language Switcher -->
          <div class="flex flex-col lg:flex-row items-center gap-6">
            <!-- Legal Links -->
            <div class="flex flex-wrap justify-center ltr:gap-6 rtl:gap-6">
              <NuxtLink
                to="/privacy-policy"
                class="text-gray-400 hover:text-white text-sm transition-colors duration-200 hover:underline"
              >
                {{ t('footer.privacyPolicy') }}
              </NuxtLink>
              <NuxtLink
                to="/terms-of-service"
                class="text-gray-400 hover:text-white text-sm transition-colors duration-200 hover:underline"
              >
                {{ t('footer.termsOfService') }}
              </NuxtLink>
              <a
                href="#"
                class="text-gray-400 hover:text-white text-sm transition-colors duration-200 hover:underline"
              >
                {{ t('footer.cookiePolicy') }}
              </a>
              <a
                href="#"
                class="text-gray-400 hover:text-white text-sm transition-colors duration-200 hover:underline"
              >
                {{ t('footer.accessibility') }}
              </a>
            </div>

            <!-- Language Switcher -->
            <div class="flex items-center ltr:gap-4 rtl:gap-4">
              <span class="text-gray-400 text-sm">{{ t('common.language') }}:</span>
              <UiLanguageSwitcher
                variant="footer"
                :show-flag="true"
                :show-name="true"
                :show-settings="false"
                dropdown-position="right"
                display-mode="native"
                @language-changed="onLanguageChanged"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </footer>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n'

// i18n composables
const { t } = useI18n()

// Language change handler
const onLanguageChanged = (locale: string) => {
  console.log(`Language changed to: ${locale}`)
  // Additional language change logic can be added here
}

// Enhanced footer component with theme integration and improved UX
</script>

<style scoped>
/* Enhanced footer styling with theme integration */
/* footer {
  background-image:
    radial-gradient(circle at 20% 80%, rgba(26, 86, 219, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(6, 182, 212, 0.1) 0%, transparent 50%);
} */

/* Smooth hover animations */
.group:hover .group-hover\:scale-110 {
  transform: scale(1.1);
}

.group:hover .group-hover\:opacity-100 {
  opacity: 1;
}

/* Newsletter input focus styles */
input:focus {
  box-shadow: 0 0 0 2px rgba(26, 86, 219, 0.3);
}

/* Link hover effects */
a:hover {
  transition: all 0.2s ease-in-out;
}
</style>