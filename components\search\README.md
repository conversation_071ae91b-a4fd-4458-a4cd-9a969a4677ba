# Global Search Components

A comprehensive search system for the Legal SaaS platform that enables users to search across all entity types including cases, documents, users, clients, and templates.

## 🔍 Overview

The global search system consists of several interconnected components that work together to provide a powerful and intuitive search experience:

- **Search Page**: Main search interface at `/dashboard/search`
- **Search Components**: Reusable UI components for search functionality
- **Search Composable**: State management and API integration
- **Search API**: Backend endpoint for processing search requests

## 📁 Component Structure

```
components/search/
├── GlobalSearchFilters.vue     # Advanced search filters and controls
├── SearchResultItem.vue        # Individual search result display
├── SearchResultsList.vue       # Collection of search results
└── README.md                   # This documentation
```

## 🧩 Components

### GlobalSearchFilters.vue

Advanced search interface with entity type selection, filters, and sorting options.

**Features:**
- Multi-entity type selection (cases, documents, users, clients, templates)
- Advanced filtering (date range, status, priority, file type, role)
- Sort options (relevance, date, title)
- Search suggestions
- Collapsible advanced filters panel

**Props:**
```typescript
interface Props {
  modelValue: string              // Search query
  selectedTypes: SearchEntityType[] // Selected entity types
  filters: Record<string, any>    // Active filters
  sortBy: string                  // Current sort option
  totalByType?: Record<SearchEntityType, number> // Result counts by type
  suggestions?: string[]          // Search suggestions
  isSearching?: boolean          // Loading state
}
```

**Events:**
- `update:modelValue` - Search query changes
- `search` - Perform search
- `type-change` - Entity type selection changes
- `filter-change` - Filter value changes
- `sort-change` - Sort option changes
- `clear-filters` - Clear all filters

### SearchResultItem.vue

Displays individual search results with support for multiple view modes.

**Features:**
- Multiple view modes (list, grid, table)
- Syntax highlighting for search matches
- Metadata display (date, status, tags)
- Action buttons (favorite, share)
- Responsive design
- Dark mode support

**Props:**
```typescript
interface Props {
  result: GlobalSearchResultItem  // Search result data
  viewMode: SearchViewMode       // Display mode
  showThumbnail?: boolean        // Show thumbnails/icons
  showMetadata?: boolean         // Show metadata
  showTags?: boolean            // Show tags
  showActions?: boolean         // Show action buttons
  highlightQuery?: string       // Query to highlight
  compactMode?: boolean         // Compact display
}
```

**Events:**
- `click` - Result item clicked
- `action` - Action button clicked

### SearchResultsList.vue

Container component that manages and displays multiple search results.

**Features:**
- Multiple view modes with toggle
- Entity type filtering tabs
- Loading states with skeletons
- Empty states with helpful messages
- Load more functionality
- Result statistics display

**Props:**
```typescript
interface Props {
  results: GlobalSearchResultItem[]     // Search results
  loading: boolean                      // Loading state
  total: number                        // Total result count
  totalByType?: Record<SearchEntityType, number> // Counts by type
  selectedTypes: SearchEntityType[]     // Selected types
  viewMode: SearchViewMode             // Current view mode
  hasSearched: boolean                 // Has performed search
  // ... additional display options
}
```

## 🎯 Usage Examples

### Basic Search Page Implementation

```vue
<template>
  <div class="space-y-6">
    <!-- Search Filters -->
    <GlobalSearchFilters
      v-model="searchQuery"
      :selected-types="searchState.selectedTypes"
      :filters="searchState.filters"
      :sort-by="searchState.sortBy"
      :total-by-type="searchState.totalByType"
      :suggestions="searchState.suggestions"
      :is-searching="isSearching"
      @search="handleSearch"
      @type-change="handleTypeChange"
      @filter-change="handleFilterChange"
      @sort-change="handleSortChange"
      @clear-filters="handleClearFilters"
    />

    <!-- Search Results -->
    <SearchResultsList
      :results="searchState.results"
      :loading="isSearching"
      :total="searchState.total"
      :total-by-type="searchState.totalByType"
      :selected-types="searchState.selectedTypes"
      :view-mode="searchState.viewMode"
      :has-searched="searchState.hasSearched"
      @result-click="handleResultClick"
      @result-action="handleResultAction"
      @view-change="handleViewChange"
    />
  </div>
</template>

<script setup lang="ts">
import { useGlobalSearch } from '~/app/shared/composables/core/useGlobalSearch'

const {
  state: searchState,
  isSearching,
  search,
  setTypes,
  setFilters,
  setSortBy,
  setViewMode
} = useGlobalSearch()

// Event handlers
const handleSearch = (query: string) => search(query)
const handleTypeChange = (types: SearchEntityType[]) => setTypes(types)
const handleFilterChange = (key: string, value: any) => {
  const newFilters = { ...searchState.value.filters }
  newFilters[key] = value
  setFilters(newFilters)
}
// ... other handlers
</script>
```

### Standalone Search Results Display

```vue
<template>
  <SearchResultsList
    :results="results"
    :loading="false"
    :total="results.length"
    :selected-types="['all']"
    :view-mode="'list'"
    :has-searched="true"
    :show-type-filter="false"
    :compact-mode="true"
    @result-click="navigateToResult"
  />
</template>
```

## 🔧 Composable Integration

The components work seamlessly with the `useGlobalSearch` composable:

```typescript
import { useGlobalSearch } from '~/app/shared/composables/core/useGlobalSearch'

const {
  state,           // Reactive search state
  isSearching,     // Loading indicator
  hasResults,      // Has search results
  isEmpty,         // No results found
  resultsByType,   // Results grouped by type
  search,          // Perform search
  clearSearch,     // Clear search state
  setQuery,        // Set search query
  setTypes,        // Set entity types
  setFilters,      // Set filters
  setSortBy,       // Set sort option
  setViewMode      // Set view mode
} = useGlobalSearch({
  initialQuery: '',
  initialTypes: ['all'],
  autoSearch: true,
  persistState: true
})
```

## 🎨 Styling & Theming

All components follow the platform's design system:

- **Tailwind CSS**: Utility-first styling
- **Dark Mode**: Full dark mode support
- **Responsive**: Mobile-first responsive design
- **Brand Colors**: Consistent brand color usage
- **Animations**: Smooth transitions and micro-interactions

## 🔍 Search Features

### Query Syntax
- **Basic**: Simple keyword search
- **Phrases**: Use quotes for exact phrases: `"contract review"`
- **Wildcards**: Use asterisk for partial matches: `contract*`
- **Boolean**: AND/OR operators: `contract AND review`
- **Exclusion**: Minus sign to exclude: `contract -template`
- **Field Search**: Search specific fields: `title:contract`

### Entity Types
- **Cases**: Legal cases and matters
- **Documents**: Files and attachments
- **Users**: Team members and contacts
- **Clients**: Client organizations and individuals
- **Templates**: Document and email templates

### Filters
- **Date Range**: Filter by creation/modification date
- **Status**: Active, inactive, pending, closed, draft
- **Priority**: Low, medium, high, urgent (cases)
- **File Type**: PDF, Word, Excel, etc. (documents)
- **Role**: Admin, lawyer, paralegal, client (users)

### View Modes
- **List**: Detailed list view with full information
- **Grid**: Card-based grid layout
- **Table**: Compact table format

## 🚀 Performance

- **Debounced Search**: 300ms debounce on search input
- **Lazy Loading**: Components loaded on demand
- **Skeleton Loading**: Smooth loading states
- **Pagination**: Efficient result pagination
- **Caching**: Search state persistence

## 🔧 Configuration

### Search Options
```typescript
const searchOptions = {
  initialQuery: '',        // Initial search query
  initialTypes: ['all'],   // Initial entity types
  initialFilters: {},      // Initial filters
  initialSort: 'relevance', // Initial sort option
  initialViewMode: 'list', // Initial view mode
  debounceMs: 300,        // Search debounce delay
  autoSearch: true,       // Auto-search on query change
  persistState: true      // Persist state in localStorage
}
```

### API Configuration
The search API endpoint is configurable through environment variables:
- `API_BASE`: Base API URL
- Search endpoint: `GET /api/search`

## 🧪 Testing

Components include comprehensive test coverage:
- Unit tests for individual components
- Integration tests for search flow
- E2E tests for complete user journeys
- Accessibility tests for WCAG compliance

## 📱 Accessibility

- **Keyboard Navigation**: Full keyboard support
- **Screen Readers**: ARIA labels and roles
- **Focus Management**: Proper focus handling
- **High Contrast**: Support for high contrast mode
- **Reduced Motion**: Respects prefers-reduced-motion

## 🔮 Future Enhancements

- **Saved Searches**: Save and manage search queries
- **Search Analytics**: Track search patterns and performance
- **Advanced Operators**: More sophisticated query syntax
- **Faceted Search**: Dynamic filter generation
- **Search Suggestions**: AI-powered query suggestions
- **Export Results**: Export search results to various formats
