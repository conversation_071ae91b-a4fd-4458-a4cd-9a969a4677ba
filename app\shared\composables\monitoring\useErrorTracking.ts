/**
 * Enhanced Error Tracking Composable
 * 
 * Comprehensive error tracking and reporting system with
 * automatic error capture, user context, and recovery strategies
 */

import { ref, computed, onMounted, onErrorCaptured, readonly } from 'vue' // Removed watch, onUnmounted, Added readonly
import type { ISODateString, UUID, UserId } from '@shared/types/core'
import { useLogger } from '../core/useLogger.js' // Added .js
import { useAnalytics } from './useAnalytics.js' // Added .js

// ============================================================================
// ERROR TRACKING TYPES
// ============================================================================

export interface ErrorTrackingConfig {
  enabled: boolean
  autoCapture: boolean
  captureUnhandledRejections: boolean
  captureConsoleErrors: boolean
  enableUserFeedback: boolean
  enableRecoveryStrategies: boolean
  maxErrorsPerSession: number
  reportingEndpoint?: string
  beforeSend?: (error: ErrorReport) => ErrorReport | null
}

export interface ErrorReport {
  id: UUID
  timestamp: ISODateString
  type: ErrorType
  severity: ErrorSeverity
  message: string
  stack?: string
  source: ErrorSource
  context: ErrorContext
  userAgent: string
  url: string
  userId?: UserId
  sessionId: string
  fingerprint: string
  breadcrumbs: Breadcrumb[]
  tags: Record<string, string>
  extra: Record<string, any>
  resolved: boolean
  recoveryAttempted: boolean
}

export enum ErrorType {
  JAVASCRIPT = 'javascript',
  VUE = 'vue',
  PROMISE = 'promise',
  NETWORK = 'network',
  VALIDATION = 'validation',
  BUSINESS = 'business',
  SECURITY = 'security',
  PERFORMANCE = 'performance'
}

export enum ErrorSeverity {
  DEBUG = 'debug',
  INFO = 'info',
  WARNING = 'warning',
  ERROR = 'error',
  FATAL = 'fatal'
}

export enum ErrorSource {
  COMPONENT = 'component',
  COMPOSABLE = 'composable',
  STORE = 'store',
  API = 'api',
  ROUTER = 'router',
  GLOBAL = 'global',
  UNKNOWN = 'unknown'
}

export interface ErrorContext {
  component?: string
  route?: string
  action?: string
  feature?: string
  props?: Record<string, any>
  state?: Record<string, any>
  userInteraction?: UserInteraction | null // Allow null
  networkStatus?: NetworkStatus
  memoryUsage?: number
}

export interface UserInteraction {
  type: 'click' | 'input' | 'navigation' | 'form_submit' | 'other'
  target?: string
  timestamp: ISODateString
}

export interface NetworkStatus {
  online: boolean
  effectiveType?: string
  downlink?: number
  rtt?: number
}

export interface Breadcrumb {
  timestamp: ISODateString
  category: BreadcrumbCategory
  message: string
  level: ErrorSeverity
  data?: Record<string, any>
}

export enum BreadcrumbCategory {
  NAVIGATION = 'navigation',
  USER_ACTION = 'user_action',
  API_CALL = 'api_call',
  STATE_CHANGE = 'state_change',
  CONSOLE = 'console',
  ERROR = 'error'
}

export interface ErrorRecoveryStrategy {
  type: RecoveryType
  action: () => Promise<boolean>
  description: string
  maxAttempts: number
}

export enum RecoveryType {
  RETRY = 'retry',
  FALLBACK = 'fallback',
  REFRESH = 'refresh',
  REDIRECT = 'redirect',
  RESET_STATE = 'reset_state'
}

// ============================================================================
// ERROR TRACKING COMPOSABLE
// ============================================================================

export function useErrorTracking(config: Partial<ErrorTrackingConfig> = {}) {
  // ============================================================================
  // CONFIGURATION
  // ============================================================================
  
  const defaultConfig: ErrorTrackingConfig = {
    enabled: true,
    autoCapture: true,
    captureUnhandledRejections: true,
    captureConsoleErrors: process.env.NODE_ENV === 'development',
    enableUserFeedback: true,
    enableRecoveryStrategies: true,
    maxErrorsPerSession: 50
  }
  
  const errorConfig = ref({ ...defaultConfig, ...config })
  
  // ============================================================================
  // DEPENDENCIES
  // ============================================================================
  
  const logger = useLogger('ErrorTracking')
  const analytics = useAnalytics()
  
  // ============================================================================
  // STATE
  // ============================================================================
  
  const errors = ref<ErrorReport[]>([])
  const breadcrumbs = ref<Breadcrumb[]>([])
  const isCapturing = ref(false)
  const sessionErrorCount = ref(0)
  const lastUserInteraction = ref<UserInteraction | null>(null)
  const recoveryStrategies = ref<Map<string, ErrorRecoveryStrategy>>(new Map())
  
  // ============================================================================
  // COMPUTED
  // ============================================================================
  
  const errorsByType = computed(() => {
    const grouped: Record<ErrorType, ErrorReport[]> = {} as any
    
    errors.value.forEach(error => {
      if (!grouped[error.type]) {
        grouped[error.type] = []
      }
      grouped[error.type].push(error)
    })
    
    return grouped
  })
  
  const errorsBySeverity = computed(() => {
    const grouped: Record<ErrorSeverity, ErrorReport[]> = {} as any
    
    errors.value.forEach(error => {
      if (!grouped[error.severity]) {
        grouped[error.severity] = []
      }
      grouped[error.severity].push(error)
    })
    
    return grouped
  })
  
  const criticalErrors = computed(() => 
    errors.value.filter(error => 
      error.severity === ErrorSeverity.ERROR || 
      error.severity === ErrorSeverity.FATAL
    )
  )
  
  const recentErrors = computed(() => 
    errors.value
      .filter(error => {
        const errorTime = new Date(error.timestamp).getTime()
        const oneHourAgo = Date.now() - 60 * 60 * 1000
        return errorTime > oneHourAgo
      })
      .slice(-10)
  )
  
  // ============================================================================
  // ERROR CAPTURE METHODS
  // ============================================================================
  
  const captureError = (
    error: Error | string,
    context: Partial<ErrorContext> = {},
    severity: ErrorSeverity = ErrorSeverity.ERROR,
    type: ErrorType = ErrorType.JAVASCRIPT
  ): UUID => {
    if (!errorConfig.value.enabled || sessionErrorCount.value >= errorConfig.value.maxErrorsPerSession) {
      return '' as UUID
    }
    
    const errorMessage = typeof error === 'string' ? error : error.message
    const errorStack = typeof error === 'string' ? undefined : error.stack
    
    const errorReport: ErrorReport = {
      id: generateErrorId(),
      timestamp: new Date().toISOString() as ISODateString,
      type,
      severity,
      message: errorMessage,
      stack: errorStack,
      source: inferErrorSource(errorStack),
      context: {
        ...context,
        userInteraction: lastUserInteraction.value,
        networkStatus: getNetworkStatus(),
        ...(getMemoryUsage() !== undefined && { memoryUsage: getMemoryUsage() }),
      },
      userAgent: navigator.userAgent,
      url: window.location.href,
      userId: getCurrentUserId(),
      sessionId: analytics.getSessionId(),
      fingerprint: generateErrorFingerprint(errorMessage, errorStack),
      breadcrumbs: [...breadcrumbs.value],
      tags: extractErrorTags(errorMessage, errorStack),
      extra: {},
      resolved: false,
      recoveryAttempted: false
    }
    
    // Apply beforeSend filter
    const filteredError = errorConfig.value.beforeSend 
      ? errorConfig.value.beforeSend(errorReport)
      : errorReport
    
    if (!filteredError) return '' as UUID
    
    // Add to errors list
    errors.value.push(filteredError)
    sessionErrorCount.value++
    
    // Add error breadcrumb
    addBreadcrumb({
      category: BreadcrumbCategory.ERROR,
      message: `${type}: ${errorMessage}`,
      level: severity,
      data: { errorId: filteredError.id }
    })
    
    // Log error
    logger.error('Error captured', {
      id: filteredError.id,
      type,
      severity,
      message: errorMessage
    })
    
    // Track in analytics
    analytics.trackError(typeof error === 'string' ? new Error(error) : error, {
      errorId: filteredError.id,
      type,
      severity,
      source: filteredError.source
    })
    
    // Attempt recovery if enabled
    if (errorConfig.value.enableRecoveryStrategies) {
      attemptRecovery(filteredError)
    }
    
    // Send to reporting endpoint
    sendErrorReport(filteredError)
    
    return filteredError.id
  }
  
  const captureException = (error: Error, context?: Partial<ErrorContext>) => {
    return captureError(error, context, ErrorSeverity.ERROR, ErrorType.JAVASCRIPT)
  }
  
  const captureMessage = (
    message: string, 
    severity: ErrorSeverity = ErrorSeverity.INFO,
    context?: Partial<ErrorContext>
  ) => {
    return captureError(message, context, severity, ErrorType.JAVASCRIPT)
  }
  
  const captureVueError = (error: Error, instance: any, info: string) => { // info is already string
    const context: Partial<ErrorContext> = {
      component: instance?.$options?.name || 'Unknown',
      route: instance?.$route?.path,
      props: instance?.$props,
      state: instance?.$data
    }
    
    return captureError(error, context, ErrorSeverity.ERROR, ErrorType.VUE)
  }
  
  const captureNetworkError = (
    error: Error,
    url: string,
    method: string,
    status?: number
  ) => {
    const context: Partial<ErrorContext> = {
      action: `${method} ${url}`,
      feature: 'api'
    }
    
    const extra = { url, method, status }
    
    const errorReport = captureError(error, context, ErrorSeverity.ERROR, ErrorType.NETWORK)
    
    // Add extra data
    const errorIndex = errors.value.findIndex(e => e.id === errorReport)
    if (errorIndex !== -1) {
      errors.value[errorIndex].extra = extra
    }
    
    return errorReport
  }
  
  // ============================================================================
  // BREADCRUMB MANAGEMENT
  // ============================================================================
  
  const addBreadcrumb = (breadcrumb: Omit<Breadcrumb, 'timestamp'>) => {
    const fullBreadcrumb: Breadcrumb = {
      ...breadcrumb,
      timestamp: new Date().toISOString() as ISODateString
    }
    
    breadcrumbs.value.push(fullBreadcrumb)
    
    // Keep only last 50 breadcrumbs
    if (breadcrumbs.value.length > 50) {
      breadcrumbs.value.shift()
    }
  }
  
  const trackNavigation = (to: string, from?: string) => {
    addBreadcrumb({
      category: BreadcrumbCategory.NAVIGATION,
      message: `Navigated from ${from || 'unknown'} to ${to}`,
      level: ErrorSeverity.INFO,
      data: { to, from }
    })
  }
  
  const trackUserAction = (action: string, target?: string, data?: any) => {
    const interaction: UserInteraction = {
      type: inferInteractionType(action),
      timestamp: new Date().toISOString() as ISODateString
    };
    if (target !== undefined) {
      interaction.target = target;
    }
    
    lastUserInteraction.value = interaction
    
    addBreadcrumb({
      category: BreadcrumbCategory.USER_ACTION,
      message: `User ${action}${target ? ` on ${target}` : ''}`,
      level: ErrorSeverity.INFO,
      data: { action, target, ...data }
    })
  }
  
  const trackApiCall = (method: string, url: string, status: number, duration: number) => {
    addBreadcrumb({
      category: BreadcrumbCategory.API_CALL,
      message: `${method} ${url} - ${status} (${duration}ms)`,
      level: status >= 400 ? ErrorSeverity.WARNING : ErrorSeverity.INFO,
      data: { method, url, status, duration }
    })
  }
  
  const trackStateChange = (component: string, change: string, data?: any) => {
    addBreadcrumb({
      category: BreadcrumbCategory.STATE_CHANGE,
      message: `${component}: ${change}`,
      level: ErrorSeverity.DEBUG,
      data: { component, change, ...data }
    })
  }
  
  // ============================================================================
  // ERROR RECOVERY
  // ============================================================================
  
  const registerRecoveryStrategy = (
    errorPattern: string,
    strategy: ErrorRecoveryStrategy
  ) => {
    recoveryStrategies.value.set(errorPattern, strategy)
  }
  
  const attemptRecovery = async (error: ErrorReport) => {
    if (error.recoveryAttempted) return
    
    // Find matching recovery strategy
    for (const [pattern, strategy] of recoveryStrategies.value.entries()) {
      if (error.message.includes(pattern) || error.stack?.includes(pattern)) {
        try {
          logger.info('Attempting error recovery', {
            errorId: error.id,
            strategy: strategy.type
          })
          
          const success = await strategy.action()
          
          if (success) {
            error.resolved = true
            error.recoveryAttempted = true
            
            addBreadcrumb({
              category: BreadcrumbCategory.ERROR,
              message: `Recovery successful for error ${error.id}`,
              level: ErrorSeverity.INFO,
              data: { errorId: error.id, strategy: strategy.type }
            })
            
            logger.info('Error recovery successful', {
              errorId: error.id,
              strategy: strategy.type
            })
          }
        } catch (recoveryError) {
          logger.error('Error recovery failed', {
            errorId: error.id,
            strategy: strategy.type,
            recoveryError
          })
        }
        
        break
      }
    }
  }
  
  // ============================================================================
  // UTILITY FUNCTIONS
  // ============================================================================
  
  const generateErrorId = (): UUID => {
    return `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}` as UUID
  }
  
  const generateErrorFingerprint = (message: string, stack?: string): string => {
    const content = stack || message
    // Simple hash function for fingerprinting
    let hash = 0
    for (let i = 0; i < content.length; i++) {
      const char = content.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // Convert to 32-bit integer
    }
    return hash.toString(36)
  }
  
  const inferErrorSource = (stack?: string): ErrorSource => {
    if (!stack) return ErrorSource.UNKNOWN
    
    if (stack.includes('.vue')) return ErrorSource.COMPONENT
    if (stack.includes('composables/')) return ErrorSource.COMPOSABLE
    if (stack.includes('stores/')) return ErrorSource.STORE
    if (stack.includes('api/')) return ErrorSource.API
    if (stack.includes('router')) return ErrorSource.ROUTER
    
    return ErrorSource.GLOBAL
  }
  
  const extractErrorTags = (message: string, stack?: string): Record<string, string> => {
    const tags: Record<string, string> = {}
    
    // Extract component name from stack
    const componentMatch = stack?.match(/(\w+)\.vue/)
    if (componentMatch) {
      tags.component = componentMatch[1]
    }
    
    // Extract error type from message
    if (message.includes('Network')) tags.category = 'network'
    if (message.includes('Validation')) tags.category = 'validation'
    if (message.includes('Permission')) tags.category = 'security'
    
    return tags
  }
  
  const inferInteractionType = (action: string): UserInteraction['type'] => {
    if (action.includes('click')) return 'click'
    if (action.includes('input') || action.includes('type')) return 'input'
    if (action.includes('navigate')) return 'navigation'
    if (action.includes('submit')) return 'form_submit'
    return 'other'
  }
  
  const getCurrentUserId = (): UserId | undefined => {
    // Get from auth store or context
    return undefined
  }
  
  const getNetworkStatus = (): NetworkStatus => {
    const connection = (navigator as any).connection
    return {
      online: navigator.onLine,
      effectiveType: connection?.effectiveType,
      downlink: connection?.downlink,
      rtt: connection?.rtt
    }
  }
  
  const getMemoryUsage = (): number | undefined => {
    const memory = (performance as any).memory
    if (memory) {
      return (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100
    }
    return undefined
  }
  
  // ============================================================================
  // REPORTING
  // ============================================================================
  
  const sendErrorReport = async (error: ErrorReport) => {
    if (!errorConfig.value.reportingEndpoint) return
    
    try {
      await fetch(errorConfig.value.reportingEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(error)
      })
    } catch (reportingError) {
      logger.error('Failed to send error report', { reportingError })
    }
  }
  
  const exportErrors = (format: 'json' | 'csv' = 'json') => {
    if (format === 'json') {
      return JSON.stringify(errors.value, null, 2)
    } else {
      // CSV format
      const headers = ['timestamp', 'type', 'severity', 'message', 'source', 'url']
      const rows = errors.value.map(error => [
        error.timestamp,
        error.type,
        error.severity,
        error.message,
        error.source,
        error.url
      ])
      
      return [headers, ...rows]
        .map(row => row.map(cell => `"${cell}"`).join(','))
        .join('\n')
    }
  }
  
  // ============================================================================
  // AUTO-CAPTURE SETUP
  // ============================================================================
  
  const setupAutoCapture = () => {
    if (!errorConfig.value.autoCapture) return
    
    // Capture unhandled JavaScript errors
    window.addEventListener('error', (event) => {
      captureError(event.error || event.message, {
        action: 'global_error_handler'
      })
    })
    
    // Capture unhandled promise rejections
    if (errorConfig.value.captureUnhandledRejections) {
      window.addEventListener('unhandledrejection', (event) => {
        captureError(event.reason, {
          action: 'unhandled_promise_rejection'
        }, ErrorSeverity.ERROR, ErrorType.PROMISE)
      })
    }
    
    // Capture console errors
    if (errorConfig.value.captureConsoleErrors) {
      const originalConsoleError = console.error
      console.error = (...args) => {
        captureMessage(args.join(' '), ErrorSeverity.ERROR)
        originalConsoleError.apply(console, args)
      }
    }
  }
  
  // ============================================================================
  // INITIALIZATION
  // ============================================================================
  
  const initialize = () => {
    if (!errorConfig.value.enabled) return
    
    logger.info('Initializing error tracking')
    
    setupAutoCapture()
    
    // Register default recovery strategies
    registerRecoveryStrategy('Network Error', {
      type: RecoveryType.RETRY,
      action: async () => {
        // Retry network request
        return false // Placeholder
      },
      description: 'Retry failed network request',
      maxAttempts: 3
    })
    
    registerRecoveryStrategy('ChunkLoadError', {
      type: RecoveryType.REFRESH,
      action: async () => {
        window.location.reload()
        return true
      },
      description: 'Refresh page to reload chunks',
      maxAttempts: 1
    })
  }
  
  // ============================================================================
  // LIFECYCLE
  // ============================================================================
  
  onMounted(() => {
    initialize()
  })
  
  // Vue error capture
  onErrorCaptured((error, instance, info) => {
    captureVueError(error, instance, info)
    return false // Don't suppress the error
  })
  
  // ============================================================================
  // RETURN INTERFACE
  // ============================================================================
  
  return {
    // Configuration
    config: readonly(errorConfig),
    isCapturing: readonly(isCapturing),
    
    // State
    errors: readonly(errors),
    breadcrumbs: readonly(breadcrumbs),
    sessionErrorCount: readonly(sessionErrorCount),
    
    // Computed
    errorsByType,
    errorsBySeverity,
    criticalErrors,
    recentErrors,
    
    // Capture methods
    captureError,
    captureException,
    captureMessage,
    captureVueError,
    captureNetworkError,
    
    // Breadcrumb methods
    addBreadcrumb,
    trackNavigation,
    trackUserAction,
    trackApiCall,
    trackStateChange,
    
    // Recovery
    registerRecoveryStrategy,
    attemptRecovery,
    
    // Utilities
    exportErrors
  }
}
