<template>
  <div class="space-y-8">
    <!-- Billing Header -->
    <div class="bg-gradient-to-r from-yellow-600 to-orange-600 rounded-xl p-8 text-white">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-3xl font-bold mb-2">Billing Analytics</h1>
          <p class="text-yellow-100 text-lg">
            Track revenue, subscription metrics, and financial performance
          </p>
        </div>
        <div class="hidden md:flex items-center gap-4">
          <UiSelect
            v-model="selectedPeriod"
            :options="periodOptions"
            class="bg-white/10 border-white/20 text-white"
          />
          <UiButton @click="generateReport" variant="outline" class="border-white/20 text-white hover:bg-white/10" :loading="generatingReport">
            <Icon name="material-symbols:receipt" class="h-4 w-4 mr-2" />
            Generate Report
          </UiButton>
        </div>
      </div>
    </div>

    <!-- Revenue Metrics -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <!-- Monthly Revenue -->
      <UiCard
        icon="material-symbols:attach-money"
        icon-color="green"
        :loading="isLoading"
        hover-effect
      >
        <template #header>
          <div class="flex items-center justify-between w-full">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Monthly Revenue</h3>
            <UiBadge :variant="billing.revenue.growth > 0 ? 'success' : 'neutral'">
              {{ billing.revenue.growth > 0 ? '+' : '' }}{{ billing.revenue.growth }}%
            </UiBadge>
          </div>
        </template>
        <div class="space-y-3">
          <p class="text-3xl font-bold text-gray-900 dark:text-white">${{ formatCurrency(billing.revenue.current) }}</p>
          <p class="text-sm text-gray-500 dark:text-gray-400">
            Target: ${{ formatCurrency(billing.revenue.target) }}
          </p>
          <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div 
              class="bg-green-600 h-2 rounded-full transition-all duration-300"
              :style="{ width: `${Math.min((billing.revenue.current / billing.revenue.target) * 100, 100)}%` }"
            ></div>
          </div>
        </div>
      </UiCard>

      <!-- Active Subscriptions -->
      <UiCard
        icon="material-symbols:subscriptions"
        icon-color="blue"
        :loading="isLoading"
        hover-effect
      >
        <template #header>
          <div class="flex items-center justify-between w-full">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Subscriptions</h3>
            <UiBadge :variant="billing.subscriptions.growth > 0 ? 'success' : 'neutral'">
              {{ billing.subscriptions.growth > 0 ? '+' : '' }}{{ billing.subscriptions.growth }}%
            </UiBadge>
          </div>
        </template>
        <div class="space-y-3">
          <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ billing.subscriptions.active }}</p>
          <p class="text-sm text-gray-500 dark:text-gray-400">
            {{ billing.subscriptions.new }} new this month
          </p>
          <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div 
              class="bg-blue-600 h-2 rounded-full transition-all duration-300"
              :style="{ width: `${(billing.subscriptions.active / (billing.subscriptions.active + billing.subscriptions.cancelled)) * 100}%` }"
            ></div>
          </div>
        </div>
      </UiCard>

      <!-- Average Revenue Per User -->
      <UiCard
        icon="material-symbols:person-pin"
        icon-color="purple"
        :loading="isLoading"
        hover-effect
      >
        <template #header>
          <div class="flex items-center justify-between w-full">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">ARPU</h3>
            <UiBadge :variant="billing.arpu.growth > 0 ? 'success' : 'neutral'">
              {{ billing.arpu.growth > 0 ? '+' : '' }}{{ billing.arpu.growth }}%
            </UiBadge>
          </div>
        </template>
        <div class="space-y-3">
          <p class="text-3xl font-bold text-gray-900 dark:text-white">${{ billing.arpu.current }}</p>
          <p class="text-sm text-gray-500 dark:text-gray-400">
            Average revenue per user
          </p>
          <div class="space-y-1">
            <div class="flex justify-between text-sm">
              <span class="text-gray-500 dark:text-gray-400">Last Month</span>
              <span class="text-gray-900 dark:text-white">${{ billing.arpu.lastMonth }}</span>
            </div>
          </div>
        </div>
      </UiCard>

      <!-- Churn Rate -->
      <UiCard
        icon="material-symbols:trending-down"
        icon-color="red"
        :loading="isLoading"
        hover-effect
      >
        <template #header>
          <div class="flex items-center justify-between w-full">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Churn Rate</h3>
            <UiBadge :variant="billing.churn.rate < 5 ? 'success' : billing.churn.rate < 10 ? 'warning' : 'error'">
              {{ billing.churn.rate }}%
            </UiBadge>
          </div>
        </template>
        <div class="space-y-3">
          <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ billing.churn.rate }}%</p>
          <p class="text-sm text-gray-500 dark:text-gray-400">
            {{ billing.churn.cancelled }} cancelled this month
          </p>
          <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div 
              :class="[
                'h-2 rounded-full transition-all duration-300',
                billing.churn.rate < 5 ? 'bg-green-600' :
                billing.churn.rate < 10 ? 'bg-yellow-600' : 'bg-red-600'
              ]"
              :style="{ width: `${Math.min(billing.churn.rate * 10, 100)}%` }"
            ></div>
          </div>
        </div>
      </UiCard>
    </div>

    <!-- Revenue Charts -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Revenue Trends -->
      <UiCard>
        <template #header>
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Revenue Trends</h3>
            <div class="flex items-center gap-2">
              <UiButton @click="refreshCharts" variant="ghost" size="sm" :loading="refreshingCharts">
                <Icon name="material-symbols:refresh" class="h-4 w-4" />
              </UiButton>
              <UiSelect
                v-model="chartTimeRange"
                :options="chartTimeRangeOptions"
                size="sm"
              />
            </div>
          </div>
        </template>
        <div class="h-80">
          <!-- Chart placeholder -->
          <div class="w-full h-full bg-gradient-to-br from-green-50 to-yellow-50 dark:from-green-900/20 dark:to-yellow-900/20 rounded-lg flex items-center justify-center">
            <div class="text-center">
              <Icon name="material-symbols:trending-up" class="h-16 w-16 text-green-500 mx-auto mb-4" />
              <p class="text-gray-600 dark:text-gray-400">Revenue Trends Chart</p>
              <p class="text-sm text-gray-500 dark:text-gray-500">Monthly recurring revenue over time</p>
            </div>
          </div>
        </div>
      </UiCard>

      <!-- Subscription Distribution -->
      <UiCard>
        <template #header>
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Plan Distribution</h3>
            <UiBadge variant="info">{{ subscriptionPlans.length }} plans</UiBadge>
          </div>
        </template>
        <div class="h-80">
          <!-- Chart placeholder -->
          <div class="w-full h-full bg-gradient-to-br from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-lg flex items-center justify-center">
            <div class="text-center">
              <Icon name="material-symbols:pie-chart" class="h-16 w-16 text-blue-500 mx-auto mb-4" />
              <p class="text-gray-600 dark:text-gray-400">Plan Distribution Chart</p>
              <p class="text-sm text-gray-500 dark:text-gray-500">Subscription plans breakdown</p>
            </div>
          </div>
        </div>
      </UiCard>
    </div>

    <!-- Subscription Plans Performance -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Plan Performance -->
      <UiCard>
        <template #header>
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Plan Performance</h3>
            <UiButton @click="viewPlanDetails" variant="ghost" size="sm">
              View Details
            </UiButton>
          </div>
        </template>
        <div class="space-y-4">
          <div
            v-for="plan in subscriptionPlans"
            :key="plan.id"
            class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors duration-200"
          >
            <div class="flex items-center gap-3">
              <div :class="[
                'w-10 h-10 rounded-full flex items-center justify-center text-white font-semibold',
                plan.color
              ]">
                {{ plan.name.charAt(0) }}
              </div>
              <div>
                <p class="text-sm font-medium text-gray-900 dark:text-white">{{ plan.name }}</p>
                <p class="text-xs text-gray-500 dark:text-gray-400">${{ plan.price }}/month</p>
              </div>
            </div>
            <div class="text-right">
              <p class="text-sm font-medium text-gray-900 dark:text-white">{{ plan.subscribers }}</p>
              <p class="text-xs text-gray-500 dark:text-gray-400">subscribers</p>
            </div>
          </div>
        </div>
      </UiCard>

      <!-- Payment Methods -->
      <UiCard>
        <template #header>
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Payment Methods</h3>
            <UiBadge variant="success">{{ billing.payments.successRate }}% success</UiBadge>
          </div>
        </template>
        <div class="space-y-4">
          <div
            v-for="method in paymentMethods"
            :key="method.type"
            class="space-y-2"
          >
            <div class="flex items-center justify-between">
              <div class="flex items-center gap-2">
                <Icon :name="method.icon" class="h-5 w-5 text-gray-400" />
                <span class="text-sm font-medium text-gray-900 dark:text-white">{{ method.name }}</span>
              </div>
              <span class="text-sm text-gray-500 dark:text-gray-400">{{ method.percentage }}%</span>
            </div>
            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div 
                :class="['h-2 rounded-full transition-all duration-300', method.color]"
                :style="{ width: `${method.percentage}%` }"
              ></div>
            </div>
          </div>
        </div>
      </UiCard>
    </div>

    <!-- Financial Insights -->
    <UiCard>
      <template #header>
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Financial Insights & Forecasts</h3>
          <UiBadge variant="info">{{ financialInsights.length }} insights</UiBadge>
        </div>
      </template>
      <div class="space-y-4">
        <div
          v-for="insight in financialInsights"
          :key="insight.id"
          class="flex items-start gap-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg"
        >
          <div :class="[
            'w-10 h-10 rounded-full flex items-center justify-center flex-shrink-0',
            insight.type === 'positive' ? 'bg-green-100 dark:bg-green-900/20' :
            insight.type === 'warning' ? 'bg-yellow-100 dark:bg-yellow-900/20' :
            insight.type === 'forecast' ? 'bg-blue-100 dark:bg-blue-900/20' :
            'bg-purple-100 dark:bg-purple-900/20'
          ]">
            <Icon :name="insight.icon" :class="[
              'h-5 w-5',
              insight.type === 'positive' ? 'text-green-600 dark:text-green-400' :
              insight.type === 'warning' ? 'text-yellow-600 dark:text-yellow-400' :
              insight.type === 'forecast' ? 'text-blue-600 dark:text-blue-400' :
              'text-purple-600 dark:text-purple-400'
            ]" />
          </div>
          <div class="flex-1">
            <div class="flex items-center gap-2 mb-1">
              <h4 class="text-sm font-medium text-gray-900 dark:text-white">{{ insight.title }}</h4>
              <UiBadge :variant="insight.type === 'positive' ? 'success' : insight.type === 'warning' ? 'warning' : 'info'" size="sm">
                {{ insight.type }}
              </UiBadge>
            </div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">{{ insight.description }}</p>
            <div class="flex items-center gap-2">
              <UiButton v-if="insight.action" @click="handleInsightAction(insight)" variant="outline" size="sm">
                {{ insight.action }}
              </UiButton>
              <span class="text-xs text-gray-500 dark:text-gray-400">
                {{ insight.impact }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </UiCard>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { PlatformRoles } from '~/app/features/auth/constants/roles'

// Page meta configuration
definePageMeta({
  layout: 'dashboard',
  title: 'Billing Analytics',
  description: 'Track revenue, subscription metrics, and financial performance',
  pageHeaderIcon: 'material-symbols:receipt-long',
  pageHeaderStats: [
    { key: 'revenue', label: 'Monthly Revenue', value: '$45.7K', color: 'green' },
    { key: 'subscriptions', label: 'Active Subs', value: '1,247', color: 'blue' },
    { key: 'arpu', label: 'ARPU', value: '$36.7', color: 'purple' },
    { key: 'churn', label: 'Churn Rate', value: '3.2%', color: 'red' }
  ],
  showRealTimeStatus: true,
  autoRefreshEnabled: true,
  refreshInterval: 300000, // 5 minutes
  middleware: ['rbac'],
  roles: [PlatformRoles.SUPER_ADMIN],
  breadcrumb: [
    { label: 'Dashboard', href: '/dashboard' },
    { label: 'Platform', href: '/dashboard/platform' },
    { label: 'Analytics', href: '/dashboard/platform/analytics' },
    { label: 'Billing' },
  ],
})

// Reactive state
const isLoading = ref(true)
const refreshingCharts = ref(false)
const generatingReport = ref(false)
const selectedPeriod = ref('monthly')
const chartTimeRange = ref('12m')
const refreshInterval = ref<NodeJS.Timeout | null>(null)

// Period options
const periodOptions = [
  { value: 'monthly', label: 'Monthly' },
  { value: 'quarterly', label: 'Quarterly' },
  { value: 'yearly', label: 'Yearly' }
]

const chartTimeRangeOptions = [
  { value: '6m', label: 'Last 6 months' },
  { value: '12m', label: 'Last 12 months' },
  { value: '24m', label: 'Last 24 months' }
]

// Billing metrics
const billing = reactive({
  revenue: {
    current: 45750,
    target: 50000,
    growth: 12.5
  },
  subscriptions: {
    active: 1247,
    new: 89,
    cancelled: 23,
    growth: 8.3
  },
  arpu: {
    current: 36.7,
    lastMonth: 34.2,
    growth: 7.3
  },
  churn: {
    rate: 3.2,
    cancelled: 23
  },
  payments: {
    successRate: 98.5
  }
})

// Subscription plans data
const subscriptionPlans = ref([
  {
    id: 1,
    name: 'Starter',
    price: 29,
    subscribers: 456,
    color: 'bg-blue-500'
  },
  {
    id: 2,
    name: 'Professional',
    price: 79,
    subscribers: 623,
    color: 'bg-purple-500'
  },
  {
    id: 3,
    name: 'Enterprise',
    price: 199,
    subscribers: 168,
    color: 'bg-yellow-500'
  }
])

// Payment methods data
const paymentMethods = ref([
  {
    type: 'credit_card',
    name: 'Credit Card',
    icon: 'material-symbols:credit-card',
    percentage: 68,
    color: 'bg-blue-600'
  },
  {
    type: 'bank_transfer',
    name: 'Bank Transfer',
    icon: 'material-symbols:account-balance',
    percentage: 22,
    color: 'bg-green-600'
  },
  {
    type: 'paypal',
    name: 'PayPal',
    icon: 'material-symbols:payments',
    percentage: 8,
    color: 'bg-yellow-600'
  },
  {
    type: 'other',
    name: 'Other',
    icon: 'material-symbols:more-horiz',
    percentage: 2,
    color: 'bg-gray-600'
  }
])

// Financial insights
const financialInsights = ref([
  {
    id: 1,
    type: 'positive',
    icon: 'material-symbols:trending-up',
    title: 'Revenue Growth Acceleration',
    description: 'Monthly recurring revenue has grown 12.5% this month, exceeding the target growth rate of 10%.',
    action: 'View Revenue Details',
    impact: 'High - Exceeding growth targets'
  },
  {
    id: 2,
    type: 'forecast',
    icon: 'material-symbols:insights',
    title: 'Q4 Revenue Forecast',
    description: 'Based on current trends, Q4 revenue is projected to reach $185K, a 15% increase from Q3.',
    action: 'View Forecast Model',
    impact: 'Medium - Strong growth trajectory'
  },
  {
    id: 3,
    type: 'warning',
    icon: 'material-symbols:warning',
    title: 'Churn Rate Increase',
    description: 'Churn rate has increased to 3.2% this month. Consider implementing retention strategies.',
    action: 'Review Churn Analysis',
    impact: 'Medium - Monitor closely'
  },
  {
    id: 4,
    type: 'opportunity',
    icon: 'material-symbols:lightbulb',
    title: 'Enterprise Plan Opportunity',
    description: 'Enterprise plan adoption is growing. Consider introducing additional enterprise features.',
    action: 'Plan Feature Roadmap',
    impact: 'High - Revenue expansion opportunity'
  },
  {
    id: 5,
    type: 'positive',
    icon: 'material-symbols:payments',
    title: 'Payment Success Rate',
    description: 'Payment success rate remains high at 98.5%, indicating healthy payment infrastructure.',
    action: null,
    impact: 'Low - Maintaining excellence'
  }
])

// Utility functions
const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('en-US').format(amount)
}

// Methods
const generateReport = async () => {
  try {
    generatingReport.value = true

    // Simulate report generation
    console.log('Generating billing report...')
    await new Promise(resolve => setTimeout(resolve, 3000))

    // In a real app, you would generate and download the report
    // const report = await $api.post('/platform/billing/reports', {
    //   period: selectedPeriod.value,
    //   format: 'pdf'
    // })

    // Create mock report data
    const reportData = {
      period: selectedPeriod.value,
      revenue: billing.revenue,
      subscriptions: billing.subscriptions,
      plans: subscriptionPlans.value,
      generatedAt: new Date().toISOString()
    }

    const jsonContent = JSON.stringify(reportData, null, 2)
    const blob = new Blob([jsonContent], { type: 'application/json' })
    const url = URL.createObjectURL(blob)

    const link = document.createElement('a')
    link.href = url
    link.download = `billing-report-${selectedPeriod.value}-${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    URL.revokeObjectURL(url)
    console.log('Billing report generated successfully')

  } catch (error) {
    console.error('Error generating report:', error)
  } finally {
    generatingReport.value = false
  }
}

const refreshCharts = async () => {
  try {
    refreshingCharts.value = true

    // Simulate API call to refresh chart data
    await new Promise(resolve => setTimeout(resolve, 1500))

    // In a real app, you would fetch fresh billing data
    // const chartData = await $api.get('/platform/billing/charts', { timeRange: chartTimeRange.value })

    console.log('Billing charts refreshed')

  } catch (error) {
    console.error('Error refreshing charts:', error)
  } finally {
    refreshingCharts.value = false
  }
}

const viewPlanDetails = () => {
  navigateTo('/dashboard/platform/billing/plans')
}

const handleInsightAction = (insight: any) => {
  console.log('Handling insight action:', insight.action)

  switch (insight.action) {
    case 'View Revenue Details':
      navigateTo('/dashboard/platform/billing/revenue')
      break
    case 'View Forecast Model':
      navigateTo('/dashboard/platform/billing/forecasts')
      break
    case 'Review Churn Analysis':
      navigateTo('/dashboard/platform/billing/churn')
      break
    case 'Plan Feature Roadmap':
      navigateTo('/dashboard/platform/billing/roadmap')
      break
    default:
      console.log('Unknown action:', insight.action)
  }
}

const fetchBillingData = async () => {
  try {
    // In a real app, fetch billing data from API
    // const data = await $api.get('/platform/billing/analytics', {
    //   period: selectedPeriod.value
    // })

    // Simulate data updates with some variation
    billing.revenue.current = Math.max(40000, billing.revenue.current + Math.floor((Math.random() - 0.5) * 2000))
    billing.subscriptions.active = Math.max(1000, billing.subscriptions.active + Math.floor((Math.random() - 0.5) * 50))
    billing.arpu.current = Math.max(30, billing.arpu.current + (Math.random() - 0.5) * 2)
    billing.churn.rate = Math.max(1, Math.min(10, billing.churn.rate + (Math.random() - 0.5) * 0.5))

  } catch (error) {
    console.error('Error fetching billing data:', error)
  }
}

// Lifecycle hooks
onMounted(async () => {
  // Initial data load
  await fetchBillingData()
  isLoading.value = false

  // Set up auto-refresh interval
  refreshInterval.value = setInterval(fetchBillingData, 300000)
})

onUnmounted(() => {
  if (refreshInterval.value) {
    clearInterval(refreshInterval.value)
  }
})
</script>

<style scoped>
/* Enhanced animations and transitions */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* Card hover effects */
.hover\:bg-gray-100:hover {
  background-color: rgb(243 244 246);
}

.dark .hover\:bg-gray-600:hover {
  background-color: rgb(75 85 99);
}

/* Progress bar animations */
.transition-all {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .grid-cols-1 {
    gap: 1rem;
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
</style>
