<template>
    <h1>users invite</h1>
</template>
<script setup lang="ts">
import { PlatformRoles } from '~/app/features/auth/constants/roles';

definePageMeta({
    layout: 'dashboard',
    title: 'Invite Users',
    description: 'Invite users to the platform',
    pageHeaderIcon: 'material-symbols:person-add',
    showRealTimeStatus: false,
    autoRefreshEnabled: false,
    refreshInterval: 0,
    isLoading: false,
    showActionsMenu: true,
    showKeyboardShortcuts: true,
    middleware: ['rbac'],
    roles: [PlatformRoles.SUPER_ADMIN],
    breadcrumb: [
        { label: 'Dashboard', href: '/dashboard' },
        { label: 'Users', href: '/dashboard/users' },
        { label: 'Invite' },
    ],
});
</script>
