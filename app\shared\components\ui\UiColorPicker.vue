<template>
  <div class="relative">
    <!-- Color Picker Trigger -->
    <button
      ref="triggerRef"
      @click="togglePicker"
      class="w-8 h-8 rounded border-2 border-gray-300 dark:border-gray-600 cursor-pointer hover:border-gray-400 dark:hover:border-gray-500 transition-colors"
      :style="{ backgroundColor: modelValue }"
      :title="modelValue"
    >
      <span class="sr-only">Select color</span>
    </button>

    <!-- Color Picker Dropdown -->
    <Teleport to="body">
      <div
        v-if="isOpen"
        ref="dropdownRef"
        class="fixed z-50 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg p-4 w-64"
        :style="dropdownStyle"
      >
        <!-- Default Colors Section -->
        <div class="mb-4">
          <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Default Color</h3>
          <div class="grid grid-cols-10 gap-1">
            <button
              v-for="color in defaultColors"
              :key="color"
              @click="selectColor(color)"
              class="w-5 h-5 rounded border border-gray-300 dark:border-gray-600 hover:scale-110 transition-transform"
              :style="{ backgroundColor: color }"
              :title="color"
            />
          </div>
        </div>

        <!-- Standard Colors Section -->
        <div class="mb-4">
          <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Standard Colors</h3>
          <div class="grid grid-cols-10 gap-1">
            <button
              v-for="color in standardColors"
              :key="color"
              @click="selectColor(color)"
              class="w-5 h-5 rounded border border-gray-300 dark:border-gray-600 hover:scale-110 transition-transform"
              :style="{ backgroundColor: color }"
              :title="color"
            />
          </div>
        </div>

        <!-- Recently Used Section -->
        <div v-if="recentColors.length > 0" class="mb-4">
          <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Recently Used</h3>
          <div class="flex gap-1">
            <button
              v-for="color in recentColors"
              :key="color"
              @click="selectColor(color)"
              class="w-5 h-5 rounded border border-gray-300 dark:border-gray-600 hover:scale-110 transition-transform"
              :style="{ backgroundColor: color }"
              :title="color"
            />
          </div>
        </div>

        <!-- More Colors Button -->
        <button
          @click="showAdvanced = !showAdvanced"
          class="flex items-center text-sm text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 mb-4"
        >
          <Icon name="heroicons:chevron-right" class="w-4 h-4 mr-1 transition-transform" :class="{ 'rotate-90': showAdvanced }" />
          More Colors
        </button>

        <!-- Advanced Color Picker -->
        <div v-if="showAdvanced" class="space-y-4">
          <!-- Color Canvas -->
          <div class="relative">
            <canvas
              ref="colorCanvasRef"
              @mousedown="startColorDrag"
              @mousemove="onColorDrag"
              @mouseup="endColorDrag"
              class="w-full h-32 cursor-crosshair border border-gray-300 dark:border-gray-600 rounded"
              width="240"
              height="128"
            />
            <div
              class="absolute w-3 h-3 border-2 border-white rounded-full pointer-events-none transform -translate-x-1/2 -translate-y-1/2"
              :style="{ left: colorPosition.x + 'px', top: colorPosition.y + 'px' }"
            />
          </div>

          <!-- Hue Slider -->
          <div class="relative">
            <canvas
              ref="hueCanvasRef"
              @mousedown="startHueDrag"
              @mousemove="onHueDrag"
              @mouseup="endHueDrag"
              class="w-full h-4 cursor-pointer border border-gray-300 dark:border-gray-600 rounded"
              width="240"
              height="16"
            />
            <div
              class="absolute w-4 h-6 bg-white border-2 border-gray-400 rounded pointer-events-none transform -translate-x-1/2 -translate-y-1"
              :style="{ left: huePosition + 'px' }"
            />
          </div>

          <!-- Color Input Fields -->
          <div class="grid grid-cols-4 gap-2 text-xs">
            <div>
              <label class="block text-gray-600 dark:text-gray-400 mb-1">R</label>
              <input
                v-model.number="rgbColor.r"
                @input="updateFromRGB"
                type="number"
                min="0"
                max="255"
                class="w-full px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              />
            </div>
            <div>
              <label class="block text-gray-600 dark:text-gray-400 mb-1">G</label>
              <input
                v-model.number="rgbColor.g"
                @input="updateFromRGB"
                type="number"
                min="0"
                max="255"
                class="w-full px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              />
            </div>
            <div>
              <label class="block text-gray-600 dark:text-gray-400 mb-1">B</label>
              <input
                v-model.number="rgbColor.b"
                @input="updateFromRGB"
                type="number"
                min="0"
                max="255"
                class="w-full px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              />
            </div>
            <div>
              <label class="block text-gray-600 dark:text-gray-400 mb-1">Hex</label>
              <input
                v-model="hexColor"
                @input="updateFromHex"
                type="text"
                class="w-full px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              />
            </div>
          </div>
        </div>
      </div>
    </Teleport>

    <!-- Backdrop -->
    <div
      v-if="isOpen"
      @click="closePicker"
      class="fixed inset-0 z-40"
    />
  </div>
</template>

<script setup lang="ts">
// Vue imports (in case auto-import doesn't work)
import { ref, computed, watch, nextTick, onMounted, onUnmounted } from 'vue'

interface Props {
  modelValue?: string
  disabled?: boolean
}

interface Emits {
  (e: 'update:modelValue', value: string): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '#3b82f6',
  disabled: false
})

const emit = defineEmits<Emits>()

// Refs
const triggerRef = ref<HTMLElement>()
const dropdownRef = ref<HTMLElement>()
const colorCanvasRef = ref<HTMLCanvasElement>()
const hueCanvasRef = ref<HTMLCanvasElement>()

// State
const isOpen = ref(false)
const showAdvanced = ref(false)
const isDraggingColor = ref(false)
const isDraggingHue = ref(false)

// Color state
const hsvColor = ref({ h: 220, s: 100, v: 100 })
const rgbColor = ref({ r: 59, g: 130, b: 246 })
const hexColor = ref('#3b82f6')
const colorPosition = ref({ x: 240, y: 0 })
const huePosition = ref(220 * 240 / 360)

// Dropdown positioning
const dropdownStyle = ref({})

// Recent colors (stored in localStorage)
const recentColors = ref<string[]>([])

// Predefined color palettes
const defaultColors = [
  '#000000', '#1f2937', '#3b82f6', '#10b981', '#ef4444', '#f97316', '#eab308', '#8b5cf6',
  '#6b7280', '#374151', '#60a5fa', '#34d399', '#f87171', '#fb923c', '#fbbf24', '#a78bfa',
  '#9ca3af', '#4b5563', '#93c5fd', '#6ee7b7', '#fca5a5', '#fdba74', '#fcd34d', '#c4b5fd',
  '#d1d5db', '#6b7280', '#dbeafe', '#d1fae5', '#fee2e2', '#fed7aa', '#fef3c7', '#e9d5ff',
  '#e5e7eb', '#9ca3af', '#eff6ff', '#ecfdf5', '#fef2f2', '#fff7ed', '#fffbeb', '#f3f4f6'
]

const standardColors = [
  '#dc2626', '#ea580c', '#d97706', '#ca8a04', '#65a30d', '#16a34a', '#059669', '#0891b2',
  '#0284c7', '#2563eb', '#4f46e5', '#7c3aed', '#9333ea', '#c026d3', '#db2777', '#e11d48'
]

// Load recent colors from localStorage
onMounted(() => {
  loadRecentColors()
  initializeFromModelValue()
  drawColorCanvas()
  drawHueCanvas()
})

// Watch for modelValue changes
watch(() => props.modelValue, (newValue) => {
  if (newValue && newValue !== hexColor.value) {
    initializeFromModelValue()
  }
})

// Initialize color from modelValue
function initializeFromModelValue() {
  if (props.modelValue) {
    hexColor.value = props.modelValue
    const rgb = hexToRgb(props.modelValue)
    if (rgb) {
      rgbColor.value = rgb
      const hsv = rgbToHsv(rgb.r, rgb.g, rgb.b)
      hsvColor.value = hsv
      updatePositions()
    }
  }
}

// Color conversion utilities
function hexToRgb(hex: string): { r: number; g: number; b: number } | null {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
  return result ? {
    r: parseInt(result[1], 16),
    g: parseInt(result[2], 16),
    b: parseInt(result[3], 16)
  } : null
}

function rgbToHex(r: number, g: number, b: number): string {
  return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1)
}

function rgbToHsv(r: number, g: number, b: number): { h: number; s: number; v: number } {
  r /= 255
  g /= 255
  b /= 255

  const max = Math.max(r, g, b)
  const min = Math.min(r, g, b)
  const diff = max - min

  let h = 0
  const s = max === 0 ? 0 : diff / max
  const v = max

  if (diff !== 0) {
    switch (max) {
      case r: h = (g - b) / diff + (g < b ? 6 : 0); break
      case g: h = (b - r) / diff + 2; break
      case b: h = (r - g) / diff + 4; break
    }
    h /= 6
  }

  return { h: h * 360, s: s * 100, v: v * 100 }
}

function hsvToRgb(h: number, s: number, v: number): { r: number; g: number; b: number } {
  h /= 360
  s /= 100
  v /= 100

  const c = v * s
  const x = c * (1 - Math.abs((h * 6) % 2 - 1))
  const m = v - c

  let r = 0, g = 0, b = 0

  if (h >= 0 && h < 1/6) { r = c; g = x; b = 0 }
  else if (h >= 1/6 && h < 2/6) { r = x; g = c; b = 0 }
  else if (h >= 2/6 && h < 3/6) { r = 0; g = c; b = x }
  else if (h >= 3/6 && h < 4/6) { r = 0; g = x; b = c }
  else if (h >= 4/6 && h < 5/6) { r = x; g = 0; b = c }
  else if (h >= 5/6 && h < 1) { r = c; g = 0; b = x }

  return {
    r: Math.round((r + m) * 255),
    g: Math.round((g + m) * 255),
    b: Math.round((b + m) * 255)
  }
}

// Update positions based on current HSV values
function updatePositions() {
  colorPosition.value = {
    x: (hsvColor.value.s / 100) * 240,
    y: (1 - hsvColor.value.v / 100) * 128
  }
  huePosition.value = (hsvColor.value.h / 360) * 240
}

// Canvas drawing functions
function drawColorCanvas() {
  nextTick(() => {
    const canvas = colorCanvasRef.value
    if (!canvas) return

    const ctx = canvas.getContext('2d')
    if (!ctx) return

    // Create gradient from white to current hue
    const hueColor = hsvToRgb(hsvColor.value.h, 100, 100)
    const hueHex = rgbToHex(hueColor.r, hueColor.g, hueColor.b)

    // Horizontal gradient (saturation)
    const satGradient = ctx.createLinearGradient(0, 0, 240, 0)
    satGradient.addColorStop(0, '#ffffff')
    satGradient.addColorStop(1, hueHex)

    ctx.fillStyle = satGradient
    ctx.fillRect(0, 0, 240, 128)

    // Vertical gradient (brightness)
    const brightGradient = ctx.createLinearGradient(0, 0, 0, 128)
    brightGradient.addColorStop(0, 'rgba(0,0,0,0)')
    brightGradient.addColorStop(1, 'rgba(0,0,0,1)')

    ctx.fillStyle = brightGradient
    ctx.fillRect(0, 0, 240, 128)
  })
}

function drawHueCanvas() {
  nextTick(() => {
    const canvas = hueCanvasRef.value
    if (!canvas) return

    const ctx = canvas.getContext('2d')
    if (!ctx) return

    const gradient = ctx.createLinearGradient(0, 0, 240, 0)
    gradient.addColorStop(0, '#ff0000')
    gradient.addColorStop(1/6, '#ffff00')
    gradient.addColorStop(2/6, '#00ff00')
    gradient.addColorStop(3/6, '#00ffff')
    gradient.addColorStop(4/6, '#0000ff')
    gradient.addColorStop(5/6, '#ff00ff')
    gradient.addColorStop(1, '#ff0000')

    ctx.fillStyle = gradient
    ctx.fillRect(0, 0, 240, 16)
  })
}

// Color picker interactions
function togglePicker() {
  if (props.disabled) return

  if (isOpen.value) {
    closePicker()
  } else {
    openPicker()
  }
}

function openPicker() {
  isOpen.value = true
  nextTick(() => {
    updateDropdownPosition()
    drawColorCanvas()
    drawHueCanvas()
  })
}

function closePicker() {
  isOpen.value = false
  showAdvanced.value = false
  isDraggingColor.value = false
  isDraggingHue.value = false
}

function updateDropdownPosition() {
  if (!triggerRef.value || !dropdownRef.value) return

  const triggerRect = triggerRef.value.getBoundingClientRect()
  const dropdownRect = dropdownRef.value.getBoundingClientRect()
  const viewportWidth = window.innerWidth
  const viewportHeight = window.innerHeight

  let left = triggerRect.left
  let top = triggerRect.bottom + 8

  // Adjust horizontal position if dropdown would overflow
  if (left + dropdownRect.width > viewportWidth) {
    left = viewportWidth - dropdownRect.width - 16
  }
  if (left < 16) {
    left = 16
  }

  // Adjust vertical position if dropdown would overflow
  if (top + dropdownRect.height > viewportHeight) {
    top = triggerRect.top - dropdownRect.height - 8
  }

  dropdownStyle.value = {
    left: `${left}px`,
    top: `${top}px`
  }
}

// Color selection
function selectColor(color: string) {
  hexColor.value = color
  const rgb = hexToRgb(color)
  if (rgb) {
    rgbColor.value = rgb
    const hsv = rgbToHsv(rgb.r, rgb.g, rgb.b)
    hsvColor.value = hsv
    updatePositions()
    drawColorCanvas()
  }

  emit('update:modelValue', color)
  addToRecentColors(color)

  if (!showAdvanced.value) {
    closePicker()
  }
}

// Update functions for different input methods
function updateFromRGB() {
  const hex = rgbToHex(rgbColor.value.r, rgbColor.value.g, rgbColor.value.b)
  hexColor.value = hex
  const hsv = rgbToHsv(rgbColor.value.r, rgbColor.value.g, rgbColor.value.b)
  hsvColor.value = hsv
  updatePositions()
  drawColorCanvas()
  emit('update:modelValue', hex)
}

function updateFromHex() {
  const rgb = hexToRgb(hexColor.value)
  if (rgb) {
    rgbColor.value = rgb
    const hsv = rgbToHsv(rgb.r, rgb.g, rgb.b)
    hsvColor.value = hsv
    updatePositions()
    drawColorCanvas()
    emit('update:modelValue', hexColor.value)
  }
}

function updateFromHSV() {
  const rgb = hsvToRgb(hsvColor.value.h, hsvColor.value.s, hsvColor.value.v)
  rgbColor.value = rgb
  hexColor.value = rgbToHex(rgb.r, rgb.g, rgb.b)
  emit('update:modelValue', hexColor.value)
}

// Drag interactions for color canvas
function startColorDrag(event: MouseEvent) {
  isDraggingColor.value = true
  onColorDrag(event)
  document.addEventListener('mousemove', onColorDrag)
  document.addEventListener('mouseup', endColorDrag)
}

function onColorDrag(event: MouseEvent) {
  if (!isDraggingColor.value || !colorCanvasRef.value) return

  const rect = colorCanvasRef.value.getBoundingClientRect()
  const x = Math.max(0, Math.min(240, event.clientX - rect.left))
  const y = Math.max(0, Math.min(128, event.clientY - rect.top))

  colorPosition.value = { x, y }

  hsvColor.value.s = (x / 240) * 100
  hsvColor.value.v = (1 - y / 128) * 100

  updateFromHSV()
}

function endColorDrag() {
  isDraggingColor.value = false
  document.removeEventListener('mousemove', onColorDrag)
  document.removeEventListener('mouseup', endColorDrag)
}

// Drag interactions for hue slider
function startHueDrag(event: MouseEvent) {
  isDraggingHue.value = true
  onHueDrag(event)
  document.addEventListener('mousemove', onHueDrag)
  document.addEventListener('mouseup', endHueDrag)
}

function onHueDrag(event: MouseEvent) {
  if (!isDraggingHue.value || !hueCanvasRef.value) return

  const rect = hueCanvasRef.value.getBoundingClientRect()
  const x = Math.max(0, Math.min(240, event.clientX - rect.left))

  huePosition.value = x
  hsvColor.value.h = (x / 240) * 360

  drawColorCanvas()
  updateFromHSV()
}

function endHueDrag() {
  isDraggingHue.value = false
  document.removeEventListener('mousemove', onHueDrag)
  document.removeEventListener('mouseup', endHueDrag)
}

// Recent colors management
function loadRecentColors() {
  try {
    const stored = localStorage.getItem('color-picker-recent')
    if (stored) {
      recentColors.value = JSON.parse(stored)
    }
  } catch (error) {
    console.warn('Failed to load recent colors:', error)
  }
}

function addToRecentColors(color: string) {
  const colors = [...recentColors.value]
  const index = colors.indexOf(color)

  if (index > -1) {
    colors.splice(index, 1)
  }

  colors.unshift(color)
  recentColors.value = colors.slice(0, 10) // Keep only last 10 colors

  try {
    localStorage.setItem('color-picker-recent', JSON.stringify(recentColors.value))
  } catch (error) {
    console.warn('Failed to save recent colors:', error)
  }
}

// Click outside to close - simple implementation
function handleClickOutside(event: Event) {
  if (!dropdownRef.value || !isOpen.value) return

  const target = event.target as Node
  if (!dropdownRef.value.contains(target) && !triggerRef.value?.contains(target)) {
    closePicker()
  }
}

// Setup click outside listener
watch(isOpen, (newValue) => {
  if (newValue) {
    document.addEventListener('click', handleClickOutside)
  } else {
    document.removeEventListener('click', handleClickOutside)
  }
})

// Cleanup on unmount
onUnmounted(() => {
  document.removeEventListener('mousemove', onColorDrag)
  document.removeEventListener('mouseup', endColorDrag)
  document.removeEventListener('mousemove', onHueDrag)
  document.removeEventListener('mouseup', endHueDrag)
  document.removeEventListener('click', handleClickOutside)
})
</script>