<!--
  Case Create Form Component
  
  Form for creating new legal cases with validation
  and client information
-->

<template>
  <div class="max-w-4xl mx-auto space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">Create New Case</h1>
        <p class="mt-1 text-sm text-gray-500">
          Enter the details for the new legal case
        </p>
      </div>
      
      <NuxtLink
        to="/dashboard/cases"
        class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200"
      >
        <Icon name="heroicons:arrow-left" class="h-4 w-4 mr-2" />
        Back to Cases
      </NuxtLink>
    </div>

    <!-- Form -->
    <form @submit.prevent="handleSubmit" class="space-y-6">
      <!-- Case Information -->
      <div class="bg-white shadow rounded-lg p-6">
        <h2 class="text-lg font-medium text-gray-900 mb-6">Case Information</h2>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Case Title -->
          <div class="md:col-span-2">
            <label for="title" class="block text-sm font-medium text-gray-700 mb-2">
              Case Title *
            </label>
            <input
              id="title"
              v-model="form.title"
              type="text"
              required
              class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              placeholder="Enter case title"
            />
          </div>

          <!-- Case Type -->
          <div>
            <label for="type" class="block text-sm font-medium text-gray-700 mb-2">
              Case Type *
            </label>
            <select
              id="type"
              v-model="form.type"
              required
              class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
            >
              <option value="">Select case type</option>
              <option value="contract">Contract Law</option>
              <option value="employment">Employment Law</option>
              <option value="corporate">Corporate Law</option>
              <option value="litigation">Litigation</option>
              <option value="intellectual-property">Intellectual Property</option>
              <option value="real-estate">Real Estate</option>
              <option value="family">Family Law</option>
              <option value="criminal">Criminal Law</option>
            </select>
          </div>

          <!-- Priority -->
          <div>
            <label for="priority" class="block text-sm font-medium text-gray-700 mb-2">
              Priority *
            </label>
            <select
              id="priority"
              v-model="form.priority"
              required
              class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
            >
              <option value="">Select priority</option>
              <option value="low">Low</option>
              <option value="medium">Medium</option>
              <option value="high">High</option>
              <option value="urgent">Urgent</option>
            </select>
          </div>

          <!-- Description -->
          <div class="md:col-span-2">
            <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
              Case Description
            </label>
            <textarea
              id="description"
              v-model="form.description"
              rows="4"
              class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              placeholder="Provide a detailed description of the case"
            ></textarea>
          </div>
        </div>
      </div>

      <!-- Client Information -->
      <div class="bg-white shadow rounded-lg p-6">
        <h2 class="text-lg font-medium text-gray-900 mb-6">Client Information</h2>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Client Name -->
          <div>
            <label for="clientName" class="block text-sm font-medium text-gray-700 mb-2">
              Client Name *
            </label>
            <input
              id="clientName"
              v-model="form.client.name"
              type="text"
              required
              class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              placeholder="Enter client name"
            />
          </div>

          <!-- Client Email -->
          <div>
            <label for="clientEmail" class="block text-sm font-medium text-gray-700 mb-2">
              Client Email *
            </label>
            <input
              id="clientEmail"
              v-model="form.client.email"
              type="email"
              required
              class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              placeholder="Enter client email"
            />
          </div>

          <!-- Client Phone -->
          <div>
            <label for="clientPhone" class="block text-sm font-medium text-gray-700 mb-2">
              Client Phone
            </label>
            <input
              id="clientPhone"
              v-model="form.client.phone"
              type="tel"
              class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              placeholder="Enter client phone number"
            />
          </div>

          <!-- Client Company -->
          <div>
            <label for="clientCompany" class="block text-sm font-medium text-gray-700 mb-2">
              Company/Organization
            </label>
            <input
              id="clientCompany"
              v-model="form.client.company"
              type="text"
              class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              placeholder="Enter company or organization"
            />
          </div>
        </div>
      </div>

      <!-- Case Details -->
      <div class="bg-white shadow rounded-lg p-6">
        <h2 class="text-lg font-medium text-gray-900 mb-6">Case Details</h2>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Start Date -->
          <div>
            <label for="startDate" class="block text-sm font-medium text-gray-700 mb-2">
              Start Date
            </label>
            <input
              id="startDate"
              v-model="form.startDate"
              type="date"
              class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
            />
          </div>

          <!-- Expected End Date -->
          <div>
            <label for="expectedEndDate" class="block text-sm font-medium text-gray-700 mb-2">
              Expected End Date
            </label>
            <input
              id="expectedEndDate"
              v-model="form.expectedEndDate"
              type="date"
              class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
            />
          </div>

          <!-- Billing Rate -->
          <div>
            <label for="billingRate" class="block text-sm font-medium text-gray-700 mb-2">
              Hourly Billing Rate ($)
            </label>
            <input
              id="billingRate"
              v-model="form.billingRate"
              type="number"
              min="0"
              step="0.01"
              class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              placeholder="0.00"
            />
          </div>

          <!-- Estimated Hours -->
          <div>
            <label for="estimatedHours" class="block text-sm font-medium text-gray-700 mb-2">
              Estimated Hours
            </label>
            <input
              id="estimatedHours"
              v-model="form.estimatedHours"
              type="number"
              min="0"
              step="0.5"
              class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              placeholder="0"
            />
          </div>
        </div>
      </div>

      <!-- Form Actions -->
      <div class="flex justify-end space-x-3">
        <NuxtLink
          to="/dashboard/cases"
          class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200"
        >
          Cancel
        </NuxtLink>
        
        <button
          type="submit"
          :disabled="isSubmitting"
          class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
        >
          <UiSpinner v-if="isSubmitting" size="sm" class="mr-2" />
          <Icon v-else name="heroicons:plus" class="h-4 w-4 mr-2" />
          {{ isSubmitting ? 'Creating...' : 'Create Case' }}
        </button>
      </div>
    </form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'

// Composables
const router = useRouter()

// State
const isSubmitting = ref(false)

const form = reactive({
  title: '',
  type: '',
  priority: '',
  description: '',
  client: {
    name: '',
    email: '',
    phone: '',
    company: ''
  },
  startDate: '',
  expectedEndDate: '',
  billingRate: '',
  estimatedHours: ''
})

// Methods
const handleSubmit = async () => {
  try {
    isSubmitting.value = true
    
    // Validate form
    if (!form.title || !form.type || !form.priority || !form.client.name || !form.client.email) {
      alert('Please fill in all required fields')
      return
    }
    
    // Create case data
    const caseData = {
      ...form,
      billingRate: form.billingRate ? parseFloat(form.billingRate) : null,
      estimatedHours: form.estimatedHours ? parseFloat(form.estimatedHours) : null,
      status: 'active',
      createdAt: new Date().toISOString()
    }
    
    // TODO: Replace with actual API call
    console.log('Creating case:', caseData)
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // Success - redirect to cases list
    router.push('/dashboard/cases')
    
  } catch (error) {
    console.error('Error creating case:', error)
    alert('Failed to create case. Please try again.')
  } finally {
    isSubmitting.value = false
  }
}
</script>
