// Global language management utilities
// This doesn't use composables so it can be used in plugins

export const RTL_LANGUAGES = ['he', 'ar']

export const isRTLLanguage = (locale: string): boolean => {
  return RTL_LANGUAGES.includes(locale)
}

export const getDirection = (locale: string): 'ltr' | 'rtl' => {
  return isRTLLanguage(locale) ? 'rtl' : 'ltr'
}

export const updateHtmlAttributes = (locale: string): void => {
  if (typeof window === 'undefined') return

  const html = document.documentElement
  const direction = getDirection(locale)
  
  // Update lang attribute
  html.setAttribute('lang', locale)
  
  // Update dir attribute
  html.setAttribute('dir', direction)
  
  // Update body classes for RTL/LTR
  document.body.classList.remove('rtl', 'ltr')
  document.body.classList.add(direction)
  
  // Update CSS custom properties for direction
  html.style.setProperty('--text-direction', direction)
  
  console.log(`Updated HTML attributes: lang="${locale}", dir="${direction}"`)
}

export const saveLanguagePreference = (locale: string): void => {
  if (typeof window === 'undefined') return
  
  try {
    localStorage.setItem('preferred-language', locale)
    console.log(`Saved language preference: ${locale}`)
  } catch (error) {
    console.warn('Failed to save language preference:', error)
  }
}

export const getLanguagePreference = (): string | null => {
  if (typeof window === 'undefined') return null
  
  try {
    return localStorage.getItem('preferred-language')
  } catch (error) {
    console.warn('Failed to get language preference:', error)
    return null
  }
}

export const initializeLanguageAttributes = (): void => {
  if (typeof window === 'undefined') return
  
  // Get current locale from HTML or default to 'en'
  const currentLocale = document.documentElement.getAttribute('lang') || 'en'
  updateHtmlAttributes(currentLocale)
  
  console.log('Language attributes initialized')
}
