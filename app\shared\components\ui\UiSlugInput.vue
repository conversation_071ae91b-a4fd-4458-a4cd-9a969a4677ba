<template>
  <UiInput
    :id="id"
    v-model="internalValue"
    :label="label"
    :placeholder="placeholder"
    :error-message="errorMessage"
    :loading="isValidating"
    :required="required"
    :helpText="helpText"
    :trailingIcon="trailingIcon"
  />
</template>

<script setup lang="ts">
import { computed, watch } from "vue";
import { useSlug } from "~/composables/useSlug";

const props = defineProps({
  id: { type: String, required: true },
  name: { type: String, required: true },
  label: { type: String, required: true },
  source: { type: String },
  required: { type: Boolean, default: false },
  placeholder: { type: String },
  helpText: { type: String },
  modelValue: { type: String, default: "" },
  errorMessage: { type: String, default: "" },
});

 

const emit = defineEmits<{
  "update:modelValue": [value: string];
}>();

const { slug, isValidating, isUnique, slugValidationRule } = useSlug(props.modelValue);

// Watch the source prop for changes and update slug accordingly

watch(
  () => props.source,
  (newValue) => {
   

    if (newValue) {
      const formatted = newValue
        .toLowerCase()
        .replace(/\s+/g, "-")
        .replace(/[^a-z0-9-]/g, "");
      slug.value = formatted;
      emit("update:modelValue", formatted);
    } else if (newValue === "") {
      slug.value = "";
      emit("update:modelValue", "");
    }
  },
  { immediate: true }
);

// Create computed property for two-way binding
const internalValue = computed({
  get: () => slug.value,
  set: (value: string) => {
    slug.value = value;
    emit("update:modelValue", value);
  },
});

// Watch for external modelValue changes
watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue !== slug.value) {
      slug.value = newValue;
    }
  }
);



const trailingIcon = computed(() => {
  if (isValidating.value) return "";
  if (isUnique.value === true) return "material-symbols:check-circle";
  if (isUnique.value === false) return "material-symbols:error";
  return "";
});

</script>
