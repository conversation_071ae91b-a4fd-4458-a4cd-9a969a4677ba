/**
 * Composable for managing legal document templates
 */
export const useLegalTemplates = () => {
  const { t } = useI18n()

  // Legal document templates
  const templates = computed(() => [
    {
      id: 'contract',
      name: t('common.templates.contract'),
      description: t('common.templates.contractDesc'),
      icon: 'material-symbols:contract',
      content: `
        <h1>${t('common.templates.contractTitle')}</h1>
        <p><strong>${t('common.templates.parties')}:</strong></p>
        <p>${t('common.templates.party1')}: ___________________</p>
        <p>${t('common.templates.party2')}: ___________________</p>
        
        <h2>${t('common.templates.terms')}</h2>
        <p>${t('common.templates.termsContent')}</p>
        
        <h2>${t('common.templates.signatures')}</h2>
        <p>${t('common.templates.signature1')}: ___________________</p>
        <p>${t('common.templates.signature2')}: ___________________</p>
        <p>${t('common.templates.date')}: ___________________</p>
      `
    },
    {
      id: 'nda',
      name: t('common.templates.nda'),
      description: t('common.templates.ndaDesc'),
      icon: 'material-symbols:security',
      content: `
        <h1>${t('common.templates.ndaTitle')}</h1>
        <p>${t('common.templates.ndaIntro')}</p>
        
        <h2>${t('common.templates.confidentialInfo')}</h2>
        <p>${t('common.templates.confidentialInfoContent')}</p>
        
        <h2>${t('common.templates.obligations')}</h2>
        <ul>
          <li>${t('common.templates.obligation1')}</li>
          <li>${t('common.templates.obligation2')}</li>
          <li>${t('common.templates.obligation3')}</li>
        </ul>
        
        <h2>${t('common.templates.signatures')}</h2>
        <p>${t('common.templates.signature1')}: ___________________</p>
        <p>${t('common.templates.signature2')}: ___________________</p>
        <p>${t('common.templates.date')}: ___________________</p>
      `
    },
    {
      id: 'letter',
      name: t('common.templates.letter'),
      description: t('common.templates.letterDesc'),
      icon: 'material-symbols:mail',
      content: `
        <div style="text-align: right; margin-bottom: 2rem;">
          <p>${t('common.templates.date')}: ___________________</p>
        </div>
        
        <div style="margin-bottom: 2rem;">
          <p><strong>${t('common.templates.to')}:</strong></p>
          <p>___________________</p>
          <p>___________________</p>
          <p>___________________</p>
        </div>
        
        <p><strong>${t('common.templates.subject')}:</strong> ___________________</p>
        
        <p>${t('common.templates.letterGreeting')},</p>
        
        <p>${t('common.templates.letterContent')}</p>
        
        <p>${t('common.templates.letterClosing')},</p>
        
        <div style="margin-top: 3rem;">
          <p>___________________</p>
          <p>${t('common.templates.signature')}</p>
        </div>
      `
    },
    {
      id: 'memo',
      name: t('common.templates.memo'),
      description: t('common.templates.memoDesc'),
      icon: 'material-symbols:note',
      content: `
        <div style="text-align: center; margin-bottom: 2rem;">
          <h1>${t('common.templates.memoTitle')}</h1>
        </div>
        
        <div style="margin-bottom: 2rem;">
          <p><strong>${t('common.templates.to')}:</strong> ___________________</p>
          <p><strong>${t('common.templates.from')}:</strong> ___________________</p>
          <p><strong>${t('common.templates.date')}:</strong> ___________________</p>
          <p><strong>${t('common.templates.subject')}:</strong> ___________________</p>
        </div>
        
        <hr style="margin: 2rem 0;">
        
        <h2>${t('common.templates.purpose')}</h2>
        <p>${t('common.templates.purposeContent')}</p>
        
        <h2>${t('common.templates.background')}</h2>
        <p>${t('common.templates.backgroundContent')}</p>
        
        <h2>${t('common.templates.recommendation')}</h2>
        <p>${t('common.templates.recommendationContent')}</p>
      `
    }
  ])

  // Get template by ID
  const getTemplate = (id: string) => {
    return templates.value.find(template => template.id === id)
  }

  // Apply template to editor
  const applyTemplate = (templateId: string, editor: any) => {
    const template = getTemplate(templateId)
    if (template && editor) {
      editor.commands.setContent(template.content)
      return true
    }
    return false
  }

  // Create blank document
  const createBlankDocument = (editor: any) => {
    if (editor) {
      editor.commands.setContent('<p></p>')
      return true
    }
    return false
  }

  return {
    templates: readonly(templates),
    getTemplate,
    applyTemplate,
    createBlankDocument
  }
}
