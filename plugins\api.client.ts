// plugins/api.client.ts
import axios from 'axios';
import type { AxiosInstance, AxiosError, InternalAxiosRequestConfig } from 'axios';
import { useAuthStore } from '~/stores/auth';
import { useToast } from '~/composables/useToast';
import { defineNuxtPlugin, useRouter, useNuxtApp } from '#app'; // Removed useRuntimeConfig as apiBase is not used here

// Define a type for your expected error response structure
interface ApiErrorData {
  statusCode: number;
  message: string | string[];
  error?: string; // Optional error type string
}

export default defineNuxtPlugin((nuxtApp) => {
  const authStore = useAuthStore();
  const { showToast } = useToast();
  const router = useRouter();
  const { $i18n } = useNuxtApp(); // Access i18n instance

  // 1. Create an Axios instance
  // All client-side API calls will be prefixed with /api/proxy
  // e.g., $api.get('/users') will request '/api/proxy/users' from the Nuxt server
  const api: AxiosInstance = axios.create({
    baseURL: '/api/proxy', 
    headers: {
      'Content-Type': 'application/json',
    },
  });

  // 2. Request Interceptor: Attach Accept-Language Header
  api.interceptors.request.use(
    (config) => {
      // Attach Accept-Language header
      const currentLocale = ($i18n as any)?.locale?.value || 'en'; // Fallback to 'en'
      config.headers['Accept-Language'] = currentLocale;

      // Authorization header is NOT set here.
      // HttpOnly access_token cookie will be sent automatically by the browser
      // for same-origin requests to the Nuxt server (/api/proxy/...).
      // The Nuxt server proxy will handle extracting it and using it.
      return config;
    },
    (error) => {
      return Promise.reject(error);
    }
  );

  // 3. Response Interceptor: Handle Errors
  api.interceptors.response.use(
    (response) => {
      return response;
    },
    async (error: AxiosError<ApiErrorData>) => {
      const originalRequest = error.config as InternalAxiosRequestConfig;

      if (!error.response) {
        // Network error (no response from server)
        showToast({
          type: 'error',
          title: 'Network Error',
          message: 'Could not connect to the server. Please check your internet connection or try again later.',
        });
        return Promise.reject(error);
      }

      const { statusCode, message, error: errorType } = error.response.data;
      console.error(`[API Client Interceptor] Response Error: Status ${statusCode} for ${originalRequest?.url}`, error.response.data);

      // If 401 from the Nuxt proxy, it means server-side refresh failed or token is invalid.
      if (statusCode === 401) {
        console.warn('[API Client Interceptor] Received 401 from proxy. Logging out.');
        // authStore.logout(); // Clear tokens and user state on the client
        router.push('/auth/login');
        showToast({
          type: 'error',
          title: 'Session Expired',
          message: 'Your session has expired. Please log in again.',
        });
        return Promise.reject(error); // Reject the original promise
      }

      // Handle other errors
      const errorMessage = Array.isArray(message) ? message.join(', ') : message || errorType || 'An unexpected error occurred.';
      showToast({
        type: 'error',
        title: `Error ${statusCode}`,
        message: errorMessage,
      });

      return Promise.reject(error); // Re-throw the error for component-level handling
    }
  );

  // Provide the Axios instance globally
  return {
    provide: {
      api: api,
    },
  };
});