/**
 * Cache Composable
 * 
 * Provides caching functionality for API requests and data
 */

import { ref, computed } from 'vue'

export enum CacheStrategy {
  MEMORY = 'memory',
  SESSION = 'session',
  LOCAL = 'local'
}

export interface CacheConfig {
  enabled: boolean
  strategy: CacheStrategy
  ttl: number // Time to live in milliseconds
  maxSize?: number
}

export interface CacheEntry<T = any> {
  data: T
  timestamp: number
  ttl: number
  key: string
}

export function useCache(config: CacheConfig = {
  enabled: true,
  strategy: CacheStrategy.MEMORY,
  ttl: 300000 // 5 minutes
}) {
  // Memory cache
  const memoryCache = new Map<string, CacheEntry>()
  
  /**
   * Generate cache key
   */
  function generateKey(prefix: string, params?: any): string {
    if (!params) return prefix
    const paramString = JSON.stringify(params)
    return `${prefix}:${btoa(paramString)}`
  }
  
  /**
   * Check if cache entry is valid
   */
  function isValid(entry: CacheEntry): boolean {
    const now = Date.now()
    return (now - entry.timestamp) < entry.ttl
  }
  
  /**
   * Get from cache
   */
  function get<T>(key: string): T | null {
    if (!config.enabled) return null
    
    let entry: CacheEntry<T> | null = null
    
    switch (config.strategy) {
      case CacheStrategy.MEMORY:
        entry = memoryCache.get(key) || null
        break
      case CacheStrategy.SESSION:
        try {
          const stored = sessionStorage.getItem(key)
          entry = stored ? JSON.parse(stored) : null
        } catch {
          entry = null
        }
        break
      case CacheStrategy.LOCAL:
        try {
          const stored = localStorage.getItem(key)
          entry = stored ? JSON.parse(stored) : null
        } catch {
          entry = null
        }
        break
    }
    
    if (!entry || !isValid(entry)) {
      remove(key)
      return null
    }
    
    return entry.data
  }
  
  /**
   * Set cache entry
   */
  function set<T>(key: string, data: T, customTtl?: number): void {
    if (!config.enabled) return
    
    const entry: CacheEntry<T> = {
      data,
      timestamp: Date.now(),
      ttl: customTtl || config.ttl,
      key
    }
    
    switch (config.strategy) {
      case CacheStrategy.MEMORY:
        // Check max size
        if (config.maxSize && memoryCache.size >= config.maxSize) {
          // Remove oldest entry
          const oldestKey = memoryCache.keys().next().value
          memoryCache.delete(oldestKey)
        }
        memoryCache.set(key, entry)
        break
      case CacheStrategy.SESSION:
        try {
          sessionStorage.setItem(key, JSON.stringify(entry))
        } catch {
          // Storage full or unavailable
        }
        break
      case CacheStrategy.LOCAL:
        try {
          localStorage.setItem(key, JSON.stringify(entry))
        } catch {
          // Storage full or unavailable
        }
        break
    }
  }
  
  /**
   * Remove from cache
   */
  function remove(key: string): void {
    switch (config.strategy) {
      case CacheStrategy.MEMORY:
        memoryCache.delete(key)
        break
      case CacheStrategy.SESSION:
        sessionStorage.removeItem(key)
        break
      case CacheStrategy.LOCAL:
        localStorage.removeItem(key)
        break
    }
  }
  
  /**
   * Clear all cache
   */
  function clear(): void {
    switch (config.strategy) {
      case CacheStrategy.MEMORY:
        memoryCache.clear()
        break
      case CacheStrategy.SESSION:
        sessionStorage.clear()
        break
      case CacheStrategy.LOCAL:
        localStorage.clear()
        break
    }
  }
  
  /**
   * Get cache size
   */
  const size = computed(() => {
    switch (config.strategy) {
      case CacheStrategy.MEMORY:
        return memoryCache.size
      case CacheStrategy.SESSION:
        return sessionStorage.length
      case CacheStrategy.LOCAL:
        return localStorage.length
      default:
        return 0
    }
  })
  
  return {
    generateKey,
    get,
    set,
    remove,
    clear,
    size,
    isValid
  }
}
