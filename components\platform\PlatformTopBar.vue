<!--
  Platform Top Bar Component
  
  Responsive top navigation bar with sidebar controls,
  search functionality, and user actions
-->

<template>
  <div class="flex items-center justify-between h-16 px-4 bg-white">
    <!-- Left section: Sidebar controls -->
    <div class="flex items-center ltr:space-x-4 rtl:space-x-reverse">
      <!-- Mobile sidebar toggle -->
      <button
        v-if="mobile"
        type="button"
        class="p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-brandPrimary-500 lg:hidden"
        @click="$emit('toggle-sidebar')"
        :aria-label="t('navigation.openSidebar')"
      >
        <Icon name="heroicons:bars-3" size="calc(var(--spacing) * 6)" />
      </button>

      <!-- Desktop sidebar collapse toggle -->
      <button
        v-if="!mobile"
        type="button"
        class="p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-brandPrimary-500"
        @click="$emit('toggle-collapse')"
        :aria-label="
          sidebarCollapsed
            ? t('navigation.expandSidebar')
            : t('navigation.collapseSidebar')
        "
      >
        <Icon
          :name="
            sidebarCollapsed
              ? isRTL
                ? 'heroicons:chevron-left'
                : 'heroicons:chevron-right'
              : isRTL
              ? 'heroicons:chevron-right'
              : 'heroicons:chevron-left'
          "
          size="calc(var(--spacing) * 6)"
        />
      </button>

      <!-- Page title or breadcrumb indicator -->
      <div class="hidden sm:block">
        <h1 class="text-lg font-semibold text-gray-900">
          {{ pageTitle }}
        </h1>
      </div>
    </div>

    <!-- Right section: Actions and user menu -->
    <div class="flex items-center ltr:space-x-4 rtl:space-x-reverse" style="min-width: 52vw;">
      <!-- Center section: Search (optional) -->
      <div class="flex-1 max-w-lg ltr:mx-4 rtl:mx-4 hidden md:block">
        <div class="relative">
          <div
            class="absolute inset-y-0 ltr:left-0 rtl:right-0 ltr:pl-3 rtl:pr-3 flex items-center pointer-events-none"
          >
            <Icon
              name="heroicons:magnifying-glass"
              size="calc(var(--spacing) * 5)"
              class="text-gray-400"
            />
          </div>
          <input
            type="search"
            :placeholder="t('common.search')"
            class="block w-full ltr:pl-10 ltr:pr-3 rtl:pr-10 rtl:pl-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-brandPrimary-500 focus:border-brandPrimary-500 sm:text-sm"
            v-model="searchQuery"
           
          />
        </div>
      </div>
      <!-- Enhanced Notifications -->
      <PlatformNotificationCenter ref="notificationCenter" />

      <!-- Quick actions dropdown -->
      <div class="relative">
        <UiButton
          @click="toggleQuickActions"
          variant="flat"
          size="lg"
          :aria-label="t('navigation.quickActions')"
          class="p-2"
        >
          <Icon name="heroicons:plus" size="calc(var(--spacing) * 6)" />
        </UiButton>

        <!-- Quick actions dropdown menu -->
        <Transition
          enter-active-class="transition ease-out duration-100"
          enter-from-class="transform opacity-0 scale-95"
          enter-to-class="transform opacity-100 scale-100"
          leave-active-class="transition ease-in duration-75"
          leave-from-class="transform opacity-100 scale-100"
          leave-to-class="transform opacity-0 scale-95"
        >
          <div
            v-if="showQuickActions"
            class="absolute ltr:right-0 rtl:left-0 mt-2 w-48 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none z-50"
            @click.stop
          >
            <div class="py-1">
              <button
                v-for="action in quickActions"
                :key="action.name"
                type="button"
                class="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                @click="handleQuickAction(action)"
              >
                <Icon
                  :name="action.icon"
                  class="ltr:mr-3 rtl:ml-3"
                  size="calc(var(--spacing) * 4)"
                />
                {{ action.name }}
              </button>
            </div>
          </div>
        </Transition>
      </div>

      <!-- User avatar and menu -->
      <div class="relative">
        <button
          type="button"
          class="flex items-center ltr:space-x-2 rtl:space-x-reverse p-2 rounded-md hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-brandPrimary-500"
          @click="toggleUserMenu"
          :aria-label="t('navigation.userMenu')"
        >
          <img class="h-8 w-8 rounded-full" :src="userAvatar" :alt="userName" />
          <span class="hidden sm:block text-sm font-medium text-gray-700">
            {{ userName }}
          </span>
          <Icon
            name="heroicons:chevron-down"
            size="calc(var(--spacing) * 5)"
            class="text-gray-400"
          />
        </button>

        <!-- User dropdown menu -->
        <Transition
          enter-active-class="transition ease-out duration-100"
          enter-from-class="transform opacity-0 scale-95"
          enter-to-class="transform opacity-100 scale-100"
          leave-active-class="transition ease-in duration-75"
          leave-from-class="transform opacity-100 scale-100"
          leave-to-class="transform opacity-0 scale-95"
        >
          <div
            v-if="showUserMenu"
            class="absolute ltr:right-0 rtl:left-0 mt-2 w-48 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none z-50"
            @click.stop
          >
            <div class="py-1">
              <NuxtLink
                to="/dashboard/profile"
                class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                @click="closeUserMenu"
              >
                <Icon
                  name="heroicons:user"
                  class="ltr:mr-3 rtl:ml-3"
                  size="calc(var(--spacing) * 4)"
                />
                {{ t("navigation.profile") }}
              </NuxtLink>
              <NuxtLink
                to="/dashboard/settings"
                class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                @click="closeUserMenu"
              >
                <Icon
                  name="heroicons:cog-6-tooth"
                  class="ltr:mr-3 rtl:ml-3"
                  size="calc(var(--spacing) * 4)"
                />
                {{ t("navigation.settings") }}
              </NuxtLink>
              <div class="border-t border-gray-100"></div>
              <button
                type="button"
                class="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                @click="handleLogout"
              >
                <Icon
                  name="heroicons:arrow-right-on-rectangle"
                  class="ltr:mr-3 rtl:ml-3"
                  size="calc(var(--spacing) * 4)"
                />
                {{ t("auth.logout") }}
              </button>
            </div>
          </div>
        </Transition>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, onUnmounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useI18n } from "vue-i18n";
import PlatformNotificationCenter from "./PlatformNotificationCenter.vue";
const { emit: globalEmit } = useEventEmitters();

// Props
interface Props {
  sidebarCollapsed?: boolean;
  mobile?: boolean;
  title?: string | Function;
}

const props = withDefaults(defineProps<Props>(), {
  sidebarCollapsed: false,
  mobile: false,
  title: () => "Dashboard",
});

// Emits
const emit = defineEmits<{
  "toggle-sidebar": [];
  "toggle-collapse": [];
}>();

// Composables
const route = useRoute();
const router = useRouter();
const { t } = useI18n();
const { locale } = useI18n();

// RTL detection
const isRTL = computed(() => {
  const rtlLocales = ["he", "ar"];
  return rtlLocales.includes(locale.value);
});

// State
const showQuickActions = ref(false);
const showUserMenu = ref(false);
const notificationCenter = ref();

// Mock user data (replace with actual auth when implemented)
const mockUser = {
  name: "SuperAdmin",
  avatar: "https://ui-avatars.com/api/?name=SuperAdmin&background=6366f1&color=fff",
};

// Computed
const pageTitle = computed(() => {
  return props.title;
});

const userName = computed(() => {
  return mockUser.name;
});

const userAvatar = computed(() => {
  return mockUser.avatar;
});

// Quick actions configuration
const quickActions = [
  { name: "New Case", icon: "heroicons:document-plus", action: "create-case" },
  { name: "New Document", icon: "heroicons:document-text", action: "create-document" },
  {
    name: "New Template",
    icon: "heroicons:clipboard-document-list",
    action: "create-template",
  },
];

// ===================================================================
// SEARCH FUNCTIONALITY
// ===================================================================
// ===================================================================
// ===================================================================

const handleSearch = (value: string) => globalEmit("global:search", { value }); // Emit the search event


const searchQuery = computed({
  get() {
    return route.query.search || '';
  },
  set(value) {
   handleSearch(value as string);
  },
});

const resetSearch = () => {
  searchQuery.value = '';
};

watch(() =>route.path, resetSearch);

// Methods
const toggleQuickActions = () => {
  showQuickActions.value = !showQuickActions.value;
  showUserMenu.value = false;
};

const toggleUserMenu = () => {
  showUserMenu.value = !showUserMenu.value;
  showQuickActions.value = false;
};

const closeUserMenu = () => {
  showUserMenu.value = false;
};



const handleQuickAction = (action: typeof quickActions[0]) => {
  console.log("Quick action:", action.action);
  showQuickActions.value = false;
  // Implement quick action handlers
};

const handleLogout = async () => {
  try {
    // TODO: Implement actual logout when auth is set up
    useAuthStore().logout();
    showUserMenu.value = false;
    // For now, just redirect to login
    router.push("/auth/login");
  } catch (error) {
    console.error("Logout error:", error);
  }
};

// Close dropdowns when clicking outside
const handleClickOutside = (event: Event) => {
  const target = event.target as Element;
  if (!target.closest(".relative")) {
    showQuickActions.value = false;
    showUserMenu.value = false;
  }
};

onMounted(() => {
  document.addEventListener("click", handleClickOutside);
});

onUnmounted(() => {
  document.removeEventListener("click", handleClickOutside);
});
</script>

<style scoped>
/* Component-specific styles if needed */
</style>
