import { defineE<PERSON><PERSON><PERSON><PERSON>, readBody, readMultipartFormData, getQuery, getRequestHeaders, setResponseStatus, send } from 'h3';
import { type H3Event } from 'h3';
import { createApiInstance } from '~/server/utils/apiInstance';
import type { AxiosRequestConfig, AxiosError } from 'axios';
import FormDataNode from 'form-data';

import { useRuntimeConfig } from '#imports';

export default defineEventHandler(async (event: H3Event) => {
  const config = useRuntimeConfig();
  const apiInstance = createApiInstance(event);

  // Extract the part of the path after /api/proxy/
  // event.context.params._ is the catch-all parameter from Nuxt/Nitro
  const path = event.context.params?._ || '';
  const method = event.method;
  const query = getQuery(event);
  const headers = getRequestHeaders(event);
  let body;

  // Handle different content types for body reading
  if (method !== 'GET' && method !== 'HEAD') {
    try {
      const contentType = headers['content-type'] || '';

      if (contentType.includes('multipart/form-data')) {
        // Handle multipart/form-data (file uploads)
        console.log('[Proxy] Processing multipart/form-data request');
        const formData = await readMultipartFormData(event);
        if (formData) {
          // Convert H3 multipart data to FormData for Axios
          // Use form-data package for Node.js compatibility
          const axiosFormData = new FormDataNode();

          console.log(`[Proxy] Processing ${formData.length} form fields`);
          for (const field of formData) {
            if (field.filename) {
              // This is a file field
              console.log(`[Proxy] Adding file field: ${field.name}, filename: ${field.filename}, size: ${field.data.length} bytes`);
              axiosFormData.append(field.name || 'file', field.data, {
                filename: field.filename,
                contentType: field.type || 'application/octet-stream'
              });
            } else {
              // This is a regular form field
              console.log(`[Proxy] Adding form field: ${field.name}`);
              axiosFormData.append(field.name || 'field', field.data.toString());
            }
          }
          body = axiosFormData;
        }
      } else {
        // Handle regular JSON/text body
        body = await readBody(event);
      }
    } catch (e) {
      // Ignore error if body is empty or not applicable
      console.warn('Error reading request body:', e);
    }
  }

  // Prepare the request config for Axios
  const requestConfig: AxiosRequestConfig = {
    method: method as any, // H3Event['method'] is uppercase, Axios expects it
    url: `${config.public.apiBase}/${path}`,
    params: query,
    headers: {}, // Initialize as empty
    data: body,
    responseType: 'arraybuffer', // Important to handle all content types correctly
    // validateStatus: () => true, // Handle all statuses manually
  };

  // Construct headers object carefully
  const forwardedHeaders: Record<string, string> = {};

  // Handle Content-Type header specially for multipart/form-data
  const contentType = headers['content-type'] || '';
  if (contentType.includes('multipart/form-data')) {
    // Don't set Content-Type for FormData - let Axios handle it with boundary
    // Axios will automatically set the correct Content-Type with boundary when using FormData
    console.log('[Proxy] Detected multipart/form-data, letting Axios handle Content-Type header');
  } else if (contentType) {
    forwardedHeaders['Content-Type'] = contentType;
  }

  if (headers['accept']) {
    forwardedHeaders['Accept'] = headers['accept'];
  }
  if (headers['accept-language']) {
    forwardedHeaders['Accept-Language'] = headers['accept-language'];
  }
  // Authorization header will be added by apiInstance interceptor

  requestConfig.headers = forwardedHeaders;


  try {
    const response = await apiInstance.request(requestConfig);
 
    
    // Set status and headers from the backend response
    setResponseStatus(event, response.status, response.statusText);
    Object.entries(response.headers).forEach(([key, value]) => {
      // Filter out headers that shouldn't be directly proxied or are handled by the server
      if (key.toLowerCase() !== 'transfer-encoding' && key.toLowerCase() !== 'connection' && key.toLowerCase() !== 'keep-alive' && key.toLowerCase() !== 'content-encoding') {
         if (value) { // Ensure value is not undefined or null
            event.node.res.setHeader(key, value as string | string[]);
         }
      }
    });
    
    // Send the response body
    // response.data is an ArrayBuffer due to responseType: 'arraybuffer'
    // h3's send can handle Buffer.
    return send(event, Buffer.from(response.data));

  } catch (error) {
    const axiosError = error as AxiosError;
    console.error(`Proxy error for ${method} /${path}:`, axiosError.message);

    if (axiosError.response) {
      setResponseStatus(event, axiosError.response.status, axiosError.response.statusText);
      Object.entries(axiosError.response.headers).forEach(([key, value]) => {
         if (key.toLowerCase() !== 'transfer-encoding' && key.toLowerCase() !== 'connection' && key.toLowerCase() !== 'keep-alive' && key.toLowerCase() !== 'content-encoding') {
            if (value) {
                event.node.res.setHeader(key, value as string | string[]);
            }
         }
      });
      // axiosError.response.data could be various types, ensure it's a Buffer if it's binary
      // For simplicity, assuming it's often JSON string or ArrayBuffer based on initial request
      const responseData = axiosError.response.data;
      if (responseData instanceof ArrayBuffer) {
        return send(event, Buffer.from(responseData));
      }
      return send(event, responseData); // send can also handle strings, objects (JSON)
    } else {
      // Network error or other issue
      setResponseStatus(event, 500, 'Proxy Internal Server Error');
      return { error: 'Proxy request failed', details: axiosError.message };
    }
  }
});