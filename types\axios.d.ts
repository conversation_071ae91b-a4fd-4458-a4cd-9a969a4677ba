// types/axios.d.ts
import 'axios';

declare module 'axios' {
  export interface InternalAxiosRequestConfig {
    _retry?: boolean;
    _isRefreshTokenRequest?: boolean;
  }
  // If you also pass these custom flags directly in AxiosRequestConfig (e.g. when calling nuxtApp.$api.post)
  // you might need to augment AxiosRequestConfig as well, though InternalAxiosRequestConfig is usually what's used by interceptors.
  export interface AxiosRequestConfig {
    _retry?: boolean;
    _isRefreshTokenRequest?: boolean;
  }
}