<template>
  <div class="space-y-6">
    <div>
      <h2 class="text-md font-semibold text-gray-800 dark:text-gray-100 mb-4">Notification Settings</h2>
    </div>

    <!-- Default Notification Preferences Section -->
    <UiCard title="Default Notification Preferences">
      <div class="space-y-4 flex justify-between items-center">
        <UiToggle v-model="defaultPreferences.emailNotifications" label="Default Email Notifications" description="Users can override this in their profile." />
        <UiToggle v-model="defaultPreferences.inAppNotifications" label="Default In-App Notifications" description="Users can override this in their profile." />
        <UiToggle v-model="defaultPreferences.allowUserCustomization" label="Allow Users to Customize Notification Channels" />
      </div>
    </UiCard>

    <!-- System Notification Settings Section -->
    <UiCard title="System Notification Settings">
      <div class="space-y-4">
        <UiToggle v-model="systemSettings.sendCriticalAlertsToAdmins" label="Send Email for Critical System Alerts to Admins" />
        <div v-if="systemSettings.sendCriticalAlertsToAdmins">
          <label for="adminAlertEmail" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Admin Email for System Alerts</label>
          <UiInput id="adminAlertEmail" :name="'adminAlertEmail'" v-model="systemSettings.adminEmail" type="email" placeholder="<EMAIL>" class="mt-1" />
        </div>
        <div>
          <label for="notificationThrottling" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Notification Throttling (Max per minute per user)</label>
          <UiInput id="notificationThrottling" :name="'notificationThrottling'" v-model="systemSettings.throttlingMaxPerMinute" type="number" placeholder="e.g., 10" class="mt-1" />
        </div>
      </div>
    </UiCard>

    <!-- Notification Templates Management Section -->
    <UiCard title="Notification Templates Management">
      <div class="space-y-2">
        <p class="text-sm text-gray-600 dark:text-gray-400">Manage detailed notification triggers and content templates for various system events.</p>
        <NuxtLink to="/dashboard/settings/notifications/triggers">
          <UiButton variant="outline">Go to Notification Triggers</UiButton>
        </NuxtLink>
      </div>
    </UiCard>

  </div>
</template>

<script setup lang="ts">
import { reactive } from 'vue';
import UiCard from '~/components/ui/UiCard.vue';
import UiToggle from '~/components/ui/UiToggle.vue';
import UiInput from '~/components/ui/UiInput.vue';
import UiButton from '~/components/ui/UiButton.vue';
// NuxtLink is globally available

// Default Notification Preferences
const defaultPreferences = reactive({
  emailNotifications: true,
  inAppNotifications: true,
  allowUserCustomization: true,
});

// System Notification Settings
const systemSettings = reactive({
  sendCriticalAlertsToAdmins: true,
  adminEmail: '',
  throttlingMaxPerMinute: 10,
});

// TODO: Load initial values from backend and implement save logic for these settings
</script>
