<!--
  Event System Demo Component
  Demonstrates how to use the event listeners and emitters system
-->

<template>
  <div class="p-6 bg-white rounded-lg shadow-lg">
    <h2 class="text-2xl font-bold mb-6 text-gray-900">Event System Demo</h2>
    
    <!-- Event Listeners Status -->
    <div class="mb-6">
      <h3 class="text-lg font-semibold mb-3 text-gray-800">Event Listeners Status</h3>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div class="bg-blue-50 p-4 rounded-lg">
          <div class="text-sm text-blue-600 font-medium">Listening</div>
          <div class="text-2xl font-bold text-blue-900">
            {{ isListening ? 'Active' : 'Paused' }}
          </div>
        </div>
        <div class="bg-green-50 p-4 rounded-lg">
          <div class="text-sm text-green-600 font-medium">Active Listeners</div>
          <div class="text-2xl font-bold text-green-900">{{ listenerCount }}</div>
        </div>
        <div class="bg-purple-50 p-4 rounded-lg">
          <div class="text-sm text-purple-600 font-medium">Events Tracked</div>
          <div class="text-2xl font-bold text-purple-900">{{ activeEvents.length }}</div>
        </div>
      </div>
    </div>

    <!-- Event Controls -->
    <div class="mb-6">
      <h3 class="text-lg font-semibold mb-3 text-gray-800">Event Controls</h3>
      <div class="flex flex-wrap gap-3">
        <UiButton 
          @click="isListening ? pause() : resume()"
          :variant="isListening ? 'outline' : 'contained'"
          color="blue"
        >
          {{ isListening ? 'Pause Listeners' : 'Resume Listeners' }}
        </UiButton>
        
        <UiButton @click="clearListeners" variant="outline" color="red">
          Clear All Listeners
        </UiButton>
        
        <UiButton @click="toggleRealtimeConnection" :variant="realtimeConnected ? 'outline' : 'contained'" color="green">
          {{ realtimeConnected ? 'Disconnect' : 'Connect' }} Realtime
        </UiButton>
      </div>
    </div>

    <!-- Event Emitters -->
    <div class="mb-6">
      <h3 class="text-lg font-semibold mb-3 text-gray-800">Test Event Emitters</h3>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <!-- Case Events -->
        <div class="bg-gray-50 p-4 rounded-lg">
          <h4 class="font-medium text-gray-900 mb-3">Case Events</h4>
          <div class="space-y-2">
            <UiButton @click="emitCaseCreated" size="sm" variant="outline" full-width>
              Emit Case Created
            </UiButton>
            <UiButton @click="emitCaseUpdated" size="sm" variant="outline" full-width>
              Emit Case Updated
            </UiButton>
            <UiButton @click="emitCaseStatusChanged" size="sm" variant="outline" full-width>
              Emit Status Changed
            </UiButton>
          </div>
        </div>

        <!-- Document Events -->
        <div class="bg-gray-50 p-4 rounded-lg">
          <h4 class="font-medium text-gray-900 mb-3">Document Events</h4>
          <div class="space-y-2">
            <UiButton @click="emitDocumentUploaded" size="sm" variant="outline" full-width>
              Emit Document Uploaded
            </UiButton>
            <UiButton @click="emitDocumentShared" size="sm" variant="outline" full-width>
              Emit Document Shared
            </UiButton>
            <UiButton @click="emitDocumentDeleted" size="sm" variant="outline" full-width>
              Emit Document Deleted
            </UiButton>
          </div>
        </div>

        <!-- UI Events -->
        <div class="bg-gray-50 p-4 rounded-lg">
          <h4 class="font-medium text-gray-900 mb-3">UI Events</h4>
          <div class="space-y-2">
            <UiButton @click="emitSearch" size="sm" variant="outline" full-width>
              Emit Search
            </UiButton>
            <UiButton @click="emitFilter" size="sm" variant="outline" full-width>
              Emit Filter
            </UiButton>
            <UiButton @click="emitBulkAction" size="sm" variant="outline" full-width>
              Emit Bulk Action
            </UiButton>
          </div>
        </div>
      </div>
    </div>

    <!-- Event Log -->
    <div class="mb-6">
      <div class="flex items-center justify-between mb-3">
        <h3 class="text-lg font-semibold text-gray-800">Event Log</h3>
        <UiButton @click="clearEventLog" size="sm" variant="ghost">
          Clear Log
        </UiButton>
      </div>
      <div class="bg-gray-900 text-green-400 p-4 rounded-lg h-64 overflow-y-auto font-mono text-sm">
        <div v-if="eventLog.length === 0" class="text-gray-500">
          No events logged yet. Try emitting some events above.
        </div>
        <div v-for="(event, index) in eventLog" :key="index" class="mb-2">
          <span class="text-blue-400">{{ event.timestamp }}</span>
          <span class="text-yellow-400 mx-2">{{ event.type }}</span>
          <span class="text-green-400">{{ event.summary }}</span>
        </div>
      </div>
    </div>

    <!-- Active Events List -->
    <div>
      <h3 class="text-lg font-semibold mb-3 text-gray-800">Active Event Listeners</h3>
      <div class="bg-gray-50 p-4 rounded-lg">
        <div v-if="activeEvents.length === 0" class="text-gray-500">
          No active event listeners
        </div>
        <div v-else class="flex flex-wrap gap-2">
          <span 
            v-for="event in activeEvents" 
            :key="event"
            class="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium"
          >
            {{ event }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useEventListeners } from '~/composables/useEventListeners'
import { useEventEmitters } from '~/composables/useEventEmitters'
import { useRealtimeEvents } from '~/composables/useRealtimeEvents'

// ============================================================================
// COMPOSABLES
// ============================================================================

const { 
  on, 
  once, 
  off, 
  emit, 
  clear, 
  pause, 
  resume, 
  isListening, 
  listenerCount, 
  activeEvents 
} = useEventListeners()

const {
  emitCaseCreated: _emitCaseCreated,
  emitCaseUpdated: _emitCaseUpdated,
  emitCaseStatusChanged: _emitCaseStatusChanged,
  emitDocumentUploaded: _emitDocumentUploaded,
  emitDocumentShared: _emitDocumentShared,
  emitDocumentDeleted: _emitDocumentDeleted,
  emitSearch: _emitSearch,
  emitFilter: _emitFilter,
  emitBulkAction: _emitBulkAction
} = useEventEmitters()

const {
  isConnected: realtimeConnected,
  connect: connectRealtime,
  disconnect: disconnectRealtime
} = useRealtimeEvents({
  websocketUrl: 'ws://localhost:3001/ws',
  enableLogging: true
})

// ============================================================================
// STATE
// ============================================================================

const eventLog = ref<Array<{ timestamp: string; type: string; summary: string }>>([])

// ============================================================================
// METHODS
// ============================================================================

const logEvent = (type: string, summary: string) => {
  eventLog.value.unshift({
    timestamp: new Date().toLocaleTimeString(),
    type,
    summary
  })
  
  // Keep only last 50 events
  if (eventLog.value.length > 50) {
    eventLog.value = eventLog.value.slice(0, 50)
  }
}

const clearEventLog = () => {
  eventLog.value = []
}

const clearListeners = () => {
  clear()
  logEvent('SYSTEM', 'All event listeners cleared')
}

const toggleRealtimeConnection = async () => {
  if (realtimeConnected.value) {
    disconnectRealtime()
    logEvent('REALTIME', 'Disconnected from realtime events')
  } else {
    try {
      await connectRealtime()
      logEvent('REALTIME', 'Connected to realtime events')
    } catch (error) {
      logEvent('ERROR', 'Failed to connect to realtime events')
    }
  }
}

// ============================================================================
// EVENT EMITTERS
// ============================================================================

const emitCaseCreated = () => {
  _emitCaseCreated({
    caseId: `case-${Date.now()}`,
    caseTitle: 'Demo Case',
    caseType: 'litigation',
    clientId: 'client-123',
    assignedTo: ['user-1', 'user-2']
  })
  logEvent('EMIT', 'Case created event emitted')
}

const emitCaseUpdated = () => {
  _emitCaseUpdated({
    caseId: 'case-123',
    caseTitle: 'Updated Demo Case',
    changes: { status: 'in-progress', priority: 'high' },
    updatedBy: 'user-1'
  })
  logEvent('EMIT', 'Case updated event emitted')
}

const emitCaseStatusChanged = () => {
  _emitCaseStatusChanged({
    caseId: 'case-123',
    previousStatus: 'draft',
    newStatus: 'active',
    reason: 'Client approved'
  })
  logEvent('EMIT', 'Case status changed event emitted')
}

const emitDocumentUploaded = () => {
  _emitDocumentUploaded({
    documentId: `doc-${Date.now()}`,
    documentName: 'demo-document.pdf',
    documentType: 'contract',
    fileSize: 1024000,
    mimeType: 'application/pdf',
    uploadedBy: 'user-1',
    caseId: 'case-123'
  })
  logEvent('EMIT', 'Document uploaded event emitted')
}

const emitDocumentShared = () => {
  _emitDocumentShared({
    documentId: 'doc-123',
    documentName: 'shared-document.pdf',
    sharedWith: ['user-2', 'user-3'],
    permissions: ['read', 'comment'],
    sharedBy: 'user-1'
  })
  logEvent('EMIT', 'Document shared event emitted')
}

const emitDocumentDeleted = () => {
  _emitDocumentDeleted({
    documentId: 'doc-456',
    documentName: 'deleted-document.pdf'
  })
  logEvent('EMIT', 'Document deleted event emitted')
}

const emitSearch = () => {
  _emitSearch({
    component: 'demo-component',
    action: 'search',
    query: 'legal documents',
    filters: { type: 'contract', status: 'active' },
    resultsCount: 42
  })
  logEvent('EMIT', 'Search event emitted')
}

const emitFilter = () => {
  _emitFilter({
    component: 'demo-component',
    action: 'filter',
    filterType: 'status',
    filterValue: 'active',
    activeFilters: { status: 'active', type: 'contract' }
  })
  logEvent('EMIT', 'Filter event emitted')
}

const emitBulkAction = () => {
  _emitBulkAction({
    component: 'demo-component',
    action: 'bulk-delete',
    itemCount: 5,
    itemIds: ['item-1', 'item-2', 'item-3', 'item-4', 'item-5'],
    entityType: 'documents'
  })
  logEvent('EMIT', 'Bulk action event emitted')
}

// ============================================================================
// LIFECYCLE
// ============================================================================

onMounted(() => {
  // Set up event listeners for demonstration
  on('case:created', (data) => {
    logEvent('RECEIVED', `Case created: ${data.caseTitle} (${data.caseId})`)
  })

  on('case:updated', (data) => {
    logEvent('RECEIVED', `Case updated: ${data.caseId} by ${data.updatedBy}`)
  })

  on('case:status-changed', (data) => {
    logEvent('RECEIVED', `Case status: ${data.previousStatus} → ${data.newStatus}`)
  })

  on('document:uploaded', (data) => {
    logEvent('RECEIVED', `Document uploaded: ${data.documentName} (${data.fileSize} bytes)`)
  })

  on('document:shared', (data) => {
    logEvent('RECEIVED', `Document shared: ${data.documentName} with ${data.sharedWith.length} users`)
  })

  on('document:deleted', (data) => {
    logEvent('RECEIVED', `Document deleted: ${data.documentName}`)
  })

  on('ui:search', (data) => {
    logEvent('RECEIVED', `Search performed: "${data.query}" (${data.resultsCount} results)`)
  })

  on('ui:filter', (data) => {
    logEvent('RECEIVED', `Filter applied: ${data.filterType} = ${data.filterValue}`)
  })

  on('ui:bulk-action', (data) => {
    logEvent('RECEIVED', `Bulk action: ${data.action} on ${data.itemCount} ${data.entityType}`)
  })

  on('realtime:connected', (data) => {
    logEvent('REALTIME', `Connected to ${data.channel}`)
  })

  on('realtime:disconnected', (data) => {
    logEvent('REALTIME', `Disconnected from ${data.channel}`)
  })

  on('realtime:message', (data) => {
    logEvent('REALTIME', `Message received: ${data.eventType}`)
  })

  logEvent('SYSTEM', 'Event system demo initialized')
})
</script>
