/**
 * Monitoring Plugin
 * 
 * Initializes comprehensive monitoring and analytics systems
 * for the Legal SaaS Frontend application
 */

import { defineNuxtPlugin, useRuntimeConfig, type NuxtApp } from '#app'
import { useAnalytics } from '@shared/composables/monitoring/useAnalytics'
import { usePerformanceMonitoring } from '@shared/composables/monitoring/usePerformanceMonitoring'
import { useErrorTracking } from '@shared/composables/monitoring/useErrorTracking'
import { useLogger } from '@shared/composables/core/useLogger'

export default defineNuxtPlugin({
  name: 'monitoring',
  parallel: true,
  async setup(nuxtApp: NuxtApp) {
    // Only run on client side
    if (typeof window === 'undefined') return // Corrected server check
    
    const logger = useLogger('MonitoringPlugin')
    const config = useRuntimeConfig()
    
    try {
      logger.info('Initializing monitoring systems')
      
      // ============================================================================
      // ANALYTICS SETUP
      // ============================================================================
      
      const analytics = useAnalytics({
        enabled: config.public.analytics?.enabled ?? true,
        debug: config.public.analytics?.debug ?? (process.env.NODE_ENV === 'development'),
        trackingId: config.public.analytics?.trackingId,
        apiEndpoint: config.public.analytics?.endpoint,
        enableAutoTracking: true,
        enablePerformanceTracking: true,
        enableErrorTracking: true,
        enableUserTracking: true,
        consentRequired: config.public.analytics?.consentRequired ?? true
      })
      
      // Grant consent in development
      if (process.env.NODE_ENV === 'development') {
        analytics.grantConsent()
      }
      
      // ============================================================================
      // PERFORMANCE MONITORING SETUP
      // ============================================================================
      
      const performanceMonitoring = usePerformanceMonitoring({
        enabled: config.public.performance?.enabled ?? true,
        enableCoreWebVitals: true,
        enableResourceTracking: true,
        enableMemoryMonitoring: true,
        enableUserTiming: true,
        enableNavigationTiming: true,
        reportingInterval: config.public.performance?.reportingInterval ?? 30000,
        thresholds: {
          lcp: { good: 2500, poor: 4000 },
          fid: { good: 100, poor: 300 },
          cls: { good: 0.1, poor: 0.25 },
          ttfb: { good: 800, poor: 1800 },
          fcp: { good: 1800, poor: 3000 },
          memoryUsage: { warning: 70, critical: 90 }
        }
      })
      
      // ============================================================================
      // ERROR TRACKING SETUP
      // ============================================================================
      
      const errorTracking = useErrorTracking({
        enabled: config.public.errorTracking?.enabled ?? true,
        autoCapture: true,
        captureUnhandledRejections: true,
        captureConsoleErrors: process.env.NODE_ENV === 'development',
        enableUserFeedback: true,
        enableRecoveryStrategies: true,
        maxErrorsPerSession: 50,
        reportingEndpoint: config.public.errorTracking?.endpoint,
        beforeSend: (error: Error) => { // Typed error
          // Filter out development errors in production
          if (process.env.NODE_ENV === 'production' &&
              error.message.includes('development')) {
            return null
          }
          return error
        }
      })
      
      // ============================================================================
      // ROUTER INTEGRATION
      // ============================================================================
      
      // Track page views on route changes
      nuxtApp.hook('page:start', () => {
        analytics.page()
        errorTracking.trackNavigation(nuxtApp.$router.currentRoute.value.path)
      })
      
      nuxtApp.hook('page:finish', () => {
        // Track page load performance
        // const timing = performanceMonitoring.startTiming('page_load') // Unused variable
        performanceMonitoring.startTiming('page_load'); // Start timing
        setTimeout(() => {
          performanceMonitoring.endTiming('page_load');
        }, 100);
      });
      
      // Track navigation errors
      nuxtApp.hook('page:loading:end', () => {
        // Page loaded successfully
      })
      
      // ============================================================================
      // API INTEGRATION
      // ============================================================================
      
      // Track API calls and errors
      nuxtApp.hook('app:error', (error: Error) => { // Typed error
        errorTracking.captureException(error, {
          feature: 'app',
          action: 'global_error'
        })
      })
      
      // ============================================================================
      // USER INTERACTION TRACKING
      // ============================================================================
      
      // Track user interactions
      const trackUserInteraction = (event: Event) => {
        const target = event.target as HTMLElement
        const action = event.type
        const targetInfo = target.tagName.toLowerCase() + 
          (target.id ? `#${target.id}` : '') +
          (target.className ? `.${target.className.split(' ').join('.')}` : '')
        
        errorTracking.trackUserAction(action, targetInfo)
        
        // Track specific business actions
        if (target.dataset.track) {
          analytics.trackUIEvent(
            target.dataset.component || 'unknown',
            target.dataset.track,
            {
              element: targetInfo,
              page: nuxtApp.$router.currentRoute.value.path
            }
          )
        }
      }
      
      // Add global event listeners
      document.addEventListener('click', trackUserInteraction, { passive: true })
      document.addEventListener('submit', trackUserInteraction, { passive: true })
      
      // ============================================================================
      // BUSINESS EVENT TRACKING
      // ============================================================================
      
      // Provide global tracking methods
      nuxtApp.provide('analytics', analytics)
      nuxtApp.provide('performanceMonitoring', performanceMonitoring)
      nuxtApp.provide('errorTracking', errorTracking)
      
      // Global tracking helpers
      nuxtApp.provide('trackEvent', (name: string, properties?: any) => {
        analytics.track(name, properties)
      })
      
      nuxtApp.provide('trackCaseEvent', (action: string, caseId: string, properties?: any) => {
        analytics.trackCaseEvent(action, caseId, properties)
      })
      
      nuxtApp.provide('trackDocumentEvent', (action: string, documentId: string, properties?: any) => {
        analytics.trackDocumentEvent(action, documentId, properties)
      })
      
      nuxtApp.provide('trackBillingEvent', (action: string, properties?: any) => {
        analytics.trackBillingEvent(action, properties)
      })
      
      nuxtApp.provide('trackPerformance', (name: string, value: number) => {
        performanceMonitoring.recordMetric(name, value)
      })
      
      nuxtApp.provide('captureError', (error: Error, context?: any) => {
        errorTracking.captureException(error, context)
      })
      
      // ============================================================================
      // FEATURE FLAG INTEGRATION
      // ============================================================================
      
      // Track feature flag usage
      nuxtApp.hook('feature-flag:evaluated', (flagName: string, value: boolean, context: any) => {
        analytics.track('feature_flag_evaluated', {
          flagName,
          value,
          context
        })
      })
      
      // ============================================================================
      // A/B TEST INTEGRATION
      // ============================================================================
      
      // Track A/B test assignments
      nuxtApp.hook('ab-test:assigned', (testName: string, variant: string, context: any) => {
        analytics.track('ab_test_assigned', {
          testName,
          variant,
          context
        })
        
        // Set user properties for segmentation
        analytics.identify(context.userId, {
          [`ab_test_${testName}`]: variant
        })
      })
      
      // ============================================================================
      // CONSENT MANAGEMENT
      // ============================================================================
      
      // Handle consent changes
      nuxtApp.hook('consent:granted', (consentTypes: string[]) => {
        if (consentTypes.includes('analytics')) {
          analytics.grantConsent()
        }
      })
      
      nuxtApp.hook('consent:revoked', (consentTypes: string[]) => {
        if (consentTypes.includes('analytics')) {
          analytics.revokeConsent()
        }
      })
      
      // ============================================================================
      // DEVELOPMENT HELPERS
      // ============================================================================
      
      if (process.env.NODE_ENV === 'development') {
        // Expose monitoring tools to window for debugging
        ;(window as any).__monitoring = {
          analytics,
          performanceMonitoring,
          errorTracking,
          
          // Helper methods
          trackEvent: analytics.track,
          captureError: errorTracking.captureException,
          getPerformanceReport: performanceMonitoring.generateReport,
          
          // Debug methods
          exportErrors: errorTracking.exportErrors,
          getMetrics: () => ({
            analytics: {
              sessionId: analytics.getSessionId(),
              canTrack: analytics.canTrack
            },
            performance: {
              score: performanceMonitoring.overallScore,
              grade: performanceMonitoring.performanceGrade,
              vitals: performanceMonitoring.coreWebVitals
            },
            errors: {
              total: errorTracking.errors.length,
              critical: errorTracking.criticalErrors.length,
              recent: errorTracking.recentErrors.length
            }
          })
        }
        
        logger.info('Monitoring tools exposed to window.__monitoring')
      }
      
      // ============================================================================
      // CLEANUP
      // ============================================================================
      
      nuxtApp.hook('app:beforeMount', () => {
        // Final setup before app mounts
        logger.info('Monitoring systems ready')
      })
      
      nuxtApp.hook('app:beforeUnmount', () => {
        // Cleanup event listeners
        document.removeEventListener('click', trackUserInteraction)
        document.removeEventListener('submit', trackUserInteraction)
        
        // Flush any pending data
        analytics.flush()
        
        logger.info('Monitoring systems cleaned up')
      })
      
      logger.info('Monitoring plugin initialized successfully')
      
    } catch (error) {
      logger.error('Failed to initialize monitoring plugin', { error })
      
      // Don't let monitoring failures break the app
      console.warn('Monitoring initialization failed:', error)
    }
  }
})

// ============================================================================
// TYPE DECLARATIONS
// ============================================================================

declare module '#app' {
  interface NuxtApp {
    $analytics: ReturnType<typeof useAnalytics>
    $performanceMonitoring: ReturnType<typeof usePerformanceMonitoring>
    $errorTracking: ReturnType<typeof useErrorTracking>
    $trackEvent: (name: string, properties?: any) => void
    $trackCaseEvent: (action: string, caseId: string, properties?: any) => void
    $trackDocumentEvent: (action: string, documentId: string, properties?: any) => void
    $trackBillingEvent: (action: string, properties?: any) => void
    $trackPerformance: (name: string, value: number) => void
    $captureError: (error: Error, context?: any) => void
  }
}

declare module 'vue' {
  interface ComponentCustomProperties {
    $analytics: ReturnType<typeof useAnalytics>
    $performanceMonitoring: ReturnType<typeof usePerformanceMonitoring>
    $errorTracking: ReturnType<typeof useErrorTracking>
    $trackEvent: (name: string, properties?: any) => void
    $trackCaseEvent: (action: string, caseId: string, properties?: any) => void
    $trackDocumentEvent: (action: string, documentId: string, properties?: any) => void
    $trackBillingEvent: (action: string, properties?: any) => void
    $trackPerformance: (name: string, value: number) => void
    $captureError: (error: Error, context?: any) => void
  }
}
