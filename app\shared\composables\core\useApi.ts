/**
 * Enhanced API Composable
 * 
 * Comprehensive API client with type safety, caching, error handling,
 * retry logic, and performance optimizations
 */

import { ref, computed, onUnmounted, readonly } from 'vue' // Removed watch, added readonly
import { useRuntimeConfig } from '#app'; // Import useRuntimeConfig
import {
  type ApiRequestConfig,
  type EnhancedApiResponse,
  type ApiError
  // ErrorRecoveryStrategy, // Unused
  // HttpMethod // Unused
} from '../../types/api'
import { useLogger } from './useLogger'
import { useCache, CacheStrategy } from './useCache'
import { useRetry } from './useRetry'

// ============================================================================
// API CLIENT CONFIGURATION
// ============================================================================

interface ApiClientConfig {
  baseURL: string
  timeout: number
  retries: number
  retryDelay: number
  headers: Record<string, string>
  cache: {
    enabled: boolean
    strategy: CacheStrategy
    ttl: number
  }
  auth: {
    enabled: boolean
    tokenKey: string
    refreshTokenKey: string
  }
  interceptors: {
    request: RequestInterceptor[]
    response: ResponseInterceptor[]
  }
}

type RequestInterceptor = (config: ApiRequestConfig) => ApiRequestConfig | Promise<ApiRequestConfig>
type ResponseInterceptor = (response: EnhancedApiResponse) => EnhancedApiResponse | Promise<EnhancedApiResponse>

// ============================================================================
// API COMPOSABLE IMPLEMENTATION
// ============================================================================

export function useApi(config: Partial<ApiClientConfig> = {}) {
  // ============================================================================
  // CONFIGURATION
  // ============================================================================
  
  const defaultConfig: ApiClientConfig = {
    baseURL: useRuntimeConfig().public.apiBase || 'http://localhost:3000',
    timeout: 30000,
    retries: 3,
    retryDelay: 1000,
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    },
    cache: {
      enabled: true,
      strategy: CacheStrategy.MEMORY,
      ttl: 300000 // 5 minutes
    },
    auth: {
      enabled: true,
      tokenKey: 'access_token',
      refreshTokenKey: 'refresh_token'
    },
    interceptors: {
      request: [],
      response: []
    }
  }
  
  const apiConfig = ref({ ...defaultConfig, ...config })
  
  // ============================================================================
  // DEPENDENCIES
  // ============================================================================
  
  const logger = useLogger('ApiClient')
  const cache = useCache()
  const retry = useRetry()
  
  // ============================================================================
  // STATE
  // ============================================================================
  
  const isLoading = ref(false)
  const error = ref<ApiError | null>(null)
  const lastResponse = ref<EnhancedApiResponse | null>(null)
  const requestCount = ref(0)
  const errorCount = ref(0)
  
  // Active requests for cancellation
  const activeRequests = ref(new Map<string, AbortController>())
  
  // ============================================================================
  // COMPUTED
  // ============================================================================
  
  const isOnline = computed(() => {
    if (typeof navigator === 'undefined') return true
    return navigator.onLine
  })
  
  const hasActiveRequests = computed(() => activeRequests.value.size > 0)
  
  const errorRate = computed(() => {
    if (requestCount.value === 0) return 0
    return (errorCount.value / requestCount.value) * 100
  })
  
  // ============================================================================
  // REQUEST INTERCEPTORS
  // ============================================================================
  
  const applyRequestInterceptors = async (config: ApiRequestConfig): Promise<ApiRequestConfig> => {
    let processedConfig = { ...config }
    
    // Apply built-in interceptors
    processedConfig = await applyAuthInterceptor(processedConfig)
    processedConfig = await applyHeadersInterceptor(processedConfig)
    
    // Apply custom interceptors
    for (const interceptor of apiConfig.value.interceptors.request) {
      processedConfig = await interceptor(processedConfig)
    }
    
    return processedConfig
  }
  
  const applyAuthInterceptor = async (config: ApiRequestConfig): Promise<ApiRequestConfig> => {
    if (!apiConfig.value.auth.enabled) return config
    
    // Get token from storage or auth store
    const token = getAuthToken()
    if (token) {
      config.headers = {
        ...config.headers,
        'Authorization': `Bearer ${token}`
      }
    }
    
    return config
  }
  
  const applyHeadersInterceptor = async (config: ApiRequestConfig): Promise<ApiRequestConfig> => {
    config.headers = {
      ...apiConfig.value.headers,
      ...config.headers,
      'X-Request-ID': generateRequestId(),
      'X-Client-Version': getClientVersion(),
      'X-Timestamp': new Date().toISOString()
    }
    
    return config
  }
  
  // ============================================================================
  // RESPONSE INTERCEPTORS
  // ============================================================================
  
  const applyResponseInterceptors = async (response: EnhancedApiResponse): Promise<EnhancedApiResponse> => {
    let processedResponse = { ...response }
    
    // Apply built-in interceptors
    processedResponse = await applyErrorInterceptor(processedResponse)
    processedResponse = await applyCacheInterceptor(processedResponse)
    
    // Apply custom interceptors
    for (const interceptor of apiConfig.value.interceptors.response) {
      processedResponse = await interceptor(processedResponse)
    }
    
    return processedResponse
  }
  
  const applyErrorInterceptor = async (response: EnhancedApiResponse): Promise<EnhancedApiResponse> => {
    if (response.status >= 400) {
      errorCount.value++
      
      // Handle specific error cases
      if (response.status === 401) {
        await handleUnauthorized()
      } else if (response.status === 403) {
        await handleForbidden()
      } else if (response.status >= 500) {
        await handleServerError(response)
      }
    }
    
    return response
  }
  
  const applyCacheInterceptor = async (response: EnhancedApiResponse): Promise<EnhancedApiResponse> => {
    if (apiConfig.value.cache.enabled && response.status === 200) {
      const cacheKey = generateCacheKey(response.config)
      await cache.set(cacheKey, response.data, apiConfig.value.cache.ttl)
      response.cached = false
    }
    
    return response
  }
  
  // ============================================================================
  // CORE REQUEST METHOD
  // ============================================================================
  
  const request = async <T = any>(config: ApiRequestConfig): Promise<EnhancedApiResponse<T>> => {
    const startTime = Date.now()
    const requestId = generateRequestId()
    
    try {
      isLoading.value = true
      error.value = null
      requestCount.value++
      
      logger.debug('API request started', { requestId, config })
      
      // Apply request interceptors
      const processedConfig = await applyRequestInterceptors(config)
      
      // Check cache first
      if (processedConfig.method === 'GET' && apiConfig.value.cache.enabled) {
        const cacheKey = generateCacheKey(processedConfig)
        const cachedData = await cache.get<T>(cacheKey)
        
        if (cachedData) {
          logger.debug('Cache hit', { requestId, cacheKey })
          
          const cachedResponse: EnhancedApiResponse<T> = {
            data: cachedData,
            status: 200,
            statusText: 'OK',
            headers: {},
            config: processedConfig,
            success: true,
            duration: Date.now() - startTime,
            cached: true,
            retryCount: 0
          }
          
          lastResponse.value = cachedResponse
          return cachedResponse
        }
      }
      
      // Create abort controller for cancellation
      const abortController = new AbortController()
      activeRequests.value.set(requestId, abortController)
      
      // Build fetch options
      const fetchOptions: RequestInit = {
        method: processedConfig.method,
        headers: processedConfig.headers,
        signal: abortController.signal
      }
      
      // Add body for non-GET requests
      if (processedConfig.method !== 'GET' && processedConfig.data) {
        if (processedConfig.headers?.['Content-Type']?.includes('application/json')) {
          fetchOptions.body = JSON.stringify(processedConfig.data)
        } else {
          fetchOptions.body = processedConfig.data
        }
      }
      
      // Build URL with query parameters
      const url = buildUrl(processedConfig.url, processedConfig.params)
      
      // Execute request with retry logic
      const response = await retry.execute(
        () => fetch(url, fetchOptions),
        {
          maxAttempts: apiConfig.value.retries + 1,
          delay: apiConfig.value.retryDelay,
          backoff: 'exponential',
          condition: (error: any) => { // Typed error
            // Retry on network errors and 5xx status codes
            return error instanceof TypeError || 
                   (error instanceof Response && error.status >= 500)
          }
        }
      )
      
      // Parse response
      const responseData = await parseResponse<T>(response)
      
      const enhancedResponse: EnhancedApiResponse<T> = {
        data: responseData,
        status: response.status,
        statusText: response.statusText,
        headers: Object.fromEntries(response.headers.entries()),
        config: processedConfig,
        success: response.ok,
        duration: Date.now() - startTime,
        cached: false,
        retryCount: retry.getAttemptCount() - 1
      }
      
      // Apply response interceptors
      const processedResponse = await applyResponseInterceptors(enhancedResponse)
      
      lastResponse.value = processedResponse
      logger.debug('API request completed', { 
        requestId, 
        status: response.status, 
        duration: processedResponse.duration 
      })
      
      return processedResponse
      
    } catch (err) {
      errorCount.value++
      
      const apiError: ApiError = {
        code: 'REQUEST_FAILED',
        message: err instanceof Error ? err.message : 'Request failed',
        status: err instanceof Response ? err.status : 0,
        statusText: err instanceof Response ? err.statusText : 'Unknown',
        url: config.url,
        method: config.method,
        timestamp: new Date().toISOString(),
        requestId,
        duration: Date.now() - startTime
      }
      
      error.value = apiError
      logger.error('API request failed', { requestId, error: apiError })
      
      throw apiError
      
    } finally {
      isLoading.value = false
      activeRequests.value.delete(requestId)
    }
  }
  
  // ============================================================================
  // HTTP METHOD HELPERS
  // ============================================================================
  
  const get = <T = any>(url: string, config: Partial<ApiRequestConfig> = {}) => {
    return request<T>({ ...config, method: 'GET', url })
  }
  
  const post = <T = any>(url: string, data?: any, config: Partial<ApiRequestConfig> = {}) => {
    return request<T>({ ...config, method: 'POST', url, data })
  }
  
  const put = <T = any>(url: string, data?: any, config: Partial<ApiRequestConfig> = {}) => {
    return request<T>({ ...config, method: 'PUT', url, data })
  }
  
  const patch = <T = any>(url: string, data?: any, config: Partial<ApiRequestConfig> = {}) => {
    return request<T>({ ...config, method: 'PATCH', url, data })
  }
  
  const del = <T = any>(url: string, config: Partial<ApiRequestConfig> = {}) => {
    return request<T>({ ...config, method: 'DELETE', url })
  }
  
  // ============================================================================
  // UTILITY FUNCTIONS
  // ============================================================================
  
  const buildUrl = (url: string, params?: Record<string, any>): string => {
    const baseUrl = url.startsWith('http') ? url : `${apiConfig.value.baseURL}${url}`
    
    if (!params) return baseUrl
    
    const searchParams = new URLSearchParams()
    for (const [key, value] of Object.entries(params)) {
      if (value !== undefined && value !== null) {
        searchParams.append(key, String(value))
      }
    }
    
    const queryString = searchParams.toString()
    return queryString ? `${baseUrl}?${queryString}` : baseUrl
  }
  
  const parseResponse = async <T>(response: Response): Promise<T> => {
    const contentType = response.headers.get('content-type')
    
    if (contentType?.includes('application/json')) {
      return await response.json()
    } else if (contentType?.includes('text/')) {
      return await response.text() as T
    } else {
      return await response.blob() as T
    }
  }
  
  const generateRequestId = (): string => {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }
  
  const generateCacheKey = (config: ApiRequestConfig): string => {
    const key = `${config.method}_${config.url}`
    if (config.params) {
      const params = new URLSearchParams(config.params).toString()
      return `${key}_${params}`
    }
    return key
  }
  
  const getAuthToken = (): string | null => {
    // Get token from auth store or storage
    if (typeof window === 'undefined') return null
    return localStorage.getItem(apiConfig.value.auth.tokenKey)
  }
  
  const getClientVersion = (): string => {
    return process.env.npm_package_version || '1.0.0'
  }
  
  // ============================================================================
  // ERROR HANDLERS
  // ============================================================================
  
  const handleUnauthorized = async () => {
    logger.warn('Unauthorized request, attempting token refresh')
    // Implement token refresh logic
  }
  
  const handleForbidden = async () => {
    logger.warn('Forbidden request')
    // Handle forbidden access
  }
  
  const handleServerError = async (response: EnhancedApiResponse) => {
    logger.error('Server error', { status: response.status, statusText: response.statusText })
    // Handle server errors
  }
  
  // ============================================================================
  // REQUEST MANAGEMENT
  // ============================================================================
  
  const cancelRequest = (requestId: string) => {
    const controller = activeRequests.value.get(requestId)
    if (controller) {
      controller.abort()
      activeRequests.value.delete(requestId)
      logger.debug('Request cancelled', { requestId })
    }
  }
  
  const cancelAllRequests = () => {
    for (const [, controller] of activeRequests.value.entries()) { // requestId unused
      controller.abort()
    }
    activeRequests.value.clear()
    logger.debug('All requests cancelled')
  }
  
  // ============================================================================
  // CONFIGURATION MANAGEMENT
  // ============================================================================
  
  const updateConfig = (newConfig: Partial<ApiClientConfig>) => {
    apiConfig.value = { ...apiConfig.value, ...newConfig }
    logger.debug('API config updated', newConfig)
  }
  
  const addRequestInterceptor = (interceptor: RequestInterceptor) => {
    apiConfig.value.interceptors.request.push(interceptor)
  }
  
  const addResponseInterceptor = (interceptor: ResponseInterceptor) => {
    apiConfig.value.interceptors.response.push(interceptor)
  }
  
  // ============================================================================
  // CLEANUP
  // ============================================================================
  
  onUnmounted(() => {
    cancelAllRequests()
  })
  
  // ============================================================================
  // RETURN INTERFACE
  // ============================================================================
  
  return {
    // State
    isLoading: readonly(isLoading),
    error: readonly(error),
    lastResponse: readonly(lastResponse),
    requestCount: readonly(requestCount),
    errorCount: readonly(errorCount),
    
    // Computed
    isOnline,
    hasActiveRequests,
    errorRate,
    
    // HTTP methods
    request,
    get,
    post,
    put,
    patch,
    delete: del,
    
    // Request management
    cancelRequest,
    cancelAllRequests,
    
    // Configuration
    updateConfig,
    addRequestInterceptor,
    addResponseInterceptor,
    
    // Utilities
    buildUrl,
    generateRequestId
  }
}
