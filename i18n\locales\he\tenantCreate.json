{"pageTitle": "יצירת דייר חדש", "pageDescription": "הגדרת דייר חדש עם תצורה והגדרות ראשוניות", "backToTenants": "חז<PERSON>ה לדיירים", "preview": "תצוגה מקדימה", "resetForm": "איפוס טופס", "cancel": "ביטול", "createTenant": "יציר<PERSON> דייר", "formTitle": "מי<PERSON><PERSON> דייר", "formDescription": "הגדרת הדייר החדש עם כל הפרטים וההגדרות הנדרשים.", "errorTitle": "יצירה נכשלה", "errorMessage": "אנא בדוק את הטופס ונסה שוב.", "successTitle": "<PERSON><PERSON><PERSON><PERSON> נוצר", "successMessage": "הדייר נוצר בהצלחה.", "validationErrors": {"networkError": "אירעה שגיאת רשת. אנא בדוק את החיבור שלך ונסה שוב.", "serverError": "אירעה שגיאת שרת. אנא נסה שוב מאוחר יותר.", "duplicateSlug": "מזהה הדייר הזה כבר תפוס. אנא בחר מזהה אחר.", "invalidData": "אנא בדוק את כל השדות הנדרשים ונסה שוב.", "logoUploadFailed": "העלאת הלוגו נכשלה. אנא נסה שוב עם תמונה אחרת.", "slugValidationFailed": "לא ניתן לאמת את ייחודיות המזהה. אנא נסה שוב."}, "groups": {"basicInformation": "מידע בסיסי", "branding": "מיתוג", "localization": "לוקליזציה", "planSubscription": "תוכנית ומנוי", "initialSettings": "הגדרות ראשוניות"}, "fields": {"tenantName": {"label": "שם הדייר", "placeholder": "הזן שם דייר", "validation": {"required": "שם הדייר נדרש", "minLength": "שם הדייר חייב להיות לפחות 2 תווים", "maxLength": "שם הדייר לא יכול לעלות על 100 תווים"}}, "tenantSlug": {"label": "מזהה דייר", "placeholder": "הזן מזהה דייר (לדוגמה: acme-corp)", "hint": "משמ<PERSON> בכתובות URL ונקודות קצה של API. רק אותיות קטנות, מספרים ומקפים מותרים.", "validation": {"required": "מזהה הדייר נדרש", "minLength": "המזהה חייב להיות לפחות 2 תווים", "maxLength": "המזהה לא יכול לעלות על 50 תווים", "pattern": "המזהה יכול להכיל רק אותיות קטנות, מספרים ומקפים"}}, "tenantLogo": {"label": "לוג<PERSON> דייר", "hint": "העלה לוגו עבור הדייר. גודל מומלץ: 200x200px, מקסימום 2MB. פורמטים נתמכים: JPEG, PNG, GIF, WebP.", "uploadText": "גרור לוגו לכאן או לחץ לעיון", "supportText": "JPEG, PNG, GIF, WebP עד 2MB"}, "defaultLanguage": {"label": "שפת ברירת מחדל", "validation": {"required": "שפת ברירת מחדל נדרשת"}}, "defaultTimezone": {"label": "אזור ז<PERSON>ן ברירת מחדל", "validation": {"required": "אזור ז<PERSON>ן ברירת מחדל נדרש"}}, "subscriptionPlan": {"label": "תוכנית מנוי", "validation": {"required": "תוכנית מנוי נדרשת"}}, "activateImmediately": {"label": "הפעל דייר מיידית", "hint": "כא<PERSON>ר מופעל, הדייר יהיה פעיל והמשתמשים יוכלו לגשת לפלטפורמה מיידית."}, "enableApiAccess": {"label": "אפ<PERSON>ר גישה ל-API", "hint": "אפשר לדייר זה לגשת ל-API של הפלטפורמה."}, "enableAuditLog": {"label": "אפשר רישום ביקורת", "hint": "עקוב אחר פעולות משתמשים ושינויים במערכת עבור דייר זה."}, "enableNotifications": {"label": "אפשר התראות אימייל", "hint": "שלח התראות אימייל עבור אירועים חשובים."}, "seedDefaultData": {"label": "זרע עם נתונים ברירת מחדל", "hint": "צור תפקידים ברירת מחדל, תבניות ונתוני דוגמה להגדרה מהירה."}}, "localeOptions": {"en": "אנגלית", "es": "ספרדית", "fr": "צרפתית", "de": "גרמנית", "it": "איטלקית", "pt": "פורטוגזית", "he": "עברית", "ar": "ערבית"}, "timezoneOptions": {"UTC": "<PERSON><PERSON><PERSON> אוניבר<PERSON>לי מתואם (UTC)", "America/New_York": "ז<PERSON><PERSON> מזרחי (ET)", "America/Chicago": "<PERSON><PERSON><PERSON> (CT)", "America/Denver": "ז<PERSON>ן הרים (MT)", "America/Los_Angeles": "<PERSON><PERSON><PERSON>סיפיק (PT)", "Europe/London": "זמן גריניץ' (GMT)", "Europe/Paris": "ז<PERSON>ן מרכז אירופה (CET)", "Asia/Tokyo": "ז<PERSON><PERSON> י<PERSON>ן סטנדרטי (JST)", "Asia/Shanghai": "<PERSON><PERSON><PERSON> סין סטנ<PERSON>רטי (CST)", "Australia/Sydney": "זמן מזרח אוסטרליה (AET)"}, "planOptions": {"basic": {"label": "תוכנית בסיסית", "description": "עד 5 משתמשים, תכונות בסיסיות"}, "professional": {"label": "תוכנית מקצועית", "description": "עד 25 משתמשים, תכונות מתקדמות"}, "enterprise": {"label": "תוכנית ארגונית", "description": "משתמשים ללא הגבלה, כל התכונות"}, "custom": {"label": "תוכנית מותאמת", "description": "פתרון מותאם אישית"}}, "breadcrumbs": {"dashboard": "לוח <PERSON><PERSON><PERSON>ה", "tenants": "דיירים", "create": "יצירה"}}