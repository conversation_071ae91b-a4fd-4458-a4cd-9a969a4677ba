<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900 py-12">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
      <!-- Header -->
      <div class="text-center mb-12">
        <h1 class="text-4xl font-bold text-gray-900 dark:text-white mb-4">
          Color Picker Component Demo
        </h1>
        <p class="text-lg text-gray-600 dark:text-gray-400">
          Interactive color picker with predefined colors, recent colors, and advanced HSV controls
        </p>
      </div>

      <!-- Demo Section -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8 mb-8">
        <h2 class="text-2xl font-semibold text-gray-900 dark:text-white mb-6">
          Basic Usage
        </h2>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
          <!-- Color Picker Examples -->
          <div class="space-y-6">
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Primary Color
              </label>
              <div class="flex items-center space-x-4">
                <UiColorPicker v-model="primaryColor" />
                <span class="text-sm text-gray-600 dark:text-gray-400">
                  {{ primaryColor }}
                </span>
              </div>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Secondary Color
              </label>
              <div class="flex items-center space-x-4">
                <UiColorPicker v-model="secondaryColor" />
                <span class="text-sm text-gray-600 dark:text-gray-400">
                  {{ secondaryColor }}
                </span>
              </div>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Accent Color
              </label>
              <div class="flex items-center space-x-4">
                <UiColorPicker v-model="accentColor" />
                <span class="text-sm text-gray-600 dark:text-gray-400">
                  {{ accentColor }}
                </span>
              </div>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Disabled Color Picker
              </label>
              <div class="flex items-center space-x-4">
                <UiColorPicker v-model="disabledColor" :disabled="true" />
                <span class="text-sm text-gray-600 dark:text-gray-400">
                  {{ disabledColor }} (disabled)
                </span>
              </div>
            </div>
          </div>

          <!-- Preview Panel -->
          <div class="space-y-4">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white">
              Color Preview
            </h3>
            
            <div class="space-y-3">
              <div class="flex items-center space-x-3">
                <div 
                  class="w-12 h-12 rounded-lg border-2 border-gray-300 dark:border-gray-600"
                  :style="{ backgroundColor: primaryColor }"
                />
                <div>
                  <div class="text-sm font-medium text-gray-900 dark:text-white">Primary</div>
                  <div class="text-xs text-gray-500 dark:text-gray-400">{{ primaryColor }}</div>
                </div>
              </div>

              <div class="flex items-center space-x-3">
                <div 
                  class="w-12 h-12 rounded-lg border-2 border-gray-300 dark:border-gray-600"
                  :style="{ backgroundColor: secondaryColor }"
                />
                <div>
                  <div class="text-sm font-medium text-gray-900 dark:text-white">Secondary</div>
                  <div class="text-xs text-gray-500 dark:text-gray-400">{{ secondaryColor }}</div>
                </div>
              </div>

              <div class="flex items-center space-x-3">
                <div 
                  class="w-12 h-12 rounded-lg border-2 border-gray-300 dark:border-gray-600"
                  :style="{ backgroundColor: accentColor }"
                />
                <div>
                  <div class="text-sm font-medium text-gray-900 dark:text-white">Accent</div>
                  <div class="text-xs text-gray-500 dark:text-gray-400">{{ accentColor }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Usage Example -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8">
        <h2 class="text-2xl font-semibold text-gray-900 dark:text-white mb-6">
          Usage Example
        </h2>
        
        <div class="bg-gray-50 dark:bg-gray-900 rounded-lg p-4">
          <pre class="text-sm text-gray-800 dark:text-gray-200 overflow-x-auto"><code>&lt;template&gt;
  &lt;div&gt;
    &lt;label&gt;Select Color:&lt;/label&gt;
    &lt;UiColorPicker v-model="selectedColor" /&gt;
    &lt;p&gt;Selected: {{ selectedColor }}&lt;/p&gt;
  &lt;/div&gt;
&lt;/template&gt;

&lt;script setup&gt;
const selectedColor = ref('#3b82f6')
&lt;/script&gt;</code></pre>
        </div>
      </div>

      <!-- Navigation -->
      <div class="mt-12 text-center">
        <NuxtLink 
          to="/demo" 
          class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors"
        >
          ← Back to Demo Index
        </NuxtLink>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// Demo state
const primaryColor = ref('#3b82f6')
const secondaryColor = ref('#10b981')
const accentColor = ref('#f59e0b')
const disabledColor = ref('#6b7280')

// Page meta
definePageMeta({
  title: 'Color Picker Demo',
  description: 'Interactive demo of the UiColorPicker component'
})
</script>
