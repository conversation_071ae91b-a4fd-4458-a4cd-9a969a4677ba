import { computed, reactive, watch } from 'vue'
import type { 
  DynamicFormField, 
  DynamicFormCondition, 
  DynamicFormFieldConditional 
} from '~/app/shared/types'

export interface ConditionalState {
  visibleFields: Set<string>
  hiddenFields: Set<string>
  enabledFields: Set<string>
  disabledFields: Set<string>
  requiredFields: Set<string>
  readonlyFields: Set<string>
  evaluationCount: number
}

export const useDynamicFormConditionals = (
  fields: DynamicFormField[], 
  formData: Record<string, any>
) => {
  // Reactive conditional state
  const conditionalState = reactive<ConditionalState>({
    visibleFields: new Set(),
    hiddenFields: new Set(),
    enabledFields: new Set(),
    disabledFields: new Set(),
    requiredFields: new Set(),
    readonlyFields: new Set(),
    evaluationCount: 0
  })

  // Field dependency graph for optimization
  const dependencyGraph = new Map<string, Set<string>>()
  const reverseDependencyGraph = new Map<string, Set<string>>()

  // Build dependency graphs
  const buildDependencyGraphs = () => {
    dependencyGraph.clear()
    reverseDependencyGraph.clear()

    fields.forEach(field => {
      const fieldName = field.name
      const dependencies = new Set<string>()

      // Extract dependencies from all conditional types
      const conditionals = [
        ...(field.conditional?.show || []),
        ...(field.conditional?.hide || []),
        ...(field.conditional?.enable || []),
        ...(field.conditional?.disable || []),
        ...(field.conditional?.require || []),
        ...(field.conditional?.readonly || [])
      ]

      conditionals.forEach(condition => {
        dependencies.add(condition.field)
        
        // Build reverse dependency graph
        if (!reverseDependencyGraph.has(condition.field)) {
          reverseDependencyGraph.set(condition.field, new Set())
        }
        reverseDependencyGraph.get(condition.field)!.add(fieldName)
      })

      dependencyGraph.set(fieldName, dependencies)
    })
  }

  // Evaluate a single condition
  const evaluateCondition = (condition: DynamicFormCondition): boolean => {
    const fieldValue = formData[condition.field]

    switch (condition.operator) {
      case 'equals':
        return fieldValue === condition.value

      case 'not_equals':
        return fieldValue !== condition.value

      case 'contains':
        if (Array.isArray(fieldValue)) {
          return fieldValue.includes(condition.value)
        }
        return String(fieldValue || '').includes(String(condition.value || ''))

      case 'not_contains':
        if (Array.isArray(fieldValue)) {
          return !fieldValue.includes(condition.value)
        }
        return !String(fieldValue || '').includes(String(condition.value || ''))

      case 'in':
        return Array.isArray(condition.values) && condition.values.includes(fieldValue)

      case 'not_in':
        return !Array.isArray(condition.values) || !condition.values.includes(fieldValue)

      case 'greater_than':
        return Number(fieldValue || 0) > Number(condition.value || 0)

      case 'less_than':
        return Number(fieldValue || 0) < Number(condition.value || 0)

      case 'greater_equal':
        return Number(fieldValue || 0) >= Number(condition.value || 0)

      case 'less_equal':
        return Number(fieldValue || 0) <= Number(condition.value || 0)

      case 'empty':
        if (Array.isArray(fieldValue)) {
          return fieldValue.length === 0
        }
        return !fieldValue || fieldValue === '' || fieldValue === null || fieldValue === undefined

      case 'not_empty':
        if (Array.isArray(fieldValue)) {
          return fieldValue.length > 0
        }
        return fieldValue !== null && fieldValue !== undefined && fieldValue !== ''

      case 'regex':
        if (!condition.pattern) return false
        try {
          const regex = new RegExp(condition.pattern)
          return regex.test(String(fieldValue || ''))
        } catch {
          return false
        }

      default:
        console.warn(`Unknown condition operator: ${condition.operator}`)
        return true
    }
  }

  // Evaluate multiple conditions with logic
  const evaluateConditions = (conditions: DynamicFormCondition[]): boolean => {
    if (!conditions || conditions.length === 0) return true

    // Group conditions by logic operator
    const andConditions = conditions.filter(c => !c.logic || c.logic === 'and')
    const orConditions = conditions.filter(c => c.logic === 'or')

    let result = true

    // Evaluate AND conditions (all must be true)
    if (andConditions.length > 0) {
      result = andConditions.every(condition => evaluateCondition(condition))
    }

    // Evaluate OR conditions (at least one must be true)
    if (orConditions.length > 0) {
      const orResult = orConditions.some(condition => evaluateCondition(condition))
      result = andConditions.length > 0 ? result && orResult : orResult
    }

    return result
  }

  // Evaluate field visibility
  const evaluateFieldVisibility = (field: DynamicFormField): boolean => {
    const conditional = field.conditional

    if (!conditional?.show && !conditional?.hide) {
      return true // Default to visible
    }

    let isVisible = true

    // Check show conditions
    if (conditional.show && conditional.show.length > 0) {
      isVisible = evaluateConditions(conditional.show)
    }

    // Check hide conditions
    if (conditional.hide && conditional.hide.length > 0) {
      const shouldHide = evaluateConditions(conditional.hide)
      isVisible = isVisible && !shouldHide
    }

    return isVisible
  }

  // Evaluate field enabled state
  const evaluateFieldEnabled = (field: DynamicFormField): boolean => {
    const conditional = field.conditional

    if (field.disabled) return false // Field is explicitly disabled

    if (!conditional?.enable && !conditional?.disable) {
      return true // Default to enabled
    }

    let isEnabled = true

    // Check enable conditions
    if (conditional.enable && conditional.enable.length > 0) {
      isEnabled = evaluateConditions(conditional.enable)
    }

    // Check disable conditions
    if (conditional.disable && conditional.disable.length > 0) {
      const shouldDisable = evaluateConditions(conditional.disable)
      isEnabled = isEnabled && !shouldDisable
    }

    return isEnabled
  }

  // Evaluate field required state
  const evaluateFieldRequired = (field: DynamicFormField): boolean => {
    if (!field.required && !field.conditional?.require) {
      return false // Not required by default
    }

    if (field.required && !field.conditional?.require) {
      return true // Always required
    }

    // Check conditional require
    if (field.conditional?.require && field.conditional.require.length > 0) {
      return evaluateConditions(field.conditional.require)
    }

    return field.required || false
  }

  // Evaluate field readonly state
  const evaluateFieldReadonly = (field: DynamicFormField): boolean => {
    const conditional = field.conditional

    if (field.readonly) return true // Field is explicitly readonly

    if (!conditional?.readonly) {
      return false // Default to not readonly
    }

    // Check readonly conditions
    if (conditional.readonly && conditional.readonly.length > 0) {
      return evaluateConditions(conditional.readonly)
    }

    return false
  }

  // Evaluate all field states
  const evaluateAllFields = () => {
    conditionalState.evaluationCount++

    // Clear previous states
    conditionalState.visibleFields.clear()
    conditionalState.hiddenFields.clear()
    conditionalState.enabledFields.clear()
    conditionalState.disabledFields.clear()
    conditionalState.requiredFields.clear()
    conditionalState.readonlyFields.clear()

    fields.forEach(field => {
      const fieldName = field.name

      // Evaluate visibility
      const isVisible = evaluateFieldVisibility(field)
      if (isVisible) {
        conditionalState.visibleFields.add(fieldName)
      } else {
        conditionalState.hiddenFields.add(fieldName)
      }

      // Only evaluate other states if field is visible
      if (isVisible) {
        // Evaluate enabled state
        const isEnabled = evaluateFieldEnabled(field)
        if (isEnabled) {
          conditionalState.enabledFields.add(fieldName)
        } else {
          conditionalState.disabledFields.add(fieldName)
        }

        // Evaluate required state
        const isRequired = evaluateFieldRequired(field)
        if (isRequired) {
          conditionalState.requiredFields.add(fieldName)
        }

        // Evaluate readonly state
        const isReadonly = evaluateFieldReadonly(field)
        if (isReadonly) {
          conditionalState.readonlyFields.add(fieldName)
        }
      }
    })
  }

  // Evaluate specific fields (for optimization)
  const evaluateFields = (fieldNames: string[]) => {
    fieldNames.forEach(fieldName => {
      const field = fields.find(f => f.name === fieldName)
      if (!field) return

      // Update visibility
      const isVisible = evaluateFieldVisibility(field)
      if (isVisible) {
        conditionalState.visibleFields.add(fieldName)
        conditionalState.hiddenFields.delete(fieldName)
      } else {
        conditionalState.hiddenFields.add(fieldName)
        conditionalState.visibleFields.delete(fieldName)
      }

      // Update other states only if visible
      if (isVisible) {
        const isEnabled = evaluateFieldEnabled(field)
        const isRequired = evaluateFieldRequired(field)
        const isReadonly = evaluateFieldReadonly(field)

        // Update enabled state
        if (isEnabled) {
          conditionalState.enabledFields.add(fieldName)
          conditionalState.disabledFields.delete(fieldName)
        } else {
          conditionalState.disabledFields.add(fieldName)
          conditionalState.enabledFields.delete(fieldName)
        }

        // Update required state
        if (isRequired) {
          conditionalState.requiredFields.add(fieldName)
        } else {
          conditionalState.requiredFields.delete(fieldName)
        }

        // Update readonly state
        if (isReadonly) {
          conditionalState.readonlyFields.add(fieldName)
        } else {
          conditionalState.readonlyFields.delete(fieldName)
        }
      } else {
        // Remove from other states if not visible
        conditionalState.enabledFields.delete(fieldName)
        conditionalState.disabledFields.delete(fieldName)
        conditionalState.requiredFields.delete(fieldName)
        conditionalState.readonlyFields.delete(fieldName)
      }
    })
  }

  // Get fields that depend on a specific field
  const getDependentFields = (fieldName: string): string[] => {
    return Array.from(reverseDependencyGraph.get(fieldName) || [])
  }

  // Computed properties for easy access
  const visibleFields = computed(() => Array.from(conditionalState.visibleFields))
  const hiddenFields = computed(() => Array.from(conditionalState.hiddenFields))
  const enabledFields = computed(() => Array.from(conditionalState.enabledFields))
  const disabledFields = computed(() => Array.from(conditionalState.disabledFields))
  const requiredFields = computed(() => Array.from(conditionalState.requiredFields))
  const readonlyFields = computed(() => Array.from(conditionalState.readonlyFields))

  // Helper functions
  const isFieldVisible = (fieldName: string): boolean => {
    return conditionalState.visibleFields.has(fieldName)
  }

  const isFieldEnabled = (fieldName: string): boolean => {
    return conditionalState.enabledFields.has(fieldName)
  }

  const isFieldRequired = (fieldName: string): boolean => {
    return conditionalState.requiredFields.has(fieldName)
  }

  const isFieldReadonly = (fieldName: string): boolean => {
    return conditionalState.readonlyFields.has(fieldName)
  }

  // Initialize
  buildDependencyGraphs()
  evaluateAllFields()

  // Watch for form data changes and re-evaluate dependent fields
  watch(
    () => formData,
    (newData, oldData) => {
      const changedFields = Object.keys(newData).filter(
        key => newData[key] !== oldData?.[key]
      )

      if (changedFields.length > 0) {
        // Get all fields that depend on the changed fields
        const fieldsToEvaluate = new Set<string>()
        changedFields.forEach(changedField => {
          getDependentFields(changedField).forEach(dependentField => {
            fieldsToEvaluate.add(dependentField)
          })
        })

        if (fieldsToEvaluate.size > 0) {
          evaluateFields(Array.from(fieldsToEvaluate))
        }
      }
    },
    { deep: true }
  )

  return {
    // State
    conditionalState,
    
    // Computed
    visibleFields,
    hiddenFields,
    enabledFields,
    disabledFields,
    requiredFields,
    readonlyFields,

    // Methods
    evaluateCondition,
    evaluateConditions,
    evaluateAllFields,
    evaluateFields,
    getDependentFields,
    buildDependencyGraphs,

    // Helper functions
    isFieldVisible,
    isFieldEnabled,
    isFieldRequired,
    isFieldReadonly
  }
}
