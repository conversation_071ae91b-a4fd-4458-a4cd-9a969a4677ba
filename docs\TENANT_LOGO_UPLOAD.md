# Tenant Logo Upload Feature

## Overview

The tenant creation process has been enhanced to support logo upload functionality using the new `POST /tenants/with-logo` endpoint. This allows SuperAdmins to upload a logo file directly when creating a new tenant.

## Implementation Details

### 1. New API Endpoint

**Endpoint:** `POST /tenants/with-logo`
**Authorization:** SuperAdmin only
**Content-Type:** `multipart/form-data`

#### Request Parameters

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `logo` | File | No | Logo image file (JPEG, PNG, GIF, WebP) |
| `name` | String | Yes | Tenant name |
| `slug` | String | Yes | Tenant slug (URL-friendly identifier) |
| `locale` | String | No | Default locale (default: 'en') |
| `timezone` | String | No | Default timezone (default: 'UTC') |
| `plan` | String | No | Subscription plan (basic/pro/enterprise, default: 'basic') |

#### Logo File Requirements

- **Supported formats:** JPEG, PNG, GIF, WebP
- **Maximum size:** 2MB
- **Recommended dimensions:** 200x200px or similar square aspect ratio

#### Response

```json
{
  "id": "tenant-uuid-123",
  "name": "Acme Corporation",
  "slug": "acme-corp",
  "logoUrl": "/uploads/logos/logo-acme-corp-1234567890.png",
  "locale": "en",
  "timezone": "UTC",
  "plan": "pro",
  "isActive": true,
  "createdAt": "2024-01-15T10:30:00.000Z",
  "updatedAt": "2024-01-15T10:30:00.000Z"
}
```

### 2. Frontend Components

#### UiLogoUpload Component

A new reusable component for logo upload functionality:

**Location:** `components/ui/UiLogoUpload.vue`

**Features:**
- Drag and drop support
- File type validation (JPEG, PNG, GIF, WebP)
- File size validation (2MB max)
- Image preview
- Error handling
- Accessibility support

**Props:**
- `modelValue`: File | null - The selected file
- `label`: string - Label for the upload area
- `required`: boolean - Whether the field is required
- `disabled`: boolean - Whether the upload is disabled
- `error`: string - Error message to display
- `helpText`: string - Help text to display
- `maxSize`: number - Maximum file size in bytes (default: 2MB)
- `acceptedTypes`: string[] - Accepted file extensions

**Events:**
- `update:modelValue`: Emitted when file selection changes
- `change`: Emitted when file selection changes
- `error`: Emitted when validation fails

#### Updated Create Tenant Page

**Location:** `pages/dashboard/tenants/create.vue`

**Changes:**
- Replaced `logoUrl` text input with `UiLogoUpload` component
- Updated form data interface to use `File` instead of `string`
- Modified submit handler to use new `createTenantWithLogo` method

### 3. Store Updates

#### Tenant Store

**Location:** `stores/tenant.ts`

**New Interface:**
```typescript
export interface CreateTenantWithLogoPayload {
  name: string;
  slug: string;
  logo?: File | null;
  locale?: string;
  timezone?: string;
  plan?: string;
}
```

**New Method:**
```typescript
async createTenantWithLogo(payload: CreateTenantWithLogoPayload): Promise<Tenant>
```

This method:
- Creates a `FormData` object for multipart/form-data requests
- Appends all form fields including the logo file
- Makes a POST request to `/tenants/with-logo`
- Handles success/error states and notifications

### 4. Usage Example

```vue
<template>
  <form @submit.prevent="handleSubmit">
    <UiInput
      v-model="formData.name"
      label="Tenant Name"
      required
    />
    
    <UiLogoUpload
      v-model="formData.logo"
      label="Tenant Logo (Optional)"
      :max-size="2 * 1024 * 1024"
      help-text="Upload a logo for the tenant. Recommended size: 200x200px"
      @error="handleLogoError"
    />
    
    <UiButton type="submit" :loading="isLoading">
      Create Tenant
    </UiButton>
  </form>
</template>

<script setup>
const formData = ref({
  name: '',
  slug: '',
  logo: null,
  locale: 'en',
  timezone: 'UTC',
  plan: 'basic'
})

const handleSubmit = async () => {
  await tenantStore.createTenantWithLogo(formData.value)
}

const handleLogoError = (error) => {
  // Handle logo validation errors
  console.error('Logo error:', error)
}
</script>
```

## Technical Notes

### FormData Handling

When using `FormData` for multipart/form-data requests:
- **Do NOT** set the `Content-Type` header manually
- The browser automatically sets it with the correct boundary parameter
- The API client handles FormData objects correctly

### File Validation

The `UiLogoUpload` component performs client-side validation:
- File type validation using file extensions and MIME types
- File size validation
- Real-time error feedback

Server-side validation should also be implemented for security.

### Error Handling

- Client-side validation errors are displayed immediately
- Server-side errors are handled by the store and displayed via notifications
- The component provides accessible error messages

## Testing

To test the logo upload functionality:

1. Navigate to `/dashboard/tenants/create`
2. Fill in the required tenant information
3. Upload a logo file (JPEG, PNG, GIF, or WebP under 2MB)
4. Submit the form
5. Verify the tenant is created with the logo URL in the response

## Future Enhancements

- Image cropping/resizing functionality
- Multiple logo sizes (favicon, header, etc.)
- Logo management for existing tenants
- Bulk logo upload for multiple tenants
- Integration with cloud storage services
