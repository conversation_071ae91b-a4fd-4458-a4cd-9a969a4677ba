<!--
  Not Found Error Component
  
  Displays a user-friendly 404 error page with navigation options
  and helpful suggestions for users who encounter missing pages
-->

<template>
  <div class="min-h-[60vh] flex items-center justify-center px-4 py-12">
    <div class="max-w-md w-full text-center">
      <!-- Error illustration -->
      <div class="mb-8">
        <div class="mx-auto flex items-center justify-center h-24 w-24 rounded-full bg-gray-100 mb-6">
          <Icon name="heroicons:document-magnifying-glass" class="h-12 w-12 text-gray-400" />
        </div>
        
        <!-- Error code -->
        <div class="text-6xl font-bold text-gray-300 mb-2">404</div>
      </div>

      <!-- Error message -->
      <h1 class="text-2xl font-bold text-gray-900 mb-4">
        {{ displayTitle }}
      </h1>

      <p class="text-gray-600 mb-8 leading-relaxed">
        {{ displayMessage }}
      </p>

      <!-- Action buttons -->
      <div class="flex flex-col sm:flex-row gap-3 justify-center">
        <button
          type="button"
          class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200"
          @click="goBack"
        >
          <Icon name="heroicons:arrow-left" class="ltr:h-4 ltr:w-4 ltr:mr-2 rtl:h-4 rtl:w-4 rtl:ml-2" />
          {{ t('common.goBack') }}
        </button>

        <NuxtLink
          to="/dashboard"
          class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200"
        >
          <Icon name="heroicons:home" class="ltr:h-4 ltr:w-4 ltr:mr-2 rtl:h-4 rtl:w-4 rtl:ml-2" />
          {{ t('navigation.dashboard') }}
        </NuxtLink>
      </div>

      <!-- Helpful suggestions -->
      <div class="mt-12 text-left">
        <h3 class="text-sm font-medium text-gray-900 mb-4">
          You might be looking for:
        </h3>
        
        <ul class="space-y-2">
          <li>
            <NuxtLink
              to="/dashboard/cases"
              class="flex items-center text-sm text-indigo-600 hover:text-indigo-500 transition-colors duration-200"
            >
              <Icon name="heroicons:briefcase" class="h-4 w-4 mr-2" />
              Cases Management
            </NuxtLink>
          </li>
          
          <li>
            <NuxtLink
              to="/dashboard/documents"
              class="flex items-center text-sm text-indigo-600 hover:text-indigo-500 transition-colors duration-200"
            >
              <Icon name="heroicons:document-text" class="h-4 w-4 mr-2" />
              Documents
            </NuxtLink>
          </li>
          
          <li>
            <NuxtLink
              to="/dashboard/templates"
              class="flex items-center text-sm text-indigo-600 hover:text-indigo-500 transition-colors duration-200"
            >
              <Icon name="heroicons:clipboard-document-list" class="h-4 w-4 mr-2" />
              Templates
            </NuxtLink>
          </li>
          
          <li>
            <NuxtLink
              to="/dashboard/settings"
              class="flex items-center text-sm text-indigo-600 hover:text-indigo-500 transition-colors duration-200"
            >
              <Icon name="heroicons:cog-6-tooth" class="h-4 w-4 mr-2" />
              Settings
            </NuxtLink>
          </li>
        </ul>
      </div>

      <!-- Search suggestion -->
      <div class="mt-8 p-4 bg-gray-50 rounded-lg">
        <h4 class="text-sm font-medium text-gray-900 mb-2">
          Can't find what you're looking for?
        </h4>
        <p class="text-xs text-gray-600 mb-3">
          Try searching for it or contact support for assistance.
        </p>
        
        <div class="flex gap-2">
          <button
            type="button"
            class="flex-1 inline-flex items-center justify-center px-3 py-2 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200"
            @click="openSearch"
          >
            <Icon name="heroicons:magnifying-glass" class="h-3 w-3 mr-1" />
            Search
          </button>
          
          <button
            type="button"
            class="flex-1 inline-flex items-center justify-center px-3 py-2 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200"
            @click="contactSupport"
          >
            <Icon name="heroicons:question-mark-circle" class="h-3 w-3 mr-1" />
            Support
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useI18n } from 'vue-i18n'

// Props
interface Props {
  title?: string
  message?: string
  showSuggestions?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  title: '',
  message: '',
  showSuggestions: true
})

// Composables
const router = useRouter()
const route = useRoute()
const { t } = useI18n()

// Computed properties for translations
const displayTitle = computed(() => props.title || t('errors.pageNotFound'))
const displayMessage = computed(() => props.message || t('errors.pageNotFoundMessage'))

// Methods
const goBack = () => {
  // Check if there's history to go back to
  if (window.history.length > 1) {
    router.back()
  } else {
    // Fallback to dashboard if no history
    router.push('/dashboard')
  }
}

const openSearch = () => {
  // Emit event or trigger search functionality
  // This could open a search modal or navigate to a search page
  console.log('Open search functionality')
  // Example: router.push('/dashboard/search')
}

const contactSupport = () => {
  // Open support contact method
  // This could open a modal, navigate to support page, or open email client
  console.log('Contact support')
  // Example: router.push('/dashboard/support')
}

// SEO and meta
useHead({
  title: 'Page Not Found - Legal SaaS',
  meta: [
    {
      name: 'description',
      content: 'The page you are looking for could not be found. Return to the dashboard or browse our available features.'
    },
    {
      name: 'robots',
      content: 'noindex, nofollow'
    }
  ]
})

// Analytics - track 404 errors
onMounted(() => {
  // Track 404 error for analytics
  console.log('404 Error:', {
    path: route.fullPath,
    referrer: document.referrer,
    timestamp: new Date().toISOString()
  })
})
</script>

<style scoped>
/* Custom animations for error state */
.error-illustration {
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .error-code {
    font-size: 4rem;
  }
}

/* Focus styles for accessibility */
button:focus,
a:focus {
  outline: none;
  box-shadow: 0 0 0 2px #6366f1, 0 0 0 4px rgba(99, 102, 241, 0.2);
}

/* Hover effects */
.suggestion-link:hover {
  background-color: rgba(99, 102, 241, 0.05);
  border-radius: 0.375rem;
  padding: 0.25rem;
  margin: -0.25rem;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .bg-gray-50 {
    background-color: #f9fafb;
    border: 1px solid #d1d5db;
  }
  
  .text-gray-600 {
    color: #374151;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .error-illustration {
    animation: none;
  }
  
  * {
    transition: none !important;
  }
}
</style>
