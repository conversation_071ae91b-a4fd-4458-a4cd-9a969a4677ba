<template>
  <div id="legal-saas-app" class="antialiased text-gray-900 bg-gray-50 min-h-screen">
    <AuthLoadingPage v-if="isLoading" />
    <ClientOnly>
      <template v-if="!isLoading">
        <NuxtLayout>
          <NuxtPage />
        </NuxtLayout>
        <PlatformToasts />
        <!-- Global Modals - accessible across the entire application -->
        <GlobalModals />
      </template>
    </ClientOnly>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue';
import { useAuth } from '~/composables/useAuth';
import { useLanguageSwitcher } from '~/composables/useLanguageSwitcher';
import { useHead } from '#imports';
import PlatformToasts from './components/platform/PlatformToasts.vue';
import GlobalModals from './components/GlobalModals.vue';

const { isLoading } = useAuth(); // Get isLoading state

// Initialize language switcher
const { initializeLanguage, locale, currentDirection } = useLanguageSwitcher()

// Initialize language on mount
onMounted(() => {
  initializeLanguage()
})
 

// Global setup for head management (e.g., app title, favicons)
useHead({
  titleTemplate: (titleChunk?: string) => {
    return titleChunk ? `${titleChunk} - Legal SaaS Platform` : 'Legal SaaS Platform';
  },
  htmlAttrs: {
    lang: locale,
    dir: currentDirection
  },
  meta: [
    { name: 'description', content: 'Secure and compliant Legal SaaS Platform for law firms.' },
    { name: 'language', content: locale },
    // Add other relevant meta tags for SEO and accessibility
  ],
  link: [
    // Add favicon links
  ]
});

 

// Potentially global middleware or composables for initial setup
// For instance, checking authentication state on initial load
</script>

<style>
 

/* Any specific global overrides or base styles */
body {
  /* Ensure consistent font smoothing */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</style>