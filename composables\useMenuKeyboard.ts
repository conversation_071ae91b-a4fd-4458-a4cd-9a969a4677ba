import { ref, onMounted, onUnmounted, computed } from 'vue';
import { useRouter } from 'vue-router';

export interface MenuKeyboardOptions {
  menuItems: Array<{
    name: string;
    href: string;
    current: boolean;
    roles: string[];
  }>;
  isOpen?: boolean;
  onClose?: () => void;
}

export function useMenuKeyboard(options: MenuKeyboardOptions) {
  const router = useRouter();
  const activeIndex = ref(-1);
  const searchQuery = ref('');
  const isSearchMode = ref(false);

  // Filter menu items based on search query
  const filteredItems = computed(() => {
    if (!searchQuery.value) return options.menuItems;
    
    return options.menuItems.filter(item =>
      item.name.toLowerCase().includes(searchQuery.value.toLowerCase())
    );
  });

  // Handle keyboard navigation
  const handleKeydown = (event: KeyboardEvent) => {
    if (!options.isOpen) return;

    switch (event.key) {
      case 'Escape':
        event.preventDefault();
        if (isSearchMode.value) {
          exitSearchMode();
        } else {
          options.onClose?.();
        }
        break;

      case 'ArrowDown':
        event.preventDefault();
        navigateDown();
        break;

      case 'ArrowUp':
        event.preventDefault();
        navigateUp();
        break;

      case 'Enter':
        event.preventDefault();
        selectCurrentItem();
        break;

      case '/':
        if (!isSearchMode.value) {
          event.preventDefault();
          enterSearchMode();
        }
        break;

      case 'Home':
        event.preventDefault();
        activeIndex.value = 0;
        break;

      case 'End':
        event.preventDefault();
        activeIndex.value = filteredItems.value.length - 1;
        break;

      default:
        // Handle search input
        if (isSearchMode.value && event.key.length === 1) {
          searchQuery.value += event.key;
          activeIndex.value = 0; // Reset to first item when searching
        } else if (isSearchMode.value && event.key === 'Backspace') {
          searchQuery.value = searchQuery.value.slice(0, -1);
          if (searchQuery.value === '') {
            exitSearchMode();
          }
        }
        break;
    }
  };

  const navigateDown = () => {
    if (filteredItems.value.length === 0) return;
    activeIndex.value = (activeIndex.value + 1) % filteredItems.value.length;
  };

  const navigateUp = () => {
    if (filteredItems.value.length === 0) return;
    activeIndex.value = activeIndex.value <= 0 
      ? filteredItems.value.length - 1 
      : activeIndex.value - 1;
  };

  const selectCurrentItem = () => {
    if (activeIndex.value >= 0 && activeIndex.value < filteredItems.value.length) {
      const selectedItem = filteredItems.value[activeIndex.value];
      router.push(selectedItem.href);
      options.onClose?.();
    }
  };

  const enterSearchMode = () => {
    isSearchMode.value = true;
    searchQuery.value = '';
    activeIndex.value = 0;
  };

  const exitSearchMode = () => {
    isSearchMode.value = false;
    searchQuery.value = '';
    activeIndex.value = -1;
  };

  // Focus management
  const focusMenuItem = (index: number) => {
    activeIndex.value = index;
  };

  const resetNavigation = () => {
    activeIndex.value = -1;
    exitSearchMode();
  };

  // Lifecycle
  onMounted(() => {
    document.addEventListener('keydown', handleKeydown);
  });

  onUnmounted(() => {
    document.removeEventListener('keydown', handleKeydown);
  });

  return {
    activeIndex,
    searchQuery,
    isSearchMode,
    filteredItems,
    focusMenuItem,
    resetNavigation,
    enterSearchMode,
    exitSearchMode,
    selectCurrentItem,
  };
}
