/**
 * Global Search Composable
 * 
 * Comprehensive composable for managing global search functionality
 * across the legal SaaS platform
 */

import { ref, computed, watch, nextTick } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useApi } from './useApi'
import { useDebounce } from './useDebounce'
import { useLocalStorage } from './useLocalStorage'
import { useToast } from '../ui/useToast'
import type { 
  GlobalSearchState,
  GlobalSearchResultItem,
  SearchEntityType,
  SearchViewMode,
  UseGlobalSearchOptions,
  UseGlobalSearchReturn,
  GlobalSearchRequest,
  GlobalSearchResponse
} from '../../types/search'

/**
 * Default search configuration
 */
const DEFAULT_OPTIONS: Required<UseGlobalSearchOptions> = {
  initialQuery: '',
  initialTypes: ['all'],
  initialFilters: {},
  initialSort: 'relevance',
  initialViewMode: 'list',
  debounceMs: 300,
  autoSearch: true,
  persistState: true
}

/**
 * Search entity configurations
 */
const SEARCH_ENTITY_CONFIGS = {
  cases: {
    label: 'Cases',
    icon: 'heroicons:briefcase',
    color: 'blue',
    endpoint: '/search/cases'
  },
  documents: {
    label: 'Documents',
    icon: 'heroicons:document-text',
    color: 'green',
    endpoint: '/search/documents'
  },
  users: {
    label: 'Users',
    icon: 'heroicons:users',
    color: 'purple',
    endpoint: '/search/users'
  },
  clients: {
    label: 'Clients',
    icon: 'heroicons:user-group',
    color: 'orange',
    endpoint: '/search/clients'
  },
  templates: {
    label: 'Templates',
    icon: 'heroicons:document-duplicate',
    color: 'indigo',
    endpoint: '/search/templates'
  },
  all: {
    label: 'All',
    icon: 'heroicons:magnifying-glass',
    color: 'gray',
    endpoint: '/search'
  }
} as const

/**
 * Global search composable
 */
export function useGlobalSearch(options: UseGlobalSearchOptions = {}): UseGlobalSearchReturn {
  const config = { ...DEFAULT_OPTIONS, ...options }
  const router = useRouter()
  const route = useRoute()
  const { get } = useApi()
  const { showToast } = useToast()
  
  // ============================================================================
  // STATE MANAGEMENT
  // ============================================================================
  
  const state = ref<GlobalSearchState>({
    query: config.initialQuery,
    selectedTypes: config.initialTypes,
    filters: { ...config.initialFilters },
    sortBy: config.initialSort,
    viewMode: config.initialViewMode,
    isLoading: false,
    results: [],
    total: 0,
    totalByType: {
      cases: 0,
      documents: 0,
      users: 0,
      clients: 0,
      templates: 0,
      all: 0
    },
    suggestions: [],
    error: null,
    hasSearched: false
  })
  
  // Persistent state storage
  const { save: saveState, load: loadState } = useLocalStorage<Partial<GlobalSearchState>>(
    'global-search-state',
    {}
  )
  
  // ============================================================================
  // COMPUTED PROPERTIES
  // ============================================================================
  
  const isSearching = computed(() => state.value.isLoading)
  
  const hasResults = computed(() => state.value.results.length > 0)
  
  const isEmpty = computed(() => 
    state.value.hasSearched && !state.value.isLoading && state.value.results.length === 0
  )
  
  const resultsByType = computed(() => {
    const grouped: Record<SearchEntityType, GlobalSearchResultItem[]> = {
      cases: [],
      documents: [],
      users: [],
      clients: [],
      templates: [],
      all: []
    }
    
    state.value.results.forEach(result => {
      if (grouped[result.type]) {
        grouped[result.type].push(result)
      }
      grouped.all.push(result)
    })
    
    return grouped
  })
  
  // ============================================================================
  // DEBOUNCED SEARCH
  // ============================================================================
  
  const debouncedSearch = useDebounce(async (query: string) => {
    if (!query.trim() && !config.autoSearch) return
    await performSearch(query)
  }, config.debounceMs)
  
  // ============================================================================
  // CORE METHODS
  // ============================================================================
  
  /**
   * Perform the actual search
   */
  async function performSearch(query?: string): Promise<void> {
    const searchQuery = query ?? state.value.query
    
    if (!searchQuery.trim()) {
      clearSearch()
      return
    }
    
    state.value.isLoading = true
    state.value.error = null
    
    try {
      const searchRequest: GlobalSearchRequest = {
        query: searchQuery,
        types: state.value.selectedTypes.includes('all') ? undefined : state.value.selectedTypes,
        filters: state.value.filters,
        sort: {
          field: state.value.sortBy === 'relevance' ? 'score' : state.value.sortBy,
          direction: 'desc'
        },
        pagination: {
          page: 1,
          limit: 50
        },
        highlight: true,
        facets: true
      }
      
      const response = await get<GlobalSearchResponse>('/search', searchRequest)
      
      if (response.success) {
        state.value.results = response.data.results
        state.value.total = response.data.total
        state.value.totalByType = response.data.totalByType || state.value.totalByType
        state.value.suggestions = response.data.suggestions || []
        state.value.hasSearched = true
        
        // Update URL with search parameters
        await updateUrlParams()
        
        // Save state if persistence is enabled
        if (config.persistState) {
          saveSearchState()
        }
      } else {
        throw new Error('Search request failed')
      }
    } catch (error) {
      console.error('Search error:', error)
      state.value.error = error instanceof Error ? error.message : 'Search failed'
      showToast({
        type: 'error',
        title: 'Search Error',
        message: 'Failed to perform search. Please try again.'
      })
    } finally {
      state.value.isLoading = false
    }
  }
  
  /**
   * Clear search results and state
   */
  function clearSearch(): void {
    state.value.query = ''
    state.value.results = []
    state.value.total = 0
    state.value.totalByType = {
      cases: 0,
      documents: 0,
      users: 0,
      clients: 0,
      templates: 0,
      all: 0
    }
    state.value.suggestions = []
    state.value.error = null
    state.value.hasSearched = false
    
    // Clear URL parameters
    router.replace({ query: {} })
  }
  
  // ============================================================================
  // STATE SETTERS
  // ============================================================================
  
  function setQuery(query: string): void {
    state.value.query = query
    if (config.autoSearch) {
      debouncedSearch(query)
    }
  }
  
  function setTypes(types: SearchEntityType[]): void {
    state.value.selectedTypes = types
    if (state.value.hasSearched) {
      performSearch()
    }
  }
  
  function setFilters(filters: Record<string, any>): void {
    state.value.filters = { ...filters }
    if (state.value.hasSearched) {
      performSearch()
    }
  }
  
  function setSortBy(sortBy: string): void {
    state.value.sortBy = sortBy
    if (state.value.hasSearched) {
      performSearch()
    }
  }
  
  function setViewMode(viewMode: SearchViewMode): void {
    state.value.viewMode = viewMode
    if (config.persistState) {
      saveSearchState()
    }
  }
  
  function toggleType(type: SearchEntityType): void {
    const types = [...state.value.selectedTypes]
    const index = types.indexOf(type)
    
    if (index > -1) {
      types.splice(index, 1)
    } else {
      types.push(type)
    }
    
    // Ensure at least one type is selected
    if (types.length === 0) {
      types.push('all')
    }
    
    setTypes(types)
  }
  
  function addFilter(key: string, value: any): void {
    state.value.filters[key] = value
    if (state.value.hasSearched) {
      performSearch()
    }
  }
  
  function removeFilter(key: string): void {
    delete state.value.filters[key]
    if (state.value.hasSearched) {
      performSearch()
    }
  }
  
  function clearFilters(): void {
    state.value.filters = {}
    if (state.value.hasSearched) {
      performSearch()
    }
  }
  
  // ============================================================================
  // UTILITY METHODS
  // ============================================================================
  
  function getResultUrl(result: GlobalSearchResultItem): string {
    return result.url || `#${result.id}`
  }
  
  function getResultIcon(result: GlobalSearchResultItem): string {
    return result.icon || SEARCH_ENTITY_CONFIGS[result.type]?.icon || 'heroicons:document'
  }
  
  function getResultColor(result: GlobalSearchResultItem): string {
    return SEARCH_ENTITY_CONFIGS[result.type]?.color || 'gray'
  }
  
  function formatResultDate(date: string): string {
    return new Date(date).toLocaleDateString()
  }
  
  // ============================================================================
  // INTERNAL HELPERS
  // ============================================================================
  
  async function updateUrlParams(): Promise<void> {
    const query: Record<string, any> = {}
    
    if (state.value.query) query.q = state.value.query
    if (state.value.selectedTypes.length > 0 && !state.value.selectedTypes.includes('all')) {
      query.types = state.value.selectedTypes.join(',')
    }
    if (Object.keys(state.value.filters).length > 0) {
      query.filters = JSON.stringify(state.value.filters)
    }
    if (state.value.sortBy !== 'relevance') query.sort = state.value.sortBy
    if (state.value.viewMode !== 'list') query.view = state.value.viewMode
    
    await router.replace({ query })
  }
  
  function saveSearchState(): void {
    if (!config.persistState) return
    
    saveState({
      selectedTypes: state.value.selectedTypes,
      filters: state.value.filters,
      sortBy: state.value.sortBy,
      viewMode: state.value.viewMode
    })
  }
  
  function loadSearchState(): void {
    if (!config.persistState) return
    
    const savedState = loadState()
    if (savedState) {
      if (savedState.selectedTypes) state.value.selectedTypes = savedState.selectedTypes
      if (savedState.filters) state.value.filters = savedState.filters
      if (savedState.sortBy) state.value.sortBy = savedState.sortBy
      if (savedState.viewMode) state.value.viewMode = savedState.viewMode
    }
  }
  
  // ============================================================================
  // INITIALIZATION
  // ============================================================================
  
  // Load persisted state on initialization
  if (config.persistState) {
    loadSearchState()
  }
  
  // Initialize from URL parameters
  if (route.query.q) {
    state.value.query = route.query.q as string
  }
  if (route.query.types) {
    state.value.selectedTypes = (route.query.types as string).split(',') as SearchEntityType[]
  }
  if (route.query.filters) {
    try {
      state.value.filters = JSON.parse(route.query.filters as string)
    } catch (e) {
      console.warn('Failed to parse filters from URL')
    }
  }
  if (route.query.sort) {
    state.value.sortBy = route.query.sort as string
  }
  if (route.query.view) {
    state.value.viewMode = route.query.view as SearchViewMode
  }
  
  // Perform initial search if query exists
  if (state.value.query && config.autoSearch) {
    nextTick(() => {
      performSearch()
    })
  }
  
  // ============================================================================
  // RETURN INTERFACE
  // ============================================================================
  
  return {
    // State
    state,
    
    // Computed
    isSearching,
    hasResults,
    isEmpty,
    resultsByType,
    
    // Methods
    search: performSearch,
    clearSearch,
    setQuery,
    setTypes,
    setFilters,
    setSortBy,
    setViewMode,
    toggleType,
    addFilter,
    removeFilter,
    clearFilters,
    
    // Utilities
    getResultUrl,
    getResultIcon,
    getResultColor,
    formatResultDate
  }
}
