import { define<PERSON><PERSON><PERSON><PERSON><PERSON>, get<PERSON><PERSON><PERSON>, getRequestHead<PERSON>, setResponseStatus, send } from 'h3';
import { type H3Event } from 'h3';
import { createApiInstance } from '~/server/utils/apiInstance';
import type { AxiosRequestConfig, AxiosError } from 'axios';
import { useRuntimeConfig } from '#imports';

export default defineEventHandler(async (event: H3Event) => {
  const config = useRuntimeConfig();
  const apiInstance = createApiInstance(event);

  // Extract the part of the path after /api/storage/uploads/
  // event.context.params._ is the catch-all parameter from Nuxt/Nitro
  const path = event.context.params?._ || '';
  const method = event.method;
  const query = getQuery(event);
  const headers = getRequestHeaders(event);

  console.log(`[Storage Uploads Proxy] Proxying ${method} request from /storage/uploads/${path} to /uploads/${path}`);

  // Prepare the request config for Axios
  const requestConfig: AxiosRequestConfig = {
    method: method as any,
    url: `${config.public.apiBase}/uploads/${path}`,
    params: query,
    headers: {},
    responseType: 'arraybuffer', // Important for binary files like images, PDFs, etc.
  };

  // Construct headers object carefully
  const forwardedHeaders: Record<string, string> = {};

  // Forward relevant headers
  if (headers['accept']) {
    forwardedHeaders['Accept'] = headers['accept'];
  }
  if (headers['accept-language']) {
    forwardedHeaders['Accept-Language'] = headers['accept-language'];
  }
  if (headers['range']) {
    forwardedHeaders['Range'] = headers['range']; // For partial content requests
  }
  if (headers['if-none-match']) {
    forwardedHeaders['If-None-Match'] = headers['if-none-match']; // For caching
  }
  if (headers['if-modified-since']) {
    forwardedHeaders['If-Modified-Since'] = headers['if-modified-since']; // For caching
  }

  // Authorization header will be added by apiInstance interceptor
  requestConfig.headers = forwardedHeaders;

  try {
    const response = await apiInstance.request(requestConfig);

    console.log(`[Storage Uploads Proxy] Successfully proxied file: ${response.status} ${response.statusText}`);

    // Set status and headers from the backend response
    setResponseStatus(event, response.status, response.statusText);
    
    // Forward important headers for file serving
    Object.entries(response.headers).forEach(([key, value]) => {
      const lowerKey = key.toLowerCase();
      // Forward headers that are important for file serving
      if (
        lowerKey === 'content-type' ||
        lowerKey === 'content-length' ||
        lowerKey === 'content-disposition' ||
        lowerKey === 'cache-control' ||
        lowerKey === 'etag' ||
        lowerKey === 'last-modified' ||
        lowerKey === 'expires' ||
        lowerKey === 'accept-ranges' ||
        lowerKey === 'content-range'
      ) {
        if (value) {
          event.node.res.setHeader(key, value as string | string[]);
        }
      }
    });

    // Add security headers for file serving
    event.node.res.setHeader('X-Content-Type-Options', 'nosniff');
    
    // Send the response body
    // response.data is an ArrayBuffer due to responseType: 'arraybuffer'
    return send(event, Buffer.from(response.data));

  } catch (error) {
    const axiosError = error as AxiosError;
    console.error(`[Storage Uploads Proxy] Error proxying file ${method} from /storage/uploads/${path} to /uploads/${path}:`, axiosError.message);

    if (axiosError.response) {
      setResponseStatus(event, axiosError.response.status, axiosError.response.statusText);
      
      // Forward error response headers
      Object.entries(axiosError.response.headers).forEach(([key, value]) => {
        const lowerKey = key.toLowerCase();
        if (
          lowerKey === 'content-type' ||
          lowerKey === 'cache-control' ||
          lowerKey === 'expires'
        ) {
          if (value) {
            event.node.res.setHeader(key, value as string | string[]);
          }
        }
      });

      // Handle error response data
      const responseData = axiosError.response.data;
      if (responseData instanceof ArrayBuffer) {
        return send(event, Buffer.from(responseData));
      }
      return send(event, responseData);
    } else {
      // Network error or other issue
      setResponseStatus(event, 500, 'Storage File Proxy Internal Server Error');
      return { error: 'Storage file proxy request failed', details: axiosError.message };
    }
  }
});
