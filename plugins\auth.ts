// plugins/auth.ts
import { defineNuxtPlugin, type NuxtApp } from '#app';
import { useAuthStore } from '~/stores/auth';
import type { Pinia } from 'pinia'; // Import Pinia type for type hinting

/**
 * Nuxt.js Authentication Plugin
 *
 * This plugin runs before the main Vue application is mounted.
 * It's responsible for initializing the authentication state
 * from server-provided data (via HttpOnly cookies and Nuxt payload hydration)
 * or client-side storage (if applicable, e.g., for some non-sensitive flags).
 * It also provides a global `$auth` utility.
 */
export default defineNuxtPlugin(async (nuxtApp) => {
  // Ensure we get the Pinia instance.
  // In Nuxt 3, `nuxtApp.$pinia` is typically available within plugins after `@pinia/nuxt` module.
  const pinia = nuxtApp.$pinia as Pinia;
  const authStore = useAuthStore(pinia);

  // CRITICAL: Initialize the authentication state.
  // This will trigger the `initAuth` action in your auth store,
  // which handles:
  // - On server (SSR): Attempting to fetch user data using HttpOnly access_token cookie from the Nuxt server's incoming request.
  // - On client (hydration/subsequent navigation): Rehydrating state from the Nuxt payload, or fetching if needed.
  try {
    await authStore.initAuth();
  } catch (error) {
    console.warn('Auth Plugin: Failed to initialize auth, continuing gracefully:', error);
    // Don't throw to prevent breaking app initialization
  }

  // Provide a global $auth utility.
  // This makes auth state and actions easily accessible throughout your Nuxt app.
  // Example usage in a component: `const { $auth } = useNuxtApp();`
  nuxtApp.provide('auth', {
    /**
     * Checks if the user is authenticated.
     * @returns {boolean} True if authenticated, false otherwise.
     */
    isAuthenticated: () => authStore.isAuthenticated,

    /**
     * Returns the current authenticated user object.
     * @returns {User | null} The user object or null.
     */
    currentUser: () => authStore.currentUser,

    /**
     * Returns the current user's platform roles.
     * @returns {string[]} An array of platform role strings.
     */
    platformUserRoles: () => authStore.platformUserRoles,

    /**
     * Returns the current user's active tenant roles.
     * @returns {string[]} An array of tenant role strings.
     */
    activeTenantUserRoles: () => authStore.activeTenantUserRoles,

    /**
     * Returns all user roles (platform + tenant).
     * @returns {string[]} An array of all role strings.
     */
    allUserRoles: () => authStore.allUserRoles,

    /**
     * Initiates the login process.
     * @param {object} credentials - User login credentials (email, password).
     * @returns {Promise<void>} A promise that resolves on successful login.
     */
    login: authStore.login, // Directly expose the action

    /**
     * Initiates the logout process.
     * @returns {Promise<void>} A promise that resolves on successful logout.
     */
    logout: authStore.logout, // Directly expose the action

    /**
     * Forces a re-fetch of the user profile and updates auth state.
     * @returns {Promise<void>} A promise that resolves after fetching.
     */
    fetchUser: authStore.fetchUser, // Directly expose the action

    /**
     * Checks if any authentication operation is currently in progress.
     * @returns {boolean} True if loading, false otherwise.
     */
    isLoading: () => authStore.isLoading,

    /**
     * Returns any authentication-related error message.
     * @returns {string | null} The error message or null.
     */
    error: () => authStore.authError,
  });
});