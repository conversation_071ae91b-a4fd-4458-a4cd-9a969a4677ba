/**
 * Shared API Types
 * 
 * Common types used across multiple stores and API calls
 */

// Interface for common query parameters used in GET requests
export interface GetRequestQuery {
  page?: number;
  limit?: number;
  search?: string;
  sort?: string; // Format: fieldName:(ASC|DESC), e.g., "createdAt:DESC"
  select?: string; // Comma-separated field names
}

// Common API response wrapper
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

// Pagination metadata
export interface PaginationMeta {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

// Paginated response wrapper
export interface PaginatedResponse<T = any> {
  success: boolean;
  data: T[];
  pagination: PaginationMeta;
  message?: string;
}

// Common error response
export interface ApiError {
  success: false;
  error: string;
  message: string;
  statusCode?: number;
  details?: any;
}

// HTTP methods
export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';

// Request configuration
export interface RequestConfig {
  method?: HttpMethod;
  headers?: Record<string, string>;
  params?: Record<string, any>;
  data?: any;
  timeout?: number;
}
