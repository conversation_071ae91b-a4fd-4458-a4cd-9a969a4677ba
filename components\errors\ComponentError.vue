<!--
  Component Error Component
  
  Displays an error when a component fails to load or render
  Used as an error boundary for dynamic component loading
-->

<template>
  <div class="min-h-[40vh] flex items-center justify-center px-4 py-8">
    <div class="max-w-lg w-full text-center">
      <!-- Error illustration -->
      <div class="mb-6">
        <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100 mb-4">
          <Icon name="heroicons:exclamation-triangle" class="h-8 w-8 text-red-500" />
        </div>
      </div>

      <!-- Error message -->
      <h2 class="text-xl font-semibold text-gray-900 mb-3">
        {{ title }}
      </h2>
      
      <p class="text-gray-600 mb-6 text-sm leading-relaxed">
        {{ message }}
      </p>

      <!-- Error details (development only) -->
      <div v-if="showDetails && errorDetails" class="mb-6">
        <details class="text-left bg-gray-50 rounded-lg p-4">
          <summary class="cursor-pointer text-sm font-medium text-gray-700 hover:text-gray-900 mb-2">
            Error Details
          </summary>
          <div class="bg-gray-100 rounded p-3 text-xs font-mono text-gray-800 overflow-auto max-h-32 mt-2">
            <pre>{{ errorDetails }}</pre>
          </div>
        </details>
      </div>

      <!-- Action buttons -->
      <div class="flex flex-col sm:flex-row gap-3 justify-center mb-6">
        <button
          type="button"
          class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200"
          @click="retry"
        >
          <Icon name="heroicons:arrow-path" class="h-4 w-4 mr-2" />
          Try Again
        </button>

        <button
          type="button"
          class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200"
          @click="goBack"
        >
          <Icon name="heroicons:arrow-left" class="h-4 w-4 mr-2" />
          Go Back
        </button>
      </div>

      <!-- Help section -->
      <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div class="flex items-start">
          <Icon name="heroicons:information-circle" class="h-5 w-5 text-blue-600 mt-0.5 mr-3 flex-shrink-0" />
          <div class="text-left">
            <h3 class="text-sm font-medium text-blue-800 mb-2">
              What happened?
            </h3>
            <p class="text-xs text-blue-700 mb-3">
              This component failed to load properly. This could be due to a network issue, 
              server problem, or a temporary glitch.
            </p>
            
            <div class="flex flex-wrap gap-2">
              <button
                type="button"
                class="inline-flex items-center px-3 py-1.5 border border-blue-300 text-xs font-medium rounded text-blue-700 bg-white hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
                @click="reportError"
              >
                <Icon name="heroicons:bug-ant" class="h-3 w-3 mr-1" />
                Report Issue
              </button>
              
              <button
                type="button"
                class="inline-flex items-center px-3 py-1.5 border border-blue-300 text-xs font-medium rounded text-blue-700 bg-white hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
                @click="refreshPage"
              >
                <Icon name="heroicons:arrow-path" class="h-3 w-3 mr-1" />
                Refresh Page
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Alternative actions -->
      <div class="mt-6 text-center" v-if="showAlternatives">
        <p class="text-xs text-gray-500 mb-3">
          Or try these alternatives:
        </p>
        
        <div class="flex flex-wrap justify-center gap-2">
          <NuxtLink
            to="/dashboard"
            class="text-xs text-indigo-600 hover:text-indigo-500 underline transition-colors duration-200"
          >
            Dashboard
          </NuxtLink>
          
          <span class="text-xs text-gray-300">•</span>
          
          <button
            type="button"
            class="text-xs text-indigo-600 hover:text-indigo-500 underline transition-colors duration-200"
            @click="contactSupport"
          >
            Contact Support
          </button>
          
          <span class="text-xs text-gray-300">•</span>
          
          <NuxtLink
            to="/dashboard/help"
            class="text-xs text-indigo-600 hover:text-indigo-500 underline transition-colors duration-200"
          >
            Help Center
          </NuxtLink>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { useRouter } from 'vue-router'

// Props
interface Props {
  title?: string
  message?: string
  error?: Error | string
  showDetails?: boolean
  showAlternatives?: boolean
  componentName?: string
}

const props = withDefaults(defineProps<Props>(), {
  title: 'Component Error',
  message: 'This component failed to load. Please try refreshing the page or contact support if the problem persists.',
  showDetails: false,
  showAlternatives: true,
  componentName: 'Unknown Component'
})

// Emits
const emit = defineEmits<{
  retry: []
  report: [error: Error | string]
}>()

// Composables
const router = useRouter()

// Computed
const errorDetails = computed(() => {
  if (!props.error) return null
  
  if (typeof props.error === 'string') {
    return props.error
  }
  
  return {
    name: props.error.name,
    message: props.error.message,
    stack: props.error.stack,
    componentName: props.componentName,
    timestamp: new Date().toISOString(),
    userAgent: navigator.userAgent,
    url: window.location.href
  }
})

const isDevelopment = computed(() => {
  return process.env.NODE_ENV === 'development'
})

// Methods
const retry = () => {
  emit('retry')
  
  // Force component re-render by triggering a route refresh
  router.go(0)
}

const goBack = () => {
  if (window.history.length > 1) {
    router.back()
  } else {
    router.push('/dashboard')
  }
}

const refreshPage = () => {
  window.location.reload()
}

const reportError = () => {
  if (props.error) {
    emit('report', props.error)
  }
  
  // Example error reporting
  console.error('Component Error Report:', {
    error: props.error,
    componentName: props.componentName,
    timestamp: new Date().toISOString(),
    url: window.location.href,
    userAgent: navigator.userAgent
  })
  
  // In a real app, this would send to an error reporting service
  alert('Error report sent. Thank you for helping us improve!')
}

const contactSupport = () => {
  // Open support contact method
  console.log('Contact support about component error')
  alert('Support contact functionality would be implemented here')
}

// Auto-report critical errors in development
if (isDevelopment.value && props.error) {
  console.error('Component Error:', props.error)
}
</script>

<style scoped>
/* Error state animations */
.error-icon {
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .error-container {
    padding: 1rem;
  }
}

/* Focus styles for accessibility */
button:focus,
a:focus {
  outline: none;
  box-shadow: 0 0 0 2px #6366f1, 0 0 0 4px rgba(99, 102, 241, 0.2);
}

/* Error details styling */
details summary {
  outline: none;
}

details summary:focus {
  box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.5);
  border-radius: 4px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .bg-blue-50 {
    background-color: #eff6ff;
    border: 2px solid #3b82f6;
  }
  
  .bg-gray-50 {
    background-color: #f9fafb;
    border: 1px solid #d1d5db;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .error-icon {
    animation: none;
  }
  
  * {
    transition: none !important;
  }
}
</style>
