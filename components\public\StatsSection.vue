<template>
  <section :class="sectionClass">
    <div class="container mx-auto px-6">
      <!-- Section Header -->
      <div v-if="title || subtitle" :class="headerClass">
        <h2 v-if="title" :class="titleClass">
          {{ title }}
        </h2>
        <p v-if="subtitle" :class="subtitleClass">
          {{ subtitle }}
        </p>
      </div>

      <!-- Stats Grid -->
      <div :class="gridClass">
        <div
          v-for="(stat, index) in stats"
          :key="index"
          :class="statCardClass"
          :style="{ animationDelay: `${index * 100}ms` }"
        >
          <!-- Stat Icon -->
          <div v-if="stat.icon" :class="iconContainerClass">
            <Icon :name="stat.icon" :class="iconClass" />
          </div>

          <!-- Stat Value -->
          <div :class="valueClass">
            <span v-if="stat.prefix" class="text-2xl">{{ stat.prefix }}</span>
            <AnimatedCounter
              :target="typeof stat.value === 'number' ? stat.value : 0"
              :duration="animationDuration"
              :format="stat.format"
            />
            <span v-if="stat.suffix" class="text-2xl">{{ stat.suffix }}</span>
          </div>

          <!-- Stat Label -->
          <div :class="labelClass">
            {{ stat.label }}
          </div>

          <!-- Stat Description -->
          <div v-if="stat.description" :class="descriptionClass">
            {{ stat.description }}
          </div>

          <!-- Stat Trend -->
          <div v-if="stat.trend" class="flex items-center justify-center mt-2">
            <Icon
              :name="stat.trend.direction === 'up' ? 'material-symbols:trending-up' : 'material-symbols:trending-down'"
              :class="[
                'w-4 h-4 mr-1',
                stat.trend.direction === 'up' ? 'text-green-500' : 'text-red-500'
              ]"
            />
            <span :class="[
              'text-sm font-medium',
              stat.trend.direction === 'up' ? 'text-green-500' : 'text-red-500'
            ]">
              {{ stat.trend.value }}{{ stat.trend.unit || '%' }}
            </span>
            <span class="text-sm text-gray-500 dark:text-gray-400 ml-1">
              {{ stat.trend.period || 'vs last month' }}
            </span>
          </div>
        </div>
      </div>

      <!-- Bottom Content -->
      <div v-if="$slots.bottom" class="mt-12">
        <slot name="bottom" />
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
interface StatTrend {
  direction: 'up' | 'down'
  value: number
  unit?: string
  period?: string
}

interface Stat {
  label: string
  value: number | string
  prefix?: string
  suffix?: string
  icon?: string
  description?: string
  format?: 'number' | 'currency' | 'percentage'
  trend?: StatTrend
}

interface Props {
  title?: string
  subtitle?: string
  stats: Stat[]
  variant?: 'default' | 'cards' | 'minimal'
  columns?: 2 | 3 | 4 | 5
  background?: 'white' | 'gray' | 'dark' | 'primary'
  textAlign?: 'left' | 'center'
  animationDuration?: number
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'default',
  columns: 4,
  background: 'white',
  textAlign: 'center',
  animationDuration: 2000
})

// Computed classes
const sectionClass = computed(() => {
  const backgrounds = {
    white: 'bg-white',
    gray: 'bg-gray-50',
    dark: 'bg-gray-900 text-white',
    primary: 'bg-brandPrimary-600 text-white'
  }
  return `py-16 ${backgrounds[props.background]}`
})

const headerClass = computed(() => {
  const alignments = {
    left: 'text-left mb-12',
    center: 'text-center mb-12'
  }
  return alignments[props.textAlign]
})

const titleClass = computed(() => {
  const baseClass = 'font-bold mb-4'
  const colorClass = ['dark', 'primary'].includes(props.background) ? 'text-white' : 'text-gray-800'
  return `${baseClass} text-3xl md:text-4xl ${colorClass}`
})

const subtitleClass = computed(() => {
  const baseClass = 'max-w-3xl'
  const colorClass = ['dark', 'primary'].includes(props.background) ? 'text-gray-300' : 'text-gray-600'
  const alignClass = props.textAlign === 'center' ? 'mx-auto' : ''
  return `${baseClass} text-lg ${colorClass} ${alignClass}`
})

const gridClass = computed(() => {
  const columns = {
    2: 'grid-cols-1 md:grid-cols-2',
    3: 'grid-cols-1 md:grid-cols-3',
    4: 'grid-cols-2 md:grid-cols-4',
    5: 'grid-cols-2 md:grid-cols-3 lg:grid-cols-5'
  }
  return `grid ${columns[props.columns]} gap-8`
})

const statCardClass = computed(() => {
  const baseClass = 'text-center animate-fade-in-up'
  
  if (props.variant === 'cards') {
    const cardBg = ['dark', 'primary'].includes(props.background) 
      ? 'bg-white/10 backdrop-blur-sm border border-white/20' 
      : 'bg-white border border-gray-200 shadow-sm hover:shadow-lg'
    return `${baseClass} p-6 rounded-xl transition-all duration-300 ${cardBg}`
  }
  
  if (props.variant === 'minimal') {
    return `${baseClass} p-4 hover:bg-gray-50 dark:hover:bg-gray-800 rounded-lg transition-colors duration-200`
  }
  
  return `${baseClass} p-6`
})

const iconContainerClass = computed(() => {
  const baseClass = 'flex justify-center mb-4'
  const colorClass = ['dark', 'primary'].includes(props.background) ? 'text-white/80' : 'text-brandPrimary-600'
  return `${baseClass} ${colorClass}`
})

const iconClass = computed(() => {
  return 'w-8 h-8'
})

const valueClass = computed(() => {
  const baseClass = 'font-extrabold mb-2'
  const sizeClass = 'text-3xl md:text-4xl'
  const colorClass = ['dark', 'primary'].includes(props.background) ? 'text-white' : 'text-gray-900'
  return `${baseClass} ${sizeClass} ${colorClass}`
})

const labelClass = computed(() => {
  const baseClass = 'font-semibold'
  const colorClass = ['dark', 'primary'].includes(props.background) ? 'text-white/90' : 'text-gray-700'
  return `${baseClass} ${colorClass}`
})

const descriptionClass = computed(() => {
  const baseClass = 'text-sm mt-2'
  const colorClass = ['dark', 'primary'].includes(props.background) ? 'text-white/70' : 'text-gray-600'
  return `${baseClass} ${colorClass}`
})
</script>

<style scoped>
@keyframes fadeInFromBottom {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  animation: fadeInFromBottom 0.6s ease-out forwards;
  opacity: 0;
}
</style>
