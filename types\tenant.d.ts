// Define the Tenant interface
// This is a simplified example, extend as needed based on actual backend response
export interface Tenant {
    id: string;
    name: string;
    slug?: string;
    status: 'active' | 'pending' | 'suspended'; // Example statuses
    createdAt: string;
    plan?: string;
    ownerId?: string;
    usage?: any; // Usage statistics
    industry?: string;
    website?: string;
    // Add other properties like 'ownerEmail', etc.
}
// Payload for creating a new tenant
export interface TenantCreationPayload {
  name: string;
  email: string; // Primary contact email for the tenant
  owner: {
    firstName: string;
    lastName: string;
    email: string; // Owner's email, can be same as tenant email or different
    password?: string; // Initial password, backend might enforce strength
  };
  subscriptionPlanId: string; // ID of the selected subscription plan
  defaultLanguage: string; // e.g., 'en', 'he'
  // Add any other fields required by the backend
}

// Response after creating a tenant (example, adjust as per actual API)
export interface TenantCreationResponse extends Tenant {
  // Potentially includes the full tenant object
  // Or could be a simpler success message structure
  message?: string;
}