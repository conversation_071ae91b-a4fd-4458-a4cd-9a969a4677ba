<template>
  <div>
    <label v-if="label" :for="inputId" class="block text-sm font-medium text-gray-700 mb-1">
      {{ label }}
    </label>

    <div v-if="selectedRecipients.length > 0" class="mb-3 flex flex-wrap gap-2">
      <span
        v-for="recipient in selectedRecipients"
        :key="getRec<PERSON><PERSON>Key(recipient)"
        :class="[
          'inline-flex items-center px-3 py-1 rounded-full text-sm font-medium',
          getRecipientClasses(recipient.type)
        ]"
      >
        <template v-if="recipient.type === 'user'">
          <img v-if="recipient.avatarUrl" :src="recipient.avatarUrl" class="h-5 w-5 rounded-full mr-2" alt="User avatar" />
          {{ recipient.name || recipient.email }}
        </template>
        <template v-else-if="recipient.type === 'role'">
          <Icon name="heroicons:tag" class="h-4 w-4 mr-1" />
          {{ recipient.label }}
        </template>
        <template v-else>
          <Icon name="heroicons:at-symbol" class="h-4 w-4 mr-1" />
          {{ recipient.value }}
        </template>
        <button
          type="button"
          @click="removeRecipient(recipient)"
          class="flex-shrink-0 ml-1.5 h-4 w-4 rounded-full inline-flex items-center justify-center text-current hover:opacity-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-current"
        >
          <span class="sr-only">Remove {{ getRecipientLabel(recipient) }}</span>
          <svg class="h-2 w-2" stroke="currentColor" fill="none" viewBox="0 0 8 8">
            <path stroke-linecap="round" stroke-width="1.5" d="M1 1l6 6m0-6L1 7" />
          </svg>
        </button>
      </span>
    </div>

    <div class="relative">
      <UiInput
        :id="inputId"
        v-model="searchText"
        @input="handleSearchInput"
        @keydown.enter.prevent="addCustomEmail"
        @keydown.down="handleKeydown('down')"
        @keydown.up="handleKeydown('up')"
        @keydown.tab="handleKeydown('tab')"
        :placeholder="placeholder"
        :disabled="disabled"
        :error="inputError"
        class="w-full"
      />
      <p v-if="helpText" class="mt-1 text-sm text-gray-500">{{ helpText }}</p>

      <div v-if="showDropdown" class="absolute z-10 mt-1 w-full bg-white shadow-lg rounded-md border border-gray-200 py-1 max-h-60 overflow-auto focus:outline-none">
        <ul role="listbox">
          <li
            v-for="(option, index) in filteredOptions"
            :key="option.value + '-' + option.type"
            @click="selectOption(option)"
            :class="[
              'cursor-pointer select-none relative py-2 pl-3 pr-9 text-gray-900',
              {'bg-indigo-600 text-white': activeOptionIndex === index, 'text-gray-900': activeOptionIndex !== index}
            ]"
            role="option"
            :aria-selected="activeOptionIndex === index"
          >
            <div class="flex items-center">
              <template v-if="option.type === 'user'">
                <img v-if="option.data?.avatarUrl" :src="option.data.avatarUrl" class="h-6 w-6 rounded-full mr-2" alt="User avatar" />
                <Icon v-else name="heroicons:user-circle" class="h-6 w-6 text-gray-400 mr-2" />
              </template>
              <template v-else-if="option.type === 'role'">
                <Icon name="heroicons:tag" class="h-5 w-5 text-gray-500 mr-2" />
              </template>
              <template v-else-if="option.type === 'email'">
                <Icon name="heroicons:envelope" class="h-5 w-5 text-gray-500 mr-2" />
              </template>
              <span class="font-normal truncate">{{ option.label }}</span>
            </div>
            <span v-if="activeOptionIndex === index" class="absolute inset-y-0 right-0 flex items-center pr-4">
              <Icon name="heroicons:check" class="h-5 w-5" />
            </span>
          </li>
          <li v-if="!hasMatchingOption && searchText.length > 0"
            class="relative py-2 pl-3 pr-9 text-gray-700 cursor-pointer"
            @click="addCustomEmail()"
            >
            <span class="font-normal">Add email: <strong>{{ searchText }}</strong> (Press Enter)</span>
          </li>
          <li v-if="filteredOptions.length === 0 && searchText.length === 0 && !loadingSuggestions"
            class="relative py-2 pl-3 pr-9 text-gray-500">
            No suggestions
          </li>
          <li v-if="loadingSuggestions" class="relative py-2 pl-3 pr-9 text-gray-500">
            <UiSpinner class="inline-block h-4 w-4 mr-2" /> Loading...
          </li>
        </ul>
      </div>
    </div>
     <p v-if="validationError" class="mt-1 text-sm text-red-600">{{ validationError }}</p>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, defineProps, defineEmits } from 'vue';
import { useApi } from '~/composables/useApi'; // Assuming useApi is correctly implemented
import {  TenantRoles } from '~/app/features/auth/constants/roles';
import type { UserRecipient, RoleRecipient, EmailRecipient, Recipient, RecipientOption } from '~/types/recipient';
import UiInput from '~/components/ui/UiInput.vue';
import UiSpinner from '~/components/ui/UiSpinner.vue';

// Props and Emits
const props = defineProps<{
  label?: string;
  placeholder?: string;
  modelValue: Recipient[];
  disabled?: boolean;
  helpText?: string;
  allowCustomEmails?: boolean;
}>();

const emit = defineEmits(['update:modelValue']);

const { get } = useApi();

// Internal state
const inputId = `recipient-select-${Math.random().toString(36).substr(2, 9)}`;
const searchText = ref('');
const selectedRecipients = ref<Recipient[]>(props.modelValue || []);
const suggestions = ref<RecipientOption[]>([]);
const loadingSuggestions = ref(false);
const showDropdown = ref(false);
const activeOptionIndex = ref(-1); // For keyboard navigation
const inputError = ref<string | null>(null);
const validationError = ref<string | null>(null);


// Predefined static roles for selection
const staticRoleOptions: RecipientOption[] = [
  { value: TenantRoles.TENANT_OWNER, label: 'Tenant Owner Role', type: 'role', data: { type: 'role', role: TenantRoles.TENANT_OWNER, label: 'Tenant Owner Role' } as RoleRecipient },
  { value: TenantRoles.ADMIN, label: 'Admin Role', type: 'role', data: { type: 'role', role: TenantRoles.ADMIN, label: 'Admin Role' } as RoleRecipient },
  { value: TenantRoles.LAWYER, label: 'Lawyer Role', type: 'role', data: { type: 'role', role: TenantRoles.LAWYER, label: 'Lawyer Role' } as RoleRecipient },
  { value: TenantRoles.PARALEGAL, label: 'Paralegal Role', type: 'role', data: { type: 'role', role: TenantRoles.PARALEGAL, label: 'Paralegal Role' } as RoleRecipient },
  { value: TenantRoles.CLIENT, label: 'Client Role', type: 'role', data: { type: 'role', role: TenantRoles.CLIENT, label: 'Client Role' } as RoleRecipient },
  // Add SUPER_ADMIN, SUPPORT if applicable for multi-tenant scenario
];

// Computed properties
const filteredOptions = computed(() => {
  let filtered = suggestions.value.filter(option =>
    option.label.toLowerCase().includes(searchText.value.toLowerCase()) &&
    !selectedRecipients.value.some(r => r.type === option.type && (
      (r.type === 'user' && option.type === 'user' && r.id === (option.data as UserRecipient)?.id) ||
      (r.type === 'role' && option.type === 'role' && r.role === (option.data as RoleRecipient)?.role) ||
      (r.type === 'email' && option.type === 'email' && r.value === (option.data as EmailRecipient)?.value)
    ))
  );

  // Always include static role options if search is empty or matches
  if (!searchText.value) {
    filtered = [...staticRoleOptions, ...filtered];
  } else {
    const matchingRoles = staticRoleOptions.filter(role =>
      role.label.toLowerCase().includes(searchText.value.toLowerCase()) &&
      !selectedRecipients.value.some(r => r.type === 'role' && r.role === (role.data as RoleRecipient)?.role)
    );
    filtered = [...matchingRoles, ...filtered];
  }

  // Deduplicate based on value and type
  const uniqueOptions = new Map<string, RecipientOption>();
  filtered.forEach(option => {
    uniqueOptions.set(`${option.type}-${option.value}`, option);
  });
  return Array.from(uniqueOptions.values());
});

const hasMatchingOption = computed(() => {
  return filteredOptions.value.length > 0;
});

// Watchers
watch(() => props.modelValue, (newValue) => {
  selectedRecipients.value = newValue || [];
}, { deep: true, immediate: true });

watch(searchText, () => {
  inputError.value = null; // Clear input error on typing
  validationError.value = null; // Clear general validation error
});

// Methods
let debounceTimeout: ReturnType<typeof setTimeout>;
const handleSearchInput = () => {
  clearTimeout(debounceTimeout);
  if (searchText.value.length > 2) { // Start search after 2 characters
    loadingSuggestions.value = true;
    debounceTimeout = setTimeout(async () => {
      await fetchUserSuggestions(searchText.value);
      showDropdown.value = true;
      activeOptionIndex.value = filteredOptions.value.length > 0 ? 0 : -1;
      loadingSuggestions.value = false;
    }, 300);
  } else {
    suggestions.value = []; // Clear suggestions if search text is too short
    showDropdown.value = false;
    loadingSuggestions.value = false;
  }
};

const fetchUserSuggestions = async (query: string) => {
  try {
    // API call to search for users
    // Adjust endpoint and parameters based on your backend's user search API
    // Backend Integration Guide mentions GET /v1/users and GET /v1/users/email/:email 
    // A general search endpoint like /v1/users?search=query is ideal.
    const { data } = await get<{ data: any[] }>(`/v1/users`, { params: { search: query, limit: 10 } }); 
    suggestions.value = data.data.map((user: any) => ({
      value: user.id,
      label: `${user.name || user.email} (${user.email})`,
      type: 'user',
      data: { id: user.id, email: user.email, name: user.name, avatarUrl: user.avatarUrl } as UserRecipient,
    }));
  } catch (err) {
    console.error('Failed to fetch user suggestions:', err);
    suggestions.value = [];
  }
};

const selectOption = (option: RecipientOption) => {
  if (selectedRecipients.value.some(r => r.type === option.type && r.value === option.value)) {
    // Already selected, do nothing
    return;
  }

  let newRecipient: Recipient;
  if (option.type === 'user' && option.data) {
    newRecipient = option.data as UserRecipient;
  } else if (option.type === 'role' && option.data) {
    newRecipient = option.data as RoleRecipient;
  } else if (option.type === 'email' && option.data) {
    newRecipient = option.data as EmailRecipient;
  } else {
    // Fallback for custom emails entered by the user directly via selection
    newRecipient = { type: 'email', value: option.value } as EmailRecipient;
  }

  selectedRecipients.value.push(newRecipient);
  emit('update:modelValue', selectedRecipients.value);
  searchText.value = ''; // Clear input after selection
  showDropdown.value = false;
  inputError.value = null; // Clear error
};

const addCustomEmail = () => {
  if (!props.allowCustomEmails) {
    inputError.value = 'Custom email entry is not allowed for this field.';
    return;
  }

  const email = searchText.value.trim();
  if (email && validateEmail(email)) {
    if (!selectedRecipients.value.some(r => r.type === 'email' && r.value === email)) {
      selectedRecipients.value.push({ type: 'email', value: email });
      emit('update:modelValue', selectedRecipients.value);
      searchText.value = '';
      inputError.value = null;
    } else {
      inputError.value = 'This email address is already added.';
    }
  } else if (email) {
    inputError.value = 'Please enter a valid email address.';
  }
  showDropdown.value = false;
};

const removeRecipient = (recipientToRemove: Recipient) => {
  selectedRecipients.value = selectedRecipients.value.filter(recipient => {
    if (recipient.type === recipientToRemove.type) {
      if (recipient.type === 'user' && recipientToRemove.type === 'user') {
        return recipient.id !== recipientToRemove.id;
      }
      if (recipient.type === 'role' && recipientToRemove.type === 'role') {
        return recipient.role !== recipientToRemove.role;
      }
      if (recipient.type === 'email' && recipientToRemove.type === 'email') {
        return recipient.value !== recipientToRemove.value;
      }
    }
    return true; // Keep recipients of different types or non-matching values/ids
  });
  emit('update:modelValue', selectedRecipients.value);
};

const validateEmail = (email: string): boolean => {
  const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return re.test(email);
};

const getRecipientKey = (recipient: Recipient): string => {
  if (recipient.type === 'user') return `user-${recipient.id}`;
  if (recipient.type === 'role') return `role-${recipient.role}`;
  if (recipient.type === 'email') return `email-${recipient.value}`;
  return '';
};

const getRecipientLabel = (recipient: Recipient): string => {
  if (recipient.type === 'user') return recipient.name || recipient.email;
  if (recipient.type === 'role') return recipient.label;
  if (recipient.type === 'email') return recipient.value;
  return '';
};

const getRecipientClasses = (type: Recipient['type']) => {
  switch (type) {
    case 'user': return 'bg-blue-100 text-blue-800';
    case 'role': return 'bg-purple-100 text-purple-800';
    case 'email': return 'bg-gray-100 text-gray-800';
    default: return 'bg-gray-100 text-gray-800';
  }
};

const handleKeydown = (key: 'up' | 'down' | 'enter' | 'tab') => {
  if (key === 'down') {
    activeOptionIndex.value = (activeOptionIndex.value + 1) % filteredOptions.value.length;
  } else if (key === 'up') {
    activeOptionIndex.value = (activeOptionIndex.value - 1 + filteredOptions.value.length) % filteredOptions.value.length;
  } else if (key === 'enter') {
    if (activeOptionIndex.value !== -1 && filteredOptions.value.length > 0) {
      selectOption(filteredOptions.value[activeOptionIndex.value]);
    } else {
      addCustomEmail(); // Allow pressing Enter to add custom email if no suggestions or no active option
    }
  } else if (key === 'tab') {
      showDropdown.value = false; // Hide dropdown on tab out
  }
};

// Close dropdown when clicking outside
onMounted(() => {
  document.addEventListener('click', (e) => {
    const target = e.target as HTMLElement;
    if (!target.closest(`#${inputId}`) && !target.closest('.absolute.z-10')) {
      showDropdown.value = false;
    }
  });
});
</script>

<style scoped>
/* Scoped styles for multi-recipient select if needed, beyond Tailwind utilities */
</style>