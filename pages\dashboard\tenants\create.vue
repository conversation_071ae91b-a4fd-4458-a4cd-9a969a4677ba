<template>
  <div  class="space-y-6 py-6 min-h-screen max-w-7xl mx-auto">
    <forms-form ref="formRef" :schema="schema" :initial-values="initialValues" @submit="handleSubmit"
      :sticky-sidebar="true" />
  </div>
</template>

<script setup lang="ts">
import { PlatformRoles } from "~/app/features/auth/constants/roles";
import { z } from 'zod'
import { useTenantStore } from '~/stores/tenant'



// Page meta
definePageMeta({
  layout: "dashboard",
  title: () => {
    const { t } = useI18n();
    return t("tenantCreate.pageTitle");
  },
  description: () => {
    const { t } = useI18n();
    return t("tenantCreate.pageDescription");
  },
  pageHeaderIcon: "i-mdi:office-building-plus-outline",
  showPageHeader: true,
  showPageHeaderTitle: true,
  middleware: ["rbac"],
  roles: [PlatformRoles.SUPER_ADMIN],
  breadcrumbs: [
    { label: "tenantCreate.breadcrumbs.dashboard", href: "/dashboard" },
    { label: "tenantCreate.breadcrumbs.tenants", href: "/dashboard/tenants" },
    { label: "tenantCreate.breadcrumbs.create" },
  ],
  pageHeaderActions: () => {
    const { t } = useI18n();
    return [
      {
        label: t("tenantCreate.resetForm"),
        variant: "outline",
        click: () => {
          const { $global } = useNuxtApp()
          const form = ($global as any).get('tenantFormRef')
          form?.resetForm()
        },
      },
      {
        label: t("tenantCreate.cancel"),
        variant: "flat",
        click: () => navigateTo("/dashboard/tenants"),
      },
      {
        label: t("tenantCreate.createTenant"),
        type: "submit",
        loading: false,
        disabled: false,
        click: () => {
          const { $global } = useNuxtApp()
          const form = ($global as any).get('tenantFormRef')
          form?.submitForm()

             console.log(form.values.logo);
          console.log(form.errors);



        },
      },
    ];
  },
});

// Composables
const { t } = useI18n()
const router = useRouter()
const tenantStore = useTenantStore()

// Constants for form options
const TENANT_TYPES = [
  { value: 'law_firm', label: 'Law Firm' },
  { value: 'freelance_lawyer', label: 'Freelance Lawyer' },
  { value: 'corporate_legal', label: 'Corporate Legal Department' },
  { value: 'legal_clinic', label: 'Legal Clinic' },
  { value: 'government_legal', label: 'Government Legal Office' },
  { value: 'other', label: 'Other' }
]

const LEGAL_INDUSTRIES = [
  { value: 'corporate', label: 'Corporate Law' },
  { value: 'criminal', label: 'Criminal Law' },
  { value: 'family', label: 'Family Law' },
  { value: 'real_estate', label: 'Real Estate Law' },
  { value: 'personal_injury', label: 'Personal Injury' },
  { value: 'employment', label: 'Employment Law' },
  { value: 'immigration', label: 'Immigration Law' },
  { value: 'intellectual_property', label: 'Intellectual Property' },
  { value: 'tax', label: 'Tax Law' },
  { value: 'bankruptcy', label: 'Bankruptcy Law' },
  { value: 'environmental', label: 'Environmental Law' },
  { value: 'healthcare', label: 'Healthcare Law' },
  { value: 'general_practice', label: 'General Practice' },
  { value: 'other', label: 'Other' }
]

const COUNTRIES = [
  { value: 'IL', label: 'Israel' },
  { value: 'SA', label: 'Saudi Arabia' },
  { value: 'KW', label: 'Kuwait' },
  { value: 'QA', label: 'Qatar' },
  { value: 'BH', label: 'Bahrain' },
  { value: 'OM', label: 'Oman' },
  { value: 'AE', label: 'United Arab Emirates' },
  { value: 'OTHER', label: 'Other' }
]

const CURRENCIES = [
  { value: 'ILS', label: 'ILS - Israeli Shekel' },
  { value: 'USD', label: 'USD - US Dollar' },
  { value: 'EUR', label: 'EUR - Euro' },
  { value: 'SAR', label: 'SAR - Saudi Riyal' },
  { value: 'KWD', label: 'KWD - Kuwaiti Dinar' },
  { value: 'QAR', label: 'QAR - Qatari Riyal' },
  { value: 'BHD', label: 'BHD - Bahraini Dinar' },
  { value: 'OMR', label: 'OMR - Omani Rial' },
  { value: 'AED', label: 'AED - United Arab Emirates Dirham' },
]

const TIMEZONES = [
  { value: 'UTC', label: 'UTC - Coordinated Universal Time' },
  { value: 'Asia/Jerusalem', label: 'Jerusalem (IST)' },
  { value: 'Asia/Dubai', label: 'Dubai (UTC+4)' },
  { value: 'Asia/Kuwait', label: 'Kuwait (UTC+3)' },
  { value: 'Asia/Qatar', label: 'Qatar (UTC+3)' },
  { value: 'Asia/Bahrain', label: 'Bahrain (UTC+3)' },
  { value: 'Asia/Oman', label: 'Oman (UTC+4)' },
  { value: 'Asia/UAE', label: 'UAE (UTC+4)' },
  { value: 'Asia/Riyadh', label: 'Riyadh (UTC+3)' },


]

const SUBSCRIPTION_PLANS = [
  { value: 'starter', label: 'Starter Plan' },
  { value: 'professional', label: 'Professional Plan' },
  { value: 'enterprise', label: 'Enterprise Plan' },
  { value: 'custom', label: 'Custom Plan' }
]

const TENANT_STATUS = [
  { value: 'active', label: 'Active' },
  { value: 'pending', label: 'Pending Setup' },
  { value: 'suspended', label: 'Suspended' }
]

const COMPLIANCE_LEVELS = [
  { value: 'basic', label: 'Basic Compliance' },
  { value: 'gdpr', label: 'GDPR Compliant' },
  { value: 'hipaa', label: 'HIPAA Compliant' },
  { value: 'sox', label: 'SOX Compliant' },
  { value: 'iso27001', label: 'ISO 27001 Compliant' }
]

const LOCALES = [
  { value: 'he', label: 'Hebrew' },
  { value: 'en', label: 'English' },
  { value: 'ar', label: 'Arabic' },

]

const schema = {
  sidebar: [
    {
      card: {

        content: [
          {
            name: "logo",
            label: "Tenant Logo (Optional)",
            component: "UiLogoUpload",
            rules: z.instanceof(File).optional().nullable(),
            props: {
              maxSize: 2 * 1024 * 1024,
              acceptedTypes: ['.jpg', '.jpeg', '.png', '.gif', '.webp'],
              placeholder: "Upload a logo"
            }
          }
        ]
      }
    },
    {
      card: {
        title: "Tenant Preferences",
        content: [
          {
            name: "locale",
            label: "Default Language",
            component: "UiSelect",
            defaultValue: "en",
            rules: z.string().min(1, 'Language is required'),
            props: { options: LOCALES }
          },
          {
            name: "timezone",
            label: "Default Timezone",
            component: "UiSelect",
            defaultValue: "UTC",
            rules: z.string().min(1, 'Timezone is required'),
            props: { options: TIMEZONES }
          },
          {
            name: "currency",
            label: "Preferred Currency",
            component: "UiSelect",
            defaultValue: "USD",
            rules: z.string().min(1, 'Currency is required'),
            props: { options: CURRENCIES }
          },

        ]
      }
    },
    {
      card: {
        title: "Usage & Billing",
        content: [
          {
            name: "plan",
            label: "Subscription Plan",
            component: "UiSelect",
            defaultValue: "starter",
            rules: z.string().min(1, 'Plan is required'),
            props: { options: SUBSCRIPTION_PLANS }
          },
          {
            name: "status",
            label: "Initial Status",
            component: "UiSelect",
            defaultValue: "pending",
            rules: z.string().min(1, 'Status is required'),
            props: { options: TENANT_STATUS }
          },
          {
            name: "seats",
            label: "Number of User Seats",
            component: "UiInput",
            defaultValue: "5",
            rules: z.number().refine(n => n > 0 && n <= 1000, 'Must be between 1 and 1000'),
            props: {
              type: "number",
              min: "1",
              max: "1000",
              placeholder: "5"
            }
          }
        ]
      }
    },

  ],
  content: [
    {
      card: {
        content: [
          {
            tabs: {
              tabs: [
                {
                  value: "tenantInfo", label: "Tenant Information",
                  content: [
                    { name: "name", label: "Tenant Name", component: "UiInput", rules: z.string().min(1, "Name is required.") },
                    { name: "slug", initialValue: "", label: "Tenant Slug", props: { source: ({ values }: { values: Record<string, any> }) => values.name || '' }, component: "UiSlugInput", rules: z.string().min(1, "Slug is required.") },
                    {
                      name: "email",
                      label: "Primary Contact Email",
                      component: "UiInput",
                      rules: z.string().email('Invalid email format'),
                      props: { type: "email", placeholder: "<EMAIL>" }
                    },
                    {
                      name: "phone",
                      label: "Primary Phone Number",
                      component: "UiInput",
                      rules: z.string().regex(/^[\+]?[1-9][\d]{0,15}$/, 'Invalid phone number').optional(),
                      props: { type: "tel", placeholder: "******-123-4567" }
                    },
                    {
                      name: "type",
                      label: "Tenant Type",
                      component: "UiSelect",
                      defaultValue: "law_firm",
                      rules: z.string().min(1, 'Tenant type is required'),
                      props: { options: TENANT_TYPES }
                    },
                    {
                      name: "industry",
                      label: "Legal Industry (Optional)",
                      component: "UiSelect",
                      rules: z.string().optional(),
                      props: {
                        options: [{ value: '', label: 'Select industry...' }, ...LEGAL_INDUSTRIES],
                        placeholder: "Select legal practice area"
                      }
                    }

                  ]
                },
                {
                  value: "address", label: "Tenant Address", content: [

                    {
                      name: "country",
                      label: "Country",
                      component: "UiSelect",
                      defaultValue: "US",
                      rules: z.string().min(1, 'Country is required'),
                      props: { options: COUNTRIES }
                    },
                    {
                      name: "city",
                      label: "City",
                      component: "UiInput",
                      rules: z.string().min(1, 'City is required').max(50, 'City name too long'),
                      props: { placeholder: "New York" }
                    },
                    {
                      name: "address_line1",
                      label: "Street Address",
                      component: "UiInput",
                      rules: z.string().min(1, 'Street address is required').max(100, 'Address too long'),
                      props: { placeholder: "123 Main Street" }
                    },
                    {
                      name: "address_line2",
                      label: "Address Line 2 (Optional)",
                      component: "UiInput",
                      rules: z.string().max(100, 'Address too long').optional(),
                      props: { placeholder: "Suite 456" }
                    },
                    {
                      name: "zip_code",
                      label: "Postal Code",
                      component: "UiInput",
                      rules: z.string().min(3, 'Invalid postal code').max(20, 'Postal code too long'),
                      props: { placeholder: "10001" }
                    }
                  ]
                },
                {
                  value: "legal", label: "Legal & Compliance", content: [

                    {
                      name: "vat_number",
                      label: "VAT/Tax Number (Optional)",
                      component: "UiInput",
                      rules: z.string().max(50, 'VAT number too long').optional(),
                      props: { placeholder: "VAT123456789" }
                    },
                    {
                      name: "registration_number",
                      label: "Business Registration Number (Optional)",
                      component: "UiInput",
                      rules: z.string().max(50, 'Registration number too long').optional(),
                      props: { placeholder: "REG123456" }
                    },
                    {
                      name: "compliance_level",
                      label: "Compliance Level",
                      component: "UiSelect",
                      defaultValue: "basic",
                      rules: z.string().min(1, 'Compliance level is required'),
                      props: { options: COMPLIANCE_LEVELS }
                    }
                  ]
                },
                {
                  value: "adminContact", label: "Admin Contact", content: [
                    {
                      name: "contact_person_name",
                      label: "Main Contact Name",
                      component: "UiInput",
                      rules: z.string().min(1, 'Contact name is required').max(100, 'Name too long'),
                      props: { placeholder: "John Doe" }
                    },
                    {
                      name: "contact_email",
                      label: "Contact Email",
                      component: "UiInput",
                      rules: z.string().email('Invalid email format'),
                      props: { type: "email", placeholder: "<EMAIL>" }
                    },
                    {
                      name: "contact_phone",
                      label: "Contact Phone",
                      component: "UiInput",
                      rules: z.string().regex(/^[\+]?[1-9][\d]{0,15}$/, 'Invalid phone number').optional(),
                      props: { type: "tel", placeholder: "******-123-4567" }
                    }
                  ]
                },


              ]
            }
          },
        ]

      }
    },
    {
      card: {
        title: "🧩 Optional Extensions",
        content: [
          {
            name: "document_storage_quota",
            label: "Document Storage Quota (GB)",
            component: "UiInput",
            defaultValue: "10",
            rules: z.number().refine(n => n > 0 && n <= 1000, 'Must be between 1 and 1000GB').optional(),
            props: {
              type: "number",
              min: "1",
              max: "1000",
              placeholder: "10"
            }
          },
          {
            name: "api_access_enabled",
            label: "Enable API Access",
            component: "UiCheckbox",
            defaultValue: false,
            rules: z.boolean().optional()
          },
          {
            name: "audit_log_enabled",
            label: "Enable Audit Logging",
            component: "UiCheckbox",
            defaultValue: true,
            rules: z.boolean().optional()
          },
          {
            name: "webhooks_enabled",
            label: "Enable Webhooks",
            component: "UiCheckbox",
            defaultValue: false,
            rules: z.boolean().optional()
          }
        ]
      }
    }


  ]

}

 

// Initial form values
const initialValues = {
  // Basic Information
  name: "Test tenant full",
  slug: "test-tenant-full",
  email: "<EMAIL>",
  phone: "+972512332019",
  type: "law_firm",
  industry: "corporate",

  // Address
  country: "IL",
  city: "Jerusalem",
  address_line1: "123 Main St",
  address_line2: "Suite 456",
  zip_code: "12345",

  // Legal & Compliance
  vat_number: "301472171",
  registration_number: "123456789",
  compliance_level: "basic",

  // Admin Contact
  contact_person_name: "John Doe",
  contact_email: "<EMAIL>",
  contact_phone: "+972512332019",

  // Preferences
  locale: "he",
  timezone: "Asia/Jerusalem",
  currency: "ILS",
  logo: null,
  custom_domain: "",

  // Usage & Billing
  plan: "starter",
  status: "active",
  seats: 5,

  // Optional Extensions
  document_storage_quota: 10,
  api_access_enabled: false,
  audit_log_enabled: true,
  webhooks_enabled: false
}

// Form submission handler
const handleSubmit = async (values: Record<string, any>) => {
  try {


    // Prepare the payload for tenant creation with all fields
    const payload: CreateTenantWithLogoPayload = {
      // Basic Information
      name: values.name,
      slug: values.slug,
      email: values.email,
      phone: values.phone || undefined,
      type: values.type,
      industry: values.industry || undefined,

      // Address
      country: values.country,
      city: values.city,
      address_line1: values.address_line1,
      address_line2: values.address_line2 || undefined,
      zip_code: values.zip_code,

      // Legal & Compliance
      vat_number: values.vat_number || undefined,
      registration_number: values.registration_number || undefined,
      compliance_level: values.compliance_level,

      // Admin Contact
      contact_person_name: values.contact_person_name,
      contact_email: values.contact_email,
      contact_phone: values.contact_phone || undefined,

      // Preferences
      locale: values.locale || 'en',
      timezone: values.timezone || 'UTC',
      currency: values.currency,
      logo: values.logo || null,
      custom_domain: values.custom_domain || undefined,

      // Usage & Billing
      plan: values.plan || 'starter',
      status: values.status,
      seats: values.seats,

      // Optional Extensions
      document_storage_quota: values.document_storage_quota || undefined,
      api_access_enabled: values.api_access_enabled || false,
      audit_log_enabled: values.audit_log_enabled !== false, // Default to true
      webhooks_enabled: values.webhooks_enabled || false
    }


    // Create the tenant using the store
    const newTenant = await tenantStore.createTenant(payload)

    console.log('Tenant created successfully:', newTenant)

 
    // Redirect to tenant list
    await router.push('/dashboard/tenants/' + newTenant.id)

  } catch (error) {
    console.error('Error creating tenant:', error)
 
  }
}

const { $global } = useNuxtApp()
const formRef = ref(null)

onMounted(() => {
 ($global as any).add('tenantFormRef', formRef.value)
})

onUnmounted(() => {
 ($global as any).remove('tenantFormRef')
})
</script>
