<template>
  <div class="space-y-6">
    <div>
      <h2 class="text-md font-semibold text-gray-800 dark:text-gray-100 mb-4">Legal Policy Management</h2>
      <p class="text-sm text-gray-600 dark:text-gray-400">Manage links, versions, and update requirements for your platform's legal documents.</p>
    </div>

    <!-- Terms of Service Management Section -->
    <UiCard title="Terms of Service Management">
      <div class="space-y-4">
        <div>
          <label for="tosLink" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Link to Published Terms of Service</label>
          <UiInput id="tosLink" :name="'tosLink'" v-model="termsOfService.link" type="url" placeholder="https://example.com/terms-of-service" class="mt-1" />
        </div>
        <div>
          <label for="tosVersion" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Current Version</label>
          <UiInput id="tosVersion" :name="'tosVersion'" v-model="termsOfService.version" type="text" placeholder="e.g., v2.1.0" class="mt-1" />
        </div>
        <div>
          <label for="tosLastUpdated" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Last Updated Date</label>
          <UiInput id="tosLastUpdated" :name="'tosLastUpdated'" v-model="termsOfService.lastUpdated" type="date" class="mt-1" />
        </div>
        <UiToggle v-model="termsOfService.requireReAcceptance" label="Require Re-acceptance on Major Update" />
      </div>
    </UiCard>

    <!-- Privacy Policy Management Section -->
    <UiCard title="Privacy Policy Management">
      <div class="space-y-4">
        <div>
          <label for="privacyLink" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Link to Published Privacy Policy</label>
          <UiInput id="privacyLink" :name="'privacyLink'" v-model="privacyPolicy.link" type="url" placeholder="https://example.com/privacy-policy" class="mt-1" />
        </div>
        <div>
          <label for="privacyVersion" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Current Version</label>
          <UiInput id="privacyVersion" :name="'privacyVersion'" v-model="privacyPolicy.version" type="text" placeholder="e.g., v1.5.0" class="mt-1" />
        </div>
        <div>
          <label for="privacyLastUpdated" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Last Updated Date</label>
          <UiInput id="privacyLastUpdated" :name="'privacyLastUpdated'" v-model="privacyPolicy.lastUpdated" type="date" class="mt-1" />
        </div>
        <UiToggle v-model="privacyPolicy.requireReAcceptance" label="Require Re-acceptance on Major Update" />
      </div>
    </UiCard>

    <!-- Cookie Policy Management Section -->
    <UiCard title="Cookie Policy Management">
      <div class="space-y-4">
        <div>
          <label for="cookieLink" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Link to Published Cookie Policy</label>
          <UiInput id="cookieLink" :name="'cookieLink'" v-model="cookiePolicy.link" type="url" placeholder="https://example.com/cookie-policy" class="mt-1" />
        </div>
        <div>
          <label for="cookieVersion" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Current Version</label>
          <UiInput id="cookieVersion" :name="'cookieVersion'" v-model="cookiePolicy.version" type="text" placeholder="e.g., v1.1.0" class="mt-1" />
        </div>
        <div>
          <label for="cookieLastUpdated" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Last Updated Date</label>
          <UiInput id="cookieLastUpdated" :name="'cookieLastUpdated'" v-model="cookiePolicy.lastUpdated" type="date" class="mt-1" />
        </div>
      </div>
    </UiCard>

    <!-- Data Processing Agreement (DPA) Section -->
    <UiCard title="Data Processing Agreement (DPA)">
      <div class="space-y-4">
        <UiToggle v-model="dpa.enabled" label="Enable DPA Management" />
        <div v-if="dpa.enabled" class="space-y-4 pt-2 border-t mt-4">
          <div>
            <label for="dpaLink" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Link to Published DPA</label>
            <UiInput id="dpaLink" :name="'dpaLink'" v-model="dpa.link" type="url" placeholder="https://example.com/dpa" class="mt-1" />
          </div>
          <div>
            <label for="dpaVersion" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Current DPA Version</label>
            <UiInput id="dpaVersion" :name="'dpaVersion'" v-model="dpa.version" type="text" placeholder="e.g., v1.0.0" class="mt-1" />
          </div>
          <div>
            <label for="dpaLastUpdated" class="block text-sm font-medium text-gray-700 dark:text-gray-300">DPA Last Updated Date</label>
            <UiInput id="dpaLastUpdated" :name="'dpaLastUpdated'" v-model="dpa.lastUpdated" type="date" class="mt-1" />
          </div>
        </div>
      </div>
    </UiCard>

    <!-- Policy Display & Access Section -->
    <UiCard title="Policy Display & Access">
      <div class="space-y-4">
        <UiToggle v-model="policyDisplay.inFooter" label="Display Links in Footer" description="For public-facing pages like homepage, login, etc." />
        <UiToggle v-model="policyDisplay.accessibleToAuthUsers" label="Make Policies Accessible to Authenticated Users" description="E.g., via a dedicated page or within user settings." />
      </div>
    </UiCard>

  </div>
</template>

<script setup lang="ts">
import { reactive } from 'vue';
import UiCard from '~/components/ui/UiCard.vue';
import UiInput from '~/components/ui/UiInput.vue';
import UiToggle from '~/components/ui/UiToggle.vue';

const today = () => new Date().toISOString().split('T')[0]; // Helper for default date

// Terms of Service Management
const termsOfService = reactive({
  link: '',
  version: '',
  lastUpdated: today(),
  requireReAcceptance: true,
});

// Privacy Policy Management
const privacyPolicy = reactive({
  link: '',
  version: '',
  lastUpdated: today(),
  requireReAcceptance: true,
});

// Cookie Policy Management
const cookiePolicy = reactive({
  link: '',
  version: '',
  lastUpdated: today(),
});

// Data Processing Agreement (DPA)
const dpa = reactive({
  enabled: false,
  link: '',
  version: '',
  lastUpdated: today(),
});

// Policy Display & Access
const policyDisplay = reactive({
  inFooter: true,
  accessibleToAuthUsers: true,
});

// TODO: Load initial values from backend and implement save logic for these settings
</script>
