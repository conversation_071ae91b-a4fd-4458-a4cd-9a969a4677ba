<template>
  <svg
    xmlns="http://www.w3.org/2000/svg"
    :width="size"
    :height="size"
    viewBox="0 0 32 32"
  >
    <g  v-if="variant === 'disc'" :fill="fill ? 'currentColor' : 'none'" stroke="currentColor">
      <circle cx="4" cy="8" r="2" />
      <circle cx="4" cy="16" r="2" />
      <circle cx="4" cy="24" r="2" />
    </g>
    <g v-else-if="variant === 'rect'" :fill="fill ? 'currentColor' : 'none'" stroke="currentColor">
      <rect x="2" y="6" width="4" height="4" />
      <rect x="2" y="14" width="4" height="4" />
      <rect x="2" y="22" width="4" height="4" />
    </g>
    <g v-else-if="variant === 'text'" :fill="fill ? 'currentColor' : 'none'" stroke="currentColor">
      <text v-for="(letter, index) in letters" :key="index" :x="letterX" :y="9.5 + index * 8" font-size="7"  fill="currentColor"   >{{ letter }}</text>
    </g>
    <g stroke="currentColor" stroke-opacity="0.3" stroke-width="3" stroke-linecap="round">
      <line :x1="linesX1" y1="8" :x2="linesX2" y2="8" />
      <line :x1="linesX1" y1="16" :x2="linesX2" y2="16" />
      <line :x1="linesX1" y1="24" :x2="linesX2" y2="24" />
    </g>
  </svg>
</template>

<script setup lang="ts">
import { computed } from 'vue'; // Import computed from Vue
interface Props {
  size?: string;
  variant?: "disc" | "rect" | "text";
  fill?: boolean;
  letters?: string[];
  rtl?: boolean;
}
const props = withDefaults(defineProps<Props>(), {
  size: "3em",
  variant: "disc",
  fill: true,
  letters: () => ["A", "B", "C"], // Default letters for text variant
  rtl: false,
});

const letterX = computed(() => {
  const x = props.rtl ? 26 : 2;
  return x;
});
const linesX1 = computed(() => {
  const x = props.rtl ? 2 : 10;
  return x;
});

const linesX2 = computed(() => {
  const x = props.rtl ? 21 : 25;
  return x;
});
 
</script>
