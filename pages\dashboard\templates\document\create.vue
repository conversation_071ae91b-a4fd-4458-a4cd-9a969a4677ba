<template>
  <div class="space-y-6 py-6 min-h-screen max-w-7xl mx-auto">
    <forms-form
      ref="formRef"
      :schema="schema"
      :initial-values="initialValues"
      @submit="handleSubmit"
      :sticky-sidebar="true"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive, watch, defineAsyncComponent } from "vue";
import { useRouter } from "vue-router";
import { useTemplateStore } from "~/stores/template";
import {
  getTemplateCategoriesByType,
  getTemplateCategoryById,
} from "~/utils/templateCategories";
import { PlatformRoles, TenantRoles } from "~/app/features/auth/constants/roles";
import { z } from "zod";
import AsyncComponent from "~/app/shared/components/forms/AsyncComponent.vue";
import { label } from "@unovis/ts/components/axis/style";


// Lazy load components
const DocumentTemplateEditor = defineAsyncComponent(
  () => import("~/components/template-editors/DocumentTemplateEditor.vue")
);
const DocumentTemplateLibraryModal = defineAsyncComponent(
  () => import("~/components/template-modals/DocumentTemplateLibraryModal.vue")
);
const VariableInsertModal = defineAsyncComponent(
  () => import("~/components/template-modals/VariableInsertModal.vue")
);

// Page meta
definePageMeta({
  layout: "dashboard",
  title: "Create Document Template",
  description: "Create a new legal document template with dynamic content and variables",
  pageHeaderIcon: "heroicons:document-text",
  showPageHeader: true,
  showPageHeaderTitle: true,
  pageHeaderActions: () => {
    return [
      {
        label: "Save as Draft",
        icon: "i-heroicons:document-duplicate",
        color: "gray",
        variant: "outline",
        click: () => "saveDraft()",
      },
      {
        label: "Preview",
        icon: "i-heroicons:eye",
        color: "info",
        variant: "outline",
        click: () => "previewTemplate()",
      },
    ].reverse();
  },
  middleware: ["rbac"],
  roles: [
    PlatformRoles.SUPER_ADMIN,
    TenantRoles.TENANT_OWNER,
    TenantRoles.ADMIN,
    TenantRoles.LAWYER,
  ],
  breadcrumb: [
    { label: "Dashboard", href: "/dashboard" },
    { label: "Templates", href: "/dashboard/templates" },
    { label: "Document Templates", href: "/dashboard/templates/document" },
    { label: "Create" },
  ],
});

// Composables
const router = useRouter();
const templateStore = useTemplateStore();

// State
const isSaving = ref(false);
const showTemplateLibrary = ref(false);
const showVariableModal = ref(false);
const errors = ref<Record<string, string>>({});

const formData = reactive({
  name: "",
  description: "",
  category: "",
  content: "",
  variables: [] as string[],
  type: "document",
  isDraft: false,
  metadata: {},
});

// Computed
const documentCategories = computed(() => getTemplateCategoriesByType("document").map(cat => ({
  label: cat.name,
  value: cat.id,
})));

const selectedCategory = computed(() =>
  formData.category ? getTemplateCategoryById(formData.category) : null
);

const wordCount = computed(() => {
  const text = formData.content.replace(/<[^>]*>/g, "").trim();
  return text ? text.split(/\s+/).length : 0;
});

const completionPercentage = computed(() => {
  let score = 0;
  if (formData.name.trim()) score += 25;
  if (formData.category) score += 25;
  if (formData.description.trim()) score += 25;
  if (formData.content.trim()) score += 25;
  return score;
});

const isFormValid = computed(
  () =>
    formData.name.trim() &&
    formData.category &&
    formData.content.trim() &&
    !Object.keys(errors.value).length
);

const canPreview = computed(() => formData.content.trim() && formData.name.trim());

const handleContentChange = (content: string, variables: string[]) => {
  formData.content = content;
  formData.variables = variables;
};


// schema
const schema = {
 
  sidebar: [
     {
      card: {
        title: "Stats",
        content: [
           {
            asyncComponent: defineAsyncComponent(() => import("~/components/documents/Stats.vue")),
            props: {
              formData,
            },
           }
        ],
      },
    },
    {
      card: {
         title: "Tips & Guidelines",
        content: [
       
           {
            asyncComponent: defineAsyncComponent(() => import("~/components/documents/Tips.vue")),
           }
        ],
      },
    },
  ],
  content: [
    {
      card: {
        title: "Template Information",
        props: {
          contentProps: {
            class: "space-y-4 py-4",
          },
        },

        content: [
          {
            grid: {
              cols: 2,
              gap: "4",
              content: [
                {
                  name: "name",
                  label: "Template Name",
                  component: "UiInput",
                  rules: z.string().min(3).max(100),
                  props: {
                    placeholder: "e.g., Service Agreement Template",
                    helpText: "choose a descriptive name for easy identification",
                  },
                },
                {
                  name: "category",
                  label: "Category",
                  component: "UiSelect",
                  rules: z.string().min(1),
                  props: {
                    options: documentCategories.value,
                    placeholder: "Select a category",
                    helpText: "Categorize your template for better organization",
                  },
                },
              ],
            },
          },
          {
            name: "description",
            label: "Description (Optional)",
            component: "UiTextarea",
            rules: z.string().max(200).optional(),
            props: {
              autoGrow: true,
              placeholder: "Describe the purpose and usage of this template...",
              rows: 3,
              helpText: "Help others understand when and how to use this template",
            },
          },
        ],
      },
    },
    {
      card: {
        content: [
          {
            asyncComponent: defineAsyncComponent(() => import("~/components/ui/UiButton.vue")),
            slots: {
              default: "Open Editor",
            },
            events: {
              click: () => {
                const modal =useGlobalModal()
                modal.openModal({
                  component: DocumentTemplateEditor,
                  size: 'full',
                  title: "Edit Template",
                  props: {
                    content: formData.content,
                    variables: formData.variables,
                    templateData: formData,
                    category: selectedCategory,
                  },
                  events: {
                    "content-change": handleContentChange,
                  },
                });
              },
            },
          },
          {
            name: "content",
            label: "Template Content",
            component: DocumentTemplateEditor,
            rules: z.string().min(1),
            props: {
              placeholder: "Enter template content",
              content: formData.content,
              variables: formData.variables,
              templateData: formData,
              category: selectedCategory,
            },
            evens: {
              "content-change": handleContentChange,
            },
          },
        ],
      },
    },
  ],
};

 

// initialValues
const initialValues = {
  name: "",
  description: "",
  category: "",
  content: "",
  variables: [] as string[],
  type: "document",
  isDraft: false,
  metadata: {},
};

// Watchers
watch(
  () => formData.name,
  (newName) => {
    if (newName.trim() && errors.value.name) {
      delete errors.value.name;
    }
  }
);

watch(
  () => formData.category,
  (newCategory) => {
    if (newCategory && selectedCategory.value?.metadata) {
      formData.metadata = { ...selectedCategory.value.metadata };
    }
  }
);

// Methods
const validateForm = () => {
  errors.value = {};

  if (!formData.name.trim()) {
    errors.value.name = "Template name is required";
  } else if (formData.name.length < 3) {
    errors.value.name = "Template name must be at least 3 characters";
  }

  if (!formData.category) {
    errors.value.category = "Please select a category";
  }

  if (!formData.content.trim()) {
    errors.value.content = "Template content is required";
  }

  return Object.keys(errors.value).length === 0;
};

const handleSubmit = async () => {
  if (!validateForm()) return;

  isSaving.value = true;
  try {
    const template = await templateStore.createTemplate({
      ...formData,
      isDraft: false,
    });

    if (template) {
      await router.push(`/dashboard/templates/document/${template.id}`);
    }
  } catch (error) {
    console.error("Error creating template:", error);
    // Handle error notification
  } finally {
    isSaving.value = false;
  }
};

const saveDraft = async () => {
  if (!formData.name.trim()) {
    errors.value.name = "Template name is required to save draft";
    return;
  }

  isSaving.value = true;
  try {
    const template = await templateStore.createTemplate({
      ...formData,
      isDraft: true,
    });

    if (template) {
      await router.push(`/dashboard/templates/document/${template.id}/edit`);
    }
  } catch (error) {
    console.error("Error saving draft:", error);
  } finally {
    isSaving.value = false;
  }
};

const previewTemplate = () => {
  if (!canPreview.value) return;

  // Open preview in new tab or modal
  const previewData = {
    name: formData.name,
    content: formData.content,
    variables: formData.variables,
    category: selectedCategory.value,
  };

  // Store preview data temporarily and navigate
  sessionStorage.setItem("templatePreview", JSON.stringify(previewData));
  window.open("/dashboard/templates/preview", "_blank");
};

const insertVariable = () => {
  showVariableModal.value = true;
};

const handleVariableInsert = (variable: string) => {
  if (!formData.variables.includes(variable)) {
    formData.variables.push(variable);
  }
  showVariableModal.value = false;
};

const removeVariable = (variable: string) => {
  const index = formData.variables.indexOf(variable);
  if (index > -1) {
    formData.variables.splice(index, 1);
  }
};

const insertTemplate = (template: any) => {
  formData.content = template.content;
  formData.variables = [...formData.variables, ...template.variables];
  showTemplateLibrary.value = false;
};
</script>
