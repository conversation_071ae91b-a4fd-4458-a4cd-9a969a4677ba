<template>
  <UiModal @close="$emit('close')" size="lg">
    <div class="p-6">
      <!-- Header -->
      <div class="flex items-center space-x-3 mb-6">
        <div class="w-10 h-10 bg-gray-100 dark:bg-gray-900 rounded-full flex items-center justify-center">
          <Icon name="heroicons:document-text" class="w-5 h-5 text-gray-600 dark:text-gray-400" />
        </div>
        <div>
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Log Template Library</h3>
          <p class="text-sm text-gray-600 dark:text-gray-400">Choose from pre-built logging templates</p>
        </div>
      </div>

      <!-- Search and Filter -->
      <div class="flex items-center space-x-4 mb-6">
        <div class="flex-1">
          <UiInput
            v-model="searchQuery"
            placeholder="Search log templates..."
            class="w-full"
          >
            <template #prefix>
              <Icon name="heroicons:magnifying-glass" class="w-4 h-4 text-gray-400" />
            </template>
          </UiInput>
        </div>
        <div class="flex items-center space-x-2">
          <select
            v-model="selectedCategory"
            class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-gray-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm"
          >
            <option value="">All Categories</option>
            <option value="user-actions">User Actions</option>
            <option value="system-events">System Events</option>
            <option value="security">Security</option>
            <option value="errors">Errors</option>
            <option value="performance">Performance</option>
          </select>
          <select
            v-model="selectedLevel"
            class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-gray-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm"
          >
            <option value="">All Levels</option>
            <option value="debug">Debug</option>
            <option value="info">Info</option>
            <option value="warn">Warning</option>
            <option value="error">Error</option>
            <option value="fatal">Fatal</option>
          </select>
        </div>
      </div>

      <!-- Template List -->
      <div class="space-y-3 max-h-96 overflow-y-auto">
        <div
          v-for="template in filteredTemplates"
          :key="template.id"
          class="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:border-gray-300 dark:hover:border-gray-600 cursor-pointer transition-colors"
          @click="selectTemplate(template)"
        >
          <div class="flex items-start justify-between mb-3">
            <div>
              <h4 class="text-sm font-medium text-gray-900 dark:text-white">{{ template.name }}</h4>
              <p class="text-xs text-gray-500 dark:text-gray-400">{{ template.category }}</p>
            </div>
            <div class="flex items-center space-x-2">
              <span
                :class="[
                  'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium',
                  getCategoryColor(template.category)
                ]"
              >
                {{ template.category }}
              </span>
              <span
                :class="[
                  'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium',
                  getLevelColor(template.level)
                ]"
              >
                {{ template.level.toUpperCase() }}
              </span>
            </div>
          </div>
          
          <p class="text-sm text-gray-600 dark:text-gray-300 mb-3">{{ template.description }}</p>
          
          <!-- Log Preview -->
          <div class="bg-gray-900 text-green-400 rounded-lg p-3 mb-3 font-mono text-xs overflow-x-auto">
            <div class="whitespace-pre-wrap">{{ formatLogPreview(template) }}</div>
          </div>
          
          <div v-if="template.variables.length > 0" class="pt-3 border-t border-gray-200 dark:border-gray-700">
            <div class="text-xs text-gray-500 dark:text-gray-400 mb-1">Variables:</div>
            <div class="flex flex-wrap gap-1">
              <span
                v-for="variable in template.variables"
                :key="variable"
                class="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-mono bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200"
              >
                {{ variable }}
              </span>
            </div>
          </div>
          
          <div v-if="template.data.length > 0" class="pt-2">
            <div class="text-xs text-gray-500 dark:text-gray-400 mb-1">Structured Fields:</div>
            <div class="flex flex-wrap gap-1">
              <span
                v-for="field in template.data"
                :key="field.key"
                class="inline-flex items-center px-1.5 py-0.5 rounded text-xs bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
              >
                {{ field.key }}
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- Actions -->
      <div class="flex items-center justify-end space-x-3 mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
        <UiButton @click="$emit('close')" variant="outline">
          Cancel
        </UiButton>
      </div>
    </div>
  </UiModal>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

// Emits
const emit = defineEmits<{
  close: []
  select: [template: any]
}>()

// State
const searchQuery = ref('')
const selectedCategory = ref('')
const selectedLevel = ref('')

// Log templates library
const logTemplates = [
  {
    id: 'user-login',
    name: 'User Login',
    category: 'user-actions',
    level: 'info',
    description: 'Log successful user authentication',
    message: 'User {userName} successfully logged in from {ipAddress}',
    data: [
      { key: 'userId', value: '{userId}', type: 'string' },
      { key: 'sessionId', value: '{sessionId}', type: 'string' },
      { key: 'userAgent', value: '{userAgent}', type: 'string' },
      { key: 'loginMethod', value: '{loginMethod}', type: 'string' }
    ],
    variables: ['userName', 'ipAddress', 'userId', 'sessionId', 'userAgent', 'loginMethod']
  },
  {
    id: 'case-created',
    name: 'Case Created',
    category: 'user-actions',
    level: 'info',
    description: 'Log when a new case is created',
    message: 'New case "{caseTitle}" created by {attorneyName} for client {clientName}',
    data: [
      { key: 'caseId', value: '{caseId}', type: 'string' },
      { key: 'caseType', value: '{caseType}', type: 'string' },
      { key: 'attorneyId', value: '{attorneyId}', type: 'string' },
      { key: 'clientId', value: '{clientId}', type: 'string' }
    ],
    variables: ['caseTitle', 'attorneyName', 'clientName', 'caseId', 'caseType', 'attorneyId', 'clientId']
  },
  {
    id: 'document-accessed',
    name: 'Document Accessed',
    category: 'user-actions',
    level: 'info',
    description: 'Log document access for audit trail',
    message: 'Document "{documentTitle}" accessed by {userName}',
    data: [
      { key: 'documentId', value: '{documentId}', type: 'string' },
      { key: 'userId', value: '{userId}', type: 'string' },
      { key: 'accessType', value: '{accessType}', type: 'string' },
      { key: 'caseId', value: '{caseId}', type: 'string' }
    ],
    variables: ['documentTitle', 'userName', 'documentId', 'userId', 'accessType', 'caseId']
  },
  {
    id: 'payment-processed',
    name: 'Payment Processed',
    category: 'user-actions',
    level: 'info',
    description: 'Log successful payment processing',
    message: 'Payment of ${amount} processed for invoice {invoiceNumber} by {clientName}',
    data: [
      { key: 'paymentId', value: '{paymentId}', type: 'string' },
      { key: 'invoiceId', value: '{invoiceId}', type: 'string' },
      { key: 'paymentMethod', value: '{paymentMethod}', type: 'string' },
      { key: 'transactionId', value: '{transactionId}', type: 'string' }
    ],
    variables: ['amount', 'invoiceNumber', 'clientName', 'paymentId', 'invoiceId', 'paymentMethod', 'transactionId']
  },
  {
    id: 'system-backup',
    name: 'System Backup',
    category: 'system-events',
    level: 'info',
    description: 'Log system backup operations',
    message: 'System backup {backupType} completed successfully',
    data: [
      { key: 'backupId', value: '{backupId}', type: 'string' },
      { key: 'backupSize', value: '{backupSize}', type: 'number' },
      { key: 'duration', value: '{duration}', type: 'number' },
      { key: 'location', value: '{backupLocation}', type: 'string' }
    ],
    variables: ['backupType', 'backupId', 'backupSize', 'duration', 'backupLocation']
  },
  {
    id: 'failed-login',
    name: 'Failed Login Attempt',
    category: 'security',
    level: 'warn',
    description: 'Log failed authentication attempts',
    message: 'Failed login attempt for user {userName} from {ipAddress}',
    data: [
      { key: 'attemptCount', value: '{attemptCount}', type: 'number' },
      { key: 'userAgent', value: '{userAgent}', type: 'string' },
      { key: 'reason', value: '{failureReason}', type: 'string' }
    ],
    variables: ['userName', 'ipAddress', 'attemptCount', 'userAgent', 'failureReason']
  },
  {
    id: 'suspicious-activity',
    name: 'Suspicious Activity',
    category: 'security',
    level: 'warn',
    description: 'Log potentially suspicious user activity',
    message: 'Suspicious activity detected: {activityDescription} by user {userName}',
    data: [
      { key: 'userId', value: '{userId}', type: 'string' },
      { key: 'riskScore', value: '{riskScore}', type: 'number' },
      { key: 'activityType', value: '{activityType}', type: 'string' },
      { key: 'ipAddress', value: '{ipAddress}', type: 'string' }
    ],
    variables: ['activityDescription', 'userName', 'userId', 'riskScore', 'activityType', 'ipAddress']
  },
  {
    id: 'application-error',
    name: 'Application Error',
    category: 'errors',
    level: 'error',
    description: 'Log application errors and exceptions',
    message: 'Application error in {moduleName}: {errorMessage}',
    data: [
      { key: 'errorCode', value: '{errorCode}', type: 'string' },
      { key: 'userId', value: '{userId}', type: 'string' },
      { key: 'requestId', value: '{requestId}', type: 'string' },
      { key: 'stackTrace', value: '{stackTrace}', type: 'string' }
    ],
    variables: ['moduleName', 'errorMessage', 'errorCode', 'userId', 'requestId', 'stackTrace']
  },
  {
    id: 'database-error',
    name: 'Database Error',
    category: 'errors',
    level: 'error',
    description: 'Log database connection and query errors',
    message: 'Database error: {errorMessage} in query {queryType}',
    data: [
      { key: 'queryId', value: '{queryId}', type: 'string' },
      { key: 'executionTime', value: '{executionTime}', type: 'number' },
      { key: 'affectedRows', value: '{affectedRows}', type: 'number' },
      { key: 'connectionPool', value: '{connectionPool}', type: 'string' }
    ],
    variables: ['errorMessage', 'queryType', 'queryId', 'executionTime', 'affectedRows', 'connectionPool']
  },
  {
    id: 'performance-slow-query',
    name: 'Slow Query Alert',
    category: 'performance',
    level: 'warn',
    description: 'Log slow database queries for optimization',
    message: 'Slow query detected: {queryType} took {executionTime}ms',
    data: [
      { key: 'queryId', value: '{queryId}', type: 'string' },
      { key: 'threshold', value: '{threshold}', type: 'number' },
      { key: 'userId', value: '{userId}', type: 'string' },
      { key: 'endpoint', value: '{endpoint}', type: 'string' }
    ],
    variables: ['queryType', 'executionTime', 'queryId', 'threshold', 'userId', 'endpoint']
  }
]

// Computed
const filteredTemplates = computed(() => {
  let templates = logTemplates
  
  // Filter by category
  if (selectedCategory.value) {
    templates = templates.filter(template => template.category === selectedCategory.value)
  }
  
  // Filter by level
  if (selectedLevel.value) {
    templates = templates.filter(template => template.level === selectedLevel.value)
  }
  
  // Filter by search query
  if (searchQuery.value.trim()) {
    const query = searchQuery.value.toLowerCase()
    templates = templates.filter(template =>
      template.name.toLowerCase().includes(query) ||
      template.description.toLowerCase().includes(query) ||
      template.message.toLowerCase().includes(query)
    )
  }
  
  return templates
})

// Methods
const selectTemplate = (template: any) => {
  emit('select', template)
}

const formatLogPreview = (template: any) => {
  const timestamp = new Date().toISOString()
  const logEntry = {
    timestamp,
    level: template.level.toUpperCase(),
    category: template.category,
    message: template.message,
    data: {}
  }
  
  // Add sample data
  template.data.forEach((field: any) => {
    logEntry.data[field.key] = getSampleValue(field.key, field.type)
  })
  
  return JSON.stringify(logEntry, null, 2)
}

const getSampleValue = (key: string, type: string) => {
  switch (type) {
    case 'number':
      if (key.includes('time') || key.includes('duration')) return 1250
      if (key.includes('count')) return 3
      if (key.includes('size')) return 1024000
      return 42
    case 'boolean':
      return true
    case 'object':
      return { sample: 'object' }
    case 'array':
      return ['item1', 'item2']
    default:
      if (key.includes('id')) return 'abc123'
      if (key.includes('email')) return '<EMAIL>'
      if (key.includes('name')) return 'Sample Name'
      if (key.includes('ip')) return '***********'
      return 'sample_value'
  }
}

const getCategoryColor = (category: string) => {
  const colors = {
    'user-actions': 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
    'system-events': 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
    'security': 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
    'errors': 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
    'performance': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
  }
  return colors[category as keyof typeof colors] || 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
}

const getLevelColor = (level: string) => {
  const colors = {
    'debug': 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200',
    'info': 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
    'warn': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
    'error': 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
    'fatal': 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
  }
  return colors[level as keyof typeof colors] || 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
}
</script>
