/**
 * Documents Feature Module
 * 
 * Exports all document-related functionality including types, composables, and utilities
 */

// Export types
export * from './types/index.js' // Assuming an index.js barrel file

// Export composables
export * from './composables/index.js' // Assuming an index.js barrel file

// Export stores
export * from './stores/index.js' // Assuming an index.js barrel file

// Export constants
export * from './constants/index.js' // Assuming an index.js barrel file

// Export utilities (when created)
// export * from './utils'

// Default export for the feature module
export default {
  name: 'documents',
  version: '1.0.0',
  description: 'Document management feature module'
}
