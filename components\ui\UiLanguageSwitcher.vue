<template>
  <UiDropdown
    :align="dropdownPosition === 'left' ? 'right' : 'left'"
    :width="variant === 'compact' ? '56' : '64'"
    :prevent-overflow="true"
    :offset="8"
  >
    <template #trigger>
      <button
        :class="[
          'flex items-center ltr:gap-2 rtl:gap-2 px-3 py-2 rounded-lg transition-all duration-200',
          'hover:bg-gray-100 dark:hover:bg-gray-800',
          'focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2',
          'text-gray-700 dark:text-gray-300',
          variant === 'compact' ? 'text-sm' : 'text-base',
          variant === 'footer' ? 'text-gray-400 hover:text-white text-sm' : ''
        ]"
        :aria-label="t('common.language')"
      >
        <!-- Current Language Flag -->
        <span class="text-lg" v-if="showFlag && currentLocale?.flag">
          {{ currentLocale.flag }}
        </span>

        <!-- Current Language Name -->
        <span v-if="showName" class="font-medium">
          {{ displayCurrentLanguage }}
        </span>

        <!-- Language Icon (if no flag) -->
        <Icon
          v-if="!showFlag || !currentLocale?.flag"
          name="heroicons:language"
          :class="[
            'transition-transform duration-200',
            variant === 'compact' ? 'h-4 w-4' : 'h-5 w-5'
          ]"
        />

        <!-- Dropdown Arrow -->
        <Icon
          name="heroicons:chevron-down"
          class="h-4 w-4 transition-transform duration-200"
        />
      </button>
    </template>

    <!-- Language Options -->
    <UiDropdownItem
      v-for="localeOption in availableLocales"
      :key="localeOption.code"
      @click="selectLanguage(localeOption.code)"
    >
      <div
        :class="[
          'w-full flex items-center ltr:gap-3 rtl:gap-3 ltr:text-left rtl:text-right',
          localeOption.code === locale ?
            'text-indigo-700 dark:text-indigo-300' :
            'text-gray-700 dark:text-gray-300'
        ]"
        role="menuitem"
        :aria-current="localeOption.code === locale ? 'true' : 'false'"
      >
        <!-- Flag -->
        <span class="text-lg" v-if="localeOption.flag">
          {{ localeOption.flag }}
        </span>

        <!-- Language Names -->
        <div class="flex-1">
          <div class="font-medium">{{ localeOption.nativeName }}</div>
          <div class="text-xs text-gray-500 dark:text-gray-400">
            {{ localeOption.name }}
          </div>
        </div>

        <!-- Current Language Indicator -->
        <Icon
          v-if="localeOption.code === locale"
          name="heroicons:check"
          class="h-4 w-4 text-indigo-600 dark:text-indigo-400"
        />

        <!-- RTL Indicator -->
        <span
          v-if="localeOption.dir === 'rtl'"
          class="text-xs px-1.5 py-0.5 bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 rounded"
        >
          RTL
        </span>
      </div>
    </UiDropdownItem>

    <!-- Divider -->
    <div
      v-if="showSettings"
      class="my-1 border-t border-gray-200 dark:border-gray-700 mx-2"
    ></div>

    <!-- Settings Link (Optional) -->
    <UiDropdownItem
      v-if="showSettings"
      @click="openLanguageSettings"
    >
      <div class="w-full flex items-center ltr:gap-3 rtl:gap-3 ltr:text-left rtl:text-right text-sm text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300">
        <Icon name="heroicons:cog-6-tooth" class="h-4 w-4" />
        {{ t('navigation.settings') }}
      </div>
    </UiDropdownItem>
  </UiDropdown>
</template>

<script setup lang="ts">
import { computed } from 'vue'

export interface LanguageSwitcherProps {
  variant?: 'default' | 'compact' | 'footer'
  showFlag?: boolean
  showName?: boolean
  showSettings?: boolean
  dropdownPosition?: 'left' | 'right'
  displayMode?: 'native' | 'english' | 'both'
}

const props = withDefaults(defineProps<LanguageSwitcherProps>(), {
  variant: 'default',
  showFlag: true,
  showName: true,
  showSettings: false,
  dropdownPosition: 'left',
  displayMode: 'native'
})

const emit = defineEmits<{
  languageChanged: [locale: string]
  settingsClicked: []
}>()

// i18n composables
const { locale, t } = useI18n()
const { switchLanguage } = useLanguageSwitcher()

// Available locales from i18n config
const availableLocales = computed(() => {
  return [
    { code: 'en', name: 'English', nativeName: 'English', dir: 'ltr', flag: '🇺🇸', enabled: true },
    { code: 'he', name: 'Hebrew', nativeName: 'עברית', dir: 'rtl', flag: '🇮🇱', enabled: true },
    { code: 'ar', name: 'Arabic', nativeName: 'العربية', dir: 'rtl', flag: '🇸🇦', enabled: true }
  ]
})

// Current locale info
const currentLocale = computed(() =>
  availableLocales.value.find(l => l.code === locale.value)
)

// Display current language based on mode
const displayCurrentLanguage = computed(() => {
  if (!currentLocale.value) return 'Language'

  switch (props.displayMode) {
    case 'english':
      return currentLocale.value.name
    case 'both':
      return `${currentLocale.value.nativeName} (${currentLocale.value.name})`
    case 'native':
    default:
      return currentLocale.value.nativeName
  }
})

// Language selection
const selectLanguage = async (localeCode: string) => {
  try {
    const success = await switchLanguage(localeCode)

    if (success) {
      emit('languageChanged', localeCode)

      // Show success message
      console.log(`Language switched to: ${localeCode}`)
    } else {
      console.error('Failed to switch language')
    }
  } catch (error) {
    console.error('Error switching language:', error)
  }
}

// Settings
const openLanguageSettings = () => {
  emit('settingsClicked')
}
</script>

<style scoped>
/* Enhanced RTL support with Tailwind CSS 4.1 */

/* Smooth transitions */
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

/* Focus styles for accessibility */
button:focus-visible {
  outline: 2px solid var(--color-brandPrimary-500);
  outline-offset: 2px;
}
</style>