/**
 * Date Formatters Plugin
 *
 * Makes date formatting utilities globally available in the Nuxt app
 * This plugin runs on the client side to ensure Day.js is available
 */

import { dateFormatters } from '~/utils/dateFormatters'

export default defineNuxtPlugin(() => {
  return {
    provide: {
      // Provide all date formatters
      dateFormatters,
      
      // Provide individual formatters for convenience
      formatDate: dateFormatters.formatDate,
      formatDateDisplay: dateFormatters.formatDateDisplay,
      formatDateFull: dateFormatters.formatDateFull,
      formatDateTime: dateFormatters.formatDateTime,
      formatDateTimeFull: dateFormatters.formatDateTimeFull,
      formatTime: dateFormatters.formatTime,
      formatTime24: dateFormatters.formatTime24,
      formatRelativeTime: dateFormatters.formatRelativeTime,
      formatRelativeTimeLong: dateFormatters.formatRelativeTimeLong,
      formatLegalDate: dateFormatters.formatLegalDate,
      formatLegalDateTime: dateFormatters.formatLegalDateTime,
      formatFileTimestamp: dateFormatters.formatFileTimestamp,
      formatLogTimestamp: dateFormatters.formatLogTimestamp,
      formatDateWithTimezone: dateFormatters.formatDateWithTimezone,
      formatDuration: dateFormatters.formatDuration,
      formatAge: dateFormatters.formatAge,
      fromNow: dateFormatters.fromNow,
      isValidDate: dateFormatters.isValidDate
    }
  }
})

// Type declarations for the date formatters
interface DateFormatters {
  formatDate: typeof dateFormatters.formatDate
  formatDateDisplay: typeof dateFormatters.formatDateDisplay
  formatDateFull: typeof dateFormatters.formatDateFull
  formatDateTime: typeof dateFormatters.formatDateTime
  formatDateTimeFull: typeof dateFormatters.formatDateTimeFull
  formatTime: typeof dateFormatters.formatTime
  formatTime24: typeof dateFormatters.formatTime24
  formatRelativeTime: typeof dateFormatters.formatRelativeTime
  formatRelativeTimeLong: typeof dateFormatters.formatRelativeTimeLong
  formatLegalDate: typeof dateFormatters.formatLegalDate
  formatLegalDateTime: typeof dateFormatters.formatLegalDateTime
  formatFileTimestamp: typeof dateFormatters.formatFileTimestamp
  formatLogTimestamp: typeof dateFormatters.formatLogTimestamp
  formatDateWithTimezone: typeof dateFormatters.formatDateWithTimezone
  formatDuration: typeof dateFormatters.formatDuration
  formatAge: typeof dateFormatters.formatAge
  fromNow: typeof dateFormatters.fromNow
  isValidDate: typeof dateFormatters.isValidDate
}

// Extend the NuxtApp interface for TypeScript support
declare module '#app' {
  interface NuxtApp {
    $dateFormatters: DateFormatters
    $formatDate: typeof dateFormatters.formatDate
    $formatDateDisplay: typeof dateFormatters.formatDateDisplay
    $formatDateFull: typeof dateFormatters.formatDateFull
    $formatDateTime: typeof dateFormatters.formatDateTime
    $formatDateTimeFull: typeof dateFormatters.formatDateTimeFull
    $formatTime: typeof dateFormatters.formatTime
    $formatTime24: typeof dateFormatters.formatTime24
    $formatRelativeTime: typeof dateFormatters.formatRelativeTime
    $formatRelativeTimeLong: typeof dateFormatters.formatRelativeTimeLong
    $formatLegalDate: typeof dateFormatters.formatLegalDate
    $formatLegalDateTime: typeof dateFormatters.formatLegalDateTime
    $formatFileTimestamp: typeof dateFormatters.formatFileTimestamp
    $formatLogTimestamp: typeof dateFormatters.formatLogTimestamp
    $formatDateWithTimezone: typeof dateFormatters.formatDateWithTimezone
    $formatDuration: typeof dateFormatters.formatDuration
    $formatAge: typeof dateFormatters.formatAge
    $fromNow: typeof dateFormatters.fromNow
    $isValidDate: typeof dateFormatters.isValidDate
  }
}

declare module 'vue' {
  interface ComponentCustomProperties {
    $dateFormatters: typeof dateFormatters
    $formatDate: typeof dateFormatters.formatDate
    $formatDateDisplay: typeof dateFormatters.formatDateDisplay
    $formatDateFull: typeof dateFormatters.formatDateFull
    $formatDateTime: typeof dateFormatters.formatDateTime
    $formatDateTimeFull: typeof dateFormatters.formatDateTimeFull
    $formatTime: typeof dateFormatters.formatTime
    $formatTime24: typeof dateFormatters.formatTime24
    $formatRelativeTime: typeof dateFormatters.formatRelativeTime
    $formatRelativeTimeLong: typeof dateFormatters.formatRelativeTimeLong
    $formatLegalDate: typeof dateFormatters.formatLegalDate
    $formatLegalDateTime: typeof dateFormatters.formatLegalDateTime
    $formatFileTimestamp: typeof dateFormatters.formatFileTimestamp
    $formatLogTimestamp: typeof dateFormatters.formatLogTimestamp
    $formatDateWithTimezone: typeof dateFormatters.formatDateWithTimezone
    $formatDuration: typeof dateFormatters.formatDuration
    $formatAge: typeof dateFormatters.formatAge
    $fromNow: typeof dateFormatters.fromNow
    $isValidDate: typeof dateFormatters.isValidDate
  }
}
