<template>
  <section :class="sectionClass">
    <div class="container mx-auto px-6">
      <!-- Section Header -->
      <div v-if="title || subtitle" :class="headerClass">
        <h2 v-if="title" :class="titleClass">
          {{ title }}
        </h2>
        <p v-if="subtitle" :class="subtitleClass">
          {{ subtitle }}
        </p>
      </div>

      <!-- Features Grid -->
      <div :class="gridClass">
        <div
          v-for="(feature, index) in features"
          :key="index"
          :class="[
            'group relative',
            cardClass,
            feature.highlighted ? highlightedCardClass : ''
          ]"
          :style="{ animationDelay: `${index * 100}ms` }"
        >
          <!-- Feature Icon -->
          <div :class="iconContainerClass">
            <Icon 
              v-if="feature.icon" 
              :name="feature.icon" 
              :class="iconClass"
            />
            <div v-else-if="feature.image" class="w-full h-full">
              <img 
                :src="feature.image" 
                :alt="feature.title"
                class="w-full h-full object-cover rounded-lg"
              />
            </div>
            <div v-else :class="defaultIconClass">
              <Icon name="material-symbols:star" :class="iconClass" />
            </div>
          </div>

          <!-- Feature Content -->
          <div class="flex-1">
            <h3 :class="featureTitleClass">
              {{ feature.title }}
            </h3>
            <p :class="featureDescriptionClass">
              {{ feature.description }}
            </p>

            <!-- Feature List -->
            <ul v-if="feature.items && feature.items.length > 0" class="mt-4 space-y-2">
              <li 
                v-for="(item, itemIndex) in feature.items"
                :key="itemIndex"
                class="flex items-center text-sm text-gray-600 dark:text-gray-300"
              >
                <Icon name="material-symbols:check-circle" class="w-4 h-4 text-green-500 mr-2 flex-shrink-0" />
                {{ item }}
              </li>
            </ul>

            <!-- Feature Action -->
            <div v-if="feature.action" class="mt-6">
              <UiButton
                :variant="feature.action.variant || 'outline'"
                :size="feature.action.size || 'sm'"
                :to="feature.action.to"
                :href="feature.action.href"
                class="group-hover:scale-105 transition-transform duration-200"
              >
                {{ feature.action.text }}
                <Icon 
                  v-if="feature.action.icon" 
                  :name="feature.action.icon" 
                  class="w-4 h-4 ml-2" 
                />
              </UiButton>
            </div>
          </div>

          <!-- Highlight Badge -->
          <div v-if="feature.badge" class="absolute -top-2 -right-2">
            <UiBadge 
              :variant="feature.badge.variant || 'primary'"
              :size="feature.badge.size || 'sm'"
            >
              {{ feature.badge.text }}
            </UiBadge>
          </div>
        </div>
      </div>

      <!-- Bottom CTA -->
      <div v-if="bottomAction" class="text-center mt-12">
        <UiButton
          :variant="bottomAction.variant || 'primary'"
          :size="bottomAction.size || 'lg'"
          :to="bottomAction.to"
          :href="bottomAction.href"
          class="transform hover:scale-105 transition-all duration-300"
        >
          <Icon v-if="bottomAction.icon" :name="bottomAction.icon" class="w-5 h-5 mr-2" />
          {{ bottomAction.text }}
        </UiButton>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
interface FeatureAction {
  text: string
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost'
  size?: 'sm' | 'md' | 'lg'
  to?: string
  href?: string
  icon?: string
}

interface FeatureBadge {
  text: string
  variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'danger'
  size?: 'sm' | 'md' | 'lg'
}

interface Feature {
  title: string
  description: string
  icon?: string
  image?: string
  items?: string[]
  action?: FeatureAction
  badge?: FeatureBadge
  highlighted?: boolean
}

interface Props {
  title?: string
  subtitle?: string
  features: Feature[]
  variant?: 'default' | 'cards' | 'minimal' | 'highlighted'
  columns?: 2 | 3 | 4
  background?: 'white' | 'gray' | 'dark'
  textAlign?: 'left' | 'center'
  bottomAction?: FeatureAction
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'cards',
  columns: 3,
  background: 'white',
  textAlign: 'center'
})

// Computed classes
const sectionClass = computed(() => {
  const backgrounds = {
    white: 'bg-white',
    gray: 'bg-gray-50',
    dark: 'bg-gray-900 text-white'
  }
  return `py-16 ${backgrounds[props.background]}`
})

const headerClass = computed(() => {
  const alignments = {
    left: 'text-left mb-12',
    center: 'text-center mb-12'
  }
  return alignments[props.textAlign]
})

const titleClass = computed(() => {
  const baseClass = 'font-bold mb-4'
  const colorClass = props.background === 'dark' ? 'text-white' : 'text-gray-800'
  return `${baseClass} text-3xl md:text-4xl ${colorClass}`
})

const subtitleClass = computed(() => {
  const baseClass = 'max-w-3xl'
  const colorClass = props.background === 'dark' ? 'text-gray-300' : 'text-gray-600'
  const alignClass = props.textAlign === 'center' ? 'mx-auto' : ''
  return `${baseClass} text-lg ${colorClass} ${alignClass}`
})

const gridClass = computed(() => {
  const columns = {
    2: 'grid-cols-1 md:grid-cols-2',
    3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
    4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4'
  }
  return `grid ${columns[props.columns]} gap-8`
})

const cardClass = computed(() => {
  const variants = {
    default: 'p-6',
    cards: 'bg-white dark:bg-gray-800 p-8 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-lg hover:border-brandPrimary-200 dark:hover:border-brandPrimary-700 transition-all duration-300',
    minimal: 'p-6 hover:bg-gray-50 dark:hover:bg-gray-800 rounded-lg transition-colors duration-200',
    highlighted: 'bg-gradient-to-br from-brandPrimary-50 to-purple-50 dark:from-brandPrimary-900/20 dark:to-purple-900/20 p-8 rounded-xl border border-brandPrimary-200 dark:border-brandPrimary-700 hover:shadow-lg transition-all duration-300'
  }
  return `${variants[props.variant]} animate-fade-in-up`
})

const highlightedCardClass = computed(() => {
  return 'ring-2 ring-brandPrimary-500 ring-opacity-50'
})

const iconContainerClass = computed(() => {
  const baseClass = 'flex-shrink-0 mb-4'
  const colorClass = props.background === 'dark' ? 'text-brandPrimary-400' : 'text-brandPrimary-600'
  return `${baseClass} ${colorClass}`
})

const iconClass = computed(() => {
  return 'w-12 h-12'
})

const defaultIconClass = computed(() => {
  const colorClass = props.background === 'dark' ? 'text-brandPrimary-400' : 'text-brandPrimary-600'
  return colorClass
})

const featureTitleClass = computed(() => {
  const baseClass = 'font-semibold mb-3'
  const colorClass = props.background === 'dark' ? 'text-white' : 'text-gray-900'
  return `${baseClass} text-xl ${colorClass}`
})

const featureDescriptionClass = computed(() => {
  const colorClass = props.background === 'dark' ? 'text-gray-300' : 'text-gray-600'
  return `${colorClass} leading-relaxed`
})
</script>

<style scoped>
@keyframes fadeInFromBottom {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  animation: fadeInFromBottom 0.6s ease-out forwards;
  opacity: 0;
}
</style>
