<template>
  <div class="relative">
    <!-- Notification Bell -->
    <UiButton
      @click="toggleNotifications"
      variant="flat"
      size="lg"
      class="relative p-2"
      aria-label="View notifications"
    >
      <Icon name="heroicons:bell" size="calc(var(--spacing) * 6)" />
      
      <!-- Notification Badge -->
      <span
        v-if="unreadCount > 0"
        class="absolute top-0 right-0  bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-medium "
      >
        {{ unreadCount > 99 ? '99+' : unreadCount }}
      </span>
      
      <!-- Pulse Animation for New Notifications -->
      <span
        v-if="hasNewNotifications"
        class="absolute top-0 right-0 bg-red-500 rounded-full h-5 w-5 animate-ping"
      ></span>
    </UiButton>

    <!-- Notifications Dropdown -->
    <Transition
      enter-active-class="transition ease-out duration-200"
      enter-from-class="transform opacity-0 scale-95"
      enter-to-class="transform opacity-100 scale-100"
      leave-active-class="transition ease-in duration-150"
      leave-from-class="transform opacity-100 scale-100"
      leave-to-class="transform opacity-0 scale-95"
    >
      <div
        v-if="showNotifications"
        class="absolute right-0 top-12 w-96 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-50"
      >
        <!-- Header -->
        <div class="p-4 border-b border-gray-200 dark:border-gray-700">
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Notifications</h3>
            <div class="flex items-center gap-2">
              <UiButton
                v-if="unreadCount > 0"
                @click="markAllAsRead"
                size="sm"
                variant="ghost"
                class="text-brandPrimary"
              >
                Mark all read
              </UiButton>
              <UiButton @click="showNotifications = false" size="sm" variant="ghost">
                <Icon name="material-symbols:close" class="h-4 w-4" />
              </UiButton>
            </div>
          </div>
          
          <!-- Filter Tabs -->
          <div class="flex gap-1 mt-3">
            <UiButton
              @click="activeFilter = 'all'"
              size="sm"
              :variant="activeFilter === 'all' ? 'primary' : 'ghost'"
            >
              All ({{ notifications.length }})
            </UiButton>
            <UiButton
              @click="activeFilter = 'unread'"
              size="sm"
              :variant="activeFilter === 'unread' ? 'primary' : 'ghost'"
            >
              Unread ({{ unreadCount }})
            </UiButton>
            <UiButton
              @click="activeFilter = 'system'"
              size="sm"
              :variant="activeFilter === 'system' ? 'primary' : 'ghost'"
            >
              System
            </UiButton>
            <UiButton
              @click="activeFilter = 'security'"
              size="sm"
              :variant="activeFilter === 'security' ? 'primary' : 'ghost'"
            >
              Security
            </UiButton>
          </div>
        </div>

        <!-- Notifications List -->
        <div class="max-h-96 overflow-y-auto">
          <div v-if="filteredNotifications.length === 0" class="p-8 text-center">
            <Icon name="material-symbols:notifications-off" class="h-12 w-12 text-gray-400 mx-auto mb-3" />
            <p class="text-gray-500 dark:text-gray-400">No notifications</p>
            <p class="text-xs text-gray-400 dark:text-gray-500 mt-1">
              You're all caught up!
            </p>
          </div>
          
          <div
            v-for="notification in filteredNotifications"
            :key="notification.id"
            @click="handleNotificationClick(notification)"
            :class="[
              'p-4 border-b border-gray-100 dark:border-gray-700 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors',
              !notification.read ? 'bg-blue-50 dark:bg-blue-900/10' : ''
            ]"
          >
            <div class="flex items-start gap-3">
              <!-- Notification Icon -->
              <div 
                :class="[
                  'flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center',
                  getNotificationIconClass(notification.type)
                ]"
              >
                <Icon :name="getNotificationIcon(notification.type)" class="h-4 w-4 text-white" />
              </div>
              
              <!-- Notification Content -->
              <div class="flex-1 min-w-0">
                <div class="flex items-start justify-between">
                  <div class="flex-1">
                    <p class="text-sm font-medium text-gray-900 dark:text-white">
                      {{ notification.title }}
                    </p>
                    <p class="text-sm text-gray-600 dark:text-gray-300 mt-1">
                      {{ notification.message }}
                    </p>
                    
                    <!-- Priority Badge -->
                    <div v-if="notification.priority && notification.priority !== 'normal'" class="mt-2">
                      <span
                        :class="[
                          'inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium',
                          getPriorityClass(notification.priority)
                        ]"
                      >
                        {{ notification.priority }}
                      </span>
                    </div>
                    
                    <!-- Action Buttons -->
                    <div v-if="notification.actions && notification.actions.length > 0" class="flex gap-2 mt-3">
                      <UiButton
                        v-for="action in notification.actions"
                        :key="action.label"
                        @click.stop="handleNotificationAction(notification, action)"
                        size="sm"
                        :variant="action.primary ? 'primary' : 'outline'"
                      >
                        {{ action.label }}
                      </UiButton>
                    </div>
                  </div>
                  
                  <!-- Unread Indicator -->
                  <div v-if="!notification.read" class="w-2 h-2 bg-blue-500 rounded-full flex-shrink-0 mt-1"></div>
                </div>
                
                <!-- Timestamp -->
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-2">
                  {{ formatTimestamp(notification.timestamp) }}
                </p>
              </div>
            </div>
          </div>
        </div>

        <!-- Footer -->
        <div class="p-4 border-t border-gray-200 dark:border-gray-700">
          <div class="flex gap-2">
            <UiButton variant="outline" class="flex-1" @click="viewAllNotifications">
              View All Notifications
            </UiButton>
            <UiButton variant="outline" @click="openNotificationSettings">
              <Icon name="material-symbols:settings" class="h-4 w-4" />
            </UiButton>
          </div>
        </div>
      </div>
    </Transition>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, onUnmounted, ref } from 'vue'
import { useRouter } from 'vue-router'

interface NotificationAction {
  label: string
  action: string
  primary?: boolean
}

interface Notification {
  id: string
  type: 'system' | 'security' | 'user' | 'tenant' | 'backup' | 'update' | 'warning' | 'error' | 'success' | 'info'
  title: string
  message: string
  timestamp: string
  read: boolean
  priority?: 'low' | 'normal' | 'high' | 'urgent'
  category?: 'system' | 'security' | 'user' | 'tenant'
  actionUrl?: string
  actions?: NotificationAction[]
  metadata?: Record<string, any>
}

const router = useRouter()

// State
const showNotifications = ref(false)
const activeFilter = ref<'all' | 'unread' | 'system' | 'security'>('all')
const hasNewNotifications = ref(false)

// Mock notifications data - in real app, this would come from API/WebSocket
const notifications = ref<Notification[]>([
  {
    id: '1',
    type: 'security',
    title: 'Security Alert',
    message: 'Multiple failed login attempts detected from IP *************',
    timestamp: new Date(Date.now() - 1000 * 60 * 5).toISOString(),
    read: false,
    priority: 'high',
    category: 'security',
    actions: [
      { label: 'Block IP', action: 'block-ip', primary: true },
      { label: 'View Details', action: 'view-details' }
    ]
  },
  {
    id: '2',
    type: 'system',
    title: 'System Update Available',
    message: 'Platform version 2.1.0 is available with security improvements',
    timestamp: new Date(Date.now() - 1000 * 60 * 15).toISOString(),
    read: false,
    priority: 'normal',
    category: 'system',
    actions: [
      { label: 'Update Now', action: 'update-system', primary: true },
      { label: 'Schedule Later', action: 'schedule-update' }
    ]
  },
  {
    id: '3',
    type: 'backup',
    title: 'Backup Completed',
    message: 'Daily backup completed successfully at 2:00 AM',
    timestamp: new Date(Date.now() - 1000 * 60 * 30).toISOString(),
    read: true,
    priority: 'normal',
    category: 'system'
  },
  {
    id: '4',
    type: 'warning',
    title: 'Storage Warning',
    message: 'Platform storage is 85% full. Consider upgrading storage capacity.',
    timestamp: new Date(Date.now() - 1000 * 60 * 60).toISOString(),
    read: false,
    priority: 'high',
    category: 'system',
    actions: [
      { label: 'Upgrade Storage', action: 'upgrade-storage', primary: true },
      { label: 'View Usage', action: 'view-usage' }
    ]
  },
  {
    id: '5',
    type: 'user',
    title: 'New Admin User',
    message: 'Sarah Johnson has been granted admin privileges',
    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(),
    read: true,
    priority: 'normal',
    category: 'user'
  },
  {
    id: '6',
    type: 'error',
    title: 'API Rate Limit Exceeded',
    message: 'Tenant "Acme Corp" exceeded API rate limits',
    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 3).toISOString(),
    read: false,
    priority: 'urgent',
    category: 'tenant',
    actions: [
      { label: 'Increase Limits', action: 'increase-limits', primary: true },
      { label: 'Contact Tenant', action: 'contact-tenant' }
    ]
  }
])

// Computed properties
const unreadCount = computed(() => notifications.value.filter(n => !n.read).length)

const filteredNotifications = computed(() => {
  let filtered = notifications.value
  
  switch (activeFilter.value) {
    case 'unread':
      filtered = filtered.filter(n => !n.read)
      break
    case 'system':
      filtered = filtered.filter(n => n.category === 'system')
      break
    case 'security':
      filtered = filtered.filter(n => n.category === 'security')
      break
  }
  
  return filtered.sort((a, b) => {
    // Sort by priority first, then by timestamp
    const priorityOrder = { urgent: 4, high: 3, normal: 2, low: 1 }
    const aPriority = priorityOrder[a.priority || 'normal']
    const bPriority = priorityOrder[b.priority || 'normal']
    
    if (aPriority !== bPriority) {
      return bPriority - aPriority
    }
    
    return new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
  })
})

// Methods
const toggleNotifications = () => {
  showNotifications.value = !showNotifications.value
  if (showNotifications.value) {
    hasNewNotifications.value = false
  }
}

const markAllAsRead = () => {
  notifications.value.forEach(notification => {
    notification.read = true
  })
}

const handleNotificationClick = (notification: Notification) => {
  notification.read = true
  
  if (notification.actionUrl) {
    router.push(notification.actionUrl)
    showNotifications.value = false
  }
}

const handleNotificationAction = (notification: Notification, action: NotificationAction) => {
  console.log('Notification action:', action.action, 'for notification:', notification.id)
  
  // Mark as read when action is taken
  notification.read = true
  
  // Handle specific actions
  switch (action.action) {
    case 'block-ip':
      // Handle IP blocking
      break
    case 'update-system':
      // Handle system update
      break
    case 'upgrade-storage':
      // Handle storage upgrade
      break
    // Add more action handlers as needed
  }
}

const getNotificationIcon = (type: Notification['type']) => {
  const icons = {
    system: 'material-symbols:settings',
    security: 'material-symbols:security',
    user: 'material-symbols:person',
    tenant: 'material-symbols:business',
    backup: 'material-symbols:backup',
    update: 'material-symbols:system-update',
    warning: 'material-symbols:warning',
    error: 'material-symbols:error',
    success: 'material-symbols:check-circle',
    info: 'material-symbols:info'
  }
  return icons[type] || 'material-symbols:notifications'
}

const getNotificationIconClass = (type: Notification['type']) => {
  const classes = {
    system: 'bg-blue-500',
    security: 'bg-red-500',
    user: 'bg-purple-500',
    tenant: 'bg-green-500',
    backup: 'bg-indigo-500',
    update: 'bg-cyan-500',
    warning: 'bg-orange-500',
    error: 'bg-red-600',
    success: 'bg-green-600',
    info: 'bg-blue-400'
  }
  return classes[type] || 'bg-gray-500'
}

const getPriorityClass = (priority: string) => {
  const classes = {
    low: 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300',
    normal: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
    high: 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300',
    urgent: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
  }
  return classes[priority as keyof typeof classes] || classes.normal
}

const formatTimestamp = (timestamp: string) => {
  const date = new Date(timestamp)
  const now = new Date()
  const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))
  
  if (diffInMinutes < 1) return 'Just now'
  if (diffInMinutes < 60) return `${diffInMinutes}m ago`
  
  const diffInHours = Math.floor(diffInMinutes / 60)
  if (diffInHours < 24) return `${diffInHours}h ago`
  
  const diffInDays = Math.floor(diffInHours / 24)
  if (diffInDays < 7) return `${diffInDays}d ago`
  
  return date.toLocaleDateString()
}

const viewAllNotifications = () => {
  showNotifications.value = false
  router.push('/dashboard/notifications')
}

const openNotificationSettings = () => {
  showNotifications.value = false
  router.push('/dashboard/settings/notifications')
}

const simulateNewNotifications = () => {
  // Simulate receiving new notifications
  setInterval(() => {
    if (Math.random() < 0.05) { // 5% chance every interval
      const types: Notification['type'][] = ['system', 'security', 'user', 'warning']
      const randomType = types[Math.floor(Math.random() * types.length)]
      
      const newNotification: Notification = {
        id: Date.now().toString(),
        type: randomType,
        title: `New ${randomType} notification`,
        message: 'This is a simulated notification for demo purposes',
        timestamp: new Date().toISOString(),
        read: false,
        priority: 'normal',
        category: randomType === 'security' ? 'security' : 'system'
      }
      
      notifications.value.unshift(newNotification)
      hasNewNotifications.value = true
    }
  }, 30000) // Check every 30 seconds
}

// Close notifications when clicking outside
const handleClickOutside = (event: Event) => {
  const target = event.target as HTMLElement
  if (!target.closest('.relative')) {
    showNotifications.value = false
  }
}

// Expose unread count for parent component
defineExpose({
  unreadCount,
  hasNewNotifications
})

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
  simulateNewNotifications()
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>
