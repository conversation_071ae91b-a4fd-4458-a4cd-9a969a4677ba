<template>
  <div class="space-y-6">


    <div v-if="loading" class="flex justify-center items-center py-8">
      <p class="text-primary-500 dark:text-primary-400">Loading audit log settings...</p>
    </div>

    <div v-else-if="error" class="text-red-500 dark:text-red-400 py-8 text-center">
      <p>Error loading audit log settings: {{ error.message }}</p>
      <UiButton @click="fetchAuditLogSettings" class="mt-4" color="primary">Retry</UiButton>
    </div>

    <div v-else class="space-y-8">
      <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <h2 class="text-xl font-semibold text-gray-800 dark:text-gray-100 mb-4">Audit Log Retention Policy</h2>
        <p class="text-gray-600 dark:text-gray-400 mb-4">
          Define how long audit logs are stored before being automatically purged.
          Longer retention may increase storage costs but is essential for compliance and historical auditing.
        </p>

        <div class="flex items-center space-x-4">

          <label for="retentionDays" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Retain logs
            for</label>
          <UiInput type="number" id="retentionDays" v-model.number="form.retentionDays"
            @blur="updateSetting('auditLogRetentionDays', form.retentionDays)" min="30" max="1825"></UiInput>
          <span class="text-sm text-gray-700 dark:text-gray-300">days</span>
        </div>
        <p class="mt-2 text-xs text-gray-500 dark:text-gray-400">Recommended minimum: 90 days. Max: 5 years (1825 days).
        </p>
      </div>

      <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <h2 class="text-xl font-semibold text-gray-800 dark:text-gray-100 mb-4">Audit Log Export Schedule</h2>
        <p class="text-gray-600 dark:text-gray-400 mb-4">
          Automate the export of audit logs to a secure destination for long-term archival.
        </p>

        <div class="space-y-4">
          <div class="flex items-center justify-between">
            <label for="enableAutoExport" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Enable
              Automatic Exports</label>
            <UiToggle id="enableAutoExport" v-model="form.enableAutoExport"
              @change="updateSetting('auditLogEnableAutoExport', form.enableAutoExport)" color="primary" />
          </div>

          <div v-if="form.enableAutoExport" class="pl-6 border-l-2 border-gray-200 dark:border-gray-700 space-y-4">
            <div>
              <label for="exportFrequency" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Export
                Frequency</label>
              <UiSelect id="exportFrequency" v-model="form.exportFrequency"
                @change="updateSetting('auditLogExportFrequency', form.exportFrequency)"
                :options="[{ value: 'daily', label: 'Daily' }, { value: 'weekly', label: 'Weekly' }, { value: 'monthly', label: 'Monthly' }]"
                class="mt-1 block"
                />
               
            </div>
            <div>
              <label for="exportDestination" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Export
                Destination (e.g., S3 Bucket Name)</label>
              <UiInput type="text" id="exportDestination" v-model="form.exportDestination"
                @blur="updateSetting('auditLogExportDestination', form.exportDestination)"
                placeholder="my-secure-s3-bucket" />
              <p class="mt-2 text-xs text-gray-500 dark:text-gray-400">Ensure this destination is properly configured
                for write access.</p>
            </div>
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <h2 class="text-xl font-semibold text-gray-800 dark:text-gray-100 mb-4">Critical Event Alerts</h2>
        <p class="text-gray-600 dark:text-gray-400 mb-4">
          Receive immediate notifications for high-priority audit events.
        </p>
        <div class="space-y-4">
          <div class="flex items-center justify-between">
            <label for="alertOnFailedLogins" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Alert on
              multiple failed login attempts</label>
            <UiToggle id="alertOnFailedLogins" v-model="form.alertOnFailedLogins"
              @change="updateSetting('auditLogAlertOnFailedLogins', form.alertOnFailedLogins)" color="primary" />
          </div>
          <div class="flex items-center justify-between">
            <label for="alertOnDataExport" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Alert on
              large data exports</label>
            <UiToggle id="alertOnDataExport" v-model="form.alertOnDataExport"
              @change="updateSetting('auditLogAlertOnDataExport', form.alertOnDataExport)" color="primary" />
          </div>
          <div>
            <label for="alertRecipients" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Alert
              Recipients (comma-separated emails)</label>
            <UiInput type="email" id="alertRecipients" v-model="form.alertRecipients"
              @blur="updateSetting('auditLogAlertRecipients', form.alertRecipients)"
              placeholder="<EMAIL>, <EMAIL>" />
            <p class="mt-2 text-xs text-gray-500 dark:text-gray-400">Emails to notify when critical events occur.</p>
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6 text-center">
        <p class="text-lg font-medium text-gray-800 dark:text-gray-100 mb-4">Need to review specific events?</p>
        <UiButton to="/dashboard/platform/audit-logs" color="primary" icon="i-heroicons-arrow-right-circle" size="lg">
          View Detailed Audit Logs
        </UiButton>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive } from 'vue';
import { useApi } from '~/composables/useApi';
import { useToast } from '~/composables/useToast';
import { PlatformRoles, TenantRoles } from'~/app/features/auth/constants/roles';

definePageMeta({
  layout: 'dashboard',
  title: 'Audit Log Configuration',
  subtitle: 'Manage audit log settings and access detailed logs.',
  middleware: ['rbac'],
  roles: [PlatformRoles.SUPER_ADMIN],
  breadcrumb: [
    { label: 'Dashboard', href: '/dashboard' },
    { label: 'Settings', href: '/dashboard/settings' },
    { label: 'Audit Logs' },
  ],
});

const { get, patch } = useApi();
const { showToast } = useToast();

// Reactive form state to hold current settings
const form = reactive({
  retentionDays: 365, // Default value
  enableAutoExport: false,
  exportFrequency: 'monthly',
  exportDestination: '',
  alertOnFailedLogins: true,
  alertOnDataExport: true,
  alertRecipients: '',
});

const loading = ref(true);
const error = ref<Error | null>(null);

// Map of setting keys to their default values for fetching
const settingKeys = {
  auditLogRetentionDays: 365,
  auditLogEnableAutoExport: false,
  auditLogExportFrequency: 'monthly',
  auditLogExportDestination: '',
  auditLogAlertOnFailedLogins: true,
  auditLogAlertOnDataExport: true,
  auditLogAlertRecipients: '',
};

onMounted(() => {
  fetchAuditLogSettings();
});

const fetchAuditLogSettings = async () => {
  loading.value = true;
  error.value = null;
  try {
    // Fetch each setting individually using the /settings/:key endpoint
    // This assumes the backend's /settings endpoint supports these keys
    for (const key in settingKeys) {
      const response = await get(`/settings/${key}`);
      if (response && response.value !== undefined) {
        // Explicitly cast key to a keyof typeof form to satisfy TypeScript
        (form as any)[key.replace('auditLog', '').charAt(0).toLowerCase() + key.replace('auditLog', '').slice(1)] = response.value;
      }
    }
    showToast('Audit log settings loaded.', 'info');
  } catch (err: any) {
    error.value = new Error(err.message || 'Failed to fetch audit log settings.');
    console.error('Error fetching audit log settings:', err);
    showToast(`Error: ${error.value.message}`, 'error');
  } finally {
    loading.value = false;
  }
};

const updateSetting = async (key: string, value: any) => {
  try {
    // API Call: PATCH /settings/:key
    await patch(`/settings/${key}`, { value });
    showToast(`Setting '${key}' updated successfully!`, 'success');
  } catch (err: any) {
    console.error(`Failed to update setting '${key}':`, err);
    showToast(`Error updating setting: ${err.message || 'Unknown error'}`, 'error');
    // Optionally, revert the UI state if the update failed
    // await fetchAuditLogSettings(); // Or revert specific field
  }
};
</script>

<style scoped>
/* Page-specific styles can go here */
</style>