import { Plugin, Plugin<PERSON>ey } from 'prosemirror-state'

export const AutoPageBreak = () => {
  return new Plugin({
    key: new PluginKey('auto-page-break'),

    view(editorView) {
        console.log('editorView', editorView, editorView.dom);
        
      const pageHeightPx = mmToPx(297)
      const pagePaddingPx = mmToPx(40)
      const contentMaxHeight = pageHeightPx - pagePaddingPx

      const checkHeights = () => {
        const { state, dispatch } = editorView
        const { doc } = state
    
        
        let foundOverflow = false
        console.log(foundOverflow);
        
        doc.descendants((node, pos) => {
          if (node.type.name === 'page') {
            const dom = editorView.domAtPos(pos + 1).node as HTMLElement
            const contentDom = dom.querySelector('.page-content') as HTMLElement

            if (contentDom && contentDom.scrollHeight > contentMaxHeight) {
              foundOverflow = true

              // Insert a new page node after this one
              const newPage = state.schema.nodes.page.create({}, state.schema.nodes.paragraph.create())
              const tr = state.tr.insert(pos + node.nodeSize, newPage)
              dispatch(tr)

              return false // Stop checking more pages
            }
          }

          return true
        })

        if (foundOverflow) {
          requestAnimationFrame(checkHeights) // Re-check after DOM updates
        }
      }

      const observer = new ResizeObserver(() => {
        requestAnimationFrame(checkHeights)
      })

 
      observer.observe(editorView.dom)

      return {
        destroy() {
          observer.disconnect()
        },
      }
    },
  })
}

function mmToPx(mm: number): number {
  return (mm * 96) / 25.4
}