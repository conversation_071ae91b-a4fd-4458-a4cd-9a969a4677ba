<template>
  <section :class="sectionClass">
    <!-- Background Pattern -->
    <div v-if="showPattern" class="absolute inset-0 overflow-hidden pointer-events-none">
      <div class="absolute -top-40 -right-40 w-80 h-80 bg-white/5 rounded-full blur-3xl" />
      <div class="absolute -bottom-40 -left-40 w-80 h-80 bg-white/5 rounded-full blur-3xl" />
    </div>

    <div class="container mx-auto px-6 relative z-10">
      <div :class="contentClass">
        <!-- Badge/Announcement -->
        <div v-if="badge" class="mb-6 animate-fade-in-up">
          <UiBadge 
            :variant="badge.variant || 'secondary'" 
            :size="badge.size || 'lg'"
            class="inline-flex items-center gap-2"
          >
            <Icon v-if="badge.icon" :name="badge.icon" class="w-4 h-4" />
            {{ badge.text }}
          </UiBadge>
        </div>

        <!-- Main Title -->
        <h2 :class="titleClass" class="animate-fade-in-up">
          {{ title }}
        </h2>

        <!-- Subtitle -->
        <p v-if="subtitle" :class="subtitleClass" class="animate-fade-in-up delay-200">
          {{ subtitle }}
        </p>

        <!-- Features List -->
        <div v-if="features && features.length > 0" class="mt-8 animate-fade-in-up delay-300">
          <ul :class="featuresListClass">
            <li
              v-for="(feature, index) in features"
              :key="index"
              class="flex items-center"
            >
              <Icon name="material-symbols:check-circle" :class="checkIconClass" />
              <span :class="featureTextClass">{{ feature }}</span>
            </li>
          </ul>
        </div>

        <!-- Action Buttons -->
        <div v-if="actions.length > 0" :class="actionsClass" class="animate-fade-in-up delay-400">
          <UiButton
            v-for="(action, index) in actions"
            :key="index"
            :variant="action.variant || (index === 0 ? 'primary' : 'outline')"
            :size="action.size || 'lg'"
            :to="action.to"
            :href="action.href"
            :target="action.external ? '_blank' : undefined"
            :class="[
              'transform hover:scale-105 transition-all duration-300 ease-in-out',
              action.class,
              index === 0 && variant !== 'light' ? 'bg-white text-brandPrimary-700 hover:bg-gray-100' : ''
            ]"
            @click="action.onClick"
          >
            <Icon v-if="action.icon" :name="action.icon" class="w-5 h-5 mr-2" />
            {{ action.text }}
          </UiButton>
        </div>

        <!-- Additional Info -->
        <div v-if="additionalInfo" class="mt-6 animate-fade-in-up delay-500">
          <p :class="additionalInfoClass">
            {{ additionalInfo }}
          </p>
        </div>

        <!-- Trust Indicators -->
        <div v-if="trustIndicators && trustIndicators.length > 0" class="mt-8 animate-fade-in-up delay-600">
          <div class="flex flex-wrap justify-center items-center gap-6">
            <div
              v-for="(indicator, index) in trustIndicators"
              :key="index"
              class="flex items-center gap-2 opacity-80 hover:opacity-100 transition-opacity duration-200"
            >
              <Icon v-if="indicator.icon" :name="indicator.icon" class="w-5 h-5" />
              <img v-else-if="indicator.image" :src="indicator.image" :alt="indicator.text" class="h-6" />
              <span class="text-sm font-medium">{{ indicator.text }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
interface Badge {
  text: string
  icon?: string
  variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'danger'
  size?: 'sm' | 'md' | 'lg'
}

interface Action {
  text: string
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost'
  size?: 'sm' | 'md' | 'lg' | 'xl'
  to?: string
  href?: string
  external?: boolean
  icon?: string
  class?: string
  onClick?: () => void
}

interface TrustIndicator {
  text: string
  icon?: string
  image?: string
}

interface Props {
  title: string
  subtitle?: string
  badge?: Badge
  actions?: Action[]
  features?: string[]
  additionalInfo?: string
  trustIndicators?: TrustIndicator[]
  variant?: 'primary' | 'secondary' | 'dark' | 'light' | 'gradient'
  size?: 'sm' | 'md' | 'lg' | 'xl'
  textAlign?: 'left' | 'center' | 'right'
  showPattern?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  actions: () => [],
  variant: 'primary',
  size: 'lg',
  textAlign: 'center',
  showPattern: true
})

// Computed classes
const sectionClass = computed(() => {
  const variants = {
    primary: 'bg-brandPrimary-600 text-white',
    secondary: 'bg-purple-600 text-white',
    dark: 'bg-gray-900 text-white',
    light: 'bg-gray-50 text-gray-900',
    gradient: 'bg-gradient-to-r from-brandPrimary-600 via-purple-600 to-brandPrimary-700 text-white'
  }
  
  const sizes = {
    sm: 'py-12',
    md: 'py-16',
    lg: 'py-20',
    xl: 'py-24'
  }
  
  return `relative overflow-hidden ${variants[props.variant]} ${sizes[props.size]}`
})

const contentClass = computed(() => {
  const alignments = {
    left: 'text-left max-w-4xl',
    center: 'text-center max-w-4xl mx-auto',
    right: 'text-right max-w-4xl ml-auto'
  }
  return alignments[props.textAlign]
})

const titleClass = computed(() => {
  const baseClass = 'font-extrabold leading-tight mb-6'
  const sizeClass = props.size === 'xl' ? 'text-4xl md:text-6xl' : 
                   props.size === 'lg' ? 'text-3xl md:text-5xl' :
                   props.size === 'md' ? 'text-2xl md:text-4xl' : 
                   'text-xl md:text-3xl'
  return `${baseClass} ${sizeClass}`
})

const subtitleClass = computed(() => {
  const baseClass = 'opacity-90 mb-8 max-w-3xl'
  const sizeClass = props.size === 'xl' ? 'text-xl md:text-2xl' :
                   props.size === 'lg' ? 'text-lg md:text-xl' :
                   'text-base md:text-lg'
  const alignClass = props.textAlign === 'center' ? 'mx-auto' :
                    props.textAlign === 'right' ? 'ml-auto' : ''
  return `${baseClass} ${sizeClass} ${alignClass}`
})

const featuresListClass = computed(() => {
  const baseClass = 'space-y-3'
  const layoutClass = props.textAlign === 'center' ? 'max-w-md mx-auto' : ''
  return `${baseClass} ${layoutClass}`
})

const checkIconClass = computed(() => {
  const colorClass = props.variant === 'light' ? 'text-green-600' : 'text-green-400'
  return `w-5 h-5 mr-3 flex-shrink-0 ${colorClass}`
})

const featureTextClass = computed(() => {
  const colorClass = props.variant === 'light' ? 'text-gray-700' : 'text-white/90'
  return `text-sm ${colorClass}`
})

const actionsClass = computed(() => {
  const baseClass = 'flex gap-4 mt-8'
  const alignClass = props.textAlign === 'center' ? 'justify-center' :
                    props.textAlign === 'right' ? 'justify-end' : 'justify-start'
  const responsiveClass = 'flex-col sm:flex-row'
  return `${baseClass} ${alignClass} ${responsiveClass}`
})

const additionalInfoClass = computed(() => {
  const baseClass = 'text-sm opacity-80'
  const alignClass = props.textAlign === 'center' ? 'text-center' :
                    props.textAlign === 'right' ? 'text-right' : 'text-left'
  return `${baseClass} ${alignClass}`
})
</script>

<style scoped>
@keyframes fadeInFromBottom {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  animation: fadeInFromBottom 0.8s ease-out forwards;
  opacity: 0;
}

.delay-200 { animation-delay: 0.2s; }
.delay-300 { animation-delay: 0.3s; }
.delay-400 { animation-delay: 0.4s; }
.delay-500 { animation-delay: 0.5s; }
.delay-600 { animation-delay: 0.6s; }
</style>
