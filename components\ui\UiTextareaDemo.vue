<template>
  <div class="max-w-4xl mx-auto p-8 space-y-8">
    <div class="text-center">
      <h1 class="text-3xl font-bold text-gray-900 mb-2">Enhanced UiTextarea Component</h1>
      <p class="text-lg text-gray-600">Comprehensive textarea component with advanced features</p>
    </div>

    <!-- Basic Examples -->
    <section class="space-y-6">
      <h2 class="text-2xl font-semibold text-gray-800 border-b pb-2">Basic Examples</h2>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- Default Textarea -->
        <div class="space-y-2">
          <h3 class="text-lg font-medium text-gray-700">Default</h3>
          <UiTextarea
            id="basic-textarea"
            v-model="basicValue"
            label="Description"
            placeholder="Enter your description here..."
            help-text="This is a basic textarea with label and help text"
          />
        </div>

        <!-- With Icons -->
        <div class="space-y-2">
          <h3 class="text-lg font-medium text-gray-700">With Icons</h3>
          <UiTextarea
            id="icon-textarea"
            v-model="iconValue"
            label="Comments"
            placeholder="Add your comments..."
            leading-icon="heroicons:chat-bubble-left-ellipsis"
            trailing-icon="heroicons:information-circle"
            help-text="Textarea with leading and trailing icons"
          />
        </div>
      </div>
    </section>

    <!-- Size Variants -->
    <section class="space-y-6">
      <h2 class="text-2xl font-semibold text-gray-800 border-b pb-2">Size Variants</h2>
      
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="space-y-2">
          <h3 class="text-lg font-medium text-gray-700">Small</h3>
          <UiTextarea
            id="small-textarea"
            v-model="smallValue"
            size="sm"
            label="Small Size"
            placeholder="Small textarea..."
            rows="3"
          />
        </div>

        <div class="space-y-2">
          <h3 class="text-lg font-medium text-gray-700">Medium (Default)</h3>
          <UiTextarea
            id="medium-textarea"
            v-model="mediumValue"
            size="md"
            label="Medium Size"
            placeholder="Medium textarea..."
            rows="3"
          />
        </div>

        <div class="space-y-2">
          <h3 class="text-lg font-medium text-gray-700">Large</h3>
          <UiTextarea
            id="large-textarea"
            v-model="largeValue"
            size="lg"
            label="Large Size"
            placeholder="Large textarea..."
            rows="3"
          />
        </div>
      </div>
    </section>

    <!-- Variants -->
    <section class="space-y-6">
      <h2 class="text-2xl font-semibold text-gray-800 border-b pb-2">Style Variants</h2>
      
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="space-y-2">
          <h3 class="text-lg font-medium text-gray-700">Default</h3>
          <UiTextarea
            id="default-variant"
            v-model="defaultVariantValue"
            variant="default"
            label="Default Variant"
            placeholder="Default style..."
          />
        </div>

        <div class="space-y-2">
          <h3 class="text-lg font-medium text-gray-700">Filled</h3>
          <UiTextarea
            id="filled-variant"
            v-model="filledVariantValue"
            variant="filled"
            label="Filled Variant"
            placeholder="Filled style..."
          />
        </div>

        <div class="space-y-2">
          <h3 class="text-lg font-medium text-gray-700">Outlined</h3>
          <UiTextarea
            id="outlined-variant"
            v-model="outlinedVariantValue"
            variant="outlined"
            label="Outlined Variant"
            placeholder="Outlined style..."
          />
        </div>
      </div>
    </section>

    <!-- Advanced Features -->
    <section class="space-y-6">
      <h2 class="text-2xl font-semibold text-gray-800 border-b pb-2">Advanced Features</h2>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- Character Count -->
        <div class="space-y-2">
          <h3 class="text-lg font-medium text-gray-700">Character Count</h3>
          <UiTextarea
            id="char-count-textarea"
            v-model="charCountValue"
            label="Limited Text"
            placeholder="Type something..."
            :maxlength="100"
            show-char-count
            help-text="Maximum 100 characters allowed"
          />
        </div>

        <!-- Clearable -->
        <div class="space-y-2">
          <h3 class="text-lg font-medium text-gray-700">Clearable</h3>
          <UiTextarea
            id="clearable-textarea"
            v-model="clearableValue"
            label="Clearable Text"
            placeholder="Type to see clear button..."
            clearable
            help-text="Clear button appears when text is entered"
          />
        </div>

        <!-- Auto Resize -->
        <div class="space-y-2">
          <h3 class="text-lg font-medium text-gray-700">Auto Resize</h3>
          <UiTextarea
            id="auto-resize-textarea"
            v-model="autoResizeValue"
            label="Auto Resize"
            placeholder="Start typing to see auto-resize..."
            auto-resize
            :rows="2"
            help-text="Textarea grows automatically with content"
          />
        </div>

        <!-- Resize Options -->
        <div class="space-y-2">
          <h3 class="text-lg font-medium text-gray-700">Resize Control</h3>
          <UiTextarea
            id="resize-textarea"
            v-model="resizeValue"
            label="Manual Resize"
            placeholder="You can resize this manually..."
            resize="both"
            help-text="Drag the corner to resize manually"
          />
        </div>
      </div>
    </section>

    <!-- States -->
    <section class="space-y-6">
      <h2 class="text-2xl font-semibold text-gray-800 border-b pb-2">States</h2>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- Error State -->
        <div class="space-y-2">
          <h3 class="text-lg font-medium text-gray-700">Error State</h3>
          <UiTextarea
            id="error-textarea"
            v-model="errorValue"
            label="Required Field"
            placeholder="This field has an error..."
            error-message="This field is required and cannot be empty"
            required
          />
        </div>

        <!-- Success State -->
        <div class="space-y-2">
          <h3 class="text-lg font-medium text-gray-700">Success State</h3>
          <UiTextarea
            id="success-textarea"
            v-model="successValue"
            label="Validated Field"
            placeholder="This field is valid..."
            success-message="Great! Your input looks good"
            show-success
          />
        </div>

        <!-- Disabled State -->
        <div class="space-y-2">
          <h3 class="text-lg font-medium text-gray-700">Disabled State</h3>
          <UiTextarea
            id="disabled-textarea"
            v-model="disabledValue"
            label="Disabled Field"
            placeholder="This field is disabled..."
            disabled
            help-text="This field cannot be edited"
          />
        </div>

        <!-- Readonly State -->
        <div class="space-y-2">
          <h3 class="text-lg font-medium text-gray-700">Readonly State</h3>
          <UiTextarea
            id="readonly-textarea"
            v-model="readonlyValue"
            label="Readonly Field"
            readonly
            help-text="This field is readonly"
          />
        </div>
      </div>
    </section>

    <!-- Loading State -->
    <section class="space-y-6">
      <h2 class="text-2xl font-semibold text-gray-800 border-b pb-2">Loading State</h2>
      
      <div class="max-w-md">
        <UiTextarea
          id="loading-textarea"
          v-model="loadingValue"
          label="Processing..."
          placeholder="Content is being processed..."
          loading
          help-text="Loading spinner is shown"
        />
      </div>
    </section>

    <!-- Form Integration -->
    <section class="space-y-6">
      <h2 class="text-2xl font-semibold text-gray-800 border-b pb-2">Form Integration</h2>
      
      <form @submit.prevent="handleSubmit" class="max-w-2xl space-y-4">
        <UiTextarea
          id="form-textarea"
          v-model="formValue"
          label="Message"
          placeholder="Enter your message..."
          required
          :maxlength="500"
          show-char-count
          clearable
          help-text="Please provide detailed information"
        />
        
        <div class="flex gap-4">
          <button
            type="submit"
            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            Submit
          </button>
          <button
            type="button"
            @click="resetForm"
            class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors"
          >
            Reset
          </button>
        </div>
      </form>
    </section>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

// Basic examples
const basicValue = ref('');
const iconValue = ref('');

// Size variants
const smallValue = ref('');
const mediumValue = ref('');
const largeValue = ref('');

// Style variants
const defaultVariantValue = ref('');
const filledVariantValue = ref('');
const outlinedVariantValue = ref('');

// Advanced features
const charCountValue = ref('');
const clearableValue = ref('Type something to see the clear button');
const autoResizeValue = ref('');
const resizeValue = ref('');

// States
const errorValue = ref('');
const successValue = ref('This is a valid input');
const disabledValue = ref('This field is disabled');
const readonlyValue = ref('This content is readonly and cannot be modified');

// Loading state
const loadingValue = ref('');

// Form integration
const formValue = ref('');

const handleSubmit = () => {
  alert(`Form submitted with value: ${formValue.value}`);
};

const resetForm = () => {
  formValue.value = '';
};
</script>
