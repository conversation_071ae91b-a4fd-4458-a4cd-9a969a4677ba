<template>
  <MenuItem v-slot="{ active }">
    <div :class="[
      active ? 'bg-gray-50 text-gray-900' : 'text-gray-700',
      'block px-3 py-2 text-sm rounded-lg mx-1 my-0.5 transition-all duration-150 ease-out',
      'hover:bg-gray-50 cursor-pointer'
    ]">
      <slot></slot>
    </div>
  </MenuItem>
</template>

<script setup lang="ts">
import { MenuItem } from '@headlessui/vue';
// No props needed for a basic item, as it primarily relies on Headless UI's styling
</script>

<style scoped>
/* Tailwind classes handled by parent UiDropdown and Headless UI */
</style>