<template>
  <div class="space-y-6">
    <UiCard title="Global Alert Configuration">
      <div class="space-y-4">
        <div>
          <div class="flex items-center justify-between">
            <span class="flex-grow text-sm font-medium text-gray-700 dark:text-gray-300">Enable Global System Alerts</span>
            <UiToggle v-model="form.enableGlobalAlerts" @update:modelValue="updateSetting('enableGlobalAlerts', $event)" label="Enable global alerts" :hide-label="true" />
          </div>
          <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">Determines if critical system-wide alerts are displayed to relevant users.</p>
        </div>
        <div>
          <UiInput name="adminNotificationEmail" type="email" id="adminNotificationEmail" placeholder="<EMAIL>"
            label="Admin Notification Email" v-model="form.adminNotificationEmail"
            @update:modelValue="updateSetting('adminNotificationEmail', $event)" />
          <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">Email address for receiving critical platform notifications.</p>
        </div>
      </div>
    </UiCard>

    <UiCard title="Default Notification Delivery & Presentation">
      <div class="space-y-4">
        <div>
          <div class="flex items-center justify-between">
            <span class="flex-grow text-sm font-medium text-gray-700 dark:text-gray-300">Enable Notification Sounds by Default</span>
            <UiToggle v-model="form.defaultEnableSounds" @update:modelValue="updateSetting('defaultEnableSounds', $event)" label="Enable sounds" :hide-label="true" />
          </div>
        </div>
        <div v-if="form.defaultEnableSounds">
          <UiSelect label="Default Notification Sound" id="defaultNotificationSound" :name="'defaultNotificationSound'" v-model="form.defaultNotificationSound" :options="notificationSoundOptions" @update:modelValue="updateSetting('defaultNotificationSound', $event)" class="mt-1"></UiSelect>
        </div>
        <div>
          <label for="autoDismissDuration" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Auto-Dismiss Non-Critical Notifications After (seconds)</label>
          <UiInput id="autoDismissDuration" :name="'autoDismissDuration'" v-model.number="form.autoDismissDuration" type="number" placeholder="e.g., 10 (0 for manual)" @update:modelValue="updateSetting('autoDismissDuration', $event)" class="mt-1" />
        </div>
        <div>
          <div class="flex items-center justify-between">
            <span class="flex-grow text-sm font-medium text-gray-700 dark:text-gray-300">Show Unread Notification Count Badge</span>
            <UiToggle v-model="form.showUnreadCountBadge" @update:modelValue="updateSetting('showUnreadCountBadge', $event)" label="Show unread badge" :hide-label="true" />
          </div>
        </div>
      </div>
    </UiCard>

    <UiCard title="Default 'Do Not Disturb' / Quiet Hours">
      <div class="space-y-4">
        <div>
          <div class="flex items-center justify-between">
            <span class="flex-grow text-sm font-medium text-gray-700 dark:text-gray-300">Enable Default Quiet Hours for New Users</span>
            <UiToggle v-model="form.defaultEnableQuietHours" @update:modelValue="updateSetting('defaultEnableQuietHours', $event)" label="Enable quiet hours" :hide-label="true" />
          </div>
          <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">Users can override these settings in their personal notification preferences.</p>
        </div>
        <div v-if="form.defaultEnableQuietHours" class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label for="quietHoursStartTime" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Quiet Hours Start Time</label>
            <UiInput id="quietHoursStartTime" :name="'quietHoursStartTime'" v-model="form.quietHoursStartTime" type="time" @update:modelValue="updateSetting('quietHoursStartTime', $event)" class="mt-1" />
          </div>
          <div>
            <label for="quietHoursEndTime" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Quiet Hours End Time</label>
            <UiInput id="quietHoursEndTime" :name="'quietHoursEndTime'" v-model="form.quietHoursEndTime" type="time" @update:modelValue="updateSetting('quietHoursEndTime', $event)" class="mt-1" />
          </div>
        </div>
      </div>
    </UiCard>

    <UiCard title="General Platform Announcements (User Defaults)">
      <div class="space-y-2">
        <p class="text-sm text-gray-500 dark:text-gray-400">Controls default opt-in for various platform-wide informational messages. Users can override these.</p>
        <div class="flex items-center justify-between pt-2">
          <span class="flex-grow text-sm font-medium text-gray-700 dark:text-gray-300">Receive "New Feature" Announcements by Default</span>
          <UiToggle v-model="form.defaultReceiveNewFeatures" @update:modelValue="updateSetting('defaultReceiveNewFeatures', $event)" label="New features" :hide-label="true" />
        </div>
        <div class="flex items-center justify-between pt-2">
          <span class="flex-grow text-sm font-medium text-gray-700 dark:text-gray-300">Receive "Platform Maintenance" Notifications by Default</span>
          <UiToggle v-model="form.defaultReceiveMaintenance" @update:modelValue="updateSetting('defaultReceiveMaintenance', $event)" label="Maintenance" :hide-label="true" />
        </div>
        <div class="flex items-center justify-between pt-2">
          <span class="flex-grow text-sm font-medium text-gray-700 dark:text-gray-300">Receive "Tips & Tricks" / "Usage Guides" by Default</span>
          <UiToggle v-model="form.defaultReceiveTips" @update:modelValue="updateSetting('defaultReceiveTips', $event)" label="Tips & tricks" :hide-label="true" />
        </div>
      </div>
    </UiCard>

  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useApi } from '~/composables/useApi';
import { useToast } from '~/composables/useToast';
import UiCard from '~/components/ui/UiCard.vue';
import UiInput from '~/components/ui/UiInput.vue';
import UiToggle from '~/components/ui/UiToggle.vue';
import UiSelect from '~/components/ui/UiSelect.vue';


const { get, patch } = useApi();
const { showToast } = useToast();

const form = ref<{
  enableGlobalAlerts: boolean;
  adminNotificationEmail: string;
  // Delivery & Presentation
  defaultEnableSounds: boolean;
  defaultNotificationSound: string;
  autoDismissDuration: number;
  showUnreadCountBadge: boolean;
  // Quiet Hours
  defaultEnableQuietHours: boolean;
  quietHoursStartTime: string; // HH:mm format
  quietHoursEndTime: string;   // HH:mm format
  // Announcements
  defaultReceiveNewFeatures: boolean;
  defaultReceiveMaintenance: boolean;
  defaultReceiveTips: boolean;
}>({
  enableGlobalAlerts: true,
  adminNotificationEmail: '',
  // Defaults for Delivery & Presentation
  defaultEnableSounds: true,
  defaultNotificationSound: 'default_beep',
  autoDismissDuration: 10,
  showUnreadCountBadge: true,
  // Defaults for Quiet Hours
  defaultEnableQuietHours: false,
  quietHoursStartTime: '22:00',
  quietHoursEndTime: '08:00',
  // Defaults for Announcements
  defaultReceiveNewFeatures: true,
  defaultReceiveMaintenance: true,
  defaultReceiveTips: false,
});

const notificationSoundOptions = ref([
  { label: 'Default Beep', value: 'default_beep' },
  { label: 'Subtle Chime', value: 'subtle_chime' },
  { label: 'Alert Tone 1', value: 'alert_tone_1' },
  { label: 'None', value: 'none' },
]);

onMounted(async () => {
  await fetchSettings();
});

const fetchSettings = async () => {
  const settingsToFetch = [
    'enableGlobalAlerts', 'adminNotificationEmail',
    'defaultEnableSounds', 'defaultNotificationSound', 'autoDismissDuration', 'showUnreadCountBadge',
    'defaultEnableQuietHours', 'quietHoursStartTime', 'quietHoursEndTime',
    'defaultReceiveNewFeatures', 'defaultReceiveMaintenance', 'defaultReceiveTips'
  ];
  for (const key of settingsToFetch) {
    try {
      const settingValue = await get(`/settings/${key}`);
      if (settingValue !== undefined && settingValue !== null) {
        (form.value as any)[key] = settingValue;
      }
    } catch (error) {
      console.error(`Failed to fetch platform notification setting for ${key}:`, error);
    }
  }
};

const updateSetting = async (key: string, value: any) => {
  try {
    const payloadValue = typeof value === 'boolean' ? value : (value === undefined ? null : value) ;
    await patch(`/settings/${key}`, { value: payloadValue });
    showToast({
      title: 'Success',
      message: `Successfully updated ${key}.`,
      type: 'success'
    });
  } catch (error: any) {
    console.error(`Failed to update notification setting ${key}:`, error);
    showToast({
      title: 'Error',
      message: `Failed to update ${key}. Please try again.`,
      type: 'error'
    });
  }
};
</script>