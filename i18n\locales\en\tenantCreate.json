{"pageTitle": "Create New Tenant", "pageDescription": "Set up a new tenant with configuration and initial settings", "backToTenants": "Back to Tenants", "preview": "Preview", "resetForm": "Reset Form", "cancel": "Cancel", "createTenant": "Create Tenant", "formTitle": "Tenant Information", "formDescription": "Configure the new tenant with all necessary details and settings.", "errorTitle": "Creation Failed", "errorMessage": "Please review the form and try again.", "successTitle": "Tenant Created", "successMessage": "Tenant has been created successfully.", "validationErrors": {"networkError": "Network error occurred. Please check your connection and try again.", "serverError": "Server error occurred. Please try again later.", "duplicateSlug": "This tenant slug is already taken. Please choose a different one.", "invalidData": "Please check all required fields and try again.", "logoUploadFailed": "Logo upload failed. Please try again with a different image.", "slugValidationFailed": "Unable to validate slug uniqueness. Please try again."}, "groups": {"basicInformation": "Basic Information", "branding": "Branding", "localization": "Localization", "planSubscription": "Plan & Subscription", "initialSettings": "Initial Settings"}, "fields": {"tenantName": {"label": "Tenant Name", "placeholder": "Enter tenant name", "validation": {"required": "Tenant name is required", "minLength": "Tenant name must be at least 2 characters", "maxLength": "Tenant name cannot exceed 100 characters"}}, "tenantSlug": {"label": "Tenant Slug", "placeholder": "Enter tenant slug (e.g., acme-corp)", "hint": "Used in URLs and API endpoints. Only lowercase letters, numbers, and hyphens allowed.", "validation": {"required": "Tenant slug is required", "minLength": "Slug must be at least 2 characters", "maxLength": "Slug cannot exceed 50 characters", "pattern": "Slug can only contain lowercase letters, numbers, and hyphens"}}, "tenantLogo": {"label": "Tenant <PERSON>", "hint": "Upload a logo for the tenant. Recommended size: 200x200px, max 2MB. Supported formats: JPEG, PNG, GIF, WebP.", "uploadText": "Drop logo here or click to browse", "supportText": "JPEG, PNG, GIF, WebP up to 2MB"}, "defaultLanguage": {"label": "Default Language", "validation": {"required": "Default language is required"}}, "defaultTimezone": {"label": "Default Timezone", "validation": {"required": "Default timezone is required"}}, "subscriptionPlan": {"label": "Subscription Plan", "validation": {"required": "Subscription plan is required"}}, "activateImmediately": {"label": "Activate tenant immediately", "hint": "When enabled, the tenant will be active and users can access the platform immediately."}, "enableApiAccess": {"label": "Enable API access", "hint": "Allow this tenant to access the platform API."}, "enableAuditLog": {"label": "Enable audit logging", "hint": "Track user actions and system changes for this tenant."}, "enableNotifications": {"label": "Enable email notifications", "hint": "Send email notifications for important events."}, "seedDefaultData": {"label": "Seed with default data", "hint": "Create default roles, templates, and sample data for quick setup."}}, "localeOptions": {"en": "English", "es": "Spanish", "fr": "French", "de": "German", "it": "Italian", "pt": "Portuguese", "he": "Hebrew", "ar": "Arabic"}, "timezoneOptions": {"UTC": "Coordinated Universal Time (UTC)", "America/New_York": "Eastern Time (ET)", "America/Chicago": "Central Time (CT)", "America/Denver": "Mountain Time (MT)", "America/Los_Angeles": "Pacific Time (PT)", "Europe/London": "Greenwich Mean Time (GMT)", "Europe/Paris": "Central European Time (CET)", "Asia/Tokyo": "Japan Standard Time (JST)", "Asia/Shanghai": "China Standard Time (CST)", "Australia/Sydney": "Australian Eastern Time (AET)"}, "planOptions": {"basic": {"label": "Basic Plan", "description": "Up to 5 users, basic features"}, "professional": {"label": "Professional Plan", "description": "Up to 25 users, advanced features"}, "enterprise": {"label": "Enterprise Plan", "description": "Unlimited users, all features"}, "custom": {"label": "Custom Plan", "description": "Tailored solution"}}, "breadcrumbs": {"dashboard": "Dashboard", "tenants": "Tenants", "create": "Create"}}