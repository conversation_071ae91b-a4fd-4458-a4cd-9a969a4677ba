<template>
  <div class="space-y-6 py-6">
 
    <UiCard v-if="currentView == 'table'">
      <UiTable
        :headers="tableHeaders"
        :items="tenantStore.getTenants"
        :loading="tenantStore.isLoading"
        :selectable="true"
        :expandable="true"
        item-key="id"
        @sort="handleSort"
        @selection-change="handleSelectionChange"
      >
        <template #item.name="{ item }">
          <div class="flex items-center gap-3">
            <div class="flex-shrink-0">
              <img
                v-if="item.logoUrl"
                :src="item.logoUrl"
                :alt="`${item.name} logo`"
                class="w-10 h-10 rounded-full object-cover"
              />
              <div
                v-else
                class="w-10 h-10 rounded-full bg-brandPrimary/10 flex items-center justify-center"
              >
                <Icon
                  name="material-symbols:business"
                  class="h-5 w-5 text-brandPrimary"
                />
              </div>
            </div>
            <div>
              <p class="font-medium text-gray-900 dark:text-white">{{ item.name }}</p>
              <p class="text-sm text-gray-500 dark:text-gray-400">{{ item.slug }}</p>
            </div>
          </div>
        </template>
        <template #expanded-row="{ item }">
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <!-- Tenant Details -->
            <div class="space-y-3">
              <h4 class="text-sm font-semibold text-gray-900 dark:text-white">
                Tenant Details
              </h4>
              <div class="space-y-2 text-sm">
                <div class="flex justify-between">
                  <span class="text-gray-500 dark:text-gray-400">ID:</span>
                  <span class="text-gray-900 dark:text-white font-mono">{{
                    item.id
                  }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-500 dark:text-gray-400">Locale:</span>
                  <span class="text-gray-900 dark:text-white">{{
                    item.locale || "en-US"
                  }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-500 dark:text-gray-400">Timezone:</span>
                  <span class="text-gray-900 dark:text-white">{{
                    item.timezone || "UTC"
                  }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-500 dark:text-gray-400">Last Updated:</span>
                  <span class="text-gray-900 dark:text-white">{{
                    formatDate(item.updatedAt)
                  }}</span>
                </div>
              </div>
            </div>

            <!-- Usage Statistics -->
            <div class="space-y-3">
              <h4 class="text-sm font-semibold text-gray-900 dark:text-white">
                Usage Statistics
              </h4>
              <div class="space-y-3">
                <div>
                  <div class="flex justify-between text-sm mb-1">
                    <span class="text-gray-500 dark:text-gray-400">Storage Used</span>
                    <span class="text-gray-900 dark:text-white">
                      {{ item?.usageStatistics?.storageUsed }}</span
                    >
                  </div>
                  <div class="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2">
                    <div
                      class="bg-blue-500 h-2 rounded-full"
                      :style="{
                        width: `${percentage(
                          item?.usageStatistics?.storageUsedGB,
                          item?.usageStatistics?.storageLimitGB
                        )}%`,
                      }"
                    ></div>
                  </div>
                </div>
                <div>
                  <div class="flex justify-between text-sm mb-1">
                    <span class="text-gray-500 dark:text-gray-400">API Calls</span>
                    <span class="text-gray-900 dark:text-white">{{
                      item?.usageStatistics?.apiCalls
                    }}</span>
                  </div>
                  <div class="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2">
                    <div
                      class="bg-green-500 h-2 rounded-full"
                      :style="{
                        width: `${percentage(
                          item?.usageStatistics?.apiCallsUsed,
                          item?.usageStatistics?.apiCallsLimit
                        )}%`,
                      }"
                    ></div>
                  </div>
                </div>
                <div>
                  <div class="flex justify-between text-sm mb-1">
                    <span class="text-gray-500 dark:text-gray-400">Documents</span>
                    <span class="text-gray-900 dark:text-white">{{
                      item?.usageStatistics?.documents
                    }}</span>
                  </div>
                  <div class="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2">
                    <div
                      class="bg-yellow-500 h-2 rounded-full"
                      :style="{
                        width: `${percentage(
                          item?.usageStatistics?.documentsUsed,
                          item?.usageStatistics?.documentsLimit
                        )}%`,
                      }"
                    ></div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Quick Actions -->
            <div class="space-y-3">
              <h4 class="text-sm font-semibold text-gray-900 dark:text-white">
                Quick Actions
              </h4>
              <div class="grid grid-cols-2 gap-2">
                <UiButton
                  @click="handleTenantAction({ action: 'view', item })"
                  size="sm"
                  variant="outline"
                  class="w-full"
                >
                  <Icon
                    name="material-symbols:visibility"
                    size="calc(var(--spacing) * 5)"
                    class="mr-1"
                  />
                  View
                </UiButton>
                <UiButton
                  @click="handleTenantAction({ action: 'edit', item })"
                  size="sm"
                  variant="outline"
                  class="w-full"
                >
                  <Icon
                    name="material-symbols:edit"
                    size="calc(var(--spacing) * 5)"
                    class="mr-1"
                  />
                  Edit
                </UiButton>
                <UiButton
                  @click="handleTenantAction({ action: 'usage', item })"
                  size="sm"
                  variant="outline"
                  class="w-full"
                >
                  <Icon
                    name="material-symbols:analytics"
                    size="calc(var(--spacing) * 5)"
                    class="mr-1"
                  />
                  Usage
                </UiButton>
                <UiButton
                  @click="handleTenantAction({ action: 'invite', item })"
                  size="sm"
                  variant="outline"
                  class="w-full"
                >
                  <Icon
                    name="material-symbols:person-add"
                    size="calc(var(--spacing) * 5)"
                    class="mr-1"
                  />
                  Invite
                </UiButton>
              </div>

              <!-- Danger Zone -->
              <div class="pt-3 border-t border-gray-200 dark:border-gray-600">
                <UiButton
                  @click="
                    handleTenantAction({
                      action: item.isActive ? 'deactivate' : 'activate',
                      item,
                    })
                  "
                  size="sm"
                  :variant="item.isActive ? 'outline' : 'primary'"
                  :class="
                    item.isActive
                      ? 'text-red-600 border-red-300 hover:bg-red-50'
                      : 'text-green-600 border-green-300 hover:bg-green-50'
                  "
                  class="w-full"
                >
                  <Icon
                    :name="
                      item.isActive
                        ? 'material-symbols:pause'
                        : 'material-symbols:play-arrow'
                    "
                    size="calc(var(--spacing) * 5)"
                    class="mr-1"
                  />
                  {{ item.isActive ? "Deactivate" : "Activate" }}
                </UiButton>
              </div>
            </div>
          </div>
        </template>
      </UiTable>
    </UiCard>

     <!-- Cards View -->
      <div v-else-if="currentView === 'cards'">
        <UiCardsGrid
          :items="tenantStore.getTenants"
          :loading="tenantStore.isLoading"
          :card-config="cardConfig"
          item-key="id"
          empty-state-title="No tenants found"
          empty-state-description="Get started by creating your first tenant to manage your platform."
          empty-state-icon="material-symbols:business"
          empty-action-label="Create Tenant"
          default-icon="material-symbols:business"
          @item-action="handleTenantAction"
          @empty-action="handleCreateTenant"
        />
      </div>

    <UiPagination
      v-if="tenantStore.tenantsMeta && tenantStore.tenantsMeta.total > 0"
      :current-page="parseInt(query.page)"
      :total-pages="tenantStore.tenantsMeta.totalPages"
      :total-items="tenantStore.tenantsMeta.total"
      :items-per-page="parseInt(query.limit)"
      @page-change="handlePageChange"
    />
  
    
  </div>
</template>
<script setup lang="ts">

import { useTenantStore } from '~/stores/tenant';
import { useQuery } from '~/composables/useQuery';
import { onMounted, onUnmounted } from 'vue';
import { PlatformRoles } from "~/app/features/auth/constants/roles";
import type { Tenant } from '~/types/tenant';
 

const { $global } = useNuxtApp()
const { on, off } = useEventListeners();

const  currentView = ref('table');


definePageMeta({
  layout: "dashboard",
  middleware: ["rbac"],
  roles: [PlatformRoles.SUPER_ADMIN],
  breadcrumbs: [
    { label: "Dashboard", href: "/dashboard" },
    { label: "Tenants", href: "/dashboard/tenants" },
    { label: "Manage" },
  ],
  showPageHeader: true,
  title: 'Manage Tenants',
  showPageHeaderTitle: false,
  pageHeaderActions: () => {
    return [
      {
        label: 'Create Tenant',
        icon: 'i-mdi:office-building-plus-outline',
        color: 'success',
        variant: 'outline',

        click: () => navigateTo('/dashboard/tenants/create')
      },
       {
        label: 'Invite Users',
        icon: 'i-heroicons:user-plus',
         variant: 'outline',
        color: 'info',
        click: () => navigateTo('/dashboard/tenants/invite'),
      },
      {
        label: 'Refresh',
        icon: 'i-mdi:refresh',
        variant: 'outline',
        color: 'secondary',
        disabled: () => (useTenantStore().isLoading || useTenantStore().isRefreshing),
        loading:  () => (useTenantStore().isLoading || useTenantStore().isRefreshing),
        click: () => {
          const { $global } =  useNuxtApp()
          if ($global.get('refreshData')) {
            $global.get('refreshData')()
          }

        }
      },
     
    ].reverse();
  },
  showActionsMenu: false,
  actionsMenu: () => {

    return [
      {
        key: 'export',
        label: 'Export All Data',
        icon: 'material-symbols-light:export-notes-outline-sharp'
      },
     
      {
        key: 'import',
        label: 'Bulk Import',
        icon: 'material-symbols-light:add-notes-outline-rounded'
      },
       { type: 'divider' },
       {
        key: 'settings',
        label: 'Settings',
        icon: 'material-symbols-light:settings-outline'
       },
       {
        key: 'support',
        label: 'Help & Support',
        icon: 'material-symbols-light:help-outline-rounded'
       }
    ];
  },
  showViewToggle: true,
   // Filter configuration
  showFilters: true,
  filterConfigs: [
    {
      key: 'isActive',
      options: [
        { label: 'All Status', value: '' },
        { label: 'Active', value: true },
        { label: 'Inactive', value: false }
      ],
      placeholder: 'Status'
    },
    {
      key: 'plan',
      options: [
        { label: 'All Plans', value: '' },
        { label: 'Basic', value: 'basic' },
        { label: 'Pro', value: 'pro' },
        { label: 'Enterprise', value: 'enterprise' }
      ],
      placeholder: 'Plans'
    },
    {
      key: 'sort',
      type: 'select',
      placeholder: 'Sort By',
      options: [
        { label: 'Default', value: '' },
        { label: 'Name A-Z', value: 'name:asc' },
        { label: 'Name Z-A', value: 'name:desc' },
        { label: 'Newest First', value: 'created:desc' },
        { label: 'Oldest First', value: 'created:asc' }
      ]
    }
  ],
  advancedFilterConfigs: [
    {
      key: 'createdAt',
      type: 'dateRange',
      label: 'Created Date Range'
    }
  ],
  initialFilters: {},
  showAdvancedFilters: true,
  compactFilters: true

 
});

const { formatDate } = useDateFormatters();
const { getStorageUsagePercentage: percentage } = useStorageFormatters();
const tenantStore = useTenantStore();
const fetchTenants = async (q: any) => {
  try {
   
    
    const parsedQuery = JSON.parse(JSON.stringify(q));
    if (parsedQuery.sort.length > 0) {
      parsedQuery.sort = parsedQuery.sort.map((s: any) => `${s.key}:${s.direction.toUpperCase()}`).join(',');
    }
    console.log(parsedQuery);
    
    await tenantStore.fetchAllTenants(parsedQuery, 'manage.vue');
  } catch (error) {
    console.error("Error fetching tenants:", error);
  }
};

const { state: query, setQuery, setQueryDebounced, querySync} = useQuery({
  page: 1,
  limit: 10,
  search: "",
  sort: [],
},{
  debounceDelay: 500,
  history: 'push',
  onQuerySynced: ({ state }: { state: any }) => {
    fetchTenants(state);
  },
});

const tableHeaders = [
  { key: "name", label: "Name", sortable: true },
  { key: "plan", label: "Plan", sortable: true },
  { key: "status", label: "Status", sortable: true },
  { key: "locale", label: "Locale", sortable: true },
  { key: "timezone", label: "Timezone", sortable: true },
  {
    key: "createdAt",
    label: "Created",
    sortable: true,
    format: (value: string) => formatDate(value),
  },
];



// Cards configuration
const cardConfig = computed(() => ({
  title: (tenant: Tenant) => tenant.name,
  subtitle: (tenant: Tenant) => tenant.slug,
  image: (tenant: Tenant) => tenant.logoUrl,
  badges: (tenant: Tenant) => [
    {
      text: tenant.plan,
      variant: 'info'
    },
    {
      text: tenant.isActive ? 'Active' : 'Inactive',
      variant: tenant.isActive ? 'success' : 'error'
    }
  ],
  metadata: (tenant: Tenant) => [
    {
      label: 'Locale',
      value: tenant.locale,
      icon: 'material-symbols:language'
    },
    {
      label: 'Timezone',
      value: tenant.timezone,
      icon: 'material-symbols:schedule'
    },
    {
      label: 'Created',
      value: formatDate(tenant.createdAt),
      icon: 'material-symbols:calendar-today'
    }
  ],
  actions: [
    {
      key: 'view',
      label: 'View',
      icon: 'material-symbols:visibility',
      variant: 'primary'
    },
    {
      key: 'edit',
      label: 'Edit',
      icon: 'material-symbols:edit',
      variant: 'secondary'
    },
    {
      key: 'usage',
      label: 'Usage',
      icon: 'material-symbols:analytics',
      variant: 'outline'
    }
  ],
  menuActions: [
    {
      key: 'invite',
      label: 'Invite Users',
      icon: 'material-symbols:person-add'
    },
    {
      key: 'activate',
      label: 'Activate',
      icon: 'material-symbols:play-arrow',
      condition: (tenant: Tenant) => !tenant.isActive
    },
    {
      key: 'deactivate',
      label: 'Deactivate',
      icon: 'material-symbols:pause',
      condition: (tenant: Tenant) => tenant.isActive
    },
    {
      key: 'delete',
      label: 'Delete',
      icon: 'material-symbols:delete',
      destructive: true
    }
  ]
}))


const handleSort = (sortConfig: { key: string; direction: "asc" | "desc" }) => {
  setQuery({ sort: !!sortConfig.key ? [sortConfig] : [] });
};

const handleSelectionChange = (selectedItems: Tenant[]) => {
  // Handle table selection change
  console.log("Selection changed:", selectedItems);
};
const handlePageChange = (page: number) => {
  setQuery({ page: page }, { history: 'push' });
};


const handleCreateTenant = () => {
  navigateTo("/dashboard/tenants/create");
};

const handleTenantAction = (payload: any) => {
  // Handle both table and cards action formats
  const action = payload.action || payload.key;
  const tenant = payload.tenant || payload.item || payload.row;

  if (!tenant || !action) {
    console.error("Invalid action payload:", payload);
    return;
  }

  switch (action) {
    case "view":
      navigateTo(`/dashboard/tenants/${tenant.id}`);
      break;
    case "edit":
      navigateTo(`/dashboard/tenants/${tenant.id}/edit`);
      break;
    case "usage":
      navigateTo(`/dashboard/tenants/${tenant.id}/usage`);
      break;
    case "invite":
     navigateTo(`/dashboard/tenants/${tenant.id}/invite`);
      break;
    case "delete":
      // handleDeleteTenant(tenant)
      break;
    case "activate":
      // handleToggleTenantStatus(tenant, true)
      break;
    case "deactivate":
      // handleToggleTenantStatus(tenant, false)
      break;
    default:
      console.warn("Unknown action:", action);
  }
};
const handleSearch = ({value}: any) => {
  setQueryDebounced({ search: value }, { resetPage: true, history: 'push' });
};

const handleViewChange = (e: any) => {
  currentView.value = e.view;
};

const handleFiltersChanged = (e: any) => {
  const filtersKeys  = Object.keys(e).filter((key) => key !== 'timestamp');
  for (const key of filtersKeys) {
     if (key === 'sort') {
      const [sortKey, sortDirection] = !!e[key] ? e[key].split(':') : ['', ''];
      const sortConfig: { key: string; direction: "asc" | "desc" } = { key: sortKey, direction: sortDirection as "asc" | "desc" };
      handleSort(sortConfig);
    } else {
      setQuery({ [key]: e[key] }, { resetPage: true });
    }
  }
 
};

// Lifecycle hooks
onMounted(async () => {
  $global.add('refreshData', () => fetchTenants(query))
  on('global:search', handleSearch)
  on('sc:action:refresh', querySync)
  on('view-changed', handleViewChange)
  on('filters-change', handleFiltersChanged)
});
onUnmounted(() => {
  $global.remove('refreshData', fetchTenants)
  off('global:search', handleSearch)
  off('sc:action:refresh', querySync)
  off('view-changed', handleViewChange)
  off('filters-change', handleFiltersChanged)
});
</script>
