<template>
  <div class="nuxt-charts-examples space-y-8">
    <!-- Line Chart Examples -->
    <div class="example-section">
      <h3 class="text-lg font-semibold mb-4">Line Chart Examples</h3>
      
      <div class="bg-white dark:bg-gray-800 p-6 rounded-lg border mb-6">
        <h4 class="text-md font-medium mb-4">Basic Line Chart</h4>
        <UiLineChart
          :data="lineChartData"
          :height="300"
          y-label="Sales"
          :x-num-ticks="4"
          :y-num-ticks="4"
          :categories="lineChartCategories"
          :x-formatter="lineChartXFormatter"
          :y-grid-line="true"
          curve-type="linear"
          legend-position="top"
          :hide-legend="false"
        />
      </div>

      <!-- Code Example -->
      <details class="mt-4">
        <summary class="cursor-pointer text-sm font-medium text-gray-600 dark:text-gray-400">
          Show Code
        </summary>
        <pre class="mt-2 p-4 bg-gray-100 dark:bg-gray-900 rounded text-sm overflow-x-auto"><code>&lt;UiLineChart
  :data="chartData"
  :height="300"
  y-label="Sales"
  :x-num-ticks="4"
  :y-num-ticks="4"
  :categories="categories"
  :x-formatter="xFormatter"
  :y-grid-line="true"
  curve-type="linear"
  legend-position="top"
  :hide-legend="false"
/&gt;</code></pre>
      </details>
    </div>

    <!-- Area Chart Examples -->
    <div class="example-section">
      <h3 class="text-lg font-semibold mb-4">Area Chart Examples</h3>
      
      <div class="bg-white dark:bg-gray-800 p-6 rounded-lg border mb-6">
        <h4 class="text-md font-medium mb-4">Smooth Area Chart</h4>
        <UiAreaChart
          :data="areaChartData"
          :height="300"
          :categories="areaChartCategories"
          :y-grid-line="true"
          :x-formatter="areaChartXFormatter"
          curve-type="monotoneX"
          legend-position="top"
          :hide-legend="false"
        />
      </div>

      <!-- Code Example -->
      <details class="mt-4">
        <summary class="cursor-pointer text-sm font-medium text-gray-600 dark:text-gray-400">
          Show Code
        </summary>
        <pre class="mt-2 p-4 bg-gray-100 dark:bg-gray-900 rounded text-sm overflow-x-auto"><code>&lt;UiAreaChart
  :data="areaChartData"
  :height="300"
  :categories="categories"
  :y-grid-line="true"
  :x-formatter="xFormatter"
  curve-type="monotoneX"
  legend-position="top"
  :hide-legend="false"
/&gt;</code></pre>
      </details>
    </div>

    <!-- Bar Chart Examples -->
    <div class="example-section">
      <h3 class="text-lg font-semibold mb-4">Bar Chart Examples</h3>
      
      <div class="bg-white dark:bg-gray-800 p-6 rounded-lg border mb-6">
        <h4 class="text-md font-medium mb-4">Revenue Bar Chart</h4>
        <UiBarChart
          :data="barChartData"
          :height="300"
          :categories="barChartCategories"
          :y-axis="['revenue']"
          :x-num-ticks="6"
          :radius="4"
          :y-grid-line="true"
          :x-formatter="barChartXFormatter"
          :y-formatter="barChartYFormatter"
          legend-position="top"
          :hide-legend="false"
        />
      </div>

      <!-- Code Example -->
      <details class="mt-4">
        <summary class="cursor-pointer text-sm font-medium text-gray-600 dark:text-gray-400">
          Show Code
        </summary>
        <pre class="mt-2 p-4 bg-gray-100 dark:bg-gray-900 rounded text-sm overflow-x-auto"><code>&lt;UiBarChart
  :data="barChartData"
  :height="300"
  :categories="categories"
  :y-axis="['revenue']"
  :x-num-ticks="6"
  :radius="4"
  :y-grid-line="true"
  :x-formatter="xFormatter"
  :y-formatter="yFormatter"
  legend-position="top"
  :hide-legend="false"
/&gt;</code></pre>
      </details>
    </div>

    <!-- Interactive Example -->
    <div class="example-section">
      <h3 class="text-lg font-semibold mb-4">Interactive Charts</h3>
      
      <div class="bg-white dark:bg-gray-800 p-6 rounded-lg border mb-6">
        <h4 class="text-md font-medium mb-4">Dynamic Data Updates</h4>
        
        <div class="mb-4 flex gap-2">
          <UiButton @click="randomizeData" size="sm">
            Randomize Data
          </UiButton>
          <UiButton @click="toggleCurveType" size="sm" variant="secondary">
            Toggle Curve: {{ currentCurveType }}
          </UiButton>
          <UiButton @click="toggleLegendPosition" size="sm" variant="ghost">
            Legend: {{ currentLegendPosition }}
          </UiButton>
        </div>
        
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div>
            <h5 class="text-sm font-medium mb-2">Interactive Line Chart</h5>
            <UiLineChart
              :data="interactiveLineData"
              :height="250"
              :categories="interactiveCategories"
              :x-formatter="interactiveXFormatter"
              :curve-type="currentCurveType"
              :legend-position="currentLegendPosition"
              :y-grid-line="true"
            />
          </div>
          
          <div>
            <h5 class="text-sm font-medium mb-2">Interactive Area Chart</h5>
            <UiAreaChart
              :data="interactiveAreaData"
              :height="250"
              :categories="interactiveCategories"
              :x-formatter="interactiveXFormatter"
              :curve-type="currentCurveType"
              :legend-position="currentLegendPosition"
              :y-grid-line="true"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- Data Structure Information -->
    <div class="example-section">
      <h3 class="text-lg font-semibold mb-4">Data Structure Guide</h3>
      
      <div class="bg-white dark:bg-gray-800 p-6 rounded-lg border">
        <h4 class="text-md font-medium mb-4">Required Data Format</h4>
        
        <div class="space-y-4">
          <div>
            <h5 class="text-sm font-semibold mb-2">Chart Data Array:</h5>
            <pre class="p-3 bg-gray-100 dark:bg-gray-900 rounded text-xs overflow-x-auto"><code>const chartData = [
  { date: 'Jan 23', subscriptions: 2890, downloads: 2338 },
  { date: 'Feb 23', subscriptions: 2756, downloads: 2103 },
  // ... more data points
]</code></pre>
          </div>
          
          <div>
            <h5 class="text-sm font-semibold mb-2">Categories Configuration:</h5>
            <pre class="p-3 bg-gray-100 dark:bg-gray-900 rounded text-xs overflow-x-auto"><code>const categories = {
  subscriptions: { name: 'Subscriptions', color: '#3b82f6' },
  downloads: { name: 'Downloads', color: '#22c55e' }
}</code></pre>
          </div>
          
          <div>
            <h5 class="text-sm font-semibold mb-2">X-Axis Formatter:</h5>
            <pre class="p-3 bg-gray-100 dark:bg-gray-900 rounded text-xs overflow-x-auto"><code>const xFormatter = (i: number): string => `${chartData[i]?.date}`</code></pre>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

// Line Chart Data
const lineChartData = ref([
  { date: 'Jan 23', subscriptions: 2890, downloads: 2338 },
  { date: 'Feb 23', subscriptions: 2756, downloads: 2103 },
  { date: 'Mar 23', subscriptions: 3322, downloads: 2194 },
  { date: 'Apr 23', subscriptions: 3470, downloads: 2108 },
  { date: 'May 23', subscriptions: 3475, downloads: 1812 },
  { date: 'Jun 23', subscriptions: 3129, downloads: 1726 }
])

const lineChartCategories = computed(() => ({
  subscriptions: { name: 'Subscriptions', color: '#3b82f6' },
  downloads: { name: 'Downloads', color: '#22c55e' }
}))

const lineChartXFormatter = (i: number): string => `${lineChartData.value[i]?.date}`

// Area Chart Data
const areaChartData = ref([
  { date: '2024-04-01', desktop: 222, mobile: 150 },
  { date: '2024-04-02', desktop: 180, mobile: 97 },
  { date: '2024-04-03', desktop: 167, mobile: 120 },
  { date: '2024-04-04', desktop: 260, mobile: 240 },
  { date: '2024-04-05', desktop: 240, mobile: 290 }
])

const areaChartCategories = computed(() => ({
  desktop: { name: 'Desktop', color: '#3b82f6' },
  mobile: { name: 'Mobile', color: '#22c55e' }
}))

const areaChartXFormatter = (i: number): string => `${areaChartData.value[i]?.date}`

// Bar Chart Data
const barChartData = ref([
  { month: 'January', revenue: 186 },
  { month: 'February', revenue: 305 },
  { month: 'March', revenue: 237 },
  { month: 'April', revenue: 73 },
  { month: 'May', revenue: 209 },
  { month: 'June', revenue: 214 }
])

const barChartCategories = computed(() => ({
  revenue: { name: 'Revenue', color: '#22c55e' }
}))

const barChartXFormatter = (i: number): string => `${barChartData.value[i]?.month}`
const barChartYFormatter = (i: number) => i

// Interactive Data
const interactiveLineData = ref([
  { period: 'Q1', value1: 30, value2: 25 },
  { period: 'Q2', value1: 45, value2: 35 },
  { period: 'Q3', value1: 35, value2: 40 },
  { period: 'Q4', value1: 50, value2: 45 }
])

const interactiveAreaData = ref([
  { period: 'Q1', series1: 20, series2: 15 },
  { period: 'Q2', series1: 35, series2: 25 },
  { period: 'Q3', series1: 25, series2: 30 },
  { period: 'Q4', series1: 40, series2: 35 }
])

const interactiveCategories = computed(() => ({
  value1: { name: 'Series 1', color: '#8b5cf6' },
  value2: { name: 'Series 2', color: '#f59e0b' },
  series1: { name: 'Series 1', color: '#8b5cf6' },
  series2: { name: 'Series 2', color: '#f59e0b' }
}))

const interactiveXFormatter = (i: number): string => 
  `${interactiveLineData.value[i]?.period || interactiveAreaData.value[i]?.period}`

// Interactive Controls
const currentCurveType = ref('linear')
const currentLegendPosition = ref('top')

const curveTypes = ['linear', 'monotoneX', 'natural']
const legendPositions = ['top', 'bottom']

// Methods
const randomizeData = () => {
  interactiveLineData.value = interactiveLineData.value.map(item => ({
    ...item,
    value1: Math.floor(Math.random() * 50) + 10,
    value2: Math.floor(Math.random() * 50) + 10
  }))
  
  interactiveAreaData.value = interactiveAreaData.value.map(item => ({
    ...item,
    series1: Math.floor(Math.random() * 40) + 10,
    series2: Math.floor(Math.random() * 40) + 10
  }))
}

const toggleCurveType = () => {
  const currentIndex = curveTypes.indexOf(currentCurveType.value)
  const nextIndex = (currentIndex + 1) % curveTypes.length
  currentCurveType.value = curveTypes[nextIndex]
}

const toggleLegendPosition = () => {
  const currentIndex = legendPositions.indexOf(currentLegendPosition.value)
  const nextIndex = (currentIndex + 1) % legendPositions.length
  currentLegendPosition.value = legendPositions[nextIndex]
}
</script>

<style scoped>
.example-section {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 2rem;
}

.dark .example-section {
  border-bottom-color: #374151;
}

.example-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

details summary {
  transition: color 0.2s ease;
}

details summary:hover {
  color: #374151;
}

.dark details summary:hover {
  color: #d1d5db;
}

details[open] summary {
  margin-bottom: 0.5rem;
}

pre code {
  color: #374151;
}

.dark pre code {
  color: #d1d5db;
}
</style>
