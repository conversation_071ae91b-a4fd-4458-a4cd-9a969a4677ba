/**
 * Composable for handling scroll-based animations
 * Provides intersection observer functionality for animating elements on scroll
 */
import { ref, onMounted, onUnmounted, nextTick, readonly } from 'vue';
 
export const useScrollAnimation = () => {
  const observer = ref<IntersectionObserver | null>(null)
  const animatedElements = ref<Set<Element>>(new Set())

  /**
   * Initialize the intersection observer
   */
  const initializeObserver = () => {
    if (typeof window === 'undefined') return

    observer.value = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add('in-view')
            animatedElements.value.add(entry.target)
            
            // Unobserve the element after animation to improve performance
            observer.value?.unobserve(entry.target)
          }
        })
      },
      {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
      }
    )
  }

  /**
   * Observe an element for scroll animations
   */
  const observeElement = (element: Element) => {
    if (!observer.value) {
      initializeObserver()
    }
    
    if (observer.value && !animatedElements.value.has(element)) {
      element.classList.add('scroll-animate')
      observer.value.observe(element)
    }
  }

  /**
   * Observe multiple elements
   */
  const observeElements = (selector: string) => {
    if (typeof window === 'undefined') return

    const elements = document.querySelectorAll(selector)
    elements.forEach(observeElement)
  }

  /**
   * Auto-observe elements with specific classes
   */
  const autoObserve = () => {
    if (typeof window === 'undefined') return

    // Common selectors for auto-animation
    const selectors = [
      '.animate-on-scroll',
      '.card-enhanced',
      '.feature-card',
      '.testimonial-card',
      '.stats-card'
    ]

    selectors.forEach(selector => {
      observeElements(selector)
    })
  }

  /**
   * Cleanup observer
   */
  const cleanup = () => {
    if (observer.value) {
      observer.value.disconnect()
      observer.value = null
    }
    animatedElements.value.clear()
  }

  /**
   * Stagger animations for multiple elements
   */
  const staggerAnimation = (elements: NodeListOf<Element> | Element[], delay = 100) => {
    elements.forEach((element, index) => {
      const animationDelay = index * delay
      ;(element as HTMLElement).style.animationDelay = `${animationDelay}ms`
      observeElement(element)
    })
  }

  /**
   * Add animation class with optional delay
   */
  const addAnimation = (element: Element, animationClass: string, delay = 0) => {
    if (delay > 0) {
      setTimeout(() => {
        element.classList.add(animationClass)
      }, delay)
    } else {
      element.classList.add(animationClass)
    }
  }

  /**
   * Animate elements in sequence
   */
  const animateSequence = (elements: Element[], animationClass: string, interval = 200) => {
    elements.forEach((element, index) => {
      setTimeout(() => {
        addAnimation(element, animationClass)
      }, index * interval)
    })
  }

  /**
   * Check if user prefers reduced motion
   */
  const prefersReducedMotion = () => {
    if (typeof window === 'undefined') return false
    return window.matchMedia('(prefers-reduced-motion: reduce)').matches
  }

  /**
   * Conditionally apply animations based on user preferences
   */
  const conditionalAnimate = (element: Element, animationClass: string) => {
    if (prefersReducedMotion()) {
      element.classList.add('in-view')
    } else {
      addAnimation(element, animationClass)
      observeElement(element)
    }
  }

  // Lifecycle hooks
  onMounted(() => {
    if (!prefersReducedMotion()) {
      initializeObserver()
      // Auto-observe after a short delay to ensure DOM is ready
      nextTick(() => {
        setTimeout(autoObserve, 100)
      })
    }
  })

  onUnmounted(() => {
    cleanup()
  })

  return {
    observeElement,
    observeElements,
    autoObserve,
    staggerAnimation,
    addAnimation,
    animateSequence,
    conditionalAnimate,
    prefersReducedMotion,
    cleanup,
    animatedElements: readonly(animatedElements)
  }
}

/**
 * Directive for automatic scroll animations
 */
export const vScrollAnimate = {
  mounted(el: HTMLElement, binding: any) {
    const { observeElement, prefersReducedMotion } = useScrollAnimation()
    
    if (prefersReducedMotion()) {
      el.classList.add('in-view')
      return
    }

    const animationClass = binding.value || 'animate-fade-in-up'
    const delay = binding.modifiers.delay ? parseInt(binding.arg) || 0 : 0

    if (delay > 0) {
      setTimeout(() => {
        el.classList.add(animationClass)
        observeElement(el)
      }, delay)
    } else {
      el.classList.add(animationClass)
      observeElement(el)
    }
  }
}

/**
 * Performance monitoring for animations
 */
export const useAnimationPerformance = () => {
  const performanceMetrics = ref({
    animationCount: 0,
    averageFrameTime: 0,
    droppedFrames: 0
  })

  const startPerformanceMonitoring = () => {
    if (typeof window === 'undefined') return

    let frameCount = 0
    let totalFrameTime = 0
    let lastFrameTime = performance.now()

    const measureFrame = () => {
      const currentTime = performance.now()
      const frameTime = currentTime - lastFrameTime
      
      frameCount++
      totalFrameTime += frameTime
      
      performanceMetrics.value.averageFrameTime = totalFrameTime / frameCount
      
      if (frameTime > 16.67) { // 60fps threshold
        performanceMetrics.value.droppedFrames++
      }
      
      lastFrameTime = currentTime
      requestAnimationFrame(measureFrame)
    }

    requestAnimationFrame(measureFrame)
  }

  return {
    performanceMetrics: readonly(performanceMetrics),
    startPerformanceMonitoring
  }
}
