# Global Navigation Component

The `GlobalNavigation.vue` component provides a consistent top navigation bar for all dashboard pages (except the dashboard home page). It replaces traditional breadcrumbs with an enhanced navigation experience that includes dynamic breadcrumbs, quick actions, view toggles, and keyboard shortcuts.

## Features

- **Dynamic Breadcrumbs**: Auto-generates breadcrumbs from the current route or accepts custom breadcrumb items
- **View Toggle**: Switch between different view modes (table, cards, list, grid)
- **Quick Filters**: Dropdown with customizable filter presets
- **Actions Menu**: Contextual actions like export, import, settings, and help
- **Keyboard Shortcuts**: Built-in keyboard shortcuts with help modal
- **Contextual Action Bar**: Shows when items are selected for bulk operations
- **Responsive Design**: Works across all screen sizes
- **Dark Mode Support**: Full dark mode compatibility

## Usage

The component is automatically included in the dashboard layout for all pages except the dashboard home page. You can control its behavior through page meta properties:

```vue
<script setup lang="ts">
definePageMeta({
  layout: 'dashboard',
  title: 'Your Page Title',
  showViewToggle: true,        // Show view toggle buttons
  showQuickFilters: true,      // Show quick filters dropdown
  showActionsMenu: true,       // Show actions menu
  showKeyboardShortcuts: true, // Show keyboard shortcuts button
  breadcrumb: [                // Custom breadcrumb items (optional)
    { label: 'Dashboard', href: '/dashboard' },
    { label: 'Your Section', href: '/dashboard/section' },
    { label: 'Current Page' },
  ],
});
</script>
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `currentView` | `'table' \| 'cards' \| 'list' \| 'grid'` | `'table'` | Current view mode |
| `selectedCount` | `number` | `0` | Number of selected items |
| `hasActiveFilters` | `boolean` | `false` | Whether filters are currently active |
| `breadcrumbItems` | `BreadcrumbItem[]` | `[]` | Custom breadcrumb items |
| `quickFilterPresets` | `QuickFilterPreset[]` | `[]` | Custom quick filter presets |
| `showViewToggle` | `boolean` | `true` | Show view toggle buttons |
| `showQuickFilters` | `boolean` | `true` | Show quick filters dropdown |
| `showActionsMenu` | `boolean` | `true` | Show actions menu |
| `showKeyboardShortcuts` | `boolean` | `true` | Show keyboard shortcuts button |

## Events

| Event | Payload | Description |
|-------|---------|-------------|
| `view-changed` | `view: string` | Emitted when view mode changes |
| `quick-filter` | `filter: string` | Emitted when a quick filter is applied |
| `bulk-action` | `action: string` | Emitted when a bulk action is triggered |
| `action` | `action: string` | Emitted when a general action is triggered |
| `clear-selection` | - | Emitted when selection should be cleared |

## Keyboard Shortcuts

The component includes built-in keyboard shortcuts:

- `Ctrl/Cmd + N`: Create new item
- `Ctrl/Cmd + F`: Focus search input
- `Ctrl/Cmd + R`: Refresh data
- `Ctrl/Cmd + V`: Toggle view mode
- `Ctrl/Cmd + A`: Select all items
- `Ctrl/Cmd + E`: Export data
- `Ctrl/Cmd + ?`: Show keyboard shortcuts help
- `Escape`: Close modals and dropdowns

## Customization

### Custom Breadcrumbs

You can provide custom breadcrumb items through the `breadcrumbItems` prop:

```typescript
const customBreadcrumbs = [
  { label: 'Dashboard', href: '/dashboard' },
  { label: 'Legal Cases', href: '/dashboard/cases' },
  { label: 'Case Details' } // No href for current page
]
```

### Custom Quick Filters

Provide custom quick filter presets:

```typescript
const customFilters = [
  { key: 'active', label: 'Active Cases', icon: 'material-symbols:check-circle', count: 25 },
  { key: 'pending', label: 'Pending Review', icon: 'material-symbols:pending', count: 8 },
  { key: 'archived', label: 'Archived', icon: 'material-symbols:archive', count: 150 }
]
```

## Integration with Dashboard Layout

The component is automatically integrated into the dashboard layout and will appear on all dashboard pages except the home page (`/dashboard`). The layout handles the component's events and can be extended to provide more sophisticated state management.

## Styling

The component uses Tailwind CSS classes and follows the application's design system. It supports both light and dark modes and is fully responsive.

---

# Global Page Header Component

The `GlobalPageHeader.vue` component provides a consistent page header section that appears under the navigation. It includes a title, description, icon, statistics, and action buttons.

## Features

- **Page Title & Description**: Prominent title with optional description
- **Icon Support**: Optional icon with brand primary color background
- **Statistics Display**: Colored stat badges with customizable colors
- **Action Buttons**: Configurable export and create buttons
- **Custom Actions**: Slot for additional custom action buttons
- **Responsive Design**: Works across all screen sizes
- **Dark Mode Support**: Full dark mode compatibility
- **Loading States**: Support for loading states on buttons

## Usage

The component is automatically included in the dashboard layout when `showPageHeader` is enabled. Configure it through page meta properties:

```vue
<script setup lang="ts">
definePageMeta({
  layout: 'dashboard',
  showPageHeader: true,
  pageHeaderTitle: 'Your Page Title',
  pageHeaderDescription: 'Description of what this page does',
  pageHeaderIcon: 'material-symbols:your-icon',
  pageHeaderStats: [
    { key: 'active', label: 'Active', value: 25, color: 'green' },
    { key: 'pending', label: 'Pending', value: 8, color: 'yellow' },
    { key: 'total', label: 'Total', value: 33, color: 'blue' }
  ],
  showPageHeaderActions: true,
  showPageHeaderExport: true,
  showPageHeaderCreate: true,
  pageHeaderCreateLabel: 'Create Item',
  pageHeaderCreateIcon: 'material-symbols:add',
});
</script>
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `title` | `string` | - | Page title (required) |
| `description` | `string` | `''` | Page description |
| `icon` | `string` | `''` | Icon name for the header |
| `stats` | `Stat[]` | `[]` | Array of statistics to display |
| `loading` | `boolean` | `false` | Loading state for buttons |
| `showDefaultActions` | `boolean` | `true` | Show default action buttons |
| `showExport` | `boolean` | `true` | Show export button |
| `showCreate` | `boolean` | `true` | Show create button |
| `createLabel` | `string` | `'Create'` | Label for create button |
| `createIcon` | `string` | `'material-symbols:add'` | Icon for create button |

## Stat Object

```typescript
interface Stat {
  key: string
  label: string
  value: string | number
  color: 'green' | 'blue' | 'purple' | 'yellow' | 'red' | 'gray'
}
```

## Events

| Event | Description |
|-------|-------------|
| `export` | Emitted when export button is clicked |
| `create` | Emitted when create button is clicked |

## Slots

| Slot | Description |
|------|-------------|
| `actions` | Custom action buttons to display alongside default actions |

## Page Meta Configuration

Configure the header through page meta properties:

- `showPageHeader`: Enable/disable the header
- `pageHeaderTitle`: Header title
- `pageHeaderDescription`: Header description
- `pageHeaderIcon`: Header icon
- `pageHeaderStats`: Array of statistics
- `pageHeaderLoading`: Loading state
- `showPageHeaderActions`: Show/hide action buttons
- `showPageHeaderExport`: Show/hide export button
- `showPageHeaderCreate`: Show/hide create button
- `pageHeaderCreateLabel`: Create button label
- `pageHeaderCreateIcon`: Create button icon

## Color Options

Available colors for stats: `green`, `blue`, `purple`, `yellow`, `red`, `gray`

Each color provides appropriate background, text, and dot colors for both light and dark modes.
