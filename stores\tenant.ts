import { defineStore } from 'pinia';
import { useApi } from '~/composables/useApi';
import type { GetRequestQuery } from '~/types/api';

// Define interfaces for better type safety and clarity
export interface Tenant { // Added export keyword
  id: string;
  createdAt: string;
  updatedAt: string;
  createdById: string | null;
  updatedById: string | null;
  isDeleted: boolean;
  deletedAt: string | null;
  name: string;
  slug: string;
  logoUrl: string | null;
  locale: string;
  timezone: string;
  plan: string;
  isActive: boolean;
}

interface TenantProfile {
  id: string;
  name: string;
  contactEmail: string;
  // Add other relevant tenant profile properties
  plan: {
    name: string;
    // ... other plan details
  };
  usage: {
    cases: number;
    documents: number;
    // ... other usage stats
  };
}

export interface TenantUsage {
  documents: number;
  storageMB: number;
  users: number;
  cases: number;
  documentsLimit: number;
  storageMBLimit: number;
  usersLimit: number;
  casesLimit: number;
}

// Enhanced growth trends interfaces
export interface NewTenantsGrowth {
  total: number;
  growth: number;
  growthPercentage: number;
  period: string;
}

export interface ChurnRate {
  churnRate: number;
  churnedTenants: number;
  period: string;
  churnRateChange: number;
}

export interface AvgRevenuePerTenant {
  avgRevenue: number;
  currency: string;
  avgRevenueChange: number;
  avgRevenueChangePercentage: number;
  period: string;
}

export interface MRRGrowth {
  total: number;
  growth: number;
  growthPercentage: number;
  period: string;
}

export interface ActiveTenantGrowth {
  total: number;
  growth: number;
  growthPercentage: number;
  period: string;
}

export interface StorageGrowth {
  total: number;
  growth: number;
  growthPercentage: number;
  period: string;
}

export interface GrowthTrends {
  newTenants: NewTenantsGrowth;
  churnRate: ChurnRate;
  avgRevenuePerTenant: AvgRevenuePerTenant;
  mrrGrowth: MRRGrowth;
  activeTenantGrowth: ActiveTenantGrowth;
  storageGrowth: StorageGrowth;
}

export interface AlertThresholds {
  critical: {
    churnRate: number; // > 10%
  };
  warning: {
    newTenantGrowth: number; // < 5%
  };
  opportunity: {
    arptGrowth: number; // > 20%
  };
  infrastructure: {
    storageGrowth: number; // > 50%
  };
}

// New interfaces for tenant overview API responses
export interface TenantOverview {
  totalTenants: {
    total: number;
    growth: number;
    growthPercentage: number;
    period: string;
  };
  statusCounts: {
    active: number;
    inactive: number;
    suspended: number;
    pending: number;
  };
  monthlyRevenue: {
    monthlyRevenue: number;
    revenueGrowth: number;
    revenueGrowthPercentage: number;
    currency: string;
  };
  storageUsed: {
    totalUsedMB: number;
    totalAvailableMB: number;
    usagePercentage: number;
    monthlyGrowthMB: number;
  };
  planDistribution: Array<{
    plan: string;
    count: number;
    percentage: number;
    revenue: number;
  }>;
  growthTrends: GrowthTrends;
  recentTenants: Array<{
    id: string;
    name: string;
    slug: string;
    plan: string;
    status: string;
    createdAt: string;
    userCount: number;
  }>;
}

export interface PlatformActivity {
  userActivity: {
    activeSessions: number;
    dailyLogins: number;
    dailyApiCalls: number;
    authSuccessRate: number;
  };
  documentActivity: {
    dailyUploads: number;
    dailyDownloads: number;
    dailySignatures: number;
    processingQueue: number;
    averageUploadTime: number;
  };
  caseActivity: {
    dailyNewCases: number;
    dailyClosedCases: number;
    totalActiveCases: number;
    averageProcessingDays: number;
    completionRate: number;
  };
  systemPerformance: {
    cpuUsage: number;
    memoryUsage: number;
    averageResponseTime: number;
    uptime: number;
    errorRate: number;
  };
  activityTrends: Array<{
    date: string;
    count: number;
    type: string;
  }>;
  topTenants: Array<{
    tenantId: string;
    tenantName: string;
    activityCount: number;
    activityType: string;
  }>;
  lastUpdated: string;
}

export interface TenantMetrics {
  totalTenants?: {
    total: number;
    growth: number;
    growthPercentage: number;
    period: string;
  };
  statusCounts?: {
    active: number;
    inactive: number;
    suspended: number;
    pending: number;
  };
  planDistribution?: Array<{
    plan: string;
    count: number;
    percentage: number;
    revenue: number;
  }>;
  growthTrends?: {
    total: number;
    growth: number;
    growthPercentage: number;
    period: string;
  };
  monthlyRevenue?: {
    monthlyRevenue: number;
    revenueGrowth: number;
    revenueGrowthPercentage: number;
    currency: string;
  };
  storageUsed?: {
    totalUsedMB: number;
    totalAvailableMB: number;
    usagePercentage: number;
    monthlyGrowthMB: number;
  };
}

export interface RecentTenant {
  id: string;
  name: string;
  slug: string;
  plan: string;
  status: string;
  createdAt: string;
  userCount: number;
}

 

export interface CreateTenantPayload {
  // Basic Information
  name: string;
  slug: string;
  email: string;
  phone?: string;
  type: string;
  industry?: string;

  // Address
  country: string;
  city: string;
  address_line1: string;
  address_line2?: string;
  zip_code: string;

  // Legal & Compliance
  vat_number?: string;
  registration_number?: string;
  compliance_level: string;

  // Admin Contact
  contact_person_name: string;
  contact_email: string;
  contact_phone?: string;

  // Preferences
  locale?: string;
  timezone?: string;
  currency: string;
  logo?: File | null;
  custom_domain?: string;

  // Usage & Billing
  plan?: string;
  status: string;
  seats: number;

  // Optional Extensions
  document_storage_quota?: number;
  api_access_enabled?: boolean;
  audit_log_enabled?: boolean;
  webhooks_enabled?: boolean;
}

export interface UpdateTenantPayload { // Added export
  name?: string;
  slug?: string;
  logoUrl?: string | null;
  locale?: string;
  timezone?: string;
  plan?: string;
  isActive?: boolean;
  // Add other updatable fields for a tenant
}

export interface TenantInvitePayload { // Updated export
  email: string; // Changed from emails: string[]
  role: string;
  tenantId: string; // Added tenantId
  // Optionally, add a custom message or other invitation-specific fields
  // message?: string;
}

// Extended query interface for tenants
interface TenantQuery extends GetRequestQuery {
  relations?: string; // Comma-separated relation names
}

interface TenantsResponse {
  data: Tenant[];
  meta: {
    total: number;
    page: number;
    limit: number;
  };
}

export const useTenantStore = defineStore('tenant', {
  state: () => ({
    currentTenantProfile: null as TenantProfile | null,
    selectedTenant: null as Tenant | null, // Added for viewing/editing a specific tenant
    selectedTenantUsage: null as TenantUsage | null, // Added for viewing a specific tenant's usage
    tenants: [] as Tenant[], // For SUPER_ADMIN to list tenants
    tenantsMeta: null as TenantsResponse['meta'] | null, // To store pagination metadata
    // New state for overview data
    tenantOverview: null as TenantOverview | null,
    platformActivity: null as PlatformActivity | null,
    tenantMetrics: null as TenantMetrics | null,
    recentTenants: [] as RecentTenant[],
    // Enhanced growth trends data
    growthTrends: null as GrowthTrends | null,
    alertThresholds: {
      critical: { churnRate: 10 },
      warning: { newTenantGrowth: 5 },
      opportunity: { arptGrowth: 20 },
      infrastructure: { storageGrowth: 50 }
    } as AlertThresholds,
    isLoading: false,
    error: null as string | null,
  }),

  getters: {
    // Getter to easily access the current tenant's ID
    tenantId: (state) => state.currentTenantProfile?.id,
    // Getter for the current tenant's name
    tenantName: (state) => state.currentTenantProfile?.name,
    getTenants: (state) => state.tenants.map(tenant => ({...tenant, logoUrl: tenant.logoUrl ? '/api/storage'+tenant.logoUrl : `https://ui-avatars.com/api/?name=${tenant.name}&background=1a56db&color=fff`})),
    selectedTenantWithLogo: (state) =>({ ...state.selectedTenant, logoUrl: state.selectedTenant?.logoUrl ? '/api/storage'+state.selectedTenant?.logoUrl : `https://ui-avatars.com/api/?name=${state.selectedTenant?.name}&background=1a56db&color=fff`}),
    // Check if a tenant profile is loaded
    isTenantProfileLoaded: (state) => !!state.currentTenantProfile,
    // For Super Admin to check if tenants list is loaded
    hasTenants: (state) => state.tenants.length > 0,
    // New getters for overview data
    hasOverviewData: (state) => !!state.tenantOverview,
    hasPlatformActivity: (state) => !!state.platformActivity,
    hasRecentTenants: (state) => state.recentTenants.length > 0,
    hasGrowthTrends: (state) => !!state.growthTrends,
    // Quick access to key metrics
    totalTenantsCount: (state) => state.tenantOverview?.totalTenants.total || 0,
    activeTenantsCount: (state) => state.tenantOverview?.statusCounts.active || 0,
    monthlyRevenueAmount: (state) => state.tenantOverview?.monthlyRevenue.monthlyRevenue || 0,
    storageUsagePercentage: (state) => state.tenantOverview?.storageUsed.usagePercentage || 0,

    // Enhanced growth trends getters
    newTenantsGrowth: (state) => state.tenantOverview?.growthTrends?.newTenants || state.growthTrends?.newTenants,
    churnRateData: (state) => state.tenantOverview?.growthTrends?.churnRate || state.growthTrends?.churnRate,
    avgRevenuePerTenantData: (state) => state.tenantOverview?.growthTrends?.avgRevenuePerTenant || state.growthTrends?.avgRevenuePerTenant,
    mrrGrowthData: (state) => state.tenantOverview?.growthTrends?.mrrGrowth || state.growthTrends?.mrrGrowth,
    activeTenantGrowthData: (state) => state.tenantOverview?.growthTrends?.activeTenantGrowth || state.growthTrends?.activeTenantGrowth,
    storageGrowthData: (state) => state.tenantOverview?.growthTrends?.storageGrowth || state.growthTrends?.storageGrowth,

    // Alert status getters
    isCriticalChurnRate: (state) => {
      const churnRate = state.tenantOverview?.growthTrends?.churnRate?.churnRate || state.growthTrends?.churnRate?.churnRate || 0;
      return churnRate > state.alertThresholds.critical.churnRate;
    },
    isLowNewTenantGrowth: (state) => {
      const growth = state.tenantOverview?.growthTrends?.newTenants?.growthPercentage || state.growthTrends?.newTenants?.growthPercentage || 0;
      return growth < state.alertThresholds.warning.newTenantGrowth;
    },
    isHighARPTGrowth: (state) => {
      const growth = state.tenantOverview?.growthTrends?.avgRevenuePerTenant?.avgRevenueChangePercentage || state.growthTrends?.avgRevenuePerTenant?.avgRevenueChangePercentage || 0;
      return growth > state.alertThresholds.opportunity.arptGrowth;
    },
    isHighStorageGrowth: (state) => {
      const growth = state.tenantOverview?.growthTrends?.storageGrowth?.growthPercentage || state.growthTrends?.storageGrowth?.growthPercentage || 0;
      return growth > state.alertThresholds.infrastructure.storageGrowth;
    },
  },

  actions: {
    /**
     * Fetches the current authenticated tenant's profile.
     * This is typically called on application startup or after login for TENANT_OWNER/ADMIN roles.
     * API: GET /tenants/me/profile 
     */
    async fetchCurrentTenantProfile(): Promise<void> {
      this.isLoading = true;
      this.error = null;
      try {
        const api = useApi();
        this.currentTenantProfile = await api.get<TenantProfile>('/tenants/me/profile');
        console.log('Current tenant profile fetched:', this.currentTenantProfile);
      } catch (err: any) {
        this.error = this.handleApiError(err, 'Failed to fetch current tenant profile.');
        this.currentTenantProfile = null; // Clear profile on error
        console.error('Error fetching current tenant profile:', err);
      } finally {
        this.isLoading = false;
      }
    },

    /**
     * Fetches the current tenant's plan usage statistics.
     * API: GET /tenants/me/usage 
     */
    async fetchCurrentTenantUsage(): Promise<void> {
      this.isLoading = true;
      this.error = null;
      try {
        const api = useApi();
        const usageData = await api.get<TenantProfile['usage']>('/tenants/me/usage');
        if (this.currentTenantProfile) {
          this.currentTenantProfile.usage = usageData;
        } else {
          // If profile isn't loaded, we might need to load it first or handle this case
          console.warn('Tenant profile not loaded when trying to fetch usage.');
        }
        console.log('Current tenant usage fetched:', usageData);
      } catch (err: any) {
        this.error = this.handleApiError(err, 'Failed to fetch current tenant usage.');
        console.error('Error fetching current tenant usage:', err);
      } finally {
        this.isLoading = false;
      }
    },

    /**
     * Updates the current tenant's plan. (Tenant Owner only)
     * API: PATCH /tenants/me/plan 
     * @param newPlanData The new plan details to update.
     */
    async updateCurrentTenantPlan(newPlanData: any): Promise<void> {
      this.isLoading = true;
      this.error = null;
      try {
        const api = useApi();
        this.currentTenantProfile = await api.patch<TenantProfile>('/tenants/me/plan', newPlanData);
        this.showSuccessNotification('Tenant plan updated successfully.');
      } catch (err: any) {
        this.error = this.handleApiError(err, 'Failed to update tenant plan.');
        console.error('Error updating tenant plan:', err);
        throw err; // Re-throw to allow component to catch specific errors
      } finally {
        this.isLoading = false;
      }
    },

    /**
     * Creates a new tenant. (Super Admin only)
     * API: POST /tenants
     * @param payload The data for the new tenant.
     * @returns The created tenant object.
     */
    // async createTenant(payload: CreateTenantPayload): Promise<Tenant> {
    //   this.isLoading = true;
    //   this.error = null;
    //   try {
    //     const api = useApi();
    //     const newTenant = await api.post<Tenant>('/tenants', payload);
    //     this.tenants.push(newTenant); // Add to the list if we're managing it
    //     this.showSuccessNotification('Tenant created successfully.');
    //     return newTenant;
    //   } catch (err: any) {
    //     this.error = this.handleApiError(err, 'Failed to create tenant.');
    //     this.showErrorNotification(this.error);
    //     console.error('Error creating tenant:', err);
    //     throw err;
    //   } finally {
    //     this.isLoading = false;
    //   }
    // },

    /**
     * Creates a new tenant with logo upload. (Super Admin only)
     * API: POST /tenants/with-logo (multipart/form-data)
     * @param payload The data for the new tenant including logo file.
     * @returns The created tenant object.
     */
    async createTenant(payload: CreateTenantPayload): Promise<Tenant> {
      this.isLoading = true;
      this.error = null;
      try {
        const api = useApi();

        // Create FormData for multipart/form-data request
        const formData = new FormData();

        // Basic Information
        formData.append('name', payload.name);
        formData.append('slug', payload.slug);
        formData.append('email', payload.email);
        if (payload.phone) formData.append('phone', payload.phone);
        formData.append('type', payload.type);
        if (payload.industry) formData.append('industry', payload.industry);

        // Address
        formData.append('country', payload.country);
        formData.append('city', payload.city);
        formData.append('address_line1', payload.address_line1);
        if (payload.address_line2) formData.append('address_line2', payload.address_line2);
        formData.append('zip_code', payload.zip_code);

        // Legal & Compliance
        if (payload.vat_number) formData.append('vat_number', payload.vat_number);
        if (payload.registration_number) formData.append('registration_number', payload.registration_number);
        formData.append('compliance_level', payload.compliance_level);

        // Admin Contact
        formData.append('contact_person_name', payload.contact_person_name);
        formData.append('contact_email', payload.contact_email);
        if (payload.contact_phone) formData.append('contact_phone', payload.contact_phone);

        // Preferences
        formData.append('locale', payload.locale || 'en');
        formData.append('timezone', payload.timezone || 'UTC');
        formData.append('currency', payload.currency);
        if (payload.custom_domain) formData.append('custom_domain', payload.custom_domain);

        // Usage & Billing
        formData.append('plan', payload.plan || 'starter');
        formData.append('status', payload.status);
        formData.append('seats', payload.seats.toString());

        // Optional Extensions
        if (payload.document_storage_quota) formData.append('document_storage_quota', payload.document_storage_quota.toString());
        formData.append('api_access_enabled', payload.api_access_enabled ? 'true' : 'false');
        formData.append('audit_log_enabled', payload.audit_log_enabled ? 'true' : 'false');
        formData.append('webhooks_enabled', payload.webhooks_enabled ? 'true' : 'false');

        // Logo file
        if (payload.logo) {
          formData.append('logo', payload.logo);
        }

        // Make request with multipart/form-data
        // Note: Don't set Content-Type header manually for FormData
        // The browser will set it automatically with the correct boundary
        const newTenant = await api.post<Tenant>('/tenants', formData, { headers: { 'Content-Type': 'multipart/form-data' } });

        this.tenants.push(newTenant); // Add to the list if we're managing it
        this.showSuccessNotification('Tenant created successfully .');
        return newTenant;
      } catch (err: any) {
        this.error = this.handleApiError(err, 'Failed to create tenant.');
        this.showErrorNotification(this.error);
        console.error('Error creating tenant:', err);
        throw err;
      } finally {
        this.isLoading = false;
      }
    },

    /**
     * Lists all tenants. (Super Admin only)
     * API: GET /tenants
     * @param query Optional query parameters for pagination, sorting, and filtering.
     */
    async fetchAllTenants(query?: TenantQuery, caller = 'unknown'): Promise<void> {
      console.debug(`fetchAllTenants called by: ${caller}`);
      this.isLoading = true;
      this.error = null;
      try {
        const api = useApi();
        const response = await api.get<TenantsResponse>('/tenants', { params: query });
        this.tenants = response.data;
        this.tenantsMeta = response.meta;
        console.log('All tenants fetched with query:', query, this.tenants, this.tenantsMeta);
      } catch (err: any) {
        this.error = this.handleApiError(err, 'Failed to fetch all tenants.');
        this.tenants = []; // Clear tenants on error
        this.tenantsMeta = null; // Clear meta on error
        console.error('Error fetching all tenants:', err);
      } finally {
        this.isLoading = false;
      }
    },

    /**
     * Fetches a single tenant by its ID. (Super Admin only)
     * API: GET /tenants/:id
     * @param id The ID of the tenant to fetch.
     * @returns The tenant object.
     */
    async fetchTenantById(id: string): Promise<Tenant | null> {
      this.isLoading = true;
      this.error = null;
      this.selectedTenant = null;
      try {
        const api = useApi();
        const tenant = await api.get<Tenant>(`/tenants/${id}`);
        this.selectedTenant = tenant;
        // Optionally, update it in the main list if it's already there
        const index = this.tenants.findIndex(t => t.id === id);
        if (index !== -1) {
          this.tenants[index] = tenant;
        }
        return tenant;
      } catch (err: any) {
        this.error = this.handleApiError(err, `Failed to fetch tenant with ID: ${id}.`);
        console.error('Error fetching tenant by ID:', err);
        return null;
      } finally {
        this.isLoading = false;
      }
    },

    /**
     * Fetches usage data for a specific tenant by its ID. (Super Admin only)
     * API: GET /tenants/:id/usage
     * @param id The ID of the tenant to fetch usage for.
     * @returns The tenant usage object.
     */
    async fetchTenantUsageById(id: string): Promise<TenantUsage | null> {
      this.isLoading = true;
      this.error = null;
      this.selectedTenantUsage = null;
      try {
        const api = useApi();
        // Ensure the endpoint matches your API structure, e.g., /admin/tenants/:id/usage or /platform/tenants/:id/usage
        const usageData = await api.get<TenantUsage>(`/tenants/${id}/usage`);
        this.selectedTenantUsage = usageData;
        return usageData;
      } catch (err: any) {
        this.error = this.handleApiError(err, `Failed to fetch usage for tenant with ID: ${id}.`);
        console.error('Error fetching tenant usage by ID:', err);
        return null;
      } finally {
        this.isLoading = false;
      }
    },

    /**
     * Updates a specific tenant by ID. (Super Admin only)
     * API: PATCH /tenants/:id
     * @param id The ID of the tenant to update.
     * @param payload The data to update.
     * @returns The updated tenant object.
     */
    async updateTenant(id: string, payload: UpdateTenantPayload): Promise<Tenant> {
      this.isLoading = true;
      this.error = null;
      try {
        const api = useApi();
        const updatedTenant = await api.patch<Tenant>(`/tenants/${id}`, payload);
        // Update the tenant in the local list if it exists
        const index = this.tenants.findIndex(t => t.id === id);
        if (index !== -1) {
          this.tenants[index] = updatedTenant;
        }
        this.showSuccessNotification('Tenant updated successfully.');
        return updatedTenant;
      } catch (err: any) {
        this.error = this.handleApiError(err, `Failed to update tenant with ID: ${id}.`);
        console.error('Error updating tenant:', err);
        throw err;
      } finally {
        this.isLoading = false;
      }
    },

    /**
     * Soft deletes a tenant by ID. (Super Admin only)
     * API: DELETE /tenants/:id/soft-delete 
     * @param id The ID of the tenant to soft delete.
     */
    async softDeleteTenant(id: string): Promise<void> {
      this.isLoading = true;
      this.error = null;
      try {
        const api = useApi();
        await api.delete(`/tenants/${id}/soft-delete`);
        this.tenants = this.tenants.filter(t => t.id !== id);
        this.showSuccessNotification('Tenant soft-deleted successfully.');
      } catch (err: any) {
        this.error = this.handleApiError(err, `Failed to soft delete tenant with ID: ${id}.`);
        console.error('Error soft deleting tenant:', err);
        throw err;
      } finally {
        this.isLoading = false;
      }
    },

    /**
     * Seeds default roles and triggers for a tenant. (Super Admin only)
     * This is useful for new tenant onboarding/setup.
     * API: POST /tenants/:id/seed 
     * @param id The ID of the tenant to seed.
     */
    async seedTenant(id: string): Promise<void> {
      this.isLoading = true;
      this.error = null;
      try {
        const api = useApi();
        await api.post(`/tenants/${id}/seed`);
        this.showSuccessNotification('Tenant seeded successfully.');
      } catch (err: any) {
        this.error = this.handleApiError(err, `Failed to seed tenant with ID: ${id}.`);
        console.error('Error seeding tenant:', err);
        throw err;
      } finally {
        this.isLoading = false;
      }
    },
    
        /**
         * Sends an invitation to a user for a specific tenant. (Super Admin only)
         * API: POST /users/invite
         * @param payload The invitation details (email, role, tenantId).
         */
        async sendInvitesToTenant(payload: TenantInvitePayload): Promise<void> { // tenantId removed from params, it's in payload
          this.isLoading = true;
          this.error = null;
          try {
            const api = useApi();
            // API path updated to /users/invite
            // The payload now contains email, role, and tenantId
            await api.post(`/users/invite`, payload);
            this.showSuccessNotification(`Invitation sent successfully to ${payload.email}.`);
          } catch (err: any) {
            this.error = this.handleApiError(err, `Failed to send invitation to ${payload.email}.`);
            console.error('Error sending tenant invitation:', err);
            throw err; // Re-throw to allow component to catch specific errors
          } finally {
            this.isLoading = false;
          }
        },

    /**
     * Fetches complete tenant overview data.
     * API: GET /tenants/overview
     */
    async fetchTenantOverview(): Promise<void> {
      this.isLoading = true;
      this.error = null;
      try {
        const api = useApi();
        this.tenantOverview = await api.get<TenantOverview>('/admin/tenant-analytics');
        console.log('Tenant overview fetched:', this.tenantOverview);
      } catch (err: any) {
        this.error = this.handleApiError(err, 'Failed to fetch tenant overview.');
        this.tenantOverview = null;
        console.error('Error fetching tenant overview:', err);
      } finally {
        this.isLoading = false;
      }
    },

    /**
     * Fetches platform activity data.
     * API: GET /tenants/overview/platform-activity
     */
    async fetchPlatformActivity(): Promise<void> {
      this.isLoading = true;
      this.error = null;
      try {
        const api = useApi();
        this.platformActivity = await api.get<PlatformActivity>('/admin/tenant-analytics/platform-activity');
        console.log('Platform activity fetched:', this.platformActivity);
      } catch (err: any) {
        this.error = this.handleApiError(err, 'Failed to fetch platform activity.');
        this.platformActivity = null;
        console.error('Error fetching platform activity:', err);
      } finally {
        this.isLoading = false;
      }
    },

    /**
     * Fetches specific tenant metrics.
     * API: GET /tenants/overview/metrics/{type}
     * @param type The type of metrics to fetch ('tenants', 'revenue', 'storage')
     */
    async fetchTenantMetrics(type: 'tenants' | 'revenue' | 'storage'): Promise<void> {
      this.isLoading = true;
      this.error = null;
      try {
        const api = useApi();
        const metrics = await api.get<TenantMetrics>(`/admin/tenant-analytics/metrics/${type}`);
        // Merge with existing metrics
        this.tenantMetrics = { ...this.tenantMetrics, ...metrics };
        console.log(`Tenant ${type} metrics fetched:`, metrics);
      } catch (err: any) {
        this.error = this.handleApiError(err, `Failed to fetch ${type} metrics.`);
        console.error(`Error fetching ${type} metrics:`, err);
      } finally {
        this.isLoading = false;
      }
    },

    /**
     * Fetches recently created tenants.
     * API: GET /tenants/overview/recent-tenants
     */
    async fetchRecentTenants(): Promise<void> {
      this.isLoading = true;
      this.error = null;
      try {
        const api = useApi();
        const response = await api.get<{ recentTenants: RecentTenant[] }>('/admin/tenant-analytics/recent-tenants');
        this.recentTenants = response.recentTenants;
        console.log('Recent tenants fetched:', this.recentTenants);
      } catch (err: any) {
        this.error = this.handleApiError(err, 'Failed to fetch recent tenants.');
        this.recentTenants = [];
        console.error('Error fetching recent tenants:', err);
      } finally {
        this.isLoading = false;
      }
    },

    /**
     * Fetches comprehensive tenant analytics including all growth trends.
     * API: GET /admin/tenant-analytics
     */
    async fetchTenantAnalytics(): Promise<void> {
      this.isLoading = true;
      this.error = null;
      try {
        const api = useApi();
        const analytics = await api.get<{ growthTrends: GrowthTrends }>('/admin/tenant-analytics');
        this.growthTrends = analytics.growthTrends;
        console.log('Tenant analytics fetched:', this.growthTrends);
      } catch (err: any) {
        this.error = this.handleApiError(err, 'Failed to fetch tenant analytics.');
        this.growthTrends = null;
        console.error('Error fetching tenant analytics:', err);
      } finally {
        this.isLoading = false;
      }
    },

    /**
     * Fetches focused growth metrics for tenants.
     * API: GET /admin/tenant-analytics/metrics/tenants
     */
    async fetchTenantGrowthMetrics(): Promise<void> {
      this.isLoading = true;
      this.error = null;
      try {
        const api = useApi();
        const metrics = await api.get<{ growthTrends: Partial<GrowthTrends> }>('/admin/tenant-analytics/metrics/tenants');
        // Merge with existing growth trends
        this.growthTrends = { ...this.growthTrends, ...metrics.growthTrends } as GrowthTrends;
        console.log('Tenant growth metrics fetched:', metrics.growthTrends);
      } catch (err: any) {
        this.error = this.handleApiError(err, 'Failed to fetch tenant growth metrics.');
        console.error('Error fetching tenant growth metrics:', err);
      } finally {
        this.isLoading = false;
      }
    },

    /**
     * Fetches all overview data in one call for dashboard initialization.
     */
    async fetchAllOverviewData(): Promise<void> {
      try {
        await Promise.all([
          this.fetchTenantOverview(),
          this.fetchPlatformActivity(),
          this.fetchRecentTenants(),
          this.fetchTenantAnalytics()
        ]);
      } catch (err: any) {
        console.error('Error fetching overview data:', err);
        // Individual methods handle their own errors
      }
    },

    /**
     * Gets alert status for all growth trends.
     * @returns Object containing alert statuses
     */
    getAlertStatus(): {
      critical: string[];
      warning: string[];
      opportunity: string[];
      infrastructure: string[];
    } {
      const alerts = {
        critical: [] as string[],
        warning: [] as string[],
        opportunity: [] as string[],
        infrastructure: [] as string[]
      };

      if (this.isCriticalChurnRate) {
        alerts.critical.push(`High churn rate: ${this.churnRateData?.churnRate?.toFixed(1)}%`);
      }

      if (this.isLowNewTenantGrowth) {
        alerts.warning.push(`Low new tenant growth: ${this.newTenantsGrowth?.growthPercentage?.toFixed(1)}%`);
      }

      if (this.isHighARPTGrowth) {
        alerts.opportunity.push(`High ARPT growth: ${this.avgRevenuePerTenantData?.avgRevenueChangePercentage?.toFixed(1)}%`);
      }

      if (this.isHighStorageGrowth) {
        alerts.infrastructure.push(`High storage growth: ${this.storageGrowthData?.growthPercentage?.toFixed(1)}%`);
      }

      return alerts;
    },

        /**
         * Helper to handle API errors consistently.
         * This assumes your `api-requester.server.ts` or a global error handler might structure errors.
         * For NestJS backend, it will be `error.response.data`
         */
        handleApiError(err: any, defaultMessage: string): string {
      // Assuming consistent JSON error responses from backend
      // Example: { "statusCode": 400, "message": ["password must be strong enough"], "error": "Bad Request" } 
      if (err.response && err.response.data && err.response.data.message) {
        if (Array.isArray(err.response.data.message)) {
          return err.response.data.message.join(', ');
        }
        return err.response.data.message;  
      }
      return err.message || defaultMessage;
    },

    /**
     * Show success notification using toast system
     */
    showSuccessNotification(message: string) {
      const { showToast } = useToast();
      showToast({
        type: 'success',
        message,
        duration: 3000
      });
    },

    /**
     * Show error notification using toast system
     */
    showErrorNotification(message: string) {
      const { showToast } = useToast();
      showToast({
        type: 'error',
        message,
        duration: 0 // Errors don't auto-dismiss
      });
    },
  },
});