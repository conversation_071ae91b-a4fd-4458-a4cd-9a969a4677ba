import { cmToPixels, inchToPixels } from './utils'

export const pageFormats = {
  A4: {
    width: cmToPixels(21.0),
    height: cmToPixels(29.7),
    margins: {
      top: cmToPixels(2.5),
      right: cmToPixels(2.0),
      bottom: cmToPixels(2.5),
      left: cmToPixels(2.0),
    },
  },
  A3: {
    width: cmToPixels(29.7),
    height: cmToPixels(42.0),
    margins: {
      top: cmToPixels(2.5),
      right: cmToPixels(2.0),
      bottom: cmToPixels(2.5),
      left: cmToPixels(2.0),
    },
  },
  A5: {
    width: cmToPixels(14.8),
    height: cmToPixels(21.0),
    margins: {
      top: cmToPixels(2.0),
      right: cmToPixels(1.5),
      bottom: cmToPixels(2.0),
      left: cmToPixels(1.5),
    },
  },
  Letter: {
    width: inchToPixels(8.5),
    height: inchToPixels(11),
    margins: {
      top: inchToPixels(1),
      right: inchToPixels(1),
      bottom: inchToPixels(1),
      left: inchToPixels(1),
    },
  },
  Legal: {
    width: inchToPixels(8.5),
    height: inchToPixels(14),
    margins: {
      top: inchToPixels(1),
      right: inchToPixels(1),
      bottom: inchToPixels(1),
      left: inchToPixels(1),
    },
  },
  Tabloid: {
    width: inchToPixels(11),
    height: inchToPixels(17),
    margins: {
      top: inchToPixels(1),
      right: inchToPixels(1),
      bottom: inchToPixels(1),
      left: inchToPixels(1),
    },
  },
}

export const getFormat = (format: string) => {
  if (pageFormats[format]) {
    return pageFormats[format];
  }
  throw new Error('invalid page format name');
}

export const getFormatCss = format => {
    const { width, height, margins } = getFormat(format)
    const { top, left, bottom, right } = margins
    return `width: ${width}px; height: ${height}px; padding: ${top}px ${right}px ${bottom}px ${left}px;`
}