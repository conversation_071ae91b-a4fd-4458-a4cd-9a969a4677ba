<template>
  <div class="space-y-6">
    <!-- Document Editor Toolbar -->
    <div class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
      <div class="flex items-center space-x-2">
        <!-- Formatting Toolbar -->
        <UiButton @click="formatText('bold')" variant="ghost" size="sm" class="p-2">
          <Icon name="heroicons:bold" class="w-4 h-4" />
        </UiButton>
        <UiButton @click="formatText('italic')" variant="ghost" size="sm" class="p-2">
          <Icon name="heroicons:italic" class="w-4 h-4" />
        </UiButton>
        <UiButton @click="formatText('underline')" variant="ghost" size="sm" class="p-2">
          <Icon name="heroicons:underline" class="w-4 h-4" />
        </UiButton>
        <div class="w-px h-4 bg-gray-300 dark:bg-gray-600"></div>
        <UiButton @click="formatText('insertUnorderedList')" variant="ghost" size="sm" class="p-2">
          <Icon name="heroicons:list-bullet" class="w-4 h-4" />
        </UiButton>
        <UiButton @click="formatText('insertOrderedList')" variant="ghost" size="sm" class="p-2">
          <Icon name="heroicons:numbered-list" class="w-4 h-4" />
        </UiButton>
        <div class="w-px h-4 bg-gray-300 dark:bg-gray-600"></div>
        <UiButton @click="insertLink" variant="ghost" size="sm" class="p-2">
          <Icon name="heroicons:link" class="w-4 h-4" />
        </UiButton>
        <UiButton @click="insertTable" variant="ghost" size="sm" class="p-2">
          <Icon name="heroicons:table-cells" class="w-4 h-4" />
        </UiButton>
      </div>
      
      <div class="flex items-center space-x-2">
        <UiButton @click="toggleEditorMode" variant="outline" size="sm">
          <Icon :name="editorMode === 'visual' ? 'heroicons:code-bracket' : 'heroicons:eye'" class="w-4 h-4 mr-1" />
          {{ editorMode === 'visual' ? 'Source' : 'Visual' }}
        </UiButton>
        <UiButton @click="insertVariable" variant="outline" size="sm">
          <Icon name="heroicons:plus" class="w-4 h-4 mr-1" />
          Variable
        </UiButton>
      </div>
    </div>

    <!-- Visual Editor -->
    <div v-if="editorMode === 'visual'" class="border border-gray-300 dark:border-gray-600 rounded-lg overflow-hidden">
      <div
        ref="visualEditor"
        contenteditable="true"
        @input="handleVisualEditorInput"
        @paste="handlePaste"
        class="min-h-[500px] p-6 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-inset bg-white dark:bg-gray-900"
        :class="[
          'prose prose-lg max-w-none',
          'dark:prose-invert dark:text-gray-100',
          'placeholder:text-gray-400 dark:placeholder:text-gray-500'
        ]"
        :data-placeholder="'Start writing your document template here...'"
        v-html="localContent"
      ></div>
    </div>

    <!-- Source Editor -->
    <div v-else class="space-y-2">
      <UiTextarea
        id="documentSourceContent"
        v-model="localContent"
        label=""
        placeholder="Enter document content..."
        :rows="20"
        class="font-mono text-sm"
      />
      <div class="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
        <span>Source mode - you can use HTML markup and legal document formatting</span>
        <span>{{ contentLength }} characters</span>
      </div>
    </div>

    <!-- Document Settings -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <div class="space-y-4">
        <h3 class="text-sm font-medium text-gray-900 dark:text-white">Document Settings</h3>
        
        <div class="space-y-3">
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Page Size
            </label>
            <select
              v-model="documentSettings.pageSize"
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm"
            >
              <option value="letter">Letter (8.5" x 11")</option>
              <option value="a4">A4 (210mm x 297mm)</option>
              <option value="legal">Legal (8.5" x 14")</option>
            </select>
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Font Family
            </label>
            <select
              v-model="documentSettings.fontFamily"
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm"
            >
              <option value="times">Times New Roman</option>
              <option value="arial">Arial</option>
              <option value="helvetica">Helvetica</option>
              <option value="calibri">Calibri</option>
            </select>
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Font Size
            </label>
            <select
              v-model="documentSettings.fontSize"
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm"
            >
              <option value="10">10pt</option>
              <option value="11">11pt</option>
              <option value="12">12pt</option>
              <option value="14">14pt</option>
            </select>
          </div>
        </div>
      </div>

      <div class="space-y-4">
        <h3 class="text-sm font-medium text-gray-900 dark:text-white">Legal Features</h3>
        
        <div class="space-y-3">
          <label class="flex items-center space-x-2">
            <input
              type="checkbox"
              v-model="documentSettings.requireSignature"
              class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span class="text-sm text-gray-700 dark:text-gray-300">Require digital signature</span>
          </label>
          
          <label class="flex items-center space-x-2">
            <input
              type="checkbox"
              v-model="documentSettings.enableWatermark"
              class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span class="text-sm text-gray-700 dark:text-gray-300">Add watermark</span>
          </label>
          
          <label class="flex items-center space-x-2">
            <input
              type="checkbox"
              v-model="documentSettings.enableLineNumbers"
              class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span class="text-sm text-gray-700 dark:text-gray-300">Show line numbers</span>
          </label>
          
          <label class="flex items-center space-x-2">
            <input
              type="checkbox"
              v-model="documentSettings.enableFooter"
              class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span class="text-sm text-gray-700 dark:text-gray-300">Include page footer</span>
          </label>
        </div>
      </div>
    </div>

    <!-- Document Preview -->
    <div class="border-t border-gray-200 dark:border-gray-700 pt-6">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-sm font-medium text-gray-900 dark:text-white">Document Preview</h3>
        <div class="flex items-center space-x-2">
          <UiButton @click="printPreview" variant="outline" size="sm">
            <Icon name="heroicons:printer" class="w-4 h-4 mr-1" />
            Print Preview
          </UiButton>
          <UiButton @click="exportPdf" variant="outline" size="sm">
            <Icon name="heroicons:arrow-down-tray" class="w-4 h-4 mr-1" />
            Export PDF
          </UiButton>
        </div>
      </div>
      
      <!-- Document Preview Container -->
      <div class="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg">
        <div
          class="bg-white dark:bg-gray-900 shadow-lg mx-auto rounded border border-gray-200 dark:border-gray-700 overflow-hidden"
          :class="getPageSizeClass()"
        >
          <div class="p-8">
            <div v-html="renderedContent" class="prose prose-lg max-w-none dark:prose-invert"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- Variable Insert Modal -->
    <VariableInsertModal
      v-if="showVariableModal"
      @close="showVariableModal = false"
      @insert="handleVariableInsert"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive, watch, onMounted, defineAsyncComponent } from 'vue'

// Lazy load modals
const VariableInsertModal = defineAsyncComponent(() => import('../template-modals/VariableInsertModal.vue'))

// Props
interface Props {
  content: string
  variables: string[]
  templateData: any
  category?: any
}

const props = withDefaults(defineProps<Props>(), {
  content: '',
  variables: () => [],
})

// Emits
const emit = defineEmits<{
  'update:content': [content: string]
  'update:variables': [variables: string[]]
  'content-change': [content: string, variables: string[]]
}>()

// State
const editorMode = ref<'visual' | 'source'>('visual')
const showVariableModal = ref(false)
const visualEditor = ref<HTMLElement>()

const localContent = ref(props.content)
const localVariables = ref([...props.variables])

const documentSettings = reactive({
  pageSize: 'letter',
  fontFamily: 'times',
  fontSize: '12',
  requireSignature: false,
  enableWatermark: false,
  enableLineNumbers: false,
  enableFooter: true,
})

// Computed
const contentLength = computed(() => localContent.value?.length || 0)

const renderedContent = computed(() => {
  let content = localContent.value || 'Your document content will appear here...'
  
  // Replace variables with placeholder values for preview
  localVariables.value.forEach(variable => {
    const placeholder = `<span class="bg-blue-100 text-blue-800 px-1 rounded text-sm font-mono">[${variable.toUpperCase()}]</span>`
    content = content.replace(new RegExp(`{${variable}}`, 'g'), placeholder)
  })
  
  return content
})

// Watchers
watch(localContent, (newContent) => {
  emit('update:content', newContent)
  emit('content-change', newContent, localVariables.value)
}, { deep: true })

watch(localVariables, (newVariables) => {
  emit('update:variables', newVariables)
  emit('content-change', localContent.value, newVariables)
}, { deep: true })

// Methods
const toggleEditorMode = () => {
  editorMode.value = editorMode.value === 'visual' ? 'source' : 'visual'
}

const formatText = (command: string) => {
  document.execCommand(command, false)
  handleVisualEditorInput()
}

const insertLink = () => {
  const url = prompt('Enter URL:')
  if (url) {
    document.execCommand('createLink', false, url)
    handleVisualEditorInput()
  }
}

const insertTable = () => {
  const rows = prompt('Number of rows:', '3')
  const cols = prompt('Number of columns:', '3')
  if (rows && cols) {
    let tableHtml = '<table border="1" style="border-collapse: collapse; width: 100%;">'
    for (let i = 0; i < parseInt(rows); i++) {
      tableHtml += '<tr>'
      for (let j = 0; j < parseInt(cols); j++) {
        tableHtml += '<td style="padding: 8px; border: 1px solid #ccc;">&nbsp;</td>'
      }
      tableHtml += '</tr>'
    }
    tableHtml += '</table>'
    document.execCommand('insertHTML', false, tableHtml)
    handleVisualEditorInput()
  }
}

const handleVisualEditorInput = () => {
  if (visualEditor.value) {
    localContent.value = visualEditor.value.innerHTML
  }
}

const handlePaste = (event: ClipboardEvent) => {
  event.preventDefault()
  const text = event.clipboardData?.getData('text/plain') || ''
  document.execCommand('insertText', false, text)
  handleVisualEditorInput()
}

const insertVariable = () => {
  showVariableModal.value = true
}

const handleVariableInsert = (variable: string) => {
  if (!localVariables.value.includes(variable)) {
    localVariables.value.push(variable)
  }
  
  const variableTag = `{${variable}}`
  
  if (editorMode.value === 'visual' && visualEditor.value) {
    document.execCommand('insertText', false, variableTag)
    handleVisualEditorInput()
  } else {
    localContent.value += variableTag
  }
  
  showVariableModal.value = false
}

const getPageSizeClass = () => {
  switch (documentSettings.pageSize) {
    case 'letter':
      return 'w-[8.5in] min-h-[11in]'
    case 'a4':
      return 'w-[210mm] min-h-[297mm]'
    case 'legal':
      return 'w-[8.5in] min-h-[14in]'
    default:
      return 'w-[8.5in] min-h-[11in]'
  }
}

const printPreview = () => {
  const printWindow = window.open('', '_blank')
  if (printWindow) {
    printWindow.document.write(`
      <html>
        <head>
          <title>Document Preview</title>
          <style>
            body { font-family: ${documentSettings.fontFamily}; font-size: ${documentSettings.fontSize}pt; margin: 1in; }
            .watermark { position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%) rotate(-45deg); opacity: 0.1; font-size: 72pt; z-index: -1; }
          </style>
        </head>
        <body>
          ${documentSettings.enableWatermark ? '<div class="watermark">DRAFT</div>' : ''}
          ${renderedContent.value}
        </body>
      </html>
    `)
    printWindow.document.close()
    printWindow.print()
  }
}

const exportPdf = () => {
  // PDF export functionality would be implemented here
  console.log('Exporting to PDF...')
}

// Lifecycle
onMounted(() => {
  // Initialize content if empty
  if (!localContent.value && props.category?.metadata?.defaultDocumentContent) {
    localContent.value = props.category.metadata.defaultDocumentContent
  }
})
</script>

<style scoped>
[contenteditable]:empty:before {
  content: attr(data-placeholder);
  color: var(--color-green-400);
  font-style: italic;
}

.dark [contenteditable]:empty:before {
  color: var(--color-green-500);
}
</style>
