<template>
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <!-- Total Users -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
      <div class="flex items-center justify-between">
        <div>
          <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Users</p>
          <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ totalUsers }}</p>
          <div class="flex items-center mt-2">
            <Icon 
              :name="totalUsersChange >= 0 ? 'material-symbols:trending-up' : 'material-symbols:trending-down'"
              :class="[
                'h-4 w-4 mr-1',
                totalUsersChange >= 0 ? 'text-green-500' : 'text-red-500'
              ]"
            />
            <span 
              :class="[
                'text-sm font-medium',
                totalUsersChange >= 0 ? 'text-green-600' : 'text-red-600'
              ]"
            >
              {{ Math.abs(totalUsersChange) }}%
            </span>
            <span class="text-sm text-gray-500 dark:text-gray-400 ml-1">vs last month</span>
          </div>
        </div>
        <div class="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
          <Icon name="material-symbols:group" class="h-8 w-8 text-blue-600" />
        </div>
      </div>
    </div>

    <!-- Active Users -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
      <div class="flex items-center justify-between">
        <div>
          <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Active Users</p>
          <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ activeUsers }}</p>
          <div class="flex items-center mt-2">
            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div 
                class="bg-green-500 h-2 rounded-full transition-all duration-300"
                :style="{ width: `${activeUsersPercentage}%` }"
              ></div>
            </div>
            <span class="text-sm font-medium text-green-600 ml-2">
              {{ activeUsersPercentage }}%
            </span>
          </div>
        </div>
        <div class="p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
          <Icon name="material-symbols:person-check" class="h-8 w-8 text-green-600" />
        </div>
      </div>
    </div>

    <!-- Online Users -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
      <div class="flex items-center justify-between">
        <div>
          <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Online Now</p>
          <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ onlineUsers }}</p>
          <div class="flex items-center mt-2">
            <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse mr-2"></div>
            <span class="text-sm text-gray-500 dark:text-gray-400">
              {{ Math.round((onlineUsers / totalUsers) * 100) }}% of total
            </span>
          </div>
        </div>
        <div class="p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
          <Icon name="material-symbols:circle" class="h-8 w-8 text-purple-600" />
        </div>
      </div>
    </div>

    <!-- 2FA Enabled -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
      <div class="flex items-center justify-between">
        <div>
          <p class="text-sm font-medium text-gray-600 dark:text-gray-400">2FA Enabled</p>
          <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ twoFactorUsers }}</p>
          <div class="flex items-center mt-2">
            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div 
                class="bg-blue-500 h-2 rounded-full transition-all duration-300"
                :style="{ width: `${twoFactorPercentage}%` }"
              ></div>
            </div>
            <span class="text-sm font-medium text-blue-600 ml-2">
              {{ twoFactorPercentage }}%
            </span>
          </div>
        </div>
        <div class="p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
          <Icon name="material-symbols:security" class="h-8 w-8 text-yellow-600" />
        </div>
      </div>
    </div>
  </div>

  <!-- Charts Row -->
  <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
    <!-- Role Distribution Chart -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
      <div class="flex items-center justify-between mb-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Role Distribution</h3>
        <UiButton size="sm" variant="ghost">
          <Icon name="material-symbols:more-vert" class="h-4 w-4" />
        </UiButton>
      </div>
      
      <!-- Simple Donut Chart -->
      <div class="relative h-64 flex items-center justify-center">
        <svg class="w-48 h-48 transform -rotate-90" viewBox="0 0 100 100">
          <!-- Background circle -->
          <circle
            cx="50"
            cy="50"
            r="40"
            fill="none"
            stroke="currentColor"
            stroke-width="8"
            class="text-gray-200 dark:text-gray-700"
          />
          
          <!-- Role segments -->
          <circle
            v-for="(segment, index) in roleSegments"
            :key="segment.role"
            cx="50"
            cy="50"
            r="40"
            fill="none"
            :stroke="segment.color"
            stroke-width="8"
            :stroke-dasharray="`${segment.percentage * 2.51} ${251 - segment.percentage * 2.51}`"
            :stroke-dashoffset="segment.offset"
            class="transition-all duration-300 hover:stroke-width-10"
          />
        </svg>
        
        <!-- Center text -->
        <div class="absolute inset-0 flex items-center justify-center">
          <div class="text-center">
            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ totalUsers }}</p>
            <p class="text-sm text-gray-500 dark:text-gray-400">Total</p>
          </div>
        </div>
      </div>
      
      <!-- Legend -->
      <div class="mt-6 space-y-2">
        <div 
          v-for="role in roleDistribution" 
          :key="role.name"
          class="flex items-center justify-between"
        >
          <div class="flex items-center gap-2">
            <div 
              class="w-3 h-3 rounded-full"
              :style="{ backgroundColor: role.color }"
            ></div>
            <span class="text-sm font-medium text-gray-700 dark:text-gray-300">{{ role.name }}</span>
          </div>
          <div class="flex items-center gap-2">
            <span class="text-sm text-gray-500 dark:text-gray-400">{{ role.count }}</span>
            <span class="text-sm font-medium text-gray-900 dark:text-white">{{ role.percentage }}%</span>
          </div>
        </div>
      </div>
    </div>

    <!-- User Activity Chart -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
      <div class="flex items-center justify-between mb-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">User Activity</h3>
        <div class="flex items-center gap-2">
          <UiButton 
            size="sm" 
            :variant="activityPeriod === '7d' ? 'primary' : 'ghost'"
            @click="activityPeriod = '7d'"
          >
            7D
          </UiButton>
          <UiButton 
            size="sm" 
            :variant="activityPeriod === '30d' ? 'primary' : 'ghost'"
            @click="activityPeriod = '30d'"
          >
            30D
          </UiButton>
          <UiButton 
            size="sm" 
            :variant="activityPeriod === '90d' ? 'primary' : 'ghost'"
            @click="activityPeriod = '90d'"
          >
            90D
          </UiButton>
        </div>
      </div>
      
      <!-- Simple Activity Chart -->
      <div class="h-64">
        <svg class="w-full h-full" viewBox="0 0 400 200">
          <!-- Grid lines -->
          <defs>
            <pattern id="grid" width="40" height="20" patternUnits="userSpaceOnUse">
              <path d="M 40 0 L 0 0 0 20" fill="none" stroke="currentColor" stroke-width="0.5" class="text-gray-200 dark:text-gray-700"/>
            </pattern>
          </defs>
          <rect width="100%" height="100%" fill="url(#grid)" />
          
          <!-- Activity bars -->
          <g v-for="(bar, index) in activityData" :key="index">
            <rect
              :x="bar.x"
              :y="bar.y"
              :width="bar.width"
              :height="bar.height"
              :fill="bar.color"
              class="hover:opacity-80 transition-opacity duration-200"
            />
          </g>
        </svg>
      </div>
      
      <!-- Activity stats -->
      <div class="mt-4 grid grid-cols-3 gap-4">
        <div class="text-center">
          <p class="text-lg font-semibold text-gray-900 dark:text-white">{{ activityStats.logins }}</p>
          <p class="text-sm text-gray-500 dark:text-gray-400">Logins</p>
        </div>
        <div class="text-center">
          <p class="text-lg font-semibold text-gray-900 dark:text-white">{{ activityStats.sessions }}</p>
          <p class="text-sm text-gray-500 dark:text-gray-400">Sessions</p>
        </div>
        <div class="text-center">
          <p class="text-lg font-semibold text-gray-900 dark:text-white">{{ activityStats.avgDuration }}m</p>
          <p class="text-sm text-gray-500 dark:text-gray-400">Avg Duration</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import type { User } from '~/stores/user'

interface Props {
  users: User[]
}

const props = defineProps<Props>()

// State
const activityPeriod = ref<'7d' | '30d' | '90d'>('30d')

// Computed metrics
const totalUsers = computed(() => props.users.length)
const activeUsers = computed(() => props.users.filter(u => u.isActive).length)
const onlineUsers = computed(() => Math.floor(Math.random() * activeUsers.value * 0.3)) // Mock online users
const twoFactorUsers = computed(() => props.users.filter(u => u.otpEnabled).length)

const activeUsersPercentage = computed(() => 
  totalUsers.value > 0 ? Math.round((activeUsers.value / totalUsers.value) * 100) : 0
)

const twoFactorPercentage = computed(() => 
  totalUsers.value > 0 ? Math.round((twoFactorUsers.value / totalUsers.value) * 100) : 0
)

// Mock data for demo - in real app, these would come from API
const totalUsersChange = ref(8.5)

// Role distribution
const roleDistribution = computed(() => {
  const roles = props.users.reduce((acc, user) => {
    user.roles.forEach(role => {
      acc[role] = (acc[role] || 0) + 1
    })
    return acc
  }, {} as Record<string, number>)

  const colors = ['#3b82f6', '#8b5cf6', '#10b981', '#f59e0b', '#ef4444']
  
  return Object.entries(roles).map(([name, count], index) => ({
    name,
    count,
    percentage: Math.round((count / totalUsers.value) * 100),
    color: colors[index % colors.length]
  }))
})

// Role segments for donut chart
const roleSegments = computed(() => {
  let offset = 0
  return roleDistribution.value.map(role => {
    const segment = {
      role: role.name,
      percentage: role.percentage,
      color: role.color,
      offset: -offset
    }
    offset += role.percentage * 2.51
    return segment
  })
})

// Activity data (mock)
const activityData = computed(() => {
  const bars = []
  const barWidth = 30
  const maxHeight = 150
  const dataPoints = 12
  
  for (let i = 0; i < dataPoints; i++) {
    const height = Math.random() * maxHeight + 20
    bars.push({
      x: i * (barWidth + 5) + 20,
      y: 200 - height - 20,
      width: barWidth,
      height: height,
      color: '#3b82f6'
    })
  }
  
  return bars
})

const activityStats = computed(() => ({
  logins: Math.floor(Math.random() * 500) + 100,
  sessions: Math.floor(Math.random() * 300) + 50,
  avgDuration: Math.floor(Math.random() * 45) + 15
}))
</script>
