/**
 * Comprehensive API Type System
 * 
 * Centralized API types, contracts, and utilities for the entire application
 * Provides type safety for all API interactions
 */

import type {
  UUID,
  UserId,
  TenantId,
  ISODateString,
  ApiResponse,
  // PaginatedResponse, // Unused
  ApiError,
  // PaginationParams, // Unused
  SortParams,
  FilterCondition,
  FilterGroup
} from './core.js' // Added .js

// ============================================================================
// HTTP METHOD TYPES
// ============================================================================

export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE' | 'HEAD' | 'OPTIONS'

export type ContentType = 
  | 'application/json'
  | 'application/x-www-form-urlencoded'
  | 'multipart/form-data'
  | 'text/plain'
  | 'text/html'
  | 'application/xml'

// ============================================================================
// REQUEST TYPES
// ============================================================================

/**
 * Base API request configuration
 */
export interface ApiRequestConfig {
  method: HttpMethod
  url: string
  headers?: Record<string, string>
  params?: Record<string, any>
  data?: any
  timeout?: number
  retries?: number
  cache?: boolean
  cacheTTL?: number
}

/**
 * Authenticated API request
 */
export interface AuthenticatedRequest extends ApiRequestConfig {
  requiresAuth: true
  permissions?: string[]
  roles?: string[]
}

/**
 * File upload request
 */
export interface FileUploadRequest extends ApiRequestConfig {
  method: 'POST' | 'PUT'
  files: File[]
  fields?: Record<string, string>
  onProgress?: (progress: UploadProgress) => void
}

/**
 * Upload progress information
 */
export interface UploadProgress {
  loaded: number
  total: number
  percentage: number
  speed: number
  timeRemaining: number
}

// ============================================================================
// RESPONSE TYPES
// ============================================================================

/**
 * Enhanced API response with metadata
 */
export interface EnhancedApiResponse<T = any> extends ApiResponse<T> {
  status: number
  statusText: string
  headers: Record<string, string>
  config: ApiRequestConfig
  duration: number
  cached: boolean
  retryCount: number
}

/**
 * Streaming response for large data
 */
export interface StreamingResponse<T = any> {
  stream: ReadableStream<T>
  metadata: {
    totalSize?: number
    contentType: string
    encoding?: string
  }
  onChunk?: (chunk: T) => void
  onComplete?: () => void
  onError?: (error: Error) => void
}

// ============================================================================
// API CLIENT TYPES
// ============================================================================

/**
 * API client configuration
 */
export interface ApiClientConfig {
  baseURL: string
  timeout: number
  retries: number
  retryDelay: number
  headers: Record<string, string>
  interceptors: {
    request: RequestInterceptor[]
    response: ResponseInterceptor[]
  }
  cache: CacheConfig
  auth: AuthConfig
}

/**
 * Request interceptor
 */
export type RequestInterceptor = (config: ApiRequestConfig) => ApiRequestConfig | Promise<ApiRequestConfig>

/**
 * Response interceptor
 */
export type ResponseInterceptor = (response: EnhancedApiResponse) => EnhancedApiResponse | Promise<EnhancedApiResponse>

/**
 * Cache configuration
 */
export interface CacheConfig {
  enabled: boolean
  ttl: number
  maxSize: number
  strategy: CacheStrategy
  keyGenerator?: (config: ApiRequestConfig) => string
}

/**
 * Cache strategies
 */
export enum CacheStrategy {
  MEMORY = 'memory',
  LOCAL_STORAGE = 'localStorage',
  SESSION_STORAGE = 'sessionStorage',
  INDEXED_DB = 'indexedDB'
}

/**
 * Authentication configuration
 */
export interface AuthConfig {
  type: AuthType
  tokenKey: string
  refreshTokenKey: string
  tokenPrefix: string
  autoRefresh: boolean
  refreshThreshold: number
}

/**
 * Authentication types
 */
export enum AuthType {
  BEARER = 'bearer',
  BASIC = 'basic',
  API_KEY = 'apiKey',
  OAUTH2 = 'oauth2',
  CUSTOM = 'custom'
}

// ============================================================================
// ENDPOINT DEFINITION TYPES
// ============================================================================

/**
 * API endpoint definition
 */
export interface ApiEndpoint<TRequest = any, TResponse = any> {
  method: HttpMethod
  path: string
  description?: string
  tags?: string[]
  auth?: {
    required: boolean
    permissions?: string[]
    roles?: string[]
  }
  validation?: {
    request?: ValidationSchema
    response?: ValidationSchema
  }
  cache?: {
    enabled: boolean
    ttl?: number
    key?: string
  }
  rateLimit?: {
    requests: number
    window: number
  }
  examples?: {
    request?: TRequest
    response?: TResponse
  }
}

/**
 * Validation schema
 */
export interface ValidationSchema {
  type: 'object' | 'array' | 'string' | 'number' | 'boolean'
  properties?: Record<string, ValidationSchema>
  required?: string[]
  items?: ValidationSchema
  format?: string
  pattern?: string
  minimum?: number
  maximum?: number
  minLength?: number
  maxLength?: number
}

// ============================================================================
// QUERY BUILDER TYPES
// ============================================================================

/**
 * Query builder for complex API requests
 */
export interface QueryBuilder {
  select(fields: string[]): QueryBuilder
  where(condition: FilterCondition): QueryBuilder
  whereGroup(group: FilterGroup): QueryBuilder
  orderBy(field: string, direction: 'asc' | 'desc'): QueryBuilder
  limit(count: number): QueryBuilder
  offset(count: number): QueryBuilder
  include(relations: string[]): QueryBuilder
  build(): QueryParams
}

/**
 * Query parameters
 */
export interface QueryParams {
  select?: string[]
  where?: FilterGroup
  orderBy?: SortParams[]
  limit?: number
  offset?: number
  include?: string[]
  [key: string]: any
}

// ============================================================================
// BATCH OPERATION TYPES
// ============================================================================

/**
 * Batch request for multiple operations
 */
export interface BatchRequest {
  operations: BatchOperation[]
  atomic?: boolean
  continueOnError?: boolean
}

/**
 * Individual batch operation
 */
export interface BatchOperation {
  id: string
  method: HttpMethod
  path: string
  data?: any
  headers?: Record<string, string>
}

/**
 * Batch response
 */
export interface BatchResponse {
  results: BatchOperationResult[]
  success: boolean
  errors?: ApiError[]
}

/**
 * Batch operation result
 */
export interface BatchOperationResult {
  id: string
  success: boolean
  status: number
  data?: any
  error?: ApiError
}

// ============================================================================
// WEBHOOK TYPES
// ============================================================================

/**
 * Webhook configuration
 */
export interface WebhookConfig {
  id: UUID
  url: string
  events: string[]
  secret: string
  active: boolean
  headers?: Record<string, string>
  retries: number
  timeout: number
  createdAt: ISODateString
  updatedAt: ISODateString
}

/**
 * Webhook payload
 */
export interface WebhookPayload<T = any> {
  id: UUID
  event: string
  timestamp: ISODateString
  data: T
  signature: string
  attempt: number
  webhook: {
    id: UUID
    url: string
  }
}

// ============================================================================
// REAL-TIME TYPES
// ============================================================================

/**
 * WebSocket connection configuration
 */
export interface WebSocketConfig {
  url: string
  protocols?: string[]
  reconnect: boolean
  reconnectInterval: number
  maxReconnectAttempts: number
  heartbeat: boolean
  heartbeatInterval: number
  auth?: {
    token: string
    type: AuthType
  }
}

/**
 * WebSocket message
 */
export interface WebSocketMessage<T = any> {
  id: UUID
  type: string
  event: string
  data: T
  timestamp: ISODateString
  userId?: UserId
  tenantId?: TenantId
}

/**
 * Real-time subscription
 */
export interface RealtimeSubscription {
  id: UUID
  channel: string
  events: string[]
  filters?: FilterGroup
  callback: (message: WebSocketMessage) => void
  onError?: (error: Error) => void
  active: boolean
}

// ============================================================================
// ERROR HANDLING TYPES
// ============================================================================

/**
 * Enhanced API error with context
 */
export interface EnhancedApiError extends ApiError {
  status: number
  statusText: string
  url: string
  method: HttpMethod
  timestamp: ISODateString
  requestId: UUID
  userId?: UserId
  tenantId?: TenantId
  stack?: string
  context?: Record<string, any>
}

/**
 * Error recovery strategy
 */
export interface ErrorRecoveryStrategy {
  type: RecoveryType
  maxAttempts: number
  delay: number
  backoff: BackoffStrategy
  condition?: (error: EnhancedApiError) => boolean
}

/**
 * Recovery types
 */
export enum RecoveryType {
  RETRY = 'retry',
  FALLBACK = 'fallback',
  CIRCUIT_BREAKER = 'circuit_breaker',
  NONE = 'none'
}

/**
 * Backoff strategies
 */
export enum BackoffStrategy {
  LINEAR = 'linear',
  EXPONENTIAL = 'exponential',
  FIXED = 'fixed',
  RANDOM = 'random'
}

// ============================================================================
// TYPE UTILITIES
// ============================================================================

/**
 * Extract request type from endpoint
 */
export type ExtractRequest<T> = T extends ApiEndpoint<infer R, any> ? R : never

/**
 * Extract response type from endpoint
 */
export type ExtractResponse<T> = T extends ApiEndpoint<any, infer R> ? R : never

/**
 * Create typed API client
 */
export type TypedApiClient<T extends Record<string, ApiEndpoint>> = {
  [K in keyof T]: (
    request: ExtractRequest<T[K]>,
    config?: Partial<ApiRequestConfig>
  ) => Promise<ExtractResponse<T[K]>>
}
