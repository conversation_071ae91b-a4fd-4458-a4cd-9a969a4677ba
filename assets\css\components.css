/**
 * Component-specific CSS styles
 *
 * This file contains custom component styles for the Legal SaaS Frontend
 * Compatible with Tailwind CSS 4.x - No @apply directives, pure CSS
 */

/* ============================================================================
   BUTTON COMPONENTS
   ============================================================================ */

.btn {
  @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-sm border border-transparent transition-colors duration-200 focus:outline-hidden focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
}

.btn--primary {
  @apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500;
}

.btn--secondary {
  @apply bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500;
}

.btn--danger {
  @apply bg-red-600 text-white hover:bg-red-700 focus:ring-red-500;
}

.btn--ghost {
  @apply bg-transparent text-gray-700 hover:bg-gray-100 focus:ring-gray-500;
}

.btn--xs {
  @apply px-2 py-1 text-xs;
}

.btn--sm {
  @apply px-3 py-1.5 text-sm;
}

.btn--md {
  @apply px-4 py-2 text-sm;
}

.btn--lg {
  @apply px-6 py-3 text-base;
}

.btn--xl {
  @apply px-8 py-4 text-lg;
}

/* ============================================================================
   FORM COMPONENTS
   ============================================================================ */

.form-field {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.form-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: rgb(55, 65, 81);
}

.form-label--required::after {
  content: ' *';
  color: rgb(239, 68, 68);
}

.form-input {
  display: block;
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 1px solid rgb(209, 213, 219);
  border-radius: 0.375rem;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  font-size: 0.875rem;
  background-color: white;
}

.form-input::placeholder {
  color: rgb(156, 163, 175);
}

.form-input:focus {
  outline: none;
  border-color: rgb(59, 130, 246);
  box-shadow: 0 0 0 1px rgb(59, 130, 246);
}

.form-input--error {
  border-color: rgb(252, 165, 165);
  color: rgb(127, 29, 29);
}

.form-input--error::placeholder {
  color: rgb(252, 165, 165);
}

.form-input--error:focus {
  border-color: rgb(239, 68, 68);
  box-shadow: 0 0 0 1px rgb(239, 68, 68);
}

.form-input--success {
  border-color: rgb(134, 239, 172);
}

.form-input--success:focus {
  border-color: rgb(34, 197, 94);
  box-shadow: 0 0 0 1px rgb(34, 197, 94);
}

.form-error {
  font-size: 0.875rem;
  color: rgb(220, 38, 38);
}

.form-hint {
  font-size: 0.875rem;
  color: rgb(107, 114, 128);
}

/* ============================================================================
   CARD COMPONENTS
   ============================================================================ */

.card {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  border: 1px solid rgb(229, 231, 235);
}

.card__header {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid rgb(229, 231, 235);
}

.card__title {
  font-size: 1.125rem;
  font-weight: 500;
  color: rgb(17, 24, 39);
}

.card__content {
  padding: 1rem 1.5rem;
}

.card__footer {
  padding: 1rem 1.5rem;
  border-top: 1px solid rgb(229, 231, 235);
  background-color: rgb(249, 250, 251);
  border-bottom-left-radius: 0.5rem;
  border-bottom-right-radius: 0.5rem;
}

/* ============================================================================
   MODAL COMPONENTS
   ============================================================================ */

.modal {
  @apply fixed inset-0 z-50 overflow-y-auto;
}

.modal-mask {
  @apply fixed inset-0 bg-black/50 transition-opacity;
}

.modal-container {
  @apply flex min-h-full items-center justify-center p-4;
}

.modal-content {
  @apply relative bg-white rounded-md shadow-2xl transition-all max-w-lg w-full;
}

.modal--sm .modal-content {
  @apply max-w-sm;
}

.modal--md .modal-content {
  @apply max-w-md;
}

.modal--lg .modal-content {
  @apply max-w-2xl;
}

.modal--xl .modal-content {
  @apply max-w-4xl;
}

.modal--full .modal-content {
  @apply max-w-none w-full h-full rounded-none;
}

.modal__header {
  @apply px-6 py-4 border-b border-gray-200;
}

.modal__title {
  @apply text-lg font-medium text-gray-900;
}

.modal__body {
  @apply px-6 py-4;
}

.modal__footer {
  @apply px-6 py-4 border-t border-gray-200 bg-gray-50 rounded-b-lg flex justify-end space-x-3;
}

/* ============================================================================
   TABLE COMPONENTS
   ============================================================================ */

.table {
  @apply min-w-full divide-y divide-gray-200;
}

.table__header {
  @apply bg-gray-50;
}

.table__header-cell {
  @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
}

.table__body {
  @apply bg-white divide-y divide-gray-200;
}

.table__row {
  @apply hover:bg-gray-50 transition-colors duration-150;
}

.table__cell {
  @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
}

/* ============================================================================
   NAVIGATION COMPONENTS
   ============================================================================ */

.nav {
  @apply flex space-x-8;
}

.nav__item {
  @apply text-gray-500 hover:text-gray-700 px-3 py-2 rounded-sm text-sm font-medium transition-colors duration-200;
}

.nav__item--active {
  @apply text-blue-600 bg-blue-50;
}

.sidebar {
  @apply bg-white shadow-xs border-r border-gray-200;
}

.sidebar__nav {
  @apply space-y-1 px-2 py-4;
}

.sidebar__item {
  display: flex;
  align-items: center;
  padding: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: rgb(75, 85, 99);
  border-radius: 0.125rem;
  transition: all 0.2s ease-in-out;
}

.sidebar__item:hover {
  background-color: rgb(249, 250, 251);
  color: rgb(17, 24, 39);
}

.sidebar__item:hover .sidebar__icon {
  color: rgb(107, 114, 128);
}

.sidebar__item--active {
  background-color: rgb(239, 246, 255);
  color: rgb(29, 78, 216);
}

.sidebar__icon {
  color: rgb(156, 163, 175);
  margin-right: 0.75rem;
  flex-shrink: 0;
  height: 1.25rem;
  width: 1.25rem;
  transition: color 0.2s ease-in-out;
}

.sidebar__item--active .sidebar__icon {
  color: rgb(59, 130, 246);
}

/* ============================================================================
   BADGE COMPONENTS
   ============================================================================ */

.badge {
  display: inline-flex;
  align-items: center;
  padding: 0.125rem 0.625rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
}

.badge--primary {
  background-color: rgb(219, 234, 254);
  color: rgb(30, 64, 175);
}

.badge--secondary {
  background-color: rgb(243, 244, 246);
  color: rgb(31, 41, 55);
}

.badge--success {
  background-color: rgb(220, 252, 231);
  color: rgb(22, 101, 52);
}

.badge--warning {
  background-color: rgb(254, 249, 195);
  color: rgb(146, 64, 14);
}

.badge--danger {
  background-color: rgb(254, 226, 226);
  color: rgb(153, 27, 27);
}

/* ============================================================================
   LOADING COMPONENTS
   ============================================================================ */

.spinner {
  animation: spin 1s linear infinite;
  border-radius: 50%;
  border: 2px solid rgb(209, 213, 219);
  border-top-color: rgb(37, 99, 235);
}

.spinner--sm {
  height: 1rem;
  width: 1rem;
}

.spinner--md {
  height: 1.5rem;
  width: 1.5rem;
}

.spinner--lg {
  height: 2rem;
  width: 2rem;
}

.loading-overlay {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: rgba(255, 255, 255, 0.75);
  display: flex;
  align-items: center;
  justify-content: center;
}

/* ============================================================================
   ANIMATION CLASSES
   ============================================================================ */

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-enter-active,
.slide-leave-active {
  transition: transform 0.3s ease;
}

.slide-enter-from {
  transform: translateX(-100%);
}

.slide-leave-to {
  transform: translateX(100%);
}

/* ============================================================================
   UTILITY CLASSES
   ============================================================================ */

.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.scrollbar-thin {
  scrollbar-width: thin;
  scrollbar-color: rgb(156 163 175) transparent;
}

.scrollbar-thin::-webkit-scrollbar {
  width: 6px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: transparent;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background-color: rgb(156 163 175);
  border-radius: 3px;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background-color: rgb(107 114 128);
}

/* ============================================================================
   RESPONSIVE UTILITIES
   ============================================================================ */

@media (max-width: 640px) {
  .mobile-hidden {
    display: none;
  }

  .mobile-full {
    width: 100%;
  }
}

@media (min-width: 768px) {
  .tablet-visible {
    display: block;
  }
}

@media (min-width: 1024px) {
  .desktop-visible {
    display: block;
  }
}
