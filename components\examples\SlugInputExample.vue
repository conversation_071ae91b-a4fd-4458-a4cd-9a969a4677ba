<template>
  <div class="max-w-md mx-auto p-6 bg-white dark:bg-gray-800 rounded-lg shadow-md">
    <h2 class="text-2xl font-bold mb-6 text-gray-800 dark:text-gray-200">Create a New Post</h2>
    <form @submit.prevent="onSubmit">
      <div class="mb-6">
        <UiInput
          id="post-title"
          v-model="title"
          label="Post Title"
          placeholder="Enter your post title"
          required
        />
      </div>
      <div class="mb-6">
        <UiSlugInput
          id="post-slug"
          name="slug"
          label="Post Slug"
          :source="title"
          placeholder="my-awesome-post"
          required
          help-text="The slug will be auto-generated from the title."
        />
      </div>
      <button
        type="submit"
        class="w-full bg-blue-600 text-white py-3 rounded-md hover:bg-blue-700 transition-colors"
      >
        Submit
      </button>
    </form>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useForm } from 'vee-validate'
import UiInput from '~/components/ui/UiInput.vue'
import UiSlugInput from '~/components/ui/UiSlugInput.vue'

const title = ref('')

const { handleSubmit } = useForm()

const onSubmit = handleSubmit(values => {
  alert(JSON.stringify(values, null, 2))
})
</script>
