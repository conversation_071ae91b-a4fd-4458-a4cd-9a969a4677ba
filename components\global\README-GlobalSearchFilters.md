# GlobalSearchFilters Component

A comprehensive, reusable search and filtering component for the Legal SaaS platform. This component provides a consistent interface for search functionality, basic filters, advanced filters, and view toggles across all pages.

## Features

- **Search Input**: Debounced search with customizable placeholder
- **Basic Filters**: Configurable dropdown filters (status, plan, etc.)
- **Advanced Filters**: Expandable panel with date ranges, number ranges, and custom filters
- **View Toggle**: Optional table/cards view switcher
- **Clear Filters**: One-click filter reset
- **Responsive Design**: Works on all screen sizes
- **Dark Mode**: Full dark mode support
- **TypeScript**: Fully typed with proper interfaces

## Basic Usage

```vue
<template>
  <div>
    <GlobalSearchFilters
      search-placeholder="Search tenants by name, slug, or plan..."
      :filter-configs="filterConfigs"
      :advanced-filter-configs="advancedFilterConfigs"
      :show-view-toggle="true"
      @search="handleSearch"
      @filter-change="handleFilterChange"
      @view-change="handleViewChange"
    />
  </div>
</template>

<script setup lang="ts">
const filterConfigs = [
  {
    key: 'status',
    options: [
      { label: 'Active', value: 'active' },
      { label: 'Inactive', value: 'inactive' },
      { label: 'Pending', value: 'pending' }
    ],
    placeholder: 'All Status'
  },
  {
    key: 'plan',
    options: [
      { label: 'Basic', value: 'basic' },
      { label: 'Pro', value: 'pro' },
      { label: 'Enterprise', value: 'enterprise' }
    ],
    placeholder: 'All Plans'
  }
]

const advancedFilterConfigs = [
  {
    key: 'createdDate',
    type: 'dateRange',
    label: 'Created Date Range'
  },
  {
    key: 'userCount',
    type: 'numberRange',
    label: 'User Count',
    minPlaceholder: 'Min Users',
    maxPlaceholder: 'Max Users'
  },
  {
    key: 'sortBy',
    type: 'select',
    label: 'Sort By',
    placeholder: 'Default',
    options: [
      { label: 'Name A-Z', value: 'name_asc' },
      { label: 'Name Z-A', value: 'name_desc' },
      { label: 'Newest First', value: 'created_desc' },
      { label: 'Oldest First', value: 'created_asc' }
    ]
  }
]

const handleSearch = (searchTerm: string) => {
  console.log('Search:', searchTerm)
  // Implement search logic
}

const handleFilterChange = (key: string, value: any) => {
  console.log('Filter changed:', key, value)
  // Implement filter logic
}

const handleViewChange = (view: 'table' | 'cards') => {
  console.log('View changed:', view)
  // Implement view change logic
}
</script>
```

## Props

### Basic Configuration
- `searchPlaceholder` (string): Placeholder text for search input
- `searchId` (string): ID for the search input element
- `showViewToggle` (boolean): Show table/cards view toggle
- `defaultView` ('table' | 'cards'): Default view mode

### Filter Configuration
- `filterConfigs` (FilterConfig[]): Basic filter dropdown configurations
- `advancedFilterConfigs` (AdvancedFilterConfig[]): Advanced filter configurations

### Initial Values
- `initialSearch` (string): Initial search value
- `initialFilters` (Record<string, any>): Initial filter values

### Performance
- `searchDebounce` (number): Search debounce delay in milliseconds (default: 300)

## Events

- `@search`: Emitted when search value changes (debounced)
- `@filter-change`: Emitted when a single filter changes
- `@filters-change`: Emitted when any filter changes (all filters object)
- `@clear-filters`: Emitted when filters are cleared
- `@view-change`: Emitted when view mode changes
- `@advanced-filters-toggle`: Emitted when advanced filters panel is toggled

## Filter Types

### Basic Filters (FilterConfig)
```typescript
interface FilterConfig {
  key: string              // Unique identifier
  options: FilterOption[]  // Dropdown options
  placeholder: string      // Placeholder text
  class?: string          // Optional CSS classes
}
```

### Advanced Filters (AdvancedFilterConfig)

#### Date Range Filter
```typescript
{
  key: 'createdDate',
  type: 'dateRange',
  label: 'Created Date Range'
}
```

#### Number Range Filter
```typescript
{
  key: 'userCount',
  type: 'numberRange',
  label: 'User Count',
  minPlaceholder: 'Min Users',
  maxPlaceholder: 'Max Users'
}
```

#### Select Filter
```typescript
{
  key: 'category',
  type: 'select',
  label: 'Category',
  placeholder: 'All Categories',
  options: [
    { label: 'Legal', value: 'legal' },
    { label: 'Business', value: 'business' }
  ]
}
```

#### Custom Filter
```typescript
{
  key: 'custom',
  type: 'custom',
  label: 'Custom Filter'
}
```

For custom filters, use the slot:
```vue
<template #advanced-filter-custom="{ filter, value }">
  <!-- Your custom filter component -->
  <YourCustomFilter :value="value" @change="handleCustomChange" />
</template>
```

## Exposed Methods

The component exposes methods for programmatic control:

```vue
<script setup lang="ts">
const filtersRef = ref()

// Clear all filters
filtersRef.value?.clearFilters()

// Set search value
filtersRef.value?.setSearch('new search term')

// Set specific filter
filtersRef.value?.setFilter('status', 'active')

// Toggle advanced filters
filtersRef.value?.toggleAdvanced()
</script>
```

## Integration Examples

### Tenants Page
```vue
<GlobalSearchFilters
  search-placeholder="Search tenants by name, slug, or plan..."
  :filter-configs="tenantFilters"
  :advanced-filter-configs="tenantAdvancedFilters"
  :show-view-toggle="true"
  @search="searchTenants"
  @filters-change="filterTenants"
  @view-change="setViewMode"
/>
```

### Users Page
```vue
<GlobalSearchFilters
  search-placeholder="Search users by name, email, or role..."
  :filter-configs="userFilters"
  :advanced-filter-configs="userAdvancedFilters"
  @search="searchUsers"
  @filters-change="filterUsers"
/>
```

### Cases Page
```vue
<GlobalSearchFilters
  search-placeholder="Search cases by title, client, or status..."
  :filter-configs="caseFilters"
  :advanced-filter-configs="caseAdvancedFilters"
  :show-view-toggle="true"
  @search="searchCases"
  @filters-change="filterCases"
  @view-change="setCaseViewMode"
/>
```

## Styling

The component uses Tailwind CSS classes and follows the platform's design system:
- Consistent spacing and typography
- Dark mode support
- Responsive breakpoints
- Brand color integration
- Smooth transitions and animations

## Accessibility

- Proper ARIA labels and roles
- Keyboard navigation support
- Screen reader friendly
- Focus management
- Semantic HTML structure
