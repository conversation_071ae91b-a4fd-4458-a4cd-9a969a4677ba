import { ref, computed, nextTick, onMounted, onUnmounted } from 'vue'
import type { DynamicForm<PERSON>ield } from '~/app/shared/types'

export interface AccessibilityOptions {
  announceErrors?: boolean
  announceFieldChanges?: boolean
  focusFirstError?: boolean
  keyboardNavigation?: boolean
  screenReaderSupport?: boolean
}

export const useDynamicFormAccessibility = (
  fields: DynamicFormField[],
  formErrors: Record<string, string[]>,
  options: AccessibilityOptions = {}
) => {
  const defaultOptions: AccessibilityOptions = {
    announceErrors: true,
    announceFieldChanges: false,
    focusFirstError: true,
    keyboardNavigation: true,
    screenReaderSupport: true
  }

  const config = { ...defaultOptions, ...options }

  // Refs
  const ariaLiveRegion = ref<HTMLElement | null>(null)
  const focusedFieldId = ref<string | null>(null)
  const fieldElements = ref<Map<string, HTMLElement>>(new Map())

  // ARIA live region for announcements
  const createAriaLiveRegion = () => {
    if (!config.screenReaderSupport) return

    const liveRegion = document.createElement('div')
    liveRegion.setAttribute('aria-live', 'polite')
    liveRegion.setAttribute('aria-atomic', 'true')
    liveRegion.setAttribute('class', 'sr-only')
    liveRegion.style.cssText = `
      position: absolute !important;
      width: 1px !important;
      height: 1px !important;
      padding: 0 !important;
      margin: -1px !important;
      overflow: hidden !important;
      clip: rect(0, 0, 0, 0) !important;
      white-space: nowrap !important;
      border: 0 !important;
    `
    
    document.body.appendChild(liveRegion)
    ariaLiveRegion.value = liveRegion
  }

  // Announce message to screen readers
  const announce = (message: string, priority: 'polite' | 'assertive' = 'polite') => {
    if (!config.screenReaderSupport || !ariaLiveRegion.value) return

    ariaLiveRegion.value.setAttribute('aria-live', priority)
    ariaLiveRegion.value.textContent = message

    // Clear after announcement
    setTimeout(() => {
      if (ariaLiveRegion.value) {
        ariaLiveRegion.value.textContent = ''
      }
    }, 1000)
  }

  // Generate accessible field attributes
  const getFieldAriaAttributes = (field: DynamicFormField) => {
    const attributes: Record<string, string | boolean> = {}

    // Basic ARIA attributes
    attributes['aria-required'] = field.required || false
    attributes['aria-invalid'] = !!formErrors[field.name]?.length

    // Description attributes
    if (field.description) {
      attributes['aria-describedby'] = `${field.name}-description`
    }

    if (field.hint) {
      const describedBy = attributes['aria-describedby'] 
        ? `${attributes['aria-describedby']} ${field.name}-hint`
        : `${field.name}-hint`
      attributes['aria-describedby'] = describedBy
    }

    // Error attributes
    if (formErrors[field.name]?.length) {
      const describedBy = attributes['aria-describedby']
        ? `${attributes['aria-describedby']} ${field.name}-error`
        : `${field.name}-error`
      attributes['aria-describedby'] = describedBy
    }

    // Field-specific attributes
    switch (field.type) {
      case 'password':
        attributes['aria-autocomplete'] = 'current-password'
        break
      case 'email':
        attributes['aria-autocomplete'] = 'email'
        break
      case 'select':
      case 'multiselect':
        attributes['aria-haspopup'] = 'listbox'
        break
      case 'checkbox':
        if (field.options && field.options.length > 1) {
          attributes['role'] = 'group'
          attributes['aria-labelledby'] = `${field.name}-legend`
        }
        break
      case 'radio':
        attributes['role'] = 'radiogroup'
        attributes['aria-labelledby'] = `${field.name}-legend`
        break
    }

    return attributes
  }

  // Generate accessible label attributes
  const getLabelAttributes = (field: DynamicFormField) => {
    const attributes: Record<string, string> = {}

    if (field.type === 'checkbox' && field.options && field.options.length > 1) {
      attributes['id'] = `${field.name}-legend`
      attributes['role'] = 'legend'
    } else if (field.type === 'radio') {
      attributes['id'] = `${field.name}-legend`
      attributes['role'] = 'legend'
    }

    return attributes
  }

  // Focus management
  const focusField = (fieldName: string) => {
    const element = fieldElements.value.get(fieldName)
    if (element) {
      element.focus()
      focusedFieldId.value = fieldName
    }
  }

  const focusFirstError = () => {
    if (!config.focusFirstError) return

    const firstErrorField = Object.keys(formErrors).find(field => formErrors[field]?.length)
    if (firstErrorField) {
      nextTick(() => {
        focusField(firstErrorField)
        
        if (config.announceErrors) {
          const errorMessage = formErrors[firstErrorField][0]
          announce(`Error in ${getFieldLabel(firstErrorField)}: ${errorMessage}`, 'assertive')
        }
      })
    }
  }

  const focusNextField = (currentFieldName: string) => {
    const currentIndex = fields.findIndex(f => f.name === currentFieldName)
    if (currentIndex < fields.length - 1) {
      const nextField = fields[currentIndex + 1]
      focusField(nextField.name)
    }
  }

  const focusPreviousField = (currentFieldName: string) => {
    const currentIndex = fields.findIndex(f => f.name === currentFieldName)
    if (currentIndex > 0) {
      const previousField = fields[currentIndex - 1]
      focusField(previousField.name)
    }
  }

  // Keyboard navigation
  const handleKeyboardNavigation = (event: KeyboardEvent, fieldName: string) => {
    if (!config.keyboardNavigation) return

    const field = fields.find(f => f.name === fieldName)
    if (!field) return

    switch (event.key) {
      case 'ArrowDown':
        if (field.type === 'radio' || field.type === 'select') {
          // Let the field handle its own navigation
          return
        }
        event.preventDefault()
        focusNextField(fieldName)
        break

      case 'ArrowUp':
        if (field.type === 'radio' || field.type === 'select') {
          // Let the field handle its own navigation
          return
        }
        event.preventDefault()
        focusPreviousField(fieldName)
        break

      case 'Enter':
        if (field.type === 'checkbox' || field.type === 'radio') {
          // Let the field handle the action
          return
        }
        if (event.ctrlKey || event.metaKey) {
          // Ctrl+Enter or Cmd+Enter to submit form
          const form = (event.target as HTMLElement).closest('form')
          if (form) {
            form.dispatchEvent(new Event('submit', { cancelable: true }))
          }
        }
        break

      case 'Escape':
        // Clear focus or close dropdowns
        (event.target as HTMLElement).blur()
        break
    }
  }

  // Register field element
  const registerFieldElement = (fieldName: string, element: HTMLElement) => {
    fieldElements.value.set(fieldName, element)
  }

  const unregisterFieldElement = (fieldName: string) => {
    fieldElements.value.delete(fieldName)
  }

  // Get field label for announcements
  const getFieldLabel = (fieldName: string): string => {
    const field = fields.find(f => f.name === fieldName)
    return field?.label || fieldName
  }

  // Announce field changes
  const announceFieldChange = (fieldName: string, value: any) => {
    if (!config.announceFieldChanges) return

    const field = fields.find(f => f.name === fieldName)
    if (!field) return

    let message = `${getFieldLabel(fieldName)} changed`

    // Add value context for certain field types
    switch (field.type) {
      case 'checkbox':
        message += value ? ' to checked' : ' to unchecked'
        break
      case 'select':
      case 'radio':
        const option = field.options?.find(opt => opt.value === value)
        if (option) {
          message += ` to ${option.label}`
        }
        break
      case 'slider':
      case 'rating':
        message += ` to ${value}`
        break
    }

    announce(message)
  }

  // Announce validation errors
  const announceErrors = (errors: Record<string, string[]>) => {
    if (!config.announceErrors) return

    const errorCount = Object.keys(errors).length
    if (errorCount === 0) {
      announce('All form errors have been resolved')
      return
    }

    if (errorCount === 1) {
      const fieldName = Object.keys(errors)[0]
      const errorMessage = errors[fieldName][0]
      announce(`Error in ${getFieldLabel(fieldName)}: ${errorMessage}`, 'assertive')
    } else {
      announce(`Form has ${errorCount} errors. Please review and correct them.`, 'assertive')
    }
  }

  // Responsive design helpers
  const getResponsiveClasses = (field: DynamicFormField) => {
    const classes = []

    // Mobile-first responsive classes
    if (field.layout?.breakpoint) {
      Object.entries(field.layout.breakpoint).forEach(([breakpoint, layout]) => {
        if (layout.span) {
          classes.push(`${breakpoint}:col-span-${layout.span}`)
        }
        if (layout.offset) {
          classes.push(`${breakpoint}:col-start-${layout.offset + 1}`)
        }
      })
    }

    return classes
  }

  // Touch and mobile support
  const isTouchDevice = computed(() => {
    return 'ontouchstart' in window || navigator.maxTouchPoints > 0
  })

  const getMobileClasses = () => {
    return isTouchDevice.value ? ['touch-device'] : ['no-touch']
  }

  // Cleanup
  const cleanup = () => {
    if (ariaLiveRegion.value) {
      document.body.removeChild(ariaLiveRegion.value)
      ariaLiveRegion.value = null
    }
  }

  // Lifecycle
  onMounted(() => {
    createAriaLiveRegion()
  })

  onUnmounted(() => {
    cleanup()
  })

  return {
    // State
    focusedFieldId,
    isTouchDevice,

    // Methods
    getFieldAriaAttributes,
    getLabelAttributes,
    focusField,
    focusFirstError,
    focusNextField,
    focusPreviousField,
    handleKeyboardNavigation,
    registerFieldElement,
    unregisterFieldElement,
    announce,
    announceFieldChange,
    announceErrors,
    getResponsiveClasses,
    getMobileClasses,

    // Utilities
    getFieldLabel,
    cleanup
  }
}
