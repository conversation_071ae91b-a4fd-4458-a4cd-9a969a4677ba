<template>
  <div class="space-y-8">
    <!-- Notification Logs Header -->
    <div class="bg-gradient-to-r from-indigo-600 to-purple-600 rounded-xl p-8 text-white">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-3xl font-bold mb-2">Notification Logs</h1>
          <p class="text-indigo-100 text-lg">
            Monitor and analyze notification delivery, performance, and user engagement
          </p>
        </div>
        <div class="hidden md:flex items-center gap-4">
          <UiSelect
            v-model="selectedTimeRange"
            :options="timeRangeOptions"
            class="bg-white/10 border-white/20 text-white"
          />
          <UiButton @click="exportLogs" variant="outline" class="border-white/20 text-white hover:bg-white/10">
            <Icon name="material-symbols:download" class="h-4 w-4 mr-2" />
            Export
          </UiButton>
        </div>
      </div>
    </div>

    <!-- Logs Overview -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <!-- Total Notifications -->
      <UiCard
        icon="material-symbols:notifications"
        icon-color="indigo"
        :loading="isLoading"
        hover-effect
      >
        <template #header>
          <div class="flex items-center justify-between w-full">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Total Sent</h3>
            <UiBadge variant="info">{{ formatNumber(totalNotifications) }}</UiBadge>
          </div>
        </template>
        <div class="space-y-3">
          <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ formatNumber(totalNotifications) }}</p>
          <p class="text-sm text-gray-500 dark:text-gray-400">
            {{ formatNumber(todayNotifications) }} sent today
          </p>
          <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div 
              class="bg-indigo-600 h-2 rounded-full transition-all duration-300"
              :style="{ width: `${Math.min((todayNotifications / dailyTarget) * 100, 100)}%` }"
            ></div>
          </div>
        </div>
      </UiCard>

      <!-- Delivery Rate -->
      <UiCard
        icon="material-symbols:check-circle"
        icon-color="green"
        :loading="isLoading"
        hover-effect
      >
        <template #header>
          <div class="flex items-center justify-between w-full">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Delivery Rate</h3>
            <UiBadge :variant="deliveryRate >= 95 ? 'success' : deliveryRate >= 90 ? 'warning' : 'error'">
              {{ deliveryRate }}%
            </UiBadge>
          </div>
        </template>
        <div class="space-y-3">
          <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ deliveryRate }}%</p>
          <p class="text-sm text-gray-500 dark:text-gray-400">
            {{ formatNumber(successfulDeliveries) }} delivered successfully
          </p>
          <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div 
              :class="[
                'h-2 rounded-full transition-all duration-300',
                deliveryRate >= 95 ? 'bg-green-600' :
                deliveryRate >= 90 ? 'bg-yellow-600' : 'bg-red-600'
              ]"
              :style="{ width: `${deliveryRate}%` }"
            ></div>
          </div>
        </div>
      </UiCard>

      <!-- Failed Notifications -->
      <UiCard
        icon="material-symbols:error"
        icon-color="red"
        :loading="isLoading"
        hover-effect
      >
        <template #header>
          <div class="flex items-center justify-between w-full">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Failed</h3>
            <UiBadge :variant="failedNotifications > 100 ? 'error' : failedNotifications > 50 ? 'warning' : 'success'">
              {{ formatNumber(failedNotifications) }}
            </UiBadge>
          </div>
        </template>
        <div class="space-y-3">
          <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ formatNumber(failedNotifications) }}</p>
          <p class="text-sm text-gray-500 dark:text-gray-400">
            {{ failureRate }}% failure rate
          </p>
          <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div 
              class="bg-red-600 h-2 rounded-full transition-all duration-300"
              :style="{ width: `${Math.min(failureRate * 10, 100)}%` }"
            ></div>
          </div>
        </div>
      </UiCard>

      <!-- Average Response Time -->
      <UiCard
        icon="material-symbols:speed"
        icon-color="blue"
        :loading="isLoading"
        hover-effect
      >
        <template #header>
          <div class="flex items-center justify-between w-full">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Avg Response</h3>
            <UiBadge :variant="avgResponseTime <= 2 ? 'success' : avgResponseTime <= 5 ? 'warning' : 'error'">
              {{ avgResponseTime }}s
            </UiBadge>
          </div>
        </template>
        <div class="space-y-3">
          <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ avgResponseTime }}s</p>
          <p class="text-sm text-gray-500 dark:text-gray-400">
            delivery response time
          </p>
          <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div 
              :class="[
                'h-2 rounded-full transition-all duration-300',
                avgResponseTime <= 2 ? 'bg-green-600' :
                avgResponseTime <= 5 ? 'bg-yellow-600' : 'bg-red-600'
              ]"
              :style="{ width: `${Math.max(20, 100 - (avgResponseTime * 10))}%` }"
            ></div>
          </div>
        </div>
      </UiCard>
    </div>

    <!-- Filters and Search -->
    <UiCard>
      <template #header>
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Filter Logs</h3>
          <UiButton @click="clearFilters" variant="ghost" size="sm">
            <Icon name="material-symbols:clear" class="h-4 w-4 mr-2" />
            Clear Filters
          </UiButton>
        </div>
      </template>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <UiInput
          v-model="searchQuery"
          placeholder="Search notifications..."
          :loading="searching"
        >
          <template #prefix>
            <Icon name="material-symbols:search" class="h-4 w-4 text-gray-400" />
          </template>
        </UiInput>
        
        <UiSelect
          v-model="selectedStatus"
          :options="statusOptions"
          placeholder="Filter by status"
        />
        
        <UiSelect
          v-model="selectedType"
          :options="typeOptions"
          placeholder="Filter by type"
        />
        
        <UiSelect
          v-model="selectedChannel"
          :options="channelOptions"
          placeholder="Filter by channel"
        />
      </div>
    </UiCard>

    <!-- Notification Logs Table -->
    <UiCard>
      <template #header>
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Notification Logs</h3>
          <div class="flex items-center gap-2">
            <UiButton @click="refreshLogs" variant="ghost" size="sm" :loading="refreshing">
              <Icon name="material-symbols:refresh" class="h-4 w-4" />
            </UiButton>
            <UiBadge variant="info">{{ filteredLogs.length }} results</UiBadge>
          </div>
        </div>
      </template>
      
      <UiTable
        :data="paginatedLogs"
        :columns="tableColumns"
        :loading="isLoading"
        :pagination="pagination"
        @update:pagination="updatePagination"
        @row-click="viewLogDetails"
      >
        <template #status="{ row }">
          <UiBadge 
            :variant="
              row.status === 'delivered' ? 'success' :
              row.status === 'pending' ? 'warning' :
              row.status === 'failed' ? 'error' : 'neutral'
            "
            size="sm"
          >
            {{ row.status }}
          </UiBadge>
        </template>
        
        <template #type="{ row }">
          <div class="flex items-center gap-2">
            <Icon 
              :name="
                row.type === 'email' ? 'material-symbols:email' :
                row.type === 'sms' ? 'material-symbols:sms' :
                row.type === 'push' ? 'material-symbols:notifications' :
                'material-symbols:notifications'
              "
              class="h-4 w-4 text-gray-400"
            />
            <span class="capitalize">{{ row.type }}</span>
          </div>
        </template>
        
        <template #recipient="{ row }">
          <div class="max-w-xs truncate" :title="row.recipient">
            {{ row.recipient }}
          </div>
        </template>
        
        <template #timestamp="{ row }">
          <div class="text-sm">
            <div>{{ formatDate(row.timestamp) }}</div>
            <div class="text-gray-500 dark:text-gray-400">{{ formatTime(row.timestamp) }}</div>
          </div>
        </template>
        
        <template #actions="{ row }">
          <div class="flex items-center gap-1">
            <UiButton @click.stop="viewLogDetails(row)" variant="ghost" size="sm">
              <Icon name="material-symbols:visibility" class="h-4 w-4" />
            </UiButton>
            <UiButton 
              v-if="row.status === 'failed'"
              @click.stop="retryNotification(row)" 
              variant="ghost" 
              size="sm"
            >
              <Icon name="material-symbols:refresh" class="h-4 w-4" />
            </UiButton>
          </div>
        </template>
      </UiTable>
    </UiCard>

    <!-- Error Analysis -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Common Errors -->
      <UiCard>
        <template #header>
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Common Errors</h3>
            <UiBadge variant="error">{{ commonErrors.length }} types</UiBadge>
          </div>
        </template>
        <div class="space-y-3">
          <div
            v-for="error in commonErrors"
            :key="error.type"
            class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg"
          >
            <div class="flex items-center gap-3">
              <div class="w-8 h-8 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center">
                <Icon name="material-symbols:error" class="h-4 w-4 text-red-600 dark:text-red-400" />
              </div>
              <div>
                <p class="text-sm font-medium text-gray-900 dark:text-white">{{ error.type }}</p>
                <p class="text-xs text-gray-500 dark:text-gray-400">{{ error.description }}</p>
              </div>
            </div>
            <div class="text-right">
              <p class="text-sm font-medium text-gray-900 dark:text-white">{{ error.count }}</p>
              <p class="text-xs text-gray-500 dark:text-gray-400">occurrences</p>
            </div>
          </div>
        </div>
      </UiCard>

      <!-- Performance Metrics -->
      <UiCard>
        <template #header>
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Performance Metrics</h3>
            <UiButton @click="refreshMetrics" variant="ghost" size="sm" :loading="refreshingMetrics">
              <Icon name="material-symbols:refresh" class="h-4 w-4" />
            </UiButton>
          </div>
        </template>
        <div class="space-y-4">
          <div class="space-y-2">
            <div class="flex justify-between text-sm">
              <span class="text-gray-500 dark:text-gray-400">Email Delivery</span>
              <span class="text-gray-900 dark:text-white">{{ performanceMetrics.email.deliveryRate }}%</span>
            </div>
            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div class="bg-blue-600 h-2 rounded-full" :style="{ width: `${performanceMetrics.email.deliveryRate}%` }"></div>
            </div>
          </div>
          
          <div class="space-y-2">
            <div class="flex justify-between text-sm">
              <span class="text-gray-500 dark:text-gray-400">SMS Delivery</span>
              <span class="text-gray-900 dark:text-white">{{ performanceMetrics.sms.deliveryRate }}%</span>
            </div>
            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div class="bg-green-600 h-2 rounded-full" :style="{ width: `${performanceMetrics.sms.deliveryRate}%` }"></div>
            </div>
          </div>
          
          <div class="space-y-2">
            <div class="flex justify-between text-sm">
              <span class="text-gray-500 dark:text-gray-400">Push Notifications</span>
              <span class="text-gray-900 dark:text-white">{{ performanceMetrics.push.deliveryRate }}%</span>
            </div>
            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div class="bg-purple-600 h-2 rounded-full" :style="{ width: `${performanceMetrics.push.deliveryRate}%` }"></div>
            </div>
          </div>
          
          <div class="pt-2 border-t border-gray-200 dark:border-gray-700">
            <div class="grid grid-cols-2 gap-4 text-center">
              <div>
                <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ performanceMetrics.avgResponseTime }}s</p>
                <p class="text-xs text-gray-500 dark:text-gray-400">Avg Response</p>
              </div>
              <div>
                <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ performanceMetrics.throughput }}</p>
                <p class="text-xs text-gray-500 dark:text-gray-400">Per Minute</p>
              </div>
            </div>
          </div>
        </div>
      </UiCard>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { PlatformRoles } from '~/app/features/auth/constants/roles'

// Page meta configuration
definePageMeta({
  layout: 'dashboard',
  title: 'Notification Logs',
  description: 'Monitor and analyze notification delivery, performance, and user engagement',
  pageHeaderIcon: 'material-symbols:fact-check',
  pageHeaderStats: [
    { key: 'total', label: 'Total Sent', value: '45.2K', color: 'indigo' },
    { key: 'delivery', label: 'Delivery Rate', value: '98.5%', color: 'green' },
    { key: 'failed', label: 'Failed', value: '234', color: 'red' },
    { key: 'response', label: 'Avg Response', value: '1.8s', color: 'blue' }
  ],
  showRealTimeStatus: true,
  autoRefreshEnabled: true,
  refreshInterval: 30000, // 30 seconds
  middleware: ['rbac'],
  roles: [PlatformRoles.SUPER_ADMIN],
  breadcrumb: [
    { label: 'Dashboard', href: '/dashboard' },
    { label: 'Settings', href: '/dashboard/settings' },
    { label: 'Notifications', href: '/dashboard/settings/notifications' },
    { label: 'Logs' },
  ],
})

// Reactive state
const isLoading = ref(true)
const refreshing = ref(false)
const refreshingMetrics = ref(false)
const searching = ref(false)
const selectedTimeRange = ref('24h')

// Filter state
const searchQuery = ref('')
const selectedStatus = ref('')
const selectedType = ref('')
const selectedChannel = ref('')

// Pagination state
const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0
})

// Options
const timeRangeOptions = [
  { value: '1h', label: 'Last hour' },
  { value: '24h', label: 'Last 24 hours' },
  { value: '7d', label: 'Last 7 days' },
  { value: '30d', label: 'Last 30 days' }
]

const statusOptions = [
  { value: '', label: 'All Statuses' },
  { value: 'delivered', label: 'Delivered' },
  { value: 'pending', label: 'Pending' },
  { value: 'failed', label: 'Failed' }
]

const typeOptions = [
  { value: '', label: 'All Types' },
  { value: 'email', label: 'Email' },
  { value: 'sms', label: 'SMS' },
  { value: 'push', label: 'Push Notification' }
]

const channelOptions = [
  { value: '', label: 'All Channels' },
  { value: 'system', label: 'System' },
  { value: 'user', label: 'User Triggered' },
  { value: 'scheduled', label: 'Scheduled' }
]

// Metrics data
const totalNotifications = ref(45234)
const todayNotifications = ref(1567)
const dailyTarget = ref(2000)
const deliveryRate = ref(98.5)
const successfulDeliveries = ref(44567)
const failedNotifications = ref(234)
const failureRate = ref(1.5)
const avgResponseTime = ref(1.8)

// Table configuration
const tableColumns = [
  { key: 'id', label: 'ID', sortable: true },
  { key: 'type', label: 'Type', sortable: true },
  { key: 'recipient', label: 'Recipient', sortable: false },
  { key: 'subject', label: 'Subject', sortable: false },
  { key: 'status', label: 'Status', sortable: true },
  { key: 'timestamp', label: 'Sent At', sortable: true },
  { key: 'actions', label: 'Actions', sortable: false }
]

// Mock notification logs data
const notificationLogs = ref([
  {
    id: 'NTF-001',
    type: 'email',
    recipient: '<EMAIL>',
    subject: 'Case Update: Smith vs. Johnson',
    status: 'delivered',
    timestamp: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
    channel: 'system',
    responseTime: 1.2
  },
  {
    id: 'NTF-002',
    type: 'sms',
    recipient: '+1234567890',
    subject: 'Appointment reminder',
    status: 'delivered',
    timestamp: new Date(Date.now() - 1000 * 60 * 45), // 45 minutes ago
    channel: 'scheduled',
    responseTime: 0.8
  },
  {
    id: 'NTF-003',
    type: 'email',
    recipient: '<EMAIL>',
    subject: 'Document ready for review',
    status: 'failed',
    timestamp: new Date(Date.now() - 1000 * 60 * 60), // 1 hour ago
    channel: 'user',
    responseTime: null,
    error: 'Invalid email address'
  },
  {
    id: 'NTF-004',
    type: 'push',
    recipient: 'user_12345',
    subject: 'New message received',
    status: 'delivered',
    timestamp: new Date(Date.now() - 1000 * 60 * 90), // 1.5 hours ago
    channel: 'system',
    responseTime: 2.1
  },
  {
    id: 'NTF-005',
    type: 'email',
    recipient: '<EMAIL>',
    subject: 'Invoice #INV-2024-001',
    status: 'pending',
    timestamp: new Date(Date.now() - 1000 * 60 * 120), // 2 hours ago
    channel: 'system',
    responseTime: null
  }
])

// Common errors data
const commonErrors = ref([
  {
    type: 'Invalid Email',
    description: 'Email address format is invalid',
    count: 45
  },
  {
    type: 'SMTP Timeout',
    description: 'Email server connection timeout',
    count: 23
  },
  {
    type: 'SMS Gateway Error',
    description: 'SMS service provider error',
    count: 12
  },
  {
    type: 'Push Token Invalid',
    description: 'Device push token is invalid',
    count: 8
  }
])

// Performance metrics
const performanceMetrics = reactive({
  email: {
    deliveryRate: 98.2
  },
  sms: {
    deliveryRate: 99.1
  },
  push: {
    deliveryRate: 96.8
  },
  avgResponseTime: 1.8,
  throughput: 156
})

// Computed properties
const filteredLogs = computed(() => {
  let filtered = notificationLogs.value

  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(log =>
      log.id.toLowerCase().includes(query) ||
      log.recipient.toLowerCase().includes(query) ||
      log.subject.toLowerCase().includes(query)
    )
  }

  if (selectedStatus.value) {
    filtered = filtered.filter(log => log.status === selectedStatus.value)
  }

  if (selectedType.value) {
    filtered = filtered.filter(log => log.type === selectedType.value)
  }

  if (selectedChannel.value) {
    filtered = filtered.filter(log => log.channel === selectedChannel.value)
  }

  return filtered
})

const paginatedLogs = computed(() => {
  const start = (pagination.page - 1) * pagination.pageSize
  const end = start + pagination.pageSize
  pagination.total = filteredLogs.value.length
  return filteredLogs.value.slice(start, end)
})

// Utility functions
const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

const formatDate = (date: Date): string => {
  return date.toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric'
  })
}

const formatTime = (date: Date): string => {
  return date.toLocaleTimeString('en-US', {
    hour: '2-digit',
    minute: '2-digit'
  })
}

// Methods
const updatePagination = (newPagination: any) => {
  Object.assign(pagination, newPagination)
}

const clearFilters = () => {
  searchQuery.value = ''
  selectedStatus.value = ''
  selectedType.value = ''
  selectedChannel.value = ''
  pagination.page = 1
}

const refreshLogs = async () => {
  try {
    refreshing.value = true

    // Simulate API call to refresh logs
    await new Promise(resolve => setTimeout(resolve, 1000))

    // In a real app, you would fetch fresh logs
    // const logs = await $api.get('/notifications/logs', { timeRange: selectedTimeRange.value })

    console.log('Notification logs refreshed')

  } catch (error) {
    console.error('Error refreshing logs:', error)
  } finally {
    refreshing.value = false
  }
}

const refreshMetrics = async () => {
  try {
    refreshingMetrics.value = true

    // Simulate API call to refresh metrics
    await new Promise(resolve => setTimeout(resolve, 1000))

    // In a real app, you would fetch fresh metrics
    // const metrics = await $api.get('/notifications/metrics')

    // Simulate some updates
    performanceMetrics.email.deliveryRate = Math.max(95, Math.min(100, performanceMetrics.email.deliveryRate + (Math.random() - 0.5) * 2))
    performanceMetrics.sms.deliveryRate = Math.max(95, Math.min(100, performanceMetrics.sms.deliveryRate + (Math.random() - 0.5) * 2))
    performanceMetrics.push.deliveryRate = Math.max(90, Math.min(100, performanceMetrics.push.deliveryRate + (Math.random() - 0.5) * 3))

    console.log('Performance metrics refreshed')

  } catch (error) {
    console.error('Error refreshing metrics:', error)
  } finally {
    refreshingMetrics.value = false
  }
}

const viewLogDetails = (log: any) => {
  console.log('Viewing log details:', log.id)
  // In a real app, you would open a modal or navigate to details page
  // navigateTo(`/dashboard/settings/notifications/logs/${log.id}`)
}

const retryNotification = async (log: any) => {
  try {
    console.log('Retrying notification:', log.id)

    // In a real app, you would retry the notification
    // await $api.post(`/notifications/${log.id}/retry`)

    // Update the log status
    log.status = 'pending'

    alert(`Notification ${log.id} queued for retry`)

  } catch (error) {
    console.error('Error retrying notification:', error)
    alert('Failed to retry notification')
  }
}

const exportLogs = async () => {
  try {
    console.log('Exporting notification logs...')

    // Create export data
    const exportData = {
      logs: filteredLogs.value,
      metrics: {
        totalNotifications: totalNotifications.value,
        deliveryRate: deliveryRate.value,
        failedNotifications: failedNotifications.value,
        avgResponseTime: avgResponseTime.value
      },
      timeRange: selectedTimeRange.value,
      exportedAt: new Date().toISOString()
    }

    const jsonContent = JSON.stringify(exportData, null, 2)
    const blob = new Blob([jsonContent], { type: 'application/json' })
    const url = URL.createObjectURL(blob)

    const link = document.createElement('a')
    link.href = url
    link.download = `notification-logs-${selectedTimeRange.value}-${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    URL.revokeObjectURL(url)
    console.log('Notification logs exported successfully')

  } catch (error) {
    console.error('Error exporting logs:', error)
  }
}

// Watchers
watch(searchQuery, () => {
  searching.value = true
  setTimeout(() => {
    searching.value = false
  }, 500)
})

// Lifecycle hooks
onMounted(async () => {
  // Initial data load
  isLoading.value = false
  console.log('Notification logs loaded')
})
</script>

<style scoped>
/* Enhanced animations and transitions */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* Table row hover effects */
.table-row:hover {
  background-color: rgb(249 250 251);
}

.dark .table-row:hover {
  background-color: rgb(55 65 81);
}

/* Progress bar animations */
.transition-all {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .grid-cols-1 {
    gap: 1rem;
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
</style>
