<template>
  <div class="space-y-6 py-6">
    <!-- Global Error State -->
    <div v-if="userStore.error && !userStore.hasUsers && !userStore.hasOverviewData" class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6">
      <div class="flex items-center">
        <Icon name="material-symbols:error" class="h-6 w-6 text-red-500 mr-3" />
        <div>
          <h3 class="text-lg font-medium text-red-800 dark:text-red-200">{{ $t('userManagement.unableToLoadUserData') }}</h3>
          <p class="mt-1 text-sm text-red-700 dark:text-red-300">{{ userStore.error }}</p>
        </div>
      </div>
      <div class="mt-4">
        <UiButton @click="retryDataFetch" variant="outline" class="border-red-300 text-red-700 hover:bg-red-50">
          <Icon name="material-symbols:refresh" class="h-4 w-4 mr-2" />
          {{ $t('userManagement.retryLoadingData') }}
        </UiButton>
      </div>
    </div>

    <!-- User Overview Dashboard -->
    <UserOverviewDashboard
      v-else
      :loading="userStore.isAnyLoading"
      @create-user="handleCreateUser"
      @view-all="handleViewAllUsers"
    />



    <!-- Recent Activity -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Recent Users -->
      <UiCard :title="$t('userManagement.recentUsers')" icon="material-symbols:history" icon-color="yellow" :loading="userStore.isAnyLoading" hover-effect >
        <template #header>
          <div class="flex items-center justify-between">
            <UiButton @click="handleViewAllUsers" variant="ghost" size="sm">
              View All
            </UiButton>
          </div>
        </template>

        <div class="space-y-3">
          <div
            v-for="user in recentUsers"
            :key="user.id"
            class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors duration-200 cursor-pointer"
            @click="handleUserAction({ action: 'view', item: user })"
          >
            <div class="flex items-center gap-3">
              <div class="flex-shrink-0">
                <img
                  v-if="user.avatar"
                  :src="user.avatar"
                  :alt="`${user.name} avatar`"
                  class="w-10 h-10 rounded-full object-cover"
                />
                <div
                  v-else
                  class="w-10 h-10 rounded-full bg-brandPrimary/10 flex items-center justify-center"
                >
                  <Icon name="material-symbols:person" size="calc(var(--spacing) * 5)" class=" text-brandPrimary" />
                </div>
              </div>
              <div>
                <p class="font-medium text-gray-900 dark:text-white">{{ user.name }}</p>
                <p class="text-sm text-gray-500 dark:text-gray-400">{{ user.email }}</p>
              </div>
            </div>
            <div class="flex items-center gap-2">
              <UiBadge :variant="user.isActive ? 'success' : 'error'">
                {{ user.isActive ? 'Active' : 'Inactive' }}
              </UiBadge>
              <Icon name="material-symbols:chevron-right" size="calc(var(--spacing) * 5)" class=" text-gray-400" />
            </div>
          </div>

          <div v-if="recentUsers.length === 0" class="text-center py-6">
            <Icon name="material-symbols:person-outline" size="calc(var(--spacing) * 8)" class=" text-gray-400 mx-auto mb-2" />
            <p class="text-sm text-gray-500 dark:text-gray-400">No recent users</p>
          </div>
        </div>
      </UiCard>

      <!-- User Activity -->
      <UiCard :title="$t('userManagement.userActivity')" icon="material-symbols:timeline" icon-color="blue" :loading="userStore.isAnyLoading" hover-effect>
        <template #header>
          <div class="flex items-center justify-between">
            <UiButton @click="handleViewActivity" variant="ghost" size="sm">
              View All
            </UiButton>
          </div>
        </template>

        <div class="space-y-3">
          <div
            v-for="activity in recentActivity"
            :key="activity.id"
            class="flex items-start gap-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg"
          >
            <div class="flex-shrink-0">
              <div class="w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900/20 flex items-center justify-center">
                <Icon :name="activity.icon" size="calc(var(--spacing) * 5)" class=" text-blue-600 dark:text-blue-400" />
              </div>
            </div>
            <div class="flex-1 min-w-0">
              <p class="text-sm text-gray-900 dark:text-white">{{ activity.description }}</p>
              <p class="text-xs text-gray-500 dark:text-gray-400">{{ activity.timestamp }}</p>
            </div>
          </div>

          <div v-if="recentActivity.length === 0" class="text-center py-6">
            <Icon name="material-symbols:timeline" size="calc(var(--spacing) * 8)" class=" text-gray-400 mx-auto mb-2" />
            <p class="text-sm text-gray-500 dark:text-gray-400">No recent activity</p>
          </div>
        </div>
      </UiCard>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from '#app'
import { useUserStore, type User } from '~/stores/user'
import { PlatformRoles } from '~/app/features/auth/constants/roles'
import { useDateFormatters } from '~/composables/useDateFormatters'

const dateFormatters = useDateFormatters()

// Components
import UserOverviewDashboard from '~/components/users/UserOverviewDashboard.vue'

// Composables
const { t: $t } = useI18n()

definePageMeta({
  title: 'User Overview',
  description: 'Overview and quick access to user management features',
  showPageHeader: true,
  showPageHeaderTitle: true,
  pageHeaderIcon: 'material-symbols:group',
  pageHeaderActions: () => {
    return ([
       {
        label: 'Invite User',
        icon: 'i-heroicons:user-plus',
        color: 'primary',
        variant: 'outline',
        click: () => navigateTo('/dashboard/users/invite')
      },
      {
        label: 'Manage Users',
        icon: 'i-heroicons:users',
        color: 'secondary',
        variant: 'outline',
        click: () => navigateTo('/dashboard/users/manage')
      },
      {
        label: 'View Reports',
        icon: 'i-heroicons:chart-bar',
        color: 'secondary',
        variant: 'outline',
        click: () => navigateTo('/dashboard/users/analytics')
      },
    ]).reverse()
  },
  layout: 'dashboard',
  middleware: ['rbac'],
  roles: [PlatformRoles.SUPER_ADMIN],
  breadcrumbs: [
    { label: 'Dashboard', href: '/dashboard' },
    { label: 'Users' },
  ],
})

// Stores and composables
const userStore = useUserStore()
const router = useRouter()

// Computed properties
const recentUsers = computed(() => {
  // Use recent users from overview API if available, otherwise fallback to sorted users list
  if (userStore.recentUsers && userStore.recentUsers.length > 0) {
    return userStore.recentUsers.slice(0, 5)
  }

  return userStore.users
    .slice()
    .sort((a: any, b: any) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
    .slice(0, 5)
})

const recentActivity = computed(() => {
  return userStore.userActivity.slice(0, 5).map((activity: any) => ({
    id: activity.id,
    icon: 'material-symbols:person',
    description: activity.description,
    timestamp: dateFormatters.formatRelativeTime(activity.createdAt)
  }))
})

// Methods
const handleCreateUser = () => {
  router.push('/dashboard/platform/users/create')
}

const handleViewAllUsers = () => {
  router.push('/dashboard/platform/users/manage')
}

const handleViewAnalytics = () => {
  router.push('/dashboard/platform/users/analytics')
}

const handleViewRoles = () => {
  router.push('/dashboard/platform/users/roles')
}

const handleViewActivity = () => {
  router.push('/dashboard/platform/users/activity')
}

const handleUserAction = (payload: any) => {
  const action = payload.action || payload.key
  const user = payload.user || payload.item || payload.row

  if (!user || !action) {
    console.error('Invalid action payload:', payload)
    return
  }

  switch (action) {
    case 'view':
      router.push(`/dashboard/platform/users/${user.id}`)
      break
    case 'edit':
      router.push(`/dashboard/platform/users/${user.id}/edit`)
      break
    case 'permissions':
      router.push(`/dashboard/platform/users/${user.id}/permissions`)
      break
    default:
      console.warn('Unknown action:', action)
  }
}

// Error handling
const retryDataFetch = async () => {
  try {
    // Clear previous errors
    userStore.error = null
    userStore.overviewError = null

    // Retry fetching data
    await Promise.all([
      userStore.fetchUserOverview(),
      userStore.fetchAllUsers(),
      userStore.fetchRecentUsers(10)
    ])
  } catch (error) {
    console.error('Failed to retry data fetch:', error)
  }
}

// Lifecycle hooks
onMounted(async () => {
  // Fetch user overview data first for dashboard metrics
  if (!userStore.hasOverviewData) {
    await userStore.fetchUserOverview()
  }

  // Fetch all users for detailed management if needed
  if (!userStore.hasUsers) {
    await userStore.fetchAllUsers()
  }

  // Fetch recent users for the activity section
  if (userStore.recentUsers.length === 0) {
    await userStore.fetchRecentUsers(10)
  }

  // Fetch recent activity for the activity section
  if (userStore.userActivity.length === 0) {
    await userStore.fetchUserActivity()
  }

  console.log(userStore.userActivity);
  
})
</script>


<style scoped>
/* Enhanced animations and transitions */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* Custom scrollbar for advanced filters */
.overflow-hidden {
  scrollbar-width: thin;
  scrollbar-color: var(--theme-gray-400) var(--theme-gray-100);
}

.overflow-hidden::-webkit-scrollbar {
  width: 6px;
}

.overflow-hidden::-webkit-scrollbar-track {
  background: var(--theme-gray-100);
  border-radius: 3px;
}

.overflow-hidden::-webkit-scrollbar-thumb {
  background: var(--theme-gray-400);
  border-radius: 3px;
}

.overflow-hidden::-webkit-scrollbar-thumb:hover {
  background: var(--theme-gray-500);
}

/* Enhanced hover effects */
.group:hover .group-hover\:scale-105 {
  transform: scale(1.05);
}

/* Loading shimmer effect */
@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.animate-pulse {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
  .animate-pulse {
    background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
    background-size: 200px 100%;
  }
}

/* Status indicator animations */
@keyframes pulse-green {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse-green {
  animation: pulse-green 2s infinite;
}

/* Role badge hover effects */
.role-badge {
  transition: all 0.2s ease-in-out;
}

.role-badge:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* User card hover effects */
.user-card {
  transition: all 0.3s ease-in-out;
}

.user-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* Responsive grid adjustments */
@media (max-width: 640px) {
  .grid-cols-1 {
    gap: 1rem;
  }
}

@media (min-width: 768px) {
  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

@media (min-width: 1024px) {
  .lg\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
}

@media (min-width: 1280px) {
  .xl\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}

/* Focus states for accessibility */
.focus-ring:focus {
  outline: 2px solid var(--theme-brand-primary);
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .border-gray-200 {
    border-color: #000;
  }

  .text-gray-500 {
    color: #000;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
</style>