<template>
   <div >
 

    <div v-if="isImpersonating" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
      <strong class="font-bold">IMPERSONATION ACTIVE: </strong>
      <span class="block sm:inline">You are currently impersonating <span class="font-semibold">{{ impersonatedUserName }}</span>.</span>
      <UiButton @click="exitImpersonation" :disabled="isLoading" variant="danger" size="sm" class="ml-4">
        {{ isLoading ? 'Exiting...' : 'Exit Impersonation' }}
      </UiButton>
    </div>

    <div class="bg-white shadow-md rounded-lg p-6">
      <p class="text-gray-700 mb-4">
        As a `SUPER_ADMIN`, you can impersonate other users to assist with support or administrative tasks.
        Enter the User ID or Email of the user you wish to impersonate.
      </p>

      <form @submit.prevent="initiateImpersonation" class="space-y-4">
        <div>
          <label for="userId" class="block text-sm font-medium text-gray-700">User ID</label>
          <UiAutocomplete 
            id="userId"
            name="userId"
            v-model="userIdToImpersonate"
            api-url="/users" 
            value-key="id" 
            label-key="name"
            placeholder="Search user..."
            ></UiAutocomplete>
          
        </div>
        <div class="text-center text-gray-500">- OR -</div>
        <div>
          <label for="userEmail" class="block text-sm font-medium text-gray-700">User Email</label>
          <UiInput
            id="userEmail"
            name="userEmail"
            v-model="userEmailToImpersonate"
            type="email"
            placeholder="<EMAIL>"
            class="mt-1 block w-full"
            :disabled="isLoading || isImpersonating"
          />
        </div>

        <UiButton
          type="submit"
          :disabled="isLoading || isImpersonating || (!userIdToImpersonate && !userEmailToImpersonate)"
          variant="primary"
          class="w-full"
        >
          {{ isLoading ? 'Initiating...' : 'Impersonate User' }}
        </UiButton>
      </form>
    </div>

    <div class="mt-8 bg-blue-50 border-l-4 border-blue-400 text-blue-800 p-4" role="alert">
      <p class="font-bold">Important Security Note:</p>
      <p class="text-sm">Impersonating a user grants you their exact permissions and access rights. All actions performed during an impersonation session will be logged against the impersonated user, with an audit trail linking back to your `SUPER_ADMIN` account. Always exit impersonation when your task is complete.</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useRouter } from 'vue-router';
import { useAuthStore } from '~/stores/auth'; // Assuming Pinia or Vuex store for auth
import { useToast } from '~/composables/useToast'; // Custom composable for toasts
import type { User } from '~/types/auth'; // Assuming a user type definition

import { PlatformRoles, TenantRoles } from'~/app/features/auth/constants/roles';

definePageMeta({
  layout: 'dashboard',
  title: 'User Impersonation',
  subtitle: 'Manage user impersonation sessions for administrative tasks.',
  middleware: ['rbac'],
  roles: [PlatformRoles.SUPER_ADMIN],
  breadcrumb: [
    { label: 'Dashboard', href: '/dashboard' },
    { label: 'Settings', href: '/dashboard/settings' },
    { label: 'User Impersonation' },
  ],
});


const authStore = useAuthStore();
const router = useRouter();
const { showToast } = useToast();

const userIdToImpersonate = ref<string>('');
const userEmailToImpersonate = ref<string>('');
const isLoading = ref<boolean>(false);

const isImpersonating = computed(() => authStore.isImpersonating); // Assuming store tracks this
const impersonatedUserName = computed(() => authStore.impersonatedUser?.fullName || authStore.impersonatedUser?.email); // Assuming store holds impersonated user details

const searchUsers = async (query: string): Promise<User[]> => {
  if (!query) return [];
  // This would typically call a backend API to search users by ID or email
  // For demonstration, let's mock it or assume a direct user fetch for known IDs/emails
  try {
    const { data } = await useFetch(`/users/email/${query}`); // Or /users/${query} for ID
    if (data.value) {
      return [data.value as User]; // Assuming the endpoint returns a single user
    }
    // Alternatively, a search endpoint might return an array
    // const { data } = await useFetch(`/users?search=${query}`);
    // return data.value as User[];
  } catch (error) {
    console.error('Error searching user:', error);
    showToast('Failed to search user. Check console.', 'error');
    return [];
  }
  return [];
};

const initiateImpersonation = async () => {
  if (!userIdToImpersonate.value && !userEmailToImpersonate.value) {
    showToast('Please enter a User ID or Email to impersonate.', 'warning');
    return;
  }

  isLoading.value = true;
  try {
    // First, find the user ID if only email is provided
    let targetUserId = userIdToImpersonate.value;
    if (!targetUserId && userEmailToImpersonate.value) {
      const users = await searchUsers(userEmailToImpersonate.value);
      if (users.length > 0) {
        targetUserId = users[0].id;
      } else {
        showToast('User not found with provided email.', 'error');
        isLoading.value = false;
        return;
      }
    }

    if (!targetUserId) {
      showToast('No user ID resolved for impersonation.', 'error');
      isLoading.value = false;
      return;
    }

    // Call the backend impersonation endpoint
    const { data, error } = await useFetch(`/users/${targetUserId}/impersonate`, {
      method: 'POST',
      body: {}, // Backend might expect an empty body or specific details
    });

    if (error.value) {
      console.error('Impersonation failed:', error.value);
      showToast(`Impersonation failed: ${error.value.data?.message || error.value.message}`, 'error');
      return;
    }

    if (data.value && data.value.accessToken && data.value.refreshToken) {
      // Assuming the backend returns new tokens for the impersonated session
      authStore.setImpersonationTokens(data.value.accessToken, data.value.refreshToken, data.value.impersonatedUser); // Store impersonated user details
      showToast(`Successfully impersonating user: ${impersonatedUserName.value}`, 'success');
      // Redirect to a default dashboard page for the impersonated user
      router.push('/dashboard');
    } else {
      showToast('Impersonation successful, but no tokens received. Please check backend response.', 'warning');
    }

  } catch (e) {
    console.error('Impersonation error:', e);
    showToast('An unexpected error occurred during impersonation.', 'error');
  } finally {
    isLoading.value = false;
  }
};

const exitImpersonation = async () => {
  if (!isImpersonating.value) {
    showToast('Not currently impersonating.', 'info');
    return;
  }
  isLoading.value = true;
  try {
    // Call a backend endpoint to invalidate the impersonated session if necessary,
    // or simply clear client-side state. A dedicated backend endpoint is safer.
    // For simplicity, we'll just clear client-side here and rely on original token to be restored/used.
    // await useFetch('/auth/exit-impersonation', { method: 'POST' }); // Hypothetical endpoint

    authStore.exitImpersonation(); // Restore original tokens
    showToast('Exited impersonation session.', 'info');
    router.push('/dashboard/platform/impersonate'); // Redirect back to this page or a safe admin page
  } catch (e) {
    console.error('Error exiting impersonation:', e);
    showToast('Failed to exit impersonation. Please try logging out.', 'error');
  } finally {
    isLoading.value = false;
  }
};
</script>

<style scoped>
/* Page-specific styles can go here */
</style>