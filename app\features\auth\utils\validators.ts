/**
 * Authentication Validators
 * 
 * Utility functions for validating authentication-related data
 * with comprehensive validation rules and error messages
 */

import { VALIDATION_PATTERNS, PASSWORD_REQUIREMENTS } from '../constants/auth.js' // Added .js
import type { PasswordStrength } from '../types/ui.js' // Added .js

// Email validation
export const validateEmail = (email: string): { isValid: boolean; error?: string } => {
  if (!email || email.trim() === '') {
    return { isValid: false, error: 'Email is required' }
  }

  if (!VALIDATION_PATTERNS.EMAIL.test(email)) {
    return { isValid: false, error: 'Please enter a valid email address' }
  }

  if (email.length > 254) {
    return { isValid: false, error: 'Email address is too long' }
  }

  return { isValid: true }
}

// Password validation
export const validatePassword = (password: string): { isValid: boolean; errors: string[] } => {
  const errors: string[] = []

  if (!password || password.trim() === '') {
    errors.push('Password is required')
    return { isValid: false, errors }
  }

  if (password.length < PASSWORD_REQUIREMENTS.MIN_LENGTH) {
    errors.push(`Password must be at least ${PASSWORD_REQUIREMENTS.MIN_LENGTH} characters long`)
  }

  if (password.length > PASSWORD_REQUIREMENTS.MAX_LENGTH) {
    errors.push(`Password must not exceed ${PASSWORD_REQUIREMENTS.MAX_LENGTH} characters`)
  }

  if (PASSWORD_REQUIREMENTS.REQUIRE_UPPERCASE && !/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter')
  }

  if (PASSWORD_REQUIREMENTS.REQUIRE_LOWERCASE && !/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter')
  }

  if (PASSWORD_REQUIREMENTS.REQUIRE_NUMBERS && !/\d/.test(password)) {
    errors.push('Password must contain at least one number')
  }

  if (PASSWORD_REQUIREMENTS.REQUIRE_SPECIAL_CHARS) {
    const specialCharsRegex = new RegExp(`[${PASSWORD_REQUIREMENTS.SPECIAL_CHARS.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}]`)
    if (!specialCharsRegex.test(password)) {
      errors.push('Password must contain at least one special character')
    }
  }

  return { isValid: errors.length === 0, errors }
}

// Password strength calculation
export const calculatePasswordStrength = (password: string): PasswordStrength => {
  if (!password) {
    return {
      score: 0,
      level: 'weak',
      feedback: ['Password is required'],
      requirements: {
        minLength: false,
        hasUppercase: false,
        hasLowercase: false,
        hasNumbers: false,
        hasSpecialChars: false
      }
    }
  }

  const requirements = {
    minLength: password.length >= PASSWORD_REQUIREMENTS.MIN_LENGTH,
    hasUppercase: /[A-Z]/.test(password),
    hasLowercase: /[a-z]/.test(password),
    hasNumbers: /\d/.test(password),
    hasSpecialChars: new RegExp(`[${PASSWORD_REQUIREMENTS.SPECIAL_CHARS.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}]`).test(password)
  }

  let score = 0
  const feedback: string[] = []

  // Length scoring
  if (password.length >= 8) score += 1
  if (password.length >= 12) score += 1
  if (password.length >= 16) score += 1

  // Character variety scoring
  if (requirements.hasUppercase) score += 1
  if (requirements.hasLowercase) score += 1
  if (requirements.hasNumbers) score += 1
  if (requirements.hasSpecialChars) score += 1

  // Additional complexity checks
  if (password.length >= 12 && Object.values(requirements).filter(Boolean).length >= 3) {
    score += 1
  }

  // Penalty for common patterns
  if (/(.)\1{2,}/.test(password)) {
    score -= 1
    feedback.push('Avoid repeating characters')
  }

  if (/123|abc|qwe|password|admin/i.test(password)) {
    score -= 2
    feedback.push('Avoid common patterns and words')
  }

  // Normalize score to 0-4 range
  score = Math.max(0, Math.min(4, score))

  // Generate feedback
  if (!requirements.minLength) {
    feedback.push(`Use at least ${PASSWORD_REQUIREMENTS.MIN_LENGTH} characters`)
  }
  if (!requirements.hasUppercase) {
    feedback.push('Add uppercase letters')
  }
  if (!requirements.hasLowercase) {
    feedback.push('Add lowercase letters')
  }
  if (!requirements.hasNumbers) {
    feedback.push('Add numbers')
  }
  if (!requirements.hasSpecialChars) {
    feedback.push('Add special characters')
  }

  // Determine level
  let level: PasswordStrength['level']
  if (score <= 1) level = 'weak'
  else if (score <= 2) level = 'fair'
  else if (score <= 3) level = 'good'
  else level = 'strong'

  return { score, level, feedback, requirements }
}

// Name validation
export const validateName = (name: string, fieldName = 'Name'): { isValid: boolean; error?: string } => {
  if (!name || name.trim() === '') {
    return { isValid: false, error: `${fieldName} is required` }
  }

  if (name.trim().length < 2) {
    return { isValid: false, error: `${fieldName} must be at least 2 characters long` }
  }

  if (name.length > 50) {
    return { isValid: false, error: `${fieldName} must not exceed 50 characters` }
  }

  if (!/^[a-zA-Z\s'-]+$/.test(name)) {
    return { isValid: false, error: `${fieldName} can only contain letters, spaces, hyphens, and apostrophes` }
  }

  return { isValid: true }
}

// Phone validation
export const validatePhone = (phone: string): { isValid: boolean; error?: string } => {
  if (!phone || phone.trim() === '') {
    return { isValid: true } // Phone is optional
  }

  if (!VALIDATION_PATTERNS.PHONE.test(phone)) {
    return { isValid: false, error: 'Please enter a valid phone number' }
  }

  return { isValid: true }
}

// Two-factor token validation
export const validate2FAToken = (token: string): { isValid: boolean; error?: string } => {
  if (!token || token.trim() === '') {
    return { isValid: false, error: '2FA token is required' }
  }

  if (!VALIDATION_PATTERNS.TWO_FACTOR_TOKEN.test(token)) {
    return { isValid: false, error: '2FA token must be 6 digits' }
  }

  return { isValid: true }
}

// Backup code validation
export const validateBackupCode = (code: string): { isValid: boolean; error?: string } => {
  if (!code || code.trim() === '') {
    return { isValid: false, error: 'Backup code is required' }
  }

  if (!VALIDATION_PATTERNS.BACKUP_CODE.test(code.toUpperCase())) {
    return { isValid: false, error: 'Backup code must be 8 characters (letters and numbers)' }
  }

  return { isValid: true }
}

// Password confirmation validation
export const validatePasswordConfirmation = (
  password: string, 
  confirmPassword: string
): { isValid: boolean; error?: string } => {
  if (!confirmPassword || confirmPassword.trim() === '') {
    return { isValid: false, error: 'Please confirm your password' }
  }

  if (password !== confirmPassword) {
    return { isValid: false, error: 'Passwords do not match' }
  }

  return { isValid: true }
}

// Terms acceptance validation
export const validateTermsAcceptance = (accepted: boolean): { isValid: boolean; error?: string } => {
  if (!accepted) {
    return { isValid: false, error: 'You must accept the terms and conditions' }
  }

  return { isValid: true }
}

// Age validation (for registration)
export const validateAge = (birthDate: string): { isValid: boolean; error?: string } => {
  if (!birthDate) {
    return { isValid: false, error: 'Birth date is required' }
  }

  const birth = new Date(birthDate)
  const today = new Date()
  let age = today.getFullYear() - birth.getFullYear()
  const monthDiff = today.getMonth() - birth.getMonth()

  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
    age--
  }

  if (age < 13) {
    return { isValid: false, error: 'You must be at least 13 years old to register' }
  }

  if (age > 120) {
    return { isValid: false, error: 'Please enter a valid birth date' }
  }

  return { isValid: true }
}

// URL validation (for profile links)
export const validateUrl = (url: string): { isValid: boolean; error?: string } => {
  if (!url || url.trim() === '') {
    return { isValid: true } // URL is optional
  }

  try {
    new URL(url)
    return { isValid: true }
  } catch {
    return { isValid: false, error: 'Please enter a valid URL' }
  }
}

// Timezone validation
export const validateTimezone = (timezone: string): { isValid: boolean; error?: string } => {
  if (!timezone) {
    return { isValid: true } // Timezone is optional
  }

  try {
    Intl.DateTimeFormat(undefined, { timeZone: timezone })
    return { isValid: true }
  } catch {
    return { isValid: false, error: 'Invalid timezone' }
  }
}

// Composite validation for registration form
export const validateRegistrationForm = (data: {
  firstName: string
  lastName: string
  email: string
  password: string
  confirmPassword: string
  phone?: string
  acceptTerms: boolean
}): { isValid: boolean; errors: Record<string, string> } => {
  const errors: Record<string, string> = {}

  const firstNameValidation = validateName(data.firstName, 'First name')
  if (!firstNameValidation.isValid) {
    errors.firstName = firstNameValidation.error!
  }

  const lastNameValidation = validateName(data.lastName, 'Last name')
  if (!lastNameValidation.isValid) {
    errors.lastName = lastNameValidation.error!
  }

  const emailValidation = validateEmail(data.email)
  if (!emailValidation.isValid) {
    errors.email = emailValidation.error!
  }

  const passwordValidation = validatePassword(data.password)
  if (!passwordValidation.isValid) {
    errors.password = passwordValidation.errors[0]
  }

  const confirmPasswordValidation = validatePasswordConfirmation(data.password, data.confirmPassword)
  if (!confirmPasswordValidation.isValid) {
    errors.confirmPassword = confirmPasswordValidation.error!
  }

  if (data.phone) {
    const phoneValidation = validatePhone(data.phone)
    if (!phoneValidation.isValid) {
      errors.phone = phoneValidation.error!
    }
  }

  const termsValidation = validateTermsAcceptance(data.acceptTerms)
  if (!termsValidation.isValid) {
    errors.acceptTerms = termsValidation.error!
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors
  }
}
