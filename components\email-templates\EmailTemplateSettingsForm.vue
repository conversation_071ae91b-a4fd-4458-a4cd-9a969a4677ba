<!--
  Email Template Settings Form
  
  Form component for configuring email template settings
-->

<template>
  <div class="email-template-settings-form">
    <form @submit.prevent="handleSubmit" class="space-y-6">
      <!-- Basic Settings -->
      <div>
        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
          Basic Settings
        </h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <UiInput
            v-model="form.name"
            label="Template Name"
            placeholder="Enter template name"
            required
            :error="errors.name"
          />
          
          <UiSelect
            v-model="form.category"
            label="Category"
            :options="categoryOptions"
            required
            :error="errors.category"
          />
          
          <UiInput
            v-model="form.subject"
            label="Email Subject"
            placeholder="Enter email subject"
            required
            :error="errors.subject"
            class="md:col-span-2"
          />
          
          <UiTextarea
            v-model="form.description"
            label="Description"
            placeholder="Enter template description"
            :rows="3"
            :error="errors.description"
            class="md:col-span-2"
          />
        </div>
      </div>

      <!-- Email Settings -->
      <div>
        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
          Email Settings
        </h3>
        
        <div class="space-y-4">
          <div class="flex items-center justify-between">
            <div>
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
                Responsive Design
              </label>
              <p class="text-xs text-gray-500 dark:text-gray-400">
                Optimize template for mobile devices
              </p>
            </div>
            <UiToggle v-model="form.settings.responsive" />
          </div>
          
          <div class="flex items-center justify-between">
            <div>
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
                Dark Mode Support
              </label>
              <p class="text-xs text-gray-500 dark:text-gray-400">
                Include dark mode styles
              </p>
            </div>
            <UiToggle v-model="form.settings.darkMode" />
          </div>
          
          <div class="flex items-center justify-between">
            <div>
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
                Email Tracking
              </label>
              <p class="text-xs text-gray-500 dark:text-gray-400">
                Enable open and click tracking
              </p>
            </div>
            <UiToggle v-model="form.settings.trackingEnabled" />
          </div>
          
          <div class="flex items-center justify-between">
            <div>
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
                Unsubscribe Link
              </label>
              <p class="text-xs text-gray-500 dark:text-gray-400">
                Include unsubscribe link in footer
              </p>
            </div>
            <UiToggle v-model="form.settings.unsubscribeLink" />
          </div>
          
          <div class="flex items-center justify-between">
            <div>
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
                Social Media Links
              </label>
              <p class="text-xs text-gray-500 dark:text-gray-400">
                Include social media icons in footer
              </p>
            </div>
            <UiToggle v-model="form.settings.socialLinks" />
          </div>
        </div>
      </div>

      <!-- Advanced Settings -->
      <div>
        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
          Advanced Settings
        </h3>
        
        <div class="space-y-4">
          <UiInput
            v-model="form.settings.preheader"
            label="Preheader Text"
            placeholder="Enter preheader text (optional)"
            :error="errors['settings.preheader']"
          />
          
          <UiTextarea
            v-model="form.settings.footerText"
            label="Footer Text"
            placeholder="Enter custom footer text (optional)"
            :rows="2"
            :error="errors['settings.footerText']"
          />
          
          <UiTextarea
            v-model="form.settings.customCss"
            label="Custom CSS"
            placeholder="Enter custom CSS styles (optional)"
            :rows="4"
            :error="errors['settings.customCss']"
            class="font-mono text-sm"
          />
        </div>
      </div>

      <!-- Template Variables -->
      <div>
        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
          Template Variables
        </h3>
        
        <div class="space-y-3">
          <div 
            v-for="(variable, index) in form.variables"
            :key="index"
            class="flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg"
          >
            <UiInput
              v-model="variable.name"
              placeholder="Variable name"
              class="flex-1"
            />
            <UiInput
              v-model="variable.label"
              placeholder="Display label"
              class="flex-1"
            />
            <UiSelect
              v-model="variable.type"
              :options="variableTypeOptions"
              class="w-32"
            />
            <UiButton
              variant="ghost"
              size="sm"
              @click="removeVariable(index)"
              class="text-red-600 hover:text-red-700"
            >
              <Icon name="heroicons:trash" class="w-4 h-4" />
            </UiButton>
          </div>
          
          <UiButton
            variant="ghost"
            size="sm"
            @click="addVariable"
            class="w-full border-2 border-dashed border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500"
          >
            <Icon name="heroicons:plus" class="w-4 h-4 mr-2" />
            Add Variable
          </UiButton>
        </div>
      </div>

      <!-- Tags -->
      <div>
        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
          Tags
        </h3>
        
        <div class="space-y-3">
          <div class="flex flex-wrap gap-2">
            <span
              v-for="(tag, index) in form.metadata.tags"
              :key="index"
              class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-brandPrimary-100 text-brandPrimary-800 dark:bg-brandPrimary-900 dark:text-brandPrimary-200"
            >
              {{ tag }}
              <button
                @click="removeTag(index)"
                class="ml-1.5 inline-flex items-center justify-center w-4 h-4 rounded-full text-brandPrimary-600 hover:bg-brandPrimary-200 hover:text-brandPrimary-800 dark:text-brandPrimary-400 dark:hover:bg-brandPrimary-800 dark:hover:text-brandPrimary-200"
              >
                <Icon name="heroicons:x-mark" class="w-3 h-3" />
              </button>
            </span>
          </div>
          
          <div class="flex space-x-2">
            <UiInput
              v-model="newTag"
              placeholder="Add a tag"
              @keyup.enter="addTag"
              class="flex-1"
            />
            <UiButton
              variant="ghost"
              size="sm"
              @click="addTag"
              :disabled="!newTag.trim()"
            >
              Add
            </UiButton>
          </div>
        </div>
      </div>

      <!-- Form Actions -->
      <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700">
        <UiButton
          variant="ghost"
          @click="$emit('cancel')"
        >
          Cancel
        </UiButton>
        <UiButton
          type="submit"
          variant="primary"
          :loading="isLoading"
        >
          Save Settings
        </UiButton>
      </div>
    </form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import type { EmailTemplate, EmailTemplateVariable } from '~/app/shared/types'

// Props
interface Props {
  template: EmailTemplate
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  save: [settings: any]
  cancel: []
}>()

// State
const isLoading = ref(false)
const newTag = ref('')
const errors = ref<Record<string, string>>({})

// Form data
const form = reactive({
  name: '',
  description: '',
  category: 'custom',
  subject: '',
  settings: {
    responsive: true,
    darkMode: false,
    preheader: '',
    trackingEnabled: true,
    unsubscribeLink: true,
    socialLinks: false,
    footerText: '',
    customCss: ''
  },
  variables: [] as EmailTemplateVariable[],
  metadata: {
    tags: [] as string[]
  }
})

// Options
const categoryOptions = [
  { label: 'Legal Notice', value: 'legal-notice' },
  { label: 'Client Communication', value: 'client-communication' },
  { label: 'Case Update', value: 'case-update' },
  { label: 'Invoice', value: 'invoice' },
  { label: 'Reminder', value: 'reminder' },
  { label: 'Welcome', value: 'welcome' },
  { label: 'Newsletter', value: 'newsletter' },
  { label: 'Marketing', value: 'marketing' },
  { label: 'System', value: 'system' },
  { label: 'Custom', value: 'custom' }
]

const variableTypeOptions = [
  { label: 'Text', value: 'text' },
  { label: 'Number', value: 'number' },
  { label: 'Date', value: 'date' },
  { label: 'Boolean', value: 'boolean' },
  { label: 'Select', value: 'select' }
]

// Methods
const loadTemplateData = () => {
  if (props.template) {
    form.name = props.template.name
    form.description = props.template.description || ''
    form.category = props.template.category
    form.subject = props.template.subject
    form.settings = { ...form.settings, ...props.template.settings }
    form.variables = [...props.template.variables]
    form.metadata.tags = [...props.template.metadata.tags]
  }
}

const addVariable = () => {
  form.variables.push({
    name: '',
    label: '',
    type: 'text',
    required: false,
    defaultValue: ''
  })
}

const removeVariable = (index: number) => {
  form.variables.splice(index, 1)
}

const addTag = () => {
  const tag = newTag.value.trim()
  if (tag && !form.metadata.tags.includes(tag)) {
    form.metadata.tags.push(tag)
    newTag.value = ''
  }
}

const removeTag = (index: number) => {
  form.metadata.tags.splice(index, 1)
}

const validateForm = () => {
  errors.value = {}
  
  if (!form.name.trim()) {
    errors.value.name = 'Template name is required'
  }
  
  if (!form.subject.trim()) {
    errors.value.subject = 'Email subject is required'
  }
  
  if (!form.category) {
    errors.value.category = 'Category is required'
  }
  
  return Object.keys(errors.value).length === 0
}

const handleSubmit = async () => {
  if (!validateForm()) return
  
  try {
    isLoading.value = true
    
    const settings = {
      name: form.name,
      description: form.description,
      category: form.category,
      subject: form.subject,
      settings: form.settings,
      variables: form.variables,
      metadata: {
        ...props.template.metadata,
        tags: form.metadata.tags
      }
    }
    
    emit('save', settings)
    
  } catch (error) {
    console.error('Error saving template settings:', error)
  } finally {
    isLoading.value = false
  }
}

// Lifecycle
onMounted(() => {
  loadTemplateData()
})
</script>

<style scoped>
.email-template-settings-form {
  max-height: 70vh;
  overflow-y: auto;
}

/* Custom scrollbar */
.email-template-settings-form {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 transparent;
}

.email-template-settings-form::-webkit-scrollbar {
  width: 6px;
}

.email-template-settings-form::-webkit-scrollbar-track {
  background: transparent;
}

.email-template-settings-form::-webkit-scrollbar-thumb {
  background-color: #cbd5e1;
  border-radius: 3px;
}

.email-template-settings-form::-webkit-scrollbar-thumb:hover {
  background-color: #94a3b8;
}

.dark .email-template-settings-form {
  scrollbar-color: #4b5563 transparent;
}

.dark .email-template-settings-form::-webkit-scrollbar-thumb {
  background-color: #4b5563;
}

.dark .email-template-settings-form::-webkit-scrollbar-thumb:hover {
  background-color: #6b7280;
}
</style>
