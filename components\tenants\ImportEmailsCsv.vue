<template>
  <div class="space-y-4">
    <p class="text-sm text-gray-600 dark:text-gray-400">
      Upload a CSV file or paste email addresses to send bulk invitations.
    </p>
    <UiTextarea
      id="bulkEmails"
      v-model="bulkEmailText"
      placeholder="Paste email addresses here, one per line..."
      :rows="8"
      label="Email Addresses"
      @change="handleFileUpload"
    />
    <div class="text-xs text-gray-500 dark:text-gray-400">
      <p class="font-medium mb-1">Supported formats:</p>
      <p>• One email per line</p>
      <p>• Comma-separated emails</p>
      <p>• CSV format: email,role (role is optional)</p>
    </div>
  </div>
</template>

<script setup lang="ts">
const bulkEmailText = ref('')

const handleFileUpload = (event: Event) => {
  const file = (event.target as HTMLInputElement).files?.[0]
  if (!file) return

  const reader = new FileReader()
  reader.onload = (e: any) => {
    bulkEmailText.value = e.target.result
  }
  reader.readAsText(file)
}
</script>
 