<!--
  Platform Footer Component
  
  Footer for the platform dashboard with links, version info,
  and legal information. Responsive and accessible design.
-->

<template>
  <footer class="bg-white border-t border-gray-200 px-4 py-3">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-2 sm:space-y-0">
      <!-- Left section: Copyright and version -->
      <div class="flex flex-col sm:flex-row sm:items-center space-y-1 sm:space-y-0 sm:space-x-4">
        <div class="text-xs text-gray-500">
          © {{ currentYear }} Legal SaaS Platform. All rights reserved.
        </div>
        
        <div class="flex items-center space-x-2 text-xs text-gray-400">
          <span>Version {{ version }}</span>
          <span class="hidden sm:inline">•</span>
          <span class="flex items-center">
            <Icon name="heroicons:signal" class="w-3 h-3 mr-1" />
            <span :class="statusColor">{{ systemStatus }}</span>
          </span>
        </div>
      </div>

      <!-- Right section: Links and actions -->
      <div class="flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 ltr:sm:space-x-4 rtl:sm:space-x-reverse">
        <!-- Language Switcher -->
        <div class="flex items-center ltr:space-x-2 rtl:space-x-reverse">
          <span class="text-xs text-gray-500">{{ t('common.language') }}:</span>
          <UiLanguageSwitcher
            variant="compact"
            :show-flag="true"
            :show-name="false"
            :show-settings="false"
            dropdown-position="right"
            display-mode="native"
            @language-changed="onLanguageChanged"
          />
        </div>

        <!-- Quick links -->
        <div class="flex items-center ltr:space-x-3 rtl:space-x-reverse text-xs">
          <NuxtLink
            to="/dashboard/help"
            class="text-gray-500 hover:text-gray-700 transition-colors duration-200"
          >
            {{ t('navigation.help') }}
          </NuxtLink>

          <span class="text-gray-300">•</span>

          <NuxtLink
            to="/dashboard/support"
            class="text-gray-500 hover:text-gray-700 transition-colors duration-200"
          >
            {{ t('navigation.support') }}
          </NuxtLink>
          
          <span class="text-gray-300">•</span>
          
          <button
            type="button"
            class="text-gray-500 hover:text-gray-700 transition-colors duration-200"
            @click="showFeedbackModal = true"
          >
            Feedback
          </button>
          
          <span class="text-gray-300 hidden sm:inline">•</span>
          
          <NuxtLink
            to="/privacy"
            class="text-gray-500 hover:text-gray-700 transition-colors duration-200 hidden sm:inline"
            target="_blank"
          >
            Privacy
          </NuxtLink>
        </div>

        <!-- Theme toggle -->
        <button
          type="button"
          class="flex items-center space-x-1 text-xs text-gray-500 hover:text-gray-700 transition-colors duration-200"
          @click="toggleTheme"
          :aria-label="isDarkMode ? 'Switch to light mode' : 'Switch to dark mode'"
        >
          <Icon
            :name="isDarkMode ? 'heroicons:sun' : 'heroicons:moon'"
            class="w-3 h-3"
          />
          <span class="hidden sm:inline">
            {{ isDarkMode ? 'Light' : 'Dark' }}
          </span>
        </button>
      </div>
    </div>

    <!-- Expanded footer (optional) -->
    <div v-if="showExpandedFooter" class="mt-4 pt-4 border-t border-gray-100">
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 text-xs">
        <!-- Platform Info -->
        <div>
          <h4 class="font-medium text-gray-900 mb-2">Platform</h4>
          <ul class="space-y-1">
            <li>
              <NuxtLink
                to="/dashboard/about"
                class="text-gray-500 hover:text-gray-700 transition-colors duration-200"
              >
                About
              </NuxtLink>
            </li>
            <li>
              <NuxtLink
                to="/dashboard/changelog"
                class="text-gray-500 hover:text-gray-700 transition-colors duration-200"
              >
                Changelog
              </NuxtLink>
            </li>
            <li>
              <NuxtLink
                to="/dashboard/status"
                class="text-gray-500 hover:text-gray-700 transition-colors duration-200"
              >
                System Status
              </NuxtLink>
            </li>
          </ul>
        </div>

        <!-- Resources -->
        <div>
          <h4 class="font-medium text-gray-900 mb-2">Resources</h4>
          <ul class="space-y-1">
            <li>
              <NuxtLink
                to="/dashboard/help/documentation"
                class="text-gray-500 hover:text-gray-700 transition-colors duration-200"
              >
                Documentation
              </NuxtLink>
            </li>
            <li>
              <NuxtLink
                to="/dashboard/help/tutorials"
                class="text-gray-500 hover:text-gray-700 transition-colors duration-200"
              >
                Tutorials
              </NuxtLink>
            </li>
            <li>
              <NuxtLink
                to="/dashboard/help/api"
                class="text-gray-500 hover:text-gray-700 transition-colors duration-200"
              >
                API Reference
              </NuxtLink>
            </li>
          </ul>
        </div>

        <!-- Support -->
        <div>
          <h4 class="font-medium text-gray-900 mb-2">Support</h4>
          <ul class="space-y-1">
            <li>
              <NuxtLink
                to="/dashboard/support/tickets"
                class="text-gray-500 hover:text-gray-700 transition-colors duration-200"
              >
                Support Tickets
              </NuxtLink>
            </li>
            <li>
              <NuxtLink
                to="/dashboard/support/contact"
                class="text-gray-500 hover:text-gray-700 transition-colors duration-200"
              >
                Contact Us
              </NuxtLink>
            </li>
            <li>
              <button
                type="button"
                class="text-gray-500 hover:text-gray-700 transition-colors duration-200"
                @click="startLiveChat"
              >
                Live Chat
              </button>
            </li>
          </ul>
        </div>

        <!-- Legal -->
        <div>
          <h4 class="font-medium text-gray-900 mb-2">Legal</h4>
          <ul class="space-y-1">
            <li>
              <NuxtLink
                to="/terms"
                class="text-gray-500 hover:text-gray-700 transition-colors duration-200"
                target="_blank"
              >
                Terms of Service
              </NuxtLink>
            </li>
            <li>
              <NuxtLink
                to="/privacy"
                class="text-gray-500 hover:text-gray-700 transition-colors duration-200"
                target="_blank"
              >
                Privacy Policy
              </NuxtLink>
            </li>
            <li>
              <NuxtLink
                to="/security"
                class="text-gray-500 hover:text-gray-700 transition-colors duration-200"
                target="_blank"
              >
                Security
              </NuxtLink>
            </li>
          </ul>
        </div>
      </div>
    </div>

    <!-- Feedback Modal -->
    <Teleport to="body">
      <div
        v-if="showFeedbackModal"
        class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
        @click="showFeedbackModal = false"
      >
        <div
          class="bg-white rounded-lg p-6 max-w-md w-full mx-4"
          @click.stop
        >
          <h3 class="text-lg font-medium text-gray-900 mb-4">Send Feedback</h3>
          <textarea
            v-model="feedbackText"
            class="w-full h-32 p-3 border border-gray-300 rounded-md resize-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
            placeholder="Tell us what you think..."
          ></textarea>
          <div class="flex justify-end space-x-3 mt-4">
            <button
              type="button"
              class="px-4 py-2 text-sm text-gray-700 hover:text-gray-900"
              @click="showFeedbackModal = false"
            >
              Cancel
            </button>
            <button
              type="button"
              class="px-4 py-2 bg-indigo-600 text-white text-sm rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
              @click="submitFeedback"
              :disabled="!feedbackText.trim()"
            >
              Send Feedback
            </button>
          </div>
        </div>
      </div>
    </Teleport>
  </footer>
</template>

<script setup lang="ts">
import { computed, ref, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'

// Props
interface Props {
  showExpandedFooter?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showExpandedFooter: false
})

// i18n composables
const { t } = useI18n()

// Language change handler
const onLanguageChanged = (locale: string) => {
  console.log(`Language changed to: ${locale}`)
  // Additional language change logic can be added here
}

// State
const isDarkMode = ref(false)
const showFeedbackModal = ref(false)
const feedbackText = ref('')
const systemStatus = ref('Operational')

// Computed
const currentYear = computed(() => new Date().getFullYear())

const version = computed(() => {
  // In a real app, this would come from package.json or environment
  return '2.1.4'
})

const statusColor = computed(() => {
  switch (systemStatus.value) {
    case 'Operational':
      return 'text-green-500'
    case 'Degraded':
      return 'text-yellow-500'
    case 'Down':
      return 'text-red-500'
    default:
      return 'text-gray-500'
  }
})

// Methods
const toggleTheme = () => {
  isDarkMode.value = !isDarkMode.value
  document.documentElement.classList.toggle('dark', isDarkMode.value)
  localStorage.setItem('theme', isDarkMode.value ? 'dark' : 'light')
}

const submitFeedback = async () => {
  if (!feedbackText.value.trim()) return

  try {
    // In a real app, this would send feedback to an API
    console.log('Feedback submitted:', feedbackText.value)
    
    // Show success message (you might want to use a toast notification)
    alert('Thank you for your feedback!')
    
    // Reset form
    feedbackText.value = ''
    showFeedbackModal.value = false
  } catch (error) {
    console.error('Error submitting feedback:', error)
    alert('Failed to submit feedback. Please try again.')
  }
}

const startLiveChat = () => {
  // In a real app, this would integrate with a chat service like Intercom, Zendesk, etc.
  console.log('Starting live chat...')
  alert('Live chat feature coming soon!')
}

const checkSystemStatus = async () => {
  try {
    // In a real app, this would check system status from an API
    // For now, we'll simulate it
    const statuses = ['Operational', 'Degraded', 'Down']
    const randomStatus = statuses[Math.floor(Math.random() * statuses.length)]
    systemStatus.value = 'Operational' // Always show operational for demo
  } catch (error) {
    console.error('Error checking system status:', error)
    systemStatus.value = 'Unknown'
  }
}

// Initialize
onMounted(() => {
  const savedTheme = localStorage.getItem('theme')
  isDarkMode.value = savedTheme === 'dark'
  checkSystemStatus()
})
</script>

<style scoped>
/* Ensure proper spacing and alignment */
footer {
  flex-shrink: 0;
}

/* Smooth transitions for theme toggle */
button {
  transition: all 0.2s ease-in-out;
}

/* Focus styles for accessibility */
button:focus,
a:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.5);
  border-radius: 2px;
}

/* Modal backdrop blur effect */
.fixed.inset-0 {
  backdrop-filter: blur(4px);
}
</style>
