{"welcome": "Welcome", "hello": "Hello", "goodbye": "Goodbye", "yes": "Yes", "no": "No", "ok": "OK", "cancel": "Cancel", "save": "Save", "edit": "Edit", "delete": "Delete", "create": "Create", "update": "Update", "view": "View", "close": "Close", "open": "Open", "add": "Add", "remove": "Remove", "search": "Search", "filter": "Filter", "language": "Language", "editor": {"title": "Document Editor", "description": "Create and edit legal documents with our advanced editor.", "placeholder": "Start typing your legal document here...", "currentLocale": "Current Language", "direction": "Text Direction", "contentLength": "Content Length", "characters": "characters", "toolbar": {"bold": "Bold", "italic": "Italic", "underline": "Underline", "strikethrough": "Strikethrough", "alignLeft": "Align Left", "alignCenter": "Align Center", "alignRight": "Align Right", "alignJustify": "Justify", "heading1": "Heading 1", "heading2": "Heading 2", "heading3": "Heading 3", "bulletList": "Bullet List", "orderedList": "Numbered List", "undo": "Undo", "redo": "Redo", "fontFamily": "Font Family", "fontSize": "Font Size", "defaultFont": "<PERSON><PERSON><PERSON>", "textColor": "Text Color", "highlight": "Highlight", "noHighlight": "No Highlight", "subscript": "Subscript", "superscript": "Superscript", "indent": "Increase Indent", "outdent": "Decrease Indent", "styles": "Styles", "normal": "Normal", "title1": "Title 1", "title2": "Title 2", "title3": "Title 3", "importWord": "Import Word", "markdown": "<PERSON><PERSON>", "search": "Search", "print": "Print", "sizeDemo": "<PERSON><PERSON>"}, "templates": {"contract": "Contract", "contractDesc": "Standard legal contract template", "contractTitle": "LEGAL CONTRACT", "nda": "Non-Disclosure Agreement", "ndaDesc": "Confidentiality agreement template", "ndaTitle": "NON-DISCLOSURE AGREEMENT", "letter": "Legal Letter", "letterDesc": "Formal legal correspondence template", "memo": "Legal Memorandum", "memoDesc": "Internal legal memo template", "memoTitle": "MEMORANDUM", "parties": "Parties", "party1": "Party 1", "party2": "Party 2", "terms": "Terms and Conditions", "termsContent": "Please specify the terms and conditions of this agreement...", "signatures": "Signatures", "signature1": "Signature 1", "signature2": "Signature 2", "signature": "Signature", "date": "Date", "to": "To", "from": "From", "subject": "Subject", "ndaIntro": "This Non-Disclosure Agreement is entered into by and between the parties...", "confidentialInfo": "Confidential Information", "confidentialInfoContent": "For purposes of this Agreement, confidential information includes...", "obligations": "Obligations", "obligation1": "Maintain confidentiality of all disclosed information", "obligation2": "Use information solely for the intended purpose", "obligation3": "Return or destroy information upon request", "letterGreeting": "Dear Sir/<PERSON>am", "letterContent": "Please write your letter content here...", "letterClosing": "Since<PERSON>y", "purpose": "Purpose", "purposeContent": "The purpose of this memorandum is to...", "background": "Background", "backgroundContent": "The relevant background information includes...", "recommendation": "Recommendation", "recommendationContent": "Based on the analysis, it is recommended that..."}}, "sort": "Sort", "export": "Export", "import": "Import", "print": "Print", "share": "Share", "copy": "Copy", "paste": "Paste", "cut": "Cut", "undo": "Undo", "redo": "Redo", "select": "Select", "selectAll": "Select All", "deselect": "Deselect", "next": "Next", "previous": "Previous", "back": "Back", "forward": "Forward", "submit": "Submit", "reset": "Reset", "clear": "Clear", "refresh": "Refresh", "reload": "Reload", "upload": "Upload", "download": "Download", "send": "Send", "receive": "Receive", "connect": "Connect", "disconnect": "Disconnect", "login": "<PERSON><PERSON>", "logout": "Logout", "register": "Register", "signup": "Sign Up", "signin": "Sign In", "signout": "Sign Out", "profile": "Profile", "settings": "Settings", "preferences": "Preferences", "options": "Options", "configuration": "Configuration", "administration": "Administration", "management": "Management", "dashboard": "Dashboard", "home": "Home", "about": "About", "contact": "Contact", "help": "Help", "support": "Support", "documentation": "Documentation", "tutorial": "Tutorial", "guide": "Guide", "faq": "FAQ", "terms": "Terms", "privacy": "Privacy", "legal": "Legal", "copyright": "Copyright", "license": "License", "version": "Version", "status": "Status", "active": "Active", "inactive": "Inactive", "enabled": "Enabled", "disabled": "Disabled", "online": "Online", "offline": "Offline", "available": "Available", "unavailable": "Unavailable", "public": "Public", "private": "Private", "draft": "Draft", "published": "Published", "pending": "Pending", "approved": "Approved", "rejected": "Rejected", "completed": "Completed", "failed": "Failed", "success": "Success", "error": "Error", "warning": "Warning", "info": "Info", "notice": "Notice", "alert": "<PERSON><PERSON>", "message": "Message", "notification": "Notification", "email": "Email", "phone": "Phone", "address": "Address", "name": "Name", "title": "Title", "description": "Description", "content": "Content", "text": "Text", "image": "Image", "video": "Video", "audio": "Audio", "file": "File", "folder": "Folder", "directory": "Directory", "path": "Path", "url": "URL", "link": "Link", "date": "Date", "time": "Time", "datetime": "Date & Time", "timezone": "Timezone", "locale": "Locale", "currency": "<PERSON><PERSON><PERSON><PERSON>", "amount": "Amount", "price": "Price", "cost": "Cost", "total": "Total", "subtotal": "Subtotal", "tax": "Tax", "discount": "Discount", "quantity": "Quantity", "size": "Size", "weight": "Weight", "width": "<PERSON><PERSON><PERSON>", "height": "Height", "length": "Length", "color": "Color", "type": "Type", "category": "Category", "tag": "Tag", "label": "Label", "value": "Value", "key": "Key", "id": "ID", "code": "Code", "number": "Number", "count": "Count", "index": "Index", "order": "Order", "rank": "Rank", "level": "Level", "priority": "Priority", "importance": "Importance", "urgency": "Urgency", "severity": "Severity", "difficulty": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "complexity": "Complexity", "progress": "Progress", "percentage": "Percentage", "ratio": "<PERSON><PERSON>", "rate": "Rate", "speed": "Speed", "duration": "Duration", "frequency": "Frequency", "interval": "Interval", "period": "Period", "range": "Range", "limit": "Limit", "minimum": "Minimum", "maximum": "Maximum", "average": "Average", "median": "Median", "sum": "Sum", "difference": "Difference", "change": "Change", "increase": "Increase", "decrease": "Decrease", "growth": "Growth", "decline": "Decline", "trend": "Trend", "pattern": "Pattern", "format": "Format", "style": "Style", "theme": "Theme", "layout": "Layout", "design": "Design", "template": "Template", "model": "Model", "example": "Example", "sample": "<PERSON><PERSON>", "demo": "Demo", "test": "Test", "trial": "Trial", "preview": "Preview", "review": "Review", "feedback": "<PERSON><PERSON><PERSON>", "comment": "Comment", "note": "Note", "remark": "Remark", "annotation": "Annotation", "highlight": "Highlight", "bookmark": "Bookmark", "favorite": "Favorite", "like": "Like", "dislike": "Dislike", "vote": "Vote", "rating": "Rating", "score": "Score", "point": "Point", "credit": "Credit", "balance": "Balance", "account": "Account", "user": "User", "member": "Member", "guest": "Guest", "visitor": "Visitor", "customer": "Customer", "client": "Client", "partner": "Partner", "vendor": "<PERSON><PERSON><PERSON>", "supplier": "Supplier", "provider": "Provider", "service": "Service", "product": "Product", "item": "<PERSON><PERSON>", "object": "Object", "entity": "Entity", "record": "Record", "entry": "Entry", "data": "Data", "information": "Information", "detail": "Detail", "summary": "Summary", "overview": "Overview", "report": "Report", "analysis": "Analysis", "statistics": "Statistics", "metrics": "Metrics", "analytics": "Analytics", "insights": "Insights", "results": "Results", "outcome": "Outcome", "output": "Output", "input": "Input", "source": "Source", "target": "Target", "destination": "Destination", "origin": "Origin", "location": "Location", "position": "Position", "place": "Place", "region": "Region", "area": "Area", "zone": "Zone", "sector": "Sector", "department": "Department", "division": "Division", "unit": "Unit", "group": "Group", "team": "Team", "organization": "Organization", "company": "Company", "business": "Business", "enterprise": "Enterprise", "corporation": "Corporation", "institution": "Institution", "agency": "Agency", "office": "Office", "branch": "Branch", "subsidiary": "Subsidiary", "affiliate": "Affiliate", "network": "Network", "system": "System", "platform": "Platform", "application": "Application", "software": "Software", "program": "Program", "tool": "Tool", "utility": "Utility", "feature": "Feature", "function": "Function", "capability": "Capability", "option": "Option", "choice": "Choice", "selection": "Selection", "decision": "Decision", "action": "Action", "operation": "Operation", "process": "Process", "procedure": "Procedure", "method": "Method", "technique": "Technique", "approach": "Approach", "strategy": "Strategy", "plan": "Plan", "project": "Project", "task": "Task", "job": "Job", "work": "Work", "activity": "Activity", "event": "Event", "incident": "Incident", "issue": "Issue", "problem": "Problem", "challenge": "Challenge", "opportunity": "Opportunity", "solution": "Solution", "answer": "Answer", "response": "Response", "reply": "Reply", "request": "Request", "query": "Query", "question": "Question", "inquiry": "Inquiry", "investigation": "Investigation", "research": "Research", "study": "Study", "examination": "Examination", "inspection": "Inspection", "audit": "Audit", "assessment": "Assessment", "evaluation": "Evaluation", "validation": "Validation", "verification": "Verification", "confirmation": "Confirmation", "approval": "Approval", "authorization": "Authorization", "permission": "Permission", "access": "Access", "security": "Security", "protection": "Protection", "safety": "Safety", "confidentiality": "Confidentiality", "transparency": "Transparency", "visibility": "Visibility", "accessibility": "Accessibility", "usability": "Usability", "reliability": "Reliability", "availability": "Availability", "performance": "Performance", "efficiency": "Efficiency", "effectiveness": "Effectiveness", "productivity": "Productivity", "quality": "Quality", "specification": "Specification", "requirement": "Requirement", "criteria": "Criteria", "condition": "Condition", "rule": "Rule", "regulation": "Regulation", "policy": "Policy", "guideline": "Guideline", "principle": "Principle", "practice": "Practice", "convention": "Convention", "protocol": "Protocol", "structure": "Structure", "framework": "Framework", "architecture": "Architecture", "infrastructure": "Infrastructure", "foundation": "Foundation", "base": "Base", "core": "Core", "essence": "Essence", "fundamental": "Fundamental", "basic": "Basic", "advanced": "Advanced", "expert": "Expert", "professional": "Professional", "standard": "Standard", "premium": "Premium", "custom": "Custom", "personal": "Personal", "individual": "Individual", "collective": "Collective", "shared": "Shared", "common": "Common"}