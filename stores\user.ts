import { defineStore } from 'pinia';
import { useApi } from '~/composables/useApi';
import { useToastStore } from '~/stores/toast';
import type { GetRequestQuery } from '~/types/api'; // Import from the correct source

// Define interfaces for User
export interface User {
  id: string;
  createdAt: string;
  updatedAt: string;
  createdById: string | null;
  updatedById: string | null;
  isDeleted: boolean;
  deletedAt: string | null;
  name: string;
  email: string;
  title: string | null;
  roles: string[]; // e.g., ['Admin', 'Member', 'Owner']
  isActive: boolean;
  lastLoginAt: string | null;
  avatarUrl: string | null;
  otpEnabled: boolean;
  otpSecret: string | null;
  password?: string; // Sensitive, usually not sent to client unless necessary
  tenantId: string | null;
  // Add any other user-specific fields based on your application needs
}


export interface UserActivity {
  id: string;
  type: 'login' | 'logout' | 'password_change' | 'profile_update' | 'role_change' | 'security';
  description: string;
  timestamp: string;
  user: User;
  ipAddress: string;
  device: string;
  actionUrl: string;
} 

// User Overview API Response Interfaces
export interface UserTotalMetrics {
  total: number;
  monthlyGrowth: number;
  monthlyGrowthPercentage: number;
}

export interface UserStatusCounts {
  active: number;
  inactive: number;
  pending: number;
  suspended: number;
}

export interface UserOnlineMetrics {
  count: number;
  changePercentage: number;
}

export interface UserNewThisMonth {
  count: number;
  changePercentage: number;
}

export interface UserRoleDistribution {
  role: string;
  count: number;
  percentage: number;
}

export interface UserEngagementMetric {
  name: string;
  period: string;
  value: number;
  changePercentage: number;
  displayValue?: string;
}

export interface UserActivityData {
  date: string;
  count: number;
  type: 'registrations' | 'logins';
}

export interface RecentUser {
  id: string;
  email: string;
  name: string;
  roles: string[];
  tenantId: string;
  createdAt: string;
  lastLogin: string;
  isActive: boolean;
}

export interface UserOverview {
  totalUsers: UserTotalMetrics;
  statusCounts: UserStatusCounts;
  onlineNow: UserOnlineMetrics;
  newThisMonth: UserNewThisMonth;
  roleDistribution: UserRoleDistribution[];
  userEngagement: UserEngagementMetric[];
  userActivity: UserActivityData[];
  recentUsers: RecentUser[];
  lastUpdated: string;
}

// Platform Activity Interfaces
export interface PlatformUserActivity {
  totalUsers: number;
  activeUsers: number;
  onlineNow: number;
  newToday: number;
  newThisWeek: number;
  newThisMonth: number;
  engagement: UserEngagementMetric[];
  lastUpdated: string;
}

export interface InviteUserPayload {
  email: string;
  roles: string[];
  name?: string; // Optional, user might set it up later
  // any other fields required for invitation
}

export interface CreateUserPayload {
  name: string;
  email: string;
  roles: string[];
  title?: string;
  password?: string; // If creating directly with password
  // Add other required fields
}

export interface UpdateUserPayload {
  name?: string;
  email?: string;
  roles?: string[];
  title?: string;
  isActive?: boolean;
  avatarUrl?: string;
  otpEnabled?: boolean;
  // Add other updatable fields
}

interface UsersResponse {
  data: User[];
  meta: {
    total: number;
    page: number;
    limit: number;
  };
}
interface ActivityResponse {
  data: UserActivity[];
  meta: {
    total: number;
    page: number;
    limit: number;
  };
}

export const useUserStore = defineStore('user', {
  state: () => ({
    // Existing user list state
    users: [] as User[],
    usersMeta: null as UsersResponse['meta'] | null,

    // User Overview API state
    userOverview: null as UserOverview | null,
    platformUserActivity: null as PlatformUserActivity | null,
    userActivity: [] as UserActivity[],
    userActivityMeta: null as ActivityResponse['meta'] | null,
   

    // Individual metrics state for granular loading
    totalUserMetrics: null as UserTotalMetrics | null,
    statusCounts: null as UserStatusCounts | null,
    onlineMetrics: null as UserOnlineMetrics | null,
    newThisMonthMetrics: null as UserNewThisMonth | null,
    roleDistribution: [] as UserRoleDistribution[],
    userEngagement: [] as UserEngagementMetric[],
    userOverviewActivity: null as UserActivityData[] | null,
    selectedUser: null as User | null,
    
    recentUsers: [] as RecentUser[],

    // Loading states for different API endpoints
    isLoading: false,
    isLoadingOverview: false,
    isLoadingMetrics: false,
    isLoadingEngagement: false,
    isLoadingActivity: false,
    isLoadingRecentUsers: false,
    isLoadingPlatformActivity: false,

    // Error states
    error: null as string | null,
    overviewError: null as string | null,
    metricsError: null as string | null,

    // Last updated timestamps
    lastOverviewUpdate: null as string | null,
    lastMetricsUpdate: null as string | null,
  }),

  getters: {
    hasUsers: (state) => state.users.length > 0,
    hasOverviewData: (state) => state.userOverview !== null,
    isAnyLoading: (state) => state.isLoading || state.isLoadingOverview || state.isLoadingMetrics || state.isLoadingEngagement || state.isLoadingActivity || state.isLoadingRecentUsers || state.isLoadingPlatformActivity,
    hasUserActivity: (state) => state.userActivity.length > 0,
    hasPlatformActivity: (state) => state.platformUserActivity !== null,
    hasMetricsData: (state) => state.totalUserMetrics !== null,
    hasEngagementData: (state) => state.userEngagement.length > 0,
    hasRecentUsers: (state) => state.recentUsers.length > 0,
    hasRoleDistribution: (state) => state.roleDistribution.length > 0,
    hasUserOverviewActivity: (state) => state.userOverviewActivity !== null,
    getSelectedUser: (state) => state.selectedUser,
    // Computed metrics from overview data
    totalUsersCount: (state) => state.userOverview?.totalUsers?.total || state.users.length,
    activeUsersCount: (state) => state.userOverview?.statusCounts?.active || state.users.filter(u => u.isActive).length,
    onlineUsersCount: (state) => state.userOverview?.onlineNow?.count || 0,
    newUsersThisMonth: (state) => state.userOverview?.newThisMonth?.count || 0,
  },

  actions: {
    /**
     * Fetches all users (platform scope).
     * API: GET /users
     * @param query Optional query parameters for pagination, sorting, and filtering.
     */
    async fetchAllUsers(query?: GetRequestQuery): Promise<void> {
      this.isLoading = true;
      this.error = null;
      try {
        const api = useApi();
        // Assuming platform-level user management endpoint
        const response = await api.get<UsersResponse>('/users', { params: query });
        this.users = response.data;
        this.usersMeta = response.meta;
        console.log('All users fetched with query:', query, this.users, this.usersMeta);
      } catch (err: any) {
        this.error = this.handleApiError(err, 'Failed to fetch all users.');
        this.users = [];
        this.usersMeta = null;
        console.error('Error fetching all users:', err);
      } finally {
        this.isLoading = false;
      }
    },
    /**
     * Fetches a specific user by ID (platform scope).
     * API: GET /users/:id
     * @param id The ID of the user to fetch.
     * @returns The user object.
     */
     async fetchUserById(id: string): Promise<void> {
      this.isLoading = true;
      this.error = null;
      try {
        const api = useApi();
        const user = await api.get<User>(`/users/${id}`);
        this.selectedUser = user;
        console.log('User fetched:', user);
      } catch (err: any) {
        this.error = this.handleApiError(err, 'Failed to fetch user.');
        console.error('Error fetching user:', err);
      } finally {
        this.isLoading = false;
      }
    },

    /**
     * Invites a new user to the platform.
     * API: POST /users/invite
     * @param payload The data for the new user invitation.
     * @returns The invited user object or a confirmation message.
     */
    async inviteUser(payload: InviteUserPayload): Promise<User | { message: string }> {
      this.isLoading = true;
      this.error = null;
      try {
        const api = useApi();
        const result = await api.post<User | { message: string }>('/users/invite', payload);
        // Optionally refresh user list or add to it if full user object is returned
        // await this.fetchAllUsers(); // Or optimistically add if API returns the full user
        this.showSuccessNotification('User invited successfully.');
        return result;
      } catch (err: any) {
        this.error = this.handleApiError(err, 'Failed to invite user.');
        console.error('Error inviting user:', err);
        throw err;
      } finally {
        this.isLoading = false;
      }
    },
    
    /**
     * Creates a new user (platform scope, if direct creation is allowed).
     * API: POST /users
     * @param payload The data for the new user.
     * @returns The created user object.
     */
    async createUser(payload: CreateUserPayload): Promise<User> {
      this.isLoading = true;
      this.error = null;
      try {
        const api = useApi();
        const newUser = await api.post<User>('/users', payload);
        this.users.push(newUser); // Add to the list
        if (this.usersMeta) this.usersMeta.total++;
        this.showSuccessNotification('User created successfully.');
        return newUser;
      } catch (err: any) {
        this.error = this.handleApiError(err, 'Failed to create user.');
        console.error('Error creating user:', err);
        throw err;
      } finally {
        this.isLoading = false;
      }
    },

    /**
     * Updates a specific user by ID (platform scope).
     * API: PATCH /users/:id
     * @param id The ID of the user to update.
     * @param payload The data to update.
     * @returns The updated user object.
     */
    async updateUser(id: string, payload: UpdateUserPayload): Promise<User> {
      this.isLoading = true;
      this.error = null;
      try {
        const api = useApi();
        const updatedUser = await api.patch<User>(`/users/${id}`, payload);
        const index = this.users.findIndex(u => u.id === id);
        if (index !== -1) {
          this.users[index] = { ...this.users[index], ...updatedUser };
        }
        this.showSuccessNotification('User updated successfully.');
        return updatedUser;
      } catch (err: any) {
        this.error = this.handleApiError(err, `Failed to update user with ID: ${id}.`);
        console.error('Error updating user:', err);
        throw err;
      } finally {
        this.isLoading = false;
      }
    },
    
    /**
     * Deactivates a user by ID (platform scope).
     * API: PATCH /users/:id/deactivate
     * @param id The ID of the user to deactivate.
     */
     async deactivateUser(id: string): Promise<void> {
      this.isLoading = true;
      this.error = null;
      try {
        const api = useApi();
        await api.patch(`/users/${id}`, { isActive: false });
        const index = this.users.findIndex(u => u.id === id);
        if (index !== -1) {
          this.users[index].isActive = false;
        }
        this.showSuccessNotification('User deactivated successfully.');
      } catch (err: any) {
        this.error = this.handleApiError(err, `Failed to deactivate user with ID: ${id}.`);
        console.error('Error deactivating user:', err);
        throw err;
      } finally {
        this.isLoading = false;
      }
    },
    
    /**
     * Activates a user by ID (platform scope).
     * API: PATCH /users/:id/activate
     * @param id The ID of the user to activate.
     */
     async activateUser(id: string): Promise<void> {
      this.isLoading = true;
      this.error = null;
      try {
        const api = useApi();
        await api.patch(`/users/${id}`, { isActive: true });
        const index = this.users.findIndex(u => u.id === id);
        if (index !== -1) {
          this.users[index].isActive = true;
        }
        this.showSuccessNotification('User activated successfully.');
      } catch (err: any) {
        this.error = this.handleApiError(err, `Failed to activate user with ID: ${id}.`);
        console.error('Error activating user:', err);
        throw err;
      } finally {
        this.isLoading = false;
      }
    },

    /**
     * Deletes a user by ID (platform scope). Could be soft or hard delete.
     * API: DELETE /users/:id
     * @param id The ID of the user to delete.
     */
    async deleteUser(id: string): Promise<void> {
      this.isLoading = true;
      this.error = null;
      try {
        const api = useApi();
        await api.delete(`/users/${id}`);
        this.users = this.users.filter(u => u.id !== id);
        if (this.usersMeta) this.usersMeta.total--;
        this.showSuccessNotification('User deleted successfully.');
      } catch (err: any) {
        this.error = this.handleApiError(err, `Failed to delete user with ID: ${id}.`);
        console.error('Error deleting user:', err);
        throw err;
      } finally {
        this.isLoading = false;
      }
    },

    // ============================================================================
    // USER ACTIVITY API ACTIONS
    // ============================================================================

    /**
     * Fetches user activity trends.
     * API: GET /users/overview/metrics/activity
     * @param query Optional query parameters for filtering and pagination.
     */
    async fetchUserActivity(query?: GetRequestQuery): Promise<void> {
      this.isLoadingActivity = true;
      this.metricsError = null;
      try {
        const api = useApi();
        const activity = await api.get<ActivityResponse>('/users/activity', {
          params: query,
        });
        this.userActivity = activity.data;
        this.userActivityMeta = activity.meta;
        console.log('User activity trends fetched:', activity);
      } catch (err: any) {
        this.metricsError = this.handleApiError(err, 'Failed to fetch user activity trends.');
        console.error('Error fetching user activity trends:', err);
      } finally {
        this.isLoadingActivity = false;
      }
    },

    // ============================================================================
    // USER OVERVIEW API ACTIONS
    // ============================================================================

    /**
     * Fetches complete user overview data.
     * API: GET /users/overview
     */
    async fetchUserOverview(): Promise<void> {
      this.isLoadingOverview = true;
      this.overviewError = null;
      try {
        const api = useApi();
        const overview = await api.get<UserOverview>('/user-overview');
        this.userOverview = overview;
        this.lastOverviewUpdate = new Date().toISOString();
        console.log('User overview fetched:', overview);
      } catch (err: any) {
        this.overviewError = this.handleApiError(err, 'Failed to fetch user overview.');
        this.showErrorNotification(this.overviewError);
        console.error('Error fetching user overview:', err);
      } finally {
        this.isLoadingOverview = false;
      }
    },

    /**
     * Fetches total user metrics.
     * API: GET /users/overview/metrics/total
     */
    async fetchTotalUserMetrics(): Promise<void> {
      this.isLoadingMetrics = true;
      this.metricsError = null;
      try {
        const api = useApi();
        const metrics = await api.get<UserTotalMetrics>('/user-overview/metrics/total');
        this.totalUserMetrics = metrics;
        console.log('Total user metrics fetched:', metrics);
      } catch (err: any) {
        this.metricsError = this.handleApiError(err, 'Failed to fetch total user metrics.');
        console.error('Error fetching total user metrics:', err);
      } finally {
        this.isLoadingMetrics = false;
      }
    },

    /**
     * Fetches user status distribution.
     * API: GET /users/overview/metrics/status
     */
    async fetchUserStatusCounts(): Promise<void> {
      this.isLoadingMetrics = true;
      this.metricsError = null;
      try {
        const api = useApi();
        const statusCounts = await api.get<UserStatusCounts>('/user-overview/metrics/status');
        this.statusCounts = statusCounts;
        console.log('User status counts fetched:', statusCounts);
      } catch (err: any) {
        this.metricsError = this.handleApiError(err, 'Failed to fetch user status counts.');
        console.error('Error fetching user status counts:', err);
      } finally {
        this.isLoadingMetrics = false;
      }
    },

    /**
     * Fetches online user metrics.
     * API: GET /users/overview/metrics/online
     */
    async fetchOnlineUserMetrics(): Promise<void> {
      this.isLoadingMetrics = true;
      this.metricsError = null;
      try {
        const api = useApi();
        const onlineMetrics = await api.get<UserOnlineMetrics>('/user-overview/metrics/online');
        this.onlineMetrics = onlineMetrics;
        console.log('Online user metrics fetched:', onlineMetrics);
      } catch (err: any) {
        this.metricsError = this.handleApiError(err, 'Failed to fetch online user metrics.');
        console.error('Error fetching online user metrics:', err);
      } finally {
        this.isLoadingMetrics = false;
      }
    },

    /**
     * Fetches new users this month metrics.
     * API: GET /users/overview/metrics/new-this-month
     */
    async fetchNewThisMonthMetrics(): Promise<void> {
      this.isLoadingMetrics = true;
      this.metricsError = null;
      try {
        const api = useApi();
        const newThisMonth = await api.get<UserNewThisMonth>('/user-overview/metrics/new-this-month');
        this.newThisMonthMetrics = newThisMonth;
        console.log('New users this month metrics fetched:', newThisMonth);
      } catch (err: any) {
        this.metricsError = this.handleApiError(err, 'Failed to fetch new users this month metrics.');
        console.error('Error fetching new users this month metrics:', err);
      } finally {
        this.isLoadingMetrics = false;
      }
    },

    /**
     * Fetches role distribution data.
     * API: GET /users/overview/metrics/roles
     */
    async fetchRoleDistribution(): Promise<void> {
      this.isLoadingMetrics = true;
      this.metricsError = null;
      try {
        const api = useApi();
        const roleDistribution = await api.get<UserRoleDistribution[]>('/user-overview/metrics/roles');
        this.roleDistribution = roleDistribution;
        console.log('Role distribution fetched:', roleDistribution);
      } catch (err: any) {
        this.metricsError = this.handleApiError(err, 'Failed to fetch role distribution.');
        console.error('Error fetching role distribution:', err);
      } finally {
        this.isLoadingMetrics = false;
      }
    },

    /**
     * Fetches user engagement metrics.
     * API: GET /users/overview/metrics/engagement
     */
    async fetchUserEngagement(): Promise<void> {
      this.isLoadingEngagement = true;
      this.metricsError = null;
      try {
        const api = useApi();
        const engagement = await api.get<UserEngagementMetric[]>('/user-overview/metrics/engagement');
        this.userEngagement = engagement;
        console.log('User engagement metrics fetched:', engagement);
      } catch (err: any) {
        this.metricsError = this.handleApiError(err, 'Failed to fetch user engagement metrics.');
        console.error('Error fetching user engagement metrics:', err);
      } finally {
        this.isLoadingEngagement = false;
      }
    },

    /**
     * Fetches user activity trends.
     * API: GET /users/overview/metrics/activity
     * @param days Number of days to include (default: 30)
     */
    async fetchUserOverviewActivity(days: number = 30): Promise<void> {
      this.isLoadingActivity = true;
      this.metricsError = null;
      try {
        const api = useApi();
        const activity = await api.get<UserActivityData[]>('/user-overview/metrics/activity', {
          params: { days }
        });
        this.userOverviewActivity = activity;
        console.log('User activity trends fetched:', activity);
      } catch (err: any) {
        this.metricsError = this.handleApiError(err, 'Failed to fetch user activity trends.');
        console.error('Error fetching user activity trends:', err);
      } finally {
        this.isLoadingActivity = false;
      }
    },

    /**
     * Fetches recent users.
     * API: GET /users/overview/metrics/recent
     * @param limit Number of users to return (default: 10, max: 50)
     */
    async fetchRecentUsers(limit: number = 10): Promise<void> {
      this.isLoadingRecentUsers = true;
      this.metricsError = null;
      try {
        const api = useApi();
        const recentUsers = await api.get<RecentUser[]>('/user-overview/metrics/recent', {
          params: { limit }
        });
        this.recentUsers = recentUsers;
        console.log('Recent users fetched:', recentUsers);
      } catch (err: any) {
        this.metricsError = this.handleApiError(err, 'Failed to fetch recent users.');
        console.error('Error fetching recent users:', err);
      } finally {
        this.isLoadingRecentUsers = false;
      }
    },

    // ============================================================================
    // PLATFORM ACTIVITY API ACTIONS (SuperAdmin only)
    // ============================================================================

    /**
     * Fetches platform user activity (SuperAdmin only).
     * API: GET /platform/activity/users
     */
    async fetchPlatformUserActivity(): Promise<void> {
      this.isLoadingPlatformActivity = true;
      this.metricsError = null;
      try {
        const api = useApi();
        const activity = await api.get<PlatformUserActivity>('/platform/activity/users');
        this.platformUserActivity = activity;
        console.log('Platform user activity fetched:', activity);
      } catch (err: any) {
        this.metricsError = this.handleApiError(err, 'Failed to fetch platform user activity.');
        console.error('Error fetching platform user activity:', err);
      } finally {
        this.isLoadingPlatformActivity = false;
      }
    },

    // Helper methods (copied from tenantStore, can be refactored into a shared utility)
    handleApiError(err: any, defaultMessage: string): string {
      if (err.response && err.response.data && err.response.data.message) {
        if (Array.isArray(err.response.data.message)) {
          return err.response.data.message.join(', ');
        }
        return err.response.data.message;
      }
      return err.message || defaultMessage;
    },

    showSuccessNotification(message: string) {
      // TODO: Implement actual notification display using a global toast service
      console.log('Success:', message);
      // Example: useToast().add({ severity: 'success', summary: 'Success', detail: message, life: 3000 });
    },

    showErrorNotification(message: string) {
      const toastStore = useToastStore();
      toastStore.error(message);
    },

    // Utility method to refresh all overview data
    async refreshOverviewData(): Promise<void> {
      try {
        await Promise.all([
          this.fetchUserOverview(),
          this.fetchRecentUsers(10)
        ]);
      } catch (error) {
        console.error('Failed to refresh overview data:', error);
        throw error;
      }
    },

    // Utility method to clear all errors
    clearErrors(): void {
      this.error = null;
      this.overviewError = null;
      this.metricsError = null;
    },
  },
});