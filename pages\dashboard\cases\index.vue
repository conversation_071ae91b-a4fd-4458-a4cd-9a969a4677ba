<template>
  <div class="space-y-6">
    <!-- Global Search and Filters -->
    <GlobalSearchFilters
      :search-placeholder="$t('caseManagement.searchPlaceholder')"
      :filter-configs="caseFilterConfigs"
      :advanced-filter-configs="caseAdvancedFilterConfigs"
      :show-view-toggle="true"
      @search="handleSearch"
      @filter-change="handleFilterChange"
      @filters-change="handleFiltersChange"
      @view-change="handleViewChange"
    />

    <!-- Cases List -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg">
      <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between">
          <h2 class="text-lg font-medium text-gray-900 dark:text-white">
            {{ $t('caseManagement.casesCount', { count: filteredCases.length }) }}
          </h2>
          <UiButton @click="handleCreateCase" variant="primary" size="sm">
            <Icon name="material-symbols:add" class="h-4 w-4 mr-2" />
            {{ $t('caseManagement.newCase') }}
          </UiButton>
        </div>
      </div>

      <!-- Table View -->
      <div v-if="currentView === 'table'" class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead class="bg-gray-50 dark:bg-gray-800">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                {{ $t('caseManagement.case') }}
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                {{ $t('caseManagement.client') }}
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                {{ $t('caseManagement.type') }}
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                {{ $t('common.status') }}
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                {{ $t('caseManagement.priority') }}
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                {{ $t('caseManagement.actions') }}
              </th>
            </tr>
          </thead>
          <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
            <tr v-for="case_ in filteredCases" :key="case_.id" class="hover:bg-gray-50 dark:hover:bg-gray-700">
              <td class="px-6 py-4">
                <div>
                  <div class="text-sm font-medium text-gray-900 dark:text-white">{{ case_.title }}</div>
                  <div class="text-sm text-gray-500 dark:text-gray-400">{{ $t('caseManagement.created') }} {{ case_.createdAt }}</div>
                </div>
              </td>
              <td class="px-6 py-4 text-sm text-gray-900 dark:text-white">{{ case_.client }}</td>
              <td class="px-6 py-4 text-sm text-gray-600 dark:text-gray-400">{{ case_.type }}</td>
              <td class="px-6 py-4">
                <span :class="getStatusClass(case_.status)" class="px-2 py-1 rounded-full text-xs font-medium">
                  {{ case_.status }}
                </span>
              </td>
              <td class="px-6 py-4">
                <span :class="getPriorityClass(case_.priority)" class="px-2 py-1 rounded-full text-xs font-medium">
                  {{ case_.priority }}
                </span>
              </td>
              <td class="px-6 py-4">
                <UiButton size="sm" variant="ghost">
                  <Icon name="material-symbols:more-vert" class="h-4 w-4" />
                </UiButton>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Cards View -->
      <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 p-6">
        <div v-for="case_ in filteredCases" :key="case_.id" class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors">
          <div class="flex items-start justify-between mb-3">
            <h3 class="text-sm font-medium text-gray-900 dark:text-white line-clamp-2">{{ case_.title }}</h3>
            <UiButton size="sm" variant="ghost" class="ml-2">
              <Icon name="material-symbols:more-vert" class="h-4 w-4" />
            </UiButton>
          </div>
          <div class="space-y-2 text-sm">
            <div class="flex justify-between">
              <span class="text-gray-600 dark:text-gray-400">Client:</span>
              <span class="text-gray-900 dark:text-white">{{ case_.client }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600 dark:text-gray-400">Type:</span>
              <span class="text-gray-900 dark:text-white">{{ case_.type }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600 dark:text-gray-400">Status:</span>
              <span :class="getStatusClass(case_.status)" class="px-2 py-1 rounded-full text-xs font-medium">
                {{ case_.status }}
              </span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600 dark:text-gray-400">Priority:</span>
              <span :class="getPriorityClass(case_.priority)" class="px-2 py-1 rounded-full text-xs font-medium">
                {{ case_.priority }}
              </span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600 dark:text-gray-400">Created:</span>
              <span class="text-gray-900 dark:text-white">{{ case_.createdAt }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Empty State -->
      <div v-if="filteredCases.length === 0" class="text-center py-12">
        <Icon name="material-symbols:folder-open" class="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 class="text-sm font-medium text-gray-900 dark:text-white mb-2">No cases found</h3>
        <p class="text-sm text-gray-500 dark:text-gray-400 mb-4">
          {{ searchTerm || Object.keys(currentFilters).length > 0 ? 'Try adjusting your search or filters' : 'Get started by creating your first case' }}
        </p>
        <UiButton @click="handleCreateCase" variant="primary" size="sm">
          <Icon name="material-symbols:add" class="h-4 w-4 mr-2" />
          Create Case
        </UiButton>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

// Composables
const { t: $t } = useI18n()

// State
const searchTerm = ref('')
const currentFilters = ref<Record<string, any>>({})
const currentView = ref<'table' | 'cards'>('table')

// Sample data
const sampleCases = ref([
  {
    id: 1,
    title: 'Smith vs. Johnson Contract Dispute',
    client: 'John Smith',
    type: 'Contract Law',
    status: 'active',
    priority: 'high',
    createdAt: '2024-01-15'
  },
  {
    id: 2,
    title: 'ABC Corp Merger Review',
    client: 'ABC Corporation',
    type: 'Corporate Law',
    status: 'pending',
    priority: 'medium',
    createdAt: '2024-01-20'
  },
  {
    id: 3,
    title: 'Personal Injury Claim',
    client: 'Jane Doe',
    type: 'Personal Injury',
    status: 'active',
    priority: 'low',
    createdAt: '2024-01-10'
  },
  {
    id: 4,
    title: 'Estate Planning Review',
    client: 'Robert Wilson',
    type: 'Estate Law',
    status: 'closed',
    priority: 'medium',
    createdAt: '2024-01-05'
  }
])

// Filter configurations
const caseFilterConfigs = [
  {
    key: 'status',
    options: [
      { label: 'Active', value: 'active' },
      { label: 'Pending', value: 'pending' },
      { label: 'Closed', value: 'closed' },
      { label: 'On Hold', value: 'on-hold' }
    ],
    placeholder: 'All Status'
  },
  {
    key: 'type',
    options: [
      { label: 'Contract Law', value: 'Contract Law' },
      { label: 'Corporate Law', value: 'Corporate Law' },
      { label: 'Personal Injury', value: 'Personal Injury' },
      { label: 'Estate Law', value: 'Estate Law' },
      { label: 'Criminal Law', value: 'Criminal Law' }
    ],
    placeholder: 'All Types'
  }
]

const caseAdvancedFilterConfigs = [
  {
    key: 'createdDate',
    type: 'dateRange' as const,
    label: 'Created Date Range'
  },
  {
    key: 'priority',
    type: 'select' as const,
    label: 'Priority',
    placeholder: 'All Priorities',
    options: [
      { label: 'High', value: 'high' },
      { label: 'Medium', value: 'medium' },
      { label: 'Low', value: 'low' }
    ]
  },
  {
    key: 'sortBy',
    type: 'select' as const,
    label: 'Sort By',
    placeholder: 'Default',
    options: [
      { label: 'Title A-Z', value: 'title_asc' },
      { label: 'Title Z-A', value: 'title_desc' },
      { label: 'Newest First', value: 'created_desc' },
      { label: 'Oldest First', value: 'created_asc' },
      { label: 'Client A-Z', value: 'client_asc' }
    ]
  }
]

// Computed
const filteredCases = computed(() => {
  let cases = [...sampleCases.value]

  // Apply search filter
  if (searchTerm.value) {
    const search = searchTerm.value.toLowerCase()
    cases = cases.filter(case_ =>
      case_.title.toLowerCase().includes(search) ||
      case_.client.toLowerCase().includes(search) ||
      case_.type.toLowerCase().includes(search)
    )
  }

  // Apply other filters
  Object.entries(currentFilters.value).forEach(([key, value]) => {
    if (value && value !== '') {
      cases = cases.filter(case_ => case_[key as keyof typeof case_] === value)
    }
  })

  return cases
})

// Methods
const handleSearch = (search: string) => {
  searchTerm.value = search
  console.log('Search cases:', search)
}

const handleFilterChange = (key: string, value: any) => {
  console.log('Case filter changed:', key, value)
}

const handleFiltersChange = (filters: Record<string, any>) => {
  currentFilters.value = { ...filters }
  console.log('All case filters:', filters)
}

const handleViewChange = (view: 'table' | 'cards') => {
  currentView.value = view
  console.log('Case view changed:', view)
}

const handleCreateCase = () => {
  console.log('Create new case')
  // Navigate to create case page or open modal
}

// Helper methods for styling
const getStatusClass = (status: string) => {
  const classes = {
    active: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400',
    pending: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400',
    closed: 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400',
    'on-hold': 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
  }
  return classes[status as keyof typeof classes] || classes.closed
}

const getPriorityClass = (priority: string) => {
  const classes = {
    high: 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400',
    medium: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400',
    low: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
  }
  return classes[priority as keyof typeof classes] || classes.medium
}

definePageMeta({
  layout: 'dashboard',
  title: 'Cases',
  showViewToggle: true,
  showQuickFilters: true,
  showActionsMenu: true,
  showKeyboardShortcuts: true,
  showPageHeader: true,
  pageHeaderTitle: 'Legal Cases',
  pageHeaderDescription: 'Manage your legal cases and track their progress',
  pageHeaderIcon: 'material-symbols:folder',
  pageHeaderStats: [
    { key: 'active', label: 'Active', value: 15, color: 'green' },
    { key: 'pending', label: 'Pending', value: 8, color: 'yellow' },
    { key: 'total', label: 'Total', value: 23, color: 'blue' }
  ],
  showPageHeaderActions: true,
  showPageHeaderExport: true,
  showPageHeaderCreate: true,
  pageHeaderCreateLabel: 'New Case',
  pageHeaderCreateIcon: 'material-symbols:gavel',
  breadcrumb: [
    { label: 'Dashboard', href: '/dashboard' },
    { label: 'Cases' },
  ],
});
</script>