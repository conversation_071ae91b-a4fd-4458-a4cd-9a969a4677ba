<template>
    <!-- Cover Style Login Form -->
    <div class="space-y-6">
        <!-- Welcome Message -->
        <div>
            <h2 class="text-2xl font-bold text-gray-900 mb-2">{{ $t('authPages.welcomeBack') }}!</h2>
            <p class="text-gray-600">{{ $t('authPages.signInToAccount') }}</p>
        </div>

        <!-- Login Form -->
        <Form
            class="space-y-4"
            @submit="handleLogin"
            :validation-schema="loginSchema"
            role="form"
            aria-label="Sign in form"
        >
            <!-- Email Field -->
            <div>
                <Field name="email" v-slot="{ field, errorMessage }">
                    <UiInput
                        id="email"
                        type="email"
                        autocomplete="email"
                        :placeholder="$t('authPages.emailAddress')"
                        :disabled="loading"
                        class="w-full h-12 text-base"
                        :modelValue="field.value"
                        @update:modelValue="field.onChange"
                        @blur="field.onBlur"
                        :errorMessage="errorMessage"
                    />
                </Field>
            </div>

            <!-- Password Field -->
            <div class="relative">
                <Field name="password" v-slot="{ field, errorMessage }">
                    <UiInput
                        id="password"
                        :type="showPassword ? 'text' : 'password'"
                        autocomplete="current-password"
                        placeholder="Password"
                        :disabled="loading"
                        class="w-full h-12 text-base "
                        :modelValue="field.value"
                        @update:modelValue="field.onChange"
                        @blur="field.onBlur"
                        :errorMessage="errorMessage"
                    />
                </Field>
            </div>

            <!-- Submit Button -->
            <div class="pt-4">
                <button
                    type="submit"
                    :disabled="loading"
                    class="w-full flex justify-center py-3 px-4 border border-transparent rounded-lg text-base font-semibold text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 shadow-lg hover:shadow-xl"
                    :class="{ 'opacity-50 cursor-not-allowed': loading }"
                >
                    <span v-if="loading" class="flex items-center">
                        <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-50" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Signing in...
                    </span>
                    <span v-else>Login</span>
                </button>
            </div>
        </Form>

        <!-- Remember Me & Forgot Password -->
        <div class="flex items-center justify-between text-sm">
            <div class="flex items-center">
                <input
                    id="remember-me"
                    name="remember-me"
                    type="checkbox"
                    v-model="rememberMe"
                    class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    :disabled="loading"
                />
                <label for="remember-me" class="ml-2 block text-gray-700">
                    Remember me for 30 days
                </label>
            </div>
            <NuxtLink
                to="/auth/forgot-password"
                class="text-blue-600 hover:text-blue-700 transition-colors duration-200"
            >
                Forgot password?
            </NuxtLink>
        </div>

        <!-- Help Links -->
        <div class="text-center space-y-3 pt-4 border-t border-gray-200">
            <p class="text-sm text-gray-600">
                Need help accessing your account?
            </p>
            <div class="space-y-2">
                <p class="text-sm text-gray-600">
                    New to Legal SaaS?
                    <NuxtLink
                        to="/auth/register"
                        class="font-medium text-blue-600 hover:text-blue-700 transition-colors duration-200"
                    >
                        Create an account
                    </NuxtLink>
                </p>
                <div class="flex items-center justify-center space-x-4 text-sm">
                    <NuxtLink
                        to="/support"
                        class="text-gray-600 hover:text-blue-600 transition-colors duration-200"
                    >
                        Contact Support
                    </NuxtLink>
                    <div class="w-1 h-1 bg-gray-300 rounded-full"></div>
                    <NuxtLink
                        to="/auth/demo"
                        class="text-gray-600 hover:text-blue-600 transition-colors duration-200"
                    >
                        Request Demo
                    </NuxtLink>
                </div>
            </div>
        </div>

        <!-- Security Notice -->
        <div class="bg-green-50 border border-green-200 rounded-lg p-4">
            <div class="flex items-start space-x-3">
                <div class="flex-shrink-0">
                    <Icon name="heroicons:shield-check" class="h-5 w-5 text-green-600" />
                </div>
                <div class="text-sm">
                    <p class="font-medium text-green-800 mb-1">Bank-Level Security & Compliance</p>
                    <p class="text-green-700">
                        Your legal practice data is protected with AES-256 encryption, SOC 2 Type II compliance, and HIPAA-ready infrastructure.
                    </p>
                    <div class="flex items-center space-x-4 mt-3 text-xs text-green-600">
                        <div class="flex items-center space-x-1">
                            <Icon name="heroicons:lock-closed" class="h-3 w-3" />
                            <span>SSL/TLS</span>
                        </div>
                        <div class="flex items-center space-x-1">
                            <Icon name="heroicons:check-badge" class="h-3 w-3" />
                            <span>SOC 2</span>
                        </div>
                        <div class="flex items-center space-x-1">
                            <Icon name="heroicons:document-check" class="h-3 w-3" />
                            <span>HIPAA Ready</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Trust Indicators -->
        <div class="text-center space-y-2">
            <p class="text-xs text-gray-500">Trusted by leading law firms</p>
            <div class="flex items-center justify-center space-x-4">
                <div class="text-xs text-gray-400 px-2 py-1 bg-gray-100 rounded-full">ABA Certified</div>
                <div class="text-xs text-gray-400 px-2 py-1 bg-gray-100 rounded-full">ISO 27001</div>
                <div class="text-xs text-gray-400 px-2 py-1 bg-gray-100 rounded-full">GDPR Compliant</div>
            </div>
        </div>

        <!-- Footer -->
        <div class="text-center pt-6 border-t border-gray-200">
            <p class="text-xs text-gray-500">
                © 2024 Legal SaaS™ • All rights reserved
            </p>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { Form, Field } from 'vee-validate';
import { z } from 'zod';
import { toTypedSchema } from '@vee-validate/zod';
import { useAuth } from '../../composables/useAuth.js';
import { getRedirectUrl } from '../../app/features/auth/utils/helpers.js';

// Define page metadata
definePageMeta({
  layout: 'auth',
  middleware: ['guest'],
  title: 'Sign In',
  subtitle: 'Access your legal practice management platform'
});

// Access the useAuth composable
const { login: authLogin } = useAuth();
const { t: $t } = useI18n();
const showPassword = ref(false);

// Enhanced Zod validation schema
const loginSchema = toTypedSchema(
  z.object({
    email: z
      .string()
      .min(1, 'Email address is required')
      .email('Please enter a valid email address')
      .max(255, 'Email address is too long'),
    password: z
      .string()
      .min(8, 'Password must be at least 8 characters')
      .max(128, 'Password is too long'),
  })
);

// Interface for login credentials
interface LoginCredentials {
  email: string;
  password: string;
  rememberMe?: boolean;
}

// Reactive state
const loading = ref<boolean>(false);
 
const rememberMe = ref<boolean>(false);

 
// Enhanced login handler with better error handling and UX
const handleLogin = async (values: Record<string, any>) => {
  loading.value = true;
    console.log(values)
  try {
    const loginCredentials: LoginCredentials = {
      email: values.email,
      password: values.password
    };
    const loginOptions = {
      headers:{
        'x-tenant-id': 'cee8c902-83fb-40b5-b050-f95fe3f41427'
      }
    };

    // Call the login function from the useAuth composable
    await authLogin(loginCredentials, loginOptions);

    // Handle redirect after successful login
    const redirectUrl = getRedirectUrl('/dashboard');
    await navigateTo(redirectUrl);

    // Success feedback is handled by the auth composable

  } catch (error: any) {
    // Enhanced error logging for debugging
    console.error('Login error in component:', {
      error: error.message || error,
      email: values.email,
      timestamp: new Date().toISOString()
    });

    // Errors are displayed by the auth layout and composable
    // Additional client-side error handling could be added here if needed

  } finally {
    loading.value = false;
  }
};

// Enhanced SEO and meta configuration
useHead({
  title: 'Sign In - Legal SaaS Platform',
  meta: [
    {
      name: 'description',
      content: 'Secure sign in to your Legal SaaS Platform account. Access your legal practice management tools, case files, and client information.'
    },
    { name: 'robots', content: 'noindex, nofollow' },
    { property: 'og:title', content: 'Sign In - Legal SaaS Platform' },
    { property: 'og:description', content: 'Access your legal practice management platform' },
  ],
});
</script>
