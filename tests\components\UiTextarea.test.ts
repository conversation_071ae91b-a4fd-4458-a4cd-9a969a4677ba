import { describe, it, expect } from 'vitest';
import { mountSuspended } from '@nuxt/test-utils/runtime';
import UiTextarea from '../../app/shared/components/ui/UiTextarea.vue';

// Mock Icon component
const IconMock = {
  name: 'Icon',
  template: '<span class="icon-mock" :class="$attrs.class"><slot /></span>',
  props: ['name']
};

describe('UiTextarea', () => {
  const defaultProps = {
    id: 'test-textarea'
  };

  it('renders correctly with basic props', async () => {
    const wrapper = await mountSuspended(UiTextarea, {
      props: defaultProps,
      global: {
        components: {
          Icon: IconMock
        }
      }
    });

    expect(wrapper.find('textarea').exists()).toBe(true);
    expect(wrapper.find('textarea').attributes('id')).toBe('test-textarea');
  });

  it('displays label when provided', async () => {
    const wrapper = await mountSuspended(UiTextarea, {
      props: {
        ...defaultProps,
        label: 'Test Label'
      },
      global: {
        components: {
          Icon: IconMock
        }
      }
    });

    const label = wrapper.find('label');
    expect(label.exists()).toBe(true);
    expect(label.text()).toBe('Test Label');
    expect(label.attributes('for')).toBe('test-textarea');
  });

  it('emits update:modelValue when text changes', async () => {
    const wrapper = await mountSuspended(UiTextarea, {
      props: defaultProps,
      global: {
        components: {
          Icon: IconMock
        }
      }
    });

    const textarea = wrapper.find('textarea');
    await textarea.setValue('test value');

    expect(wrapper.emitted('update:modelValue')).toBeTruthy();
    expect(wrapper.emitted('update:modelValue')?.[0]).toEqual(['test value']);
  });

  it('displays error message when provided', async () => {
    const wrapper = await mountSuspended(UiTextarea, {
      props: {
        ...defaultProps,
        errorMessage: 'This field is required'
      },
      global: {
        components: {
          Icon: IconMock
        }
      }
    });

    const errorDiv = wrapper.find('.text-red-600');
    expect(errorDiv.exists()).toBe(true);
    expect(errorDiv.text()).toContain('This field is required');
  });

  it('shows character count when enabled', async () => {
    const wrapper = await mountSuspended(UiTextarea, {
      props: {
        ...defaultProps,
        modelValue: 'test',
        maxlength: 100,
        showCharCount: true
      },
      global: {
        components: {
          Icon: IconMock
        }
      }
    });

    const charCount = wrapper.find('.text-xs');
    expect(charCount.exists()).toBe(true);
    expect(charCount.text()).toBe('4/100');
  });

  it('shows clear button when clearable and has value', async () => {
    const wrapper = await mountSuspended(UiTextarea, {
      props: {
        ...defaultProps,
        modelValue: 'test value',
        clearable: true
      },
      global: {
        components: {
          Icon: IconMock
        }
      }
    });

    const clearButton = wrapper.find('button');
    expect(clearButton.exists()).toBe(true);
  });
});
