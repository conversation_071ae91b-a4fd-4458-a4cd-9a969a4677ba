/**
 * Storage Formatters Plugin
 *
 * Makes storage formatting utilities globally available in the Nuxt app
 * This plugin runs on the client side for consistent formatting
 */

import { useStorageFormatters } from '~/composables/useStorageFormatters'

export default defineNuxtPlugin(() => {
  // Get the storage formatters composable
  const storageFormatters = useStorageFormatters()

  return {
    provide: {
      // Provide the entire composable
      storageFormatters,
      
      // Provide individual formatters for convenience
      formatBytes: storageFormatters.formatBytes,
      formatBytesDetailed: storageFormatters.formatBytesDetailed,
      formatFileSize: storageFormatters.formatBytes, // Legacy compatibility
      parseStorageSize: storageFormatters.parseStorageSize,
      convertStorageUnit: storageFormatters.convertStorageUnit,
      getStorageUsagePercentage: storageFormatters.getStorageUsagePercentage,
      formatStorageUsage: storageFormatters.formatStorageUsage,
      getRemainingStorage: storageFormatters.getRemainingStorage,
      formatRemainingStorage: storageFormatters.formatRemainingStorage,
      isStorageNearCapacity: storageFormatters.isStorageNearCapacity,
      getStorageStatus: storageFormatters.getStorageStatus,
      formatBytesReactive: storageFormatters.formatBytesReactive
    }
  }
})

// Extend the NuxtApp interface for TypeScript support
declare module '#app' {
  interface NuxtApp {
    $storageFormatters: ReturnType<typeof useStorageFormatters>
    $formatBytes: ReturnType<typeof useStorageFormatters>['formatBytes']
    $formatBytesDetailed: ReturnType<typeof useStorageFormatters>['formatBytesDetailed']
    $formatFileSize: ReturnType<typeof useStorageFormatters>['formatBytes']
    $parseStorageSize: ReturnType<typeof useStorageFormatters>['parseStorageSize']
    $convertStorageUnit: ReturnType<typeof useStorageFormatters>['convertStorageUnit']
    $getStorageUsagePercentage: ReturnType<typeof useStorageFormatters>['getStorageUsagePercentage']
    $formatStorageUsage: ReturnType<typeof useStorageFormatters>['formatStorageUsage']
    $getRemainingStorage: ReturnType<typeof useStorageFormatters>['getRemainingStorage']
    $formatRemainingStorage: ReturnType<typeof useStorageFormatters>['formatRemainingStorage']
    $isStorageNearCapacity: ReturnType<typeof useStorageFormatters>['isStorageNearCapacity']
    $getStorageStatus: ReturnType<typeof useStorageFormatters>['getStorageStatus']
    $formatBytesReactive: ReturnType<typeof useStorageFormatters>['formatBytesReactive']
  }
}

declare module 'vue' {
  interface ComponentCustomProperties {
    $storageFormatters: ReturnType<typeof useStorageFormatters>
    $formatBytes: ReturnType<typeof useStorageFormatters>['formatBytes']
    $formatBytesDetailed: ReturnType<typeof useStorageFormatters>['formatBytesDetailed']
    $formatFileSize: ReturnType<typeof useStorageFormatters>['formatBytes']
    $parseStorageSize: ReturnType<typeof useStorageFormatters>['parseStorageSize']
    $convertStorageUnit: ReturnType<typeof useStorageFormatters>['convertStorageUnit']
    $getStorageUsagePercentage: ReturnType<typeof useStorageFormatters>['getStorageUsagePercentage']
    $formatStorageUsage: ReturnType<typeof useStorageFormatters>['formatStorageUsage']
    $getRemainingStorage: ReturnType<typeof useStorageFormatters>['getRemainingStorage']
    $formatRemainingStorage: ReturnType<typeof useStorageFormatters>['formatRemainingStorage']
    $isStorageNearCapacity: ReturnType<typeof useStorageFormatters>['isStorageNearCapacity']
    $getStorageStatus: ReturnType<typeof useStorageFormatters>['getStorageStatus']
    $formatBytesReactive: ReturnType<typeof useStorageFormatters>['formatBytesReactive']
  }
}
