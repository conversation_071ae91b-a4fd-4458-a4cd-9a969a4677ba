/**
 * Cases Feature Module
 * 
 * This module exports all case management functionality
 * following the barrel export pattern for clean imports.
 */

// Components
export { default as CaseList } from './components/CaseList.vue'
export { default as CaseCard } from './components/CaseCard.vue'
export { default as CaseDetails } from './components/CaseDetails.vue'
export { default as CaseForm } from './components/forms/CaseForm.vue'
export { default as CaseStatusBadge } from './components/ui/CaseStatusBadge.vue'
export { default as CasePriorityIndicator } from './components/ui/CasePriorityIndicator.vue'
export { default as CaseTimeline } from './components/views/CaseTimeline.vue'
export { default as CaseDocuments } from './components/views/CaseDocuments.vue'
export { default as CaseNotes } from './components/views/CaseNotes.vue'

// Composables
export { useCases } from './composables/useCases.js'
export { useCaseApi } from './composables/useCaseApi.js'
export { useCaseValidation } from './composables/useCaseValidation.js'
export { useCaseFilters } from './composables/useCaseFilters.js'
export { useCaseSearch } from './composables/useCaseSearch.js'

// Stores
export { useCaseStore } from './stores/caseStore.js'
export { useCaseCacheStore } from './stores/caseCacheStore.js'

// Types
export type * from './types/api.js'
export type * from './types/models.js'
export type * from './types/ui.js'

// Utils
export * from './utils/validators.js'
export * from './utils/formatters.js'
export * from './utils/helpers.js'
export * from './utils/filters.js'

// Constants
export * from './constants/case-status.js'
export * from './constants/case-types.js'
export * from './constants/priorities.js'
