<!--
  Document Viewer Component
  
  Displays documents with preview capabilities and metadata
-->

<template>
  <div class="h-screen flex flex-col">
    <!-- Loading State -->
    <div v-if="isLoading" class="flex-1 flex items-center justify-center">
      <UiSpinner size="lg" />
      <span class="ml-3 text-gray-600">Loading document...</span>
    </div>

    <!-- Document Not Found -->
    <div v-else-if="!document" class="flex-1 flex items-center justify-center">
      <div class="text-center">
        <Icon name="heroicons:exclamation-triangle" class="mx-auto h-12 w-12 text-gray-400" />
        <h3 class="mt-2 text-sm font-medium text-gray-900">Document not found</h3>
        <p class="mt-1 text-sm text-gray-500">The document you're looking for doesn't exist.</p>
        <div class="mt-6">
          <NuxtLink
            to="/dashboard/documents"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-indigo-600 bg-indigo-100 hover:bg-indigo-200"
          >
            Back to Documents
          </NuxtLink>
        </div>
      </div>
    </div>

    <!-- Document Viewer -->
    <template v-else>
      <!-- Header -->
      <div class="bg-white border-b border-gray-200 px-6 py-4">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <NuxtLink
              to="/dashboard/documents"
              class="inline-flex items-center text-sm text-gray-500 hover:text-gray-700 transition-colors duration-200"
            >
              <Icon name="heroicons:arrow-left" class="h-4 w-4 mr-1" />
              Back to Documents
            </NuxtLink>
            
            <div class="h-4 w-px bg-gray-300"></div>
            
            <div>
              <h1 class="text-lg font-medium text-gray-900">{{ document.name }}</h1>
              <p class="text-sm text-gray-500">
                {{ formatFileSize(document.size) }} • {{ formatDate(document.uploadedAt) }}
              </p>
            </div>
          </div>

          <div class="flex items-center space-x-3">
            <!-- Zoom Controls -->
            <div class="flex items-center space-x-1 bg-gray-100 rounded-md p-1">
              <button
                @click="zoomOut"
                :disabled="zoomLevel <= 50"
                class="p-1 rounded text-gray-600 hover:text-gray-900 hover:bg-white disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
              >
                <Icon name="heroicons:minus" class="h-4 w-4" />
              </button>
              <span class="px-2 text-sm text-gray-700 min-w-[3rem] text-center">{{ zoomLevel }}%</span>
              <button
                @click="zoomIn"
                :disabled="zoomLevel >= 200"
                class="p-1 rounded text-gray-600 hover:text-gray-900 hover:bg-white disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
              >
                <Icon name="heroicons:plus" class="h-4 w-4" />
              </button>
            </div>

            <!-- Actions -->
            <button
              @click="downloadDocument"
              class="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200"
            >
              <Icon name="heroicons:arrow-down-tray" class="h-4 w-4 mr-2" />
              Download
            </button>

            <button
              @click="shareDocument"
              class="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200"
            >
              <Icon name="heroicons:share" class="h-4 w-4 mr-2" />
              Share
            </button>

            <!-- More Actions Menu -->
            <div class="relative">
              <button
                @click="showActionsMenu = !showActionsMenu"
                class="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200"
              >
                <Icon name="heroicons:ellipsis-horizontal" class="h-4 w-4" />
              </button>

              <!-- Actions Dropdown -->
              <div
                v-if="showActionsMenu"
                class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 z-10"
              >
                <div class="py-1">
                  <button
                    @click="editDocument"
                    class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    <Icon name="heroicons:pencil" class="h-4 w-4 mr-2 inline" />
                    Edit Details
                  </button>
                  <button
                    @click="moveDocument"
                    class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    <Icon name="heroicons:folder" class="h-4 w-4 mr-2 inline" />
                    Move to Folder
                  </button>
                  <button
                    @click="deleteDocument"
                    class="block w-full text-left px-4 py-2 text-sm text-red-700 hover:bg-red-50"
                  >
                    <Icon name="heroicons:trash" class="h-4 w-4 mr-2 inline" />
                    Delete
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Main Content -->
      <div class="flex-1 flex">
        <!-- Document Preview -->
        <div class="flex-1 bg-gray-100 overflow-auto">
          <div class="p-6">
            <!-- PDF Preview -->
            <div v-if="document.type === 'application/pdf'" class="bg-white shadow-lg rounded-lg overflow-hidden">
              <div class="aspect-[8.5/11] bg-white flex items-center justify-center" :style="{ transform: `scale(${zoomLevel / 100})` }">
                <div class="text-center text-gray-500">
                  <Icon name="heroicons:document-text" class="mx-auto h-16 w-16 mb-4" />
                  <p class="text-lg font-medium">{{ $t('documents.documentViewer') }}</p>
                  <p class="text-sm">{{ document.name }}</p>
                  <p class="text-xs mt-2">{{ $t('ui.pdfViewerIntegration') }}</p>
                </div>
              </div>
            </div>

            <!-- Text Document Preview -->
            <div v-else-if="isTextDocument(document.type)" class="bg-white shadow-lg rounded-lg p-6" :style="{ transform: `scale(${zoomLevel / 100})` }">
              <div class="prose max-w-none">
                <h1 class="text-xl font-bold mb-4">{{ document.name }}</h1>
                <div class="text-gray-700 leading-relaxed">
                  <p>This is a preview of the document content.</p>
                  <p>In a real implementation, the actual document content would be displayed here.</p>
                  <p>The content would be parsed and rendered based on the document type.</p>
                </div>
              </div>
            </div>

            <!-- Unsupported Format -->
            <div v-else class="bg-white shadow-lg rounded-lg p-12 text-center">
              <Icon name="heroicons:document" class="mx-auto h-16 w-16 text-gray-400 mb-4" />
              <h3 class="text-lg font-medium text-gray-900 mb-2">Preview not available</h3>
              <p class="text-gray-500 mb-4">This file type cannot be previewed in the browser.</p>
              <button
                @click="downloadDocument"
                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
              >
                <Icon name="heroicons:arrow-down-tray" class="h-4 w-4 mr-2" />
                Download to View
              </button>
            </div>
          </div>
        </div>

        <!-- Sidebar -->
        <div class="w-80 bg-white border-l border-gray-200 overflow-y-auto">
          <div class="p-6 space-y-6">
            <!-- Document Info -->
            <div>
              <h3 class="text-sm font-medium text-gray-900 mb-3">Document Information</h3>
              <dl class="space-y-2">
                <div>
                  <dt class="text-xs font-medium text-gray-500">Type</dt>
                  <dd class="text-sm text-gray-900">{{ formatDocumentType(document.documentType) }}</dd>
                </div>
                <div>
                  <dt class="text-xs font-medium text-gray-500">Size</dt>
                  <dd class="text-sm text-gray-900">{{ formatFileSize(document.size) }}</dd>
                </div>
                <div>
                  <dt class="text-xs font-medium text-gray-500">Uploaded</dt>
                  <dd class="text-sm text-gray-900">{{ formatDate(document.uploadedAt) }}</dd>
                </div>
                <div v-if="document.caseId">
                  <dt class="text-xs font-medium text-gray-500">Associated Case</dt>
                  <dd class="text-sm text-gray-900">{{ document.caseName }}</dd>
                </div>
              </dl>
            </div>

            <!-- Tags -->
            <div v-if="document.tags && document.tags.length > 0">
              <h3 class="text-sm font-medium text-gray-900 mb-3">Tags</h3>
              <div class="flex flex-wrap gap-2">
                <span
                  v-for="tag in document.tags"
                  :key="tag"
                  class="inline-flex px-2 py-1 text-xs font-medium bg-gray-100 text-gray-800 rounded-full"
                >
                  {{ tag }}
                </span>
              </div>
            </div>

            <!-- Description -->
            <div v-if="document.description">
              <h3 class="text-sm font-medium text-gray-900 mb-3">Description</h3>
              <p class="text-sm text-gray-700">{{ document.description }}</p>
            </div>

            <!-- Privacy Settings -->
            <div>
              <h3 class="text-sm font-medium text-gray-900 mb-3">Privacy & Access</h3>
              <div class="space-y-2">
                <div class="flex items-center">
                  <Icon
                    :name="document.confidential ? 'heroicons:lock-closed' : 'heroicons:lock-open'"
                    :class="document.confidential ? 'text-red-500' : 'text-green-500'"
                    class="h-4 w-4 mr-2"
                  />
                  <span class="text-sm text-gray-700">
                    {{ document.confidential ? 'Confidential' : 'Not Confidential' }}
                  </span>
                </div>
                <div class="flex items-center">
                  <Icon
                    :name="document.clientAccess ? 'heroicons:eye' : 'heroicons:eye-slash'"
                    :class="document.clientAccess ? 'text-green-500' : 'text-gray-400'"
                    class="h-4 w-4 mr-2"
                  />
                  <span class="text-sm text-gray-700">
                    {{ document.clientAccess ? 'Client Access Allowed' : 'No Client Access' }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'

// Composables
const route = useRoute()
const router = useRouter()

// State
const isLoading = ref(true)
const document = ref(null)
const zoomLevel = ref(100)
const showActionsMenu = ref(false)

// Mock document data
const mockDocument = {
  id: '1',
  name: 'Contract Agreement.pdf',
  type: 'application/pdf',
  documentType: 'contract',
  size: 2048576,
  uploadedAt: '2024-01-15T10:30:00Z',
  caseId: '1',
  caseName: 'CASE-2024-001 - Contract Dispute',
  tags: ['contract', 'confidential', 'client-review'],
  description: 'Main contract agreement between parties with terms and conditions.',
  confidential: true,
  clientAccess: false,
  url: '/documents/contract-agreement.pdf'
}

// Methods
const loadDocument = async () => {
  try {
    const documentId = route.query.id
    console.log('Loading document:', documentId)
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    document.value = mockDocument
  } catch (error) {
    console.error('Error loading document:', error)
  } finally {
    isLoading.value = false
  }
}

const zoomIn = () => {
  if (zoomLevel.value < 200) {
    zoomLevel.value += 25
  }
}

const zoomOut = () => {
  if (zoomLevel.value > 50) {
    zoomLevel.value -= 25
  }
}

const isTextDocument = (type: string) => {
  return type.includes('text') || type.includes('word') || type.includes('document')
}

const formatFileSize = (bytes: number) => {
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  if (bytes === 0) return '0 Bytes'
  const i = Math.floor(Math.log(bytes) / Math.log(1024))
  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const formatDocumentType = (type: string) => {
  const types = {
    contract: 'Contract',
    'legal-brief': 'Legal Brief',
    correspondence: 'Correspondence',
    evidence: 'Evidence',
    template: 'Template',
    other: 'Other'
  }
  return types[type as keyof typeof types] || 'Document'
}

const downloadDocument = () => {
  console.log('Downloading document:', document.value?.name)
  // Implement download logic
}

const shareDocument = () => {
  console.log('Sharing document:', document.value?.name)
  showActionsMenu.value = false
  // Implement share logic
}

const editDocument = () => {
  console.log('Editing document:', document.value?.name)
  showActionsMenu.value = false
  // Navigate to edit form
}

const moveDocument = () => {
  console.log('Moving document:', document.value?.name)
  showActionsMenu.value = false
  // Implement move logic
}

const deleteDocument = () => {
  if (confirm(`Are you sure you want to delete "${document.value?.name}"?`)) {
    console.log('Deleting document:', document.value?.name)
    showActionsMenu.value = false
    router.push('/dashboard/documents')
  }
}

// Close actions menu when clicking outside
const handleClickOutside = (event: Event) => {
  if (showActionsMenu.value) {
    showActionsMenu.value = false
  }
}

// Lifecycle
onMounted(() => {
  loadDocument()
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>
