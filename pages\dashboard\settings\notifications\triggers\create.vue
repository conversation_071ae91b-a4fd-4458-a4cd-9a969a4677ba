<template>
  <div>
    <AppBreadcrumbs :items="breadcrumbs" />
    <UiCard class="mt-4">
      <template #header>
        <h2 class="text-lg font-semibold">Create New Notification Trigger</h2>
      </template>

      <NotificationTriggerForm
        :is-visible="true" 
        :tenant-id="authStore.currentUser?.tenantId || ''"
        @trigger-saved="handleTriggerSaved"
        @close="navigateToTriggersList" 
      />
    </UiCard>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useRouter } from '#app';
import AppBreadcrumbs from '~/components/layout/dashboard/AppBreadcrumbs.vue';
import UiCard from '~/components/ui/UiCard.vue';
import NotificationTriggerForm from '~/components/notifications/NotificationTriggerForm.vue';
import { useAuthStore } from '~/stores/auth';
import { PlatformRoles, TenantRoles } from '~/app/features/auth/constants/roles';// For RBAC if needed on this page directly

definePageMeta({
  layout: 'dashboard',
  middleware: ['rbac'], // 'auth' is global
  roles: [TenantRoles.TENANT_OWNER, TenantRoles.ADMIN],
  title: 'Create Notification Trigger',
});

const router = useRouter();
const authStore = useAuthStore();

const breadcrumbs = computed(() => [
  { label: 'Dashboard', href: '/dashboard' },
  { label: 'Settings', href: '/dashboard/settings' },
  { label: 'Notifications', href: '/dashboard/settings/notifications' },
  { label: 'Triggers', href: '/dashboard/settings/notifications/triggers' },
  { label: 'Create', current: true },
]);

const handleTriggerSaved = () => {
  // The form itself will show a toast on success via the store.
  // Navigate back to the list page.
  router.push('/dashboard/settings/notifications/triggers');
};

const navigateToTriggersList = () => {
  router.push('/dashboard/settings/notifications/triggers');
};
</script>