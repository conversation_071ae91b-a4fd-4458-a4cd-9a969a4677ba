# Settings System Documentation

## Overview

The Legal SaaS Platform features a comprehensive, modern settings system designed with robust UI/UX patterns, responsive design, and advanced functionality including search, validation, and state management.

## Architecture

### Core Components

#### 1. Main Settings Page (`pages/dashboard/settings/index.vue`)
- **Modern Layout**: Split-screen design with navigation sidebar and content area
- **Search Functionality**: Real-time search across all settings with keyword matching
- **Responsive Design**: Mobile-first approach with collapsible navigation
- **Category Organization**: Settings grouped into logical categories

#### 2. Settings Components

##### SettingsCard (`components/settings/SettingsCard.vue`)
- Enhanced card component with icons, headers, and actions
- Consistent styling and spacing
- Support for custom headers and footers

##### SettingsForm (`components/settings/SettingsForm.vue`)
- Form wrapper with built-in save/reset functionality
- Loading states and change tracking
- Visual feedback for save status

##### SettingsSection (`components/settings/SettingsSection.vue`)
- Section organizer within settings cards
- Consistent headers and descriptions

##### SettingsSearch (`components/settings/SettingsSearch.vue`)
- Advanced search with dropdown results
- Keyboard navigation support
- Category and keyword filtering

##### SettingsValidationFeedback (`components/settings/SettingsValidationFeedback.vue`)
- Comprehensive feedback component
- Support for success, error, warning, info, and loading states
- Dismissible notifications

### Composables

#### useSettingsState (`composables/useSettingsState.ts`)
- Complete state management for settings
- Auto-save functionality
- Change tracking and validation
- API integration

#### useSettingsValidation (`composables/useSettingsValidation.ts`)
- Comprehensive form validation
- Built-in validation rules
- Custom validation support
- Field-level error handling

## Features

### 1. Enhanced User Experience
- **Visual Hierarchy**: Clear organization with icons and descriptions
- **Search & Filter**: Find settings quickly across all categories
- **Real-time Feedback**: Immediate validation and save status
- **Responsive Design**: Works perfectly on all screen sizes

### 2. Developer Experience
- **Modular Components**: Reusable, composable architecture
- **Type Safety**: Full TypeScript support
- **Validation System**: Comprehensive form validation
- **State Management**: Robust state handling with change tracking

### 3. Advanced Functionality
- **Auto-save**: Optional automatic saving of changes
- **Change Tracking**: Visual indicators for unsaved changes
- **Error Handling**: Comprehensive error states and recovery
- **Accessibility**: Full keyboard navigation and screen reader support

## Usage Examples

### Basic Settings Component

```vue
<template>
  <SettingsCard
    title="Platform Settings"
    subtitle="Configure your platform preferences"
    icon="heroicons:cog-6-tooth"
  >
    <SettingsForm
      :loading="loading"
      :has-changes="hasChanges"
      :save-status="saveStatus"
      @submit="saveSettings"
      @reset="resetSettings"
    >
      <SettingsSection
        title="Basic Configuration"
        description="Essential platform settings"
      >
        <!-- Your form fields here -->
      </SettingsSection>
    </SettingsForm>
  </SettingsCard>
</template>
```

### Using Settings State Management

```typescript
import { createSettingsState } from '~/composables/useSettingsState';

const {
  data,
  loading,
  hasChanges,
  saveStatus,
  save,
  reset
} = createSettingsState('platform', {
  name: '',
  description: ''
}, {
  autoSave: true,
  autoSaveDelay: 2000
});
```

### Adding Validation

```typescript
import { useSettingsValidation, validationRules } from '~/composables/useSettingsValidation';

const validation = useSettingsValidation();

// Add field validation
validation.addField('email', validationRules.requiredEmail);
validation.addField('name', validationRules.platformName);

// Validate field
const isValid = validation.validate('email', emailValue);
```

## Customization

### Adding New Settings Categories

1. Update the `allSettingsItems` array in `pages/dashboard/settings/index.vue`
2. Create the corresponding component in `components/settings/platform/`
3. Add appropriate icons and descriptions

### Custom Validation Rules

```typescript
const customRule = {
  custom: (value: string) => {
    if (value && !value.startsWith('https://')) {
      return 'URL must start with https://';
    }
    return null;
  }
};
```

## Best Practices

1. **Consistent Naming**: Use descriptive names for settings and components
2. **Validation**: Always validate user input with appropriate feedback
3. **Loading States**: Show loading indicators for async operations
4. **Error Handling**: Provide clear error messages and recovery options
5. **Accessibility**: Ensure keyboard navigation and screen reader support
6. **Responsive Design**: Test on all screen sizes and devices

## Migration Guide

### From Old Settings System

1. Replace `UiCard` with `SettingsCard` for enhanced features
2. Wrap forms with `SettingsForm` for built-in functionality
3. Use `SettingsSection` to organize content within cards
4. Implement proper state management with `useSettingsState`
5. Add validation using `useSettingsValidation`

## Performance Considerations

- **Lazy Loading**: Settings components are loaded on-demand
- **Debounced Search**: Search queries are debounced to prevent excessive API calls
- **Change Tracking**: Efficient change detection using JSON comparison
- **Auto-save**: Configurable delays to prevent excessive save operations

## Browser Support

- Modern browsers with ES2020+ support
- Mobile browsers (iOS Safari, Chrome Mobile)
- Responsive design tested on all major screen sizes
- Accessibility features for assistive technologies
