export interface TemplateCategory {
  id: string
  name: string
  description: string
  icon: string
  color: string
  type: 'document' | 'notification' | 'invoice'
  isActive: boolean
  order: number
  metadata?: Record<string, any>
}

export interface TemplateCategoryGroup {
  type: 'document' | 'notification' | 'invoice'
  name: string
  description: string
  icon: string
  color: string
  categories: TemplateCategory[]
}

// Dynamic template categories with enhanced metadata
export const TEMPLATE_CATEGORIES: TemplateCategoryGroup[] = [
  {
    type: 'document',
    name: 'Document Templates',
    description: 'Legal documents, contracts, and agreements',
    icon: 'heroicons:document-text',
    color: 'blue',
    categories: [
      {
        id: 'contract',
        name: 'Contracts',
        description: 'Service agreements, employment contracts, and legal contracts',
        icon: 'heroicons:document-check',
        color: 'blue',
        type: 'document',
        isActive: true,
        order: 1,
        metadata: {
          requiresSignature: true,
          hasVariables: true,
          defaultLanguage: 'en'
        }
      },
      {
        id: 'case_response',
        name: 'Case Responses',
        description: 'Legal case responses and court documents',
        icon: 'heroicons:scale',
        color: 'indigo',
        type: 'document',
        isActive: true,
        order: 2,
        metadata: {
          requiresSignature: true,
          hasVariables: true,
          courtRequired: true
        }
      },
      {
        id: 'consent_form',
        name: 'Consent Forms',
        description: 'Client consent and authorization forms',
        icon: 'heroicons:clipboard-document-check',
        color: 'green',
        type: 'document',
        isActive: true,
        order: 3,
        metadata: {
          requiresSignature: true,
          hasVariables: true,
          clientRequired: true
        }
      },
      {
        id: 'termination_notice',
        name: 'Termination Notices',
        description: 'Employment and contract termination notices',
        icon: 'heroicons:exclamation-triangle',
        color: 'red',
        type: 'document',
        isActive: true,
        order: 4,
        metadata: {
          requiresSignature: false,
          hasVariables: true,
          urgentDelivery: true
        }
      },
      {
        id: 'case_closure',
        name: 'Case Closures',
        description: 'Case closure documents and final reports',
        icon: 'heroicons:check-circle',
        color: 'emerald',
        type: 'document',
        isActive: true,
        order: 5,
        metadata: {
          requiresSignature: true,
          hasVariables: true,
          finalDocument: true
        }
      },
      {
        id: 'legal_memo',
        name: 'Legal Memos',
        description: 'Internal legal memorandums and analysis',
        icon: 'heroicons:document-text',
        color: 'purple',
        type: 'document',
        isActive: true,
        order: 6,
        metadata: {
          requiresSignature: false,
          hasVariables: true,
          internal: true
        }
      },
      {
        id: 'power_of_attorney',
        name: 'Power of Attorney',
        description: 'Legal authorization and power of attorney documents',
        icon: 'heroicons:key',
        color: 'amber',
        type: 'document',
        isActive: true,
        order: 7,
        metadata: {
          requiresSignature: true,
          hasVariables: true,
          notarizationRequired: true
        }
      }
    ]
  },
  {
    type: 'notification',
    name: 'Notification Templates',
    description: 'Email, SMS, push, and in-app notifications',
    icon: 'heroicons:bell',
    color: 'orange',
    categories: [
      {
        id: 'reminder',
        name: 'Reminders',
        description: 'Appointment and deadline reminders',
        icon: 'heroicons:clock',
        color: 'blue',
        type: 'notification',
        isActive: true,
        order: 1,
        metadata: {
          channels: ['email', 'sms', 'push', 'in_app'],
          scheduling: true,
          recurring: true
        }
      },
      {
        id: 'case_update',
        name: 'Case Updates',
        description: 'Case status and progress notifications',
        icon: 'heroicons:arrow-path',
        color: 'indigo',
        type: 'notification',
        isActive: true,
        order: 2,
        metadata: {
          channels: ['email', 'sms', 'in_app'],
          priority: 'high',
          clientVisible: true
        }
      },
      {
        id: 'document_ready',
        name: 'Document Ready',
        description: 'Document completion and availability notifications',
        icon: 'heroicons:document-check',
        color: 'green',
        type: 'notification',
        isActive: true,
        order: 3,
        metadata: {
          channels: ['email', 'sms', 'push'],
          attachments: true,
          downloadLink: true
        }
      },
      {
        id: 'payment_reminder',
        name: 'Payment Reminders',
        description: 'Invoice and payment due notifications',
        icon: 'heroicons:currency-dollar',
        color: 'yellow',
        type: 'notification',
        isActive: true,
        order: 4,
        metadata: {
          channels: ['email', 'sms'],
          paymentLink: true,
          escalation: true
        }
      },
      {
        id: 'system_alert',
        name: 'System Alerts',
        description: 'System maintenance and security alerts',
        icon: 'heroicons:exclamation-triangle',
        color: 'red',
        type: 'notification',
        isActive: true,
        order: 5,
        metadata: {
          channels: ['email', 'push', 'in_app', 'log'],
          priority: 'urgent',
          systemWide: true
        }
      },
      {
        id: 'welcome',
        name: 'Welcome Messages',
        description: 'New user and client welcome notifications',
        icon: 'heroicons:hand-raised',
        color: 'emerald',
        type: 'notification',
        isActive: true,
        order: 6,
        metadata: {
          channels: ['email', 'in_app'],
          onboarding: true,
          personalized: true
        }
      }
    ]
  },
  {
    type: 'invoice',
    name: 'Invoice Templates',
    description: 'Billing, receipts, and financial documents',
    icon: 'heroicons:currency-dollar',
    color: 'green',
    categories: [
      {
        id: 'standard_invoice',
        name: 'Standard Invoice',
        description: 'Regular client billing invoices',
        icon: 'heroicons:document-currency-dollar',
        color: 'green',
        type: 'invoice',
        isActive: true,
        order: 1,
        metadata: {
          taxCalculation: true,
          paymentTerms: true,
          multiCurrency: false
        }
      },
      {
        id: 'payment_receipt',
        name: 'Payment Receipt',
        description: 'Payment confirmation and receipts',
        icon: 'heroicons:receipt-percent',
        color: 'emerald',
        type: 'invoice',
        isActive: true,
        order: 2,
        metadata: {
          paymentMethod: true,
          transactionId: true,
          autoGenerated: true
        }
      },
      {
        id: 'retainer_invoice',
        name: 'Retainer Invoice',
        description: 'Legal retainer fee invoices',
        icon: 'heroicons:banknotes',
        color: 'blue',
        type: 'invoice',
        isActive: true,
        order: 3,
        metadata: {
          retainerTracking: true,
          escrowAccount: true,
          trustAccount: true
        }
      },
      {
        id: 'expense_report',
        name: 'Expense Report',
        description: 'Case-related expense reports',
        icon: 'heroicons:calculator',
        color: 'purple',
        type: 'invoice',
        isActive: true,
        order: 4,
        metadata: {
          expenseCategories: true,
          receiptsRequired: true,
          clientReimbursable: true
        }
      },
      {
        id: 'credit_note',
        name: 'Credit Note',
        description: 'Credit notes and refund documents',
        icon: 'heroicons:arrow-uturn-left',
        color: 'orange',
        type: 'invoice',
        isActive: true,
        order: 5,
        metadata: {
          refundTracking: true,
          originalInvoice: true,
          reasonRequired: true
        }
      }
    ]
  }
]

// Utility functions
export const getTemplateCategories = (type?: 'document' | 'notification' | 'invoice') => {
  if (type) {
    return TEMPLATE_CATEGORIES.find(group => group.type === type)?.categories || []
  }
  return TEMPLATE_CATEGORIES.flatMap(group => group.categories)
}

export const getTemplateCategoryById = (id: string) => {
  return getTemplateCategories().find(category => category.id === id)
}

export const getTemplateCategoriesByType = (type: 'document' | 'notification' | 'invoice') => {
  return getTemplateCategories(type).filter(category => category.isActive)
    .sort((a, b) => a.order - b.order)
}

export const getTemplateCategoryGroup = (type: 'document' | 'notification' | 'invoice') => {
  return TEMPLATE_CATEGORIES.find(group => group.type === type)
}

export const addCustomCategory = (category: TemplateCategory) => {
  const group = TEMPLATE_CATEGORIES.find(g => g.type === category.type)
  if (group) {
    group.categories.push(category)
  }
}

export const updateCategory = (id: string, updates: Partial<TemplateCategory>) => {
  const categories = getTemplateCategories()
  const categoryIndex = categories.findIndex(cat => cat.id === id)
  if (categoryIndex !== -1) {
    Object.assign(categories[categoryIndex], updates)
  }
}

export const toggleCategoryStatus = (id: string) => {
  const category = getTemplateCategoryById(id)
  if (category) {
    category.isActive = !category.isActive
  }
}
