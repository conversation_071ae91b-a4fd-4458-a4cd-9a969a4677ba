# Forms Grid Component

The `Grid.vue` component provides a flexible and responsive grid layout system for form elements. It integrates seamlessly with the forms system using the `useFormsProps` composable and supports various layout configurations.

## Features

- **Responsive Design**: Automatically adapts to different screen sizes
- **Auto-fit Layout**: Automatically fits items based on minimum width
- **Custom Breakpoints**: Define specific column counts for different screen sizes
- **Flexible Gap Control**: Customize spacing between grid items
- **Forms Integration**: Works with the forms schema system
- **TypeScript Support**: Full type safety and IntelliSense

## Basic Usage

### In Schema-driven Forms

```typescript
const schema = [
  {
    card: {
      title: 'User Information',
      content: [
        {
          grid: {
            cols: 2,
            gap: '4',
            content: [
              { name: 'firstName', label: 'First Name', component: 'UiInput' },
              { name: 'lastName', label: 'Last Name', component: 'UiInput' },
              { name: 'email', label: 'Email', component: 'UiInput' },
              { name: 'phone', label: 'Phone', component: 'UiInput' },
            ]
          }
        }
      ]
    }
  }
]
```

### Direct Component Usage

```vue
<template>
  <forms-grid :cols="3" gap="6">
    <forms-field name="field1" label="Field 1" component="UiInput" />
    <forms-field name="field2" label="Field 2" component="UiInput" />
    <forms-field name="field3" label="Field 3" component="UiInput" />
  </forms-grid>
</template>
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `cols` | `number \| string` | `1` | Number of columns or custom grid classes |
| `gap` | `string` | `'6'` | Gap size between grid items (Tailwind spacing) |
| `responsive` | `boolean` | `true` | Enable responsive behavior |
| `autoFit` | `boolean` | `false` | Use auto-fit layout with minimum width |
| `minWidth` | `string` | `'250px'` | Minimum width for auto-fit items |
| `maxCols` | `number` | `6` | Maximum number of columns for responsive |
| `breakpoints` | `object` | `{}` | Custom breakpoint configuration |
| `props` | `any` | `{}` | Additional props (for forms integration) |

## Layout Types

### 1. Simple Grid

```typescript
{
  grid: {
    cols: 3,
    content: [/* fields */]
  }
}
```

### 2. Responsive Grid

```typescript
{
  grid: {
    cols: 4,
    responsive: true,
    content: [/* fields */]
  }
}
```

### 3. Auto-fit Grid

```typescript
{
  grid: {
    autoFit: true,
    minWidth: '300px',
    content: [/* fields */]
  }
}
```

### 4. Custom Breakpoints

```typescript
{
  grid: {
    cols: 6,
    responsive: true,
    breakpoints: {
      sm: 2,
      md: 3,
      lg: 4,
      xl: 5,
      '2xl': 6
    },
    content: [/* fields */]
  }
}
```

### 5. Custom Grid Classes

```typescript
{
  grid: {
    cols: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4',
    content: [/* fields */]
  }
}
```

## Responsive Behavior

When `responsive: true` is enabled, the component automatically generates responsive classes:

- **Mobile**: 1 column (default)
- **Small (sm)**: Up to 2 columns
- **Medium (md)**: Up to 3 columns  
- **Large (lg)**: Up to 4 columns
- **Extra Large (xl)**: Full column count
- **2XL**: Full column count

## Gap Options

The `gap` prop accepts Tailwind spacing values:

- `'1'` → `gap-1` (0.25rem)
- `'2'` → `gap-2` (0.5rem)
- `'4'` → `gap-4` (1rem)
- `'6'` → `gap-6` (1.5rem)
- `'8'` → `gap-8` (2rem)

## Examples

### Contact Form

```typescript
const contactSchema = [
  {
    card: {
      title: 'Contact Information',
      content: [
        {
          grid: {
            cols: 2,
            content: [
              { name: 'firstName', label: 'First Name', component: 'UiInput' },
              { name: 'lastName', label: 'Last Name', component: 'UiInput' },
            ]
          }
        },
        {
          grid: {
            cols: 1,
            content: [
              { name: 'email', label: 'Email Address', component: 'UiInput' },
              { name: 'message', label: 'Message', component: 'UiTextarea' },
            ]
          }
        }
      ]
    }
  }
]
```

### Settings Panel

```typescript
const settingsSchema = [
  {
    card: {
      title: 'Preferences',
      content: [
        {
          grid: {
            cols: 3,
            responsive: true,
            content: [
              { name: 'notifications', label: 'Email Notifications', component: 'UiCheckbox' },
              { name: 'darkMode', label: 'Dark Mode', component: 'UiCheckbox' },
              { name: 'autoSave', label: 'Auto Save', component: 'UiCheckbox' },
              { name: 'showTips', label: 'Show Tips', component: 'UiCheckbox' },
              { name: 'compactView', label: 'Compact View', component: 'UiCheckbox' },
              { name: 'soundEffects', label: 'Sound Effects', component: 'UiCheckbox' },
            ]
          }
        }
      ]
    }
  }
]
```

## Integration with Forms System

The Grid component integrates with the forms system through:

1. **Schema Rendering**: Automatically rendered by `SchemaRender.vue`
2. **Props Integration**: Uses `useFormsProps` for dynamic prop resolution
3. **Validation**: Works with form validation and error states
4. **Reactive Updates**: Supports reactive prop updates

## Best Practices

1. **Mobile First**: Always consider mobile layout first
2. **Consistent Gaps**: Use consistent gap values throughout your forms
3. **Logical Grouping**: Group related fields together in grids
4. **Responsive Testing**: Test on different screen sizes
5. **Accessibility**: Ensure proper tab order and focus management

## Testing

Visit `/test/forms-grid` to see live examples and test different configurations.
