<template>
  <div class="flex items-center gap-2">
    <!-- Connection Status -->
    <div class="flex items-center gap-2 px-3 py-1.5 rounded-full text-sm" :class="connectionStatusClass">
      <div class="relative">
        <div 
          :class="[
            'w-2 h-2 rounded-full',
            isConnected ? 'bg-green-500' : 'bg-red-500'
          ]"
        ></div>
        <div 
          v-if="isConnected"
          class="absolute inset-0 w-2 h-2 bg-green-500 rounded-full animate-ping"
        ></div>
      </div>
      <span class="font-medium">
        {{ isConnected ? 'Live' : 'Offline' }}
      </span>
    </div>

    <!-- Last Update Time -->
    <div class="text-xs text-gray-500 dark:text-gray-400">
      Last updated: {{ formatLastUpdate(lastUpdate) }}
    </div>

    <!-- Auto-refresh Toggle -->
    <UiButton
      @click="toggleAutoRefresh"
      size="sm"
      variant="ghost"
      :class="{ 'text-brandPrimary': autoRefresh }"
      class="p-1"
    >
      <Icon 
        :name="autoRefresh ? 'material-symbols:pause' : 'material-symbols:play-arrow'" 
        class="h-4 w-4" 
      />
    </UiButton>

    <!-- Manual Refresh -->
    <UiButton
      @click="$emit('refresh')"
      size="sm"
      variant="ghost"
      :disabled="isRefreshing"
      class="p-1"
    >
      <Icon 
        name="material-symbols:refresh" 
        :class="[
          'h-4 w-4',
          isRefreshing ? 'animate-spin' : ''
        ]" 
      />
    </UiButton>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, onUnmounted, ref } from 'vue'

interface Props {
  autoRefresh?: boolean
  refreshInterval?: number
  isRefreshing?: boolean
}

interface Emits {
  (e: 'refresh'): void
  (e: 'auto-refresh-changed', enabled: boolean): void
}

const props = withDefaults(defineProps<Props>(), {
  autoRefresh: true,
  refreshInterval: 30000, // 30 seconds
  isRefreshing: false
})

const emit = defineEmits<Emits>()

// State
const isConnected = ref(true)
const lastUpdate = ref(new Date())
const autoRefresh = ref(props.autoRefresh)

// Auto-refresh timer
let refreshTimer: NodeJS.Timeout | null = null

// Computed properties
const connectionStatusClass = computed(() => {
  if (isConnected.value) {
    return 'bg-green-50 text-green-700 border border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800'
  } else {
    return 'bg-red-50 text-red-700 border border-red-200 dark:bg-red-900/20 dark:text-red-400 dark:border-red-800'
  }
})

// Methods
const formatLastUpdate = (date: Date) => {
  const now = new Date()
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)
  
  if (diffInSeconds < 60) {
    return `${diffInSeconds}s ago`
  } else if (diffInSeconds < 3600) {
    return `${Math.floor(diffInSeconds / 60)}m ago`
  } else {
    return date.toLocaleTimeString()
  }
}

const toggleAutoRefresh = () => {
  autoRefresh.value = !autoRefresh.value
  emit('auto-refresh-changed', autoRefresh.value)
  
  if (autoRefresh.value) {
    startAutoRefresh()
  } else {
    stopAutoRefresh()
  }
}

const startAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
  }
  
  refreshTimer = setInterval(() => {
    if (autoRefresh.value && isConnected.value) {
      emit('refresh')
      lastUpdate.value = new Date()
    }
  }, props.refreshInterval)
}

const stopAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
}

const simulateConnectionStatus = () => {
  // Simulate occasional connection issues
  setInterval(() => {
    if (Math.random() < 0.05) { // 5% chance of connection issue
      isConnected.value = false
      setTimeout(() => {
        isConnected.value = true
      }, 2000 + Math.random() * 3000) // Reconnect after 2-5 seconds
    }
  }, 10000) // Check every 10 seconds
}

// Lifecycle
onMounted(() => {
  if (autoRefresh.value) {
    startAutoRefresh()
  }
  simulateConnectionStatus()
})

onUnmounted(() => {
  stopAutoRefresh()
})

// Watch for external refresh completion
const updateLastRefresh = () => {
  lastUpdate.value = new Date()
}

// Expose method to parent
defineExpose({
  updateLastRefresh
})
</script>
