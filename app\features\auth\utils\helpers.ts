/**
 * Authentication Helpers
 * 
 * Utility functions for authentication-related operations,
 * token management, and security helpers
 */

import { SESSION_CONFIG, STORAGE_KEYS } from '../constants/auth.js' // Added .js
import type { User, AuthSession } from '../types/models.js' // Added .js

// Token utilities
export const isTokenExpired = (expiresAt: string | number): boolean => {
  const expiration = typeof expiresAt === 'string' ? new Date(expiresAt).getTime() : expiresAt
  return Date.now() >= expiration
}

export const getTokenTimeRemaining = (expiresAt: string | number): number => {
  const expiration = typeof expiresAt === 'string' ? new Date(expiresAt).getTime() : expiresAt
  return Math.max(0, expiration - Date.now())
}

export const shouldRefreshToken = (expiresAt: string | number): boolean => {
  const timeRemaining = getTokenTimeRemaining(expiresAt)
  return timeRemaining <= SESSION_CONFIG.REFRESH_THRESHOLD
}

// Session utilities
export const createSessionData = (user: User, tokens: {
  accessToken: string
  refreshToken: string
  expiresAt: string
}): AuthSession => {
  // This function needs to construct a full AuthSession object.
  // Assuming 'tokens.accessToken' and 'tokens.refreshToken' are raw token strings.
  // And 'tokens.expiresAt' is the ISODateString for accessToken expiry.
  // We'll need to create placeholder JWTToken and RefreshToken objects.
  const now = new Date().toISOString();
  const placeholderJwtToken: JWTToken = {
    token: tokens.accessToken,
    payload: {} as any, // Placeholder
    header: {} as any,  // Placeholder
    signature: ''       // Placeholder
  };
  const placeholderRefreshToken: RefreshToken = {
    token: tokens.refreshToken,
    family: '' as any, // Placeholder UUID
    generation: 0,    // Placeholder
    expiresAt: '',    // Placeholder ISODateString, should come from backend
    rotated: false    // Placeholder
  };

  return {
    id: '' as any, // Placeholder UUID, should come from backend
    user,
    tokens: {
      accessToken: placeholderJwtToken,
      refreshToken: placeholderRefreshToken,
      expiresAt: tokens.expiresAt,
      refreshExpiresAt: '', // Placeholder, should come from backend
      tokenType: 'Bearer' as any, // Placeholder
      scope: [] // Placeholder
    },
    security: {} as any, // Placeholder
    context: {} as any,  // Placeholder
    metadata: {
      createdAt: now,
      lastActivity: now,
      ipAddress: '', // Placeholder
      userAgent: '', // Placeholder
      deviceId: ''   // Placeholder
    }
  };
}

export const updateSessionActivity = (session: AuthSession): AuthSession => {
  return {
    ...session,
    metadata: {
      ...session.metadata,
      lastActivity: new Date().toISOString()
    }
  };
}

export const isSessionValid = (session: AuthSession | null): boolean => {
  if (!session) return false;
  if (!session.tokens?.accessToken?.token || !session.tokens?.refreshToken?.token) return false;
  if (isTokenExpired(session.tokens.expiresAt)) return false;
  return true
}

// Local storage utilities
export const saveToStorage = (key: string, data: any): void => {
  try {
    if (typeof window !== 'undefined') {
      localStorage.setItem(key, JSON.stringify(data))
    }
  } catch (error) {
    console.warn('Failed to save to localStorage:', error)
  }
}

export const loadFromStorage = <T>(key: string): T | null => {
  try {
    if (typeof window !== 'undefined') {
      const item = localStorage.getItem(key)
      return item ? JSON.parse(item) : null
    }
  } catch (error) {
    console.warn('Failed to load from localStorage:', error)
  }
  return null
}

export const removeFromStorage = (key: string): void => {
  try {
    if (typeof window !== 'undefined') {
      localStorage.removeItem(key)
    }
  } catch (error) {
    console.warn('Failed to remove from localStorage:', error)
  }
}

export const clearAuthStorage = (): void => {
  Object.values(STORAGE_KEYS).forEach(key => {
    removeFromStorage(key as string) // Cast key to string
  })
}

// Security utilities
export const generateSecureId = (): string => {
  return crypto.randomUUID()
}

export const generateNonce = (length = 32): string => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  let result = ''
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

export const hashString = async (str: string): Promise<string> => {
  const encoder = new TextEncoder()
  const data = encoder.encode(str)
  const hashBuffer = await crypto.subtle.digest('SHA-256', data)
  const hashArray = Array.from(new Uint8Array(hashBuffer))
  return hashArray.map(b => b.toString(16).padStart(2, '0')).join('')
}

// Device fingerprinting
export const getDeviceFingerprint = (): string => {
  const canvas = document.createElement('canvas')
  const ctx = canvas.getContext('2d')
  ctx?.fillText('Device fingerprint', 10, 10)
  
  const fingerprint = [
    navigator.userAgent,
    navigator.language,
    screen.width + 'x' + screen.height,
    new Date().getTimezoneOffset(),
    canvas.toDataURL()
  ].join('|')
  
  return btoa(fingerprint).slice(0, 32)
}

export const getDeviceInfo = () => {
  const ua = navigator.userAgent
  let browser = 'Unknown'
  let os = 'Unknown'
  
  // Detect browser
  if (ua.includes('Chrome')) browser = 'Chrome'
  else if (ua.includes('Firefox')) browser = 'Firefox'
  else if (ua.includes('Safari')) browser = 'Safari'
  else if (ua.includes('Edge')) browser = 'Edge'
  
  // Detect OS
  if (ua.includes('Windows')) os = 'Windows'
  else if (ua.includes('Mac')) os = 'macOS'
  else if (ua.includes('Linux')) os = 'Linux'
  else if (ua.includes('Android')) os = 'Android'
  else if (ua.includes('iOS')) os = 'iOS'
  
  return { browser, os, userAgent: ua }
}

// URL utilities
export const buildAuthUrl = (path: string, params?: Record<string, string>): string => {
  const url = new URL(path, window.location.origin)
  
  if (params) {
    Object.entries(params).forEach(([key, value]) => {
      url.searchParams.set(key, value)
    })
  }
  
  return url.toString()
}

export const getRedirectUrl = (fallback = '/dashboard'): string => {
  if (typeof window === 'undefined') return fallback
  
  const urlParams = new URLSearchParams(window.location.search)
  const redirect = urlParams.get('redirect')
  
  // Validate redirect URL to prevent open redirect attacks
  if (redirect) {
    try {
      const url = new URL(redirect, window.location.origin)
      if (url.origin === window.location.origin) {
        return url.pathname + url.search + url.hash
      }
    } catch {
      // Invalid URL, use fallback
    }
  }
  
  return fallback
}

// Role and permission utilities
export const hasRole = (userRoles: string[], requiredRoles: string | string[]): boolean => {
  const roles = Array.isArray(requiredRoles) ? requiredRoles : [requiredRoles]
  return roles.some(role => userRoles.includes(role))
}

export const hasPermission = (userPermissions: string[], requiredPermissions: string | string[]): boolean => {
  const permissions = Array.isArray(requiredPermissions) ? requiredPermissions : [requiredPermissions]
  return permissions.every(permission => userPermissions.includes(permission))
}

export const hasAnyPermission = (userPermissions: string[], requiredPermissions: string[]): boolean => {
  return requiredPermissions.some(permission => userPermissions.includes(permission))
}

// Time utilities
export const formatDuration = (milliseconds: number): string => {
  const seconds = Math.floor(milliseconds / 1000)
  const minutes = Math.floor(seconds / 60)
  const hours = Math.floor(minutes / 60)
  const days = Math.floor(hours / 24)
  
  if (days > 0) return `${days}d ${hours % 24}h`
  if (hours > 0) return `${hours}h ${minutes % 60}m`
  if (minutes > 0) return `${minutes}m ${seconds % 60}s`
  return `${seconds}s`
}

export const addTime = (date: Date, milliseconds: number): Date => {
  return new Date(date.getTime() + milliseconds)
}

export const isWithinTimeWindow = (timestamp: number, windowMs: number): boolean => {
  return Date.now() - timestamp <= windowMs
}

// Validation utilities
export const sanitizeInput = (input: string): string => {
  return input.trim().replace(/[<>]/g, '')
}

export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

export const isStrongPassword = (password: string): boolean => {
  const strongPasswordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*(),.?":{}|<>])[A-Za-z\d!@#$%^&*(),.?":{}|<>]{8,}$/
  return strongPasswordRegex.test(password)
}

// Error handling utilities
export const createAuthError = (code: string, message: string, details?: any) => {
  return {
    code,
    message,
    details,
    timestamp: new Date().toISOString()
  }
}

export const isAuthError = (error: any): boolean => {
  return error && typeof error === 'object' && 'code' in error && 'message' in error
}

// Retry utilities
export const withRetry = async <T>(
  fn: () => Promise<T>,
  maxRetries = 3,
  delay = 1000
): Promise<T> => {
  let lastError: Error
  
  for (let i = 0; i <= maxRetries; i++) {
    try {
      return await fn()
    } catch (error) {
      lastError = error as Error
      
      if (i === maxRetries) break
      
      await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, i)))
    }
  }
  
  throw lastError!
}

// Debounce utility
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }
}

// Throttle utility
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  limit: number
): ((...args: Parameters<T>) => void) => {
  let inThrottle: boolean
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

// Deep clone utility
export const deepClone = <T>(obj: T): T => {
  if (obj === null || typeof obj !== 'object') return obj
  if (obj instanceof Date) return new Date(obj.getTime()) as unknown as T
  if (obj instanceof Array) return obj.map(item => deepClone(item)) as unknown as T
  if (typeof obj === 'object') {
    const clonedObj = {} as T
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key])
      }
    }
    return clonedObj
  }
  return obj
}
