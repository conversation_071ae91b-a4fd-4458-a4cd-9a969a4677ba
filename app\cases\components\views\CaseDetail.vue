<!--
  Case Detail View Component
  
  Displays detailed information about a specific legal case
  with tabs for different sections
-->

<template>
  <div class="space-y-6">
    <!-- Loading State -->
    <div v-if="isLoading" class="flex items-center justify-center py-12">
      <UiSpinner size="lg" />
      <span class="ml-3 text-gray-600">Loading case details...</span>
    </div>

    <!-- Case Not Found -->
    <div v-else-if="!case_" class="text-center py-12">
      <Icon name="heroicons:exclamation-triangle" class="mx-auto h-12 w-12 text-gray-400" />
      <h3 class="mt-2 text-sm font-medium text-gray-900">Case not found</h3>
      <p class="mt-1 text-sm text-gray-500">The case you're looking for doesn't exist.</p>
      <div class="mt-6">
        <NuxtLink
          to="/dashboard/cases"
          class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-indigo-600 bg-indigo-100 hover:bg-indigo-200"
        >
          Back to Cases
        </NuxtLink>
      </div>
    </div>

    <!-- Case Details -->
    <template v-else>
      <!-- Header -->
      <div class="bg-white shadow rounded-lg p-6">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div class="flex-1">
            <div class="flex items-center space-x-3">
              <h1 class="text-2xl font-bold text-gray-900">{{ case_.title }}</h1>
              <span :class="getStatusBadgeClass(case_.status)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                {{ case_.status }}
              </span>
              <span :class="getPriorityBadgeClass(case_.priority)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                {{ case_.priority }}
              </span>
            </div>
            <p class="mt-1 text-sm text-gray-500">Case #{{ case_.caseNumber }}</p>
            <p class="mt-2 text-gray-600">{{ case_.description }}</p>
          </div>
          
          <div class="mt-4 sm:mt-0 flex space-x-3">
            <NuxtLink
              :to="`/dashboard/cases/${case_.id}/edit`"
              class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
            >
              <Icon name="heroicons:pencil" class="h-4 w-4 mr-2" />
              Edit
            </NuxtLink>
            
            <NuxtLink
              to="/dashboard/cases"
              class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
            >
              <Icon name="heroicons:arrow-left" class="h-4 w-4 mr-2" />
              Back
            </NuxtLink>
          </div>
        </div>
      </div>

      <!-- Tabs -->
      <div class="bg-white shadow rounded-lg">
        <div class="border-b border-gray-200">
          <nav class="-mb-px flex space-x-8 px-6" aria-label="Tabs">
            <button
              v-for="tab in tabs"
              :key="tab.id"
              @click="activeTab = tab.id"
              :class="[
                activeTab === tab.id
                  ? 'border-indigo-500 text-indigo-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300',
                'whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm'
              ]"
            >
              <Icon :name="tab.icon" class="h-4 w-4 mr-2" />
              {{ tab.name }}
            </button>
          </nav>
        </div>

        <div class="p-6">
          <!-- Overview Tab -->
          <div v-if="activeTab === 'overview'" class="space-y-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <!-- Client Information -->
              <div>
                <h3 class="text-lg font-medium text-gray-900 mb-4">Client Information</h3>
                <dl class="space-y-3">
                  <div>
                    <dt class="text-sm font-medium text-gray-500">Name</dt>
                    <dd class="text-sm text-gray-900">{{ case_.client.name }}</dd>
                  </div>
                  <div>
                    <dt class="text-sm font-medium text-gray-500">Email</dt>
                    <dd class="text-sm text-gray-900">{{ case_.client.email }}</dd>
                  </div>
                  <div v-if="case_.client.phone">
                    <dt class="text-sm font-medium text-gray-500">Phone</dt>
                    <dd class="text-sm text-gray-900">{{ case_.client.phone }}</dd>
                  </div>
                  <div v-if="case_.client.company">
                    <dt class="text-sm font-medium text-gray-500">Company</dt>
                    <dd class="text-sm text-gray-900">{{ case_.client.company }}</dd>
                  </div>
                </dl>
              </div>

              <!-- Case Details -->
              <div>
                <h3 class="text-lg font-medium text-gray-900 mb-4">Case Details</h3>
                <dl class="space-y-3">
                  <div>
                    <dt class="text-sm font-medium text-gray-500">Type</dt>
                    <dd class="text-sm text-gray-900">{{ formatCaseType(case_.type) }}</dd>
                  </div>
                  <div>
                    <dt class="text-sm font-medium text-gray-500">Created</dt>
                    <dd class="text-sm text-gray-900">{{ formatDate(case_.createdAt) }}</dd>
                  </div>
                  <div v-if="case_.startDate">
                    <dt class="text-sm font-medium text-gray-500">Start Date</dt>
                    <dd class="text-sm text-gray-900">{{ formatDate(case_.startDate) }}</dd>
                  </div>
                  <div v-if="case_.expectedEndDate">
                    <dt class="text-sm font-medium text-gray-500">Expected End Date</dt>
                    <dd class="text-sm text-gray-900">{{ formatDate(case_.expectedEndDate) }}</dd>
                  </div>
                  <div v-if="case_.billingRate">
                    <dt class="text-sm font-medium text-gray-500">Billing Rate</dt>
                    <dd class="text-sm text-gray-900">${{ case_.billingRate }}/hour</dd>
                  </div>
                </dl>
              </div>
            </div>
          </div>

          <!-- Documents Tab -->
          <div v-else-if="activeTab === 'documents'" class="space-y-4">
            <div class="flex justify-between items-center">
              <h3 class="text-lg font-medium text-gray-900">Documents</h3>
              <button class="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-indigo-600 bg-indigo-100 hover:bg-indigo-200">
                <Icon name="heroicons:plus" class="h-4 w-4 mr-2" />
                Add Document
              </button>
            </div>
            
            <div class="text-center py-8">
              <Icon name="heroicons:document-text" class="mx-auto h-12 w-12 text-gray-400" />
              <h3 class="mt-2 text-sm font-medium text-gray-900">No documents</h3>
              <p class="mt-1 text-sm text-gray-500">Get started by uploading a document.</p>
            </div>
          </div>

          <!-- Timeline Tab -->
          <div v-else-if="activeTab === 'timeline'" class="space-y-4">
            <h3 class="text-lg font-medium text-gray-900">Case Timeline</h3>
            
            <div class="flow-root">
              <ul class="-mb-8">
                <li v-for="(event, index) in timelineEvents" :key="event.id">
                  <div class="relative pb-8">
                    <span v-if="index !== timelineEvents.length - 1" class="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200"></span>
                    <div class="relative flex space-x-3">
                      <div>
                        <span :class="[event.iconBackground, 'h-8 w-8 rounded-full flex items-center justify-center ring-8 ring-white']">
                          <Icon :name="event.icon" class="h-4 w-4 text-white" />
                        </span>
                      </div>
                      <div class="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                        <div>
                          <p class="text-sm text-gray-500">{{ event.content }}</p>
                        </div>
                        <div class="text-right text-sm whitespace-nowrap text-gray-500">
                          {{ formatDate(event.datetime) }}
                        </div>
                      </div>
                    </div>
                  </div>
                </li>
              </ul>
            </div>
          </div>

          <!-- Notes Tab -->
          <div v-else-if="activeTab === 'notes'" class="space-y-4">
            <div class="flex justify-between items-center">
              <h3 class="text-lg font-medium text-gray-900">Case Notes</h3>
              <button class="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-indigo-600 bg-indigo-100 hover:bg-indigo-200">
                <Icon name="heroicons:plus" class="h-4 w-4 mr-2" />
                Add Note
              </button>
            </div>
            
            <div class="text-center py-8">
              <Icon name="heroicons:document-text" class="mx-auto h-12 w-12 text-gray-400" />
              <h3 class="mt-2 text-sm font-medium text-gray-900">No notes</h3>
              <p class="mt-1 text-sm text-gray-500">Add notes to track important information about this case.</p>
            </div>
          </div>
        </div>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'

// Composables
const route = useRoute()

// Types
interface CaseData {
  id: string
  title: string
  caseNumber: string
  description: string
  status: string
  priority: string
  type: string
  client: {
    name: string
    email: string
    phone: string
    company: string
  }
  createdAt: string
  startDate: string
  expectedEndDate: string
  billingRate: number
}

// State
const isLoading = ref(true)
const activeTab = ref('overview')
const case_ = ref<CaseData | null>(null)

// Mock data
const mockCase = {
  id: '1',
  title: 'Contract Dispute Resolution',
  caseNumber: 'CASE-2024-001',
  description: 'Legal dispute regarding breach of contract terms between client and vendor.',
  status: 'active',
  priority: 'high',
  type: 'contract',
  client: {
    name: 'Acme Corporation',
    email: '<EMAIL>',
    phone: '+****************',
    company: 'Acme Corporation'
  },
  createdAt: '2024-01-15T10:30:00Z',
  startDate: '2024-01-15',
  expectedEndDate: '2024-06-15',
  billingRate: 350
}

const tabs = [
  { id: 'overview', name: 'Overview', icon: 'heroicons:information-circle' },
  { id: 'documents', name: 'Documents', icon: 'heroicons:document-text' },
  { id: 'timeline', name: 'Timeline', icon: 'heroicons:clock' },
  { id: 'notes', name: 'Notes', icon: 'heroicons:chat-bubble-left-ellipsis' }
]

const timelineEvents = [
  {
    id: 1,
    content: 'Case created',
    datetime: '2024-01-15T10:30:00Z',
    icon: 'heroicons:plus',
    iconBackground: 'bg-green-500'
  },
  {
    id: 2,
    content: 'Initial client consultation completed',
    datetime: '2024-01-16T14:00:00Z',
    icon: 'heroicons:chat-bubble-left-ellipsis',
    iconBackground: 'bg-blue-500'
  }
]

// Methods
const getStatusBadgeClass = (status: string) => {
  const classes = {
    active: 'bg-green-100 text-green-800',
    pending: 'bg-yellow-100 text-yellow-800',
    closed: 'bg-gray-100 text-gray-800',
    archived: 'bg-blue-100 text-blue-800'
  }
  return classes[status as keyof typeof classes] || 'bg-gray-100 text-gray-800'
}

const getPriorityBadgeClass = (priority: string) => {
  const classes = {
    high: 'bg-red-100 text-red-800',
    medium: 'bg-yellow-100 text-yellow-800',
    low: 'bg-green-100 text-green-800'
  }
  return classes[priority as keyof typeof classes] || 'bg-gray-100 text-gray-800'
}

const formatCaseType = (type: string) => {
  const types = {
    contract: 'Contract Law',
    employment: 'Employment Law',
    corporate: 'Corporate Law',
    litigation: 'Litigation',
    'intellectual-property': 'Intellectual Property',
    'real-estate': 'Real Estate',
    family: 'Family Law',
    criminal: 'Criminal Law'
  }
  return types[type as keyof typeof types] || type
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

// Lifecycle
onMounted(async () => {
  try {
    // TODO: Replace with actual API call
    const caseId = route.params.id
    console.log('Loading case:', caseId)
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    case_.value = mockCase
  } catch (error) {
    console.error('Error loading case:', error)
  } finally {
    isLoading.value = false
  }
})
</script>
