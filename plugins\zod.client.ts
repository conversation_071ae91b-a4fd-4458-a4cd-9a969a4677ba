/**
 * Zod Client Plugin
 * 
 * Provides global Zod utilities and validation helpers for client-side use
 */

import { z } from 'zod'

export default defineNuxtPlugin(() => {
  // Global Zod utilities
  const zodUtils = {
    // Common validation schemas
    email: z.string().email(),
    uuid: z.string().uuid(),
    url: z.string().url(),
    phone: z.string().regex(/^[\+]?[1-9][\d]{0,15}$/, 'Invalid phone number'),
    
    // Utility functions
    parseWithFallback: <T>(schema: z.ZodSchema<T>, data: unknown, fallback: T): T => {
      const result = schema.safeParse(data)
      return result.success ? result.data : fallback
    },
    
    validateAndFormat: <T>(schema: z.ZodSchema<T>, data: unknown) => {
      const result = schema.safeParse(data)
      if (result.success) {
        return { success: true, data: result.data, errors: null }
      } else {
        return { 
          success: false, 
          data: null, 
          errors: result.error.errors.map(err => ({
            path: err.path.join('.'),
            message: err.message
          }))
        }
      }
    }
  }

  return {
    provide: {
      zod: zodUtils
    }
  }
})
