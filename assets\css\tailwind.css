/* ~/assets/css/tailwind.css */
/* Tailwind CSS Configuration and Theme */
@import 'tailwindcss';

/* Tailwind Theme Configuration */
@theme {
  /* Brand Primary Color Palette (50-900) */
  --color-brandPrimary-50: #eff6ff;
  --color-brandPrimary-100: #dbeafe;
  --color-brandPrimary-200: #bfdbfe;
  --color-brandPrimary-300: #93c5fd;
  --color-brandPrimary-400: #60a5fa;
  --color-brandPrimary-500: #3b82f6;
  --color-brandPrimary-600: #1a56db;
  --color-brandPrimary-700: #1e40af;
  --color-brandPrimary-800: #1e3a8a;
  --color-brandPrimary-900: #1e2a5e;
  --color-brandPrimary-950: #172554;

  /* Brand Primary Aliases (for backward compatibility) */
  --color-brandPrimary: #1a56db; /* 600 */
  --color-brandPrimary-light: #3b82f6; /* 500 */
  --color-brandPrimary-dark: #1e40af; /* 700 */

  /* Brand Secondary Color Palette */
  --color-brandSecondary-50: #ecfeff;
  --color-brandSecondary-100: #cffafe;
  --color-brandSecondary-200: #a5f3fc;
  --color-brandSecondary-300: #67e8f9;
  --color-brandSecondary-400: #22d3ee;
  --color-brandSecondary-500: #06b6d4;
  --color-brandSecondary-600: #0891b2;
  --color-brandSecondary-700: #0e7490;
  --color-brandSecondary-800: #155e75;
  --color-brandSecondary-900: #164e63;
  --color-brandSecondary-950: #083344;

  /* Brand Secondary Aliases (for backward compatibility) */
  --color-brandSecondary: #06b6d4; /* 500 */
  --color-brandSecondary-dark: #0891b2; /* 600 */
  --color-brandSecondary-text-on-white: #0e7490; /* 700 */

  /* Brand Success Color Palette */
  --color-brandSuccess-50: #ecfdf5;
  --color-brandSuccess-100: #d1fae5;
  --color-brandSuccess-200: #a7f3d0;
  --color-brandSuccess-300: #6ee7b7;
  --color-brandSuccess-400: #34d399;
  --color-brandSuccess-500: #10b981;
  --color-brandSuccess-600: #059669;
  --color-brandSuccess-700: #047857;
  --color-brandSuccess-800: #065f46;
  --color-brandSuccess-900: #064e3b;
  --color-brandSuccess-950: #022c22;

  /* Brand Success Aliases (for backward compatibility) */
  --color-brandSuccess: #10b981; /* 500 */
  --color-brandSuccess-lightBg: #ecfdf5; /* 50 */
  --color-brandSuccess-lightBorder: #a7f3d0; /* 200 */
  --color-brandSuccess-darkText: #065f46; /* 800 */

  /* Brand Danger Color Palette */
  --color-brandDanger-50: #fef2f2;
  --color-brandDanger-100: #fee2e2;
  --color-brandDanger-200: #fecaca;
  --color-brandDanger-300: #fca5a5;
  --color-brandDanger-400: #f87171;
  --color-brandDanger-500: #ef4444;
  --color-brandDanger-600: #dc2626;
  --color-brandDanger-700: #b91c1c;
  --color-brandDanger-800: #991b1b;
  --color-brandDanger-900: #7f1d1d;
  --color-brandDanger-950: #450a0a;

  /* Brand Danger Aliases (for backward compatibility) */
  --color-brandDanger: #ef4444; /* 500 */
  --color-brandDanger-dark: #dc2626; /* 600 */
  --color-brandDanger-lightBg: #fef2f2; /* 50 */
  --color-brandDanger-lightBorder: #fecaca; /* 200 */
  --color-brandDanger-darkText: #991b1b; /* 800 */

  /* Brand Warning Color Palette */
  --color-brandWarning-50: #fffbeb;
  --color-brandWarning-100: #fef3c7;
  --color-brandWarning-200: #fde68a;
  --color-brandWarning-300: #fcd34d;
  --color-brandWarning-400: #fbbf24;
  --color-brandWarning-500: #f59e0b;
  --color-brandWarning-600: #d97706;
  --color-brandWarning-700: #b45309;
  --color-brandWarning-800: #92400e;
  --color-brandWarning-900: #78350f;
  --color-brandWarning-950: #451a03;

  /* Brand Warning Aliases (for backward compatibility) */
  --color-brandWarning: #fcd34d; /* 300 */
  --color-brandWarning-lightBg: #fffbeb; /* 50 */
  --color-brandWarning-lightBorder: #fef3c7; /* 100 */
  --color-brandWarning-darkText: #92400e; /* 800 */

  /* Brand Info Color Palette */
  --color-brandInfo-50: #eff6ff;
  --color-brandInfo-100: #dbeafe;
  --color-brandInfo-200: #bfdbfe;
  --color-brandInfo-300: #93c5fd;
  --color-brandInfo-400: #60a5fa;
  --color-brandInfo-500: #3b82f6;
  --color-brandInfo-600: #2563eb;
  --color-brandInfo-700: #1d4ed8;
  --color-brandInfo-800: #1e40af;
  --color-brandInfo-900: #1e3a8a;
  --color-brandInfo-950: #172554;

  /* Brand Info Aliases (for backward compatibility) */
  --color-brandInfo: #3b82f6; /* 500 */
  --color-brandInfo-lightBg: #eff6ff; /* 50 */
  --color-brandInfo-lightBorder: #bfdbfe; /* 200 */
  --color-brandInfo-darkText: #1e40af; /* 800 */

  /* RGB Color Values for Alpha Transparency */
  --color-brandPrimary-rgb: 26, 86, 219;
  --color-brandSecondary-rgb: 6, 182, 212;
  --color-brandSuccess-rgb: 16, 185, 129;
  --color-brandDanger-rgb: 239, 68, 68;
  --color-brandWarning-rgb: 245, 158, 11;
  --color-brandInfo-rgb: 59, 130, 246;

  /* Typography */
  --font-family-base: 'Inter', sans-serif;
  --font-family-mono: 'JetBrains Mono', monospace;
  --font-size-base: 0.875rem;
  --line-height-base: 1.5;
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-bold: 700;

  /* Font Families */
  --font-family-calibri: 'Calibri', sans-serif;
  --font-family-times-new-roman: 'Times New Roman', serif;
  --font-family-arial: 'Arial', sans-serif;
  --font-family-verdana: 'Verdana', sans-serif;
  --font-family-helvetica: 'Helvetica', sans-serif;
  --font-family-tahoma: 'Tahoma', sans-serif;
  --font-family-georgia: 'Georgia', serif;
  --font-family-garamond: 'Garamond', serif;
  --font-family-comic-sans-ms: 'Comic Sans MS', cursive;
  --font-family-impact: 'Impact', sans-serif;
  --font-family-courier-new: 'Courier New', monospace;
  --font-family-lucida-console: 'Lucida Console', monospace;
  --font-family-symbol: 'Symbol', fantasy;
  --font-family-webdings: 'Webdings', fantasy;

  /* Font Sizes */
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-md: 1rem;
  --font-size-lg: 1.125rem;

}

/* Custom responsive utilities */
@layer utilities {
  /* Safe area utilities for mobile devices */
  .safe-top {
    padding-top: env(safe-area-inset-top);
  }

  .safe-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }

  .safe-left {
    padding-left: env(safe-area-inset-left);
  }

  .safe-right {
    padding-right: env(safe-area-inset-right);
  }

  /* Touch-friendly tap targets */
  .tap-target {
    min-height: 44px;
    min-width: 44px;
  }

  /* Smooth scrolling for mobile */
  .smooth-scroll {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
  }

  /* Hide scrollbars but keep functionality */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* Mobile-first responsive text */
  .text-responsive {
    font-size: clamp(0.875rem, 2.5vw, 1rem);
  }

  .text-responsive-lg {
    font-size: clamp(1.125rem, 3vw, 1.25rem);
  }

  .text-responsive-xl {
    font-size: clamp(1.25rem, 4vw, 1.5rem);
  }
}


/* Enhanced Menu Utilities */
@layer utilities {
  .menu-shadow {
    box-shadow: var(--shadow-menu);
  }

  .menu-shadow-mobile {
    box-shadow: var(--shadow-menu-mobile);
  }

  .dropdown-shadow {
    box-shadow: var(--shadow-dropdown);
  }

  .transition-menu {
    transition: var(--transition-menu);
  }

  .transition-menu-fast {
    transition: var(--transition-menu-fast);
  }

  .backdrop-blur-menu {
    backdrop-filter: blur(8px);
  }

  .border-r-3 {
    border-right-width: 3px;
  }

  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: #e5e7eb transparent;
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 4px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: #e5e7eb;
    border-radius: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background-color: #d1d5db;
  }
}

/* Enhanced Design Token Utilities */
@utility safe-top {
  padding-top: env(safe-area-inset-top);
}

@utility safe-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}

@utility safe-left {
  padding-left: env(safe-area-inset-left);
}

@utility safe-right {
  padding-right: env(safe-area-inset-right);
}

@utility tap-target {
  min-height: 44px;
  min-width: 44px;
}

@utility smooth-scroll {
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
}

@utility scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

@utility scrollbar-hide {
  &::-webkit-scrollbar {
    display: none;
  }
}

@utility text-responsive {
  font-size: clamp(0.875rem, 2.5vw, 1rem);
}

@utility text-responsive-lg {
  font-size: clamp(1.125rem, 3vw, 1.25rem);
}

@utility text-responsive-xl {
  font-size: clamp(1.25rem, 4vw, 1.5rem);
}

@utility menu-shadow {
  box-shadow: var(--shadow-menu);
}

@utility menu-shadow-mobile {
  box-shadow: var(--shadow-menu-mobile);
}

@utility dropdown-shadow {
  box-shadow: var(--shadow-dropdown);
}

@utility transition-menu {
  transition: var(--transition-menu);
}

@utility transition-menu-fast {
  transition: var(--transition-menu-fast);
}

@utility backdrop-blur-menu {
  backdrop-filter: blur(8px);
}

@utility border-r-3 {
  border-right-width: 3px;
}

@utility scrollbar-thin {
  scrollbar-width: thin;
  scrollbar-color: #e5e7eb transparent;
}

@utility scrollbar-thin {
  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #e5e7eb;
    border-radius: 6px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background-color: #d1d5db;
  }
}

/* RTL-specific utilities for Tailwind CSS 4.1 */
@utility rtl-flip {
  [dir="rtl"] & {
    transform: scaleX(-1);
  }
}

@utility rtl-text-align {
  [dir="rtl"] & {
    text-align: right;
  }
  [dir="ltr"] & {
    text-align: left;
  }
}

@utility rtl-margin-start {
  [dir="ltr"] & {
    margin-left: var(--spacing);
  }
  [dir="rtl"] & {
    margin-right: var(--spacing);
  }
}

@utility rtl-margin-end {
  [dir="ltr"] & {
    margin-right: var(--spacing);
  }
  [dir="rtl"] & {
    margin-left: var(--spacing);
  }
}

@utility rtl-padding-start {
  [dir="ltr"] & {
    padding-left: var(--spacing);
  }
  [dir="rtl"] & {
    padding-right: var(--spacing);
  }
}

@utility rtl-padding-end {
  [dir="ltr"] & {
    padding-right: var(--spacing);
  }
  [dir="rtl"] & {
    padding-left: var(--spacing);
  }
}