import { Node, Extension } from '@tiptap/core'
import { getFormatCss, getFormat } from './utils/formats'
import { getCurrentlyFocusedNode } from './utils/utils'




export const PageContent = Node.create({
    name: 'content',
    group: 'block',
    content: 'block+',
    isolating: true,
    draggable: false,


    parseHTML() {
        return [
            {
                tag: 'div[data-type="content"].page-content',
            }
        ]
    },

    renderHTML({ HTMLAttributes }) {
        return ['div', { ...HTMLAttributes, 'data-type': 'page-content', class: 'page-content' }, 0]
    },

    onUpdate({ editor }) {
        const state = editor.state

        state.doc.descendants((node, pos) => {
            if (node.type.name === 'content') {
                const domAtPos = editor.view.domAtPos(pos)
                const domNode = domAtPos.node

                console.log('DOM Element for custom node:', domNode)
            }
        })

    }

})



export const Page = Node.create({
    name: 'page',
    group: 'block',
    content: 'content?',
    isolating: true,
    draggable: false,

    addExtensions() {
        return [PageContent]
    },
    addOptions() {

        const { width, height, margins } = getFormat('A4')

        return {
            HTMLAttributes: {
                'data-page-number': null,
                style: null
            },
            width,
            height,
            margins
        }
    },



    addCommands() {
        return {
            insertPage:
                (attrs?: Record<string, any>) =>
                    ({ commands, state, editor }) => {
                        const endPos = state.doc.content.size
                        const { format } = editor.storage.pages

                        // calc page number 
                        let pageNum = 1;
                        state.doc.descendants(node => {
                            if (node.type.name == this.name) pageNum++;

                        });
                        //add to the attrs
                        this.options.HTMLAttributes['data-page-number'] = pageNum
                        this.options.HTMLAttributes.style = getFormatCss(format)
                        commands.setPagesCount(pageNum)



                        // insert page
                        return commands.insertContentAt(endPos, {
                            type: this.name,
                            attrs: {
                                ...this.options.HTMLAttributes,
                                ...attrs
                            },
                            custom: pageNum,
                            content: [
                                {
                                    type: 'content',
                                    content: [
                                        {
                                            type: 'paragraph',
                                            content: [{ type: 'text', text: ' ' }],
                                        },
                                    ],
                                },
                            ],
                        })
                    },
        }
    },


    addAttributes() {
        return {
            'data-type': {
                default: 'page'
            },
            'data-page-number': {
                default: null,
            },
            'style': {
                default: '',
            }
        };
    },

    parseHTML() {
        return [
            {
                tag: 'div[data-type="page"].page',
            }
        ]
    },

    renderHTML({ HTMLAttributes }) {


        return ['div', { ...HTMLAttributes, 'data-type': 'page', class: 'page' }, 0]
    },


})

export const Pages = Extension.create({
    name: 'pages',
    addExtensions() {
        return [Page];
    },
    addProseMirrorPlugins() {
        return []
    },

    addOptions() {
        return {
            format: 'A4',
            headerHeight: 50,
            footerHeight: 50,
            pageGap: 50,
            pageBreakBackground: '#f6f3f4',
        }
    },

    addStorage() {
        return {
            format: this.options.format,
            count: 0

        }
    },

    addCommands() {
        return {
            setPagesCount:
                (value) =>
                    ({ editor }) => {
                        const { storage } = editor
                        storage.pages.count = value
                        return true
                    },
        }
    },


    onCreate({ editor }) {
        editor.on('focus', ({ transaction }) => {
            console.log(transaction);
             const res = getCurrentlyFocusedNode(transaction)
            console.log(res);
        })
        if (editor.isEmpty) {
            editor.commands?.insertPage()
            editor.commands?.insertPage()
            editor.commands?.insertPage()


        }





    },
 

})