/**
 * Document Management Domain Models
 * 
 * Comprehensive type system for document management with
 * advanced features like versioning, collaboration, and security
 */

import type {
  BaseEntity,
  UUID,
  UserId,
  // TenantId, // Unused
  DocumentId,
  CaseId,
  ISODateString,
  // EntityStatus, // Unused
  SecurityClassification,
  FileInfo,
  // Brand // Unused
} from '@shared/types/core'

// ============================================================================
// DOCUMENT CORE TYPES
// ============================================================================

/**
 * Document entity with comprehensive metadata
 */
export interface Document extends Omit<BaseEntity, 'id'> {
  id: DocumentId
  name: string
  description?: string
  type: DocumentType
  category: DocumentCategory
  status: DocumentStatus
  classification: SecurityClassification
  
  // File information
  file: DocumentFile
  versions: DocumentVersion[]
  currentVersion: number
  
  // Relationships
  caseId?: CaseId
  parentDocumentId?: DocumentId
  relatedDocuments: DocumentRelation[]
  
  // Content and metadata
  content: DocumentContent
  metadata: DocumentMetadata
  tags: string[]
  
  // Access and permissions
  access: DocumentAccess
  sharing: DocumentSharing
  
  // Workflow and approval
  workflow?: DocumentWorkflow
  approval?: DocumentApproval
  
  // Collaboration
  collaboration: DocumentCollaboration
  
  // Analytics
  analytics: DocumentAnalytics
}

/**
 * Document types
 */
export enum DocumentType {
  CONTRACT = 'contract',
  LEGAL_BRIEF = 'legal_brief',
  COURT_FILING = 'court_filing',
  CORRESPONDENCE = 'correspondence',
  EVIDENCE = 'evidence',
  RESEARCH = 'research',
  TEMPLATE = 'template',
  FORM = 'form',
  INVOICE = 'invoice',
  RECEIPT = 'receipt',
  REPORT = 'report',
  PRESENTATION = 'presentation',
  SPREADSHEET = 'spreadsheet',
  IMAGE = 'image',
  VIDEO = 'video',
  AUDIO = 'audio',
  OTHER = 'other'
}

/**
 * Document categories for organization
 */
export enum DocumentCategory {
  PLEADINGS = 'pleadings',
  DISCOVERY = 'discovery',
  MOTIONS = 'motions',
  CONTRACTS = 'contracts',
  CORRESPONDENCE = 'correspondence',
  EVIDENCE = 'evidence',
  RESEARCH = 'research',
  ADMINISTRATIVE = 'administrative',
  BILLING = 'billing',
  MARKETING = 'marketing',
  HR = 'hr',
  COMPLIANCE = 'compliance',
  OTHER = 'other'
}

/**
 * Document status
 */
export enum DocumentStatus {
  DRAFT = 'draft',
  UNDER_REVIEW = 'under_review',
  APPROVED = 'approved',
  PUBLISHED = 'published',
  ARCHIVED = 'archived',
  DELETED = 'deleted',
  LOCKED = 'locked'
}

// ============================================================================
// DOCUMENT FILE TYPES
// ============================================================================

/**
 * Document file information
 */
export interface DocumentFile extends FileInfo {
  originalName: string
  extension: string
  encoding: string
  pages?: number
  wordCount?: number
  characterCount?: number
  language?: string
  ocrProcessed: boolean
  ocrText?: string
  ocrConfidence?: number
  virus_scanned: boolean
  virus_scan_result?: VirusScanResult
}

/**
 * Virus scan result
 */
export interface VirusScanResult {
  clean: boolean
  threats: VirusThreat[]
  scannedAt: ISODateString
  scanner: string
  version: string
}

/**
 * Virus threat information
 */
export interface VirusThreat {
  name: string
  type: ThreatType
  severity: ThreatSeverity
  description: string
}

/**
 * Threat types
 */
export enum ThreatType {
  VIRUS = 'virus',
  MALWARE = 'malware',
  TROJAN = 'trojan',
  SPYWARE = 'spyware',
  ADWARE = 'adware',
  ROOTKIT = 'rootkit',
  WORM = 'worm'
}

/**
 * Threat severity levels
 */
export enum ThreatSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

// ============================================================================
// DOCUMENT VERSION TYPES
// ============================================================================

/**
 * Document version information
 */
export interface DocumentVersion {
  id: UUID
  version: number
  name: string
  description?: string
  file: DocumentFile
  changes: VersionChange[]
  createdAt: ISODateString
  createdBy: UserId
  status: VersionStatus
  size: number
  checksum: string
  downloadCount: number
  isMinor: boolean
}

/**
 * Version status
 */
export enum VersionStatus {
  CURRENT = 'current',
  PREVIOUS = 'previous',
  ARCHIVED = 'archived',
  DELETED = 'deleted'
}

/**
 * Version change information
 */
export interface VersionChange {
  type: ChangeType
  description: string
  section?: string
  lineNumber?: number
  oldValue?: string
  newValue?: string
  author: UserId
  timestamp: ISODateString
}

/**
 * Change types
 */
export enum ChangeType {
  ADDITION = 'addition',
  DELETION = 'deletion',
  MODIFICATION = 'modification',
  FORMATTING = 'formatting',
  COMMENT = 'comment',
  REVIEW = 'review'
}

// ============================================================================
// DOCUMENT CONTENT TYPES
// ============================================================================

/**
 * Document content information
 */
export interface DocumentContent {
  extractedText?: string
  summary?: string
  keywords: string[]
  entities: DocumentEntity[]
  structure: DocumentStructure
  annotations: DocumentAnnotation[]
  bookmarks: DocumentBookmark[]
}

/**
 * Document entity (extracted via NLP)
 */
export interface DocumentEntity {
  type: EntityType
  value: string
  confidence: number
  startPosition: number
  endPosition: number
  context?: string
}

/**
 * Entity types
 */
export enum EntityType {
  PERSON = 'person',
  ORGANIZATION = 'organization',
  LOCATION = 'location',
  DATE = 'date',
  MONEY = 'money',
  PHONE = 'phone',
  EMAIL = 'email',
  URL = 'url',
  LEGAL_CITATION = 'legal_citation',
  CONTRACT_TERM = 'contract_term',
  CLAUSE = 'clause'
}

/**
 * Document structure
 */
export interface DocumentStructure {
  sections: DocumentSection[]
  tableOfContents: TableOfContentsItem[]
  footnotes: Footnote[]
  headers: DocumentHeader[]
  footers: DocumentFooter[]
}

/**
 * Document section
 */
export interface DocumentSection {
  id: UUID
  title: string
  level: number
  startPage: number
  endPage: number
  startPosition: number
  endPosition: number
  subsections: DocumentSection[]
}

/**
 * Table of contents item
 */
export interface TableOfContentsItem {
  title: string
  level: number
  pageNumber: number
  position: number
  sectionId?: UUID
}

/**
 * Footnote
 */
export interface Footnote {
  id: UUID
  number: number
  text: string
  pageNumber: number
  position: number
}

/**
 * Document header/footer
 */
export interface DocumentHeader {
  id: UUID
  content: string
  pageNumbers: number[]
  position: HeaderPosition
}

export interface DocumentFooter {
  id: UUID
  content: string
  pageNumbers: number[]
  position: FooterPosition
}

/**
 * Header/footer positions
 */
export enum HeaderPosition {
  LEFT = 'left',
  CENTER = 'center',
  RIGHT = 'right'
}

export enum FooterPosition {
  LEFT = 'left',
  CENTER = 'center',
  RIGHT = 'right'
}

/**
 * Document annotation
 */
export interface DocumentAnnotation {
  id: UUID
  type: AnnotationType
  content: string
  position: AnnotationPosition
  author: UserId
  createdAt: ISODateString
  updatedAt?: ISODateString
  resolved: boolean
  resolvedBy?: UserId
  resolvedAt?: ISODateString
  replies: AnnotationReply[]
  tags: string[]
}

/**
 * Annotation types
 */
export enum AnnotationType {
  COMMENT = 'comment',
  HIGHLIGHT = 'highlight',
  STRIKETHROUGH = 'strikethrough',
  UNDERLINE = 'underline',
  NOTE = 'note',
  QUESTION = 'question',
  SUGGESTION = 'suggestion',
  APPROVAL = 'approval',
  REJECTION = 'rejection'
}

/**
 * Annotation position
 */
export interface AnnotationPosition {
  page: number
  x: number
  y: number
  width: number
  height: number
  startPosition?: number
  endPosition?: number
  selectedText?: string
}

/**
 * Annotation reply
 */
export interface AnnotationReply {
  id: UUID
  content: string
  author: UserId
  createdAt: ISODateString
  updatedAt?: ISODateString
}

/**
 * Document bookmark
 */
export interface DocumentBookmark {
  id: UUID
  name: string
  description?: string
  position: AnnotationPosition
  createdBy: UserId
  createdAt: ISODateString
  isPublic: boolean
  tags: string[]
}

// ============================================================================
// DOCUMENT METADATA TYPES
// ============================================================================

/**
 * Document metadata
 */
export interface DocumentMetadata {
  title?: string
  author?: string
  subject?: string
  creator?: string
  producer?: string
  creationDate?: ISODateString
  modificationDate?: ISODateString
  keywords?: string[]
  customProperties: Record<string, any>
  technicalMetadata: TechnicalMetadata
  legalMetadata?: LegalMetadata
  businessMetadata?: BusinessMetadata
}

/**
 * Technical metadata
 */
export interface TechnicalMetadata {
  format: string
  version?: string
  compression?: string
  colorSpace?: string
  resolution?: Resolution
  dimensions?: Dimensions
  bitDepth?: number
  sampleRate?: number
  duration?: number
  frameRate?: number
}

/**
 * Resolution information
 */
export interface Resolution {
  horizontal: number
  vertical: number
  unit: ResolutionUnit
}

/**
 * Resolution units
 */
export enum ResolutionUnit {
  DPI = 'dpi',
  PPI = 'ppi',
  PIXELS_PER_CM = 'pixels_per_cm'
}

/**
 * Dimensions
 */
export interface Dimensions {
  width: number
  height: number
  unit: DimensionUnit
}

/**
 * Dimension units
 */
export enum DimensionUnit {
  PIXELS = 'pixels',
  INCHES = 'inches',
  CM = 'cm',
  MM = 'mm',
  POINTS = 'points'
}

/**
 * Legal metadata
 */
export interface LegalMetadata {
  jurisdiction: string
  practiceArea: string[]
  caseType?: string
  courtName?: string
  judgeAssigned?: string
  filingDate?: ISODateString
  dueDate?: ISODateString
  privileged: boolean
  workProduct: boolean
  confidential: boolean
  clientMatter?: string
  billingCode?: string
}

/**
 * Business metadata
 */
export interface BusinessMetadata {
  department: string
  project?: string
  costCenter?: string
  businessUnit?: string
  retentionPeriod?: number
  retentionReason?: string
  disposalDate?: ISODateString
  recordType?: string
  complianceFlags: string[]
}
