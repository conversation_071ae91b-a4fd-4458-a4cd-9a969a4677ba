import { computed } from 'vue'
import { useFormValues } from 'vee-validate'
 

function resolveValue(value: any, values: any, item: any) {
  return () => typeof value === "function" ? value({ values, item }) : value;
}

export function useFormsProps(item: any) {
  const values = useFormValues()

  const props = computed(() => {
    const newProps: any = {};
    if (item.props)
      for (const key in item.props) {
        if (Object.prototype.hasOwnProperty.call(item.props, key)) {
          const value = item.props[key];
          newProps[key] = resolveValue(value, values.value, item)() ;
        }
      }
    return newProps;
  });

  const label = computed(resolveValue(item.label, values.value, item));
  const title = computed(resolveValue(item.title, values.value, item));
  const subtitle = computed(resolveValue(item.subtitle, values.value, item));
  const events = computed(resolveValue(item.events || (() => ({})), values.value, item));
  const showIf = computed(resolveValue(item.showIf, values.value, item));



  return { props, label, title, subtitle, events, showIf };
} 
