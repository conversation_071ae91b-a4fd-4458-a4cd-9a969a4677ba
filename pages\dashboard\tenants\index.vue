<template>
  <div class="flex flex-col *:space-y-6 py-6">
    <!-- Tenant Overview Dashboard -->
    <TenantOverviewDashboard
      @create-tenant="handleCreateTenant"
      @view-all="handleViewAllTenants"
    />


    <!-- Recent Activity -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Recent Tenants -->
      <UiCard :title="$t('tenantList.recentTenants')" >
        <template #header>
          <div class="flex items-center justify-between">
            <UiButton @click="handleViewAllTenants" variant="ghost" size="sm">
              {{ $t('tenantList.viewAll') }}
            </UiButton>
          </div>
        </template>
        <template #default>

        <div class="space-y-3">
          <div
            v-for="tenant in tenantStore.recentTenants"
            :key="tenant.id"
            class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors duration-200 cursor-pointer"
            @click="handleTenantAction({ action: 'view', item: tenant })"
          >
            <div class="flex items-center gap-3">
              <div class="flex-shrink-0">
                <div class="w-10 h-10 rounded-full bg-brandPrimary/10 flex items-center justify-center">
                  <Icon name="material-symbols:business" class="h-5 w-5 text-brandPrimary" />
                </div>
              </div>
              <div>
                <p class="font-medium text-gray-900 dark:text-white">{{ tenant.name }}</p>
                <p class="text-sm text-gray-500 dark:text-gray-400">{{ tenant.slug }}</p>
              </div>
            </div>
            <div class="flex items-center gap-2">
              <UiBadge :variant="tenant.status === 'active' ? 'success' : 'error'">
                {{ tenant.status.charAt(0).toUpperCase() + tenant.status.slice(1) }}
              </UiBadge>
              <span class="text-xs text-gray-400">{{ tenant.userCount }} {{ $t('tenantList.users') }}</span>
              <Icon name="material-symbols:chevron-right" class="h-4 w-4 text-gray-400" />
            </div>
          </div>

          <div v-if="tenantStore.recentTenants.length === 0" class="text-center py-6">
            <Icon name="material-symbols:business-outline" class="h-8 w-8 text-gray-400 mx-auto mb-2" />
            <p class="text-sm text-gray-500 dark:text-gray-400">{{ $t('tenantList.noRecentTenants') }}</p>
          </div>
        </div>
        </template>
      </UiCard>

      <!-- Platform Activity -->
      <UiCard :title="$t('tenantList.platformActivity')">
        <template #header>
          <div class="flex items-center justify-between">
            <UiButton @click="handleViewActivity" variant="ghost" size="sm">
              {{ $t('tenantList.viewAll') }}
            </UiButton>
          </div>
        </template>

        <div class="space-y-4">
          <!-- System Performance Metrics -->
          <div v-if="tenantStore.platformActivity" class="grid grid-cols-2 gap-4">
            <div class="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <div class="flex items-center justify-between">
                <div>
                  <p class="text-xs text-gray-500 dark:text-gray-400">{{ $t('tenantList.activeSessions') }}</p>
                  <p class="text-lg font-semibold text-gray-900 dark:text-white">
                    {{ tenantStore.platformActivity.userActivity.activeSessions }}
                  </p>
                </div>
                <Icon name="ic:twotone-people-outline" size="calc(var(--spacing) * 5)" class=" text-blue-500" />
              </div>
            </div>

            <div class="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <div class="flex items-center justify-between">
                <div>
                  <p class="text-xs text-gray-500 dark:text-gray-400">{{ $t('tenantList.dailyApiCalls') }}</p>
                  <p class="text-lg font-semibold text-gray-900 dark:text-white">
                    {{ tenantStore.platformActivity.userActivity.dailyApiCalls.toLocaleString() }}
                  </p>
                </div>
                <Icon name="material-symbols:api" size="calc(var(--spacing) * 5)" class=" text-green-500" />
              </div>
            </div>

            <div class="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <div class="flex items-center justify-between">
                <div>
                  <p class="text-xs text-gray-500 dark:text-gray-400">{{ $t('tenantList.systemUptime') }}</p>
                  <p class="text-lg font-semibold text-gray-900 dark:text-white">
                    {{ tenantStore.platformActivity.systemPerformance.uptime.toFixed(2) }}%
                  </p>
                </div>
                <Icon name="material-symbols:monitoring" size="calc(var(--spacing) * 5)" class=" text-purple-500" />
              </div>
            </div>

            <div class="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <div class="flex items-center justify-between">
                <div>
                  <p class="text-xs text-gray-500 dark:text-gray-400">{{ $t('tenantList.responseTime') }}</p>
                  <p class="text-lg font-semibold text-gray-900 dark:text-white">
                    {{ tenantStore.platformActivity.systemPerformance.averageResponseTime.toFixed(2) }}ms
                  </p>
                </div>
                <Icon name="material-symbols:speed" size="calc(var(--spacing) * 5)" class=" text-yellow-500" />
              </div>
            </div>
          </div>

          <!-- Top Active Tenants -->
          <div v-if="tenantStore.platformActivity?.topTenants?.length" class="space-y-2">
            <h4 class="text-sm font-medium text-gray-900 dark:text-white">{{ $t('tenantList.mostActiveTenants') }}</h4>
            <div
              v-for="topTenant in tenantStore.platformActivity.topTenants.slice(0, 3)"
              :key="topTenant.tenantId"
              class="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-700 rounded-lg"
            >
              <div>
                <p class="text-sm font-medium text-gray-900 dark:text-white">{{ topTenant.tenantName }}</p>
                <p class="text-xs text-gray-500 dark:text-gray-400">{{ topTenant.activityType }}</p>
              </div>
              <span class="text-sm font-semibold text-blue-600 dark:text-blue-400">
                {{ topTenant.activityCount }}
              </span>
            </div>
          </div>

          <div v-if="!tenantStore.platformActivity" class="text-center py-6">
            <Icon name="material-symbols:timeline" class="h-8 w-8 text-gray-400 mx-auto mb-2" />
            <p class="text-sm text-gray-500 dark:text-gray-400">{{ $t('tenantList.loadingPlatformActivity') }}</p>
          </div>
        </div>
      </UiCard>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { useRouter } from '#app'
import { useTenantStore, type Tenant } from '~/stores/tenant'
import { PlatformRoles } from '~/app/features/auth/constants/roles'

// Components
import TenantOverviewDashboard from '~/components/tenants/TenantOverviewDashboard.vue'
 
// Stores and composables
const tenantStore = useTenantStore()
const router = useRouter()
const { t: $t } = useI18n()

definePageMeta({
  title: 'Tenant Overview',
  description: 'Overview and quick access to tenant management features',
  showPageHeader: true,
  showPageHeaderTitle: true,
  pageHeaderIcon: 'solar:graph-new-broken',
  showPageHeaderActions: false,
  showRealTimeStatus: false,
  pageHeaderActions: () => {
    return ([
       {
        label: 'Create Tenants',
        icon: 'i-mdi:office-building-plus-outline',
        variant: 'outline',
        color: 'primary',
        click: () => navigateTo('/dashboard/tenants/create')
      },
      {
        label: 'Manage Tenants',
        icon: 'i-mdi:office-building-cog-outline',
        variant: 'outline',
        color: 'secondary',
        click: () => navigateTo('/dashboard/tenants/manage')
      },
      {
        label: 'View Reports',
        icon: 'i-heroicons:chart-bar',
        variant: 'outline',
        color: 'secondary',
        click: () => navigateTo('/dashboard/tenants/reports')
      },
    ]).reverse()
  },
  
 
 
  layout: 'dashboard',
  middleware: ['rbac'],
  roles: [PlatformRoles.SUPER_ADMIN],
  breadcrumbs: [
    { label: 'Dashboard', href: '/dashboard' },
    { label: 'Tenants' },
  ],
})



// Computed properties - removed unused ones as we now use store data directly

// Methods
const handleCreateTenant = () => {
  router.push('/dashboard/tenants/create')
}

const handleViewAllTenants = () => {
  router.push('/dashboard/tenants/manage')
}

// Removed unused methods

const handleViewActivity = () => {
  router.push('/dashboard/activity')
}

const handleTenantAction = (payload: any) => {
  const action = payload.action || payload.key
  const tenant = payload.tenant || payload.item || payload.row

  if (!tenant || !action) {
    console.error('Invalid action payload:', payload)
    return
  }

  switch (action) {
    case 'view':
      router.push(`/dashboard/tenants/${tenant.id}`)
      break
    case 'edit':
      router.push(`/dashboard/tenants/${tenant.id}/edit`)
      break
    case 'usage':
      router.push(`/dashboard/tenants/${tenant.id}/usage`)
      break
    case 'invite':
      router.push(`/dashboard/tenants/${tenant.id}/invite`)
      break
    default:
      console.warn('Unknown action:', action)
  }
}

// Lifecycle hooks
onMounted(async () => {
  // Fetch both tenant list and overview data
  await Promise.all([
    tenantStore.fetchAllTenants(),
    tenantStore.fetchAllOverviewData()
  ])
})
</script>