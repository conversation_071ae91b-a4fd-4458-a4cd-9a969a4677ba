/**
 * Composable for SEO enhancements
 * Provides utilities for managing meta tags, structured data, and SEO best practices
 */

interface SEOConfig {
  title?: string
  description?: string
  keywords?: string[]
  image?: string
  url?: string
  type?: 'website' | 'article' | 'product' | 'profile'
  siteName?: string
  locale?: string
  author?: string
  publishedTime?: string
  modifiedTime?: string
  section?: string
  tags?: string[]
}

interface StructuredData {
  '@context': string
  '@type': string
  [key: string]: any
}
import { useRuntimeConfig, useRoute, useHead } from '#app';
 
export const useSEO = () => {
  const config = useRuntimeConfig()
  const route = useRoute()

  // Default SEO configuration
  const defaultConfig: SEOConfig = {
    siteName: 'LegalFlow',
    locale: 'en_US',
    type: 'website',
    image: '/images/og-default.jpg'
  }

  /**
   * Set comprehensive SEO meta tags
   */
  const setSEO = (seoConfig: SEOConfig) => {
    const mergedConfig = { ...defaultConfig, ...seoConfig }
    const currentUrl = `${config.public.baseUrl || 'https://legalflow.com'}${route.path}`

    useHead({
      title: mergedConfig.title,
      meta: [
        // Basic meta tags
        { name: 'description', content: mergedConfig.description },
        { name: 'keywords', content: mergedConfig.keywords?.join(', ') },
        { name: 'author', content: mergedConfig.author },
        
        // Open Graph tags
        { property: 'og:title', content: mergedConfig.title },
        { property: 'og:description', content: mergedConfig.description },
        { property: 'og:image', content: mergedConfig.image },
        { property: 'og:url', content: mergedConfig.url || currentUrl },
        { property: 'og:type', content: mergedConfig.type },
        { property: 'og:site_name', content: mergedConfig.siteName },
        { property: 'og:locale', content: mergedConfig.locale },
        
        // Twitter Card tags
        { name: 'twitter:card', content: 'summary_large_image' },
        { name: 'twitter:title', content: mergedConfig.title },
        { name: 'twitter:description', content: mergedConfig.description },
        { name: 'twitter:image', content: mergedConfig.image },
        
        // Article specific tags
        ...(mergedConfig.type === 'article' ? [
          { property: 'article:author', content: mergedConfig.author },
          { property: 'article:published_time', content: mergedConfig.publishedTime },
          { property: 'article:modified_time', content: mergedConfig.modifiedTime },
          { property: 'article:section', content: mergedConfig.section },
          ...(mergedConfig.tags?.map(tag => ({ property: 'article:tag', content: tag })) || [])
        ] : []),
        
        // Additional SEO tags
        { name: 'robots', content: 'index, follow' },
        { name: 'googlebot', content: 'index, follow' },
        { name: 'viewport', content: 'width=device-width, initial-scale=1' },
        { 'http-equiv': 'X-UA-Compatible', content: 'IE=edge' }
      ],
      link: [
        { rel: 'canonical', href: mergedConfig.url || currentUrl }
      ]
    })
  }

  /**
   * Add structured data (JSON-LD)
   */
  const addStructuredData = (data: StructuredData | StructuredData[]) => {
    const structuredDataArray = Array.isArray(data) ? data : [data]
    
    structuredDataArray.forEach((item, index) => {
      useHead({
        script: [
          {
            type: 'application/ld+json',
            innerHTML: JSON.stringify(item),
            key: `structured-data-${index}`
          }
        ]
      })
    })
  }

  /**
   * Generate organization structured data
   */
  const getOrganizationStructuredData = (): StructuredData => {
    return {
      '@context': 'https://schema.org',
      '@type': 'Organization',
      name: 'LegalFlow',
      description: 'Secure Legal SaaS Platform for Law Firms',
      url: config.public.baseUrl || 'https://legalflow.com',
      logo: `${config.public.baseUrl || 'https://legalflow.com'}/logos/legal-saas-logo-full.svg`,
      contactPoint: {
        '@type': 'ContactPoint',
        telephone: '******-123-4567',
        contactType: 'customer service',
        email: '<EMAIL>'
      },
      address: {
        '@type': 'PostalAddress',
        streetAddress: '123 Legal Street',
        addressLocality: 'San Francisco',
        addressRegion: 'CA',
        postalCode: '94105',
        addressCountry: 'US'
      },
      sameAs: [
        'https://twitter.com/legalflow',
        'https://linkedin.com/company/legalflow'
      ]
    }
  }

  /**
   * Generate website structured data
   */
  const getWebsiteStructuredData = (): StructuredData => {
    return {
      '@context': 'https://schema.org',
      '@type': 'WebSite',
      name: 'LegalFlow',
      description: 'Secure Legal SaaS Platform for Law Firms',
      url: config.public.baseUrl || 'https://legalflow.com',
      potentialAction: {
        '@type': 'SearchAction',
        target: `${config.public.baseUrl || 'https://legalflow.com'}/search?q={search_term_string}`,
        'query-input': 'required name=search_term_string'
      }
    }
  }

  /**
   * Generate software application structured data
   */
  const getSoftwareApplicationStructuredData = (): StructuredData => {
    return {
      '@context': 'https://schema.org',
      '@type': 'SoftwareApplication',
      name: 'LegalFlow',
      description: 'Comprehensive legal practice management software for modern law firms',
      applicationCategory: 'BusinessApplication',
      operatingSystem: 'Web Browser',
      offers: {
        '@type': 'Offer',
        price: '99',
        priceCurrency: 'USD',
        priceValidUntil: '2024-12-31',
        availability: 'https://schema.org/InStock'
      },
      aggregateRating: {
        '@type': 'AggregateRating',
        ratingValue: '4.8',
        ratingCount: '150',
        bestRating: '5',
        worstRating: '1'
      },
      author: {
        '@type': 'Organization',
        name: 'LegalFlow Inc.'
      }
    }
  }

  /**
   * Generate FAQ structured data
   */
  const getFAQStructuredData = (faqs: Array<{ question: string; answer: string }>): StructuredData => {
    return {
      '@context': 'https://schema.org',
      '@type': 'FAQPage',
      mainEntity: faqs.map(faq => ({
        '@type': 'Question',
        name: faq.question,
        acceptedAnswer: {
          '@type': 'Answer',
          text: faq.answer
        }
      }))
    }
  }

  /**
   * Generate breadcrumb structured data
   */
  const getBreadcrumbStructuredData = (breadcrumbs: Array<{ name: string; url: string }>): StructuredData => {
    return {
      '@context': 'https://schema.org',
      '@type': 'BreadcrumbList',
      itemListElement: breadcrumbs.map((crumb, index) => ({
        '@type': 'ListItem',
        position: index + 1,
        name: crumb.name,
        item: crumb.url
      }))
    }
  }

  /**
   * Set up common structured data for the site
   */
  const setupCommonStructuredData = () => {
    const commonData = [
      getOrganizationStructuredData(),
      getWebsiteStructuredData(),
      getSoftwareApplicationStructuredData()
    ]
    
    addStructuredData(commonData)
  }

  /**
   * Generate meta tags for social sharing
   */
  const generateSocialMeta = (config: SEOConfig) => {
    return {
      // Facebook/Open Graph
      'og:title': config.title,
      'og:description': config.description,
      'og:image': config.image,
      'og:url': config.url,
      'og:type': config.type || 'website',
      'og:site_name': config.siteName || 'LegalFlow',
      
      // Twitter
      'twitter:card': 'summary_large_image',
      'twitter:title': config.title,
      'twitter:description': config.description,
      'twitter:image': config.image,
      
      // LinkedIn
      'linkedin:title': config.title,
      'linkedin:description': config.description,
      'linkedin:image': config.image
    }
  }

  /**
   * Validate SEO configuration
   */
  const validateSEO = (config: SEOConfig): string[] => {
    const warnings: string[] = []
    
    if (!config.title) warnings.push('Title is missing')
    if (!config.description) warnings.push('Description is missing')
    if (config.title && config.title.length > 60) warnings.push('Title is too long (>60 characters)')
    if (config.description && config.description.length > 160) warnings.push('Description is too long (>160 characters)')
    if (!config.image) warnings.push('Image is missing')
    
    return warnings
  }

  return {
    setSEO,
    addStructuredData,
    getOrganizationStructuredData,
    getWebsiteStructuredData,
    getSoftwareApplicationStructuredData,
    getFAQStructuredData,
    getBreadcrumbStructuredData,
    setupCommonStructuredData,
    generateSocialMeta,
    validateSEO
  }
}
