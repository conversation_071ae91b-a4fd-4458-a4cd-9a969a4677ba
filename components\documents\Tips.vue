<template>

    <div class="space-y-3 text-sm text-gray-600 dark:text-gray-400">
        <div class="flex items-start space-x-2">
        <Icon
            name="heroicons:light-bulb"
            size="calc(var(--spacing) * 4)"
            class=" text-yellow-500 mt-0.5 flex-shrink-0"
        />
        <p>Use variables like {clientName} for dynamic content</p>
        </div>
        <div class="flex items-start space-x-2">
        <Icon
            name="heroicons:shield-check"
            size="calc(var(--spacing) * 4)"
            class=" text-green-500 mt-0.5 flex-shrink-0"
        />
        <p>Review legal compliance before publishing</p>
        </div>
        <div class="flex items-start space-x-2">
        <Icon
            name="heroicons:document-duplicate"
            size="calc(var(--spacing) * 4)"
            class=" text-blue-500 mt-0.5 flex-shrink-0"
        />
        <p>Save drafts frequently to avoid losing work</p>
        </div>
    </div>

</template>
