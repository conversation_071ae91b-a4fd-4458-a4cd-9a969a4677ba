/**
 * Client-side i18n initialization plugin
 * Handles language preference restoration and RTL setup
 */
export default defineNuxtPlugin(() => {
  const supportedLanguages = ['en', 'he', 'ar']
  const rtlLanguages = ['he', 'ar']

  // Function to update document direction and language
  const updateDocumentLanguage = (languageCode: string) => {
    if (!process.client) return

    const isRTL = rtlLanguages.includes(languageCode)
    document.documentElement.dir = isRTL ? 'rtl' : 'ltr'
    document.documentElement.lang = languageCode

    // Add language-specific classes to body for CSS targeting
    document.body.classList.remove('lang-en', 'lang-he', 'lang-ar', 'rtl', 'ltr')
    document.body.classList.add(`lang-${languageCode}`, isRTL ? 'rtl' : 'ltr')
  }

  // Initialize language from localStorage or browser preference
  const initializeLanguage = () => {
    if (!process.client) return 'en'

    // Try to get saved preference first
    const savedLanguage = localStorage.getItem('preferred-language')
    if (savedLanguage && supportedLanguages.includes(savedLanguage)) {
      updateDocumentLanguage(savedLanguage)
      return savedLanguage
    }

    // Fall back to browser language
    const browserLanguage = navigator.language.split('-')[0]
    if (supportedLanguages.includes(browserLanguage)) {
      updateDocumentLanguage(browserLanguage)
      localStorage.setItem('preferred-language', browserLanguage)
      return browserLanguage
    }

    // Default to English
    updateDocumentLanguage('en')
    localStorage.setItem('preferred-language', 'en')
    return 'en'
  }

  // Initialize language on client side
  if (process.client) {
    // Initialize immediately
    const initialLanguage = initializeLanguage()

    // Listen for language change events from other components
    window.addEventListener('language-changed', (event: CustomEvent) => {
      const { language } = event.detail
      updateDocumentLanguage(language)
      localStorage.setItem('preferred-language', language)
    })
  }
})
