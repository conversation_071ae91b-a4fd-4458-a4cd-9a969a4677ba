/* Global enhancements for public pages - LegalFlow Theme */

/* Additional theme variables for public pages using existing brand colors */
:root {
  /* Legal-specific design tokens using existing brand colors */
  --color-legal-navy: var(--color-brandPrimary-800);
  --color-legal-gold: var(--color-brandWarning-500);
  --color-legal-silver: #64748b;
  --color-legal-charcoal: #374151;
  --color-legal-cream: var(--color-brandWarning-50);

  /* Enhanced shadows using brand colors */
  --shadow-legal: 0 4px 6px -1px rgb(26 86 219 / 0.1), 0 2px 4px -2px rgb(26 86 219 / 0.1);
  --shadow-legal-lg: 0 10px 15px -3px rgb(26 86 219 / 0.1), 0 4px 6px -4px rgb(26 86 219 / 0.1);
  --shadow-brand: var(--shadow-menu);
  --shadow-brand-lg: var(--shadow-menu-mobile);

  /* Enhanced transitions using existing variables */
  --transition-legal: var(--transition-menu);
  --transition-legal-fast: var(--transition-menu-fast);
  --transition-legal-slow: 500ms cubic-bezier(0.4, 0, 0.2, 1);
}

/* Smooth scrolling for the entire document */
html {
  scroll-behavior: smooth;
}

/* Enhanced focus styles for accessibility with brand theme */
/* *:focus {
  outline: 2px solid var(--color-brandPrimary-600);
  outline-offset: 2px;
  border-radius: 0.375rem;
} */

/* *:focus-visible {
  outline: 2px solid var(--color-brandPrimary-600);
  outline-offset: 2px;
  box-shadow: 0 0 0 4px rgb(26 86 219 / 0.1);
  border-radius: 0.375rem;
} */

/* Loading states with brand theme colors */
.loading-spinner {
  display: inline-block;
  width: 1rem;
  height: 1rem;
  border: 2px solid var(--color-brandPrimary-200);
  border-top-color: var(--color-brandPrimary-600);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.loading-skeleton {
  background: linear-gradient(
    90deg,
    #e2e8f0 25%,
    #f1f5f9 50%,
    #e2e8f0 75%
  );
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 0.375rem;
}

@keyframes skeleton-loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

/* Scroll animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(100%);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Animation classes */
.animate-fade-in-up {
  animation: fadeInUp 0.8s ease-out forwards;
  
}

.animate-fade-in-down {
  animation: fadeInDown 0.8s ease-out forwards;
 
}

.animate-fade-in-left {
  animation: fadeInLeft 0.8s ease-out forwards;
 
}

.animate-fade-in-right {
  animation: fadeInRight 0.8s ease-out forwards;
 
}

.animate-scale-in {
  animation: scaleIn 0.6s ease-out forwards;
 
}

.animate-slide-in-up {
  animation: slideInUp 0.8s ease-out forwards;
 
}

/* Animation delays */
.delay-100 { animation-delay: 0.1s; }
.delay-200 { animation-delay: 0.2s; }
.delay-300 { animation-delay: 0.3s; }
.delay-400 { animation-delay: 0.4s; }
.delay-500 { animation-delay: 0.5s; }
.delay-600 { animation-delay: 0.6s; }
.delay-700 { animation-delay: 0.7s; }
.delay-800 { animation-delay: 0.8s; }

/* Intersection Observer animations */
.scroll-animate {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.8s ease-out;
}

.scroll-animate.in-view {
  opacity: 1;
  transform: translateY(0);
}

/* Enhanced hover effects with brand theme */
.hover-lift {
  transition: transform var(--transition-legal), box-shadow var(--transition-legal);
}

.hover-lift:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-legal-lg);
}

.hover-scale {
  transition: transform var(--transition-legal);
}

.hover-scale:hover {
  transform: scale(1.05);
}

.hover-glow {
  transition: box-shadow var(--transition-legal);
}

.hover-glow:hover {
  box-shadow: 0 0 20px rgb(26 86 219 / 0.3);
}

.hover-glow-brand {
  transition: box-shadow var(--transition-legal);
}

.hover-glow-brand:hover {
  box-shadow: 0 0 20px rgb(26 86 219 / 0.2);
}

.hover-glow-secondary {
  transition: box-shadow var(--transition-legal);
}

.hover-glow-secondary:hover {
  box-shadow: 0 0 20px rgb(6 182 212 / 0.3);
}

/* Brand-themed gradient text effects */
.gradient-text {
  background: linear-gradient(135deg, var(--color-brandPrimary-600) 0%, var(--color-brandSecondary-600) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.gradient-text-primary {
  background: linear-gradient(135deg, var(--color-brandPrimary-500) 0%, var(--color-brandPrimary-700) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.gradient-text-brand {
  background: linear-gradient(135deg, var(--color-brandPrimary-600) 0%, var(--color-brandSecondary-500) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.gradient-text-legal {
  background: linear-gradient(135deg, var(--color-legal-navy) 0%, var(--color-brandPrimary-600) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.gradient-text-accent {
  background: linear-gradient(135deg, var(--color-brandWarning-500) 0%, var(--color-brandWarning-600) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Enhanced button styles with brand theme */
.btn-enhanced {
  position: relative;
  overflow: hidden;
  transition: all var(--transition-legal);
}

.btn-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.btn-enhanced:hover::before {
  left: 100%;
}

.btn-brand {
  background: linear-gradient(135deg, var(--color-brandPrimary-600) 0%, var(--color-brandPrimary-700) 100%);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 600;
  transition: all var(--transition-legal);
  box-shadow: var(--shadow-brand);
}

.btn-brand:hover {
  background: linear-gradient(135deg, var(--color-brandPrimary-700) 0%, var(--color-brandPrimary-800) 100%);
  transform: translateY(-1px);
  box-shadow: var(--shadow-brand-lg);
}

/* Card enhancements with brand theme */
.card-enhanced {
  transition: all var(--transition-legal);
  position: relative;
  overflow: hidden;
  background: white;
  border-radius: 0.75rem;
  border: 1px solid #e2e8f0;
}

.card-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgb(26 86 219 / 0.05) 0%, rgb(6 182 212 / 0.05) 100%);
  opacity: 0;
  transition: opacity var(--transition-legal);
}

.card-enhanced:hover::before {
  opacity: 1;
}

.card-enhanced:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-brand-lg);
  border-color: var(--color-brandPrimary-200);
}

.card-brand {
  background: linear-gradient(135deg, var(--color-brandPrimary-50) 0%, var(--color-brandSecondary-50) 100%);
  border: 1px solid var(--color-brandPrimary-200);
  transition: all var(--transition-legal);
}

.card-brand:hover {
  background: linear-gradient(135deg, var(--color-brandPrimary-100) 0%, var(--color-brandSecondary-100) 100%);
  border-color: var(--color-brandPrimary-300);
  box-shadow: var(--shadow-brand-lg);
}

/* Parallax effects */
.parallax {
  transform: translateZ(0);
  will-change: transform;
}

/* Performance optimizations */
.gpu-accelerated {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
  
  .scroll-animate {
    opacity: 1;
    transform: none;
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-break-before {
    page-break-before: always;
  }
  
  .print-break-after {
    page-break-after: always;
  }
}

/* High contrast mode support with brand theme */
@media (prefers-contrast: high) {
  .card-enhanced,
  .hover-lift {
    border: 2px solid var(--color-brandPrimary-600);
  }

  .btn-brand {
    border: 2px solid var(--color-brandPrimary-800);
  }

  .gradient-text,
  .gradient-text-primary,
  .gradient-text-brand {
    background: var(--color-brandPrimary-700);
    -webkit-background-clip: unset;
    -webkit-text-fill-color: unset;
    background-clip: unset;
    color: var(--color-brandPrimary-700);
  }
}

/* Additional brand-specific utility classes */
.bg-brand-gradient {
  background: linear-gradient(135deg, var(--color-brandPrimary-600) 0%, var(--color-brandSecondary-600) 100%);
}

.bg-brand-gradient-light {
  background: linear-gradient(135deg, var(--color-brandPrimary-50) 0%, var(--color-brandSecondary-50) 100%);
}

.text-brand-primary {
  color: var(--color-brandPrimary-600);
}

.text-brand-secondary {
  color: var(--color-brandSecondary-600);
}

.border-brand {
  border-color: var(--color-brandPrimary-300);
}

.shadow-brand-soft {
  box-shadow: 0 2px 4px rgb(26 86 219 / 0.1);
}

.shadow-brand-medium {
  box-shadow: var(--shadow-brand);
}

.shadow-brand-strong {
  box-shadow: var(--shadow-brand-lg);
}

/* Legal-specific styling */
.legal-section {
  background: linear-gradient(135deg, var(--color-brandPrimary-50) 0%, white 100%);
  border-top: 1px solid var(--color-brandPrimary-200);
}

.legal-card {
  background: white;
  border: 1px solid var(--color-brandPrimary-200);
  border-radius: 0.75rem;
  box-shadow: var(--shadow-brand-soft);
  transition: all var(--transition-legal);
}

.legal-card:hover {
  border-color: var(--color-brandPrimary-300);
  box-shadow: var(--shadow-brand-medium);
  transform: translateY(-1px);
}

.legal-badge {
  background: var(--color-brandPrimary-100);
  color: var(--color-brandPrimary-700);
  border: 1px solid var(--color-brandPrimary-200);
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.875rem;
  font-weight: 500;
}
