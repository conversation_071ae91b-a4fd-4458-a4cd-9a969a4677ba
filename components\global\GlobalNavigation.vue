<template>
  <div class="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
    <!-- Enhanced Breadcrumbs -->
    <div class="px-4 sm:px-6 lg:px-8 py-4">
      <nav class="flex items-center justify-between">
        <!-- Dynamic Breadcrumb Navigation -->
        <div class="flex items-center space-x-2 text-sm">
          <!-- Home/Dashboard Link -->
          <NuxtLink
            to="/dashboard"
            class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors duration-200"
          >
            <Icon name="material-symbols:home" class="h-4 w-4" />
          </NuxtLink>

          <!-- Dynamic Breadcrumb Items -->
          <template v-for="(item, index) in computedBreadcrumbItems" :key="index">
            <Icon name="material-symbols:chevron-right" class="h-4 w-4 text-gray-400" />

            <NuxtLink
              v-if="item.href && index < computedBreadcrumbItems.length - 1"
              :to="item.href"
              class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors duration-200"
            >
              {{ item.label }}
            </NuxtLink>

            <span v-else class="text-gray-900 dark:text-white font-medium">
              {{ item.label }}
            </span>
          </template>

          <!-- Current Filter Indicator -->
          <div v-if="hasActiveFilters" class="flex items-center ml-2">
            <Icon name="material-symbols:filter-alt" class="h-4 w-4 text-brandPrimary" />
            <span class="text-xs text-brandPrimary ml-1">Filtered</span>
          </div>
        </div>

        <!-- Quick Actions Toolbar -->
        <div class="flex items-center space-x-2">
          <!-- Keyboard Shortcuts Help -->
          <UiButton
            v-if="showKeyboardShortcuts"
            @click="showKeyboardShortcutsModal = true"
            variant="ghost"
            size="sm"
            class="hidden md:flex"
          >
            <Icon name="material-symbols:keyboard" class="h-4 w-4 mr-1" />
            <span class="text-xs">Shortcuts</span>
          </UiButton>

          <!-- View Toggle -->
          <div
            v-if="showViewToggle"
            class="flex items-center bg-gray-100 dark:bg-gray-700 rounded-lg p-1"
          >
            <UiButton
              @click="$emit('view-changed', 'table')"
              :variant="props.currentView === 'table' ? 'primary' : 'ghost'"
              size="sm"
              class="px-2 py-1"
            >
              <Icon name="material-symbols:table-rows" class="h-4 w-4" />
            </UiButton>
            <UiButton
              @click="$emit('view-changed', 'cards')"
              :variant="props.currentView === 'cards' ? 'primary' : 'ghost'"
              size="sm"
              class="px-2 py-1"
            >
              <Icon name="material-symbols:grid-view" class="h-4 w-4" />
            </UiButton>
            <UiButton
              v-if="props.currentView === 'list' || props.currentView === 'grid'"
              @click="$emit('view-changed', 'list')"
              :variant="props.currentView === 'list' ? 'primary' : 'ghost'"
              size="sm"
              class="px-2 py-1"
            >
              <Icon name="material-symbols:list" class="h-4 w-4" />
            </UiButton>
            <UiButton
              v-if="props.currentView === 'list' || props.currentView === 'grid'"
              @click="$emit('view-changed', 'grid')"
              :variant="props.currentView === 'grid' ? 'primary' : 'ghost'"
              size="sm"
              class="px-2 py-1"
            >
              <Icon name="material-symbols:grid-view" class="h-4 w-4" />
            </UiButton>
          </div>

          <!-- Quick Filter Presets -->
          <div v-if="showQuickFilters" class="relative">
            <UiButton
              @click="showQuickFiltersDropdown = !showQuickFiltersDropdown"
              variant="outline"
              size="sm"
            >
              <Icon name="material-symbols:speed" class="h-4 w-4 mr-1" />
              Quick Filters
              <Icon name="material-symbols:expand-more" class="h-4 w-4 ml-1" />
            </UiButton>

            <!-- Quick Filters Dropdown -->
            <Transition
              enter-active-class="transition ease-out duration-200"
              enter-from-class="transform opacity-0 scale-95"
              enter-to-class="transform opacity-100 scale-100"
              leave-active-class="transition ease-in duration-150"
              leave-from-class="transform opacity-100 scale-100"
              leave-to-class="transform opacity-0 scale-95"
            >
              <div
                v-if="showQuickFiltersDropdown"
                class="absolute right-0 top-10 w-64 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 py-2 z-20"
              >
                <button
                  v-for="preset in computedQuickFilterPresets"
                  :key="preset.key"
                  @click="applyQuickFilter(preset)"
                  class="w-full px-4 py-2 text-left text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center justify-between"
                >
                  <div class="flex items-center">
                    <Icon :name="preset.icon" class="h-4 w-4 mr-2" />
                    {{ preset.label }}
                  </div>
                  <span
                    class="text-xs text-gray-500 bg-gray-100 dark:bg-gray-700 px-2 py-0.5 rounded-full"
                  >
                    {{ preset.count }}
                  </span>
                </button>
              </div>
            </Transition>
          </div>

          <!-- Actions Menu -->
          <div v-if="showActionsMenu" class="relative">
            <UiButton
              @click="showActionsMenuDropdown = !showActionsMenuDropdown"
              variant="outline"
              size="sm"
            >
              <Icon name="material-symbols:more-horiz" class="h-4 w-4" />
            </UiButton>

            <!-- Actions Dropdown -->
            <Transition
              enter-active-class="transition ease-out duration-200"
              enter-from-class="transform opacity-0 scale-95"
              enter-to-class="transform opacity-100 scale-100"
              leave-active-class="transition ease-in duration-150"
              leave-from-class="transform opacity-100 scale-100"
              leave-to-class="transform opacity-0 scale-95"
            >
              <div
                v-if="showActionsMenuDropdown"
                class="absolute right-0 top-10 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 py-2 z-20"
              >
              <template v-for="(action, index) in computedActionsMenu" :key="index" >
                <button
                  v-if="action.type != 'divider'"
                  @click="$emit('action', action.key)"
                  class="w-full px-4 py-2 text-left text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center"
                > 
                 <Icon :name="action.icon" size="calc(var(--spacing) * 5)" class="mr-2" />
                {{ action.label }}
                </button>
                 <hr v-else class="my-2 border-gray-200 dark:border-gray-700" />
               
              </template>
              </div>
            </Transition>
          </div>
        </div>
      </nav>
    </div>

    <!-- Contextual Action Bar -->
    <Transition
      enter-active-class="transition-all duration-300 ease-out"
      enter-from-class="opacity-0 transform -translate-y-2"
      enter-to-class="opacity-100 transform translate-y-0"
      leave-active-class="transition-all duration-300 ease-in"
      leave-from-class="opacity-100 transform translate-y-0"
      leave-to-class="opacity-0 transform -translate-y-2"
    >
      <div
        v-if="selectedCount && selectedCount > 0"
        class="bg-blue-50 dark:bg-blue-900/20 border-t border-blue-200 dark:border-blue-800 px-4 sm:px-6 lg:px-8 py-3"
      >
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-3">
            <Icon name="material-symbols:check-circle" class="h-5 w-5 text-blue-600" />
            <span class="text-sm font-medium text-blue-900 dark:text-blue-100">
              {{ selectedCount }} item{{ selectedCount > 1 ? "s" : "" }} selected
            </span>
          </div>

          <div class="flex items-center space-x-2">
            <UiButton
              @click="$emit('bulk-action', 'activate')"
              size="sm"
              variant="outline"
            >
              <Icon name="material-symbols:play-arrow" class="h-4 w-4 mr-1" />
              Activate
            </UiButton>
            <UiButton
              @click="$emit('bulk-action', 'deactivate')"
              size="sm"
              variant="outline"
            >
              <Icon name="material-symbols:pause" class="h-4 w-4 mr-1" />
              Deactivate
            </UiButton>
            <UiButton @click="$emit('bulk-action', 'export')" size="sm" variant="outline">
              <Icon name="material-symbols:download" class="h-4 w-4 mr-1" />
              Export Selected
            </UiButton>
            <UiButton @click="$emit('clear-selection')" size="sm" variant="ghost">
              <Icon name="material-symbols:close" class="h-4 w-4" />
            </UiButton>
          </div>
        </div>
      </div>
    </Transition>

    <!-- Keyboard Shortcuts Modal -->
    <Transition
      enter-active-class="transition-opacity duration-300"
      enter-from-class="opacity-0"
      enter-to-class="opacity-100"
      leave-active-class="transition-opacity duration-300"
      leave-from-class="opacity-100"
      leave-to-class="opacity-0"
    >
      <div
        v-if="showKeyboardShortcutsModal"
        class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
      >
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
              Keyboard Shortcuts
            </h3>
            <UiButton
              @click="showKeyboardShortcutsModal = false"
              size="sm"
              variant="ghost"
            >
              <Icon name="material-symbols:close" class="h-4 w-4" />
            </UiButton>
          </div>

          <div class="space-y-3">
            <div
              v-for="(shortcut, index) in keyboardShortcuts"
              :key="index"
              class="flex items-center justify-between"
            >
              <span class="text-sm text-gray-700 dark:text-gray-300">{{
                shortcut.description
              }}</span>
              <div class="flex items-center space-x-1">
                <kbd
                  v-for="key in shortcut.keys"
                  :key="key"
                  class="px-2 py-1 text-xs font-semibold text-gray-800 bg-gray-100 border border-gray-200 rounded dark:bg-gray-700 dark:text-gray-200 dark:border-gray-600"
                >
                  {{ key }}
                </kbd>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Transition>
  </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, ref, computed } from "vue";
import { useRoute } from "vue-router";

interface BreadcrumbItem {
  label: string;
  href?: string;
}

interface QuickFilterPreset {
  key: string;
  label: string;
  icon: string;
  count: number;
}

interface ActionMenu {
  key: string;
  label: string;
  icon?: string;
  type?: "button" | "divider";

 
 
}

interface Props {
  currentView?: "table" | "cards" | "list" | "grid";
  selectedCount?: number;
  hasActiveFilters?: boolean;
  breadcrumbItems?: BreadcrumbItem[] | (() => BreadcrumbItem[]);
  quickFilterPresets?: QuickFilterPreset[];
  showViewToggle?: boolean;
  showQuickFilters?: boolean;
  showActionsMenu?: boolean;
  actionsMenu: ActionMenu[] | (() => ActionMenu[]);
  showKeyboardShortcuts?: boolean;
  pageTitle?: string;
}

interface Emits {
  (e: "view-changed", view: "table" | "cards" | "list" | "grid"): void;
  (e: "quick-filter", filter: string): void;
  (e: "bulk-action", action: string): void;
  (e: "action", action: string): void;
  (e: "clear-selection"): void;
}

const route = useRoute();

const props = withDefaults(defineProps<Props>(), {
  currentView: "table",
  selectedCount: 0,
  hasActiveFilters: false,
  breadcrumbItems: () => [],
  quickFilterPresets: () => [],
  showViewToggle: false,
  showQuickFilters: false,
  showActionsMenu: false,
  actionsMenu: () => [],
  showKeyboardShortcuts: false,
  pageTitle: "",
});
const emit = defineEmits<Emits>();

// State
const showQuickFiltersDropdown = ref(false);
const showActionsMenuDropdown = ref(false);
const showKeyboardShortcutsModal = ref(false);

watch(() => route.path, () => {
  emit('view-changed', 'table')
});


// Computed breadcrumb items - auto-generate from route if not provided
const computedBreadcrumbItems = computedAsync (async () => {
  let breadcrumbs: BreadcrumbItem[] = [];
  if (typeof props.breadcrumbItems === "function") {
    breadcrumbs = await props.breadcrumbItems();
  } else if (Array.isArray(props.breadcrumbItems)) {
    breadcrumbs = props.breadcrumbItems;
  } else {
    // Auto-generate breadcrumbs from route path
    const pathSegments = route.path
      .split("/")
      .filter((segment) => segment && segment !== "dashboard");
 

    let currentPath = "/dashboard";

    pathSegments.forEach((segment, index) => {
      currentPath += `/${segment}`;
      const isLast = index === pathSegments.length - 1;

      breadcrumbs.push({
        label: segment.charAt(0).toUpperCase() + segment.slice(1).replace(/-/g, " "),
        href: isLast ? undefined : currentPath,
      });
    });
  }
  
  
  return breadcrumbs;
});

// Computed quick filter presets - use props or default
const computedQuickFilterPresets = computed(() => {
  if (props.quickFilterPresets && props.quickFilterPresets.length > 0) {
    return props.quickFilterPresets;
  }

  // Default presets for backward compatibility
  return [
    {
      key: "all",
      label: "All Items",
      icon: "material-symbols:list",
      count: 0,
    },
    {
      key: "active",
      label: "Active Only",
      icon: "material-symbols:check-circle",
      count: 0,
    },
    {
      key: "inactive",
      label: "Inactive Only",
      icon: "material-symbols:pause-circle",
      count: 0,
    },
  ];
});

const computedActionsMenu = computed(() => {
 if (typeof props.actionsMenu === "function") {
    return props.actionsMenu();
  }
  return props.actionsMenu;
})

// Keyboard shortcuts
const keyboardShortcuts = [
  { description: "Create new item", keys: ["Ctrl", "C"] },
  { description: "Search items", keys: ["Ctrl", "F"] },
  { description: "Refresh data", keys: ["Ctrl", "R"] },
  { description: "Toggle view mode", keys: ["Ctrl", "V"] },
  { description: "Select all", keys: ["Ctrl", "A"] },
  { description: "Export data", keys: ["Ctrl", "E"] },
  { description: "Show shortcuts", keys: ["Ctrl", "?"] },
];

// Methods
const applyQuickFilter = (preset: QuickFilterPreset) => {
  showQuickFiltersDropdown.value = false;
  emit("quick-filter", preset.key);
};

const handleKeyboardShortcuts = (event: KeyboardEvent) => {
  if (event.ctrlKey || event.metaKey) {
    switch (event.key) {
      case "c":
        event.preventDefault();
        emit("action", "create");
        break;
      case "f":
        event.preventDefault();
        // Focus search input if available
        const searchInput = document.querySelector(
          'input[type="search"], input[placeholder*="search" i]'
        ) as HTMLInputElement;
        if (searchInput) {
          searchInput.focus();
        }
        break;
      case "r":
        event.preventDefault();
        emit("action", "refresh");
        break;
      case "v":
        event.preventDefault();
        const nextView = props.currentView === "table" ? "cards" : "table";
        emit("view-changed", nextView);
        break;
      case "a":
        event.preventDefault();
        emit("bulk-action", "select-all");
        break;
      case "e":
        event.preventDefault();
        emit("action", "export-all");
        break;
      case "/":
      case "?":
        event.preventDefault();
        showKeyboardShortcutsModal.value = true;
        break;
    }
  }

  // Escape key to close modals
  if (event.key === "Escape") {
    showKeyboardShortcutsModal.value = false;
    showQuickFiltersDropdown.value = false;
    showActionsMenuDropdown.value = false;
  }
};

// Close dropdowns when clicking outside
const handleClickOutside = (event: Event) => {
  const target = event.target as HTMLElement;
  if (!target.closest(".relative")) {
    showQuickFiltersDropdown.value = false;
    showActionsMenuDropdown.value = false;
  }
};

onMounted(() => {
  window.addEventListener("keydown", handleKeyboardShortcuts, true);
  document.addEventListener("click", handleClickOutside);
});

onUnmounted(() => {
  window.removeEventListener("keydown", handleKeyboardShortcuts, true);
  document.removeEventListener("click", handleClickOutside);
});
</script>
