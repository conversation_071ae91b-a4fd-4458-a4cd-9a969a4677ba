// types/recipient.d.ts

import { PlatformRoles, TenantRoles } from '~/app/features/auth/constants/roles';

// Represents an individual user recipient
export interface UserRecipient {
  type: 'user';
  id: string;
  email: string;
  name: string; // Full name or display name
  avatarUrl?: string; // Optional avatar for display
}

// Represents a role recipient (e.g., all 'ADMIN's)
export interface RoleRecipient {
  type: 'role';
  role: Roles; // Use the Roles enum
  label: string; // Display label for the role (e.g., 'Tenant Owner', 'All Lawyers')
}

// Represents a plain email address recipient
export interface EmailRecipient {
  type: 'email';
  value: string; // The email address
}

// Union type for any selected recipient
export type Recipient = UserRecipient | RoleRecipient | EmailRecipient;

// Interface for options displayed in the autocomplete/select
export interface RecipientOption {
  value: string; // Unique identifier (user ID, role enum, or email)
  label: string; // Display text (e.g., "<PERSON> (<EMAIL>)", "Admin Role")
  type: 'user' | 'role' | 'email';
  data?: UserRecipient | RoleRecipient; // Optional: full data for the selected item
}