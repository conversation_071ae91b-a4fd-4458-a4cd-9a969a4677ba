import { describe, it, expect, beforeEach } from 'vitest'
import { ref } from 'vue'
import { useDynamicFormConditionals } from '~/composables/useDynamicFormConditionals'
import type { DynamicFormField } from '~/app/shared/types'
import { DynamicFormFieldType } from '~/app/shared/types'

describe('useDynamicFormConditionals', () => {
  let fields: DynamicFormField[]
  let formData: Record<string, any>
  let conditionals: ReturnType<typeof useDynamicFormConditionals>

  beforeEach(() => {
    fields = [
      {
        id: 'accountType',
        name: 'accountType',
        label: 'Account Type',
        type: DynamicFormFieldType.SELECT,
        options: [
          { label: 'Personal', value: 'personal' },
          { label: 'Business', value: 'business' }
        ]
      },
      {
        id: 'companyName',
        name: 'companyName',
        label: 'Company Name',
        type: DynamicFormFieldType.TEXT,
        conditional: {
          show: [
            { field: 'accountType', operator: 'equals', value: 'business' }
          ],
          require: [
            { field: 'accountType', operator: 'equals', value: 'business' }
          ]
        }
      },
      {
        id: 'personalInfo',
        name: 'personalInfo',
        label: 'Personal Info',
        type: DynamicFormFieldType.TEXT,
        conditional: {
          hide: [
            { field: 'accountType', operator: 'equals', value: 'business' }
          ]
        }
      },
      {
        id: 'notifications',
        name: 'notifications',
        label: 'Notifications',
        type: DynamicFormFieldType.CHECKBOX,
        options: [
          { label: 'Email', value: 'email' },
          { label: 'SMS', value: 'sms' }
        ]
      },
      {
        id: 'phoneNumber',
        name: 'phoneNumber',
        label: 'Phone Number',
        type: DynamicFormFieldType.TEXT,
        conditional: {
          show: [
            { field: 'notifications', operator: 'contains', value: 'sms' }
          ],
          require: [
            { field: 'notifications', operator: 'contains', value: 'sms' }
          ]
        }
      },
      {
        id: 'age',
        name: 'age',
        label: 'Age',
        type: DynamicFormFieldType.NUMBER
      },
      {
        id: 'drinkingAge',
        name: 'drinkingAge',
        label: 'Drinking Age Confirmation',
        type: DynamicFormFieldType.CHECKBOX,
        conditional: {
          show: [
            { field: 'age', operator: 'greater_equal', value: 21 }
          ]
        }
      }
    ]

    formData = {}
    conditionals = useDynamicFormConditionals(fields, formData)
  })

  describe('Condition Evaluation', () => {
    it('evaluates equals condition correctly', () => {
      const condition = { field: 'accountType', operator: 'equals' as const, value: 'business' }
      
      formData.accountType = 'business'
      expect(conditionals.evaluateCondition(condition)).toBe(true)
      
      formData.accountType = 'personal'
      expect(conditionals.evaluateCondition(condition)).toBe(false)
    })

    it('evaluates not_equals condition correctly', () => {
      const condition = { field: 'accountType', operator: 'not_equals' as const, value: 'business' }
      
      formData.accountType = 'personal'
      expect(conditionals.evaluateCondition(condition)).toBe(true)
      
      formData.accountType = 'business'
      expect(conditionals.evaluateCondition(condition)).toBe(false)
    })

    it('evaluates contains condition correctly', () => {
      const condition = { field: 'notifications', operator: 'contains' as const, value: 'sms' }
      
      formData.notifications = ['email', 'sms']
      expect(conditionals.evaluateCondition(condition)).toBe(true)
      
      formData.notifications = ['email']
      expect(conditionals.evaluateCondition(condition)).toBe(false)
      
      // Test string contains
      formData.notifications = 'sms notifications'
      expect(conditionals.evaluateCondition(condition)).toBe(true)
    })

    it('evaluates not_contains condition correctly', () => {
      const condition = { field: 'notifications', operator: 'not_contains' as const, value: 'sms' }
      
      formData.notifications = ['email']
      expect(conditionals.evaluateCondition(condition)).toBe(true)
      
      formData.notifications = ['email', 'sms']
      expect(conditionals.evaluateCondition(condition)).toBe(false)
    })

    it('evaluates in condition correctly', () => {
      const condition = { field: 'accountType', operator: 'in' as const, values: ['business', 'enterprise'] }
      
      formData.accountType = 'business'
      expect(conditionals.evaluateCondition(condition)).toBe(true)
      
      formData.accountType = 'personal'
      expect(conditionals.evaluateCondition(condition)).toBe(false)
    })

    it('evaluates not_in condition correctly', () => {
      const condition = { field: 'accountType', operator: 'not_in' as const, values: ['business', 'enterprise'] }
      
      formData.accountType = 'personal'
      expect(conditionals.evaluateCondition(condition)).toBe(true)
      
      formData.accountType = 'business'
      expect(conditionals.evaluateCondition(condition)).toBe(false)
    })

    it('evaluates greater_than condition correctly', () => {
      const condition = { field: 'age', operator: 'greater_than' as const, value: 18 }
      
      formData.age = 25
      expect(conditionals.evaluateCondition(condition)).toBe(true)
      
      formData.age = 15
      expect(conditionals.evaluateCondition(condition)).toBe(false)
      
      formData.age = 18
      expect(conditionals.evaluateCondition(condition)).toBe(false)
    })

    it('evaluates less_than condition correctly', () => {
      const condition = { field: 'age', operator: 'less_than' as const, value: 65 }
      
      formData.age = 30
      expect(conditionals.evaluateCondition(condition)).toBe(true)
      
      formData.age = 70
      expect(conditionals.evaluateCondition(condition)).toBe(false)
      
      formData.age = 65
      expect(conditionals.evaluateCondition(condition)).toBe(false)
    })

    it('evaluates greater_equal condition correctly', () => {
      const condition = { field: 'age', operator: 'greater_equal' as const, value: 21 }
      
      formData.age = 25
      expect(conditionals.evaluateCondition(condition)).toBe(true)
      
      formData.age = 21
      expect(conditionals.evaluateCondition(condition)).toBe(true)
      
      formData.age = 18
      expect(conditionals.evaluateCondition(condition)).toBe(false)
    })

    it('evaluates less_equal condition correctly', () => {
      const condition = { field: 'age', operator: 'less_equal' as const, value: 65 }
      
      formData.age = 30
      expect(conditionals.evaluateCondition(condition)).toBe(true)
      
      formData.age = 65
      expect(conditionals.evaluateCondition(condition)).toBe(true)
      
      formData.age = 70
      expect(conditionals.evaluateCondition(condition)).toBe(false)
    })

    it('evaluates empty condition correctly', () => {
      const condition = { field: 'companyName', operator: 'empty' as const }
      
      formData.companyName = ''
      expect(conditionals.evaluateCondition(condition)).toBe(true)
      
      formData.companyName = null
      expect(conditionals.evaluateCondition(condition)).toBe(true)
      
      formData.companyName = undefined
      expect(conditionals.evaluateCondition(condition)).toBe(true)
      
      formData.companyName = []
      expect(conditionals.evaluateCondition(condition)).toBe(true)
      
      formData.companyName = 'Acme Corp'
      expect(conditionals.evaluateCondition(condition)).toBe(false)
    })

    it('evaluates not_empty condition correctly', () => {
      const condition = { field: 'companyName', operator: 'not_empty' as const }
      
      formData.companyName = 'Acme Corp'
      expect(conditionals.evaluateCondition(condition)).toBe(true)
      
      formData.companyName = ['item']
      expect(conditionals.evaluateCondition(condition)).toBe(true)
      
      formData.companyName = ''
      expect(conditionals.evaluateCondition(condition)).toBe(false)
      
      formData.companyName = null
      expect(conditionals.evaluateCondition(condition)).toBe(false)
    })

    it('evaluates regex condition correctly', () => {
      const condition = { field: 'email', operator: 'regex' as const, pattern: '^[^@]+@[^@]+\\.[^@]+$' }
      
      formData.email = '<EMAIL>'
      expect(conditionals.evaluateCondition(condition)).toBe(true)
      
      formData.email = 'invalid-email'
      expect(conditionals.evaluateCondition(condition)).toBe(false)
    })
  })

  describe('Multiple Conditions', () => {
    it('evaluates AND conditions correctly', () => {
      const conditions = [
        { field: 'accountType', operator: 'equals' as const, value: 'business' },
        { field: 'age', operator: 'greater_equal' as const, value: 18 }
      ]
      
      formData.accountType = 'business'
      formData.age = 25
      expect(conditionals.evaluateConditions(conditions)).toBe(true)
      
      formData.accountType = 'personal'
      formData.age = 25
      expect(conditionals.evaluateConditions(conditions)).toBe(false)
      
      formData.accountType = 'business'
      formData.age = 15
      expect(conditionals.evaluateConditions(conditions)).toBe(false)
    })

    it('evaluates OR conditions correctly', () => {
      const conditions = [
        { field: 'accountType', operator: 'equals' as const, value: 'business', logic: 'or' as const },
        { field: 'age', operator: 'greater_equal' as const, value: 65, logic: 'or' as const }
      ]
      
      formData.accountType = 'business'
      formData.age = 25
      expect(conditionals.evaluateConditions(conditions)).toBe(true)
      
      formData.accountType = 'personal'
      formData.age = 70
      expect(conditionals.evaluateConditions(conditions)).toBe(true)
      
      formData.accountType = 'personal'
      formData.age = 25
      expect(conditionals.evaluateConditions(conditions)).toBe(false)
    })

    it('evaluates mixed AND/OR conditions correctly', () => {
      const conditions = [
        { field: 'accountType', operator: 'equals' as const, value: 'business' }, // AND
        { field: 'age', operator: 'greater_equal' as const, value: 65, logic: 'or' as const } // OR
      ]
      
      // Business account (AND condition met) + any age = true
      formData.accountType = 'business'
      formData.age = 25
      expect(conditionals.evaluateConditions(conditions)).toBe(true)
      
      // Personal account but senior (OR condition met) = true
      formData.accountType = 'personal'
      formData.age = 70
      expect(conditionals.evaluateConditions(conditions)).toBe(true)
      
      // Personal account and young = false
      formData.accountType = 'personal'
      formData.age = 25
      expect(conditionals.evaluateConditions(conditions)).toBe(false)
    })
  })

  describe('Field Visibility', () => {
    it('shows fields when show conditions are met', () => {
      formData.accountType = 'business'
      conditionals.evaluateAllFields()
      
      expect(conditionals.isFieldVisible('companyName')).toBe(true)
    })

    it('hides fields when show conditions are not met', () => {
      formData.accountType = 'personal'
      conditionals.evaluateAllFields()
      
      expect(conditionals.isFieldVisible('companyName')).toBe(false)
    })

    it('hides fields when hide conditions are met', () => {
      formData.accountType = 'business'
      conditionals.evaluateAllFields()
      
      expect(conditionals.isFieldVisible('personalInfo')).toBe(false)
    })

    it('shows fields when hide conditions are not met', () => {
      formData.accountType = 'personal'
      conditionals.evaluateAllFields()
      
      expect(conditionals.isFieldVisible('personalInfo')).toBe(true)
    })
  })

  describe('Field States', () => {
    it('tracks required fields based on conditions', () => {
      formData.accountType = 'business'
      conditionals.evaluateAllFields()
      
      expect(conditionals.isFieldRequired('companyName')).toBe(true)
      
      formData.accountType = 'personal'
      conditionals.evaluateAllFields()
      
      expect(conditionals.isFieldRequired('companyName')).toBe(false)
    })

    it('tracks enabled/disabled fields based on conditions', () => {
      const fieldWithDisableCondition: DynamicFormField = {
        id: 'specialField',
        name: 'specialField',
        label: 'Special Field',
        type: DynamicFormFieldType.TEXT,
        conditional: {
          disable: [
            { field: 'accountType', operator: 'equals', value: 'business' }
          ]
        }
      }

      const fieldsWithDisable = [...fields, fieldWithDisableCondition]
      const conditionalsWithDisable = useDynamicFormConditionals(fieldsWithDisable, formData)

      formData.accountType = 'business'
      conditionalsWithDisable.evaluateAllFields()
      
      expect(conditionalsWithDisable.isFieldEnabled('specialField')).toBe(false)
      
      formData.accountType = 'personal'
      conditionalsWithDisable.evaluateAllFields()
      
      expect(conditionalsWithDisable.isFieldEnabled('specialField')).toBe(true)
    })
  })

  describe('Dependency Tracking', () => {
    it('identifies dependent fields correctly', () => {
      const dependentFields = conditionals.getDependentFields('accountType')
      expect(dependentFields).toContain('companyName')
      expect(dependentFields).toContain('personalInfo')
    })

    it('re-evaluates dependent fields when dependencies change', () => {
      // Initial state
      formData.accountType = 'personal'
      conditionals.evaluateAllFields()
      expect(conditionals.isFieldVisible('companyName')).toBe(false)

      // Change dependency
      formData.accountType = 'business'
      // The watcher should automatically re-evaluate dependent fields
      // In a real scenario, this would be triggered by the watcher
      const dependentFields = conditionals.getDependentFields('accountType')
      conditionals.evaluateFields(dependentFields)
      
      expect(conditionals.isFieldVisible('companyName')).toBe(true)
    })
  })

  describe('Computed Properties', () => {
    it('provides arrays of field states', () => {
      formData.accountType = 'business'
      formData.notifications = ['sms']
      conditionals.evaluateAllFields()

      expect(conditionals.visibleFields.value).toContain('companyName')
      expect(conditionals.visibleFields.value).toContain('phoneNumber')
      expect(conditionals.hiddenFields.value).toContain('personalInfo')
      expect(conditionals.requiredFields.value).toContain('companyName')
      expect(conditionals.requiredFields.value).toContain('phoneNumber')
    })
  })

  describe('Performance Optimization', () => {
    it('only evaluates specific fields when requested', () => {
      const initialEvaluationCount = conditionals.conditionalState.evaluationCount
      
      // Evaluate only specific fields
      conditionals.evaluateFields(['companyName'])
      
      // Should not increment the global evaluation count
      expect(conditionals.conditionalState.evaluationCount).toBe(initialEvaluationCount)
    })

    it('builds dependency graphs correctly', () => {
      conditionals.buildDependencyGraphs()
      
      // Check that dependencies are tracked
      const dependentFields = conditionals.getDependentFields('accountType')
      expect(dependentFields.length).toBeGreaterThan(0)
    })
  })
})
