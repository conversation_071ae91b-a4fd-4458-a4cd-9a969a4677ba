/**
 * Slug Validation Composable
 * 
 * Provides utilities for slug validation and uniqueness checking
 * with support for different entity types and API endpoints
 */

import { useApi } from '~/composables/useApi';

export interface SlugValidationOptions {
  /** The API endpoint to check uniqueness against */
  endpoint?: string;
  /** The entity type (used to construct default endpoint) */
  entityType?: 'tenants' | 'users' | 'templates' | 'cases' | 'documents';
  /** Current entity ID to exclude from uniqueness check (for edit forms) */
  excludeId?: string;
  /** Additional query parameters for the API call */
  queryParams?: Record<string, any>;
  /** Custom error message for non-unique slugs */
  errorMessage?: string;
}

export interface SlugValidationResult {
  isUnique: boolean;
  isValid: boolean;
  error?: string;
}

/**
 * Composable for slug validation and uniqueness checking
 */
export const useSlugValidation = (options: SlugValidationOptions = {}) => {
  const api = useApi();

  /**
   * Default endpoints for different entity types
   */
  const defaultEndpoints = {
    tenants: '/tenants/check-slug',
    users: '/users/check-slug', 
    templates: '/templates/check-slug',
    cases: '/cases/check-slug',
    documents: '/documents/check-slug'
  };

  /**
   * Validates slug format according to standard rules
   */
  const validateSlugFormat = (slug: string): { isValid: boolean; error?: string } => {
    if (!slug) {
      return { isValid: false, error: 'Slug is required' };
    }

    if (slug.length < 2) {
      return { isValid: false, error: 'Slug must be at least 2 characters long' };
    }

    if (slug.length > 100) {
      return { isValid: false, error: 'Slug must be less than 100 characters long' };
    }

    // Check if slug matches the standard pattern
    if (!/^[a-z0-9]+(?:-[a-z0-9]+)*$/.test(slug)) {
      return { isValid: false, error: 'Slug must contain only lowercase letters, numbers, and hyphens' };
    }

    // Check for reserved slugs
    const reservedSlugs = [
      'admin', 'api', 'www', 'mail', 'ftp', 'localhost', 'root',
      'support', 'help', 'about', 'contact', 'privacy', 'terms',
      'login', 'logout', 'register', 'signup', 'signin', 'dashboard',
      'settings', 'profile', 'account', 'billing', 'subscription'
    ];

    if (reservedSlugs.includes(slug.toLowerCase())) {
      return { isValid: false, error: 'This slug is reserved and cannot be used' };
    }

    return { isValid: true };
  };

  /**
   * Checks if a slug is unique via API call
   */
  const checkSlugUniqueness = async (slug: string): Promise<boolean> => {
    try {
      // First validate format
      const formatValidation = validateSlugFormat(slug);
      if (!formatValidation.isValid) {
        throw new Error(formatValidation.error);
      }

      // Determine endpoint
      const endpoint = options.endpoint || 
        (options.entityType ? defaultEndpoints[options.entityType] : '/check-slug');

      // Prepare query parameters
      const params: Record<string, any> = {
        slug,
        ...options.queryParams
      };

      // Add exclude ID if provided (for edit scenarios)
      if (options.excludeId) {
        params.excludeId = options.excludeId;
      }

      // Make API call
      const response = await api.get<{ isUnique: boolean; exists: boolean }>(endpoint, {
        params
      });

      return response.isUnique !== false && response.exists !== true;
    } catch (error) {
      console.error('Error checking slug uniqueness:', error);
      // If API fails, assume not unique to be safe
      return false;
    }
  };

  /**
   * Comprehensive slug validation (format + uniqueness)
   */
  const validateSlug = async (slug: string): Promise<SlugValidationResult> => {
    // First check format
    const formatValidation = validateSlugFormat(slug);
    if (!formatValidation.isValid) {
      return {
        isValid: false,
        isUnique: false,
        error: formatValidation.error
      };
    }

    // Then check uniqueness
    try {
      const isUnique = await checkSlugUniqueness(slug);
      return {
        isValid: isUnique,
        isUnique,
        error: isUnique ? undefined : (options.errorMessage || 'This slug is already taken')
      };
    } catch (error) {
      return {
        isValid: false,
        isUnique: false,
        error: 'Unable to validate slug uniqueness'
      };
    }
  };

  /**
   * Generates a slug from a given text
   */
  const generateSlug = (text: string): string => {
    return text
      .toLowerCase()
      .trim()
      // Replace spaces and underscores with hyphens
      .replace(/[\s_]+/g, '-')
      // Remove special characters except hyphens
      .replace(/[^a-z0-9-]/g, '')
      // Replace multiple consecutive hyphens with single hyphen
      .replace(/-+/g, '-')
      // Remove leading and trailing hyphens
      .replace(/^-+|-+$/g, '');
  };

  /**
   * Generates a unique slug by appending numbers if needed
   */
  const generateUniqueSlug = async (baseText: string, maxAttempts = 10): Promise<string> => {
    let baseSlug = generateSlug(baseText);
    
    if (!baseSlug) {
      baseSlug = 'item'; // Fallback if generation fails
    }

    // Try the base slug first
    if (await checkSlugUniqueness(baseSlug)) {
      return baseSlug;
    }

    // Try with numbers appended
    for (let i = 1; i <= maxAttempts; i++) {
      const candidateSlug = `${baseSlug}-${i}`;
      if (await checkSlugUniqueness(candidateSlug)) {
        return candidateSlug;
      }
    }

    // If all attempts fail, return with timestamp
    const timestamp = Date.now().toString().slice(-6);
    return `${baseSlug}-${timestamp}`;
  };

  return {
    validateSlugFormat,
    checkSlugUniqueness,
    validateSlug,
    generateSlug,
    generateUniqueSlug
  };
};

/**
 * Pre-configured composables for common entity types
 */
export const useTenantSlugValidation = (excludeId?: string) => 
  useSlugValidation({ 
    entityType: 'tenants', 
    excludeId,
    errorMessage: 'This tenant slug is already taken'
  });

export const useUserSlugValidation = (excludeId?: string) => 
  useSlugValidation({ 
    entityType: 'users', 
    excludeId,
    errorMessage: 'This username is already taken'
  });

export const useTemplateSlugValidation = (excludeId?: string) => 
  useSlugValidation({ 
    entityType: 'templates', 
    excludeId,
    errorMessage: 'This template slug is already taken'
  });

export const useCaseSlugValidation = (excludeId?: string) => 
  useSlugValidation({ 
    entityType: 'cases', 
    excludeId,
    errorMessage: 'This case slug is already taken'
  });

export const useDocumentSlugValidation = (excludeId?: string) => 
  useSlugValidation({ 
    entityType: 'documents', 
    excludeId,
    errorMessage: 'This document slug is already taken'
  });
