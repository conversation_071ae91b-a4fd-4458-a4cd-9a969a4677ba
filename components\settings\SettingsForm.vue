<template>
  <form @submit.prevent="handleSubmit" class="space-y-6">
    <slot />
    
    <!-- Form Actions -->
    <div v-if="showActions" class="flex items-center justify-between pt-6 border-t border-gray-200">
      <div class="flex items-center gap-2">
        <!-- Save Status Indicator -->
        <div v-if="saveStatus" class="flex items-center gap-2 text-sm">
          <Icon
            v-if="saveStatus === 'saving'"
            name="heroicons:arrow-path"
            class="h-4 w-4 text-gray-400 animate-spin"
          />
          <Icon
            v-else-if="saveStatus === 'saved'"
            name="heroicons:check-circle"
            class="h-4 w-4 text-brandSuccess"
          />
          <Icon
            v-else-if="saveStatus === 'error'"
            name="heroicons:exclamation-circle"
            class="h-4 w-4 text-brandDanger"
          />
          
          <span
            :class="[
              saveStatus === 'saving' ? 'text-gray-500' : '',
              saveStatus === 'saved' ? 'text-brandSuccess' : '',
              saveStatus === 'error' ? 'text-brandDanger' : ''
            ]"
          >
            {{ saveStatusText }}
          </span>
        </div>
      </div>

      <div class="flex items-center gap-3">
        <!-- Reset Button -->
        <UiButton
          v-if="showReset && hasChanges"
          type="button"
          variant="ghost"
          @click="handleReset"
          :disabled="loading"
        >
          Reset
        </UiButton>

        <!-- Save Button -->
        <UiButton
          type="submit"
          variant="primary"
          :loading="loading"
          :disabled="!hasChanges || loading"
        >
          {{ saveButtonText }}
        </UiButton>
      </div>
    </div>
  </form>
</template>

<script setup lang="ts">
import { computed } from 'vue';

interface Props {
  loading?: boolean;
  hasChanges?: boolean;
  saveStatus?: 'saving' | 'saved' | 'error' | null;
  showActions?: boolean;
  showReset?: boolean;
  saveButtonText?: string;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  hasChanges: false,
  saveStatus: null,
  showActions: true,
  showReset: true,
  saveButtonText: 'Save Changes'
});

const emit = defineEmits<{
  submit: [];
  reset: [];
}>();

const saveStatusText = computed(() => {
  switch (props.saveStatus) {
    case 'saving':
      return 'Saving...';
    case 'saved':
      return 'Changes saved';
    case 'error':
      return 'Failed to save';
    default:
      return '';
  }
});

const handleSubmit = () => {
  emit('submit');
};

const handleReset = () => {
  emit('reset');
};
</script>
