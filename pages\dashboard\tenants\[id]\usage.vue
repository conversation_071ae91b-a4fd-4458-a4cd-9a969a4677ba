<template>
  <div class="space-y-6">
    <!-- <PERSON> Header with Tenant Context -->
    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
      <div class="flex items-center gap-4">
        <!-- Tenant Logo/Avatar -->
        <div class="flex-shrink-0">
          <div v-if="selectedTenant?.logoUrl" class="w-12 h-12 rounded-lg overflow-hidden">
            <img
              :src="selectedTenant.logoUrl"
              :alt="`${selectedTenant.name} logo`"
              class="w-full h-full object-cover"
            />
          </div>
          <div v-else class="w-12 h-12 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center">
            <Icon name="material-symbols:analytics" class="h-6 w-6 text-purple-600 dark:text-purple-400" />
          </div>
        </div>
        <div>
          <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
            {{ selectedTenant?.name || 'Tenant' }} Usage Analytics
          </h1>
          <p class="text-gray-600 dark:text-gray-400 mt-1">
            Comprehensive usage analytics and insights for this tenant
          </p>
        </div>
      </div>
      <div class="flex items-center gap-3">
        <UiSelect
          id="timeRange"
          v-model="selectedTimeRange"
          :options="timeRangeOptions"
          size="sm"
          class="w-40"
        />
        <UiButton
          variant="secondary"
          size="sm"
          leading-icon="material-symbols:refresh"
          @click="refreshData"
          :loading="isLoading"
        >
          Refresh
        </UiButton>
        <UiButton
          variant="outline"
          size="sm"
          leading-icon="material-symbols:download"
          @click="exportReport"
        >
          Export
        </UiButton>
      </div>
    </div>

    <!-- Tenant Info Banner -->
    <UiCard v-if="selectedTenant" variant="outlined" class="bg-purple-50 dark:bg-purple-900/10 border-purple-200 dark:border-purple-800">
      <div class="flex items-center justify-between">
        <div class="flex items-center gap-4">
          <div class="flex items-center gap-2">
            <Icon name="material-symbols:info" class="h-5 w-5 text-purple-600 dark:text-purple-400" />
            <span class="font-medium text-purple-900 dark:text-purple-100">Tenant Overview</span>
          </div>
          <div class="flex items-center gap-4 text-sm text-purple-700 dark:text-purple-300">
            <span>Plan: {{ selectedTenant.plan }}</span>
            <span>•</span>
            <span>Users: {{ usageData.totalUsers }}</span>
            <span>•</span>
            <span>Active Since: {{ formatDate(selectedTenant.createdAt) }}</span>
          </div>
        </div>
        <div class="flex items-center gap-2">
          <UiBadge :variant="selectedTenant.isActive ? 'success' : 'warning'">
            {{ selectedTenant.isActive ? 'Active' : 'Inactive' }}
          </UiBadge>
          <UiBadge :variant="getUsageStatusVariant(usageData.usagePercentage)">
            {{ usageData.usagePercentage }}% Used
          </UiBadge>
        </div>
      </div>
    </UiCard>

    <!-- Key Metrics Overview -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <!-- Total Cases -->
      <UiCard hover-effect>
        <div class="flex items-center justify-between">
          <div class="p-3 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
            <Icon name="material-symbols:folder" class="h-6 w-6 text-blue-600 dark:text-blue-400" />
          </div>
          <div class="text-right">
            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ usageData.totalCases }}</p>
            <p class="text-sm text-gray-500 dark:text-gray-400">Total Cases</p>
          </div>
        </div>
        <div class="mt-4 flex items-center text-sm">
          <Icon
            :name="usageData.casesGrowth >= 0 ? 'material-symbols:trending-up' : 'material-symbols:trending-down'"
            :class="usageData.casesGrowth >= 0 ? 'text-green-500' : 'text-red-500'"
            class="h-4 w-4 mr-1"
          />
          <span :class="usageData.casesGrowth >= 0 ? 'text-green-600' : 'text-red-600'">
            {{ Math.abs(usageData.casesGrowth) }}%
          </span>
          <span class="text-gray-500 dark:text-gray-400 ml-1">vs last period</span>
        </div>
      </UiCard>

      <!-- Total Documents -->
      <UiCard hover-effect>
        <div class="flex items-center justify-between">
          <div class="p-3 bg-green-100 dark:bg-green-900/20 rounded-lg">
            <Icon name="material-symbols:description" class="h-6 w-6 text-green-600 dark:text-green-400" />
          </div>
          <div class="text-right">
            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ usageData.totalDocuments }}</p>
            <p class="text-sm text-gray-500 dark:text-gray-400">Documents</p>
          </div>
        </div>
        <div class="mt-4 flex items-center text-sm">
          <Icon
            :name="usageData.documentsGrowth >= 0 ? 'material-symbols:trending-up' : 'material-symbols:trending-down'"
            :class="usageData.documentsGrowth >= 0 ? 'text-green-500' : 'text-red-500'"
            class="h-4 w-4 mr-1"
          />
          <span :class="usageData.documentsGrowth >= 0 ? 'text-green-600' : 'text-red-600'">
            {{ Math.abs(usageData.documentsGrowth) }}%
          </span>
          <span class="text-gray-500 dark:text-gray-400 ml-1">vs last period</span>
        </div>
      </UiCard>

      <!-- Storage Used -->
      <UiCard hover-effect>
        <div class="flex items-center justify-between">
          <div class="p-3 bg-orange-100 dark:bg-orange-900/20 rounded-lg">
            <Icon name="material-symbols:storage" class="h-6 w-6 text-orange-600 dark:text-orange-400" />
          </div>
          <div class="text-right">
            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ formatBytes(usageData.storageUsed) }}</p>
            <p class="text-sm text-gray-500 dark:text-gray-400">Storage Used</p>
          </div>
        </div>
        <div class="mt-4">
          <div class="flex items-center justify-between text-sm mb-1">
            <span class="text-gray-500 dark:text-gray-400">{{ usageData.storagePercentage }}% of {{ formatBytes(usageData.storageLimit) }}</span>
          </div>
          <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div
              class="h-2 rounded-full transition-all duration-300"
              :class="getStorageBarColor(usageData.storagePercentage)"
              :style="{ width: `${Math.min(usageData.storagePercentage, 100)}%` }"
            ></div>
          </div>
        </div>
      </UiCard>

      <!-- Active Users -->
      <UiCard hover-effect>
        <div class="flex items-center justify-between">
          <div class="p-3 bg-purple-100 dark:bg-purple-900/20 rounded-lg">
            <Icon name="material-symbols:group" class="h-6 w-6 text-purple-600 dark:text-purple-400" />
          </div>
          <div class="text-right">
            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ usageData.activeUsers }}</p>
            <p class="text-sm text-gray-500 dark:text-gray-400">Active Users</p>
          </div>
        </div>
        <div class="mt-4 flex items-center text-sm">
          <Icon name="material-symbols:person" class="h-4 w-4 text-gray-400 mr-1" />
          <span class="text-gray-500 dark:text-gray-400">
            {{ usageData.totalUsers }} total users
          </span>
        </div>
      </UiCard>
    </div>

    <!-- Usage Charts and Analytics -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Usage Trends Chart -->
      <UiCard>
        <template #header>
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Usage Trends</h3>
            <UiSelect
              id="chartMetric"
              v-model="selectedChartMetric"
              :options="chartMetricOptions"
              size="sm"
              class="w-32"
            />
          </div>
        </template>
        <div class="h-80 flex items-center justify-center bg-gray-50 dark:bg-gray-700/50 rounded-lg">
          <div class="text-center">
            <Icon name="material-symbols:show-chart" class="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p class="text-sm text-gray-500 dark:text-gray-400">Usage trends chart would be displayed here</p>
            <p class="text-xs text-gray-400 dark:text-gray-500 mt-1">Integration with charting library needed</p>
          </div>
        </div>
      </UiCard>

      <!-- Activity Heatmap -->
      <UiCard>
        <template #header>
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Activity Heatmap</h3>
        </template>
        <div class="h-80 flex items-center justify-center bg-gray-50 dark:bg-gray-700/50 rounded-lg">
          <div class="text-center">
            <Icon name="material-symbols:calendar-view-month" class="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p class="text-sm text-gray-500 dark:text-gray-400">Activity heatmap would be displayed here</p>
            <p class="text-xs text-gray-400 dark:text-gray-500 mt-1">Shows daily activity patterns</p>
          </div>
        </div>
      </UiCard>
    </div>

    <!-- Detailed Analytics -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- Resource Usage Breakdown -->
      <div class="lg:col-span-2">
        <UiCard>
          <template #header>
            <div class="flex items-center justify-between">
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Resource Usage Breakdown</h3>
              <UiButton variant="ghost" size="sm" @click="viewDetailedBreakdown">
                View Details
              </UiButton>
            </div>
          </template>
          <div class="space-y-6">
            <!-- Cases by Status -->
            <div>
              <div class="flex items-center justify-between mb-3">
                <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300">Cases by Status</h4>
                <span class="text-sm text-gray-500 dark:text-gray-400">{{ usageData.totalCases }} total</span>
              </div>
              <div class="space-y-2">
                <div
                  v-for="status in caseStatusBreakdown"
                  :key="status.name"
                  class="flex items-center justify-between"
                >
                  <div class="flex items-center gap-3">
                    <div
                      class="w-3 h-3 rounded-full"
                      :style="{ backgroundColor: status.color }"
                    ></div>
                    <span class="text-sm text-gray-700 dark:text-gray-300">{{ status.name }}</span>
                  </div>
                  <div class="flex items-center gap-2">
                    <span class="text-sm font-medium text-gray-900 dark:text-white">{{ status.count }}</span>
                    <div class="w-20 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                      <div
                        class="h-2 rounded-full transition-all duration-300"
                        :style="{
                          width: `${(status.count / usageData.totalCases) * 100}%`,
                          backgroundColor: status.color
                        }"
                      ></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Document Types -->
            <div>
              <div class="flex items-center justify-between mb-3">
                <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300">Document Types</h4>
                <span class="text-sm text-gray-500 dark:text-gray-400">{{ usageData.totalDocuments }} total</span>
              </div>
              <div class="space-y-2">
                <div
                  v-for="docType in documentTypeBreakdown"
                  :key="docType.name"
                  class="flex items-center justify-between"
                >
                  <div class="flex items-center gap-3">
                    <Icon :name="docType.icon" class="h-4 w-4 text-gray-500" />
                    <span class="text-sm text-gray-700 dark:text-gray-300">{{ docType.name }}</span>
                  </div>
                  <div class="flex items-center gap-2">
                    <span class="text-sm font-medium text-gray-900 dark:text-white">{{ docType.count }}</span>
                    <span class="text-xs text-gray-500 dark:text-gray-400">{{ formatBytes(docType.size) }}</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- User Activity -->
            <div>
              <div class="flex items-center justify-between mb-3">
                <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300">User Activity (Last 30 Days)</h4>
                <UiButton variant="ghost" size="sm" @click="viewUserActivity">
                  View All
                </UiButton>
              </div>
              <div class="space-y-3">
                <div
                  v-for="user in topActiveUsers"
                  :key="user.id"
                  class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg"
                >
                  <div class="flex items-center gap-3">
                    <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center">
                      <Icon name="material-symbols:person" class="h-4 w-4 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div>
                      <p class="text-sm font-medium text-gray-900 dark:text-white">{{ user.name }}</p>
                      <p class="text-xs text-gray-500 dark:text-gray-400">{{ user.role }}</p>
                    </div>
                  </div>
                  <div class="text-right">
                    <p class="text-sm font-medium text-gray-900 dark:text-white">{{ user.activityScore }}</p>
                    <p class="text-xs text-gray-500 dark:text-gray-400">activity score</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </UiCard>
      </div>

      <!-- Usage Limits & Alerts -->
      <div class="space-y-6">
        <!-- Plan Limits -->
        <UiCard>
          <template #header>
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Plan Limits</h3>
          </template>
          <div class="space-y-4">
            <div
              v-for="limit in planLimits"
              :key="limit.name"
              class="space-y-2"
            >
              <div class="flex items-center justify-between">
                <span class="text-sm font-medium text-gray-700 dark:text-gray-300">{{ limit.name }}</span>
                <span class="text-sm text-gray-500 dark:text-gray-400">
                  {{ limit.used }} / {{ limit.limit === -1 ? '∞' : limit.limit }}
                </span>
              </div>
              <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div
                  class="h-2 rounded-full transition-all duration-300"
                  :class="getLimitBarColor(limit.percentage)"
                  :style="{ width: `${Math.min(limit.percentage, 100)}%` }"
                ></div>
              </div>
              <div v-if="limit.percentage > 80" class="flex items-center gap-1 text-xs text-orange-600 dark:text-orange-400">
                <Icon name="material-symbols:warning" class="h-3 w-3" />
                <span>Approaching limit</span>
              </div>
            </div>
          </div>
        </UiCard>

        <!-- Recent Activity -->
        <UiCard>
          <template #header>
            <div class="flex items-center justify-between">
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Recent Activity</h3>
              <UiButton variant="ghost" size="sm" @click="viewAllActivity">
                View All
              </UiButton>
            </div>
          </template>
          <div class="space-y-3">
            <div
              v-for="activity in recentActivity"
              :key="activity.id"
              class="flex items-start gap-3 p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg"
            >
              <div class="flex-shrink-0 mt-0.5">
                <div
                  class="w-6 h-6 rounded-full flex items-center justify-center"
                  :class="getActivityIconBg(activity.type)"
                >
                  <Icon
                    :name="getActivityIcon(activity.type)"
                    class="h-3 w-3"
                    :class="getActivityIconColor(activity.type)"
                  />
                </div>
              </div>
              <div class="flex-1 min-w-0">
                <p class="text-sm text-gray-900 dark:text-white">{{ activity.description }}</p>
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  {{ formatRelativeTime(activity.timestamp) }}
                </p>
              </div>
            </div>
          </div>
        </UiCard>

        <!-- Quick Actions -->
        <UiCard>
          <template #header>
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Quick Actions</h3>
          </template>
          <div class="space-y-3">
            <UiButton
              variant="outline"
              size="sm"
              leading-icon="material-symbols:settings"
              @click="manageTenantSettings"
              class="w-full"
            >
              Manage Settings
            </UiButton>
            <UiButton
              variant="outline"
              size="sm"
              leading-icon="material-symbols:upgrade"
              @click="upgradePlan"
              class="w-full"
            >
              Upgrade Plan
            </UiButton>
            <UiButton
              variant="outline"
              size="sm"
              leading-icon="material-symbols:support"
              @click="contactSupport"
              class="w-full"
            >
              Contact Support
            </UiButton>
          </div>
        </UiCard>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useTenantStore, type Tenant } from '~/stores/tenant'

// Define roles locally
const PlatformRoles = {
  SUPER_ADMIN: 'super_admin',
}

// Page meta
definePageMeta({
  layout: 'dashboard',
  title: 'Tenant Usage Analytics',
  description: 'Comprehensive tenant usage analytics and insights',
  pageHeaderIcon: 'material-symbols:analytics',
  showRealTimeStatus: false,
  autoRefreshEnabled: false,
  refreshInterval: 0,
  isLoading: false,
  showActionsMenu: true,
  showKeyboardShortcuts: true,
  middleware: ['rbac'],
  roles: [PlatformRoles.SUPER_ADMIN],
  breadcrumb: [
    { label: 'Dashboard', href: '/dashboard' },
    { label: 'Tenants', href: '/dashboard/tenants' },
    { label: 'Usage Analytics' },
  ],
})

// Composables
const route = useRoute()
const router = useRouter()
const tenantStore = useTenantStore()

// Get tenant ID from route
const tenantId = computed(() => route.params.id as string)

// Reactive state
const isLoading = ref(false)
const selectedTimeRange = ref('30d')
const selectedChartMetric = ref('cases')

// Time range options
const timeRangeOptions = [
  { value: '7d', label: 'Last 7 days' },
  { value: '30d', label: 'Last 30 days' },
  { value: '90d', label: 'Last 3 months' },
  { value: '1y', label: 'Last year' },
]

// Chart metric options
const chartMetricOptions = [
  { value: 'cases', label: 'Cases' },
  { value: 'documents', label: 'Documents' },
  { value: 'storage', label: 'Storage' },
  { value: 'users', label: 'Users' },
]

// Mock usage data (replace with real data from store)
const usageData = ref({
  totalCases: 156,
  totalDocuments: 1247,
  totalUsers: 12,
  activeUsers: 8,
  storageUsed: 2.4 * 1024 * 1024 * 1024, // 2.4 GB in bytes
  storageLimit: 10 * 1024 * 1024 * 1024, // 10 GB in bytes
  storagePercentage: 24,
  usagePercentage: 67,
  casesGrowth: 12.5,
  documentsGrowth: 8.3,
})

// Mock breakdown data
const caseStatusBreakdown = ref([
  { name: 'Active', count: 45, color: '#10B981' },
  { name: 'Pending', count: 32, color: '#F59E0B' },
  { name: 'Closed', count: 67, color: '#6B7280' },
  { name: 'On Hold', count: 12, color: '#EF4444' },
])

const documentTypeBreakdown = ref([
  { name: 'Contracts', count: 423, size: 856 * 1024 * 1024, icon: 'material-symbols:description' },
  { name: 'Legal Briefs', count: 234, size: 445 * 1024 * 1024, icon: 'material-symbols:gavel' },
  { name: 'Evidence', count: 345, size: 1.2 * 1024 * 1024 * 1024, icon: 'material-symbols:folder' },
  { name: 'Correspondence', count: 245, size: 123 * 1024 * 1024, icon: 'material-symbols:mail' },
])

const topActiveUsers = ref([
  { id: '1', name: 'John Doe', role: 'Senior Lawyer', activityScore: 95 },
  { id: '2', name: 'Jane Smith', role: 'Paralegal', activityScore: 87 },
  { id: '3', name: 'Bob Johnson', role: 'Associate', activityScore: 76 },
])

const planLimits = ref([
  { name: 'Cases', used: 156, limit: 500, percentage: 31.2 },
  { name: 'Users', used: 12, limit: 25, percentage: 48 },
  { name: 'Storage', used: 2.4, limit: 10, percentage: 24 },
  { name: 'API Calls', used: 8500, limit: 10000, percentage: 85 },
])

const recentActivity = ref([
  {
    id: '1',
    type: 'case_created',
    description: 'New case "Smith vs. Johnson" created by John Doe',
    timestamp: '2024-01-15T14:30:00Z',
  },
  {
    id: '2',
    type: 'document_uploaded',
    description: 'Contract document uploaded to case #156',
    timestamp: '2024-01-15T13:45:00Z',
  },
  {
    id: '3',
    type: 'user_login',
    description: 'Jane Smith logged in',
    timestamp: '2024-01-15T12:20:00Z',
  },
])

// Computed properties
const selectedTenant = computed(() => tenantStore.selectedTenant)

// Methods
const refreshData = async () => {
  isLoading.value = true
  try {
    // Refresh tenant data and usage analytics
    await tenantStore.fetchTenantById(tenantId.value)
    // Add logic to fetch usage data based on selectedTimeRange
    console.log('Refreshing data for time range:', selectedTimeRange.value)
  } catch (error) {
    console.error('Error refreshing data:', error)
  } finally {
    isLoading.value = false
  }
}

const exportReport = () => {
  // Implementation for exporting usage report
  console.log('Exporting usage report for tenant:', tenantId.value)
}

const formatBytes = (bytes: number) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString()
}

const formatRelativeTime = (dateString: string) => {
  const date = new Date(dateString)
  const now = new Date()
  const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))

  if (diffInHours < 1) return 'Just now'
  if (diffInHours < 24) return `${diffInHours}h ago`
  const diffInDays = Math.floor(diffInHours / 24)
  if (diffInDays < 7) return `${diffInDays}d ago`
  return date.toLocaleDateString()
}

const getUsageStatusVariant = (percentage: number) => {
  if (percentage >= 90) return 'danger'
  if (percentage >= 75) return 'warning'
  return 'success'
}

const getStorageBarColor = (percentage: number) => {
  if (percentage >= 90) return 'bg-red-500'
  if (percentage >= 75) return 'bg-orange-500'
  return 'bg-green-500'
}

const getLimitBarColor = (percentage: number) => {
  if (percentage >= 90) return 'bg-red-500'
  if (percentage >= 80) return 'bg-orange-500'
  return 'bg-blue-500'
}

const getActivityIcon = (type: string) => {
  const icons: Record<string, string> = {
    case_created: 'material-symbols:folder-open',
    document_uploaded: 'material-symbols:upload',
    user_login: 'material-symbols:login',
    case_updated: 'material-symbols:edit',
    document_shared: 'material-symbols:share',
  }
  return icons[type] || 'material-symbols:info'
}

const getActivityIconBg = (type: string) => {
  const backgrounds: Record<string, string> = {
    case_created: 'bg-blue-100 dark:bg-blue-900/20',
    document_uploaded: 'bg-green-100 dark:bg-green-900/20',
    user_login: 'bg-purple-100 dark:bg-purple-900/20',
    case_updated: 'bg-orange-100 dark:bg-orange-900/20',
    document_shared: 'bg-pink-100 dark:bg-pink-900/20',
  }
  return backgrounds[type] || 'bg-gray-100 dark:bg-gray-900/20'
}

const getActivityIconColor = (type: string) => {
  const colors: Record<string, string> = {
    case_created: 'text-blue-600 dark:text-blue-400',
    document_uploaded: 'text-green-600 dark:text-green-400',
    user_login: 'text-purple-600 dark:text-purple-400',
    case_updated: 'text-orange-600 dark:text-orange-400',
    document_shared: 'text-pink-600 dark:text-pink-400',
  }
  return colors[type] || 'text-gray-600 dark:text-gray-400'
}

// Navigation methods
const viewDetailedBreakdown = () => {
  router.push(`/dashboard/tenants/${tenantId.value}/analytics/breakdown`)
}

const viewUserActivity = () => {
  router.push(`/dashboard/tenants/${tenantId.value}/users/activity`)
}

const viewAllActivity = () => {
  router.push(`/dashboard/tenants/${tenantId.value}/activity`)
}

const manageTenantSettings = () => {
  router.push(`/dashboard/tenants/${tenantId.value}/edit`)
}

const upgradePlan = () => {
  router.push(`/dashboard/tenants/${tenantId.value}/upgrade`)
}

const contactSupport = () => {
  router.push('/dashboard/support')
}

// Lifecycle
onMounted(async () => {
  // Fetch tenant details if not already loaded
  if (!selectedTenant.value || selectedTenant.value.id !== tenantId.value) {
    await tenantStore.fetchTenantById(tenantId.value)
  }

  // Load usage data
  await refreshData()
})

// Watch for time range changes
watch(selectedTimeRange, () => {
  refreshData()
})

// Watch for chart metric changes
watch(selectedChartMetric, () => {
  // Update chart data based on selected metric
  console.log('Chart metric changed to:', selectedChartMetric.value)
})
</script>