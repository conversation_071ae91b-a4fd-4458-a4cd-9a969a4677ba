/**
 * Authentication Constants
 * 
 * Central location for all authentication-related constants,
 * configuration values, and enums
 */

// Authentication Routes
export const AUTH_ROUTES = {
  LOGIN: '/auth/login',
  REGISTER: '/auth/register',
  FORGOT_PASSWORD: '/auth/forgot-password',
  RESET_PASSWORD: '/auth/reset-password',
  VERIFY_EMAIL: '/auth/verify-email',
  TWO_FACTOR: '/auth/2fa',
  LOGOUT: '/auth/logout'
} as const

// Protected Routes
export const PROTECTED_ROUTES = {
  DASHBOARD: '/dashboard',
  PROFILE: '/dashboard/profile',
  SETTINGS: '/dashboard/settings',
  PLATFORM: '/dashboard/platform'
} as const

// Session Configuration
export const SESSION_CONFIG = {
  ACCESS_TOKEN_KEY: 'access_token',
  REFRESH_TOKEN_KEY: 'refresh_token',
  USER_KEY: 'user_data',
  SESSION_KEY: 'auth_session',
  EXPIRES_AT_KEY: 'expires_at',
  
  // Token expiration times (in milliseconds)
  ACCESS_TOKEN_LIFETIME: 15 * 60 * 1000, // 15 minutes
  REFRESH_TOKEN_LIFETIME: 7 * 24 * 60 * 60 * 1000, // 7 days
  REMEMBER_ME_LIFETIME: 30 * 24 * 60 * 60 * 1000, // 30 days
  
  // Auto-refresh timing
  REFRESH_THRESHOLD: 5 * 60 * 1000, // Refresh 5 minutes before expiry
  REFRESH_RETRY_DELAY: 1000, // 1 second
  MAX_REFRESH_RETRIES: 3
} as const

// Authentication States
export enum AuthState {
  IDLE = 'idle',
  LOADING = 'loading',
  AUTHENTICATED = 'authenticated',
  UNAUTHENTICATED = 'unauthenticated',
  ERROR = 'error',
  EXPIRED = 'expired'
}

// Login Methods
export enum LoginMethod {
  EMAIL_PASSWORD = 'email_password',
  GOOGLE = 'google',
  MICROSOFT = 'microsoft',
  APPLE = 'apple',
  SAML = 'saml'
}

// Two-Factor Authentication
export const TWO_FACTOR_CONFIG = {
  TOKEN_LENGTH: 6,
  BACKUP_CODES_COUNT: 10,
  BACKUP_CODE_LENGTH: 8,
  QR_CODE_SIZE: 200,
  ISSUER_NAME: 'Legal SaaS',
  ALGORITHM: 'SHA1',
  DIGITS: 6,
  PERIOD: 30
} as const

// Password Requirements
export const PASSWORD_REQUIREMENTS = {
  MIN_LENGTH: 8,
  MAX_LENGTH: 128,
  REQUIRE_UPPERCASE: true,
  REQUIRE_LOWERCASE: true,
  REQUIRE_NUMBERS: true,
  REQUIRE_SPECIAL_CHARS: true,
  SPECIAL_CHARS: '!@#$%^&*(),.?":{}|<>',
  
  // Password strength levels
  STRENGTH_LEVELS: {
    WEAK: 'weak',
    FAIR: 'fair',
    GOOD: 'good',
    STRONG: 'strong'
  }
} as const

// Account Status
export enum AccountStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended',
  PENDING_VERIFICATION = 'pending_verification',
  LOCKED = 'locked',
  DELETED = 'deleted'
}

// Email Verification
export const EMAIL_VERIFICATION = {
  TOKEN_EXPIRY: 24 * 60 * 60 * 1000, // 24 hours
  RESEND_COOLDOWN: 60 * 1000, // 1 minute
  MAX_ATTEMPTS: 5
} as const

// Password Reset
export const PASSWORD_RESET = {
  TOKEN_EXPIRY: 60 * 60 * 1000, // 1 hour
  RESEND_COOLDOWN: 60 * 1000, // 1 minute
  MAX_ATTEMPTS: 3
} as const

// Rate Limiting
export const RATE_LIMITS = {
  LOGIN_ATTEMPTS: {
    MAX_ATTEMPTS: 5,
    WINDOW: 15 * 60 * 1000, // 15 minutes
    LOCKOUT_DURATION: 30 * 60 * 1000 // 30 minutes
  },
  PASSWORD_RESET: {
    MAX_ATTEMPTS: 3,
    WINDOW: 60 * 60 * 1000, // 1 hour
    COOLDOWN: 5 * 60 * 1000 // 5 minutes
  },
  EMAIL_VERIFICATION: {
    MAX_ATTEMPTS: 5,
    WINDOW: 60 * 60 * 1000, // 1 hour
    COOLDOWN: 60 * 1000 // 1 minute
  }
} as const

// Error Codes
export enum AuthErrorCode {
  INVALID_CREDENTIALS = 'INVALID_CREDENTIALS',
  ACCOUNT_LOCKED = 'ACCOUNT_LOCKED',
  ACCOUNT_SUSPENDED = 'ACCOUNT_SUSPENDED',
  EMAIL_NOT_VERIFIED = 'EMAIL_NOT_VERIFIED',
  TWO_FACTOR_REQUIRED = 'TWO_FACTOR_REQUIRED',
  INVALID_TWO_FACTOR = 'INVALID_TWO_FACTOR',
  TOKEN_EXPIRED = 'TOKEN_EXPIRED',
  TOKEN_INVALID = 'TOKEN_INVALID',
  RATE_LIMITED = 'RATE_LIMITED',
  NETWORK_ERROR = 'NETWORK_ERROR',
  SERVER_ERROR = 'SERVER_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR'
}

// Error Messages
export const AUTH_ERROR_MESSAGES = {
  [AuthErrorCode.INVALID_CREDENTIALS]: 'Invalid email or password',
  [AuthErrorCode.ACCOUNT_LOCKED]: 'Account is locked due to too many failed login attempts',
  [AuthErrorCode.ACCOUNT_SUSPENDED]: 'Account has been suspended. Please contact support',
  [AuthErrorCode.EMAIL_NOT_VERIFIED]: 'Please verify your email address before logging in',
  [AuthErrorCode.TWO_FACTOR_REQUIRED]: 'Two-factor authentication is required',
  [AuthErrorCode.INVALID_TWO_FACTOR]: 'Invalid two-factor authentication code',
  [AuthErrorCode.TOKEN_EXPIRED]: 'Your session has expired. Please log in again',
  [AuthErrorCode.TOKEN_INVALID]: 'Invalid authentication token',
  [AuthErrorCode.RATE_LIMITED]: 'Too many attempts. Please try again later',
  [AuthErrorCode.NETWORK_ERROR]: 'Network error. Please check your connection',
  [AuthErrorCode.SERVER_ERROR]: 'Server error. Please try again later',
  [AuthErrorCode.VALIDATION_ERROR]: 'Please check your input and try again'
} as const

// Success Messages
export const AUTH_SUCCESS_MESSAGES = {
  LOGIN_SUCCESS: 'Successfully logged in',
  LOGOUT_SUCCESS: 'Successfully logged out',
  REGISTRATION_SUCCESS: 'Account created successfully',
  EMAIL_VERIFIED: 'Email verified successfully',
  PASSWORD_CHANGED: 'Password changed successfully',
  PASSWORD_RESET_SENT: 'Password reset email sent',
  PASSWORD_RESET_SUCCESS: 'Password reset successfully',
  TWO_FACTOR_ENABLED: 'Two-factor authentication enabled',
  TWO_FACTOR_DISABLED: 'Two-factor authentication disabled',
  PROFILE_UPDATED: 'Profile updated successfully'
} as const

// Local Storage Keys
export const STORAGE_KEYS = {
  AUTH_TOKEN: 'legal_saas_auth_token',
  REFRESH_TOKEN: 'legal_saas_refresh_token',
  USER_DATA: 'legal_saas_user_data',
  SESSION_DATA: 'legal_saas_session_data',
  REMEMBER_ME: 'legal_saas_remember_me',
  LAST_LOGIN: 'legal_saas_last_login',
  THEME_PREFERENCE: 'legal_saas_theme',
  LANGUAGE_PREFERENCE: 'legal_saas_language'
} as const

// API Endpoints
export const AUTH_ENDPOINTS = {
  LOGIN: '/api/v1/auth/login',
  REGISTER: '/api/v1/auth/register',
  LOGOUT: '/api/v1/auth/logout',
  REFRESH: '/api/v1/auth/refresh',
  PROFILE: '/api/v1/auth/profile',
  CHANGE_PASSWORD: '/api/v1/auth/change-password',
  FORGOT_PASSWORD: '/api/v1/auth/forgot-password',
  RESET_PASSWORD: '/api/v1/auth/reset-password',
  VERIFY_EMAIL: '/api/v1/auth/verify-email',
  RESEND_VERIFICATION: '/api/v1/auth/resend-verification',
  ENABLE_2FA: '/api/v1/auth/2fa/enable',
  VERIFY_2FA: '/api/v1/auth/2fa/verify',
  DISABLE_2FA: '/api/v1/auth/2fa/disable',
  BACKUP_CODES: '/api/v1/auth/2fa/backup-codes',
  SESSIONS: '/api/v1/auth/sessions'
} as const

// Feature Flags
export const AUTH_FEATURES = {
  SOCIAL_LOGIN: true,
  TWO_FACTOR_AUTH: true,
  EMAIL_VERIFICATION: true,
  PASSWORD_RESET: true,
  REMEMBER_ME: true,
  SESSION_MANAGEMENT: true,
  RATE_LIMITING: true
} as const

// Validation Patterns
export const VALIDATION_PATTERNS = {
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  PHONE: /^[\+]?[1-9][\d]{0,15}$/,
  PASSWORD_STRONG: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*(),.?":{}|<>])[A-Za-z\d!@#$%^&*(),.?":{}|<>]{8,}$/,
  TWO_FACTOR_TOKEN: /^\d{6}$/,
  BACKUP_CODE: /^[A-Z0-9]{8}$/
} as const
