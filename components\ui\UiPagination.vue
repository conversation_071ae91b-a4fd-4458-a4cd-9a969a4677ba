<template>
  <div class="flex items-center justify-between p-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
    <!-- Enhanced Mobile Pagination -->
    <div class="flex-1 flex justify-between items-center sm:hidden">
      <UiButton
        @click="goToPrevious"
        :disabled="currentPage <= 1 || loading"
        :loading="loading && direction === 'prev'"
        variant="outline"
        size="sm"
        :ripple="true"
        class="transition-all duration-200 hover:scale-105 active:scale-95"
      >
        <Icon name="material-symbols:chevron-left" class="h-4 w-4 mr-1" />
        Previous
      </UiButton>

      <!-- Mobile Page Indicator -->
      <div class="flex items-center space-x-2">
        <span class="text-sm font-medium text-gray-700 dark:text-gray-300">
          {{ currentPage }} of {{ totalPages }}
        </span>
        <UiSpinner v-if="loading" size="xs" />
      </div>

      <UiButton
        @click="goToNext"
        :disabled="currentPage >= totalPages || loading"
        :loading="loading && direction === 'next'"
        variant="outline"
        size="sm"
        :ripple="true"
        class="transition-all duration-200 hover:scale-105 active:scale-95"
      >
        Next
        <Icon name="material-symbols:chevron-right" class="h-4 w-4 ml-1" />
      </UiButton>
    </div>

    <!-- Enhanced Desktop Pagination -->
    <div class="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
      <!-- Enhanced Results Info -->
      <div class="flex items-center space-x-3">
        <div class="flex items-center space-x-2">
          <UiSpinner v-if="loading" size="xs" color="primary" />
          <p class="text-sm text-gray-700 dark:text-gray-300 transition-colors duration-200">
            <span v-if="!loading">
              Showing
              <span class="font-semibold text-brandPrimary">{{ startItem.toLocaleString() }}</span>
              to
              <span class="font-semibold text-brandPrimary">{{ endItem.toLocaleString() }}</span>
              of
              <span class="font-semibold text-brandPrimary">{{ totalItems.toLocaleString() }}</span>
              results
            </span>
            <span v-else class="text-gray-500">
              Loading results...
            </span>
          </p>
        </div>

        <!-- Quick Stats -->
        <div v-if="showStats && !loading" class="hidden lg:flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400">
          <div class="flex items-center space-x-1">
            <Icon name="material-symbols:speed" class="h-3 w-3" />
            <span>{{ loadTime }}ms</span>
          </div>
          <div class="flex items-center space-x-1">
            <Icon name="material-symbols:database" class="h-3 w-3" />
            <span>{{ Math.ceil(totalItems / itemsPerPage) }} pages</span>
          </div>
        </div>
      </div>

      <!-- Enhanced Pagination Controls -->
      <div class="flex items-center space-x-3">
        <!-- Enhanced Items per page selector -->
        <div v-if="showItemsPerPage" class="flex items-center space-x-2 mr-4">
          <label class="text-sm font-medium text-gray-700 dark:text-gray-300">Show:</label>
          <UiSelect
            id="itemsPerPage"
            :model-value="itemsPerPage"
            @update:model-value="handleItemsPerPageChange"
            :options="itemsPerPageOptions"
            :disabled="loading"
            size="sm"
            class="w-24 transition-all duration-200 hover:shadow-md focus:shadow-lg"
          />
          <span class="text-xs text-gray-500 dark:text-gray-400">per page</span>
        </div>

        <!-- Enhanced First page button -->
        <UiButton
          v-if="showFirstLast && currentPage > 2"
          @click="goToPage(1)"
          :disabled="loading"
          :loading="loading && direction === 'first'"
          variant="outline"
          size="sm"
          :ripple="true"
          class="hidden md:inline-flex transition-all duration-200 hover:scale-105 active:scale-95 hover:shadow-md"
          aria-label="Go to first page"
        >
          <Icon name="material-symbols:keyboard-double-arrow-left" class="h-4 w-4" />
        </UiButton>

        <!-- Enhanced Previous button -->
        <UiButton
          @click="goToPrevious"
          :disabled="currentPage <= 1 || loading"
          :loading="loading && direction === 'prev'"
          variant="outline"
          size="sm"
          :ripple="true"
          class="transition-all duration-200 hover:scale-105 active:scale-95 hover:shadow-md"
          aria-label="Go to previous page"
        >
          <Icon name="material-symbols:chevron-left" class="h-4 w-4" />
          <span class="hidden sm:inline ml-1">Previous</span>
        </UiButton>

        <!-- Enhanced Page numbers -->
        <nav class="relative z-0 inline-flex rounded-lg shadow-sm overflow-hidden" aria-label="Pagination">
          <template v-for="(page, index) in visiblePages" :key="`page-${page}-${index}`">
            <!-- Enhanced Page number button -->
            <button
              v-if="typeof page === 'number'"
              @click="goToPage(page)"
              :disabled="loading"
              :class="[
                'relative inline-flex items-center px-4 py-2 text-sm font-semibold transition-all duration-200 border-r border-gray-200 dark:border-gray-600 last:border-r-0',
                'hover:scale-105 active:scale-95 focus:outline-none focus:ring-2 focus:ring-brandPrimary focus:ring-offset-2',
                page === currentPage
                  ? 'z-10 bg-gradient-to-r from-brandPrimary to-brandPrimary-600 text-white shadow-md transform scale-105'
                  : 'bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gradient-to-r hover:from-gray-50 hover:to-gray-100 dark:hover:from-gray-700 dark:hover:to-gray-600 hover:text-brandPrimary hover:shadow-md',
                loading ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'
              ]"
              :aria-current="page === currentPage ? 'page' : undefined"
              :aria-label="`Go to page ${page}`"
            >
              <span class="relative">
                {{ page }}
                <!-- Active page indicator -->
                <span
                  v-if="page === currentPage"
                  class="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-white rounded-full animate-pulse"
                />
              </span>
            </button>

            <!-- Enhanced Ellipsis -->
            <div
              v-else
              class="relative inline-flex items-center px-4 py-2 text-sm font-medium text-gray-400 dark:text-gray-500 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-600 last:border-r-0"
            >
              <div class="flex space-x-1">
                <div class="w-1 h-1 bg-current rounded-full animate-bounce" style="animation-delay: 0ms"></div>
                <div class="w-1 h-1 bg-current rounded-full animate-bounce" style="animation-delay: 150ms"></div>
                <div class="w-1 h-1 bg-current rounded-full animate-bounce" style="animation-delay: 300ms"></div>
              </div>
            </div>
          </template>
        </nav>

        <!-- Next button -->
        <UiButton
          @click="goToNext"
          :disabled="currentPage >= totalPages"
          variant="outline"
          size="sm"
        >
          <span class="hidden sm:inline mr-1">Next</span>
          <Icon name="material-symbols:chevron-right" class="h-4 w-4" />
        </UiButton>

        <!-- Last page button -->
        <UiButton
          v-if="showFirstLast && currentPage < totalPages - 1"
          @click="goToPage(totalPages)"
          variant="outline"
          size="sm"
          class="hidden md:inline-flex"
        >
          <Icon name="material-symbols:keyboard-double-arrow-right" class="h-4 w-4" />
        </UiButton>

        <!-- Jump to page input -->
        <div v-if="showJumpTo && totalPages > 10" class="flex items-center space-x-2 ml-4">
          <label class="text-sm text-gray-700 dark:text-gray-300">Go to:</label>
          <UiInput
            v-model="jumpToPage"
            @keyup.enter="handleJumpToPage"
            type="number"
            :min="1"
            :max="totalPages"
            size="sm"
            class="w-16"
            placeholder="Page"
          />
          <UiButton
            @click="handleJumpToPage"
            variant="outline"
            size="sm"
          >
            Go
          </UiButton>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'

interface Props {
  currentPage: number
  totalPages: number
  totalItems: number
  itemsPerPage: number
  showItemsPerPage?: boolean
  showFirstLast?: boolean
  showJumpTo?: boolean
  showStats?: boolean
  maxVisiblePages?: number
  itemsPerPageOptions?: Array<{ value: number; label: string }>
  loading?: boolean
  loadTime?: number
  compact?: boolean
  animated?: boolean
}

interface Emits {
  (e: 'page-change', page: number): void
  (e: 'items-per-page-change', itemsPerPage: number): void
  (e: 'loading-start'): void
  (e: 'loading-end'): void
}

const props = withDefaults(defineProps<Props>(), {
  showItemsPerPage: true,
  showFirstLast: true,
  showJumpTo: true,
  showStats: false,
  maxVisiblePages: 7,
  loading: false,
  loadTime: 0,
  compact: false,
  animated: true,
  itemsPerPageOptions: () => [
    { value: 10, label: '10' },
    { value: 20, label: '20' },
    { value: 50, label: '50' },
    { value: 100, label: '100' }
  ]
})

const emit = defineEmits<Emits>()

// Enhanced State
const jumpToPage = ref<number | null>(null)
const direction = ref<'prev' | 'next' | 'first' | 'last' | null>(null)

// Computed properties
const startItem = computed(() => {
  return Math.min((props.currentPage - 1) * props.itemsPerPage + 1, props.totalItems)
})

const endItem = computed(() => {
  return Math.min(props.currentPage * props.itemsPerPage, props.totalItems)
})

const visiblePages = computed(() => {
  const pages: (number | string)[] = []
  const maxVisible = props.maxVisiblePages
  const current = props.currentPage
  const total = props.totalPages

  if (total <= maxVisible) {
    // Show all pages if total is less than max visible
    for (let i = 1; i <= total; i++) {
      pages.push(i)
    }
  } else {
    // Always show first page
    pages.push(1)

    // Calculate start and end of visible range
    let start = Math.max(2, current - Math.floor((maxVisible - 3) / 2))
    let end = Math.min(total - 1, start + maxVisible - 4)

    // Adjust start if end is at the limit
    if (end === total - 1) {
      start = Math.max(2, end - maxVisible + 4)
    }

    // Add ellipsis before start if needed
    if (start > 2) {
      pages.push('...')
    }

    // Add visible page numbers
    for (let i = start; i <= end; i++) {
      pages.push(i)
    }

    // Add ellipsis after end if needed
    if (end < total - 1) {
      pages.push('...')
    }

    // Always show last page if total > 1
    if (total > 1) {
      pages.push(total)
    }
  }

  return pages
})

// Enhanced Methods
const goToPage = (page: number) => {
  if (page >= 1 && page <= props.totalPages && page !== props.currentPage && !props.loading) {
    // Determine direction for loading state
    if (page === 1) direction.value = 'first'
    else if (page === props.totalPages) direction.value = 'last'
    else if (page < props.currentPage) direction.value = 'prev'
    else direction.value = 'next'

    emit('loading-start')
    emit('page-change', page)

    // Reset direction after a short delay
    setTimeout(() => {
      direction.value = null
    }, 300)
  }
}

const goToPrevious = () => {
  if (props.currentPage > 1 && !props.loading) {
    direction.value = 'prev'
    goToPage(props.currentPage - 1)
  }
}

const goToNext = () => {
  if (props.currentPage < props.totalPages && !props.loading) {
    direction.value = 'next'
    goToPage(props.currentPage + 1)
  }
}

const handleItemsPerPageChange = (newItemsPerPage: number) => {
  emit('items-per-page-change', newItemsPerPage)
}

const handleJumpToPage = () => {
  if (jumpToPage.value && jumpToPage.value >= 1 && jumpToPage.value <= props.totalPages) {
    goToPage(jumpToPage.value)
    jumpToPage.value = null
  }
}

// Watch for current page changes to reset jump input
watch(() => props.currentPage, () => {
  jumpToPage.value = null
})
</script>

<style scoped>
/* Enhanced pagination styles */
.pagination-button {
  position: relative;
  display: inline-flex;
  align-items: center;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.pagination-button:first-child {
  border-top-left-radius: 0.375rem;
  border-bottom-left-radius: 0.375rem;
}

.pagination-button:last-child {
  border-top-right-radius: 0.375rem;
  border-bottom-right-radius: 0.375rem;
}

.pagination-button:not(:first-child):not(:last-child) {
  margin-left: -1px;
}

/* Enhanced focus styles */
.pagination-button:focus {
  outline: none;
  box-shadow: 0 0 0 2px var(--color-brandPrimary), 0 0 0 4px rgba(var(--color-brandPrimary-rgb), 0.2);
}

/* Enhanced hover animations */
.pagination-button:hover:not(:disabled) {
  transform: scale(1.05);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.pagination-button:active:not(:disabled) {
  transform: scale(0.95);
}

/* Loading state animations */
@keyframes pulse-loading {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.loading-pulse {
  animation: pulse-loading 1.5s ease-in-out infinite;
}

/* Enhanced bounce animation for ellipsis */
@keyframes bounce-dot {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.3;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.animate-bounce {
  animation: bounce-dot 1.4s ease-in-out infinite both;
}

/* Smooth transitions for all interactive elements */
button, select, input {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced gradient effects */
.bg-gradient-to-r {
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .pagination-button {
    padding: 0.375rem 0.75rem;
    font-size: 0.8125rem;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .pagination-button,
  .animate-bounce,
  .loading-pulse {
    animation: none;
    transition: none;
  }

  .pagination-button:hover:not(:disabled),
  .pagination-button:active:not(:disabled) {
    transform: none;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .pagination-button {
    border-width: 2px;
  }

  .pagination-button:focus {
    outline: 3px solid;
    outline-offset: 2px;
  }
}

/* Dark mode enhancements */
@media (prefers-color-scheme: dark) {
  .pagination-button:focus {
    box-shadow: 0 0 0 2px var(--color-brandPrimary), 0 0 0 4px rgba(var(--color-brandPrimary-rgb), 0.3);
  }
}

/* Print styles */
@media print {
  .pagination-button {
    box-shadow: none;
    transform: none;
  }
}
</style>
