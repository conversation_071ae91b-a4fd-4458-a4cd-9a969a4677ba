<template>
  <div
    :class="[
      'inline-flex items-center justify-center',
      containerClasses
    ]"
    role="status"
    v-bind="{
      ...(ariaLabel ? { 'aria-label': ariaLabel } : {}),
      ...(ariaLive ? { 'aria-live': ariaLive } : {})
    }"
  >
    <!-- Circular Spinner (default) -->
    <svg
      v-if="variant === 'circular'"
      :class="[
        animationClasses,
        sizeClasses,
        colorClasses
      ]"
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
    >
      <circle
        class="opacity-25"
        cx="12"
        cy="12"
        r="10"
        stroke="currentColor"
        :stroke-width="strokeWidth"
      />
      <path
        class="opacity-75"
        fill="currentColor"
        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
      />
    </svg>

    <!-- Dots Spinner -->
    <div
      v-else-if="variant === 'dots'"
      :class="[
        'flex space-x-1',
        sizeClasses
      ]"
    >
      <div
        v-for="i in 3"
        :key="i"
        :class="[
          'rounded-full animate-bounce',
          dotSizeClasses,
          colorClasses
        ]"
        :style="{ animationDelay: `${(i - 1) * 0.1}s` }"
      />
    </div>

    <!-- Pulse Spinner -->
    <div
      v-else-if="variant === 'pulse'"
      :class="[
        'rounded-full animate-ping',
        sizeClasses,
        colorClasses
      ]"
    />

    <!-- Bars Spinner -->
    <div
      v-else-if="variant === 'bars'"
      :class="[
        'flex space-x-1 items-end',
        sizeClasses
      ]"
    >
      <div
        v-for="i in 4"
        :key="i"
        :class="[
          'animate-pulse',
          barSizeClasses,
          colorClasses
        ]"
        :style="{
          animationDelay: `${(i - 1) * 0.15}s`,
          height: `${60 + (i % 2) * 40}%`
        }"
      />
    </div>

    <!-- Ring Spinner -->
    <div
      v-else-if="variant === 'ring'"
      :class="[
        'animate-spin rounded-full border-4 border-solid border-current border-r-transparent',
        sizeClasses,
        colorClasses
      ]"
    />

    <!-- Grid Spinner -->
    <div
      v-else-if="variant === 'grid'"
      :class="[
        'grid grid-cols-3 gap-1',
        sizeClasses
      ]"
    >
      <div
        v-for="i in 9"
        :key="i"
        :class="[
          'rounded-sm animate-pulse',
          gridDotClasses,
          colorClasses
        ]"
        :style="{ animationDelay: `${(i - 1) * 0.1}s` }"
      />
    </div>

    <!-- Text Label -->
    <span
      v-if="showLabel && label"
      :class="[
        'ml-3 text-sm font-medium',
        textColorClasses
      ]"
    >
      {{ label }}
    </span>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

// Enhanced Props Interface
interface Props {
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl'
  variant?: 'circular' | 'dots' | 'pulse' | 'bars' | 'ring' | 'grid'
  color?: 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info' | 'gray' | 'current'
  speed?: 'slow' | 'normal' | 'fast'
  label?: string
  showLabel?: boolean
  thickness?: 'thin' | 'normal' | 'thick'
  ariaLive?: 'polite' | 'assertive' | 'off'
}

const props = withDefaults(defineProps<Props>(), {
  size: 'md',
  variant: 'circular',
  color: 'current',
  speed: 'normal',
  label: 'Loading...',
  showLabel: false,
  thickness: 'normal',
  ariaLive: 'polite'
})

// Enhanced Computed Properties
const sizeClasses = computed(() => {
  const sizeMap = {
    xs: 'h-3 w-3',
    sm: 'h-4 w-4',
    md: 'h-6 w-6',
    lg: 'h-8 w-8',
    xl: 'h-12 w-12',
    '2xl': 'h-16 w-16'
  }
  return sizeMap[props.size]
})

const dotSizeClasses = computed(() => {
  const dotSizeMap = {
    xs: 'h-1 w-1',
    sm: 'h-1.5 w-1.5',
    md: 'h-2 w-2',
    lg: 'h-2.5 w-2.5',
    xl: 'h-3 w-3',
    '2xl': 'h-4 w-4'
  }
  return dotSizeMap[props.size]
})

const barSizeClasses = computed(() => {
  const barSizeMap = {
    xs: 'w-0.5',
    sm: 'w-1',
    md: 'w-1.5',
    lg: 'w-2',
    xl: 'w-2.5',
    '2xl': 'w-3'
  }
  return barSizeMap[props.size]
})

const gridDotClasses = computed(() => {
  const gridDotMap = {
    xs: 'h-0.5 w-0.5',
    sm: 'h-1 w-1',
    md: 'h-1.5 w-1.5',
    lg: 'h-2 w-2',
    xl: 'h-2.5 w-2.5',
    '2xl': 'h-3 w-3'
  }
  return gridDotMap[props.size]
})

const strokeWidth = computed(() => {
  const thicknessMap = {
    thin: '2',
    normal: '4',
    thick: '6'
  }
  return thicknessMap[props.thickness]
})

const colorClasses = computed(() => {
  const colorMap = {
    primary: 'text-brandPrimary',
    secondary: 'text-brandSecondary',
    success: 'text-brandSuccess',
    warning: 'text-brandWarning-500',
    error: 'text-brandDanger',
    info: 'text-brandInfo',
    gray: 'text-gray-500',
    current: 'text-current'
  }
  return colorMap[props.color]
})

const textColorClasses = computed(() => {
  const textColorMap = {
    primary: 'text-brandPrimary',
    secondary: 'text-brandSecondary',
    success: 'text-brandSuccess',
    warning: 'text-brandWarning-600',
    error: 'text-brandDanger',
    info: 'text-brandInfo',
    gray: 'text-gray-600',
    current: 'text-gray-600 dark:text-gray-400'
  }
  return textColorMap[props.color]
})

const animationClasses = computed(() => {
  const speedMap = {
    slow: 'animate-spin-slow',
    normal: 'animate-spin',
    fast: 'animate-spin-fast'
  }
  return speedMap[props.speed]
})

const containerClasses = computed(() => {
  return props.showLabel ? 'flex-row' : ''
})

const ariaLabel = computed(() => {
  return props.label
})
</script>

<style scoped>
/* Enhanced spinner animations */
@keyframes spin-slow {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes spin-fast {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes bounce-dots {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes pulse-bars {
  0%, 100% {
    transform: scaleY(0.4);
    opacity: 0.5;
  }
  50% {
    transform: scaleY(1);
    opacity: 1;
  }
}

@keyframes fade-grid {
  0%, 70%, 100% {
    opacity: 0.3;
    transform: scale(0.8);
  }
  35% {
    opacity: 1;
    transform: scale(1);
  }
}

/* Animation classes */
.animate-spin-slow {
  animation: spin-slow 2s linear infinite;
}

.animate-spin-fast {
  animation: spin-fast 0.5s linear infinite;
}

/* Enhanced dot animations */
.animate-bounce {
  animation: bounce-dots 1.4s ease-in-out infinite both;
}

/* Enhanced bar animations */
.animate-pulse {
  animation: pulse-bars 1.2s ease-in-out infinite;
}

/* Enhanced grid animations */
.grid .animate-pulse {
  animation: fade-grid 1.5s ease-in-out infinite;
}

/* Smooth color transitions */
.text-brandPrimary,
.text-brandSecondary,
.text-brandSuccess,
.text-brandWarning-500,
.text-brandDanger,
.text-brandInfo {
  transition: color 0.2s ease-in-out;
}

/* Enhanced accessibility */
@media (prefers-reduced-motion: reduce) {
  .animate-spin,
  .animate-spin-slow,
  .animate-spin-fast,
  .animate-bounce,
  .animate-pulse,
  .animate-ping {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .opacity-25 {
    opacity: 0.1;
  }

  .opacity-75 {
    opacity: 0.9;
  }
}

/* Dark mode enhancements */
@media (prefers-color-scheme: dark) {
  .text-current {
    color: rgb(156, 163, 175);
  }
}

/* Focus states for accessibility */
div[role="status"]:focus-visible {
  outline: 2px solid var(--color-brandPrimary);
  outline-offset: 2px;
  border-radius: 4px;
}

/* Loading state overlay support */
.loading-overlay .animate-spin,
.loading-overlay .animate-bounce,
.loading-overlay .animate-pulse {
  filter: drop-shadow(0 0 4px rgba(0, 0, 0, 0.1));
}

/* Size-specific enhancements */
.h-3.w-3 {
  min-width: 0.75rem;
  min-height: 0.75rem;
}

.h-16.w-16 {
  min-width: 4rem;
  min-height: 4rem;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .h-12.w-12 {
    height: 2.5rem;
    width: 2.5rem;
  }

  .h-16.w-16 {
    height: 3rem;
    width: 3rem;
  }
}
</style>

<style scoped>
/* Ensure smooth animation */
svg {
  animation-duration: 1s;
  animation-timing-function: linear;
  animation-iteration-count: infinite;
}

/* Reduce motion for accessibility */
@media (prefers-reduced-motion: reduce) {
  svg {
    animation: none;
  }

  /* Show a static loading indicator instead */
  svg::after {
    content: '⏳';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}
</style>