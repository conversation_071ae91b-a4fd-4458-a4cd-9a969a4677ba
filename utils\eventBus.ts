/**
 * Global Event Bus for the Legal SaaS Frontend
 * Provides centralized event management using mitt with enhanced features
 */

import mitt, { type Emitter } from 'mitt'
import type { 
  EventMap, 
  EventListener, 
  EventUnsubscribe, 
  EventListenerOptions,
  EventEmitter as IEventEmitter,
  BaseEvent
} from '~/types/events'

// ============================================================================
// ENHANCED EVENT EMITTER CLASS
// ============================================================================

class EventEmitter implements IEventEmitter {
  private emitter: Emitter<EventMap>
  private listenerMap = new Map<string, Set<EventListener>>()
  private onceListeners = new Set<EventListener>()
  private debugMode = false

  constructor(debug = false) {
    this.emitter = mitt<EventMap>()
    this.debugMode = debug
  }

  /**
   * Emit an event with data
   */
  emit<K extends keyof EventMap>(event: K, data: EventMap[K]): void {
    // Add timestamp if not present
    if (!data.timestamp) {
      (data as any).timestamp = new Date().toISOString()
    }

    // Add session info if available
    if (typeof window !== 'undefined') {
      const sessionId = sessionStorage.getItem('sessionId')
      if (sessionId && !data.sessionId) {
        (data as any).sessionId = sessionId
      }
    }

    if (this.debugMode) {
      console.log(`🔔 Event emitted: ${String(event)}`, data)
    }

    this.emitter.emit(event, data)
  }

  /**
   * Listen to an event with options
   */
  on<K extends keyof EventMap>(
    event: K, 
    listener: EventListener<EventMap[K]>, 
    options: EventListenerOptions = {}
  ): EventUnsubscribe {
    let wrappedListener = listener

    // Handle once option
    if (options.once) {
      wrappedListener = (data: EventMap[K]) => {
        listener(data)
        this.off(event, wrappedListener)
        this.onceListeners.delete(wrappedListener)
      }
      this.onceListeners.add(wrappedListener)
    }

    // Handle debounce option
    if (options.debounce && options.debounce > 0) {
      wrappedListener = this.debounce(wrappedListener, options.debounce)
    }

    // Handle throttle option
    if (options.throttle && options.throttle > 0) {
      wrappedListener = this.throttle(wrappedListener, options.throttle)
    }

    // Store listener reference
    const eventKey = String(event)
    if (!this.listenerMap.has(eventKey)) {
      this.listenerMap.set(eventKey, new Set())
    }
    this.listenerMap.get(eventKey)!.add(wrappedListener)

    // Register with mitt
    this.emitter.on(event, wrappedListener)

    if (this.debugMode) {
      console.log(`👂 Listener added for: ${eventKey}`)
    }

    // Return unsubscribe function
    return () => {
      this.off(event, wrappedListener)
    }
  }

  /**
   * Remove event listener(s)
   */
  off<K extends keyof EventMap>(event: K, listener?: EventListener<EventMap[K]>): void {
    const eventKey = String(event)

    if (listener) {
      // Remove specific listener
      this.emitter.off(event, listener)
      this.listenerMap.get(eventKey)?.delete(listener)
      this.onceListeners.delete(listener)
    } else {
      // Remove all listeners for event
      this.emitter.off(event)
      this.listenerMap.delete(eventKey)
    }

    if (this.debugMode) {
      console.log(`🔇 Listener removed for: ${eventKey}`)
    }
  }

  /**
   * Listen to an event once
   */
  once<K extends keyof EventMap>(
    event: K, 
    listener: EventListener<EventMap[K]>
  ): EventUnsubscribe {
    return this.on(event, listener, { once: true })
  }

  /**
   * Clear all listeners
   */
  clear(): void {
    this.emitter.all.clear()
    this.listenerMap.clear()
    this.onceListeners.clear()

    if (this.debugMode) {
      console.log('🧹 All event listeners cleared')
    }
  }

  /**
   * Get listener count for an event
   */
  listenerCount(event?: keyof EventMap): number {
    if (event) {
      return this.listenerMap.get(String(event))?.size || 0
    }
    
    let total = 0
    for (const listeners of this.listenerMap.values()) {
      total += listeners.size
    }
    return total
  }

  /**
   * Get all registered events
   */
  getEvents(): string[] {
    return Array.from(this.listenerMap.keys())
  }

  /**
   * Enable/disable debug mode
   */
  setDebugMode(enabled: boolean): void {
    this.debugMode = enabled
  }

  // ============================================================================
  // UTILITY METHODS
  // ============================================================================

  private debounce<T extends any[]>(
    func: (...args: T) => void, 
    delay: number
  ): (...args: T) => void {
    let timeoutId: NodeJS.Timeout
    return (...args: T) => {
      clearTimeout(timeoutId)
      timeoutId = setTimeout(() => func(...args), delay)
    }
  }

  private throttle<T extends any[]>(
    func: (...args: T) => void, 
    delay: number
  ): (...args: T) => void {
    let lastCall = 0
    return (...args: T) => {
      const now = Date.now()
      if (now - lastCall >= delay) {
        lastCall = now
        func(...args)
      }
    }
  }
}

// ============================================================================
// GLOBAL EVENT BUS INSTANCE
// ============================================================================

let globalEventBus: EventEmitter

/**
 * Get or create the global event bus instance
 */
export function getEventBus(): EventEmitter {
  if (!globalEventBus) {
    const isDebug = process.env.NODE_ENV === 'development'
    globalEventBus = new EventEmitter(isDebug)
    
    // Expose to window in development
    if (isDebug && typeof window !== 'undefined') {
      (window as any).__eventBus = globalEventBus
    }
  }
  
  return globalEventBus
}

/**
 * Create a new isolated event bus instance
 */
export function createEventBus(debug = false): EventEmitter {
  return new EventEmitter(debug)
}

// ============================================================================
// CONVENIENCE EXPORTS
// ============================================================================

export const eventBus = getEventBus()
export { EventEmitter }
export type { EventMap, EventListener, EventUnsubscribe, EventListenerOptions }
