<template>
  <div class="space-y-4">
    <!-- Em<PERSON>er -->
    <div class="bg-gray-100 dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
      <div class="flex items-center justify-between mb-3">
        <h3 class="text-sm font-medium text-gray-900 dark:text-white">Email Preview</h3>
        <div class="flex items-center space-x-2">
          <UiButton @click="previewMode = 'desktop'" :variant="previewMode === 'desktop' ? 'solid' : 'outline'" size="sm">
            <Icon name="heroicons:computer-desktop" class="w-4 h-4 mr-1" />
            Desktop
          </UiButton>
          <UiButton @click="previewMode = 'mobile'" :variant="previewMode === 'mobile' ? 'solid' : 'outline'" size="sm">
            <Icon name="heroicons:device-phone-mobile" class="w-4 h-4 mr-1" />
            Mobile
          </UiButton>
        </div>
      </div>
      
      <!-- Email Client Mockup -->
      <div
        :class="[
          'bg-white dark:bg-gray-900 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden',
          previewMode === 'mobile' ? 'max-w-sm mx-auto' : 'w-full'
        ]"
      >
        <!-- Email Header Bar -->
        <div class="bg-gray-50 dark:bg-gray-800 px-4 py-3 border-b border-gray-200 dark:border-gray-700">
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-3">
              <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                <Icon name="heroicons:envelope" class="w-4 h-4 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <div class="text-sm font-medium text-gray-900 dark:text-white">
                  {{ content?.fromName || 'Your Organization' }}
                </div>
                <div class="text-xs text-gray-500 dark:text-gray-400">
                  {{ content?.fromEmail || '<EMAIL>' }}
                </div>
              </div>
            </div>
            <div class="text-xs text-gray-500 dark:text-gray-400">
              {{ currentTime }}
            </div>
          </div>
          
          <div class="mt-2">
            <div class="text-sm font-medium text-gray-900 dark:text-white">
              {{ renderedSubject }}
            </div>
          </div>
        </div>
        
        <!-- Email Content -->
        <div class="p-4 min-h-[200px]">
          <div v-html="renderedContent" class="prose prose-sm max-w-none dark:prose-invert"></div>
        </div>
        
        <!-- Email Footer -->
        <div v-if="content?.settings?.enableUnsubscribe" class="bg-gray-50 dark:bg-gray-800 px-4 py-2 border-t border-gray-200 dark:border-gray-700">
          <div class="text-xs text-gray-500 dark:text-gray-400 text-center">
            <a href="#" class="hover:text-gray-700 dark:hover:text-gray-300">Unsubscribe</a> | 
            <a href="#" class="hover:text-gray-700 dark:hover:text-gray-300">Update Preferences</a>
          </div>
        </div>
      </div>
    </div>

    <!-- Email Settings Summary -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
      <div class="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
        <div class="flex items-center space-x-2 mb-2">
          <Icon name="heroicons:cog-6-tooth" class="w-4 h-4 text-gray-500 dark:text-gray-400" />
          <h4 class="text-sm font-medium text-gray-900 dark:text-white">Settings</h4>
        </div>
        <div class="space-y-1 text-xs text-gray-600 dark:text-gray-400">
          <div class="flex justify-between">
            <span>Priority:</span>
            <span class="capitalize">{{ content?.settings?.priority || 'normal' }}</span>
          </div>
          <div class="flex justify-between">
            <span>Track Opens:</span>
            <span>{{ content?.settings?.trackOpens ? 'Yes' : 'No' }}</span>
          </div>
          <div class="flex justify-between">
            <span>Track Clicks:</span>
            <span>{{ content?.settings?.trackClicks ? 'Yes' : 'No' }}</span>
          </div>
        </div>
      </div>
      
      <div class="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
        <div class="flex items-center space-x-2 mb-2">
          <Icon name="heroicons:chart-bar" class="w-4 h-4 text-gray-500 dark:text-gray-400" />
          <h4 class="text-sm font-medium text-gray-900 dark:text-white">Content Stats</h4>
        </div>
        <div class="space-y-1 text-xs text-gray-600 dark:text-gray-400">
          <div class="flex justify-between">
            <span>Subject Length:</span>
            <span>{{ subjectLength }} chars</span>
          </div>
          <div class="flex justify-between">
            <span>Content Length:</span>
            <span>{{ contentLength }} chars</span>
          </div>
          <div class="flex justify-between">
            <span>Variables:</span>
            <span>{{ variables.length }}</span>
          </div>
        </div>
      </div>
      
      <div class="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
        <div class="flex items-center space-x-2 mb-2">
          <Icon name="heroicons:shield-check" class="w-4 h-4 text-gray-500 dark:text-gray-400" />
          <h4 class="text-sm font-medium text-gray-900 dark:text-white">Deliverability</h4>
        </div>
        <div class="space-y-1 text-xs text-gray-600 dark:text-gray-400">
          <div class="flex justify-between">
            <span>Spam Score:</span>
            <span class="text-green-600 dark:text-green-400">Low</span>
          </div>
          <div class="flex justify-between">
            <span>Authentication:</span>
            <span class="text-green-600 dark:text-green-400">Valid</span>
          </div>
          <div class="flex justify-between">
            <span>Unsubscribe:</span>
            <span>{{ content?.settings?.enableUnsubscribe ? 'Included' : 'Missing' }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Variables Used -->
    <div v-if="usedVariables.length > 0" class="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
      <div class="flex items-center space-x-2 mb-3">
        <Icon name="heroicons:variable" class="w-4 h-4 text-gray-500 dark:text-gray-400" />
        <h4 class="text-sm font-medium text-gray-900 dark:text-white">Variables in Use</h4>
      </div>
      <div class="flex flex-wrap gap-2">
        <span
          v-for="variable in usedVariables"
          :key="variable"
          class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
        >
          {{ variable }}
        </span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

// Props
interface Props {
  content: any
  variables: string[]
  templateData: any
}

const props = defineProps<Props>()

// State
const previewMode = ref<'desktop' | 'mobile'>('desktop')

// Computed
const currentTime = computed(() => {
  return new Date().toLocaleTimeString('en-US', { 
    hour: 'numeric', 
    minute: '2-digit',
    hour12: true 
  })
})

const renderedSubject = computed(() => {
  let subject = props.content?.subject || 'Email Subject'
  
  // Replace variables with sample values
  props.variables.forEach(variable => {
    const sampleValue = getSampleValue(variable)
    subject = subject.replace(new RegExp(`{${variable}}`, 'g'), sampleValue)
  })
  
  return subject
})

const renderedContent = computed(() => {
  let content = props.content?.content || 'Your email content will appear here...'
  
  // Replace variables with highlighted placeholders for preview
  props.variables.forEach(variable => {
    const sampleValue = getSampleValue(variable)
    const placeholder = `<span class="bg-blue-100 text-blue-800 px-1 rounded text-sm font-semibold">${sampleValue}</span>`
    content = content.replace(new RegExp(`{${variable}}`, 'g'), placeholder)
  })
  
  return content
})

const subjectLength = computed(() => props.content?.subject?.length || 0)
const contentLength = computed(() => props.content?.content?.length || 0)

const usedVariables = computed(() => {
  const content = (props.content?.subject || '') + (props.content?.content || '')
  return props.variables.filter(variable => 
    content.includes(`{${variable}}`)
  )
})

// Methods
const getSampleValue = (variable: string) => {
  const sampleValues: Record<string, string> = {
    clientName: 'John Doe',
    companyName: 'Acme Legal Services',
    caseTitle: 'Contract Dispute Case',
    appointmentDate: new Date().toLocaleDateString(),
    appointmentTime: '2:00 PM',
    invoiceNumber: 'INV-001',
    amount: '$1,250.00',
    dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toLocaleDateString(),
    attorneyName: 'Jane Smith',
    documentTitle: 'Legal Agreement',
    caseNumber: 'CASE-2024-001',
    companyPhone: '(*************',
    companyEmail: '<EMAIL>',
    companyAddress: '123 Main St, City, State 12345',
  }
  
  return sampleValues[variable] || `[${variable.toUpperCase()}]`
}
</script>
