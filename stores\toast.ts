// stores/toast.ts
import { defineStore } from 'pinia';

export interface ToastAction {
  label: string;
  handler: () => void;
}

export interface Toast {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title?: string;
  message: string;
  duration?: number; // 0 means no auto-dismiss
  action?: ToastAction;
  createdAt: number;
  startTime?: number;
}

// Global counter for unique toast IDs
let toastIdCounter = 0;

export const useToastStore = defineStore('toast', {
  state: () => ({
    toasts: [] as Toast[],
  }),
  actions: {
    addToast(toast: Omit<Toast, 'id' | 'createdAt'>) {
      try {
        const newToast: Toast = {
          id: `toast-${toastIdCounter++}`,
          createdAt: Date.now(),
          duration: toast.duration ?? 5000, // Default 5 seconds
          ...toast
        };

        // Limit the number of toasts to prevent memory issues
        if (this.toasts.length >= 10) {
          this.toasts.shift(); // Remove oldest toast
        }

        this.toasts.push(newToast);

        // Auto-removal is handled by ToastContainer component
        return newToast.id;
      } catch (error) {
        console.error('Failed to add toast:', error);
        return '';
      }
    },
    removeToast(id: string) {
      try {
        this.toasts = this.toasts.filter(t => t.id !== id);
      } catch (error) {
        console.error('Failed to remove toast:', error);
      }
    },
    clearAllToasts() {
      try {
        this.toasts = [];
      } catch (error) {
        console.error('Failed to clear toasts:', error);
      }
    },
    updateToastStartTime(id: string) {
      try {
        const toast = this.toasts.find(t => t.id === id);
        if (toast) {
          toast.startTime = Date.now();
        }
      } catch (error) {
        console.error('Failed to update toast start time:', error);
      }
    },
    // Convenience methods for different toast types
    success(message: string, options?: Partial<Omit<Toast, 'id' | 'type' | 'message' | 'createdAt'>>) {
      return this.addToast({
        type: 'success',
        message,
        title: options?.title || 'Success',
        ...options
      });
    },
    error(message: string, options?: Partial<Omit<Toast, 'id' | 'type' | 'message' | 'createdAt'>>) {
      return this.addToast({
        type: 'error',
        message,
        title: options?.title || 'Error',
        duration: 0,
        ...options
      }); // Errors don't auto-dismiss
    },
    warning(message: string, options?: Partial<Omit<Toast, 'id' | 'type' | 'message' | 'createdAt'>>) {
      return this.addToast({
        type: 'warning',
        message,
        title: options?.title || 'Warning',
        ...options
      });
    },
    info(message: string, options?: Partial<Omit<Toast, 'id' | 'type' | 'message' | 'createdAt'>>) {
      return this.addToast({
        type: 'info',
        message,
        title: options?.title || 'Information',
        ...options
      });
    }
  },
  getters: {
    activeToasts: (state) => state.toasts,
    toastCount: (state) => state.toasts.length,
    hasToasts: (state) => state.toasts.length > 0
  },
});