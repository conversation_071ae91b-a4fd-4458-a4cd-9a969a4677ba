<template>
  <section :class="sectionClass">
    <div class="container mx-auto px-6">
      <!-- Section Header -->
      <div v-if="title || subtitle" :class="headerClass">
        <h2 v-if="title" :class="titleClass">
          {{ title }}
        </h2>
        <p v-if="subtitle" :class="subtitleClass">
          {{ subtitle }}
        </p>
      </div>

      <!-- Testimonials Grid -->
      <div :class="gridClass">
        <div
          v-for="(testimonial, index) in testimonials"
          :key="index"
          :class="cardClass"
          :style="{ animationDelay: `${index * 150}ms` }"
        >
          <!-- Quote Icon -->
          <div class="mb-4">
            <Icon 
              name="material-symbols:format-quote" 
              :class="quoteIconClass"
            />
          </div>

          <!-- Testimonial Content -->
          <blockquote :class="quoteClass">
            "{{ testimonial.content }}"
          </blockquote>

          <!-- Rating -->
          <div v-if="testimonial.rating" class="flex items-center mt-4 mb-6">
            <div class="flex">
              <Icon
                v-for="star in 5"
                :key="star"
                name="material-symbols:star"
                :class="[
                  'w-5 h-5',
                  star <= testimonial.rating ? 'text-yellow-400' : 'text-gray-300'
                ]"
              />
            </div>
            <span class="ml-2 text-sm text-gray-600 dark:text-gray-400">
              {{ testimonial.rating }}/5
            </span>
          </div>

          <!-- Author Info -->
          <div class="flex items-center">
            <!-- Avatar -->
            <div class="flex-shrink-0">
              <img
                v-if="testimonial.author.avatar"
                :src="testimonial.author.avatar"
                :alt="testimonial.author.name"
                class="w-12 h-12 rounded-full object-cover border-2 border-gray-200 dark:border-gray-700"
              />
              <div
                v-else
                class="w-12 h-12 rounded-full bg-gradient-to-br from-brandPrimary-500 to-purple-600 flex items-center justify-center text-white font-semibold text-lg"
              >
                {{ getInitials(testimonial.author.name) }}
              </div>
            </div>

            <!-- Author Details -->
            <div class="ml-4">
              <div :class="authorNameClass">
                {{ testimonial.author.name }}
              </div>
              <div :class="authorTitleClass">
                {{ testimonial.author.title }}
              </div>
              <div v-if="testimonial.author.company" :class="authorCompanyClass">
                {{ testimonial.author.company }}
              </div>
            </div>
          </div>

          <!-- Company Logo -->
          <div v-if="testimonial.companyLogo" class="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
            <img
              :src="testimonial.companyLogo"
              :alt="`${testimonial.author.company} logo`"
              class="h-8 opacity-60 hover:opacity-100 transition-opacity duration-200"
            />
          </div>
        </div>
      </div>

      <!-- Navigation Dots (if carousel) -->
      <div v-if="showNavigation && testimonials.length > visibleCount" class="flex justify-center mt-8 space-x-2">
        <button
          v-for="(_, index) in Math.ceil(testimonials.length / visibleCount)"
          :key="index"
          :class="[
            'w-3 h-3 rounded-full transition-all duration-200',
            index === currentSlide ? 'bg-brandPrimary-600' : 'bg-gray-300 hover:bg-gray-400'
          ]"
          @click="goToSlide(index)"
        />
      </div>

      <!-- Bottom CTA -->
      <div v-if="bottomAction" class="text-center mt-12">
        <UiButton
          :variant="bottomAction.variant || 'primary'"
          :size="bottomAction.size || 'lg'"
          :to="bottomAction.to"
          :href="bottomAction.href"
          class="transform hover:scale-105 transition-all duration-300"
        >
          <Icon v-if="bottomAction.icon" :name="bottomAction.icon" class="w-5 h-5 mr-2" />
          {{ bottomAction.text }}
        </UiButton>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
interface Author {
  name: string
  title: string
  company?: string
  avatar?: string
}

interface Testimonial {
  content: string
  author: Author
  rating?: number
  companyLogo?: string
}

interface BottomAction {
  text: string
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost'
  size?: 'sm' | 'md' | 'lg'
  to?: string
  href?: string
  icon?: string
}

interface Props {
  title?: string
  subtitle?: string
  testimonials: Testimonial[]
  variant?: 'default' | 'cards' | 'minimal'
  columns?: 1 | 2 | 3
  background?: 'white' | 'gray' | 'dark'
  textAlign?: 'left' | 'center'
  showNavigation?: boolean
  visibleCount?: number
  bottomAction?: BottomAction
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'cards',
  columns: 3,
  background: 'gray',
  textAlign: 'center',
  showNavigation: false,
  visibleCount: 3
})

// Reactive state
const currentSlide = ref(0)

// Methods
const getInitials = (name: string): string => {
  return name
    .split(' ')
    .map(word => word.charAt(0))
    .join('')
    .toUpperCase()
    .slice(0, 2)
}

const goToSlide = (index: number) => {
  currentSlide.value = index
}

// Computed classes
const sectionClass = computed(() => {
  const backgrounds = {
    white: 'bg-white',
    gray: 'bg-gray-50',
    dark: 'bg-gray-900 text-white'
  }
  return `py-16 ${backgrounds[props.background]}`
})

const headerClass = computed(() => {
  const alignments = {
    left: 'text-left mb-12',
    center: 'text-center mb-12'
  }
  return alignments[props.textAlign]
})

const titleClass = computed(() => {
  const baseClass = 'font-bold mb-4'
  const colorClass = props.background === 'dark' ? 'text-white' : 'text-gray-800'
  return `${baseClass} text-3xl md:text-4xl ${colorClass}`
})

const subtitleClass = computed(() => {
  const baseClass = 'max-w-3xl'
  const colorClass = props.background === 'dark' ? 'text-gray-300' : 'text-gray-600'
  const alignClass = props.textAlign === 'center' ? 'mx-auto' : ''
  return `${baseClass} text-lg ${colorClass} ${alignClass}`
})

const gridClass = computed(() => {
  const columns = {
    1: 'grid-cols-1',
    2: 'grid-cols-1 md:grid-cols-2',
    3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3'
  }
  return `grid ${columns[props.columns]} gap-8`
})

const cardClass = computed(() => {
  const variants = {
    default: 'p-6',
    cards: 'bg-white dark:bg-gray-800 p-8 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-all duration-300',
    minimal: 'p-6 hover:bg-gray-50 dark:hover:bg-gray-800 rounded-lg transition-colors duration-200'
  }
  return `${variants[props.variant]} animate-fade-in-up`
})

const quoteIconClass = computed(() => {
  const colorClass = props.background === 'dark' ? 'text-brandPrimary-400' : 'text-brandPrimary-600'
  return `w-8 h-8 ${colorClass}`
})

const quoteClass = computed(() => {
  const colorClass = props.background === 'dark' ? 'text-gray-300' : 'text-gray-700'
  return `text-lg leading-relaxed ${colorClass}`
})

const authorNameClass = computed(() => {
  const colorClass = props.background === 'dark' ? 'text-white' : 'text-gray-900'
  return `font-semibold ${colorClass}`
})

const authorTitleClass = computed(() => {
  const colorClass = props.background === 'dark' ? 'text-gray-400' : 'text-gray-600'
  return `text-sm ${colorClass}`
})

const authorCompanyClass = computed(() => {
  const colorClass = props.background === 'dark' ? 'text-brandPrimary-400' : 'text-brandPrimary-600'
  return `text-sm font-medium ${colorClass}`
})
</script>

<style scoped>
@keyframes fadeInFromBottom {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  animation: fadeInFromBottom 0.6s ease-out forwards;
  opacity: 0;
}
</style>
