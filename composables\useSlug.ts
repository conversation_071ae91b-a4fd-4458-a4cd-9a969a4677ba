import { ref, watch, type Ref } from 'vue'
import { debounce } from 'lodash-es'

// Simulate an API call to check for slug uniqueness
const checkApi = async (slug: string): Promise<boolean> => {
  console.log(`Checking slug: ${slug}`)
  return new Promise(resolve => {
    setTimeout(() => {
      const isTaken = ['admin', 'dashboard', 'settings'].includes(slug)
      resolve(!isTaken)
    }, 500)
  })
}

export const useSlug = (
  initialSlug = '',
  sourceRef?: string
) => {
  const slug = ref(initialSlug)
  const isValidating = ref(false)
  const isUnique = ref<boolean | null>(null)

  const formatSlug = (value: string) => {
    return value
      .toLowerCase()
      .replace(/\s+/g, '-')
      .replace(/[^a-z0-9-]/g, '')
  }

  const checkUniqueness = debounce(async (newSlug: string) => {
    if (!newSlug) {
      isUnique.value = null
      isValidating.value = false
      return
    }
    isValidating.value = true
    isUnique.value = await checkApi(newSlug)
    isValidating.value = false
  }, 300)
 

  if (sourceRef) {
    watch(() => sourceRef, (newValue) => {
        console.log('sourceRef changed:', newValue)
      slug.value = formatSlug(newValue)
    })
  }

  watch(slug, (newSlug, oldSlug) => {
    if (newSlug !== oldSlug) {
      const formatted = formatSlug(newSlug)
      if (formatted !== newSlug) {
        slug.value = formatted
      }
      checkUniqueness(formatted)
    }
  }, { immediate: true })

  const slugValidationRule = (value: string) => {
    if (!value) {
      return 'Slug is required'
    }
    if (isValidating.value) {
      return 'Checking uniqueness...'
    }
    if (isUnique.value === false) {
      return 'This slug is already taken'
    }
    return true
  }

  return {
    slug,
    isValidating,
    isUnique,
    formatSlug,
    slugValidationRule
  }
}