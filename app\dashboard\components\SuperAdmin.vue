<!--
  Super Admin Dashboard Component

  Comprehensive administrative dashboard with monitoring, analytics,
  system health, user management, and platform oversight capabilities
-->

<template>
  <div class="super-admin-dashboard min-h-screen bg-gray-50 dark:bg-gray-900">
    <!-- Dashboard Header -->
    <div class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
      <div class="px-6 py-4">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
              Super Admin Dashboard
            </h1>
            <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
              Platform monitoring, analytics, and administrative controls
            </p>
          </div>

          <!-- Quick Actions -->
          <div class="flex items-center space-x-3">
            <UiButton
              variant="ghost"
              size="sm"
              @click="refreshDashboard"
              :loading="isRefreshing"
            >
              <Icon name="heroicons:arrow-path" class="h-4 w-4 mr-2" />
              Refresh
            </UiButton>

            <UiButton
              variant="secondary"
              size="sm"
              @click="exportReport"
              :loading="isExporting"
            >
              <Icon name="heroicons:document-arrow-down" class="h-4 w-4 mr-2" />
              Export Report
            </UiButton>

            <!-- Real-time Status Indicator -->
            <div class="flex items-center space-x-2">
              <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              <span class="text-xs text-gray-500 dark:text-gray-400">Live</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Dashboard Content -->
    <div class="p-6 space-y-8">
        <PerformanceMonitor/>
      <!-- System Health Overview -->
      <section class="system-health">
        <div class="flex items-center justify-between mb-6">
          <h2 class="text-lg font-semibold text-gray-900 dark:text-white">
            System Health
          </h2>
          <div class="flex items-center space-x-2">
            <div
              :class="[
                'px-2 py-1 rounded-full text-xs font-medium',
                systemHealth.status === 'healthy' ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400' :
                systemHealth.status === 'warning' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400' :
                'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
              ]"
            >
              {{ systemHealth.status.toUpperCase() }}
            </div>
          </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <!-- Server Status -->
          <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Server Status</p>
                <p class="text-2xl font-bold text-gray-900 dark:text-white">
                  {{ systemHealth.serverUptime }}
                </p>
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  Uptime
                </p>
              </div>
              <div class="p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                <Icon name="heroicons:server" class="h-6 w-6 text-green-600" />
              </div>
            </div>
          </div>

          <!-- Database Health -->
          <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Database</p>
                <p class="text-2xl font-bold text-gray-900 dark:text-white">
                  {{ systemHealth.dbResponseTime }}ms
                </p>
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  Avg Response Time
                </p>
              </div>
              <div class="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <Icon name="heroicons:circle-stack" class="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </div>

          <!-- Memory Usage -->
          <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Memory Usage</p>
                <p class="text-2xl font-bold text-gray-900 dark:text-white">
                  {{ systemHealth.memoryUsage }}%
                </p>
                <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mt-2">
                  <div
                    class="bg-orange-500 h-2 rounded-full transition-all duration-300"
                    :style="{ width: `${systemHealth.memoryUsage}%` }"
                  ></div>
                </div>
              </div>
              <div class="p-3 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
                <Icon name="heroicons:cpu-chip" class="h-6 w-6 text-orange-600" />
              </div>
            </div>
          </div>

          <!-- API Performance -->
          <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm font-medium text-gray-600 dark:text-gray-400">API Performance</p>
                <p class="text-2xl font-bold text-gray-900 dark:text-white">
                  {{ systemHealth.apiResponseTime }}ms
                </p>
                <div class="flex items-center mt-2">
                  <Icon
                    :name="systemHealth.apiTrend === 'up' ? 'heroicons:arrow-trending-up' : 'heroicons:arrow-trending-down'"
                    :class="[
                      'h-4 w-4 mr-1',
                      systemHealth.apiTrend === 'up' ? 'text-green-500' : 'text-red-500'
                    ]"
                  />
                  <span
                    :class="[
                      'text-xs font-medium',
                      systemHealth.apiTrend === 'up' ? 'text-green-600' : 'text-red-600'
                    ]"
                  >
                    {{ systemHealth.apiTrendValue }}%
                  </span>
                </div>
              </div>
              <div class="p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                <Icon name="heroicons:bolt" class="h-6 w-6 text-purple-600" />
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Platform Analytics -->
      <section class="platform-analytics">
        <div class="flex items-center justify-between mb-6">
          <h2 class="text-lg font-semibold text-gray-900 dark:text-white">
            Platform Analytics
          </h2>
          <div class="flex items-center space-x-2">
            <select
              v-model="analyticsTimeframe"
              class="text-sm border border-gray-300 dark:border-gray-600 rounded-md px-3 py-1 bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
            >
              <option value="24h">Last 24 Hours</option>
              <option value="7d">Last 7 Days</option>
              <option value="30d">Last 30 Days</option>
              <option value="90d">Last 90 Days</option>
            </select>
          </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <!-- User Metrics Dashboard -->
          <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
              User Analytics
            </h3>
            <UserMetricsDashboard :users="platformUsers" />
          </div>

          <!-- Tenant Metrics Dashboard -->
          <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Tenant Analytics
            </h3>
            <TenantMetricsDashboard :tenants="platformTenants" />
          </div>
        </div>
      </section>

      <!-- System Monitoring -->
      <section class="system-monitoring">
        <div class="flex items-center justify-between mb-6">
          <h2 class="text-lg font-semibold text-gray-900 dark:text-white">
            System Monitoring
          </h2>
          <UiButton
            variant="ghost"
            size="sm"
            @click="togglePerformanceMonitor"
          >
            <Icon name="heroicons:chart-bar" class="h-4 w-4 mr-2" />
            {{ showPerformanceMonitor ? 'Hide' : 'Show' }} Performance Monitor
          </UiButton>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <!-- Performance Metrics -->
          <div class="lg:col-span-2 bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Performance Metrics
            </h3>

            <!-- Performance Chart -->
            <div class="h-64 mb-4">
              <svg class="w-full h-full" viewBox="0 0 400 200">
                <!-- Grid lines -->
                <defs>
                  <pattern id="performanceGrid" width="40" height="20" patternUnits="userSpaceOnUse">
                    <path d="M 40 0 L 0 0 0 20" fill="none" stroke="currentColor" stroke-width="0.5" class="text-gray-200 dark:text-gray-700"/>
                  </pattern>
                </defs>
                <rect width="100%" height="100%" fill="url(#performanceGrid)" />

                <!-- Performance data lines -->
                <polyline
                  :points="performanceChartData.cpu"
                  fill="none"
                  stroke="#3B82F6"
                  stroke-width="2"
                  class="transition-all duration-300"
                />
                <polyline
                  :points="performanceChartData.memory"
                  fill="none"
                  stroke="#EF4444"
                  stroke-width="2"
                  class="transition-all duration-300"
                />
                <polyline
                  :points="performanceChartData.network"
                  fill="none"
                  stroke="#10B981"
                  stroke-width="2"
                  class="transition-all duration-300"
                />
              </svg>
            </div>

            <!-- Chart Legend -->
            <div class="flex items-center justify-center space-x-6 text-sm">
              <div class="flex items-center">
                <div class="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
                <span class="text-gray-600 dark:text-gray-400">CPU Usage</span>
              </div>
              <div class="flex items-center">
                <div class="w-3 h-3 bg-red-500 rounded-full mr-2"></div>
                <span class="text-gray-600 dark:text-gray-400">Memory Usage</span>
              </div>
              <div class="flex items-center">
                <div class="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                <span class="text-gray-600 dark:text-gray-400">Network I/O</span>
              </div>
            </div>
          </div>

          <!-- System Alerts -->
          <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
              System Alerts
            </h3>

            <div class="space-y-3">
              <div
                v-for="alert in systemAlerts"
                :key="alert.id"
                :class="[
                  'p-3 rounded-lg border-l-4',
                  alert.severity === 'critical' ? 'bg-red-50 border-red-500 dark:bg-red-900/20' :
                  alert.severity === 'warning' ? 'bg-yellow-50 border-yellow-500 dark:bg-yellow-900/20' :
                  'bg-blue-50 border-blue-500 dark:bg-blue-900/20'
                ]"
              >
                <div class="flex items-start justify-between">
                  <div class="flex-1">
                    <p
                      :class="[
                        'text-sm font-medium',
                        alert.severity === 'critical' ? 'text-red-800 dark:text-red-400' :
                        alert.severity === 'warning' ? 'text-yellow-800 dark:text-yellow-400' :
                        'text-blue-800 dark:text-blue-400'
                      ]"
                    >
                      {{ alert.title }}
                    </p>
                    <p
                      :class="[
                        'text-xs mt-1',
                        alert.severity === 'critical' ? 'text-red-600 dark:text-red-300' :
                        alert.severity === 'warning' ? 'text-yellow-600 dark:text-yellow-300' :
                        'text-blue-600 dark:text-blue-300'
                      ]"
                    >
                      {{ alert.message }}
                    </p>
                    <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                      {{ formatRelativeTime(alert.timestamp) }}
                    </p>
                  </div>
                  <UiButton
                    variant="ghost"
                    size="xs"
                    @click="dismissAlert(alert.id)"
                  >
                    <Icon name="heroicons:x-mark" class="h-3 w-3" />
                  </UiButton>
                </div>
              </div>

              <div v-if="systemAlerts.length === 0" class="text-center py-8">
                <Icon name="heroicons:check-circle" class="h-12 w-12 text-green-500 mx-auto mb-2" />
                <p class="text-sm text-gray-500 dark:text-gray-400">
                  No active alerts
                </p>
              </div>
            </div>
          </div>
        </div>

        <!-- Performance Monitor Component -->
        <div v-if="showPerformanceMonitor" class="mt-6">
          <PerformanceMonitor />
        </div>
      </section>

      <!-- Recent Activity & Audit Logs -->
      <section class="recent-activity">
        <div class="flex items-center justify-between mb-6">
          <h2 class="text-lg font-semibold text-gray-900 dark:text-white">
            Recent Activity & Audit Logs
          </h2>
          <div class="flex items-center space-x-2">
            <UiButton
              variant="ghost"
              size="sm"
              @click="refreshAuditLogs"
              :loading="isLoadingAuditLogs"
            >
              <Icon name="heroicons:arrow-path" class="h-4 w-4 mr-2" />
              Refresh
            </UiButton>
            <NuxtLink to="/dashboard/platform/audit-logs">
              <UiButton variant="secondary" size="sm">
                <Icon name="heroicons:eye" class="h-4 w-4 mr-2" />
                View All
              </UiButton>
            </NuxtLink>
          </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
          <div class="p-6">
            <div class="space-y-4">
              <div
                v-for="log in recentAuditLogs"
                :key="log.id"
                class="flex items-start space-x-4 p-4 rounded-lg bg-gray-50 dark:bg-gray-700/50"
              >
                <div
                  :class="[
                    'flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center',
                    getActivityIconBg(log.action)
                  ]"
                >
                  <Icon
                    :name="getActivityIcon(log.action)"
                    :class="['h-4 w-4', getActivityIconColor(log.action)]"
                  />
                </div>

                <div class="flex-1 min-w-0">
                  <div class="flex items-center justify-between">
                    <p class="text-sm font-medium text-gray-900 dark:text-white">
                      {{ log.description }}
                    </p>
                    <p class="text-xs text-gray-500 dark:text-gray-400">
                      {{ formatRelativeTime(log.createdAt) }}
                    </p>
                  </div>

                  <div class="mt-1 flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400">
                    <span>{{ log.userEmail || 'System' }}</span>
                    <span>•</span>
                    <span>{{ log.ipAddress }}</span>
                    <span>•</span>
                    <span>{{ log.userAgent?.split(' ')[0] || 'Unknown' }}</span>
                  </div>

                  <div v-if="log.metadata" class="mt-2">
                    <details class="text-xs">
                      <summary class="cursor-pointer text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300">
                        View Details
                      </summary>
                      <pre class="mt-2 p-2 bg-gray-100 dark:bg-gray-800 rounded text-xs overflow-x-auto">{{ JSON.stringify(log.metadata, null, 2) }}</pre>
                    </details>
                  </div>
                </div>
              </div>

              <div v-if="recentAuditLogs.length === 0" class="text-center py-8">
                <Icon name="heroicons:document-text" class="h-12 w-12 text-gray-400 mx-auto mb-2" />
                <p class="text-sm text-gray-500 dark:text-gray-400">
                  No recent activity
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Quick Admin Actions -->
      <section class="quick-actions">
        <div class="flex items-center justify-between mb-6">
          <h2 class="text-lg font-semibold text-gray-900 dark:text-white">
            Quick Admin Actions
          </h2>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <!-- User Management -->
          <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <div class="flex items-center justify-between mb-4">
              <div class="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <Icon name="heroicons:users" class="h-6 w-6 text-blue-600" />
              </div>
              <UiButton variant="ghost" size="xs">
                <Icon name="heroicons:arrow-top-right-on-square" class="h-4 w-4" />
              </UiButton>
            </div>
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
              User Management
            </h3>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">
              Manage platform users, roles, and permissions
            </p>
            <div class="space-y-2">
              <NuxtLink to="/dashboard/platform/users">
                <UiButton variant="ghost" size="sm" class="w-full justify-start">
                  <Icon name="heroicons:eye" class="h-4 w-4 mr-2" />
                  View All Users
                </UiButton>
              </NuxtLink>
              <UiButton
                variant="ghost"
                size="sm"
                class="w-full justify-start"
                @click="openCreateUserModal"
              >
                <Icon name="heroicons:plus" class="h-4 w-4 mr-2" />
                Create User
              </UiButton>
            </div>
          </div>

          <!-- Tenant Management -->
          <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <div class="flex items-center justify-between mb-4">
              <div class="p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                <Icon name="heroicons:building-office" class="h-6 w-6 text-green-600" />
              </div>
              <UiButton variant="ghost" size="xs">
                <Icon name="heroicons:arrow-top-right-on-square" class="h-4 w-4" />
              </UiButton>
            </div>
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
              Tenant Management
            </h3>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">
              Manage tenants, subscriptions, and billing
            </p>
            <div class="space-y-2">
              <NuxtLink to="/dashboard/platform/tenants">
                <UiButton variant="ghost" size="sm" class="w-full justify-start">
                  <Icon name="heroicons:eye" class="h-4 w-4 mr-2" />
                  View All Tenants
                </UiButton>
              </NuxtLink>
              <UiButton
                variant="ghost"
                size="sm"
                class="w-full justify-start"
                @click="openCreateTenantModal"
              >
                <Icon name="heroicons:plus" class="h-4 w-4 mr-2" />
                Create Tenant
              </UiButton>
            </div>
          </div>

          <!-- System Configuration -->
          <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <div class="flex items-center justify-between mb-4">
              <div class="p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                <Icon name="heroicons:cog-6-tooth" class="h-6 w-6 text-purple-600" />
              </div>
              <UiButton variant="ghost" size="xs">
                <Icon name="heroicons:arrow-top-right-on-square" class="h-4 w-4" />
              </UiButton>
            </div>
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
              System Config
            </h3>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">
              Configure system settings and features
            </p>
            <div class="space-y-2">
              <NuxtLink to="/dashboard/platform/settings">
                <UiButton variant="ghost" size="sm" class="w-full justify-start">
                  <Icon name="heroicons:cog-6-tooth" class="h-4 w-4 mr-2" />
                  System Settings
                </UiButton>
              </NuxtLink>
              <UiButton
                variant="ghost"
                size="sm"
                class="w-full justify-start"
                @click="openMaintenanceModal"
              >
                <Icon name="heroicons:wrench-screwdriver" class="h-4 w-4 mr-2" />
                Maintenance Mode
              </UiButton>
            </div>
          </div>

          <!-- Reports & Analytics -->
          <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <div class="flex items-center justify-between mb-4">
              <div class="p-3 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
                <Icon name="heroicons:chart-bar" class="h-6 w-6 text-orange-600" />
              </div>
              <UiButton variant="ghost" size="xs">
                <Icon name="heroicons:arrow-top-right-on-square" class="h-4 w-4" />
              </UiButton>
            </div>
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
              Reports & Analytics
            </h3>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">
              Generate reports and view detailed analytics
            </p>
            <div class="space-y-2">
              <NuxtLink to="/dashboard/platform/reports">
                <UiButton variant="ghost" size="sm" class="w-full justify-start">
                  <Icon name="heroicons:document-chart-bar" class="h-4 w-4 mr-2" />
                  View Reports
                </UiButton>
              </NuxtLink>
              <UiButton
                variant="ghost"
                size="sm"
                class="w-full justify-start"
                @click="generateSystemReport"
                :loading="isGeneratingReport"
              >
                <Icon name="heroicons:document-arrow-down" class="h-4 w-4 mr-2" />
                Generate Report
              </UiButton>
            </div>
          </div>
        </div>
      </section>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { storeToRefs } from 'pinia'
import { navigateTo } from '#app'

// UI Components
import UiButton from '~/components/ui/UiButton.vue'
import UserMetricsDashboard from '~/components/users/UserMetricsDashboard.vue'
import TenantMetricsDashboard from '~/components/tenants/TenantMetricsDashboard.vue'
import PerformanceMonitor from '~/components/ui/PerformanceMonitor.vue'

// Stores
import { useUserStore } from '~/stores/user'
import { useTenantStore } from '~/stores/tenant'
import { useAuditLogStore } from '~/stores/auditLog'

// Composables
import { useToast } from '~/composables/useToast'

// Types
interface SystemHealth {
  status: 'healthy' | 'warning' | 'critical'
  serverUptime: string
  dbResponseTime: number
  memoryUsage: number
  apiResponseTime: number
  apiTrend: 'up' | 'down'
  apiTrendValue: number
}

interface SystemAlert {
  id: string
  title: string
  message: string
  severity: 'info' | 'warning' | 'critical'
  timestamp: string
}

interface PerformanceData {
  cpu: string
  memory: string
  network: string
}

// ============================================================================
// STORE INITIALIZATION
// ============================================================================

const userStore = useUserStore()
const tenantStore = useTenantStore()
const auditLogStore = useAuditLogStore()
const { showToast } = useToast()

const { users: platformUsers } = storeToRefs(userStore)
const { tenants: platformTenants } = storeToRefs(tenantStore)
const { auditLogs: recentAuditLogs } = storeToRefs(auditLogStore)

// ============================================================================
// REACTIVE STATE
// ============================================================================

// Dashboard state
const isRefreshing = ref(false)
const isExporting = ref(false)
const isLoadingAuditLogs = ref(false)
const isGeneratingReport = ref(false)
const analyticsTimeframe = ref<'24h' | '7d' | '30d' | '90d'>('7d')
const showPerformanceMonitor = ref(false)

// System health data
const systemHealth = ref<SystemHealth>({
  status: 'healthy',
  serverUptime: '99.9%',
  dbResponseTime: 45,
  memoryUsage: 68,
  apiResponseTime: 120,
  apiTrend: 'up',
  apiTrendValue: 5.2
})

// System alerts
const systemAlerts = ref<SystemAlert[]>([
  {
    id: '1',
    title: 'High Memory Usage',
    message: 'Memory usage has exceeded 80% for the past 10 minutes',
    severity: 'warning',
    timestamp: new Date(Date.now() - 15 * 60 * 1000).toISOString()
  },
  {
    id: '2',
    title: 'Database Connection Pool',
    message: 'Connection pool utilization is at 95%',
    severity: 'critical',
    timestamp: new Date(Date.now() - 5 * 60 * 1000).toISOString()
  }
])

// Performance chart data
const performanceChartData = ref<PerformanceData>({
  cpu: '0,180 20,160 40,140 60,120 80,100 100,90 120,85 140,80 160,75 180,70 200,65 220,60 240,58 260,55 280,52 300,50 320,48 340,45 360,42 380,40',
  memory: '0,150 20,155 40,160 60,165 80,170 100,175 120,180 140,185 160,190 180,195 200,200 220,195 240,190 260,185 280,180 300,175 320,170 340,165 360,160 380,155',
  network: '0,100 20,105 40,110 60,115 80,120 100,125 120,130 140,135 160,140 180,145 200,150 220,145 240,140 260,135 280,130 300,125 320,120 340,115 360,110 380,105'
})

// Auto-refresh interval
let refreshInterval: NodeJS.Timeout | null = null

// ============================================================================
// COMPUTED PROPERTIES
// ============================================================================

const dashboardMetrics = computed(() => ({
  totalUsers: platformUsers.value.length,
  totalTenants: platformTenants.value.length,
  activeAlerts: systemAlerts.value.length,
  systemStatus: systemHealth.value.status
}))

// ============================================================================
// METHODS
// ============================================================================

const refreshDashboard = async () => {
  isRefreshing.value = true

  try {
    // Refresh all data sources
    await Promise.all([
      refreshSystemHealth(),
      refreshPlatformData(),
      refreshAuditLogs()
    ])

    showToast({
      type: 'success',
      title: 'Dashboard Refreshed',
      message: 'All data has been updated successfully'
    })
  } catch (error) {
    console.error('Failed to refresh dashboard:', error)
    showToast({
      type: 'error',
      title: 'Refresh Failed',
      message: 'Failed to refresh dashboard data'
    })
  } finally {
    isRefreshing.value = false
  }
}

const refreshSystemHealth = async () => {
  // Simulate API call to get system health
  await new Promise(resolve => setTimeout(resolve, 500))

  // Update system health with mock data (replace with real API call)
  systemHealth.value = {
    status: Math.random() > 0.8 ? 'warning' : 'healthy',
    serverUptime: `${(99.5 + Math.random() * 0.5).toFixed(1)}%`,
    dbResponseTime: Math.floor(30 + Math.random() * 50),
    memoryUsage: Math.floor(50 + Math.random() * 40),
    apiResponseTime: Math.floor(80 + Math.random() * 100),
    apiTrend: Math.random() > 0.5 ? 'up' : 'down',
    apiTrendValue: Math.floor(Math.random() * 20)
  }
}

const refreshPlatformData = async () => {
  try {
    // Fetch users and tenants data
    await Promise.all([
      userStore.fetchAllUsers({ limit: 100 }),
      tenantStore.fetchAllTenants({ limit: 100 })
    ])
  } catch (error) {
    console.error('Failed to refresh platform data:', error)
  }
}

const refreshAuditLogs = async () => {
  isLoadingAuditLogs.value = true

  try {
    await auditLogStore.fetchAuditLogs({
      limit: 10,
      sortBy: 'createdAt',
      sortOrder: 'desc'
    })
  } catch (error) {
    console.error('Failed to refresh audit logs:', error)
  } finally {
    isLoadingAuditLogs.value = false
  }
}

const exportReport = async () => {
  isExporting.value = true

  try {
    // Generate comprehensive report data
    const reportData = {
      timestamp: new Date().toISOString(),
      timeframe: analyticsTimeframe.value,
      systemHealth: systemHealth.value,
      metrics: dashboardMetrics.value,
      alerts: systemAlerts.value,
      users: platformUsers.value.slice(0, 100), // Limit for export
      tenants: platformTenants.value.slice(0, 100),
      auditLogs: recentAuditLogs.value
    }

    // Create and download CSV/JSON report
    const blob = new Blob([JSON.stringify(reportData, null, 2)], {
      type: 'application/json'
    })

    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `super-admin-report-${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)

    showToast({
      type: 'success',
      title: 'Report Exported',
      message: 'Dashboard report has been downloaded successfully'
    })
  } catch (error) {
    console.error('Failed to export report:', error)
    showToast({
      type: 'error',
      title: 'Export Failed',
      message: 'Failed to export dashboard report'
    })
  } finally {
    isExporting.value = false
  }
}

const togglePerformanceMonitor = () => {
  showPerformanceMonitor.value = !showPerformanceMonitor.value
}

const dismissAlert = (alertId: string) => {
  const index = systemAlerts.value.findIndex(alert => alert.id === alertId)
  if (index !== -1) {
    systemAlerts.value.splice(index, 1)

    showToast({
      type: 'info',
      title: 'Alert Dismissed',
      message: 'System alert has been dismissed'
    })
  }
}

// Activity helpers
const getActivityIcon = (action: string): string => {
  const iconMap: Record<string, string> = {
    'user.created': 'heroicons:user-plus',
    'user.updated': 'heroicons:user',
    'user.deleted': 'heroicons:user-minus',
    'tenant.created': 'heroicons:building-office',
    'tenant.updated': 'heroicons:building-office',
    'tenant.deleted': 'heroicons:building-office-2',
    'auth.login': 'heroicons:arrow-right-on-rectangle',
    'auth.logout': 'heroicons:arrow-left-on-rectangle',
    'system.config': 'heroicons:cog-6-tooth',
    'default': 'heroicons:information-circle'
  }

  return iconMap[action] || iconMap.default
}

const getActivityIconBg = (action: string): string => {
  const bgMap: Record<string, string> = {
    'user.created': 'bg-green-100 dark:bg-green-900/20',
    'user.updated': 'bg-blue-100 dark:bg-blue-900/20',
    'user.deleted': 'bg-red-100 dark:bg-red-900/20',
    'tenant.created': 'bg-purple-100 dark:bg-purple-900/20',
    'tenant.updated': 'bg-purple-100 dark:bg-purple-900/20',
    'tenant.deleted': 'bg-red-100 dark:bg-red-900/20',
    'auth.login': 'bg-green-100 dark:bg-green-900/20',
    'auth.logout': 'bg-orange-100 dark:bg-orange-900/20',
    'system.config': 'bg-gray-100 dark:bg-gray-900/20',
    'default': 'bg-gray-100 dark:bg-gray-900/20'
  }

  return bgMap[action] || bgMap.default
}

const getActivityIconColor = (action: string): string => {
  const colorMap: Record<string, string> = {
    'user.created': 'text-green-600',
    'user.updated': 'text-blue-600',
    'user.deleted': 'text-red-600',
    'tenant.created': 'text-purple-600',
    'tenant.updated': 'text-purple-600',
    'tenant.deleted': 'text-red-600',
    'auth.login': 'text-green-600',
    'auth.logout': 'text-orange-600',
    'system.config': 'text-gray-600',
    'default': 'text-gray-600'
  }

  return colorMap[action] || colorMap.default
}

const formatRelativeTime = (timestamp: string): string => {
  const now = new Date()
  const time = new Date(timestamp)
  const diffInSeconds = Math.floor((now.getTime() - time.getTime()) / 1000)

  if (diffInSeconds < 60) return `${diffInSeconds}s ago`
  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`
  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`
  return `${Math.floor(diffInSeconds / 86400)}d ago`
}

// Modal actions
const openCreateUserModal = () => {
  // Navigate to user creation page or open modal
  navigateTo('/dashboard/platform/users/create')
}

const openCreateTenantModal = () => {
  // Navigate to tenant creation page or open modal
  navigateTo('/dashboard/platform/tenants/create')
}

const openMaintenanceModal = () => {
  showToast({
    type: 'info',
    title: 'Maintenance Mode',
    message: 'Maintenance mode configuration will be available soon'
  })
}

const generateSystemReport = async () => {
  isGeneratingReport.value = true

  try {
    // Simulate report generation
    await new Promise(resolve => setTimeout(resolve, 2000))

    const reportData = {
      timestamp: new Date().toISOString(),
      systemHealth: systemHealth.value,
      platformMetrics: dashboardMetrics.value,
      performanceData: performanceChartData.value,
      alerts: systemAlerts.value,
      summary: {
        totalUsers: platformUsers.value.length,
        totalTenants: platformTenants.value.length,
        systemStatus: systemHealth.value.status,
        uptime: systemHealth.value.serverUptime
      }
    }

    // Create and download report
    const blob = new Blob([JSON.stringify(reportData, null, 2)], {
      type: 'application/json'
    })

    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `system-report-${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)

    showToast({
      type: 'success',
      title: 'Report Generated',
      message: 'System report has been generated and downloaded'
    })
  } catch (error) {
    console.error('Failed to generate system report:', error)
    showToast({
      type: 'error',
      title: 'Report Generation Failed',
      message: 'Failed to generate system report'
    })
  } finally {
    isGeneratingReport.value = false
  }
}

// ============================================================================
// LIFECYCLE HOOKS
// ============================================================================

onMounted(async () => {
  // Initialize dashboard data
  await refreshDashboard()

  // Set up auto-refresh interval (every 30 seconds)
  refreshInterval = setInterval(() => {
    refreshSystemHealth()
  }, 30000)
})

onUnmounted(() => {
  // Clean up intervals
  if (refreshInterval) {
    clearInterval(refreshInterval)
  }
})

// ============================================================================
// WATCHERS
// ============================================================================

watch(analyticsTimeframe, async (newTimeframe: '24h' | '7d' | '30d' | '90d') => {
  // Refresh data with new timeframe
  await refreshPlatformData()
})
</script>

<style scoped>
.super-admin-dashboard {
  /* Custom scrollbar for webkit browsers */
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
}

.super-admin-dashboard::-webkit-scrollbar {
  width: 6px;
}

.super-admin-dashboard::-webkit-scrollbar-track {
  background: transparent;
}

.super-admin-dashboard::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.5);
  border-radius: 3px;
}

.super-admin-dashboard::-webkit-scrollbar-thumb:hover {
  background-color: rgba(156, 163, 175, 0.7);
}

/* Animation for performance charts */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.system-health,
.platform-analytics,
.system-monitoring,
.recent-activity,
.quick-actions {
  animation: fadeInUp 0.6s ease-out;
}

/* Stagger animation delays */
.platform-analytics {
  animation-delay: 0.1s;
}

.system-monitoring {
  animation-delay: 0.2s;
}

.recent-activity {
  animation-delay: 0.3s;
}

.quick-actions {
  animation-delay: 0.4s;
}

/* Hover effects for interactive elements */
.bg-white:hover,
.dark .dark\\:bg-gray-800:hover {
  transform: translateY(-2px);
  transition: transform 0.2s ease-in-out;
}

/* Loading states */
.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}
</style>