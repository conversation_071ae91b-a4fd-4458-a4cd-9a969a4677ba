<template>
  <div class="space-y-6 py-6">
    <!-- Activity Overview Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <UiCard
        v-for="metric in activityMetrics"
        :key="metric.key"
        class="hover:shadow-lg transition-shadow duration-200"
        :class="metric.cardClass"
      >
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">
              {{ metric.label }}
            </p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white mt-1">
              {{ metric.value }}
            </p>
            <p
              v-if="metric.change"
              :class="[
                'text-xs mt-1 flex items-center gap-1',
                metric.changeType === 'positive' ? 'text-green-600 dark:text-green-400' :
                metric.changeType === 'negative' ? 'text-red-600 dark:text-red-400' :
                'text-gray-500 dark:text-gray-400'
              ]"
            >
              <Icon
                :name="metric.changeType === 'positive' ? 'material-symbols:trending-up' :
                       metric.changeType === 'negative' ? 'material-symbols:trending-down' :
                       'material-symbols:trending-flat'"
                class="h-3 w-3"
              />
              {{ metric.change }}
            </p>
          </div>
          <div
            :class="[
              'w-12 h-12 rounded-xl flex items-center justify-center',
              metric.iconBg
            ]"
          >
            <Icon :name="metric.icon" :class="['h-6 w-6', metric.iconColor]" />
          </div>
        </div>
      </UiCard>
    </div>

    <!-- Activity Chart -->
    <UiCard>
      <template #header>
        <div class="flex items-center justify-between">
          <div>
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Activity Timeline</h3>
            <p class="text-sm text-gray-500 dark:text-gray-400">User activity over the last 30 days</p>
          </div>
          <div class="flex items-center gap-2">
            <UiSelect
              v-model="selectedTimeRange"
              :options="timeRangeOptions"
              size="sm"
              @change="fetchActivityData"
            />
          </div>
        </div>
      </template>
      <div class="h-80">
        <LineChart
          :data="chartData"
          :categories="chartCategories"
          :key="colorMode.value"
          x-formatter="date"
          y-formatter="number"
          :grid="true"
          curve="smooth"
          :legend="{ position: 'top' }"
        />
      </div>
    </UiCard>

    <!-- Activity Types Distribution -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <UiCard>
        <template #header>
          <div>
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Activity Types</h3>
            <p class="text-sm text-gray-500 dark:text-gray-400">Distribution of user activities</p>
          </div>
        </template>
        <div class="h-64">
          <DonutChart
            :data="activityTypeData"
            :labels="activityTypeLabels"
            :hide-legend="true"
            :radius="0"
            :key="colorMode.value"
          />
          <div class="absolute inset-0 flex items-center justify-center">
            <div class="text-center">
              <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ totalActivities }}</p>
              <p class="text-sm text-gray-500 dark:text-gray-400">Total Activities</p>
            </div>
          </div>
        </div>
      </UiCard>

      <UiCard>
        <template #header>
          <div>
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Activity Summary</h3>
            <p class="text-sm text-gray-500 dark:text-gray-400">Key activity metrics</p>
          </div>
        </template>
        <div class="space-y-4">
          <div v-for="summary in activitySummary" :key="summary.type" class="flex items-center justify-between">
            <div class="flex items-center gap-3">
              <div :class="['w-3 h-3 rounded-full', summary.color]"></div>
              <span class="text-sm text-gray-700 dark:text-gray-300">{{ summary.label }}</span>
            </div>
            <div class="text-right">
              <p class="text-sm font-medium text-gray-900 dark:text-white">{{ summary.count }}</p>
              <p class="text-xs text-gray-500 dark:text-gray-400">{{ summary.percentage }}%</p>
            </div>
          </div>
        </div>
      </UiCard>
    </div>

    <!-- Detailed Activity Log -->
    <UiCard>
      <template #header>
        <div class="flex items-center justify-between">
          <div>
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Activity Log</h3>
            <p class="text-sm text-gray-500 dark:text-gray-400">Detailed user activity history</p>
          </div>
          <div class="flex items-center gap-2">
            <UiInput
              v-model="searchQuery"
              placeholder="Search activities..."
              size="sm"
              leading-icon="material-symbols:search"
              class="w-64"
            />
            <UiSelect
              v-model="selectedActivityType"
              :options="activityTypeFilterOptions"
              size="sm"
              @change="filterActivities"
            />
          </div>
        </div>
      </template>
      <div class="space-y-4">
        <div
          v-for="activity in filteredActivities"
          :key="activity.id"
          class="flex items-start gap-4 p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
        >
          <div class="flex-shrink-0 mt-1">
            <div
              :class="[
                'w-10 h-10 rounded-full flex items-center justify-center',
                getActivityIconBg(activity.type)
              ]"
            >
              <Icon
                :name="getActivityIcon(activity.type)"
                :class="['h-5 w-5', getActivityIconColor(activity.type)]"
              />
            </div>
          </div>
          <div class="flex-1 min-w-0">
            <div class="flex items-center justify-between mb-1">
              <h4 class="text-sm font-medium text-gray-900 dark:text-white">
                {{ activity.title }}
              </h4>
              <span class="text-xs text-gray-500 dark:text-gray-400">
                {{ fromNow(activity.timestamp) }}
              </span>
            </div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">
              {{ activity.description }}
            </p>
            <div class="flex items-center gap-4 text-xs text-gray-500 dark:text-gray-400">
              <span v-if="activity.ipAddress">IP: {{ activity.ipAddress }}</span>
              <span v-if="activity.userAgent">{{ activity.userAgent }}</span>
              <UiBadge :color="getActivityStatusColor(activity.status)" size="xs">
                {{ activity.status }}
              </UiBadge>
            </div>
          </div>
        </div>

        <!-- Empty State -->
        <div v-if="filteredActivities.length === 0" class="text-center py-12">
          <Icon name="material-symbols:history" class="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No Activities Found</h3>
          <p class="text-gray-500 dark:text-gray-400">
            {{ searchQuery ? 'Try adjusting your search criteria.' : 'No activities recorded for this user yet.' }}
          </p>
        </div>

        <!-- Load More -->
        <div v-if="hasMoreActivities" class="text-center pt-6">
          <UiButton
            variant="outline"
            @click="loadMoreActivities"
            :loading="isLoadingMore"
          >
            Load More Activities
          </UiButton>
        </div>
      </div>
    </UiCard>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { useUserStore } from '~/stores/user'
import { useColorMode } from '@vueuse/core'
import { PlatformRoles } from "~/app/features/auth/constants/roles";
import type { fromNow } from "utils/dateFormatters";

const route = useRoute()
const userStore = useUserStore()
const colorMode = useColorMode()

const userId = computed(() => route.params.id as string)
const user = computed(() => userStore.users.find(u => u.id === userId.value))

definePageMeta({
  layout: "dashboard",
  title: () => {
    const userStore = useUserStore();
    const userId = useRoute().params.id as string;
    const user = userStore.users.find(u => u.id === userId);
    return user ? `${user.name} - Activity` : "User Activity";
  },
  meta: () => ({ title: "User Activity" }),
  showPageHeader: true,
  showPageHeaderTitle: true,
  middleware: ["rbac"],
  roles: [PlatformRoles.SUPER_ADMIN],
  breadcrumbs: () => {
    const userStore = useUserStore();
    const userId = useRoute().params.id as string;
    const user = userStore.users.find(u => u.id === userId);
    if (!user) return [];
    return [
      { label: "Dashboard", href: "/dashboard" },
      { label: "Users", href: "/dashboard/users" },
      { label: user.name, href: `/dashboard/users/${userId}` },
      { label: "Activity", href: "" }
    ];
  }
});

// Reactive state
const selectedTimeRange = ref('30d')
const selectedActivityType = ref('all')
const searchQuery = ref('')
const isLoadingMore = ref(false)
const hasMoreActivities = ref(true)

// Mock data - replace with real API calls
const activities = ref([
  {
    id: '1',
    type: 'LOGIN',
    title: 'User Login',
    description: 'Successfully logged into the platform',
    timestamp: new Date().toISOString(),
    status: 'success',
    ipAddress: '***********',
    userAgent: 'Chrome 91.0'
  },
  {
    id: '2',
    type: 'CREATE_CASE',
    title: 'Case Created',
    description: 'Created new case: Smith vs. Johnson',
    timestamp: new Date(Date.now() - 3600000).toISOString(),
    status: 'success',
    ipAddress: '***********',
    userAgent: 'Chrome 91.0'
  },
  // Add more mock activities...
])

const timeRangeOptions = [
  { value: '7d', label: 'Last 7 days' },
  { value: '30d', label: 'Last 30 days' },
  { value: '90d', label: 'Last 90 days' },
  { value: '1y', label: 'Last year' },
]

const activityTypeFilterOptions = [
  { value: 'all', label: 'All Activities' },
  { value: 'LOGIN', label: 'Logins' },
  { value: 'CREATE_CASE', label: 'Case Creation' },
  { value: 'UPLOAD_DOCUMENT', label: 'Document Uploads' },
  { value: 'UPDATE_CASE', label: 'Case Updates' },
]

// Computed properties
const activityMetrics = computed(() => [
  {
    key: 'total_activities',
    label: 'Total Activities',
    value: activities.value.length,
    change: '+12%',
    changeType: 'positive',
    icon: 'material-symbols:activity-zone',
    iconBg: 'bg-blue-100 dark:bg-blue-900/20',
    iconColor: 'text-blue-600 dark:text-blue-400',
    cardClass: ''
  },
  {
    key: 'logins',
    label: 'Login Sessions',
    value: activities.value.filter(a => a.type === 'LOGIN').length,
    change: '+5%',
    changeType: 'positive',
    icon: 'material-symbols:login',
    iconBg: 'bg-green-100 dark:bg-green-900/20',
    iconColor: 'text-green-600 dark:text-green-400',
    cardClass: ''
  },
  {
    key: 'cases',
    label: 'Cases Created',
    value: activities.value.filter(a => a.type === 'CREATE_CASE').length,
    change: '+8%',
    changeType: 'positive',
    icon: 'material-symbols:folder',
    iconBg: 'bg-purple-100 dark:bg-purple-900/20',
    iconColor: 'text-purple-600 dark:text-purple-400',
    cardClass: ''
  },
  {
    key: 'documents',
    label: 'Documents Uploaded',
    value: activities.value.filter(a => a.type === 'UPLOAD_DOCUMENT').length,
    change: '0%',
    changeType: 'neutral',
    icon: 'material-symbols:upload',
    iconBg: 'bg-orange-100 dark:bg-orange-900/20',
    iconColor: 'text-orange-600 dark:text-orange-400',
    cardClass: ''
  }
])

const filteredActivities = computed(() => {
  let filtered = activities.value

  if (selectedActivityType.value !== 'all') {
    filtered = filtered.filter(a => a.type === selectedActivityType.value)
  }

  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(a => 
      a.title.toLowerCase().includes(query) ||
      a.description.toLowerCase().includes(query)
    )
  }

  return filtered
})

const totalActivities = computed(() => activities.value.length)

const chartData = computed(() => {
  // Mock chart data - replace with real data
  return Array.from({ length: 30 }, (_, i) => ({
    date: new Date(Date.now() - (29 - i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    value: Math.floor(Math.random() * 20) + 5
  }))
})

const chartCategories = computed(() => [
  { name: 'Activities', color: '#3B82F6' }
])

const activityTypeData = computed(() => {
  const types = activities.value.reduce((acc, activity) => {
    acc[activity.type] = (acc[activity.type] || 0) + 1
    return acc
  }, {} as Record<string, number>)

  return Object.values(types)
})

const activityTypeLabels = computed(() => {
  const types = activities.value.reduce((acc, activity) => {
    acc[activity.type] = (acc[activity.type] || 0) + 1
    return acc
  }, {} as Record<string, number>)

  return {
    labels: Object.keys(types),
    timestamp: new Date().toISOString()
  }
})

const activitySummary = computed(() => {
  const types = activities.value.reduce((acc, activity) => {
    acc[activity.type] = (acc[activity.type] || 0) + 1
    return acc
  }, {} as Record<string, number>)

  const total = activities.value.length
  const colors = ['bg-blue-500', 'bg-green-500', 'bg-purple-500', 'bg-orange-500', 'bg-red-500']

  return Object.entries(types).map(([type, count], index) => ({
    type,
    label: type.replace('_', ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase()),
    count,
    percentage: Math.round((count / total) * 100),
    color: colors[index % colors.length]
  }))
})

// Methods
const getActivityIcon = (type: string) => {
  const icons: Record<string, string> = {
    LOGIN: 'material-symbols:login',
    CREATE_CASE: 'material-symbols:folder',
    UPLOAD_DOCUMENT: 'material-symbols:upload',
    UPDATE_CASE: 'material-symbols:edit',
    LOGOUT: 'material-symbols:logout',
  }
  return icons[type] || 'material-symbols:help'
}

const getActivityIconBg = (type: string) => {
  const backgrounds: Record<string, string> = {
    LOGIN: 'bg-green-100 dark:bg-green-900/20',
    CREATE_CASE: 'bg-blue-100 dark:bg-blue-900/20',
    UPLOAD_DOCUMENT: 'bg-purple-100 dark:bg-purple-900/20',
    UPDATE_CASE: 'bg-orange-100 dark:bg-orange-900/20',
    LOGOUT: 'bg-gray-100 dark:bg-gray-900/20',
  }
  return backgrounds[type] || 'bg-gray-100 dark:bg-gray-900/20'
}

const getActivityIconColor = (type: string) => {
  const colors: Record<string, string> = {
    LOGIN: 'text-green-600 dark:text-green-400',
    CREATE_CASE: 'text-blue-600 dark:text-blue-400',
    UPLOAD_DOCUMENT: 'text-purple-600 dark:text-purple-400',
    UPDATE_CASE: 'text-orange-600 dark:text-orange-400',
    LOGOUT: 'text-gray-600 dark:text-gray-400',
  }
  return colors[type] || 'text-gray-600 dark:text-gray-400'
}

const getActivityStatusColor = (status: string) => {
  const colors: Record<string, string> = {
    success: 'success',
    warning: 'warning',
    error: 'error',
    info: 'info'
  }
  return colors[status] || 'neutral'
}

const fetchActivityData = async () => {
  // Fetch activity data based on selected time range
  console.log('Fetching activity data for:', selectedTimeRange.value)
}

const filterActivities = () => {
  // Filter activities based on selected type
  console.log('Filtering activities by type:', selectedActivityType.value)
}

const loadMoreActivities = async () => {
  isLoadingMore.value = true
  try {
    // Load more activities
    await new Promise(resolve => setTimeout(resolve, 1000))
    // hasMoreActivities.value = false // Set to false when no more data
  } finally {
    isLoadingMore.value = false
  }
}

onMounted(async () => {
  // Fetch initial data
  await fetchActivityData()
})
</script>
