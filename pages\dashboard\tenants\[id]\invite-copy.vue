<template>
  <div class="space-y-6 py-6">

    <!-- Main Content -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- Invite Form -->
      <div class="lg:col-span-2">
        <UiCard :title="$t('tenantDetail.sendInvitations')" :subtitle="$t('tenantDetail.inviteUsersDescription')"
          icon="fluent:send-person-16-regular" icon-class="text-green-600 dark:text-green-400">
          <template #header>
            <div class="flex items-center justify-between">

              <UiBadge v-if="pendingInvites.length > 0" color="warning" variant="outline">
                {{ pendingInvites.length }} pending
              </UiBadge>
            </div>
          </template>

          <form @submit.prevent="handleSubmit" class="space-y-6">
            <!-- Email Input Section -->
            <div>
              <label class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
                Email Addresses
                <span class="text-red-500">*</span>
              </label>
              <div class="space-y-3">
                <div v-for="(email, index) in formData.emails" :key="index" class="flex items-start gap-3">
                  <div class="flex-1">
                    <UiInput :id="`email-${index}`" :name="`email-${index}`" v-model="formData.emails[index]"
                      type="email" placeholder="Enter email address" :error="emailErrors[index]"
                      leading-icon="material-symbols:mail" required />
                  </div>
                  <div class="flex items-center gap-2 pt-2">
                    <UiButton v-if="formData.emails.length > 1" type="button" variant="ghost" size="sm" shape="circle"
                      @click="removeEmail(index)" aria-label="Remove email">
                      <Icon name="material-symbols:close" class="h-4 w-4" />
                    </UiButton>
                  </div>
                </div>

                <!-- Add Email Button -->
                <div class="flex items-center gap-3">
                  <UiButton type="button" variant="ghost" size="sm" leading-icon="material-symbols:add"
                    @click="addEmail" class="flex-1">
                    Add Another Email
                  </UiButton>
                  <UiButton type="button" variant="outline" size="sm" leading-icon="material-symbols:upload-file"
                    @click="showBulkImport = true">
                    Import CSV
                  </UiButton>
                </div>
              </div>
              <p class="text-sm text-gray-500 dark:text-gray-400 mt-2">
                Add multiple email addresses to send bulk invitations to this tenant.
              </p>
            </div>

            <!-- Role Selection -->
            <div>
              <UiSelect id="role" v-model="formData.role" label="Assign Role"
                placeholder="Select a role for the invited users" :options="roleOptions" required
                leading-icon="material-symbols:admin-panel-settings"
                help-text="All invited users will be assigned this role within the tenant" />
            </div>

            <!-- Personal Message -->
            <div>
              <UiTextarea id="personalMessage" v-model="formData.personalMessage" label="Personal Message (Optional)"
                placeholder="Add a personal welcome message for the invited users..." :rows="4"
                help-text="This message will be included in the invitation email to provide context" />
            </div>

            <!-- Invitation Settings -->
            <div class="space-y-4">
              <h4 class="text-sm font-semibold text-gray-700 dark:text-gray-300">Invitation Settings</h4>
              <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <UiCheckbox id="sendWelcomeEmail" v-model="formData.sendWelcomeEmail" label="Send welcome email" />
                <UiCheckbox id="requirePasswordReset" v-model="formData.requirePasswordReset"
                  label="Require password reset" />
                <UiCheckbox id="notifyOnAccept" v-model="formData.notifyOnAccept" label="Notify me when accepted" />
                <UiCheckbox id="setExpiration" v-model="formData.setExpiration" label="Set expiration date" />
              </div>

              <!-- Expiration Date -->
              <div v-if="formData.setExpiration" class="mt-4">
                <UiInput id="expirationDate" v-model="formData.expirationDate" type="date" label="Expiration Date"
                  :min="minExpirationDate" help-text="Invitations will expire on this date" />
              </div>
            </div>

            <!-- Error Display -->
            <div v-if="error" class="mt-4">
              <UiAlert type="error" :title="error">
                Please review the form and try again.
              </UiAlert>
            </div>

            <!-- Success Display -->
            <div v-if="successMessage" class="mt-4">
              <UiAlert type="success" :title="successMessage">
                Invitations have been sent successfully.
              </UiAlert>
            </div>

            <!-- Form Actions -->
            <div class="flex flex-col sm:flex-row gap-3 pt-6 border-t border-gray-200 dark:border-gray-700">
              <UiButton type="submit" :loading="isSubmitting" :disabled="!canSubmit" class="flex-1 sm:flex-none">
                <template v-if="validEmailCount > 1">
                  Send {{ validEmailCount }} Invitations
                </template>
                <template v-else>
                  Send Invitation
                </template>
              </UiButton>
              <UiButton type="button" variant="secondary" @click="resetForm" :disabled="isSubmitting">
                Reset
              </UiButton>
              <UiButton type="button" variant="ghost" @click="previewInvitation" :disabled="isSubmitting">
                Preview
              </UiButton>
            </div>
          </form>
        </UiCard>
      </div>

      <!-- Sidebar -->
      <div class="space-y-6">
        <!-- Current Users -->
        <UiCard :title="$t('tenantDetail.currentUsers')" :subtitle="$t('tenantDetail.currentUsersDescription')"
          icon="i-heroicons:users" icon-class="text-blue-600 dark:text-blue-400">
          <template #header>
            <div class="flex items-center justify-between">

              <UiBadge variant="neutral">{{ currentUsers.length }}</UiBadge>
            </div>
          </template>
          <div class="space-y-3">
            <div v-for="user in currentUsers.slice(0, 5)" :key="user.id"
              class="flex items-center gap-3 p-2 rounded-lg bg-gray-50 dark:bg-gray-700/50">
              <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center">
                <Icon name="heroicons:user-circle" size="calc(var(--spacing) * 7)"
                  class="text-blue-600 dark:text-blue-400" />
              </div>
              <div class="flex-1 min-w-0">
                <p class="text-sm font-medium text-gray-900 dark:text-white truncate">
                  {{ user.name }}
                </p>
                <p class="text-xs text-gray-500 dark:text-gray-400">
                  {{ user.role }}
                </p>
              </div>
            </div>
            <div v-if="currentUsers.length === 0" class="text-center py-4">
              <p class="text-sm text-gray-500 dark:text-gray-400">No users yet</p>
            </div>
            <div v-if="currentUsers.length > 5" class="text-center pt-2">
              <UiButton variant="ghost" size="sm" @click="viewTenantUsers">
                View All {{ currentUsers.length }} Users
              </UiButton>
            </div>
          </div>
        </UiCard>

        <!-- Pending Invitations -->
        <UiCard :title="$t('tenantDetail.pendingInvitations')" :subtitle="$t('tenantDetail.pendingInvitationsDescription')"
          icon="material-symbols:schedule-send-outline" :icon-props="{ color: 'gray', background: 'warning' }">
          <template #header>
            <div class="flex items-center justify-between">
      
              <UiBadge  size="xs" variant="outline"  color="warning">{{ pendingInvites.length }}</UiBadge>
            </div>
          </template>
          <div class="space-y-3">
            <div v-for="invite in pendingInvites.slice(0, 5)" :key="invite.id"
              class="flex items-center justify-between p-3 bg-yellow-50 dark:bg-yellow-900/10 rounded-lg border border-yellow-200 dark:border-yellow-800">
              <div class="flex-1 min-w-0">
                <p class="text-sm font-medium text-gray-900 dark:text-white truncate">
                  {{ invite.email }}
                </p>
                <p class="text-xs text-gray-500 dark:text-gray-400">
                  {{ invite.role }} • {{ formatDate(invite.sentAt) }}
                </p>
              </div>
              <div class="flex items-center gap-2">
                <UiButton variant="ghost" size="sm" shape="circle" @click="resendInvite(invite.id)"
                  aria-label="Resend invitation">
                  <Icon name="material-symbols:refresh" class="h-4 w-4" />
                </UiButton>
                <UiButton variant="ghost" size="sm" shape="circle" @click="cancelInvite(invite.id)"
                  aria-label="Cancel invitation">
                  <Icon name="material-symbols:close" class="h-4 w-4" />
                </UiButton>
              </div>
            </div>
            <div v-if="pendingInvites.length === 0" class="text-center py-4">
              <p class="text-sm text-gray-500 dark:text-gray-400">No pending invitations</p>
            </div>
          </div>
        </UiCard>
 
      </div>
    </div>

    <!-- Bulk Import Modal -->
    <UiModal v-model:show="showBulkImport" title="Import Email Addresses" width="lg">
      <div class="space-y-4">
        <p class="text-sm text-gray-600 dark:text-gray-400">
          Upload a CSV file or paste email addresses to send bulk invitations.
        </p>
        <UiTextarea id="bulkEmails" v-model="bulkEmailText" placeholder="Paste email addresses here, one per line..."
          :rows="8" label="Email Addresses" />
        <div class="text-xs text-gray-500 dark:text-gray-400">
          <p class="font-medium mb-1">Supported formats:</p>
          <p>• One email per line</p>
          <p>• Comma-separated emails</p>
          <p>• CSV format: email,role (role is optional)</p>
        </div>
      </div>
      <template #footer>
        <div class="flex justify-end gap-3">
          <UiButton variant="secondary" @click="showBulkImport = false">
            Cancel
          </UiButton>
          <UiButton @click="processBulkEmails">
            Import Emails
          </UiButton>
        </div>
      </template>
    </UiModal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useTenantStore, type TenantInvitePayload, type Tenant } from '~/stores/tenant'
import { PlatformRoles, TenantRoles } from '~/app/features/auth/constants/roles'
import { useUserStore } from '~/stores/user'


// Page meta
definePageMeta({
  layout: 'dashboard',
  showPageHeader: true,
  showPageHeaderTitle: true,
  pageHeaderItemStats: () => {
    const tenant = useTenantStore().selectedTenant;
    if (!tenant) return '';

    return tenant.isActive ? { key: "active", label: "Active", color: "green" } : { key: "inactive", label: "Inactive", color: "red" };
  },
  title: () => {
    const tenant = useTenantStore().selectedTenant;
    if (!tenant) return '';
    return 'Invite Users to ' + tenant.name;
  },
  description: 'Send invitations to new users and manage their roles',
  pageHeaderIcon: 'i-heroicons:user-plus',
  pageHeaderActions: () => {
    const tenant = useTenantStore().selectedTenant;
    if (!tenant) return [];
    return [
      {
        label: "Send invitations",
        action: () => router.push({ name: 'dashboard.tenants.invite' }),
        icon: "material-symbols:send-rounded",
        disabled: !tenant.isActive,
        disabledText: "Tenant is not active"
      },
      {
        label: "Reset",
        action: () => router.push({ name: 'dashboard.tenants.invite' }),
        icon: "material-symbols:refresh",
        disabled: !tenant.isActive,
        disabledText: "Tenant is not active",
        variant: "secondary"
      },
      {
        label: "Back to Tenant",
        action: () => router.push({ name: 'dashboard.tenants.id', params: { id: tenant.id } }),
        icon: "i-heroicons:arrow-left",
        variant: "flat"
      },
    ].reverse();
  },
  pageHeaderTags: () => {
    const tenant = useTenantStore().selectedTenant;
    if (!tenant) return [];
    return [
      {
        key: "plan",
        label: tenant.plan,
        class: "text-purple-600",
        icon: "i-material-symbols:workspace-premium",
      },
      {
        key: "locale",
        label: tenant.locale,
        class: "text-brandSecondary",
        icon: "i-heroicons:globe-alt",
      },
      {
        key: "timezone",
        label: tenant.timezone,
        class: "text-brandPrimary",
        icon: "i-heroicons:clock",
      },
       {
         key: "createdAt",
        label: formatDate(tenant.createdAt),
        class: "text-brandPrimary",
        icon: "i-material-symbols:calendar-today",
      },
    ];
  },
  middleware: ['rbac'],
  roles: [PlatformRoles.SUPER_ADMIN],
  breadcrumbs: () => {
    const tenant = useTenantStore().selectedTenant;
    if (!tenant) return [];
    return [
      {
      }
    ];
  },

  middleware: ['rbac'],
  roles: [PlatformRoles.SUPER_ADMIN],
  breadcrumbs: () => {
    const tenant = useTenantStore().selectedTenant;
    if (!tenant) return [];
    return [
      { label: 'Dashboard', href: '/dashboard' },
      { label: 'Tenants', href: '/dashboard/tenants' },
      { label: tenant.name, href: '/dashboard/tenants/' + tenant.id },
      { label: 'Invite Users' },
    ];
  }
})

// Composables
const route = useRoute()
const router = useRouter()
const tenantStore = useTenantStore()

// Get tenant ID from route
const tenantId = computed(() => route.params.id as string)

// Form data interface
interface InviteFormData {
  emails: string[]
  role: string
  personalMessage: string
  sendWelcomeEmail: boolean
  requirePasswordReset: boolean
  notifyOnAccept: boolean
  setExpiration: boolean
  expirationDate: string
}

// Reactive state
const formData = ref<InviteFormData>({
  emails: [''],
  role: '',
  personalMessage: '',
  sendWelcomeEmail: true,
  requirePasswordReset: true,
  notifyOnAccept: true,
  setExpiration: false,
  expirationDate: '',
})

const isSubmitting = ref(false)
const error = ref('')
const successMessage = ref('')
const emailErrors = ref<string[]>([])
const showBulkImport = ref(false)
const bulkEmailText = ref('')

// Mock data (replace with real data from store)
const currentUsers = computed(() => useUserStore().users)

const pendingInvites = computed(() => [])

// Computed properties
const selectedTenant = computed(() => tenantStore.selectedTenant)

const roleOptions = computed(() => [
  { value: TenantRoles.ADMIN, label: 'Administrator' },
  { value: TenantRoles.LAWYER, label: 'Lawyer' },
  { value: TenantRoles.PARALEGAL, label: 'Paralegal' },
  { value: TenantRoles.CLIENT, label: 'Client' },
])

const validEmailCount = computed(() => {
  return formData.value.emails.filter(email => email.trim()).length
})

const canSubmit = computed(() => {
  return (
    formData.value.role &&
    validEmailCount.value > 0 &&
    !isSubmitting.value
  )
})

const minExpirationDate = computed(() => {
  const tomorrow = new Date()
  tomorrow.setDate(tomorrow.getDate() + 1)
  return tomorrow.toISOString().split('T')[0]
})

// Methods
const addEmail = () => {
  formData.value.emails.push('')
}

const removeEmail = (index: number) => {
  if (formData.value.emails.length > 1) {
    formData.value.emails.splice(index, 1)
    emailErrors.value.splice(index, 1)
  }
}

const validateEmails = () => {
  emailErrors.value = []
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/

  formData.value.emails.forEach((email, index) => {
    if (email.trim() && !emailRegex.test(email.trim())) {
      emailErrors.value[index] = 'Please enter a valid email address'
    }
  })

  return emailErrors.value.every(error => !error)
}

const handleSubmit = async () => {
  error.value = ''
  successMessage.value = ''

  if (!validateEmails()) {
    error.value = 'Please fix email validation errors'
    return
  }

  const validEmails = formData.value.emails.filter(email => email.trim())
  if (validEmails.length === 0) {
    error.value = 'Please enter at least one email address'
    return
  }

  isSubmitting.value = true

  try {
    // Send invitations for each email
    const invitePromises = validEmails.map(email => {
      const payload: TenantInvitePayload = {
        email: email.trim(),
        role: formData.value.role,
        tenantId: tenantId.value,
      }
      return tenantStore.sendInvitesToTenant(payload)
    })

    await Promise.all(invitePromises)

    successMessage.value = `Successfully sent ${validEmails.length} invitation${validEmails.length > 1 ? 's' : ''}`

    // Reset form after successful submission
    setTimeout(() => {
      resetForm()
    }, 2000)

  } catch (err: any) {
    error.value = err.message || 'Failed to send invitations'
  } finally {
    isSubmitting.value = false
  }
}

const resetForm = () => {
  formData.value = {
    emails: [''],
    role: '',
    personalMessage: '',
    sendWelcomeEmail: true,
    requirePasswordReset: true,
    notifyOnAccept: true,
    setExpiration: false,
    expirationDate: '',
  }
  emailErrors.value = []
  error.value = ''
  successMessage.value = ''
}

const processBulkEmails = () => {
  if (!bulkEmailText.value.trim()) return

  // Parse emails from text (handle different formats)
  const emails = bulkEmailText.value
    .split(/[\n,;]/)
    .map(email => email.trim())
    .filter(email => email && email.includes('@'))

  // Replace current emails with parsed ones
  formData.value.emails = emails.length > 0 ? emails : ['']

  showBulkImport.value = false
  bulkEmailText.value = ''
}

const previewInvitation = () => {
  // Implementation for previewing invitation
  console.log('Previewing invitation...', formData.value)
}

const goBackToTenant = () => {
  router.push(`/dashboard/tenants/${tenantId.value}`)
}

const viewTenantUsers = () => {
  router.push(`/dashboard/tenants/${tenantId.value}/users`)
}

const editTenantSettings = () => {
  router.push(`/dashboard/tenants/${tenantId.value}/edit`)
}

const viewInviteHistory = () => {
  router.push(`/dashboard/tenants/${tenantId.value}/invites/history`)
}

const resendInvite = async (inviteId: string) => {
  // Implementation for resending invite
  console.log('Resending invite:', inviteId)
}

const cancelInvite = async (inviteId: string) => {
  // Implementation for canceling invite
  console.log('Canceling invite:', inviteId)
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString()
}

// Lifecycle
onMounted(async () => {
  await useUserStore().fetchAllUsers({ tenantId: tenantId.value });
 
  
  // Fetch tenant details if not already loaded
  if (!selectedTenant.value || selectedTenant.value.id !== tenantId.value) {
    await tenantStore.fetchTenantById(tenantId.value)
  }
})

// Watch for tenant store errors
watch(
  () => tenantStore.error,
  (newError) => {
    if (newError) {
      error.value = newError
    }
  }
)
</script>