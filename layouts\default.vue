<template>
  <div
    class="flex flex-col min-h-screen bg-gray-50 text-gray-800 antialiased"
    :dir="dir"
    :class="{ 'rtl': isRTL }"
  >
    <!-- Skip to main content link for accessibility -->
    <a
      href="#main-content"
      class="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-white px-4 py-2 rounded-md shadow-lg z-50 text-brand-primary font-medium"
    >
      Skip to main content
    </a>

    <!-- Enhanced TopBar with theme colors -->
    <TopBar />

    <!-- Loading indicator -->
    <div v-if="pending" class="fixed top-0 left-0 right-0 z-50">
      <div class="h-1 bg-gradient-to-r from-brandPrimary-500 to-brandSecondary-500 animate-pulse"></div>
    </div>

    <!-- Main content area -->
    <main
      id="main-content"
      class="flex-grow relative"
      role="main"
      :class="mainContentClass"
    >
      <!-- Page transition wrapper -->
      <div class="min-h-full">
        <NuxtPage />
      </div>
    </main>

    <!-- Back to top button -->
    <Transition
      enter-active-class="transition-all duration-300 ease-out"
      enter-from-class="opacity-0 transform translate-y-2"
      enter-to-class="opacity-100 transform translate-y-0"
      leave-active-class="transition-all duration-200 ease-in"
      leave-from-class="opacity-100 transform translate-y-0"
      leave-to-class="opacity-0 transform translate-y-2"
    >
      <button
        v-if="showBackToTop"
        @click="scrollToTop"
        class="fixed bottom-6 right-6 z-40 p-3 bg-brandPrimary-600 hover:bg-brandPrimary-700 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-brandPrimary-500 focus:ring-offset-2"
        aria-label="Back to top"
      >
        <Icon name="material-symbols:keyboard-arrow-up" class="w-6 h-6" />
      </button>
    </Transition>

    <!-- Enhanced Footer -->
    <Footer />

    <!-- Cookie consent banner (if needed) -->
    <CookieConsent v-if="showCookieConsent" @accept="acceptCookies" @decline="declineCookies" />
  </div>
</template>

<script setup lang="ts">
import TopBar from '~/components/public/TopBar.vue'
import Footer from '~/components/public/Footer.vue'
import CookieConsent from '~/components/public/CookieConsent.vue'

// Composables
const route = useRoute()
const { pending } = useLazyAsyncData('layout-data', () => Promise.resolve({}))
const { setupCommonStructuredData } = useSEO()
const { locale } = useI18n()

// Direction helpers
const dir = computed(() => {
  const rtlLocales = ['he', 'ar']
  return rtlLocales.includes(locale.value) ? 'rtl' : 'ltr'
})
const isRTL = computed(() => dir.value === 'rtl')

// Reactive state
const showBackToTop = ref(false)
const showCookieConsent = ref(false)

// Computed properties
const mainContentClass = computed(() => {
  const baseClass = 'transition-all duration-300 ease-in-out'

  // Add specific classes based on route
  if (route.path === '/') {
    return `${baseClass} bg-gradient-to-b from-gray-50 to-white`
  }

  return baseClass
})

// Methods
const scrollToTop = () => {
  window.scrollTo({
    top: 0,
    behavior: 'smooth'
  })
}

const acceptCookies = () => {
  showCookieConsent.value = false
  // Store cookie consent
  if (typeof window !== 'undefined') {
    localStorage.setItem('cookie-consent', 'accepted')
  }
}

const declineCookies = () => {
  showCookieConsent.value = false
  // Store cookie decline
  if (typeof window !== 'undefined') {
    localStorage.setItem('cookie-consent', 'declined')
  }
}

// Scroll event handler
const handleScroll = () => {
  showBackToTop.value = window.scrollY > 300
}

// Lifecycle hooks
onMounted(() => {
  // Setup scroll listener for back to top button
  window.addEventListener('scroll', handleScroll, { passive: true })

  // Check cookie consent
  if (typeof window !== 'undefined') {
    const consent = localStorage.getItem('cookie-consent')
    if (!consent) {
      setTimeout(() => {
        showCookieConsent.value = true
      }, 2000) // Show after 2 seconds
    }
  }

  // Setup common structured data
  setupCommonStructuredData()
})

onUnmounted(() => {
  if (typeof window !== 'undefined') {
    window.removeEventListener('scroll', handleScroll)
  }
})

// Enhanced SEO and meta tags for the default layout
useHead(() => ({
  htmlAttrs: {
    lang: locale.value,
    dir: dir.value,
    class: 'scroll-smooth'
  },
  bodyAttrs: {
    class: 'overflow-x-hidden font-sans'
  },
  meta: [
    // Viewport and mobile optimization
    { name: 'viewport', content: 'width=device-width, initial-scale=1, viewport-fit=cover' },

    // Theme and branding
    { name: 'theme-color', content: '#1a56db' },
    { name: 'msapplication-TileColor', content: '#1a56db' },
    { name: 'apple-mobile-web-app-capable', content: 'yes' },
    { name: 'apple-mobile-web-app-status-bar-style', content: 'default' },

    // Security headers
    { 'http-equiv': 'X-Content-Type-Options', content: 'nosniff' },
    { 'http-equiv': 'X-Frame-Options', content: 'DENY' },
    { 'http-equiv': 'X-XSS-Protection', content: '1; mode=block' },

    // Performance hints
    { name: 'format-detection', content: 'telephone=no' },

    // Social and SEO
    { property: 'og:site_name', content: 'LegalFlow' },
    { property: 'og:locale', content: locale.value === 'en' ? 'en_US' : locale.value === 'he' ? 'he_IL' : 'ar_SA' },
    { name: 'twitter:site', content: '@legalflow' },
    { name: 'twitter:creator', content: '@legalflow' }
  ],
  link: [
    // Favicon and app icons
    { rel: 'icon', type: 'image/x-icon', href: '/favicon.ico' },
    { rel: 'apple-touch-icon', sizes: '180x180', href: '/apple-touch-icon.png' },
    { rel: 'icon', type: 'image/png', sizes: '32x32', href: '/favicon-32x32.png' },
    { rel: 'icon', type: 'image/png', sizes: '16x16', href: '/favicon-16x16.png' },
    { rel: 'manifest', href: '/site.webmanifest' },

    // Preconnect to external domains for performance
    { rel: 'preconnect', href: 'https://fonts.googleapis.com' },
    { rel: 'preconnect', href: 'https://fonts.gstatic.com', crossorigin: '' },

    // DNS prefetch for common external resources
    { rel: 'dns-prefetch', href: '//www.google-analytics.com' },
    { rel: 'dns-prefetch', href: '//fonts.googleapis.com' }
  ]
}))

// Page-specific head management
watch(() => route.path, () => {
  // Update page-specific meta tags based on route
  nextTick(() => {
    // Any route-specific head updates can go here
  })
})
</script>

<style scoped>
/* Enhanced layout styles with theme integration */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.focus\:not-sr-only:focus {
  position: static;
  width: auto;
  height: auto;
  padding: inherit;
  margin: inherit;
  overflow: visible;
  clip: auto;
  white-space: normal;
}

/* Smooth scrolling enhancement */
@media (prefers-reduced-motion: no-preference) {
  html {
    scroll-behavior: smooth;
  }
}

/* Loading indicator animation */
@keyframes loading-pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* Custom focus styles for better accessibility */
/* *:focus-visible {
  outline: 2px solid var(--color-brandPrimary-600);
  outline-offset: 2px;
  border-radius: 0.25rem;
} */

/* RTL Support */
.rtl {
  direction: rtl;
}

.rtl .fixed.bottom-6.right-6 {
  right: auto;
  left: 1.5rem;
}

.rtl .focus\:not-sr-only:focus {
  left: auto;
  right: 1rem;
}

/* Print styles */
@media print {
  .fixed,
  .sticky {
    position: static !important;
  }

  .no-print {
    display: none !important;
  }
}
</style>