<template>
  <div v-bind="$attrs">
   
    <!-- Header Section -->

    <div v-if=" $slots['header-area']" >
      <slot name="header-area"></slot>
    </div>

    <div v-else-if="title || description || $slots['header-search-filter-area'] || $slots['header-actions']" class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
      <div class="flex flex-wrap items-start justify-between -mt-2 -ml-4 sm:flex-nowrap">
        <div class="mt-2 ml-4 flex-grow">
          <slot name="header-title">
            <h3 v-if="title" class="text-xl leading-6 font-semibold text-gray-900 dark:text-white">
              {{ title }}
            </h3>
          </slot>
          <slot name="header-description">
            <p v-if="description" class="mt-1 text-sm text-gray-600 dark:text-gray-400">
              {{ description }}
            </p>
          </slot>
          <div v-if="$slots['header-search-filter-area']" class="mt-4 flex flex-wrap gap-x-4 gap-y-2">
            <slot name="header-search-filter-area"></slot>
          </div>
        </div>
        <div v-if="$slots['header-actions']" class="mt-2 ml-4 flex-shrink-0">
          <slot name="header-actions"></slot>
        </div>
      </div>
    </div>

    <!-- Bulk Actions Bar -->
    <div v-if="selectable && selectedItems.length > 0" class="bg-blue-50 dark:bg-blue-900/20 px-6 py-4 border-b border-blue-200 dark:border-blue-800">
      <div class="flex items-center justify-between">
        <div class="flex items-center gap-3">
          <Icon name="material-symbols:check-circle" class="h-5 w-5 text-blue-600" />
          <span class="text-sm font-medium text-blue-900 dark:text-blue-100">
            {{ selectedItems.length }} {{ selectedItems.length === 1 ? 'item' : 'items' }} selected
          </span>
        </div>
        <div class="flex items-center gap-2">
          <slot name="bulk-actions" :selected-items="selectedItems" :clear-selection="clearSelection">
            <UiButton @click="$emit('bulk-action', { action: 'export', items: selectedItems })" size="sm" variant="outline">
              <Icon name="material-symbols:download" class="h-4 w-4 mr-1" />
              Export
            </UiButton>
          </slot>
          <UiButton v-if="columnCustomization" @click="showColumnCustomization = true" size="sm" variant="outline">
            <Icon name="material-symbols:view-column" class="h-4 w-4 mr-1" />
            Columns
          </UiButton>
          <UiButton @click="clearSelection" size="sm" variant="ghost">
            <Icon name="material-symbols:close" class="h-4 w-4" />
          </UiButton>
        </div>
      </div>
    </div>

    <!-- Table -->
    <div class="overflow-x-auto">
      <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
        <thead class="bg-gray-50 dark:bg-gray-800/50">
          <tr>
            <!-- Select All Checkbox -->
            <th v-if="selectable" class="px-6 py-3 text-left">
              <input
                type="checkbox"
                :checked="isAllSelected"
                :indeterminate="isIndeterminate"
                @change="toggleSelectAll"
                class="h-4 w-4 text-brandPrimary focus:ring-brandPrimary border-gray-300 rounded"
              />
            </th>

            <!-- Expand/Collapse Column -->
            <th v-if="expandable" class="px-6 py-3 text-left w-12">
              <span class="sr-only">Expand</span>
            </th>

            <!-- Dynamic Headers -->
            <th
              v-for="header in visibleHeaders"
              :key="header.key"
              scope="col"
              :class="[
                'px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider',
                header.sortable ? 'cursor-pointer hover:text-gray-700 dark:hover:text-gray-200' : '',
                header.class,
              ]"
              @click="header.sortable ? handleSort(header.key) : null"
            >
              <div class="flex items-center gap-2 group">
                <span class="transition-colors duration-200 group-hover:text-gray-700 dark:group-hover:text-gray-200">
                  {{ header.label }}
                </span>

                <!-- Enhanced Sorting Indicators -->
                <div v-if="header.sortable" class="flex items-center">
                  <div class="relative">
                    <!-- Sort Icon Container -->
                    <div class="flex flex-col items-center justify-center w-4 h-4">
                      <Icon
                        name="material-symbols:keyboard-arrow-up"
                        :class="[
                          'h-2.5 w-2.5 transition-all duration-200 ease-in-out',
                          currentSort.key === header.key && currentSort.direction === 'asc'
                            ? 'text-brandPrimary scale-110 -translate-y-0.5'
                            : 'text-gray-300 dark:text-gray-600 group-hover:text-gray-400 dark:group-hover:text-gray-500'
                        ]"
                      />
                      <Icon
                        name="material-symbols:keyboard-arrow-down"
                        :class="[
                          'h-2.5 w-2.5 transition-all duration-200 ease-in-out -mt-1',
                          currentSort.key === header.key && currentSort.direction === 'desc'
                            ? 'text-brandPrimary scale-110 translate-y-0.5'
                            : 'text-gray-300 dark:text-gray-600 group-hover:text-gray-400 dark:group-hover:text-gray-500'
                        ]"
                      />
                    </div>

                    <!-- Active Sort Indicator -->
                    <div
                      v-if="currentSort.key === header.key"
                      class="absolute -inset-1 bg-brandPrimary-100 dark:bg-brandPrimary-900/30 rounded-full animate-pulse"
                    />
                  </div>
                </div>

                <!-- Enhanced Filter Icon -->
                <Icon
                  v-if="header.filterable"
                  name="material-symbols:filter-list"
                  class="h-3.5 w-3.5 text-gray-400 hover:text-brandPrimary cursor-pointer transition-all duration-200 ease-in-out hover:scale-110"
                  @click.stop="toggleColumnFilter(header.key)"
                />
              </div>
            </th>
          </tr>
        </thead>
        <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
          <!-- Enhanced Loading State with Skeleton -->
          <template v-if="loading">
            <tr v-for="n in (loadingRows || 5)" :key="`skeleton-${n}`" class="animate-pulse">
              <!-- Selection Checkbox Skeleton -->
              <td v-if="selectable" class="px-6 py-4">
                <div class="h-4 w-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
              </td>

              <!-- Expand Button Skeleton -->
              <td v-if="expandable" class="px-6 py-4">
                <div class="h-4 w-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
              </td>

              <!-- Data Cell Skeletons -->
              <td v-for="header in visibleHeaders" :key="`skeleton-${n}-${header.key}`" class="px-6 py-4">
                <div class="space-y-2">
                  <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded" :style="{ width: getSkeletonWidth(header) }"></div>
                  <div v-if="n === 1" class="h-3 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
                </div>
              </td>
            </tr>

            <!-- Loading overlay for better UX -->
            <!-- <tr>
              <td :colspan="totalColumns" class="px-6 py-8 text-center bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm">
                <div class="flex flex-col items-center gap-3">
                  <UiSpinner size="lg" />
                  <span class="text-sm font-medium text-gray-600 dark:text-gray-400">{{ loadingMessage }}</span>
                </div>
              </td>
            </tr> -->
          </template>

          <!-- Empty State -->
          <tr v-else-if="!items || items.length === 0">
            <td :colspan="totalColumns" class="px-6 py-12 text-center">
              <div class="flex flex-col items-center gap-3">
                <slot name="empty-state-icon">
                  <Icon :name="emptyStateIcon" class="h-12 w-12 text-gray-400" />
                </slot>
                <div>
                  <h3 class="text-sm font-medium text-gray-900 dark:text-white">{{ emptyStateTitle }}</h3>
                  <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
                    {{ emptyStateDescription }}
                  </p>
                </div>
                <slot name="empty-state-action" />
              </div>
            </td>
          </tr>

          <!-- Data Rows -->
          <template v-else>
            <template v-for="(item, index) in items" :key="getItemKey(item, index)">
              <tr
                :class="[
                  'group transition-all duration-200 ease-in-out cursor-pointer',
                  'hover:bg-gradient-to-r hover:from-gray-50 hover:to-gray-100/50',
                  'dark:hover:from-gray-700/30 dark:hover:to-gray-600/20',
                  'hover:shadow-sm hover:scale-[1.001]',
                  selectable && selectedItems.includes(getItemKey(item, index))
                    ? 'bg-gradient-to-r from-brandPrimary-50 to-brandPrimary-100/50 dark:from-brandPrimary-900/30 dark:to-brandPrimary-800/20 ring-1 ring-brandPrimary-200 dark:ring-brandPrimary-700'
                    : '',
                  striped && index % 2 === 1 ? 'bg-gray-50/50 dark:bg-gray-800/30' : '',
                  'animate-fade-in-up'
                ]"
                :style="{ animationDelay: `${index * 50}ms` }"
                @click="handleRowClick(item, index)"
              >
                <!-- Enhanced Selection Checkbox -->
                <td v-if="selectable" class="px-6 py-4" @click.stop>
                  <div class="relative">
                    <input
                      type="checkbox"
                      :checked="selectedItems.includes(getItemKey(item, index))"
                      @change="toggleItemSelection(getItemKey(item, index))"
                      class="h-4 w-4 text-brandPrimary focus:ring-2 focus:ring-brandPrimary/50 focus:ring-offset-2 border-gray-300 dark:border-gray-600 rounded transition-all duration-200 ease-in-out hover:scale-110"
                    />
                    <div
                      v-if="selectedItems.includes(getItemKey(item, index))"
                      class="absolute inset-0 pointer-events-none"
                    >
                      <div class="h-4 w-4 rounded border-2 border-brandPrimary bg-brandPrimary animate-checkbox-check">
                        <Icon name="material-symbols:check" class="h-3 w-3 text-white absolute inset-0.5" />
                      </div>
                    </div>
                  </div>
                </td>

                <!-- Enhanced Expand/Collapse Button -->
                <td v-if="expandable" class="px-6 py-4" @click.stop>
                  <UiButton
                    @click="toggleRowExpansion(getItemKey(item, index))"
                    size="sm"
                    variant="ghost"
                    shape="circle"
                    :ripple="true"
                    class="transition-all duration-200 ease-in-out hover:scale-110 group-hover:bg-brandPrimary-50 dark:group-hover:bg-brandPrimary-900/30"
                    :aria-label="expandedRows.includes(getItemKey(item, index)) ? 'Collapse row' : 'Expand row'"
                  >
                    <Icon
                      :name="expandedRows.includes(getItemKey(item, index)) ? 'material-symbols:expand-less' : 'material-symbols:expand-more'"
                      :class="[
                        'h-4 w-4 transition-all duration-200 ease-in-out',
                        expandedRows.includes(getItemKey(item, index))
                          ? 'text-brandPrimary rotate-180'
                          : 'text-gray-400 group-hover:text-brandPrimary'
                      ]"
                    />
                  </UiButton>
                </td>

                <!-- Data Cells -->
                <td
                  v-for="header in visibleHeaders"
                  :key="header.key"
                  :class="[
                    'px-6 py-4 text-sm',
                    header.cellClass || 'text-gray-700 dark:text-gray-300',
                    header.wrap === false ? 'whitespace-nowrap' : ''
                  ]"
                >
                  <slot
                    :name="`item.${header.key}`"
                    :item="item"
                    :index="index"
                    :value="getNestedValue(item, header.key)"
                    :header="header"
                  >
                    <span v-if="header.format">
                      {{ header.format(getNestedValue(item, header.key), item, index) }}
                    </span>
                    <span v-else>
                      {{ getNestedValue(item, header.key) }}
                    </span>
                  </slot>
                </td>
              </tr>

              <!-- Expandable Row Content -->
              <tr v-if="expandable && expandedRows.includes(getItemKey(item, index))" class="bg-gray-50 dark:bg-gray-700/30">
                <td :colspan="totalColumns" class="px-6 py-4">
                  <slot name="expanded-row" :item="item" :index="index" />
                </td>
              </tr>
            </template>
          </template>
        </tbody>
      </table>
    </div>

    <!-- Footer Slot -->
    <div v-if="$slots.footer || pagination" class="px-4 py-3 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
      <slot name="footer">
        <!-- Default Pagination -->
        <div v-if="pagination" class="flex items-center justify-between">
          <div class="text-sm text-gray-700 dark:text-gray-300">
            Showing {{ ((pagination.currentPage - 1) * pagination.perPage) + 1 }} to
            {{ Math.min(pagination.currentPage * pagination.perPage, pagination.total) }} of
            {{ pagination.total }} results
          </div>
          <div class="flex items-center gap-2">
            <UiButton
              @click="$emit('page-change', pagination.currentPage - 1)"
              :disabled="pagination.currentPage <= 1"
              size="sm"
              variant="outline"
            >
              Previous
            </UiButton>
            <span class="text-sm text-gray-700 dark:text-gray-300">
              Page {{ pagination.currentPage }} of {{ Math.ceil(pagination.total / pagination.perPage) }}
            </span>
            <UiButton
              @click="$emit('page-change', pagination.currentPage + 1)"
              :disabled="pagination.currentPage >= Math.ceil(pagination.total / pagination.perPage)"
              size="sm"
              variant="outline"
            >
              Next
            </UiButton>
          </div>
        </div>
      </slot>
    </div>

    <!-- Column Customization Panel -->
    <Transition
      enter-active-class="transition-all duration-300 ease-out"
      enter-from-class="opacity-0 transform translate-x-full"
      enter-to-class="opacity-100 transform translate-x-0"
      leave-active-class="transition-all duration-300 ease-in"
      leave-from-class="opacity-100 transform translate-x-0"
      leave-to-class="opacity-0 transform translate-x-full"
    >
      <div v-if="columnCustomization && showColumnCustomization" class="absolute top-0 right-0 h-full w-80 bg-white dark:bg-gray-800 border-l border-gray-200 dark:border-gray-700 shadow-lg z-10">
        <div class="p-6">
          <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Customize Columns</h3>
            <UiButton @click="showColumnCustomization = false" size="sm" variant="ghost">
              <Icon name="material-symbols:close" class="h-4 w-4" />
            </UiButton>
          </div>

          <div class="space-y-3">
            <div
              v-for="header in headers"
              :key="header.key"
              class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg"
            >
              <div class="flex items-center gap-3">
                <Icon name="material-symbols:drag-indicator" class="h-4 w-4 text-gray-400 cursor-move" />
                <span class="text-sm font-medium text-gray-900 dark:text-white">{{ header.label }}</span>
              </div>
              <input
                type="checkbox"
                :checked="visibleColumns.includes(header.key)"
                @change="toggleColumn(header.key)"
                class="h-4 w-4 text-brandPrimary focus:ring-brandPrimary border-gray-300 rounded"
              />
            </div>
          </div>

          <div class="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
            <UiButton @click="resetColumns" variant="outline" class="w-full">
              Reset to Default
            </UiButton>
          </div>
        </div>
      </div>
    </Transition>

    <!-- Column Filters Panel -->
    <div v-if="showColumnFilters" class="absolute top-0 left-0 w-full h-full bg-black bg-opacity-50 z-20 flex items-center justify-center">
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 w-96 max-w-full mx-4">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Filter {{ activeFilterColumn }}</h3>
          <UiButton @click="showColumnFilters = false" size="sm" variant="ghost">
            <Icon name="material-symbols:close" class="h-4 w-4" />
          </UiButton>
        </div>

        <slot name="column-filter" :column="activeFilterColumn" :close-filter="() => showColumnFilters = false">
          <div class="space-y-4">
            <input
              v-model="columnFilterValue"
              type="text"
              placeholder="Enter filter value..."
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-brandPrimary focus:border-brandPrimary dark:bg-gray-700 dark:text-white"
            />
            <div class="flex gap-2">
              <UiButton @click="applyColumnFilter" variant="primary" class="flex-1">
                Apply Filter
              </UiButton>
              <UiButton @click="clearColumnFilter" variant="outline" class="flex-1">
                Clear
              </UiButton>
            </div>
          </div>
        </slot>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" generic="T extends Record<string, any>">
import { ref, computed, watch } from 'vue';

export interface TableHeader<ItemType extends Record<string, any> = Record<string, any>> {
  key: string;
  label: string;
  sortable?: boolean;
  filterable?: boolean;
  class?: string; // Custom class for <th>
  cellClass?: string; // Custom class for <td>
  format?: (value: any, item: ItemType, index: number) => string; // Custom formatter for cell value
  wrap?: boolean; // Whether to wrap text (default: true)
  width?: string; // Column width
  align?: 'left' | 'center' | 'right'; // Text alignment
  sticky?: boolean; // Sticky column
}

export interface SortState {
  key: string | null;
  direction: 'asc' | 'desc' | null;
}

export interface PaginationState {
  currentPage: number;
  perPage: number;
  total: number;
}


const props = withDefaults(defineProps<{
  headers: TableHeader<T>[];
  items: T[];
  loading?: boolean;
  loadingRows?: number; // Number of skeleton rows to show when loading
  itemKey?: string; // Unique key for v-for loop (e.g., 'id')
  title?: string;
  description?: string;
  loadingMessage?: string;
  emptyStateTitle?: string;
  emptyStateDescription?: string;
  emptyStateIcon?: string;
  initialSort?: SortState; // To set initial sort state from parent
  selectable?: boolean; // Enable row selection
  expandable?: boolean; // Enable row expansion
  striped?: boolean; // Alternating row colors
  columnCustomization?: boolean; // Enable column customization
  pagination?: PaginationState; // Pagination state
  dense?: boolean; // Compact table layout
  bordered?: boolean; // Add borders
  hoverable?: boolean; // Hover effects
  animated?: boolean; // Enable animations
}>(), {
  loading: false,
  loadingRows: 5,
  loadingMessage: 'Loading data...',
  emptyStateTitle: 'No data available',
  emptyStateDescription: 'There are no items to display.',
  emptyStateIcon: 'material-symbols:inbox',
  items: () => [],
  headers: () => [],
  initialSort: () => ({ key: null, direction: null }),
  selectable: false,
  expandable: false,
  striped: false,
  columnCustomization: false,
  dense: false,
  bordered: false,
  hoverable: true,
  animated: true,
});

const emit = defineEmits<{
  (e: 'sort', payload: SortState): void;
  (e: 'selection-change', payload: string[]): void;
  (e: 'bulk-action', payload: { action: string; items: string[] }): void;
  (e: 'row-click', payload: { item: T; index: number }): void;
  (e: 'row-expand', payload: { item: T; index: number; expanded: boolean }): void;
  (e: 'page-change', payload: number): void;
  (e: 'column-filter', payload: { column: string; value: any }): void;
}>();

// Reactive state
const currentSort = ref<SortState>({ ...props.initialSort });
const selectedItems = ref<string[]>([]);
const expandedRows = ref<string[]>([]);
const showColumnCustomization = ref(false);
const showColumnFilters = ref(false);
const activeFilterColumn = ref<string>('');
const columnFilterValue = ref<string>('');
const visibleColumns = ref<string[]>([]);

// Initialize visible columns
watch(() => props.headers, (newHeaders) => {
  if (newHeaders.length > 0 && visibleColumns.value.length === 0) {
    visibleColumns.value = newHeaders.map(h => h.key);
  }
}, { immediate: true });

watch(() => props.initialSort, (newSort) => {
  if (newSort) {
    currentSort.value = { ...newSort };
  }
}, { deep: true });

// Computed properties
const visibleHeaders = computed(() =>
  props.headers.filter(header => visibleColumns.value.includes(header.key))
);

const totalColumns = computed(() => {
  let count = visibleHeaders.value.length;
  if (props.selectable) count++;
  if (props.expandable) count++;
  return count;
});

const isAllSelected = computed(() =>
  props.items.length > 0 && selectedItems.value.length === props.items.length
);

const isIndeterminate = computed(() =>
  selectedItems.value.length > 0 && selectedItems.value.length < props.items.length
);

// Utility methods
const getNestedValue = (obj: T | undefined, path: string | undefined): any => {
  if (!path || !obj) return '';
  return path.split('.').reduce((acc, part) => acc && typeof acc === 'object' ? (acc as Record<string, any>)[part] : undefined, obj);
};

const getItemKey = (item: T, index: number): string => {
  if (props.itemKey) {
    const key = getNestedValue(item, props.itemKey);
    return key ? String(key) : String(index);
  }
  return String(index);
};

// Enhanced utility methods
const getSkeletonWidth = (header: TableHeader<T>): string => {
  // Generate varied skeleton widths for more realistic loading state
  const widths = ['60%', '80%', '45%', '70%', '90%', '55%'];
  const index = props.headers.findIndex(h => h.key === header.key);
  return widths[index % widths.length];
};

const handleRowClick = (item: T, index: number) => {
  emit('row-click', { item, index });
};

// Sorting methods
const handleSort = (columnKey: string) => {
  let newDirection: 'asc' | 'desc' | null;
  if (currentSort.value.key !== columnKey) {
    newDirection = 'asc';
  } else if (currentSort.value.direction === 'asc') {
    newDirection = 'desc';
  } else if (currentSort.value.direction === 'desc') {
    newDirection = null;
  } else {
    newDirection = 'asc';
  }

  currentSort.value = { key: newDirection ? columnKey : null, direction: newDirection };
  emit('sort', currentSort.value);
};

// Selection methods
const toggleSelectAll = () => {
  if (isAllSelected.value) {
    selectedItems.value = [];
  } else {
    selectedItems.value = props.items.map((item, index) => getItemKey(item, index));
  }
  emit('selection-change', selectedItems.value);
};

const toggleItemSelection = (itemKey: string) => {
  const index = selectedItems.value.indexOf(itemKey);
  if (index > -1) {
    selectedItems.value.splice(index, 1);
  } else {
    selectedItems.value.push(itemKey);
  }
  emit('selection-change', selectedItems.value);
};

const clearSelection = () => {
  selectedItems.value = [];
  emit('selection-change', selectedItems.value);
};

// Expansion methods
const toggleRowExpansion = (itemKey: string) => {
  const index = expandedRows.value.indexOf(itemKey);
  const item = props.items.find((item, idx) => getItemKey(item, idx) === itemKey);
  const itemIndex = props.items.findIndex((item, idx) => getItemKey(item, idx) === itemKey);

  if (index > -1) {
    expandedRows.value.splice(index, 1);
    if (item !== undefined) {
      emit('row-expand', { item, index: itemIndex, expanded: false });
    }
  } else {
    expandedRows.value.push(itemKey);
    if (item !== undefined) {
      emit('row-expand', { item, index: itemIndex, expanded: true });
    }
  }
};

// Column customization methods
const toggleColumn = (columnKey: string) => {
  const index = visibleColumns.value.indexOf(columnKey);
  if (index > -1) {
    visibleColumns.value.splice(index, 1);
  } else {
    visibleColumns.value.push(columnKey);
  }
};

const resetColumns = () => {
  visibleColumns.value = props.headers.map(h => h.key);
};

// Column filtering methods
const toggleColumnFilter = (columnKey: string) => {
  activeFilterColumn.value = columnKey;
  showColumnFilters.value = true;
  columnFilterValue.value = '';
};

const applyColumnFilter = () => {
  emit('column-filter', {
    column: activeFilterColumn.value,
    value: columnFilterValue.value
  });
  showColumnFilters.value = false;
};

const clearColumnFilter = () => {
  emit('column-filter', {
    column: activeFilterColumn.value,
    value: null
  });
  showColumnFilters.value = false;
};

// Expose methods for parent components
defineExpose({
  clearSelection,
  selectAll: () => {
    selectedItems.value = props.items.map((item, index) => getItemKey(item, index));
    emit('selection-change', selectedItems.value);
  },
  expandAll: () => {
    expandedRows.value = props.items.map((item, index) => getItemKey(item, index));
  },
  collapseAll: () => {
    expandedRows.value = [];
  },
  getSelectedItems: () => selectedItems.value,
  getExpandedRows: () => expandedRows.value,
});

</script>

<style scoped>
/* Enhanced table animations and styles */
@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes checkbox-check {
  0% {
    transform: scale(0.8);
    opacity: 0;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes skeleton-pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-fade-in-up {
  animation: fade-in-up 0.4s ease-out;
}

.animate-checkbox-check {
  animation: checkbox-check 0.3s ease-out;
}

.animate-pulse {
  animation: skeleton-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Enhanced table styles */
th {
  font-weight: 600;
  letter-spacing: 0.025em;
}

/* Smooth transitions for interactive elements */
tbody tr {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced hover effects */
tbody tr:hover {
  transform: translateY(-1px);
}

/* Indeterminate checkbox styling */
input[type="checkbox"]:indeterminate {
  background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M4 8h8' stroke='white' stroke-width='2'/%3e%3c/svg%3e");
  background-color: var(--color-brandPrimary);
  border-color: var(--color-brandPrimary);
}

/* Enhanced checkbox styling */
input[type="checkbox"]:checked {
  background-color: var(--color-brandPrimary);
  border-color: var(--color-brandPrimary);
  animation: checkbox-check 0.2s ease-out;
}

input[type="checkbox"]:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(var(--color-brandPrimary-rgb), 0.2);
}

/* Sticky column support */
.sticky-column {
  position: sticky;
  left: 0;
  z-index: 10;
  background-color: white;
  box-shadow: 2px 0 4px rgba(0, 0, 0, 0.1);
}

.dark .sticky-column {
  background-color: rgb(31, 41, 55);
}

/* Dense table layout */
.table-dense td {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.table-dense th {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

/* Bordered table */
.table-bordered {
  border: 1px solid rgb(229, 231, 235);
  border-radius: 0.5rem;
  overflow: hidden;
}

.dark .table-bordered {
  border-color: rgb(75, 85, 99);
}

.table-bordered th,
.table-bordered td {
  border-right: 1px solid rgb(229, 231, 235);
}

.dark .table-bordered th,
.dark .table-bordered td {
  border-right-color: rgb(75, 85, 99);
}

.table-bordered th:last-child,
.table-bordered td:last-child {
  border-right: none;
}

/* Enhanced scrollbar styling */
.overflow-x-auto {
  scrollbar-width: thin;
  scrollbar-color: rgb(203, 213, 224) rgb(247, 250, 252);
}

.overflow-x-auto::-webkit-scrollbar {
  height: 8px;
}

.overflow-x-auto::-webkit-scrollbar-track {
  background: rgb(247, 250, 252);
  border-radius: 4px;
}

.overflow-x-auto::-webkit-scrollbar-thumb {
  background: rgb(203, 213, 224);
  border-radius: 4px;
}

.overflow-x-auto::-webkit-scrollbar-thumb:hover {
  background: rgb(160, 174, 192);
}

/* Dark mode scrollbar */
.dark .overflow-x-auto {
  scrollbar-color: rgb(75, 85, 99) rgb(31, 41, 55);
}

.dark .overflow-x-auto::-webkit-scrollbar-track {
  background: rgb(31, 41, 55);
}

.dark .overflow-x-auto::-webkit-scrollbar-thumb {
  background: rgb(75, 85, 99);
}

.dark .overflow-x-auto::-webkit-scrollbar-thumb:hover {
  background: rgb(107, 114, 128);
}

/* Column customization panel */
.column-customization-panel {
  scrollbar-width: thin;
  scrollbar-color: rgb(203, 213, 224) rgb(247, 250, 252);
}

.column-customization-panel::-webkit-scrollbar {
  width: 6px;
}

.column-customization-panel::-webkit-scrollbar-track {
  background: rgb(247, 250, 252);
}

.column-customization-panel::-webkit-scrollbar-thumb {
  background: rgb(203, 213, 224);
  border-radius: 3px;
}

.column-customization-panel::-webkit-scrollbar-thumb:hover {
  background: rgb(160, 174, 192);
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .animate-fade-in-up,
  .animate-checkbox-check,
  .animate-pulse {
    animation: none;
  }

  tbody tr {
    transition: none;
  }

  tbody tr:hover {
    transform: none;
  }
}

/* Enhanced focus states for accessibility */
th:focus-visible,
td:focus-visible {
  outline: 2px solid var(--color-brandPrimary);
  outline-offset: 2px;
}

/* Loading state overlay */
.loading-overlay {
  backdrop-filter: blur(2px);
}
</style>