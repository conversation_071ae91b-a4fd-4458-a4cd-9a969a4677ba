/**
 * Authentication Domain Models
 *
 * Core domain models for the authentication feature
 * Enhanced with advanced type system and strict type safety
 */

import type {
  BaseEntity,
  UUID,
  UserId,
  TenantId,
  ISODateString, // Ensure this is exported if it's a type alias from core.js
  UnixTimestamp,
  EntityStatus,
  // SecurityClassification, // Unused
  FileInfo,
  Brand
} from '../../../shared/types/core.js' // Adjusted path
export type { ISODateString } from '../../../shared/types/core.js' // Re-export ISODateString
import type { TenantRoles, PlatformRoles, Permissions } from '../constants/roles.js' // Added .js

/**
 * User entity with comprehensive type safety
 */
export interface User extends Omit<BaseEntity, 'id'> {
  id: UserId
  email: EmailAddress
  name: PersonName
  phone?: PhoneNumber
  address?: Address
  roles: (TenantRoles | PlatformRoles)[]
  permissions: Permissions[]
  status: EntityStatus
  profile: UserProfile
  security: UserSecurity
  preferences: UserPreferences
  tenant?: UserTenantInfo
}

/**
 * Branded email type for validation
 */
export type EmailAddress = Brand<string, 'EmailAddress'>

/**
 * Phone number with country code
 */
export interface PhoneNumber {
  countryCode: string
  number: string
  formatted: string
  verified: boolean
  verifiedAt?: ISODateString
}

/**
 * Person name components
 */
export interface PersonName {
  first: string
  middle?: string
  last: string
  full: string
  display: string
  initials: string
}

/**
 * Physical address
 */
export interface Address {
  street1: string
  street2?: string
  city: string
  state: string
  postalCode: string
  country: string
  formatted: string
  coordinates?: {
    latitude: number
    longitude: number
  }
}

/**
 * User profile information
 */
export interface UserProfile {
  avatar?: FileInfo
  bio?: string
  title?: string
  department?: string
  location?: string
  website?: string
  socialLinks?: SocialLinks
  customFields?: Record<string, any>
}

/**
 * Social media links
 */
export interface SocialLinks {
  linkedin?: string
  twitter?: string
  github?: string
  website?: string
}

/**
 * User security settings
 */
export interface UserSecurity {
  emailVerified: boolean
  emailVerifiedAt?: ISODateString
  phoneVerified: boolean
  phoneVerifiedAt?: ISODateString
  twoFactorEnabled: boolean
  twoFactorMethod?: TwoFactorMethod
  lastLoginAt?: ISODateString
  lastLoginIp?: string
  lastPasswordChange?: ISODateString
  failedLoginAttempts: number
  lockedUntil?: ISODateString
  securityQuestions?: SecurityQuestion[]
  trustedDevices?: TrustedDevice[]
}

/**
 * Two-factor authentication methods
 */
export enum TwoFactorMethod {
  TOTP = 'totp',
  SMS = 'sms',
  EMAIL = 'email',
  BACKUP_CODES = 'backup_codes'
}

/**
 * Security question
 */
export interface SecurityQuestion {
  id: UUID
  question: string
  answerHash: string
  createdAt: ISODateString
}

/**
 * Trusted device
 */
export interface TrustedDevice {
  id: UUID
  name: string
  userAgent: string
  ipAddress: string
  fingerprint: string
  trustedAt: ISODateString
  lastUsedAt: ISODateString
  expiresAt: ISODateString
}

/**
 * User preferences with comprehensive settings
 */
export interface UserPreferences {
  language: LanguageCode
  timezone: TimezoneCode
  theme: ThemeMode
  accessibility: AccessibilitySettings
  notifications: NotificationPreferences
  dashboard: DashboardPreferences
  privacy: PrivacySettings
  integrations: IntegrationSettings
}

/**
 * Language and timezone codes
 */
export type LanguageCode = 'en' | 'es' | 'fr' | 'de' | 'ar' | 'he'
export type TimezoneCode = string // IANA timezone identifier

/**
 * Theme modes
 */
export enum ThemeMode {
  LIGHT = 'light',
  DARK = 'dark',
  SYSTEM = 'system',
  HIGH_CONTRAST = 'high_contrast'
}

/**
 * Accessibility settings
 */
export interface AccessibilitySettings {
  reducedMotion: boolean
  highContrast: boolean
  largeText: boolean
  screenReader: boolean
  keyboardNavigation: boolean
}

/**
 * Notification preferences
 */
export interface NotificationPreferences {
  email: EmailNotificationSettings
  push: PushNotificationSettings
  sms: SmsNotificationSettings
  inApp: InAppNotificationSettings
}

/**
 * Email notification settings
 */
export interface EmailNotificationSettings {
  enabled: boolean
  frequency: NotificationFrequency
  types: EmailNotificationType[]
  digest: boolean
  digestFrequency: DigestFrequency
}

/**
 * Push notification settings
 */
export interface PushNotificationSettings {
  enabled: boolean
  types: PushNotificationType[]
  quietHours: QuietHours
  devices: string[]
}

/**
 * SMS notification settings
 */
export interface SmsNotificationSettings {
  enabled: boolean
  types: SmsNotificationType[]
  phoneNumber: PhoneNumber
}

/**
 * In-app notification settings
 */
export interface InAppNotificationSettings {
  enabled: boolean
  types: InAppNotificationType[]
  sound: boolean
  desktop: boolean
}

/**
 * Notification frequencies
 */
export enum NotificationFrequency {
  IMMEDIATE = 'immediate',
  HOURLY = 'hourly',
  DAILY = 'daily',
  WEEKLY = 'weekly',
  NEVER = 'never'
}

/**
 * Digest frequencies
 */
export enum DigestFrequency {
  DAILY = 'daily',
  WEEKLY = 'weekly',
  MONTHLY = 'monthly'
}

/**
 * Notification types
 */
export enum EmailNotificationType {
  CASE_UPDATES = 'case_updates',
  DOCUMENT_SHARED = 'document_shared',
  TASK_ASSIGNED = 'task_assigned',
  DEADLINE_REMINDER = 'deadline_reminder',
  SYSTEM_ALERTS = 'system_alerts'
}

export enum PushNotificationType {
  URGENT_UPDATES = 'urgent_updates',
  MENTIONS = 'mentions',
  DEADLINES = 'deadlines'
}

export enum SmsNotificationType {
  CRITICAL_ALERTS = 'critical_alerts',
  SECURITY_ALERTS = 'security_alerts'
}

export enum InAppNotificationType {
  ALL = 'all',
  MENTIONS = 'mentions',
  ASSIGNMENTS = 'assignments'
}

/**
 * Quiet hours for notifications
 */
export interface QuietHours {
  enabled: boolean
  start: string // HH:MM format
  end: string   // HH:MM format
  timezone: TimezoneCode
  weekends: boolean
}

/**
 * Dashboard preferences
 */
export interface DashboardPreferences {
  layout: DashboardLayout
  density: DisplayDensity
  widgets: DashboardWidget[]
  defaultView: string
  autoRefresh: boolean
  refreshInterval: number
}

/**
 * Dashboard layouts
 */
export enum DashboardLayout {
  GRID = 'grid',
  LIST = 'list',
  KANBAN = 'kanban',
  TIMELINE = 'timeline'
}

/**
 * Display density options
 */
export enum DisplayDensity {
  COMPACT = 'compact',
  COMFORTABLE = 'comfortable',
  SPACIOUS = 'spacious'
}

/**
 * Dashboard widget configuration
 */
export interface DashboardWidget {
  id: string
  type: string
  position: WidgetPosition
  size: WidgetSize
  config: Record<string, any>
  visible: boolean
}

/**
 * Widget position and size
 */
export interface WidgetPosition {
  x: number
  y: number
}

export interface WidgetSize {
  width: number
  height: number
}

/**
 * Privacy settings
 */
export interface PrivacySettings {
  profileVisibility: ProfileVisibility
  activityTracking: boolean
  analyticsOptOut: boolean
  dataRetention: DataRetentionSettings
  shareUsageData: boolean
}

/**
 * Profile visibility options
 */
export enum ProfileVisibility {
  PUBLIC = 'public',
  ORGANIZATION = 'organization',
  PRIVATE = 'private'
}

/**
 * Data retention settings
 */
export interface DataRetentionSettings {
  enabled: boolean
  period: number // days
  autoDelete: boolean
  categories: DataCategory[]
}

/**
 * Data categories for retention
 */
export enum DataCategory {
  ACTIVITY_LOGS = 'activity_logs',
  SEARCH_HISTORY = 'search_history',
  TEMPORARY_FILES = 'temporary_files',
  CACHE_DATA = 'cache_data'
}

/**
 * Integration settings
 */
export interface IntegrationSettings {
  calendar: CalendarIntegration
  email: EmailIntegration
  storage: StorageIntegration
  thirdParty: ThirdPartyIntegration[]
}

/**
 * Calendar integration
 */
export interface CalendarIntegration {
  enabled: boolean
  provider: CalendarProvider
  syncBidirectional: boolean
  defaultCalendar: string
  reminderOffset: number
}

/**
 * Calendar providers
 */
export enum CalendarProvider {
  GOOGLE = 'google',
  OUTLOOK = 'outlook',
  APPLE = 'apple',
  EXCHANGE = 'exchange'
}

/**
 * Email integration
 */
export interface EmailIntegration {
  enabled: boolean
  provider: EmailProvider
  autoSync: boolean
  folders: string[]
  rules: EmailRule[]
}

/**
 * Email providers
 */
export enum EmailProvider {
  GMAIL = 'gmail',
  OUTLOOK = 'outlook',
  EXCHANGE = 'exchange',
  IMAP = 'imap'
}

/**
 * Email rules
 */
export interface EmailRule {
  id: UUID
  name: string
  conditions: EmailRuleCondition[]
  actions: EmailRuleAction[]
  enabled: boolean
}

export interface EmailRuleCondition {
  field: EmailField
  operator: string
  value: string
}

export interface EmailRuleAction {
  type: EmailActionType
  parameters: Record<string, any>
}

export enum EmailField {
  FROM = 'from',
  TO = 'to',
  SUBJECT = 'subject',
  BODY = 'body'
}

export enum EmailActionType {
  CREATE_CASE = 'create_case',
  ADD_TO_CASE = 'add_to_case',
  CREATE_TASK = 'create_task',
  FORWARD = 'forward'
}

/**
 * Storage integration
 */
export interface StorageIntegration {
  enabled: boolean
  providers: StorageProvider[]
  defaultProvider: string
  autoSync: boolean
  conflictResolution: ConflictResolution
}

/**
 * Storage providers
 */
export enum StorageProvider {
  GOOGLE_DRIVE = 'google_drive',
  DROPBOX = 'dropbox',
  ONEDRIVE = 'onedrive',
  BOX = 'box'
}

/**
 * Conflict resolution strategies
 */
export enum ConflictResolution {
  MANUAL = 'manual',
  LOCAL_WINS = 'local_wins',
  REMOTE_WINS = 'remote_wins',
  TIMESTAMP = 'timestamp'
}

/**
 * Third-party integrations
 */
export interface ThirdPartyIntegration {
  id: UUID
  name: string
  provider: string
  enabled: boolean
  config: Record<string, any>
  permissions: string[]
  connectedAt: ISODateString
  lastSyncAt?: ISODateString
}

/**
 * User tenant information
 */
export interface UserTenantInfo {
  id: TenantId
  name: string
  role: string
  permissions: Permissions[]
  joinedAt: ISODateString
  status: TenantMemberStatus
}

/**
 * Tenant member status
 */
export enum TenantMemberStatus {
  ACTIVE = 'active',
  PENDING = 'pending',
  SUSPENDED = 'suspended',
  LEFT = 'left'
}

/**
 * Authentication session with enhanced security
 */
export interface AuthSession {
  id: UUID
  user: User
  tokens: SessionTokens
  security: SessionSecurity
  context: SessionContext
  metadata: SessionMetadata
}

/**
 * Session tokens
 */
export interface SessionTokens {
  accessToken: JWTToken
  refreshToken: RefreshToken
  idToken?: JWTToken
  expiresAt: ISODateString
  refreshExpiresAt: ISODateString
  tokenType: TokenType
  scope: string[]
}

/**
 * JWT token with metadata
 */
export interface JWTToken {
  token: string
  payload: JWTPayload
  header: JWTHeader
  signature: string
}

/**
 * JWT payload
 */
export interface JWTPayload {
  sub: UserId
  iss: string
  aud: string[]
  exp: UnixTimestamp
  iat: UnixTimestamp
  nbf?: UnixTimestamp
  jti: UUID
  scope: string[]
  permissions: Permissions[]
  tenant?: TenantId
  roles: TenantRoles[] | PlatformRoles[]
  sessionId: UUID
}

/**
 * JWT header
 */
export interface JWTHeader {
  alg: string
  typ: string
  kid?: string
}

/**
 * Refresh token
 */
export interface RefreshToken {
  token: string
  family: UUID
  generation: number
  expiresAt: ISODateString
  rotated: boolean
}

/**
 * Token types
 */
export enum TokenType {
  BEARER = 'Bearer',
  MAC = 'MAC',
  BASIC = 'Basic'
}

/**
 * Session security information
 */
export interface SessionSecurity {
  ipAddress: string
  userAgent: string
  deviceFingerprint: string
  location?: GeoLocation
  riskScore: number
  mfaVerified: boolean
  trustedDevice: boolean
  lastActivity: ISODateString
}

/**
 * Geographic location
 */
export interface GeoLocation {
  country: string
  region: string
  city: string
  latitude: number
  longitude: number
  accuracy: number
}

/**
 * Session context
 */
export interface SessionContext {
  tenant: TenantContext
  permissions: PermissionContext
  features: FeatureContext
  limits: SessionLimits
}

/**
 * Tenant context
 */
export interface TenantContext {
  id: TenantId
  name: string
  plan: string
  features: string[]
  limits: TenantLimits
  settings: TenantSettings
}

/**
 * Permission context
 */
export interface PermissionContext {
  roles: TenantRoles[] | PlatformRoles[]
  permissions: Permissions[]
  restrictions: PermissionRestriction[]
  inheritance: PermissionInheritance[]
}

/**
 * Permission restriction
 */
export interface PermissionRestriction {
  resource: string
  action: string
  condition?: string
  reason: string
}

/**
 * Permission inheritance
 */
export interface PermissionInheritance {
  source: PermissionSource
  permissions: Permissions[]
}

/**
 * Permission sources
 */
export enum PermissionSource {
  ROLE = 'role',
  DIRECT = 'direct',
  GROUP = 'group',
  TENANT = 'tenant'
}

/**
 * Feature context
 */
export interface FeatureContext {
  enabled: string[]
  disabled: string[]
  beta: string[]
  experimental: string[]
}

/**
 * Session limits
 */
export interface SessionLimits {
  maxDuration: number
  maxIdleTime: number
  maxConcurrentSessions: number
  ipRestrictions: string[]
  timeRestrictions: TimeRestriction[]
}

/**
 * Time restrictions
 */
export interface TimeRestriction {
  days: number[] // 0-6, Sunday = 0
  startTime: string // HH:MM
  endTime: string   // HH:MM
  timezone: TimezoneCode
}

/**
 * Tenant limits
 */
export interface TenantLimits {
  users: number
  storage: number // bytes
  apiCalls: number
  features: Record<string, number>
}

/**
 * Tenant settings
 */
export interface TenantSettings {
  security: TenantSecuritySettings
  branding: TenantBranding
  integrations: TenantIntegrations
  compliance: ComplianceSettings
}

/**
 * Tenant security settings
 */
export interface TenantSecuritySettings {
  passwordPolicy: PasswordPolicy
  mfaRequired: boolean
  sessionTimeout: number
  ipWhitelist: string[]
  ssoEnabled: boolean
  ssoProvider?: string
}

/**
 * Password policy
 */
export interface PasswordPolicy {
  minLength: number
  requireUppercase: boolean
  requireLowercase: boolean
  requireNumbers: boolean
  requireSymbols: boolean
  preventReuse: number
  maxAge: number
  complexity: PasswordComplexity
}

/**
 * Password complexity levels
 */
export enum PasswordComplexity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CUSTOM = 'custom'
}

/**
 * Tenant branding
 */
export interface TenantBranding {
  logo?: FileInfo
  favicon?: FileInfo
  colors: BrandColors
  fonts: BrandFonts
  customCss?: string
}

/**
 * Brand colors
 */
export interface BrandColors {
  primary: string
  secondary: string
  accent: string
  background: string
  text: string
}

/**
 * Brand fonts
 */
export interface BrandFonts {
  primary: string
  secondary: string
  monospace: string
}

/**
 * Tenant integrations
 */
export interface TenantIntegrations {
  sso: SSOIntegration[]
  directory: DirectoryIntegration[]
  billing: BillingIntegration[]
  storage: StorageIntegration[]
}

/**
 * SSO integration
 */
export interface SSOIntegration {
  id: UUID
  provider: SSOProvider
  enabled: boolean
  config: SSOConfig
  domains: string[]
}

/**
 * SSO providers
 */
export enum SSOProvider {
  SAML = 'saml',
  OIDC = 'oidc',
  OAUTH2 = 'oauth2',
  LDAP = 'ldap',
  ACTIVE_DIRECTORY = 'active_directory'
}

/**
 * SSO configuration
 */
export interface SSOConfig {
  entityId?: string
  ssoUrl?: string
  x509Certificate?: string
  clientId?: string
  clientSecret?: string
  discoveryUrl?: string
  scopes?: string[]
  attributes?: AttributeMapping
}

/**
 * Attribute mapping for SSO
 */
export interface AttributeMapping {
  email: string
  firstName: string
  lastName: string
  roles?: string
  groups?: string
  department?: string
}

/**
 * Directory integration
 */
export interface DirectoryIntegration {
  id: UUID
  type: DirectoryType
  enabled: boolean
  config: DirectoryConfig
  syncEnabled: boolean
  lastSync?: ISODateString
}

/**
 * Directory types
 */
export enum DirectoryType {
  ACTIVE_DIRECTORY = 'active_directory',
  LDAP = 'ldap',
  GOOGLE_WORKSPACE = 'google_workspace',
  AZURE_AD = 'azure_ad'
}

/**
 * Directory configuration
 */
export interface DirectoryConfig {
  host?: string
  port?: number
  baseDn?: string
  bindDn?: string
  bindPassword?: string
  userFilter?: string
  groupFilter?: string
  attributes?: AttributeMapping
}

/**
 * Billing integration
 */
export interface BillingIntegration {
  id: UUID
  provider: BillingProvider
  enabled: boolean
  config: BillingConfig
}

/**
 * Billing providers
 */
export enum BillingProvider {
  STRIPE = 'stripe',
  PAYPAL = 'paypal',
  SQUARE = 'square',
  QUICKBOOKS = 'quickbooks'
}

/**
 * Billing configuration
 */
export interface BillingConfig {
  apiKey?: string
  webhookSecret?: string
  currency: string
  taxRate?: number
  invoicePrefix?: string
}

/**
 * Compliance settings
 */
export interface ComplianceSettings {
  gdpr: GDPRSettings
  hipaa: HIPAASettings
  sox: SOXSettings
  custom: CustomComplianceSettings[]
}

/**
 * GDPR settings
 */
export interface GDPRSettings {
  enabled: boolean
  dataRetentionPeriod: number
  rightToErasure: boolean
  dataPortability: boolean
  consentTracking: boolean
  lawfulBasis: GDPRLawfulBasis[]
}

/**
 * GDPR lawful basis
 */
export enum GDPRLawfulBasis {
  CONSENT = 'consent',
  CONTRACT = 'contract',
  LEGAL_OBLIGATION = 'legal_obligation',
  VITAL_INTERESTS = 'vital_interests',
  PUBLIC_TASK = 'public_task',
  LEGITIMATE_INTERESTS = 'legitimate_interests'
}

/**
 * HIPAA settings
 */
export interface HIPAASettings {
  enabled: boolean
  baaSigned: boolean
  encryptionRequired: boolean
  auditLogging: boolean
  accessControls: boolean
}

/**
 * SOX settings
 */
export interface SOXSettings {
  enabled: boolean
  financialControls: boolean
  auditTrail: boolean
  segregationOfDuties: boolean
  changeManagement: boolean
}

/**
 * Custom compliance settings
 */
export interface CustomComplianceSettings {
  id: UUID
  name: string
  description: string
  requirements: ComplianceRequirement[]
  enabled: boolean
}

/**
 * Compliance requirement
 */
export interface ComplianceRequirement {
  id: UUID
  name: string
  description: string
  type: ComplianceType
  mandatory: boolean
  controls: ComplianceControl[]
}

/**
 * Compliance types
 */
export enum ComplianceType {
  DATA_PROTECTION = 'data_protection',
  ACCESS_CONTROL = 'access_control',
  AUDIT_LOGGING = 'audit_logging',
  ENCRYPTION = 'encryption',
  BACKUP = 'backup'
}

/**
 * Compliance control
 */
export interface ComplianceControl {
  id: UUID
  name: string
  description: string
  implemented: boolean
  evidence?: FileInfo[]
  lastReview?: ISODateString
  nextReview?: ISODateString
}

/**
 * Session metadata
 */
export interface SessionMetadata {
  createdAt: ISODateString
  lastAccessedAt: ISODateString
  expiresAt: ISODateString
  deviceInfo: DeviceInfo
  applicationInfo: ApplicationInfo
  flags: SessionFlags
}

/**
 * Device information
 */
export interface DeviceInfo {
  type: DeviceType
  os: OperatingSystem
  browser: BrowserInfo
  screen: ScreenInfo
  capabilities: DeviceCapabilities
}

/**
 * Device types
 */
export enum DeviceType {
  DESKTOP = 'desktop',
  MOBILE = 'mobile',
  TABLET = 'tablet',
  TV = 'tv',
  WATCH = 'watch',
  OTHER = 'other'
}

/**
 * Operating system
 */
export interface OperatingSystem {
  name: string
  version: string
  architecture: string
}

/**
 * Browser information
 */
export interface BrowserInfo {
  name: string
  version: string
  engine: string
  userAgent: string
}

/**
 * Screen information
 */
export interface ScreenInfo {
  width: number
  height: number
  pixelRatio: number
  colorDepth: number
  orientation: ScreenOrientation
}

/**
 * Screen orientations
 */
export enum ScreenOrientation {
  PORTRAIT = 'portrait',
  LANDSCAPE = 'landscape'
}

/**
 * Device capabilities
 */
export interface DeviceCapabilities {
  touchScreen: boolean
  camera: boolean
  microphone: boolean
  geolocation: boolean
  notifications: boolean
  storage: StorageCapabilities
}

/**
 * Storage capabilities
 */
export interface StorageCapabilities {
  localStorage: boolean
  sessionStorage: boolean
  indexedDB: boolean
  webSQL: boolean
  quota: number
}

/**
 * Application information
 */
export interface ApplicationInfo {
  name: string
  version: string
  buildNumber: string
  environment: Environment
  features: string[]
}

/**
 * Application environments
 */
export enum Environment {
  DEVELOPMENT = 'development',
  STAGING = 'staging',
  PRODUCTION = 'production',
  TEST = 'test'
}

/**
 * Session flags
 */
export interface SessionFlags {
  isImpersonated: boolean
  isElevated: boolean
  isReadOnly: boolean
  isMobile: boolean
  isBot: boolean
  requiresMFA: boolean
  isExpiring: boolean
}



export interface LoginCredentials {
  email: string
  password: string
  rememberMe?: boolean
  totpCode?: string
}

export interface RegisterData {
  name: string
  email: string
  password: string
  confirmPassword: string
  acceptTerms: boolean
  inviteToken?: string
}

export interface PasswordResetRequest {
  email: string
}

export interface PasswordReset {
  token: string
  password: string
  confirmPassword: string
}

export interface TwoFactorSetup {
  secret: string
  qrCode: string
  backupCodes: string[]
}

export interface TwoFactorVerification {
  code: string
  backupCode?: string
}
