<template>
  <div :class="[
      'bg-white dark:bg-gray-800 rounded-md border transition-all duration-200',
      shadowClass,
      borderClass,
      paddingClass,
      hoverClass,
      clickableClass,
      {
        'opacity-50 pointer-events-none': disabled,
        'overflow-hidden': !allowOverflow,
      },
    ]" @click="handleClick">
    <!-- Header Section -->
    <div v-if="hasHeader" :class="[
        'flex items-center justify-between gap-3',
        headerPaddingClass,
        {
          'border-b border-gray-200 dark:border-gray-700': showHeaderDivider,
          'pb-4': showHeaderDivider && hasContent,
        },
      ]">

      <div class="flex items-center gap-3 min-w-0 flex-1">
        <!-- Icon -->
        <div v-if="icon" :class="['flex-shrink-0 p-2 rounded-lg', iconBackgroundClass]">
          <Icon :name="icon" :class="iconClass" :size="iconSizeClass" />
        </div>

        <!-- Title and Subtitle -->
        <div class="min-w-0 flex-1">
          <div class="flex items-center gap-2">
            <h3 v-if="title" :class="[
                'font-semibold text-gray-900 dark:text-white truncate',
                titleSizeClass,
              ]">
              {{ title }}
            </h3>

            <!-- Badge -->
            <UiBadge v-if="badge" :variant="badge.variant || 'secondary'" :size="badge.size || 'sm'">
              {{ badge.text }}
            </UiBadge>
          </div>

          <p v-if="subtitle" class="text-sm text-gray-500 dark:text-gray-400 truncate mt-1">
            {{ subtitle }}
          </p>
        </div>
      </div>
      <!-- Header Actions -->
      <div v-if="hasHeaderSlot" class="flex-shrink-0 ml-3">
        <slot name="header" />
      </div>
    </div>
   
    <!-- Content Section -->
    <div v-if="hasContent" v-bind="props.contentProps" :class="[
        contentPaddingClass,
        {
          'pt-4': hasHeader && !showHeaderDivider,
          'pt-6': hasHeader && showHeaderDivider,
          'overflow-y-auto': !!contentHeight,
        },
      ]" :style="{height: contentHeight}">
      <!-- Content -->
      <slot />
    </div>

    <!-- Footer Section -->
    <div v-if="hasFooter" :class="[
        'flex items-center justify-between',
        footerPaddingClass,
        {
          'border-t border-gray-200 dark:border-gray-700': showFooterDivider,
          'pt-4': !showFooterDivider && hasContent,
        },
      ]">
      <slot name="footer" />
    </div>

    <!-- Loading Overlay -->
    <div v-if="loading"
      class="absolute inset-0 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-xl flex items-center justify-center z-10">
      <div class="flex items-center gap-3">
        <UiSpinner size="sm" />
        <span v-if="loadingText" class="text-sm text-gray-600 dark:text-gray-400">
          {{ loadingText }}
        </span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, useSlots } from "vue";

interface Badge {
  text: string;
  variant?: "primary" | "secondary" | "success" | "warning" | "error" | "info";
  size?: "xs" | "sm" | "md";
}

interface IconProps {
  class: string;
  color?: "primary" | "secondary" | "success" | "warning" | "error" | "info" | "gray" | "purple";
  background?:  "primary" | "secondary" | "success" | "warning" | "error" | "info" | "gray" | "purple";
  size?: "xs" | "sm" | "md" | "lg" | "xl";
}

interface Props {
  title?: string;
  subtitle?: string;
  icon?: string;
  iconProps?: IconProps;
  badge?: Badge;
  variant?: "default" | "outlined" | "elevated" | "flat";
  size?: "xs" | "sm" | "md" | "lg" | "xl";
  padding?: "none" | "xs" | "sm" | "md" | "lg" | "xl";
  headerPadding?: "none" | "xs" | "sm" | "md" | "lg" | "xl";
  contentPadding?: "none" | "xs" | "sm" | "md" | "lg" | "xl";
  footerPadding?: "none" | "xs" | "sm" | "md" | "lg" | "xl";
  showHeaderDivider?: boolean;
  showFooterDivider?: boolean;
  clickable?: boolean;
  hoverable?: boolean;
  disabled?: boolean;
  loading?: boolean;
  loadingText?: string;
  allowOverflow?: boolean;
  contentHeight?: string;
  contentProps?: Record<string, any>;
}

interface Emits {
  (e: "click", event: MouseEvent): void;
}

const props = withDefaults(defineProps<Props>(), {
  variant: "default",
  size: "md",
  padding: "md",
  showHeaderDivider: true,
  showFooterDivider: true,
  clickable: false,
  hoverable: true,
  disabled: false,
  loading: false,
  allowOverflow: false,
  contentProps: () => ({}),
});

const emit = defineEmits<Emits>();
const slots = useSlots();

// Computed properties for slots
const hasHeaderSlot = computed(() => !!slots.header);
const hasFooter = computed(() => !!slots.footer);
const hasContent = computed(() => !!slots.default);
const hasHeader = computed(
  () => props.title || props.subtitle || props.icon || props.badge || hasHeaderSlot.value
);


// Style classes
const shadowClass = computed(() => {
  const shadows = {
    default: "shadow-sm border-gray-200 dark:border-gray-700",
    outlined: "shadow-none border-2 border-gray-200 dark:border-gray-700",
    elevated: "shadow-lg border-gray-200 dark:border-gray-700",
    flat: "shadow-none border-transparent",
  };
  return shadows[props.variant];
});

const borderClass = computed(() => {
  if (props.variant === "flat") return "border-transparent";
  return "border-gray-200 dark:border-gray-700";
});

const paddingClass = computed(() => {
  if (props.headerPadding || props.contentPadding || props.footerPadding) {
    return ""; // Individual padding will be applied
  }

  const paddings = {
    none: "p-0",
    xs: "p-2",
    sm: "p-3",
    md: "p-4",
    lg: "p-6",
    xl: "p-8",
  };
  return paddings[props.padding];
});

const headerPaddingClass = computed(() => {
  if (props.headerPadding) {
    const paddings = {
      none: "p-0",
      xs: "px-2 pt-2",
      sm: "px-3 pt-3",
      md: "px-4 pt-4",
      lg: "px-6 pt-6",
      xl: "px-8 pt-8",
    };
    return paddings[props.headerPadding];
  }
  return props.padding !== "none"
    ? paddingClass.value.replace("p-", "px-").replace("p-", "pt-")
    : "px-4 pt-4";
});

const contentPaddingClass = computed(() => {
  if (props.contentPadding) {
    const paddings = {
      none: "p-0",
      xs: "px-2",
      sm: "px-3",
      md: "px-4",
      lg: "px-6",
      xl: "px-8",
    };
    return paddings[props.contentPadding];
  }
  return props.padding !== "none" ? paddingClass.value.replace("p-", "px-") : "px-4";
});


const footerPaddingClass = computed(() => {
  if (props.footerPadding) {
    const paddings = {
      none: "p-0",
      xs: "px-2 pb-2",
      sm: "px-3 pb-3",
      md: "px-4 pb-4",
      lg: "px-6 pb-6",
      xl: "px-8 pb-8",
    };
    return paddings[props.footerPadding];
  }
  return props.padding !== "none"
    ? paddingClass.value.replace("p-", "px-").replace("p-", "pb-")
    : "px-4 pb-4";
});

const titleSizeClass = computed(() => {
  const sizes = {
    xs: "text-sm",
    sm: "text-base",
    md: "text-lg",
    lg: "text-xl",
    xl: "text-2xl",
  };
  return sizes[props.size];
});


const iconColorClass = computed(() => {
  const colorMap = {
    primary: "text-brandPrimary",
    secondary: "text-brandSecondary",
    success: "text-brandSuccess",
    warning: "text-brandWarning",
    error: "text-brandDanger",
    info: "text-brandInfo",
    gray: "text-gray-500",
    purple: "text-purple-600",
  };
  return colorMap[props.iconProps?.color || "primary"];
});

const iconClass = computed(() => {
 
  return `${props.iconProps?.class || ""} ${iconColorClass.value}`;
});



const iconSizeClass = computed(() => {
  const sizes = {
    xs: "calc(var(--spacing) * 3)",
    sm: "calc(var(--spacing) * 4)",
    md: "calc(var(--spacing) * 5)",
    lg: "calc(var(--spacing) * 6)",
    xl: "calc(var(--spacing) * 7)",
  };
  return sizes[props.iconProps?.size || props.size];
});

const iconBackgroundClass = computed(() => {
  const backgroundMap = {
    primary: "bg-brandPrimary/10",
    secondary: "bg-brandSecondary/10",
    success: "bg-brandSuccess/10",
    warning: "bg-brandWarning/10",
    error: "bg-brandDanger/10",
    info: "bg-brandInfo/10",
    gray: "bg-gray-200 dark:bg-gray-700",
    purple: "bg-purple-100 dark:bg-purple-900/20",
  };
  return `${backgroundMap[props.iconProps?.background || "primary"]}`;

});

const hoverClass = computed(() => {
  if (!props.hoverable || props.disabled) return "";
  return "hover:shadow-md hover:-translate-y-0.5";
});

const clickableClass = computed(() => {
  if (!props.clickable || props.disabled) return "";
  return "cursor-pointer active:scale-[0.98]";
});

// Methods
const handleClick = (event: MouseEvent) => {
  if (props.disabled || !props.clickable) return;
  emit("click", event);
};
</script>

<style scoped>
/* Custom animations */
.card-enter-active,
.card-leave-active {
  transition: all 0.3s ease;
}

.card-enter-from {
  opacity: 0;
  transform: translateY(10px);
}

.card-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

/* Loading animation */
@keyframes pulse-subtle {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

.loading-pulse {
  animation: pulse-subtle 2s infinite;
}
</style>
