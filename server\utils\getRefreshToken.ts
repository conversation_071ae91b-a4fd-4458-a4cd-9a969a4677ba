// Utility function to proxy the refresh token request to the backend API,
// and handle setting new access_token and refresh_token cookies.
import {
  H3Event,
  setCookie,
  appendHeader,
  createError
} from 'h3';
import { useRuntimeConfig } from '#imports'; // Nuxt 3 auto-import for composables
import { ACCESS_TOKEN_COOKIE, REFRESH_TOKEN_COOKIE } from '~/server/constants';

// Cookie options for the access token, mirroring login.post.ts
const accessTokenCookieOptions = {
  httpOnly: true,
  secure: process.env.NODE_ENV === 'production',
  sameSite: process.env.NODE_ENV === 'production' ? 'strict' as const : 'lax' as const,
  path: '/',
  maxAge: 60 * 15, // 15 minutes, should match backend access token lifetime
};

export default async (event: H3Event) => {
  const config = useRuntimeConfig();
  const targetUrl = `${config.public.apiBase}/auth/refresh`;

  // Forward existing cookies (especially the refresh_token) to the backend
  const existingCookies = event.node.req.headers.cookie;

  try {
    const rawBackendResponse = await $fetch.raw<{ accessToken: string, [key: string]: any }>(
      targetUrl,
      {
        method: 'POST',
        headers: {
          // Forward client's cookies to the backend
          ...(existingCookies && { cookie: existingCookies }),
        },
        // If your backend refresh endpoint expects a body (e.g., CSRF token), add it here.
        // For a simple refresh_token-in-cookie flow, body might not be needed.
      }
    );

    const backendData = rawBackendResponse._data;
    if (!backendData || !backendData.accessToken) {
      console.error('[Nuxt RefreshTokenUtil] Backend did not return expected data (accessToken missing).');
      throw createError({ statusCode: 500, statusMessage: 'Token refresh failed: Invalid response from backend.' });
    }
    
    const { accessToken } = backendData;

    // Set the new access_token cookie
    setCookie(event, ACCESS_TOKEN_COOKIE, accessToken, accessTokenCookieOptions);
    console.log('[Nuxt RefreshTokenUtil] Set new access_token cookie:', ACCESS_TOKEN_COOKIE);

    // Process Set-Cookie headers from the backend (for the new refresh_token)
    const backendSetCookieHeaders = rawBackendResponse.headers.getSetCookie();
    if (backendSetCookieHeaders && backendSetCookieHeaders.length > 0) {
      backendSetCookieHeaders.forEach((cookieHeader: string) => {
        let modifiedCookieHeader = cookieHeader;
        // Check if this is the refresh_token cookie and adjust its path
        if (cookieHeader.toLowerCase().startsWith(`${REFRESH_TOKEN_COOKIE.toLowerCase()}=`)) {
          if (cookieHeader.match(/Path=\/[^;]*/i)) { // If a Path attribute exists
            modifiedCookieHeader = cookieHeader.replace(/Path=\/[^;]*/i, 'Path=/api/');
          } else { // If no Path attribute, add Path=/api/
            modifiedCookieHeader = cookieHeader.replace(/(;|$)/, '; Path=/api/$1');
          }
          
          if (modifiedCookieHeader !== cookieHeader) {
            console.log('[Nuxt RefreshTokenUtil] Modified refresh_token cookie path to Path=/api/. Modified:');
          }
        }
        appendHeader(event, 'Set-Cookie', modifiedCookieHeader);
        
      });
    } else {
      console.warn('[Nuxt RefreshTokenUtil] No Set-Cookie headers received from backend during refresh. Refresh token might not have been updated.');
    }
    
    // Set the response status code from the backend
    event.node.res.statusCode = rawBackendResponse.status;
    
    // Return the JSON payload from the backend (e.g., { user, accessToken, ... })
    // The accessToken in the body might be useful for the client-side state,
    // even though the cookie is HttpOnly.
    return backendData;

  } catch (error: any) {
    console.error('[Nuxt RefreshTokenUtil] Error during token refresh proxy:', error.response?.data || error.message || error);
    // Clear the access token cookie on failure to prevent using an invalid/old one
    setCookie(event, ACCESS_TOKEN_COOKIE, '', { ...accessTokenCookieOptions, maxAge: -1 });

    // Rethrow or create a new error to be handled by the calling API route
    throw createError({
      statusCode: error.response?.status || error.statusCode || 500,
      statusMessage: error.response?.data?.message || error.message || 'Token refresh failed.',
      data: error.response?.data || error.data,
    });
  }
}