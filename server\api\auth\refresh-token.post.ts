import { defineE<PERSON><PERSON><PERSON><PERSON>, H3Event } from 'h3'
import getRefreshToken from '~/server/utils/getRefreshToken';
export default defineEventHandler(async (event: H3Event) => {
  console.log('Refresh token endpoint: Attempting to refresh token...');
  try {
    // getRefreshToken uses proxyRequest, which handles sending the response.
    // We await it and return its result, which delegates response handling.
    const response = await getRefreshToken(event);
    console.log('Refresh token endpoint: Proxy request completed.');
    return response; // This is the response from proxyRequest
  } catch (error: any) {
    console.error('Error during token refresh:', error);
    // If getRefreshT<PERSON> throws an error before proxyRequest sends a response,
    // we can set a status code and return an error object.
    event.node.res.statusCode = error.statusCode || 500;
    return {
      error: true,
      message: error.message || 'An internal server error occurred while refreshing the token.',
    };
  }
});