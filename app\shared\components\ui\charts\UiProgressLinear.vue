<template>
  <div class="ui-progress-linear w-full">
    <!-- Progress Label -->
    <div v-if="showLabel || label" class="flex items-center justify-between mb-2">
      <span v-if="label" class="text-sm font-medium text-gray-700 dark:text-gray-300">
        {{ label }}
      </span>
      <span v-if="showValue" class="text-sm text-gray-600 dark:text-gray-400">
        {{ value }}{{ showPercentage ? '%' : '' }}
      </span>
    </div>

    <!-- Progress Bar Container -->
    <div 
      class="relative w-full bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden"
      :style="{ height: `${height}px` }"
    >
      <!-- Background Track -->
      <div class="absolute inset-0 bg-gray-200 dark:bg-gray-700"></div>
      
      <!-- Progress Fill -->
      <div
        class="h-full transition-all duration-500 ease-out rounded-full"
        :class="getProgressColor(value)"
        :style="{ width: `${Math.min(Math.max(value, 0), 100)}%` }"
      >
        <!-- Animated Stripes (optional) -->
        <div 
          v-if="animated"
          class="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse"
        ></div>
        
        <!-- Gradient Overlay (optional) -->
        <div 
          v-if="gradient"
          class="absolute inset-0 bg-gradient-to-r opacity-80"
          :style="{ background: `linear-gradient(to right, ${gradient.from}, ${gradient.to})` }"
        ></div>
      </div>

      <!-- Buffer/Secondary Progress (optional) -->
      <div
        v-if="bufferValue !== undefined"
        class="absolute inset-y-0 left-0 bg-gray-300 dark:bg-gray-600 opacity-50 rounded-full transition-all duration-500"
        :style="{ width: `${Math.min(Math.max(bufferValue, 0), 100)}%` }"
      ></div>

      <!-- Indeterminate Animation -->
      <div
        v-if="indeterminate"
        class="absolute inset-0 bg-gradient-to-r from-transparent via-current to-transparent animate-bounce"
        :class="getProgressColor(75)"
      ></div>
    </div>

    <!-- Progress Details -->
    <div v-if="showDetails" class="flex items-center justify-between mt-2 text-xs text-gray-600 dark:text-gray-400">
      <span v-if="minLabel">{{ minLabel }}</span>
      <span v-if="maxLabel">{{ maxLabel }}</span>
    </div>

    <!-- Custom Content Slot -->
    <div v-if="$slots.default" class="mt-2">
      <slot />
    </div>
  </div>
</template>

<script setup lang="ts">
// Props interface
interface GradientConfig {
  from: string
  to: string
}

interface Props {
  value: number
  bufferValue?: number
  height?: number
  label?: string
  minLabel?: string
  maxLabel?: string
  showLabel?: boolean
  showValue?: boolean
  showPercentage?: boolean
  showDetails?: boolean
  animated?: boolean
  indeterminate?: boolean
  gradient?: GradientConfig
}

// Props with defaults
const props = withDefaults(defineProps<Props>(), {
  height: 8,
  showLabel: true,
  showValue: true,
  showPercentage: true,
  showDetails: false,
  animated: false,
  indeterminate: false
})

// Functions
const getProgressColor = (value: number): string => {
  if (value >= 75) return 'bg-emerald-600 dark:bg-emerald-500'
  if (value > 50) return 'bg-orange-600 dark:bg-orange-500'
  return 'bg-red-600 dark:bg-red-500'
}
</script>

<style scoped>
.ui-progress-linear {
  /* Custom styles for linear progress */
}

/* Indeterminate animation */
@keyframes indeterminate {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.animate-indeterminate {
  animation: indeterminate 2s infinite linear;
}

/* Striped animation */
@keyframes stripes {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: 40px 0;
  }
}

.animate-stripes {
  background-image: linear-gradient(
    45deg,
    rgba(255, 255, 255, 0.15) 25%,
    transparent 25%,
    transparent 50%,
    rgba(255, 255, 255, 0.15) 50%,
    rgba(255, 255, 255, 0.15) 75%,
    transparent 75%,
    transparent
  );
  background-size: 40px 40px;
  animation: stripes 1s linear infinite;
}
</style>
