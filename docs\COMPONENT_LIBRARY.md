# Component Library Documentation

## 🎨 Overview

The Legal SaaS Frontend Component Library provides a comprehensive set of reusable, accessible, and performant UI components built with Vue 3, TypeScript, and Tailwind CSS.

## 🏗️ Architecture

### Component Categories

```
app/shared/components/
├── ui/                 # Basic UI components
│   ├── UiButton.vue
│   ├── UiInput.vue
│   ├── UiModal.vue
│   └── UiTable.vue
├── forms/              # Form-specific components
│   ├── FormField.vue
│   ├── FormValidation.vue
│   └── FormWizard.vue
├── layout/             # Layout components
│   ├── AppHeader.vue
│   ├── AppSidebar.vue
│   └── AppFooter.vue
└── business/           # Domain-specific components
    ├── CaseCard.vue
    ├── DocumentViewer.vue
    └── TimeTracker.vue
```

## 🧩 Core UI Components

### UiButton

A versatile button component with multiple variants and states.

```vue
<template>
  <UiButton
    variant="primary"
    size="md"
    :loading="isSubmitting"
    :disabled="!isValid"
    @click="handleSubmit"
  >
    Submit Case
  </UiButton>
</template>
```

**Props:**
- `variant`: 'primary' | 'secondary' | 'danger' | 'ghost'
- `size`: 'xs' | 'sm' | 'md' | 'lg' | 'xl'
- `loading`: boolean
- `disabled`: boolean
- `icon`: string (icon name)
- `iconPosition`: 'left' | 'right'

**Events:**
- `click`: MouseEvent

### UiInput

Enhanced input component with validation and accessibility features.

```vue
<template>
  <UiInput
    v-model="email"
    type="email"
    label="Email Address"
    placeholder="Enter your email"
    :error="emailError"
    :required="true"
    autocomplete="email"
  />
</template>
```

**Props:**
- `modelValue`: string | number
- `type`: 'text' | 'email' | 'password' | 'number' | 'tel' | 'url'
- `label`: string
- `placeholder`: string
- `error`: string
- `hint`: string
- `required`: boolean
- `disabled`: boolean
- `readonly`: boolean

**Events:**
- `update:modelValue`: string | number
- `focus`: FocusEvent
- `blur`: FocusEvent

### UiModal

Accessible modal component with focus management and animations.

```vue
<template>
  <UiModal
    v-model:open="isModalOpen"
    title="Edit Case Details"
    size="lg"
    :closable="true"
    @close="handleModalClose"
  >
    <template #default>
      <CaseEditForm :case="selectedCase" />
    </template>
    
    <template #footer>
      <UiButton variant="secondary" @click="isModalOpen = false">
        Cancel
      </UiButton>
      <UiButton variant="primary" @click="handleSave">
        Save Changes
      </UiButton>
    </template>
  </UiModal>
</template>
```

**Props:**
- `open`: boolean
- `title`: string
- `size`: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'full'
- `closable`: boolean
- `maskClosable`: boolean
- `keyboard`: boolean (ESC to close)

**Slots:**
- `default`: Modal content
- `header`: Custom header
- `footer`: Modal actions

### UiTable

Data table with sorting, filtering, and pagination.

```vue
<template>
  <UiTable
    :columns="caseColumns"
    :data="cases"
    :loading="isLoading"
    :pagination="pagination"
    @sort="handleSort"
    @row-click="handleRowClick"
  />
</template>

<script setup lang="ts">
const caseColumns = [
  {
    key: 'caseNumber',
    title: 'Case Number',
    sortable: true,
    width: 150
  },
  {
    key: 'title',
    title: 'Title',
    sortable: true,
    ellipsis: true
  },
  {
    key: 'status',
    title: 'Status',
    render: (value, record) => h(CaseStatusBadge, { status: value })
  },
  {
    key: 'actions',
    title: 'Actions',
    width: 120,
    render: (_, record) => h(CaseActions, { case: record })
  }
]
</script>
```

**Props:**
- `columns`: TableColumn[]
- `data`: any[]
- `loading`: boolean
- `pagination`: PaginationConfig | false
- `rowKey`: string | function
- `selectable`: boolean

**Events:**
- `sort`: { field: string, direction: 'asc' | 'desc' }
- `row-click`: (record: any, index: number)
- `selection-change`: any[]

## 📝 Form Components

### FormField

Wrapper component for form inputs with validation.

```vue
<template>
  <FormField
    name="clientName"
    label="Client Name"
    :required="true"
    :rules="nameValidationRules"
  >
    <UiInput
      v-model="form.clientName"
      placeholder="Enter client name"
    />
  </FormField>
</template>
```

### FormWizard

Multi-step form component with progress tracking.

```vue
<template>
  <FormWizard
    :steps="caseCreationSteps"
    v-model:current-step="currentStep"
    @complete="handleFormComplete"
  >
    <template #step-1>
      <CaseBasicInfo v-model="form.basicInfo" />
    </template>
    
    <template #step-2>
      <CaseParticipants v-model="form.participants" />
    </template>
    
    <template #step-3>
      <CaseDocuments v-model="form.documents" />
    </template>
  </FormWizard>
</template>
```

## 🏢 Business Components

### CaseCard

Displays case information in a card format.

```vue
<template>
  <CaseCard
    :case="caseData"
    :interactive="true"
    @click="navigateToCase"
    @status-change="handleStatusChange"
  />
</template>
```

**Props:**
- `case`: Case
- `interactive`: boolean
- `compact`: boolean
- `showActions`: boolean

### DocumentViewer

Advanced document viewer with annotations and collaboration.

```vue
<template>
  <DocumentViewer
    :document="document"
    :annotations="annotations"
    :editable="canEdit"
    @annotate="handleAnnotation"
    @download="handleDownload"
  />
</template>
```

**Features:**
- PDF, Word, and image support
- Annotation tools
- Zoom and navigation
- Collaborative editing
- Version comparison

### TimeTracker

Time tracking component for billable hours.

```vue
<template>
  <TimeTracker
    :case-id="caseId"
    :auto-start="false"
    @time-entry="handleTimeEntry"
    @timer-start="handleTimerStart"
    @timer-stop="handleTimerStop"
  />
</template>
```

## 🎨 Design System

### Color Palette

```css
:root {
  /* Primary Colors */
  --color-primary-50: #eff6ff;
  --color-primary-500: #3b82f6;
  --color-primary-900: #1e3a8a;
  
  /* Semantic Colors */
  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-error: #ef4444;
  --color-info: #06b6d4;
  
  /* Neutral Colors */
  --color-gray-50: #f9fafb;
  --color-gray-500: #6b7280;
  --color-gray-900: #111827;
}
```

### Typography Scale

```css
:root {
  /* Font Sizes */
  --text-xs: 0.75rem;    /* 12px */
  --text-sm: 0.875rem;   /* 14px */
  --text-base: 1rem;     /* 16px */
  --text-lg: 1.125rem;   /* 18px */
  --text-xl: 1.25rem;    /* 20px */
  --text-2xl: 1.5rem;    /* 24px */
  --text-3xl: 1.875rem;  /* 30px */
  
  /* Font Weights */
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;
}
```

### Spacing System

```css
:root {
  /* Spacing Scale */
  --space-1: 0.25rem;   /* 4px */
  --space-2: 0.5rem;    /* 8px */
  --space-3: 0.75rem;   /* 12px */
  --space-4: 1rem;      /* 16px */
  --space-6: 1.5rem;    /* 24px */
  --space-8: 2rem;      /* 32px */
  --space-12: 3rem;     /* 48px */
  --space-16: 4rem;     /* 64px */
}
```

## ♿ Accessibility Features

### ARIA Support
All components include proper ARIA attributes:
- `aria-label` and `aria-labelledby`
- `aria-describedby` for help text
- `aria-invalid` for validation states
- `role` attributes for semantic meaning

### Keyboard Navigation
- Tab order management
- Arrow key navigation for lists
- Enter/Space activation
- Escape key for modals

### Screen Reader Support
- Semantic HTML elements
- Live regions for dynamic content
- Descriptive text for icons
- Form validation announcements

## 🧪 Testing Components

### Component Testing Example

```typescript
import { describe, it, expect } from 'vitest'
import { mount } from '@vue/test-utils'
import UiButton from './UiButton.vue'

describe('UiButton', () => {
  it('renders with correct variant class', () => {
    const wrapper = mount(UiButton, {
      props: { variant: 'primary' }
    })
    
    expect(wrapper.classes()).toContain('btn--primary')
  })

  it('emits click event when clicked', async () => {
    const wrapper = mount(UiButton)
    
    await wrapper.trigger('click')
    
    expect(wrapper.emitted('click')).toBeTruthy()
  })

  it('is disabled when loading', () => {
    const wrapper = mount(UiButton, {
      props: { loading: true }
    })
    
    expect(wrapper.attributes('disabled')).toBeDefined()
  })
})
```

### Visual Testing

```typescript
// Storybook stories for visual testing
export default {
  title: 'UI/Button',
  component: UiButton,
  argTypes: {
    variant: {
      control: { type: 'select' },
      options: ['primary', 'secondary', 'danger', 'ghost']
    }
  }
}

export const Primary = {
  args: {
    variant: 'primary',
    children: 'Primary Button'
  }
}

export const Loading = {
  args: {
    variant: 'primary',
    loading: true,
    children: 'Loading...'
  }
}
```

## 📱 Responsive Design

### Breakpoint System

```css
/* Mobile First Approach */
.component {
  /* Mobile styles (default) */
  padding: var(--space-4);
  
  /* Tablet and up */
  @media (min-width: 768px) {
    padding: var(--space-6);
  }
  
  /* Desktop and up */
  @media (min-width: 1024px) {
    padding: var(--space-8);
  }
}
```

### Responsive Components

```vue
<template>
  <div class="
    grid grid-cols-1
    md:grid-cols-2
    lg:grid-cols-3
    gap-4 md:gap-6
  ">
    <CaseCard
      v-for="case in cases"
      :key="case.id"
      :case="case"
      class="w-full"
    />
  </div>
</template>
```

## 🚀 Performance Optimization

### Lazy Loading
```vue
<script setup lang="ts">
// Lazy load heavy components
const DocumentViewer = defineAsyncComponent(() =>
  import('./DocumentViewer.vue')
)

const HeavyChart = defineAsyncComponent({
  loader: () => import('./HeavyChart.vue'),
  loadingComponent: LoadingSpinner,
  errorComponent: ErrorComponent,
  delay: 200,
  timeout: 3000
})
</script>
```

### Virtual Scrolling
```vue
<template>
  <VirtualList
    :items="largeDataset"
    :item-height="60"
    :buffer-size="10"
  >
    <template #item="{ item, index }">
      <CaseListItem :case="item" :index="index" />
    </template>
  </VirtualList>
</template>
```

## 📚 Usage Guidelines

### Do's ✅
- Use semantic HTML elements
- Provide proper ARIA labels
- Follow the design system
- Test with keyboard navigation
- Optimize for performance
- Write comprehensive tests

### Don'ts ❌
- Don't use generic div elements
- Don't hardcode colors or spacing
- Don't ignore accessibility
- Don't create overly complex components
- Don't skip error handling
- Don't forget responsive design

## 🔄 Component Lifecycle

### Development Process
1. **Design**: Create component design in Figma
2. **Specification**: Write component specification
3. **Implementation**: Build component with TypeScript
4. **Testing**: Write unit and integration tests
5. **Documentation**: Create usage documentation
6. **Review**: Code review and accessibility audit
7. **Release**: Add to component library

### Maintenance
- Regular accessibility audits
- Performance monitoring
- User feedback integration
- Design system updates
- Breaking change management

This component library ensures consistency, accessibility, and performance across the Legal SaaS Frontend while providing a great developer experience.
