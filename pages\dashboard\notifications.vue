<template>
  <div class="space-y-6">
    <!-- Notifications Management -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
      <!-- Header -->
      <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between">
          <div>
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">All Notifications</h2>
            <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
              Manage and view all platform notifications
            </p>
          </div>
          <div class="flex items-center gap-3">
            <UiButton @click="markAllAsRead" variant="outline" size="sm" :disabled="unreadCount === 0">
              <Icon name="material-symbols:mark-email-read" class="h-4 w-4 mr-2" />
              Mark <PERSON> Read ({{ unreadCount }})
            </UiButton>
            <UiButton @click="openSettings" variant="outline" size="sm">
              <Icon name="material-symbols:settings" class="h-4 w-4 mr-2" />
              Settings
            </UiButton>
          </div>
        </div>

        <!-- Filters -->
        <div class="flex flex-wrap gap-2 mt-4">
          <UiButton
            @click="activeFilter = 'all'"
            size="sm"
            :variant="activeFilter === 'all' ? 'primary' : 'ghost'"
          >
            All ({{ notifications.length }})
          </UiButton>
          <UiButton
            @click="activeFilter = 'unread'"
            size="sm"
            :variant="activeFilter === 'unread' ? 'primary' : 'ghost'"
          >
            Unread ({{ unreadCount }})
          </UiButton>
          <UiButton
            @click="activeFilter = 'system'"
            size="sm"
            :variant="activeFilter === 'system' ? 'primary' : 'ghost'"
          >
            System
          </UiButton>
          <UiButton
            @click="activeFilter = 'security'"
            size="sm"
            :variant="activeFilter === 'security' ? 'primary' : 'ghost'"
          >
            Security
          </UiButton>
          <UiButton
            @click="activeFilter = 'user'"
            size="sm"
            :variant="activeFilter === 'user' ? 'primary' : 'ghost'"
          >
            User
          </UiButton>
          <UiButton
            @click="activeFilter = 'tenant'"
            size="sm"
            :variant="activeFilter === 'tenant' ? 'primary' : 'ghost'"
          >
            Tenant
          </UiButton>
        </div>
      </div>

      <!-- Notifications List -->
      <div class="divide-y divide-gray-200 dark:divide-gray-700">
        <div v-if="filteredNotifications.length === 0" class="p-12 text-center">
          <Icon name="material-symbols:notifications-off" class="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No notifications</h3>
          <p class="text-gray-500 dark:text-gray-400">
            {{ activeFilter === 'all' ? "You're all caught up!" : `No ${activeFilter} notifications found.` }}
          </p>
        </div>

        <div
          v-for="notification in paginatedNotifications"
          :key="notification.id"
          :class="[
            'p-6 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors cursor-pointer',
            !notification.read ? 'bg-blue-50 dark:bg-blue-900/10' : ''
          ]"
          @click="handleNotificationClick(notification)"
        >
          <div class="flex items-start gap-4">
            <!-- Icon -->
            <div 
              :class="[
                'flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center',
                getNotificationIconClass(notification.type)
              ]"
            >
              <Icon :name="getNotificationIcon(notification.type)" class="h-5 w-5 text-white" />
            </div>

            <!-- Content -->
            <div class="flex-1 min-w-0">
              <div class="flex items-start justify-between">
                <div class="flex-1">
                  <div class="flex items-center gap-2 mb-1">
                    <h3 class="text-base font-medium text-gray-900 dark:text-white">
                      {{ notification.title }}
                    </h3>
                    <span
                      v-if="notification.priority && notification.priority !== 'normal'"
                      :class="[
                        'inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium',
                        getPriorityClass(notification.priority)
                      ]"
                    >
                      {{ notification.priority }}
                    </span>
                  </div>
                  
                  <p class="text-sm text-gray-600 dark:text-gray-300 mb-2">
                    {{ notification.message }}
                  </p>

                  <!-- Metadata -->
                  <div class="flex items-center gap-4 text-xs text-gray-500 dark:text-gray-400">
                    <span>{{ formatTimestamp(notification.timestamp) }}</span>
                    <span class="capitalize">{{ notification.category || notification.type }}</span>
                    <span v-if="notification.metadata?.source">
                      Source: {{ notification.metadata.source }}
                    </span>
                  </div>

                  <!-- Actions -->
                  <div v-if="notification.actions && notification.actions.length > 0" class="flex gap-2 mt-3">
                    <UiButton
                      v-for="action in notification.actions"
                      :key="action.label"
                      @click.stop="handleNotificationAction(notification, action)"
                      size="sm"
                      :variant="action.primary ? 'primary' : 'outline'"
                    >
                      {{ action.label }}
                    </UiButton>
                  </div>
                </div>

                <!-- Status and Actions -->
                <div class="flex items-center gap-2 ml-4">
                  <div v-if="!notification.read" class="w-2 h-2 bg-blue-500 rounded-full"></div>
                  
                  <UiButton
                    @click.stop="toggleNotificationRead(notification)"
                    size="sm"
                    variant="ghost"
                    class="opacity-0 group-hover:opacity-100 transition-opacity"
                  >
                    <Icon 
                      :name="notification.read ? 'material-symbols:mark-email-unread' : 'material-symbols:mark-email-read'" 
                      class="h-4 w-4" 
                    />
                  </UiButton>
                  
                  <UiButton
                    @click.stop="deleteNotification(notification)"
                    size="sm"
                    variant="ghost"
                    class="opacity-0 group-hover:opacity-100 transition-opacity text-red-600 hover:text-red-700"
                  >
                    <Icon name="material-symbols:delete" class="h-4 w-4" />
                  </UiButton>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Pagination -->
      <div v-if="totalPages > 1" class="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between">
          <div class="text-sm text-gray-700 dark:text-gray-300">
            Showing {{ ((currentPage - 1) * itemsPerPage) + 1 }} to 
            {{ Math.min(currentPage * itemsPerPage, filteredNotifications.length) }} of 
            {{ filteredNotifications.length }} notifications
          </div>
          <div class="flex items-center gap-2">
            <UiButton
              @click="currentPage--"
              :disabled="currentPage <= 1"
              size="sm"
              variant="outline"
            >
              Previous
            </UiButton>
            <span class="text-sm text-gray-700 dark:text-gray-300">
              Page {{ currentPage }} of {{ totalPages }}
            </span>
            <UiButton
              @click="currentPage++"
              :disabled="currentPage >= totalPages"
              size="sm"
              variant="outline"
            >
              Next
            </UiButton>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { useRouter } from 'vue-router'

interface NotificationAction {
  label: string
  action: string
  primary?: boolean
}

interface Notification {
  id: string
  type: 'system' | 'security' | 'user' | 'tenant' | 'backup' | 'update' | 'warning' | 'error' | 'success' | 'info'
  title: string
  message: string
  timestamp: string
  read: boolean
  priority?: 'low' | 'normal' | 'high' | 'urgent'
  category?: 'system' | 'security' | 'user' | 'tenant'
  actionUrl?: string
  actions?: NotificationAction[]
  metadata?: Record<string, any>
}

const router = useRouter()

// State
const activeFilter = ref<'all' | 'unread' | 'system' | 'security' | 'user' | 'tenant'>('all')
const currentPage = ref(1)
const itemsPerPage = 10

// Mock notifications - in real app, this would come from API
const notifications = ref<Notification[]>([
  // Add comprehensive sample data here
  {
    id: '1',
    type: 'security',
    title: 'Security Alert: Multiple Failed Login Attempts',
    message: 'Multiple failed login attempts detected from IP *************. This could indicate a brute force attack.',
    timestamp: new Date(Date.now() - 1000 * 60 * 5).toISOString(),
    read: false,
    priority: 'high',
    category: 'security',
    metadata: { source: 'Security Monitor', ip: '*************' },
    actions: [
      { label: 'Block IP', action: 'block-ip', primary: true },
      { label: 'View Details', action: 'view-details' }
    ]
  },
  // Add more sample notifications...
])

// Computed properties
const unreadCount = computed(() => notifications.value.filter(n => !n.read).length)

const filteredNotifications = computed(() => {
  let filtered = notifications.value
  
  switch (activeFilter.value) {
    case 'unread':
      filtered = filtered.filter(n => !n.read)
      break
    case 'system':
    case 'security':
    case 'user':
    case 'tenant':
      filtered = filtered.filter(n => n.category === activeFilter.value)
      break
  }
  
  return filtered.sort((a, b) => {
    const priorityOrder = { urgent: 4, high: 3, normal: 2, low: 1 }
    const aPriority = priorityOrder[a.priority || 'normal']
    const bPriority = priorityOrder[b.priority || 'normal']
    
    if (aPriority !== bPriority) {
      return bPriority - aPriority
    }
    
    return new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
  })
})

const totalPages = computed(() => Math.ceil(filteredNotifications.value.length / itemsPerPage))

const paginatedNotifications = computed(() => {
  const start = (currentPage.value - 1) * itemsPerPage
  const end = start + itemsPerPage
  return filteredNotifications.value.slice(start, end)
})

// Methods
const markAllAsRead = () => {
  notifications.value.forEach(notification => {
    notification.read = true
  })
}

const toggleNotificationRead = (notification: Notification) => {
  notification.read = !notification.read
}

const deleteNotification = (notification: Notification) => {
  const index = notifications.value.findIndex(n => n.id === notification.id)
  if (index > -1) {
    notifications.value.splice(index, 1)
  }
}

const handleNotificationClick = (notification: Notification) => {
  notification.read = true
  
  if (notification.actionUrl) {
    router.push(notification.actionUrl)
  }
}

const handleNotificationAction = (notification: Notification, action: NotificationAction) => {
  console.log('Notification action:', action.action, 'for notification:', notification.id)
  notification.read = true
}

const openSettings = () => {
  router.push('/dashboard/settings/notifications')
}

const getNotificationIcon = (type: Notification['type']) => {
  const icons = {
    system: 'material-symbols:settings',
    security: 'material-symbols:security',
    user: 'material-symbols:person',
    tenant: 'material-symbols:business',
    backup: 'material-symbols:backup',
    update: 'material-symbols:system-update',
    warning: 'material-symbols:warning',
    error: 'material-symbols:error',
    success: 'material-symbols:check-circle',
    info: 'material-symbols:info'
  }
  return icons[type] || 'material-symbols:notifications'
}

const getNotificationIconClass = (type: Notification['type']) => {
  const classes = {
    system: 'bg-blue-500',
    security: 'bg-red-500',
    user: 'bg-purple-500',
    tenant: 'bg-green-500',
    backup: 'bg-indigo-500',
    update: 'bg-cyan-500',
    warning: 'bg-orange-500',
    error: 'bg-red-600',
    success: 'bg-green-600',
    info: 'bg-blue-400'
  }
  return classes[type] || 'bg-gray-500'
}

const getPriorityClass = (priority: string) => {
  const classes = {
    low: 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300',
    normal: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
    high: 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300',
    urgent: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
  }
  return classes[priority as keyof typeof classes] || classes.normal
}

const formatTimestamp = (timestamp: string) => {
  const date = new Date(timestamp)
  const now = new Date()
  const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))
  
  if (diffInMinutes < 1) return 'Just now'
  if (diffInMinutes < 60) return `${diffInMinutes}m ago`
  
  const diffInHours = Math.floor(diffInMinutes / 60)
  if (diffInHours < 24) return `${diffInHours}h ago`
  
  const diffInDays = Math.floor(diffInHours / 24)
  if (diffInDays < 7) return `${diffInDays}d ago`
  
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

definePageMeta({
  layout: 'dashboard',
  title: 'Notifications',
  showPageHeader: true,
  pageHeaderTitle: 'Notifications',
  pageHeaderDescription: 'View and manage all platform notifications',
  pageHeaderIcon: 'material-symbols:notifications',
  breadcrumb: [
    { label: 'Dashboard', href: '/dashboard' },
    { label: 'Notifications' },
  ],
});
</script>
